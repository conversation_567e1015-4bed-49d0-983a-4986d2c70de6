import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  CUSTOM_ELEMENTS_SCHEMA,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  ViewChild,
} from '@angular/core';

/**
 * SlideTagComponent
 */
@Component({
  standalone: true,
  selector: 'app-slide-tag',
  templateUrl: './slide-tag.component.html',
  styleUrl: './slide-tag.component.scss',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CommonModule],
})
export class SlideTagComponent implements AfterViewInit, OnInit, OnChanges, OnDestroy {
  @ViewChild('tagContainer', { static: true }) tagContainer!: ElementRef;
  private _tags: Set<string> = new Set();

  @Input()
  set tags(value: string[]) {
    this._tags = new Set(value);
  }

  get tags(): string[] {
    return Array.from(this._tags);
  }

  @Input() classParent!: string;

  extendedTags: string[] = [];
  private animationInterval: any;
  private currentAnimationDuration = 45; // Default duration in seconds
  private readonly minDuration = 15; // Minimum speed (fastest)
  private readonly maxDuration = 55; // Maximum duration (slowest)
  private readonly durationStep = 5; // Step size for speed changes
  private readonly transitionDuration = 1000; // Transition time in ms

  isSilde = false;

  /**
   * The contructor
   * @param cdf ChangeDetectorRef
   */
  constructor(private readonly cdf: ChangeDetectorRef) {}

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.extendedTags = this.tags;
  }

  /**
   * The ngAfterViewInit function calls the startAutoSlide method.
   */
  ngAfterViewInit() {
    this.startAutoSlide();
  }

  /**
   * ngOnChanges
   * @param {SimpleChanges} changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    const { tags } = changes;
    if (tags) {
      this.startAutoSlide();
    }
  }

  /**
   * Initialize auto slide functionality
   */
  startAutoSlide() {
    // Clear any existing animation interval
    if (this.animationInterval) {
      clearInterval(this.animationInterval);
    }

    const elementLimit = document.querySelector(`.${this.classParent}`) as HTMLElement;
    if (!elementLimit) return;

    const elementWidth = elementLimit.offsetWidth;
    const tagElement = elementLimit.querySelector('#tag-wrapper-id') as HTMLElement;
    if (!tagElement) return;

    const tagWidth = tagElement.offsetWidth;

    if (tagWidth > elementWidth) {
      this.extendedTags = [...this.tags, ...this.tags];
      this.isSilde = true;
      // Reset to default animation duration and apply it
      this.currentAnimationDuration = 45;
      tagElement.style.animationDuration = `${this.currentAnimationDuration}s`;
    } else {
      this.extendedTags = [...this.tags];
      this.isSilde = false;
    }

    // Double-check after DOM updates
    setTimeout(() => {
      const updatedTagElement = elementLimit.querySelector('#tag-wrapper-id') as HTMLElement;
      if (!updatedTagElement) return;

      const updatedTagWidth = updatedTagElement.offsetWidth;
      if (updatedTagWidth > elementWidth) {
        this.extendedTags = [...this.tags, ...this.tags];
        this.isSilde = true;
        this.currentAnimationDuration = 45;
        updatedTagElement.style.animationDuration = `${this.currentAnimationDuration}s`;
      } else {
        this.extendedTags = [...this.tags];
        this.isSilde = false;
      }
    }, 30);

    this.cdf.detectChanges();
  }

  /**
   * Increase the sliding speed smoothly
   */
  speedUp() {
    if (!this.isSilde) return;

    // Cancel any ongoing animation
    if (this.animationInterval) {
      clearInterval(this.animationInterval);
    }

    const targetDuration = Math.max(this.currentAnimationDuration - this.durationStep, this.minDuration);

    if (targetDuration === this.currentAnimationDuration) {
      return; // Already at minimum duration
    }

    this.animateToSpeed(targetDuration);
  }

  /**
   * Decrease the sliding speed smoothly
   */
  slowDown() {
    if (!this.isSilde) return;

    // Cancel any ongoing animation
    if (this.animationInterval) {
      clearInterval(this.animationInterval);
    }

    const targetDuration = Math.min(this.currentAnimationDuration + this.durationStep, this.maxDuration);

    if (targetDuration === this.currentAnimationDuration) {
      return; // Already at maximum duration
    }

    this.animateToSpeed(targetDuration);
  }

  /**
   * Smoothly animate to a target speed
   * @param targetDuration Target animation duration in seconds
   */
  private animateToSpeed(targetDuration: number) {
    const tagElement = document.querySelector('.tag-wrapper') as HTMLElement;
    if (!tagElement) return;

    const startDuration = this.currentAnimationDuration;
    const durationDifference = targetDuration - startDuration;
    const startTime = Date.now();
    const steps = 20; // Number of animation steps
    const stepInterval = this.transitionDuration / steps;

    this.animationInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / this.transitionDuration, 1);

      // Use easing function for smoother animation
      const easedProgress = this.easeInOutCubic(progress);
      const currentDuration = startDuration + durationDifference * easedProgress;

      tagElement.style.animationDuration = `${currentDuration}s`;

      if (progress >= 1) {
        clearInterval(this.animationInterval);
        this.currentAnimationDuration = targetDuration;
        this.animationInterval = null;
      }
    }, stepInterval);
  }

  /**
   * Easing function for smooth animation
   * @param t Progress value between 0 and 1
   * @returns Eased progress value
   */
  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * Clean up resources when component is destroyed
   */
  ngOnDestroy(): void {
    if (this.animationInterval) {
      clearInterval(this.animationInterval);
      this.animationInterval = null;
    }
  }
}
