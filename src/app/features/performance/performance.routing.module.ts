import { RouterModule, Routes } from '@angular/router';
import { PerformanceView } from './views/performance.view';
import { NgModule } from '@angular/core';
import { WorkPerformanceContainer } from './containers/work-performance/work-performance.container';
import { FeesCommissionContainer } from './containers/fees-commission/fees-commission.container';
import { BuySellContainer } from './containers/buy-sell-container/buy-sell.container';
import { DepositWithDrawalContainer } from './containers/deposit-withdrawal/deposit-withdrawal.container';
import { OpenNewAccountContainer } from './containers/open-new-account/open-new-account.container';
import { NavManagementContainer } from './containers/nav-management/nav-management.container';
import { CustomerRatingContainer } from './containers/customer-rating/customer-rating.container';
import { FeesCommissionDetailComponent } from './components/fee-commission-detail/fee-commission-detail.component';
import { DepositWithDrawalDetailComponent } from './components/deposit-withdrawal/deposit-withdrawal.component';
import { BuySellDetailComponent } from './components/buy-sell-detail/buy-sell-detail.component';
import { OpenNewAccountDetailComponent } from './components/open-new-account-detail/open-new-account-detail.component';
const routes: Routes = [
  {
    path: '',
    component: PerformanceView,
    children: [
      {
        path: '',
        redirectTo: 'work-performance',
        pathMatch: 'full',
      },
      {
        path: 'work-performance',
        component: WorkPerformanceContainer,
      },
      {
        path: 'fees-commission',
        children: [
          {
            path: 'detail',
            component: FeesCommissionDetailComponent,
          },
          {
            path: '',
            component: FeesCommissionContainer,
          },
        ],
      },
      {
        path: 'buy-sell',
        children: [
          {
            path: 'detail',
            component: BuySellDetailComponent,
          },
          {
            path: '',
            component: BuySellContainer,
          },
        ],
      },
      {
        path: 'deposit-withdrawal',
        children: [
          {
            path: 'detail',
            component: DepositWithDrawalDetailComponent,
          },
          {
            path: '',
            component: DepositWithDrawalContainer,
          },
        ],
      },
      {
        path: 'open-new-account',
        children: [
          {
            path: 'detail',
            component: OpenNewAccountDetailComponent,
          },
          {
            path: '',
            component: OpenNewAccountContainer,
          },
        ],
      },
      {
        path: 'nav-management',
        component: NavManagementContainer,
      },
      {
        path: 'customer-ratings',
        component: CustomerRatingContainer,
      },
    ],
  },
];

/**
 * Configures and manages performance routes.
 */
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PerformanceRoutingModule {}
