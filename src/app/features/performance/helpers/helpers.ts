import { IOptionSelection } from "../models/performance";




export const convertDataForAdmin = (data: any[]) => {
    data.forEach((item) => {
      if (!item.name && item.children && item.children.length > 0) {
        item['broker'] = `${item.children.length.toString().padStart(2, '0')} Phòng MG`;
        convertDataForAdmin(item.children);
      } else {
        item['broker'] = item.name;
        if (item.children && item.children.length > 0) {
          convertDataForAdmin(item.children);
        }
      }
    });
  }


  export  const  updateOptionSelection = (data: any[], optionSelection: IOptionSelection) => {
      const { roomSelect, brokerSelect } = optionSelection;
      return data
        .map((item) => {
          if (item.parent) return null;
          const childrenMatch = item.children
            ?.map((i: any) => {
              const isRoomMatch = roomSelect?.includes(i.name.replace('MG ', ''));
              if (isRoomMatch) {
                const brokerMatch = (i.children ?? []).filter((b: any) => brokerSelect?.includes(b.name));
                return brokerMatch.length
                  ? {
                      ...i,
                      children: brokerMatch,
                      isExpanded: true,
                    }
                  : null;
              } else {
                return null;
              }
            })
            .filter((d: any) => d !== null);
          return childrenMatch.length
            ? {
                ...item,
                children: childrenMatch,
                isExpanded: true,
              }
            : null;
        })
        .filter((i: any) => i !== null);
    }
