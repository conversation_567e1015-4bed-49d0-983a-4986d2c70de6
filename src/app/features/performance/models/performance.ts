import { IRangeFilter } from '../../assets/models/asset';
import { ICommonFilterParam } from '../../customers/model/customer';
import { EViewPerformance } from '../constants/performance';

export interface IFilterWorkPerformanceParam {
  isFilter: false;
  optionFilterRange: IOptionFilter;
  tag: string | null;
  optionSelection: IOptionSelection;
}

export interface IFilterFeesCommission extends ICommonFilterParam {
  startDate: string | null;
  endDate: string | null;
}

export interface IPerformanceState {
  filterPerformance: IFilterWorkPerformanceParam;
  filterFeesCommistion: IFilterWorkPerformanceParam;
  filterDepositWithDrawal: IFilterWorkPerformanceParam;
  filterBuySell: IFilterWorkPerformanceParam;
  filterOpenNewAccount: IFilterWorkPerformanceParam;
  filterNavManagement: IFilterWorkPerformanceParam;
  filterSearchValuePerformance: ISearchTextValuePerformance;
  itemSelected: IItemSelected | null;
}

export interface IItemSelected {
  id: string;
  date: string;
  name: string;
}

export interface IOptionFilter {
  dateRange: IRangeFilter;
}

export interface ISearchTextValuePerformance {
  searchText: string | null;
}

export interface IDataSetChart {
  label: string;
  backgroundColor?: string | string[];
  hoverBackgroundColor?: string | string[];
  data: number[];
  borderColor?: string | string[];
  borderWidth?: number;
  maxBarThickness?: number;
  borderSkipped?: 'start' | 'end' | 'left' | 'right' | 'bottom' | 'top' | 'middle' | boolean;
  barPercentage?: number;
  categoryPercentage?: number;
  fill?: boolean;
  tension?: number;
  dataConfig?: any;
  // borderRadius?: number | IBorderRadiusChart
}

export interface ITypeOptionView {
  name: string;
  asset: string;
  value: EViewPerformance;
}

export interface IBorderRadiusChart {
  topLeft: number;
  topRight: number;
  bottomLeft: number;
  bottomRight: number;
}

export interface IListOptions {
  label?: string;
  value?: number;
  name?: string;
  accountNumber?: string;
  id?: string;
  isSelect: boolean;
}

export interface IOptionSelection {
  roomSelect: string[] | null;
  brokerSelect: string[] | null;
}

export enum ETypeRevenueFeeOption {
  REVENUE_FEE,
  NET,
  COMMISSION_FEE,
}

export enum ETypeTimeOption {
  DAY,
  WEEK,
  ONE_MONTH,
  HALF_OF_YEAR,
  YEAR,
}

export interface IRevenueFeeData {
  date: string;
  share: number;
  derivative: number;
  bonds: number;
}

export interface ITotalMoneyData {
  date: string;
  income: number;
  outcome: number;
}

export enum ETypeTotalMoney {
  DEPOSIT_WITHDRAWAL,
  BUY_SELL,
}

export interface ITransactionData {
  name: string;
  data: number;
}

export enum ENewAccountType {
  NEW,
  TOTAL,
}

export interface INewAccountData {
  date: string;
  data: number;
}

export interface IRankingRevenueFeeData {
  name: string;
  transactionFee: number;
  commissionBroker: number;
}

export enum EBrokerAndRoomType {
  ROOM,
  BROKER,
}

export interface IRankingAccountData {
  name: string;
  hasTransaction: number;
  notTransaction: number;
}

export interface IProportionRevenueData {
  name: string;
  data: number;
}
