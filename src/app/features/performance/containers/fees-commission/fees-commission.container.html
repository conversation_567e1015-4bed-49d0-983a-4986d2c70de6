<div class="fees-commission-container">
  <div class="header-fees-commission-container">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-256' | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[0]}}
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[1]}}
        </div>
        <div class="box-info typo-body-11"><img src="./assets/icons/table_sum.svg" alt="table_sum" /> {{tags[2]}}</div>

        <!-- <div class="dropdown-btn" *ngIf="!showCollapse" (click)="toggleButtons()">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div> -->
      </div>
      <!-- <div class="collapse-btn" *ngIf="showCollapse" (click)="toggleButtons()">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div> -->
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter
        }"
      >
      </app-action-btn>
    </div>
  </div>

  <!-- <div [ngClass]="{'hidden': !showCollapse}">
    <app-slide-tag [tags]="tags" [classParent]="'fees-commission-container'"></app-slide-tag>
  </div> -->

  <div class="table-view-container">
    <sha-grid
      #grid
      class="table-custom-cls"
      [columnConfigs]="columnConfigs"
      [data]="data"
      [expandableIconPosition]="'broker'"
      (clickColumn)="clickOnColumn($event); showInfoDetailFee($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreActionFee($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
    >
    </sha-grid>
  </div>
</div>

<!-- ST TK có giao dịch -->
<ng-template #accountTransaction let-accountTransaction="templateInfo" let-element="element">
  @if(accountTransaction){
  <div>
    <ng-container *ngIf="accountTransaction > 0">
      <div class="price-increase typo-body-12">
        <span>{{accountTransaction}} tài khoản</span>
      </div>
    </ng-container>
    <ng-container *ngIf="accountTransaction === 0">
      <div class="price-stable typo-body-12">
        <span>{{accountTransaction}} tài khoản</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>

  }
</ng-template>

<!-- SL TK không có giao dịch -->
<ng-template #accountHaventTransaction let-accountHaventTransaction="templateInfo" let-element="element">
  @if(accountHaventTransaction){
  <div>
    <ng-container *ngIf="accountHaventTransaction > 0">
      <div class="price-reduce typo-body-12">
        <span>{{accountHaventTransaction}} tài khoản</span>
      </div>
    </ng-container>
    <ng-container *ngIf="accountHaventTransaction === 0">
      <div class="price-stable typo-body-12">
        <span>{{accountHaventTransaction}} tài khoản</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>
  }
</ng-template>

<!-- GTGD(mua) -->

<ng-template #sellGmv let-sellGmv="templateInfo" let-element="element">
  @if(sellGmv){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="sellGmv > 0">
      <div class="price-increase typo-body-12">
        <span>{{customNumberFormat(sellGmv)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="sellGmv === 0">
      <div class="price-stable typo-body-12">
        <span>{{sellGmv}}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>

<!-- GTGD(bán) -->

<ng-template #buyGmv let-buyGmv="templateInfo" let-element="element">
  @if(buyGmv){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="buyGmv > 0">
      <div class="price-reduce typo-body-12">
        <span>{{customNumberFormat(buyGmv)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="buyGmv === 0">
      <div class="price-stable typo-body-12">
        <span>{{buyGmv}}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>
