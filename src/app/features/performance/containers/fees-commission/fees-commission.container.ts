import { Component, Inject, On<PERSON><PERSON>roy, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { DestroyService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { OptionsRecommendationComponent } from '../../../recommendations/components/options-recommendation/options-recommendation.component';
import { take, takeUntil } from 'rxjs';
import { IFilterWorkPerformanceParam } from '../../models/performance';
import { resetFilterPerformance, resetPerformance, setFilterPerformance } from '../../stores/performance.actions';
import { Store } from '@ngrx/store';
import {
  selectFilterPerformance$,
  selectItemSelected$,
  selectSearchValueTextPerform$,
} from '../../stores/performance.selectors';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { IClickOnColumnEvent, IColumnConfig } from '@shared/models';
import { fakeData } from './fakedata';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { PerformanceFilterComponent } from '../../components/work-performance-filter/performance-filter.component';
import { Router } from '@angular/router';
import { convertDataForAdmin, updateOptionSelection } from '../../helpers/helpers';

/**
 * DepositCommissionContainer
 */
@Component({
  selector: 'app-fees-commission-container',
  templateUrl: './fees-commission.container.html',
  styleUrl: './fees-commission.container.scss',
})
export class FeesCommissionContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('accountTransaction', { static: true }) accountTransaction: TemplateRef<any> | null = null;
  @ViewChild('accountHaventTransaction', { static: true }) accountHaventTransaction: TemplateRef<any> | null = null;
  @ViewChild('sellGmv', { static: true }) sellGmv: TemplateRef<any> | null = null;
  @ViewChild('buyGmv', { static: true }) buyGmv: TemplateRef<any> | null = null;

  customNumberFormat = customNumberFormat;

  filterOptionsFee!: IFilterWorkPerformanceParam;
  tags = [
    `Số TK : ${customNumberFormat(407)}`,
    `GTGD: ${customNumberFormat(************)}`,
    `Doanh thu phí: ${customNumberFormat(************)}`,
  ];

  fakeData: any[] = fakeData;

  showCollapse: boolean = true;
  isAdminFee = true;
  isExpandedFee = false;
  /**
   *Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param store :Ngrx-store
   * @param router Router
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly store: Store,
    private readonly router: Router
  ) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);

    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        name: 'Ngày',
        width: 136,
        minWidth: 136,
        tag: 'date',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
      },
      {
        name: 'Tổng SL TK quản lý',
        minWidth: 30,
        width: 160,
        tag: 'accountManage',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return `${v} tài khoản`;
        },
        align: 'start',
      },
      {
        name: 'SL TK có giao dịch',
        minWidth: 30,
        width: 160,
        tag: 'accountHaveTransaction',
        cellTemplate: this.accountTransaction,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'SL TK không giao dịch',
        minWidth: 30,
        width: 160,
        tag: 'accountHaventTransaction',
        cellTemplate: this.accountHaventTransaction,
        isDisplay: true,
        align: 'start',
        resizable: true,
      },
      {
        name: 'Tổng GTGD',
        minWidth: 30,
        width: 140,
        tag: 'gmv',
        isDisplay: true,
        resizable: true,
        align: 'end',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
      },
      {
        name: 'GTGD (mua)',
        minWidth: 30,
        width: 140,
        tag: 'buyGmv',
        align: 'end',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.buyGmv,
      },
      {
        name: 'GTGD (bán)',
        minWidth: 30,
        width: 140,
        tag: 'sellGmv',
        cellTemplate: this.sellGmv,
        isDisplay: true,
        align: 'end',
        resizable: true,
      },
      {
        name: 'Tổng doanh thu phí',
        minWidth: 30,
        width: 160,
        tag: 'revenueFees',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        resizable: true,
        isDisplay: true,
        align: 'end',
      },
      {
        name: 'Net phí giao dịch',
        minWidth: 30,
        width: 140,
        tag: 'transactionFees',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        resizable: true,
        isDisplay: true,
        align: 'end',
      },
      {
        name: 'Hoa hồng MG',
        minWidth: 30,
        width: 120,
        tag: 'mgCommission',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        resizable: true,
        isDisplay: true,
        align: 'end',
      },
    ];

    const brokerColumn: IColumnConfig = {
      name: 'Môi giới',
      width: 160,
      minWidth: 30,
      tag: 'broker',
      isDisplay: true,
      resizable: true,
      disable: true,
      dragDisabled: true,
      pinned: 'left',
    };

    if (this.isAdminFee) {
      this.columnConfigs.splice(1, 0, brokerColumn);
      convertDataForAdmin(this.data);
      this.initialData = structuredClone(this.data);
    }

    this.store
      .select(selectFilterPerformance$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptionsFee = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchValueTextPerform$)
      .pipe(takeUntil(this._destroy))
      .subscribe((params) => {
        this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
    this.updateViewListFee();
    if (!this.isExpandedFee) {
      setTimeout(() => {
        if (this.data.length > 0) {
          this.addItemsAtMutipleLevel({ index: 0, items: this.data[0].children });
        }
        this.initialData = structuredClone(this.data);
      }, 0);
    }
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetPerformance());
  }

  /**
   *Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    const roomMgFee: string[] = [];
    const brokerFee: string[] = [];
    this.fakeData.forEach((item) => {
      item?.children.forEach((i: any) => {
        if (!roomMgFee.includes(i.name.replace('MG ', ''))) {
          roomMgFee.push(i.name.replace('MG ', ''));
          i.children?.forEach((d: any) => brokerFee.push(d.name));
        }
      });
    });

    if (tag === 'filter') {
      const ref = this.openFilter(PerformanceFilterComponent, {
        width: '400px',
        data: {
          filterOptions: this.filterOptionsFee,
          room: roomMgFee,
          brokerFee,
        },
      });
      ref
        .afterClosed()
        .pipe(take(1))
        .subscribe((v: any) => {
          if (!v) return;
          this.applyFilterFee(v);
        });
    }
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtonsFee() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreActionFee(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElementFee = html;
    this.popoverService.open({
      origin: originElementFee,
      content: OptionsRecommendationComponent,
      width: 184,
      hasBackdrop: true,
      position: 1,
      componentConfig: { status: element.status },
    });
  }

  /**
   * @param {any} data
   */
  applyFilterFee(data: any) {
    const { type, optionFilter } = data;
    if (type === 'save') {
      const { optionFilterRange, optionSelection } = optionFilter;

      this.store.dispatch(setFilterPerformance({ params: optionFilter }));

      const newListFilterFee = (
        this.isSearch
          ? updateOptionSelection(this.searchedData, optionSelection)
          : updateOptionSelection(this.initialData, optionSelection)
      ).filter((d) => {
        const dobDateFee = new Date(d.date.split('/').reverse().join('-')).getTime();
        const startDateFee = new Date(optionFilterRange.dateRange?.start ?? '').getTime();
        const endDateFee = new Date(optionFilterRange.dateRange?.end ?? '').getTime();

        return (
          (optionFilterRange.dateRange.start ? dobDateFee >= startDateFee : true) &&
          (optionFilterRange.dateRange.end ? dobDateFee <= endDateFee : true)
        );
      });

      newListFilterFee.forEach((item) => this.traverseNodeAddExpanded(item));
      let i = 0;
      while(i < newListFilterFee.length){
        const value = newListFilterFee[i];
        if (value.isExpanded) {
          const childrenCount = value.children ? value.children.length : 0;
          this.flattenMultipleLevelChildrenItems(newListFilterFee, i + 1, 0, value.children);
          i += childrenCount;
        }
        i ++;
      }
      this.data = newListFilterFee;
      this.filteredData = newListFilterFee;
    } else if (type === 'default') {
      this.isSearch
        ? this.store
            .select(selectSearchValueTextPerform$)
            .pipe(takeUntil(this._destroy))
            .subscribe((params) => {
              this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
            })
        : (() => {
            this.data = this.initialData;
          })();
      this.store.dispatch(resetFilterPerformance());
    }
  }

  /**
   * ShowInfoDetail
   * @param event
   * @returns {void} prevent event
   */
  showInfoDetailFee(event: IClickOnColumnEvent) {
    const { element, _event } = event;
    if (element.level === 0) return;
    if ((_event.target as HTMLElement).nodeName === 'svg' || (_event.target as HTMLElement).nodeName === 'path') return;
    this.router.navigate(['/job-performance/fees-commission/detail'], {
      queryParams: {
        id: element.id,
        date: element.date,
        name: element.broker,
      },
    });
  }

  /**
   * UpdateViewListFee
   */
  updateViewListFee() {
    this.store
      .select(selectItemSelected$)
      .pipe(take(1))
      .subscribe((v) => {
        if ((v && !v.id) || !v) return;
        const { date, name } = v;
        let originElementFee: any  = null;
        const findItemSelectedFee = (listData: any) => {
          for (const item of listData) {
            if (item.broker === name && item.date === date) {
              originElementFee = item;
              return;
            }
            if (item.children && item.children.length > 0) {
              findItemSelectedFee(item.children);
              if (originElementFee) return;
            }
          }
        };
        findItemSelectedFee(this.data);
        if (!originElementFee) return;

        let parentElementFee: any  = null;
        const getParentElementFee = (element: any) => {
          if (!element.parent) {
            parentElementFee = element;
            return;
          }
          getParentElementFee(element.parent);
        };
        getParentElementFee(originElementFee);
        const index = this.data.findIndex((t) => t.id === parentElementFee.id);
        if (!parentElementFee.isExpandedFee) {
          this.isExpandedFee = true;
          setTimeout(() => {
            this.addItemsAtMutipleLevel({ index: index, items: this.data[index].children });
          });
        }

        setTimeout(() => {
          const elementRefFee = document.querySelector(`[data-id="${originElementFee.id}"]`);
          if (elementRefFee) elementRefFee.scrollIntoView({ block: 'center', behavior: 'smooth' });
        }, 100);
      });
  }
}
