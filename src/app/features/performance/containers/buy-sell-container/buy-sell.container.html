<div class="buy-sell-container">
  <div class="header-buy-sell-container">
    <div class="left-box">
      <div class="typo-heading-9 text-header">{{'MES-258' | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info-buy typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[0]}}
        </div>
        <div class="box-info-buy typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[1]}}
        </div>
        <div class="box-info-buy typo-body-11"><img src="./assets/icons/table_sum.svg" alt="table_sum" /> {{tags[2]}}</div>
      </div>

    </div>
    <div class="right-box-buy">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventClickEmit$)="clickButton($event)"
        (eventUpdateValue$)="updateColumnValue($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter
        }"
      >
      </app-action-btn>
    </div>
  </div>
  <div class="table-view-container-buy">
    <sha-grid
      #grid
      [data]="data"
      class="table-custom-cls"
      [columnConfigs]="columnConfigs"
      [expandableIconPosition]="'broker'"
      (clickColumn)="clickOnColumn($event); showInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreAction($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
    >
    </sha-grid>
  </div>
</div>

<!-- Lệnh mua đã khớp, Lệnh mua chưa khớp -->
<ng-template #buyEntries let-buyEntries="templateInfo" let-element="element">
  @if(buyEntries){
  <div>
    <ng-container *ngIf="buyEntries > 0">
      <div class="typo-body-12 pice-increaser ">
        <span>{{buyEntries}} lệnh</span>
      </div>
    </ng-container>
    <ng-container *ngIf="buyEntries === 0">
      <div class="price-stable typo-body-12">
        <span>{{buyEntries}} lệnh</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>

  }
</ng-template>

<!-- Lệnh bán đã khớp, lệnh bán chưa khớp -->

<ng-template #sellEntries let-sellEntries="templateInfo" let-element="element">
  @if(sellEntries){
  <div>
    <ng-container *ngIf="sellEntries > 0">
      <div class="typo-body-12 price-reduce ">
        <span>{{sellEntries}} lệnh</span>
      </div>
    </ng-container>
    <ng-container *ngIf="sellEntries === 0">
      <div class="price-stable typo-body-12">
        <span>{{sellEntries}} lệnh</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>
  }
</ng-template>

<!-- GTGD(mua đã khớp) -->
<ng-template #matchBuyGmv let-matchBuyGmv="templateInfo" let-element="element">
  @if(matchSellGmv){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="matchBuyGmv > 0">
      <div class="typo-body-12 price-increase ">
        <span>{{customNumberFormat(matchBuyGmv)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="matchBuyGmv === 0">
      <div class="price-stable typo-body-12">
        <span>{{matchSellGmv}}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>

<!-- GTGD(bán) -->

<ng-template #matchSellGmv let-matchSellGmv="templateInfo" let-element="element">
  @if(matchSellGmv ){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="matchSellGmv > 0">
      <div class="typo-body-12 price-reduce ">
        <span>{{customNumberFormat(matchSellGmv)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="matchSellGmv === 0">
      <div class="price-stable typo-body-12">
        <span>{{matchSellGmv}}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>
