import { Component, Inject, OnD<PERSON>roy, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { DestroyService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { OptionsRecommendationComponent } from '../../../recommendations/components/options-recommendation/options-recommendation.component';
import { take, takeUntil } from 'rxjs';
import { Store } from '@ngrx/store';
import {
  selectFilterPerformance$,
  selectItemSelected$,
  selectSearchValueTextPerform$,
} from '../../stores/performance.selectors';
import { IFilterWorkPerformanceParam} from '../../models/performance';
import { resetFilterPerformance, resetPerformance, setFilterPerformance } from '../../stores/performance.actions';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { IClickOnColumnEvent, IColumnConfig } from '@shared/models';
import { fakeData } from './fakedata';
import { PerformanceFilterComponent } from '../../components/work-performance-filter/performance-filter.component';
import { Router } from '@angular/router';
import { convertDataForAdmin, updateOptionSelection } from '../../helpers/helpers';

/**
 * BuySellContainer
 */
@Component({
  selector: 'app-buy-sell-container',
  templateUrl: './buy-sell.container.html',
  styleUrl: './buy-sell.container.scss',
})
export class BuySellContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('buyEntries', { static: true }) buyEntries: TemplateRef<any> | null = null;
  @ViewChild('sellEntries', { static: true }) sellEntries: TemplateRef<any> | null = null;
  @ViewChild('matchBuyGmv', { static: true }) matchBuyGmv: TemplateRef<any> | null = null;
  @ViewChild('matchSellGmv', { static: true }) matchSellGmv: TemplateRef<any> | null = null;

  customNumberFormat = customNumberFormat;

  tags = [
    `Lệnh khớp: ${customNumberFormat(655)}`,
    `Lệnh chưa khớp: 118`,
    `GTGD chưa khớp: ${customNumberFormat(57000000000)}`,
  ];
  showCollapse: boolean = true;
  fakeDataBuySell: any[] = fakeData;

  filterOptionsBuySell!: IFilterWorkPerformanceParam;

  isAdminBySell = true;
  isExpandedBuy = false;
  /**
   *Constructor
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param store :Ngrx-store
   * @param router Router
   */
  constructor(
    private readonly _destroyBuySell: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly storeBuy: Store,
    private readonly router: Router
  ) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);
    this.data = this.fakeDataBuySell;
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }
  /**
   * The Oninit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeDataBuySell);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        name: 'Ngày',
        width: 136,
        minWidth: 136,
        resizable: true,
        tag: 'date',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
      },
      {
        name: 'TSL lệnh đã khớp',
        minWidth: 30,
        width: 140,
        displayValueFn: (v: number) => `${v} lệnh`,
        tag: 'numberMatchEntries',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Lệnh MUA đã khớp',
        minWidth: 30,
        tag: 'matchBuyEntries',
        width: 145,
        cellTemplate: this.buyEntries,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Lệnh Bán đã khớp',
        minWidth: 30,
        tag: 'matchSellEntries',
        width: 145,
        cellTemplate: this.sellEntries,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Tổng GTGD đã khớp',
        minWidth: 30,
        displayValueFn: (v) => customNumberFormat(v),
        width: 140,
        tag: 'matchGmv',
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'GTGD(mua đã khớp)',
        minWidth: 30,
        cellTemplate: this.matchBuyGmv,
        width: 140,
        tag: 'matchBuyGmv',
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'GTGD(bán đã khớp)',
        minWidth: 30,
        cellTemplate: this.matchSellGmv,
        width: 140,
        tag: 'matchSellGmv',
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'TSL lệnh chưa khớp',
        minWidth: 30,
        displayValueFn: (v: number) => `${v} lệnh`,
        width: 160,
        tag: 'numberNotMatchEntries',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Lệnh MUA chưa khớp',
        minWidth: 30,
        cellTemplate: this.buyEntries,
        width: 156,
        tag: 'numberNotMatchBuyEntries',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Lệnh BÁN chưa khớp',
        minWidth: 30,
        cellTemplate: this.sellEntries,
        width: 156,
        tag: 'numberNotMatchSellEntries',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Tổng GTGD chưa khớp',
        minWidth: 30,
        displayValueFn: (v: number) => customNumberFormat(v),
        width: 150,
        tag: 'notMatchGmv',
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'GTGD(mua chưa khớp)',
        minWidth: 30,
        cellTemplate: this.matchBuyGmv,
        width: 150,
        tag: 'notMatchBuyGmv',
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'GTGD(bán chưa khớp)',
        minWidth: 30,
        cellTemplate: this.matchSellGmv,
        width: 150,
        tag: 'notMatchSellGmv',
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
    ];

    const brokerColumnBuy: IColumnConfig = {
      name: 'Môi giới',
      tag: 'broker',
      width: 160,
      minWidth: 30,
      isDisplay: true,
      resizable: true,
      dragDisabled: true,
      disable: true,
      pinned: 'left',
    };

    if (this.isAdminBySell) {
      this.columnConfigs.splice(1, 0, brokerColumnBuy);
      convertDataForAdmin(this.data);
      this.initialData = structuredClone(this.data);
    }

    this.storeBuy
      .select(selectFilterPerformance$)
      .pipe(takeUntil(this._destroyBuySell))
      .subscribe((filter) => {
        this.filterOptionsBuySell = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.storeBuy
      .select(selectSearchValueTextPerform$)
      .pipe(takeUntil(this._destroyBuySell))
      .subscribe((params) => {
        this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
    this.updateViewList();
    if (!this.isExpandedBuy) {
      setTimeout(() => {
        if (this.data.length > 0) {
          this.addItemsAtMutipleLevel({ index: 0, items: this.data[0].children });
        }
        this.initialData = structuredClone(this.data);
      }, 0);
    }
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.storeBuy.dispatch(resetPerformance());
  }

  /**
   *Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    const roomMgBuy: string[] = [];
    const brokerBuy: string[] = [];
    this.fakeDataBuySell.forEach((item) => {
      item?.children.forEach((i: any) => {
        if (!roomMgBuy.includes(i.name.replace('MG ', ''))) {
          roomMgBuy.push(i.name.replace('MG ', ''));
          i.children?.forEach((d: any) => brokerBuy.push(d.name));
        }
      });
    });

    if (tag === 'filter') {
      const ref = this.openFilter(PerformanceFilterComponent, {
        width: '400px',
        data: {
          filterOptions: this.filterOptionsBuySell,
          room: roomMgBuy,
          brokerBuy,
        },
      });
      this.closeApplyFilter(ref);
    }
  }

  /**
   *  CloseApplyFilter
   * @param {any} ref ref
   */
  closeApplyFilter(ref: any) {
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        this.applyFilter(v);
      });
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }
  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsRecommendationComponent,
      width: 184,
      hasBackdrop: true,
      position: 1,
      componentConfig: { status: element.status },
    });
  }

  /**
   * @param {any} data
   */
  applyFilter(data: any) {
    const { type, optionFilter } = data;
    if (type === 'save') {
      const { optionFilterRange, optionSelection } = optionFilter;

      this.storeBuy.dispatch(setFilterPerformance({ params: optionFilter }));

      const newListFilterBuySell = (
        this.isSearch
          ? updateOptionSelection(this.searchedData, optionSelection)
          : updateOptionSelection(this.initialData, optionSelection)
      ).filter((d) => {
        const dobDateBuy = new Date(d.date.split('/').reverse().join('-')).getTime();
        const startDateBuy = new Date(optionFilterRange.dateRange?.start ?? '').getTime();
        const endDateBuy = new Date(optionFilterRange.dateRange?.end ?? '').getTime();

        return (
          (optionFilterRange.dateRange.start ? dobDateBuy >= startDateBuy : true) &&
          (optionFilterRange.dateRange.end ? dobDateBuy <= endDateBuy : true)
        );
      });
      newListFilterBuySell.forEach((item) => this.traverseNodeAddExpanded(item));

      for (let i = 0; i < newListFilterBuySell.length; i++) {
        const value = newListFilterBuySell[i];
        if (value.isExpandedBuy) {
          const childrenCount = value.children ? value.children.length : 0;
          this.flattenMultipleLevelChildrenItems(newListFilterBuySell, i + 1, 0, value.children);
          i += childrenCount;
        }
      }
      this.data = newListFilterBuySell;
      this.filteredData = newListFilterBuySell;
    } else if (type === 'default') {
      this.isSearch
        ? this.storeBuy
            .select(selectSearchValueTextPerform$)
            .pipe(takeUntil(this._destroyBuySell))
            .subscribe((params) => {
              this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
            })
        : (() => {
            this.data = this.initialData;
          })();

      this.storeBuy.dispatch(resetFilterPerformance());
    }
  }

  /**
   * ShowInfoDetail
   * @param event
   * @returns {void} prevent event
   */
  showInfoDetail(event: IClickOnColumnEvent) {
    const { element, _event } = event;
    if (element.level === 0) return;
    if ((_event.target as HTMLElement).nodeName === 'svg' || (_event.target as HTMLElement).nodeName === 'path') return;
    this.router.navigate(['/job-performance/buy-sell/detail'], {
      queryParams: {
        id: element.id,
        name: element.broker,
        date: element.date,
      },
    });
  }

  /**
   * UpdateViewList
   */
  updateViewList() {
    this.storeBuy
      .select(selectItemSelected$)
      .pipe(take(1))
      .subscribe((v) => {
        if ((v && !v.id) || !v) return;
        const { date, name } = v;
        let originElement: any  = null;
        const findItemSelected = (listData: any) => {
          for (const item of listData) {
            if (item.broker === name && item.date === date) {
              originElement = item;
              return;
            }
            if (item.children && item.children.length > 0) {
              findItemSelected(item.children);
              if (originElement) return;
            }
          }
        };
        findItemSelected(this.data);
        if (!originElement) return;

        let parentElement: any  = null;
        const getParentElement = (element: any) => {
          if (!element.parent) {
            parentElement = element;
            return;
          }
          getParentElement(element.parent);
        };
        getParentElement(originElement);
        const index = this.data.findIndex((t) => t.id === parentElement.id);
        if (!parentElement.isExpandedBuy) {
          this.isExpandedBuy = true;
          setTimeout(() => {
            this.addItemsAtMutipleLevel({ index: index, items: this.data[index].children });
          });
        }

        setTimeout(() => {
          const elementRef = document.querySelector(`[data-id="${originElement.id}"]`);
          if (elementRef) elementRef.scrollIntoView({ block: 'center', behavior: 'smooth' });
        }, 100);
      });
  }
}
