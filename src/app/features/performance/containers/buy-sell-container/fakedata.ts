export const fakeData: any[] = [
  {
    date: '05/06/2024',
    numberMatchEntries: 130,
    matchBuyEntries: 70,
    matchSellEntries: 60,
    matchGmv: 110000000000,
    matchBuyGmv: 50000000000,
    matchSellGmv: 60000000000,
    numberNotMatchEntries: 30,
    numberNotMatchBuyEntries: 20,
    numberNotMatchSellEntries: 10,
    notMatchGmv: 10000000000,
    notMatchBuyGmv: 50000000000,
    notMatchSellGmv: 60000000000,
    name: null,
    children: [
      {
        date: '05/06/2024',
        numberMatchEntries: 130,
        matchBuyEntries: 70,
        matchSellEntries: 60,
        matchGmv: 110000000000,
        matchBuyGmv: 50000000000,
        matchSellGmv: 60000000000,
        numberNotMatchEntries: 30,
        numberNotMatchBuyEntries: 20,
        numberNotMatchSellEntries: 10,
        notMatchGmv: 10000000000,
        notMatchBuyGmv: 50000000000,
        notMatchSellGmv: 60000000000,
        name: '<PERSON>òng MG 01',
        children: [
          {
            date: '05/06/2024',
            numberMatchEntries: 130,
            matchBuyEntries: 70,
            matchSellEntries: 60,
            matchGmv: 110000000000,
            matchBuyGmv: 50000000000,
            matchSellGmv: 60000000000,
            numberNotMatchEntries: 30,
            numberNotMatchBuyEntries: 20,
            numberNotMatchSellEntries: 10,
            notMatchGmv: 10000000000,
            notMatchBuyGmv: 50000000000,
            notMatchSellGmv: 60000000000,
            name: 'MG-01: Đinh Sỹ Dũng',
          },
        ],
      },
    ],
  },
  {
    date: '04/06/2024',
    numberMatchEntries: 120,
    matchBuyEntries: 55,
    matchSellEntries: 65,
    matchGmv: 120000000000,
    matchBuyGmv: 60000000000,
    matchSellGmv: 60000000000,
    numberNotMatchEntries: 25,
    numberNotMatchBuyEntries: 10,
    numberNotMatchSellEntries: 15,
    notMatchGmv: 11000000000,
    notMatchBuyGmv: 50000000000,
    notMatchSellGmv: 60000000000,
    name: null,
    children: [
      {
        date: '04/06/2024',
        numberMatchEntries: 120,
        matchBuyEntries: 55,
        matchSellEntries: 65,
        matchGmv: 120000000000,
        matchBuyGmv: 60000000000,
        matchSellGmv: 60000000000,
        numberNotMatchEntries: 25,
        numberNotMatchBuyEntries: 10,
        numberNotMatchSellEntries: 15,
        notMatchGmv: 11000000000,
        notMatchBuyGmv: 50000000000,
        notMatchSellGmv: 60000000000,
        name: 'Phòng MG 02',
        children: [
          {
            date: '04/06/2024',
            numberMatchEntries: 120,
            matchBuyEntries: 55,
            matchSellEntries: 65,
            matchGmv: 120000000000,
            matchBuyGmv: 60000000000,
            matchSellGmv: 60000000000,
            numberNotMatchEntries: 25,
            numberNotMatchBuyEntries: 10,
            numberNotMatchSellEntries: 15,
            notMatchGmv: 11000000000,
            notMatchBuyGmv: 50000000000,
            notMatchSellGmv: 60000000000,
            name: 'MG-02: Nguyễn Minh Chiến',
          },
          {
            date: '04/06/2024',
            numberMatchEntries: 120,
            matchBuyEntries: 55,
            matchSellEntries: 65,
            matchGmv: 120000000000,
            matchBuyGmv: 60000000000,
            matchSellGmv: 60000000000,
            numberNotMatchEntries: 25,
            numberNotMatchBuyEntries: 10,
            numberNotMatchSellEntries: 15,
            notMatchGmv: 11000000000,
            notMatchBuyGmv: 50000000000,
            notMatchSellGmv: 60000000000,
            name: 'MG-03: Phạm Văn Tây',
          },
        ],
      },
      {
        date: '04/06/2024',
        numberMatchEntries: 120,
        matchBuyEntries: 55,
        matchSellEntries: 65,
        matchGmv: 120000000000,
        matchBuyGmv: 60000000000,
        matchSellGmv: 60000000000,
        numberNotMatchEntries: 25,
        numberNotMatchBuyEntries: 10,
        numberNotMatchSellEntries: 15,
        notMatchGmv: 11000000000,
        notMatchBuyGmv: 50000000000,
        notMatchSellGmv: 60000000000,
        name: 'Phòng MG 05',
        children: [
          {
            date: '04/06/2024',
            numberMatchEntries: 120,
            matchBuyEntries: 55,
            matchSellEntries: 65,
            matchGmv: 120000000000,
            matchBuyGmv: 60000000000,
            matchSellGmv: 60000000000,
            numberNotMatchEntries: 25,
            numberNotMatchBuyEntries: 10,
            numberNotMatchSellEntries: 15,
            notMatchGmv: 11000000000,
            notMatchBuyGmv: 50000000000,
            notMatchSellGmv: 60000000000,
            name: 'MG-05: Trương Đăng Quang',
          },
          {
            date: '04/06/2024',
            numberMatchEntries: 120,
            matchBuyEntries: 55,
            matchSellEntries: 65,
            matchGmv: 120000000000,
            matchBuyGmv: 60000000000,
            matchSellGmv: 60000000000,
            numberNotMatchEntries: 25,
            numberNotMatchBuyEntries: 10,
            numberNotMatchSellEntries: 15,
            notMatchGmv: 11000000000,
            notMatchBuyGmv: 50000000000,
            notMatchSellGmv: 60000000000,
            name: 'MG-03: Phạm Văn Tây',
          },
        ],
      },
    ],
  },
  {
    date: '03/06/2024',
    numberMatchEntries: 125,
    matchBuyEntries: 45,
    matchSellEntries: 80,
    matchGmv: 105000000000,
    matchBuyGmv: 50000000000,
    matchSellGmv: 55000000000,
    numberNotMatchEntries: 18,
    numberNotMatchBuyEntries: 10,
    numberNotMatchSellEntries: 8,
    notMatchGmv: 12000000000,
    notMatchBuyGmv: 50000000000,
    notMatchSellGmv: 60000000000,
    name: null,
    children: [
      {
        date: '03/06/2024',
        numberMatchEntries: 130,
        matchBuyEntries: 70,
        matchSellEntries: 60,
        matchGmv: 110000000000,
        matchBuyGmv: 50000000000,
        matchSellGmv: 60000000000,
        numberNotMatchEntries: 30,
        numberNotMatchBuyEntries: 20,
        numberNotMatchSellEntries: 10,
        notMatchGmv: 10000000000,
        notMatchBuyGmv: 50000000000,
        notMatchSellGmv: 60000000000,
        name: 'Phòng MG 01',
        children: [
          {
            date: '03/06/2024',
            numberMatchEntries: 130,
            matchBuyEntries: 70,
            matchSellEntries: 60,
            matchGmv: 110000000000,
            matchBuyGmv: 50000000000,
            matchSellGmv: 60000000000,
            numberNotMatchEntries: 30,
            numberNotMatchBuyEntries: 20,
            numberNotMatchSellEntries: 10,
            notMatchGmv: 10000000000,
            notMatchBuyGmv: 50000000000,
            notMatchSellGmv: 60000000000,
            name: 'MG-01: Đinh Sỹ Dũng',
          },
        ],
      },
    ],
  },
  {
    date: '02/06/2024',
    numberMatchEntries: 150,
    matchBuyEntries: 80,
    matchSellEntries: 70,
    matchGmv: 125000000000,
    matchBuyGmv: 80000000000,
    matchSellGmv: 45000000000,
    numberNotMatchEntries: 22,
    numberNotMatchBuyEntries: 12,
    numberNotMatchSellEntries: 10,
    notMatchGmv: 15000000000,
    notMatchBuyGmv: 50000000000,
    notMatchSellGmv: 60000000000,
    name: null,
    children: [
      {
        date: '02/06/2024',
        numberMatchEntries: 150,
        matchBuyEntries: 80,
        matchSellEntries: 70,
        matchGmv: 125000000000,
        matchBuyGmv: 80000000000,
        matchSellGmv: 45000000000,
        numberNotMatchEntries: 22,
        numberNotMatchBuyEntries: 12,
        numberNotMatchSellEntries: 10,
        notMatchGmv: 15000000000,
        notMatchBuyGmv: 50000000000,
        notMatchSellGmv: 60000000000,
        name: 'Phòng MG 01',
        children: [
          {
            date: '02/06/2024',
            numberMatchEntries: 150,
            matchBuyEntries: 80,
            matchSellEntries: 70,
            matchGmv: 125000000000,
            matchBuyGmv: 80000000000,
            matchSellGmv: 45000000000,
            numberNotMatchEntries: 22,
            numberNotMatchBuyEntries: 12,
            numberNotMatchSellEntries: 10,
            notMatchGmv: 15000000000,
            notMatchBuyGmv: 50000000000,
            notMatchSellGmv: 60000000000,
            name: 'MG-01: Đinh Sỹ Dũng',
          },
        ],
      },
    ],
  },
  {
    date: '01/06/2024',
    numberMatchEntries: 130,
    matchBuyEntries: 65,
    matchSellEntries: 65,
    matchGmv: 110000000000,
    matchBuyGmv: 50000000000,
    matchSellGmv: 60000000000,
    numberNotMatchEntries: 23,
    numberNotMatchBuyEntries: 16,
    numberNotMatchSellEntries: 7,
    notMatchGmv: 9000000000,
    notMatchBuyGmv: 50000000000,
    notMatchSellGmv: 60000000000,
    name: null,
    children: [
      {
        date: '01/06/2024',
        numberMatchEntries: 130,
        matchBuyEntries: 65,
        matchSellEntries: 65,
        matchGmv: 110000000000,
        matchBuyGmv: 50000000000,
        matchSellGmv: 60000000000,
        numberNotMatchEntries: 23,
        numberNotMatchBuyEntries: 16,
        numberNotMatchSellEntries: 7,
        notMatchGmv: 9000000000,
        notMatchBuyGmv: 50000000000,
        notMatchSellGmv: 60000000000,
        name: 'Phòng MG 01',
        children: [
          {
            date: '01/06/2024',
            numberMatchEntries: 130,
            matchBuyEntries: 65,
            matchSellEntries: 65,
            matchGmv: 110000000000,
            matchBuyGmv: 50000000000,
            matchSellGmv: 60000000000,
            numberNotMatchEntries: 23,
            numberNotMatchBuyEntries: 16,
            numberNotMatchSellEntries: 7,
            notMatchGmv: 9000000000,
            notMatchBuyGmv: 50000000000,
            notMatchSellGmv: 60000000000,
            name: 'MG-01: Đinh Sỹ Dũng',
          },
        ],
      },
    ],
  },
];
