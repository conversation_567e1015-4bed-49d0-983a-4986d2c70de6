import { Component, Inject, On<PERSON><PERSON>roy, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { DestroyService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { OptionsRecommendationComponent } from '../../../recommendations/components/options-recommendation/options-recommendation.component';
import { take, takeUntil } from 'rxjs';
import { IFilterWorkPerformanceParam } from '../../models/performance';
import { resetFilterPerformance, resetPerformance, setFilterPerformance } from '../../stores/performance.actions';
import { Store } from '@ngrx/store';
import { selectFilterPerformance$, selectSearchValueTextPerform$ } from '../../stores/performance.selectors';
import { IColumnConfig } from '@shared/models';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { fakeData } from './fakedata';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { PerformanceFilterComponent } from '../../components/work-performance-filter/performance-filter.component';
import { convertDataForAdmin, updateOptionSelection } from '../../helpers/helpers';

/**
 * NavManagement
 */
@Component({
  selector: 'app-nav-management',
  templateUrl: './nav-management.container.html',
  styleUrl: './nav-management.container.scss',
})
export class NavManagementContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplateNav!: GridComponent<any>;

  @ViewChild('marginNav', { static: true }) marginNav: TemplateRef<any> | null = null;

  customNumberFormat = customNumberFormat;
  tags: string[] = [
    `SL TK quản lý: ${customNumberFormat(500)}`,
    `Tổng TS ròng (NAV) đầu ngày: ${customNumberFormat(49200000000)}`,
    `Tài sản ròng (NAV): ${customNumberFormat(54500000000)}`,
  ];

  fakeData: any[] = fakeData;
  filterOptionsNav!: IFilterWorkPerformanceParam;

  showCollapse: boolean = true;
  isAdminNav = true;
  /**
   *Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param store :Ngrx-store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly storeNav: Store
  ) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);
    this.data = this.fakeData;
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }
  /**
   * The OnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        name: 'Ngày',
        minWidth: 136,
        width: 136,
        dragDisabled: true,
        tag: 'date',
        isDisplay: true,
        resizable: true,
        pinned: 'left',
      },
      {
        name: 'Tổng SL TK quản lý',
        minWidth: 30,
        width: 150,
        tag: 'accountManage',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return `${v} tài khoản`;
        },
        align: 'start',
      },
      {
        name: 'Tổng TS ròng (NAV) đầu ngày',
        minWidth: 30,
        width: 190,
        tag: 'navEarlyDay',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return `${customNumberFormat(v)} `;
        },
        align: 'end',
      },
      {
        name: 'Tổng tài sản ròng (NAV)',
        minWidth: 30,
        width: 180,
        tag: 'nav',
        displayValueFn: (v: number) => {
          return `${customNumberFormat(v)} `;
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: '+/- NAV',
        minWidth: 30,
        width: 200,
        tag: 'marginNav',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.marginNav,
        align: 'start',
      },
      {
        name: 'TK có NAV 0 - 250tr',
        width: 168,
        minWidth: 30,
        tag: 'accountNavLevelOne',
        isDisplay: true,
        resizable: true,
        align: 'center',
        displayValueFn: (v: number) => {
          return `${customNumberFormat(v)} tài khoản `;
        },
        dynamicClass: (value) => {
          if (value < 100) {
            return 'margin-rate-cls';
          } else return 'margin-rate-100-cls';
        },
      },
      {
        name: 'TK có NAV 250tr - 1 tỷ ',
        minWidth: 30,
        tag: 'accountNavLevelTwo',
        width: 168,
        displayValueFn: (v: number) => {
          return `${customNumberFormat(v)} tài khoản `;
        },
        dynamicClass: (value) => {
          if (value < 100) {
            return 'margin-rate-cls';
          } else return 'margin-rate-100-cls';
        },
        align: 'center',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'TK có NAV 1 tỷ - 2,5 tỷ',
        minWidth: 30,
        isDisplay: true,
        width: 168,
        tag: 'accountNavLevelThree',
        displayValueFn: (v: number) => {
          return `${customNumberFormat(v)} tài khoản `;
        },
        dynamicClass: (value) => {
          if (value < 100) {
            return 'margin-rate-cls';
          } else return 'margin-rate-100-cls';
        },
        align: 'center',
        resizable: true,
      },
      {
        name: 'TK có NAV 2,5 tỷ - 5 tỷ',
        minWidth: 30,
        tag: 'accountNavLevelFour',
        width: 168,
        displayValueFn: (v: number) => {
          return `${customNumberFormat(v)} tài khoản `;
        },
        dynamicClass: (value) => {
          if (value < 100) {
            return 'margin-rate-cls';
          } else return 'margin-rate-100-cls';
        },
        align: 'center',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'TK có NAV > 5 tỷ',
        minWidth: 30,
        width: 168,
        isDisplay: true,
        tag: 'accountNavLevelFive',
        displayValueFn: (v: number) => {
          return `${customNumberFormat(v)} tài khoản `;
        },
        dynamicClass: (value) => {
          if (value < 100) {
            return 'margin-rate-cls';
          } else return 'margin-rate-100-cls';
        },
        align: 'center',
        resizable: true,
      },
    ];

    const brokerColumnNav: IColumnConfig = {
      name: 'Môi giới',
      width: 160,
      dragDisabled: true,
      minWidth: 30,
      isDisplay: true,

      tag: 'broker',
      resizable: true,
      pinned: 'left',
      disable: true,
    };

    if (this.isAdminNav) {
      this.columnConfigs.splice(1, 0, brokerColumnNav);
      convertDataForAdmin(this.data);
      this.initialData = structuredClone(this.data);
    }

    this.storeNav
      .select(selectFilterPerformance$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptionsNav = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.storeNav
      .select(selectSearchValueTextPerform$)
      .pipe(takeUntil(this._destroy))
      .subscribe((params) => {
        this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplateNav;
    setTimeout(() => {
      if (this.data.length > 0) {
        this.addItemsAtMutipleLevel({ items: this.data[0].children, index: 0  });
      }
      this.initialData = structuredClone(this.data);
    }, 0);
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.storeNav.dispatch(resetPerformance());
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    const roomMgNav: string[] = [];
    const brokerNav: string[] = [];
    this.fakeData.forEach((item) => {
      item?.children.forEach((i: any) => {
        if (!roomMgNav.includes(i.name.replace('MG ', ''))) {
          roomMgNav.push(i.name.replace('MG ', ''));
          i.children?.forEach((d: any) => brokerNav.push(d.name));
        }
      });
    });

    if (tag === 'filter') {
      const refNav = this.openFilter(PerformanceFilterComponent, {
        width: '400px',
        data: {
          filterOptions: this.filterOptionsNav,
          room: roomMgNav,
          brokerNav,
        },
      });
      this.closeApplyFilternav(refNav);
    }
  }

  /**
   *  CloseApplyFilternav
   * @param {any} ref ref
   */
  closeApplyFilternav(ref: any) {
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        this.applyFilterNav(v);
      });
  }

  /**
   * @param {any} data
   */
  applyFilterNav(data: any) {
    const { type, optionFilter } = data;
    if (type === 'save') {
      const { optionFilterRange, optionSelection } = optionFilter;

      this.storeNav.dispatch(setFilterPerformance({ params: optionFilter }));

      const newListFilterNav = (
        this.isSearch
          ? updateOptionSelection(this.searchedData, optionSelection)
          : updateOptionSelection(this.initialData, optionSelection)
      ).filter((d) => {
        const dobDateNav = new Date(d.date.split('/').reverse().join('-')).getTime();
        const startDateNav = new Date(optionFilterRange.dateRange?.start ?? '').getTime();
        const endDateNav = new Date(optionFilterRange.dateRange?.end ?? '').getTime();

        return (
          (optionFilterRange.dateRange.start ? dobDateNav >= startDateNav : true) &&
          (optionFilterRange.dateRange.end ? dobDateNav <= endDateNav : true)
        );
      });
      newListFilterNav.forEach((item) => this.traverseNodeAddExpanded(item));

      let i = 0;
      while( i < newListFilterNav.length) {
    const value = newListFilterNav[i];
        if (value.isExpanded) {
          const childrenCountNav = value.children ? value.children.length : 0;
          this.flattenMultipleLevelChildrenItems(newListFilterNav, i + 1, 0, value.children);
          i += childrenCountNav;
        }
        i ++;
      }
      this.data = newListFilterNav;
      this.filteredData = newListFilterNav;
    } else if (type === 'default') {
      this.isSearch
        ? this.storeNav
            .select(selectSearchValueTextPerform$)
            .pipe(takeUntil(this._destroy))
            .subscribe((params) => {
              this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
            })
        : (() => {
            this.data = this.initialData;
          })();

      this.storeNav.dispatch(resetFilterPerformance());
    }
  }

  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreActionNav(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsRecommendationComponent,
      width: 184,
      hasBackdrop: true,
      position: 1,
      componentConfig: { status: element.status },
    });
  }

  /**
   *
   */
  toggleButtonsNav() {
    this.showCollapse = !this.showCollapse;
  }

}
