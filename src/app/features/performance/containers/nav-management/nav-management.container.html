<div class="nav-management-container">
  <div class="header-nav-management-container">
    <div class="left-box">
      <div class="typo-heading-9 text-header">{{'MES-268' | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[0]}}
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[1]}}
        </div>
        <div class="box-info typo-body-11"><img src="./assets/icons/table_sum.svg" alt="table_sum" /> {{tags[2]}}</div>

        <!-- <div class="dropdown-btn" *ngIf="!showCollapse" (click)="toggleButtons()">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div> -->
      </div>
      <!-- <div class="collapse-btn" *ngIf="showCollapse" (click)="toggleButtons()">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div> -->
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
            'filter-mode-cls' : isFilter
          }"
      >
      </app-action-btn>
    </div>
  </div>

  <!-- <div [ngClass]="{'hidden': !showCollapse}">
    <app-slide-tag [tags]="tags" [classParent]="'nav-management-container'"></app-slide-tag>
  </div> -->

  <div class="table-view-container">
    <sha-grid
      #grid
      class="table-custom-cls"
      [columnConfigs]="columnConfigs"
      [data]="data"
      [expandableIconPosition]="'broker'"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreActionNav($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
    >
    </sha-grid>
  </div>
</div>

<ng-template #marginNav let-marginNav="templateInfo">
  @if(marginNav) {
  <div>
    <ng-container *ngIf="marginNav.percentMargin > 0">
      <div class="price-increase typo-body-12">
        <img src="./assets/icons/up.svg" alt="document-logo" />
        <span class="typo-body-12"
          >+{{ marginNav.numberMargin | numberFormat}} +{{ marginNav.percentMargin | numberFormat : 'percent' }}</span
        >
      </div>
    </ng-container>
    <ng-container *ngIf="marginNav.percentMargin < 0 ">
      <div class="price-reduce typo-body-12">
        <img src="./assets/icons/down.svg" alt="document-logo" />
        <span class="typo-body-12"
          >{{ marginNav.numberMargin | numberFormat }} {{ marginNav.percentMargin | numberFormat : 'percent' }}</span
        >
      </div>
    </ng-container>
    <ng-container *ngIf="marginNav.percentMargin === 0">
      <div class="price-stable typo-body-12">
        <img src="./assets/icons/minus.svg" alt="document-logo" />
        <span class="typo-body-12"
          >{{ marginNav.numberMargin | numberFormat }} - {{ marginNav.percentMargin | numberFormat : 'percent' }}</span
        >
      </div>
    </ng-container>
  </div>
  } @else {
  <div>-</div>
  }
</ng-template>
