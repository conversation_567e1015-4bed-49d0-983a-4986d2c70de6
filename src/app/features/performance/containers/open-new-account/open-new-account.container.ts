import { Component, Inject, On<PERSON><PERSON>roy, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { DestroyService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { OptionsRecommendationComponent } from '../../../recommendations/components/options-recommendation/options-recommendation.component';
import { IFilterWorkPerformanceParam } from '../../models/performance';
import { take, takeUntil } from 'rxjs';
import { Store } from '@ngrx/store';
import { resetFilterPerformance, resetItemSelected, setFilterPerformance } from '../../stores/performance.actions';
import {
  selectFilterPerformance$,
  selectItemSelected$,
  selectSearchValueTextPerform$,
} from '../../stores/performance.selectors';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { IClickOnColumnEvent, IColumnConfig } from '@shared/models';
import { fakeData } from './fakedata';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { PerformanceFilterComponent } from '../../components/work-performance-filter/performance-filter.component';
import { Router } from '@angular/router';
import { convertDataForAdmin, updateOptionSelection } from '../../helpers/helpers';

/**
 * OpenNewAccountContainer
 */
@Component({
  selector: 'app-open-new-account',
  templateUrl: './open-new-account.container.html',
  styleUrl: './open-new-account.container.scss',
})
export class OpenNewAccountContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('approved', { static: true }) approved: TemplateRef<any> | null = null;
  @ViewChild('unApproved', { static: true }) unApproved: TemplateRef<any> | null = null;

  tags: string[] = [
    'TK 00 mở mới: 5',
    'TK 00 mở  mới (đã duyệt): 5',
    'TK 00 mở mới (chưa duyệt): 5',
    'TK 01 mở mới: 7',
    'TK 01 mở mới (đã duyệt): 5',
    'TK 01 mở mới (chưa duyệt): 2',
    'TK 80 mở mới: 4',
    'TK 80 mở mới (đã duyệt): 3',
    'TK 80 mở mới (chưa duyệt): 1',
  ];
  showCollapse: boolean = true;

  customNumberFormat = customNumberFormat;
  fakeDataOpenNewAccount: any[] = fakeData;

  filterOptionsOpenNewAccount!: IFilterWorkPerformanceParam;

  isAdminOpenNewAccount = true;
  isExpandedOpenNewAccount = false;

  /**
   *Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param store :Ngrx-store
   * @param router Router
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly store: Store,
    private readonly router: Router
  ) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);
    this.data = this.fakeDataOpenNewAccount;
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeDataOpenNewAccount);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        name: 'Ngày',
        minWidth: 136,
        width: 136,
        dragDisabled: true,
        tag: 'date',
        isDisplay: true,
        resizable: true,
        pinned: 'left',
      },
      {
        name: 'Tổng SL 00 mở mới',
        minWidth: 30,
        width: 180,
        tag: 'new00Account',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return `${v} tài khoản`;
        },
        align: 'start',
      },

      {
        name: 'SL TK 00 mở mới (đã duyệt) ',
        minWidth: 30,
        width: 180,
        tag: 'new00AccountApproved',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.approved,
        align: 'start',
      },
      {
        name: 'SL TK 00 mở mới (chưa duyệt) ',
        minWidth: 30,
        width: 200,
        tag: 'new00AccountUnapproved',
        cellTemplate: this.unApproved,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Tổng SL 01 mở mới',
        minWidth: 30,
        width: 160,
        tag: 'new01Account',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return `${v} tài khoản`;
        },
        align: 'start',
      },
      {
        name: 'SL TK 01 mở mới (đã duyệt) ',
        minWidth: 30,
        width: 180,
        tag: 'new01AccountApproved',
        cellTemplate: this.approved,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'SL TK 01 mở mới (chưa duyệt) ',
        minWidth: 30,
        width: 206,
        tag: 'new01AccountUnapproved',
        cellTemplate: this.unApproved,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Tổng SL 80 mở mới',
        minWidth: 30,
        width: 160,
        tag: 'new80Account',
        isDisplay: true,
        resizable: true,
        align: 'start',
        displayValueFn: (v: number) => {
          return `${v} tài khoản`;
        },
      },
      {
        name: 'SL TK 80 mở mới (đã duyệt) ',
        minWidth: 30,
        width: 190,
        tag: 'new80AccountApproved',
        cellTemplate: this.approved,
        isDisplay: true,
        align: 'start',
        resizable: true,
      },
      {
        name: 'SL TK 80 mở mới (chưa duyệt) ',
        minWidth: 30,
        width: 190,
        tag: 'new80AccountUnapproved',
        cellTemplate: this.unApproved,
        isDisplay: true,
        align: 'start',
        resizable: true,
      },
    ];

    const brokerColumn: IColumnConfig = {
      name: 'Môi giới',
      width: 160,
      minWidth: 30,
      tag: 'broker',
      isDisplay: true,
      resizable: true,
      dragDisabled: true,
      pinned: 'left',
      disable: true,
    };

    if (this.isAdminOpenNewAccount) {
      this.columnConfigs.splice(1, 0, brokerColumn);
      convertDataForAdmin(this.data);
      this.initialData = structuredClone(this.data);
    }

    this.store
      .select(selectFilterPerformance$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptionsOpenNewAccount = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchValueTextPerform$)
      .pipe(takeUntil(this._destroy))
      .subscribe((params) => {
        this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
    this.updateViewListOpenNewAccount();
    if (!this.isExpandedOpenNewAccount) {
      setTimeout(() => {
        if (this.data.length > 0) {
          this.addItemsAtMutipleLevel({ index: 0, items: this.data[0].children });
        }
        this.initialData = structuredClone(this.data);
      }, 0);
    }
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetItemSelected());
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    const roomMgOpenNewAccount: string[] = [];
    const brokerOpenNewAccount: string[] = [];
    this.fakeDataOpenNewAccount.forEach((item) => {
      item?.children.forEach((i: any) => {
        if (!roomMgOpenNewAccount.includes(i.name.replace('MG ', ''))) {
          roomMgOpenNewAccount.push(i.name.replace('MG ', ''));
          i.children?.forEach((d: any) => brokerOpenNewAccount.push(d.name));
        }
      });
    });

    if (tag === 'filter') {
      const ref = this.openFilter(PerformanceFilterComponent, {
        width: '400px',
        data: {
          filterOptions: this.filterOptionsOpenNewAccount,
          room: roomMgOpenNewAccount,
          brokerOpenNewAccount,
        },
      });
      this.closeApplyFilterOpenNewAccount(ref);
    }
  }

  /**
   *  CloseApplyFilterOpenNewAccount
   * @param {any} ref ref
   */
  closeApplyFilterOpenNewAccount(ref: any) {
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        this.applyFilterOpenNewAccount(v);
      });
  }

  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreActionOpenNewAccount(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsRecommendationComponent,
      width: 184,
      hasBackdrop: true,
      position: 1,
      componentConfig: { status: element.status },
    });
  }

  /**
   * @param {any} data
   */
  applyFilterOpenNewAccount(data: any) {
    const { type, optionFilter } = data;
    if (type === 'save') {
      const { optionFilterRange, optionSelection } = optionFilter;
      this.store.dispatch(setFilterPerformance({ params: optionFilter }));

      const newListFilterOpenNewAccount = (
        this.isSearch
          ? updateOptionSelection(this.searchedData, optionSelection)
          : updateOptionSelection(this.initialData, optionSelection)
      ).filter((d) => {
        const dobDateOpenNewAccount = new Date(d.date.split('/').reverse().join('-')).getTime();
        const startDateOpenNewAccount = new Date(optionFilterRange.dateRange?.start ?? '').getTime();
        const endDateOpenNewAccount = new Date(optionFilterRange.dateRange?.end ?? '').getTime();

        return (
          (optionFilterRange.dateRange.start ? dobDateOpenNewAccount >= startDateOpenNewAccount : true) &&
          (optionFilterRange.dateRange.end ? dobDateOpenNewAccount <= endDateOpenNewAccount : true)
        );
      });
      newListFilterOpenNewAccount.forEach((item) => this.traverseNodeAddExpanded(item));

      for (let i = 0; i < newListFilterOpenNewAccount.length; i++) {
        const value = newListFilterOpenNewAccount[i];
        if (value.isExpanded) {
          const childrenCount = value.children ? value.children.length : 0;
          this.flattenMultipleLevelChildrenItems(newListFilterOpenNewAccount, i + 1, 0, value.children);
          i += childrenCount;
        }
      }
      this.data = newListFilterOpenNewAccount;
      this.filteredData = newListFilterOpenNewAccount;
    } else if (type === 'default') {
      this.isSearch
        ? this.store
            .select(selectSearchValueTextPerform$)
            .pipe(takeUntil(this._destroy))
            .subscribe((params) => {
              this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
            })
        : (() => {
            this.data = this.initialData;
          })();

      this.store.dispatch(resetFilterPerformance());
    }
  }

  /**
   * ToggleButtons
   */
  toggleButtonsOpenNewAccount() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * ShowInfoDetail
   * @param event
   * @returns {void} prevent event
   */
  showInfoDetailOpenNewAccount(event: IClickOnColumnEvent) {
    const { element, _event } = event;
    if (element.level === 0) return;
    if ((_event.target as HTMLElement).nodeName === 'svg' || (_event.target as HTMLElement).nodeName === 'path') return;
    this.router.navigate(['/job-performance/open-new-account/detail'], {
      queryParams: {
        id: element.id,
        name: element.broker,
        date: element.date,
      },
    });
  }

  /**
   * UpdateViewList
   */
  updateViewListOpenNewAccount() {
    this.store
      .select(selectItemSelected$)
      .pipe(take(1))
      .subscribe((v) => {
        if ((v && !v.id) || !v) return;
        const { date, name } = v;
        let originElement: any  = null;
        const findItemSelectedOpenNewAccount = (listData: any) => {
          for (const itemOpewnNewAccount of listData) {
            if (itemOpewnNewAccount.broker === name && itemOpewnNewAccount.date === date) {
              originElement = itemOpewnNewAccount;
              return;
            }
            if (itemOpewnNewAccount.children && itemOpewnNewAccount.children.length > 0) {
              findItemSelectedOpenNewAccount(itemOpewnNewAccount.children);
              if (originElement) return;
            }
          }
        };
        findItemSelectedOpenNewAccount(this.data);
        if (!originElement) return;

        let parentElementOpenNewAccount: any  = null;
        const getParentElement = (element: any) => {
          if (!element.parent) {
            parentElementOpenNewAccount = element;
            return;
          }
          getParentElement(element.parent);
        };
        getParentElement(originElement);
        const index = this.data.findIndex((t) => t.id === parentElementOpenNewAccount.id);
        if (!parentElementOpenNewAccount.isExpanded) {
          this.isExpandedOpenNewAccount = true;
          setTimeout(() => {
            this.addItemsAtMutipleLevel({ index: index, items: this.data[index].children });
          });
        }

        setTimeout(() => {
          const elementRefOpenNewAccount = document.querySelector(`[data-id="${originElement.id}"]`);
          if (elementRefOpenNewAccount) elementRefOpenNewAccount.scrollIntoView({ block: 'center', behavior: 'smooth' });
        }, 100);
      });
  }
}
