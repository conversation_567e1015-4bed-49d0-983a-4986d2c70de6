<div class="new-open-account-container">
  <div class="header-new-open-account-container">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-266' | translate}}</div>
      <div class="number-info-cls" *ngIf="!showCollapse">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[0]}}
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[1]}}
        </div>
        <div class="box-info typo-body-11"><img src="./assets/icons/table_sum.svg" alt="table_sum" /> {{tags[2]}}</div>

        <div class="dropdown-btn" *ngIf="!showCollapse" (click)="toggleButtonsOpenNewAccount()">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div>
      </div>
      <div class="collapse-btn" *ngIf="showCollapse" (click)="toggleButtonsOpenNewAccount()">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter
        }"
      >
      </app-action-btn>
    </div>
  </div>

  <div [ngClass]="{'hidden': !showCollapse}">
    <app-slide-tag [tags]="tags" [classParent]="'new-open-account-container'"></app-slide-tag>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      class="table-custom-cls"
      [columnConfigs]="columnConfigs"
      [data]="data"
      [expandableIconPosition]="'broker'"
      (clickColumn)="clickOnColumn($event);showInfoDetailOpenNewAccount($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreActionOpenNewAccount($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
    >
    </sha-grid>
  </div>
</div>

<!-- TK đã duyệt -->
<ng-template #approved let-approved="templateInfo" let-element="element">
  @if(approved || approved === 0){
  <div>
    <ng-container>
      <div class="price-increase typo-body-12">
        <span>{{approved}} tài khoản</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>

  }
</ng-template>

<!-- Tk chưa duyệt   -->
<ng-template #unApproved let-unApproved="templateInfo" let-element="element">
  @if(unApproved || unApproved === 0){
  <div>
    <ng-container>
      <div class="price-stable typo-body-12">
        <span>{{unApproved}} tài khoản</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>

  }
</ng-template>
