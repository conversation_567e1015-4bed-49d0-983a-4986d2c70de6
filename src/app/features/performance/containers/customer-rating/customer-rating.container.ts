import { Component, Inject, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { deepClone } from 'src/app/shared/utils/utils';

/**
 * Xêp hạng khách hàng
 */
@Component({
  selector: 'app-customer-rating',
  templateUrl: './customer-rating.container.html',
  styleUrl: './customer-rating.container.scss',
})
export class CustomerRatingContainer extends BaseTableComponent<any> implements OnInit {
  @ViewChild('total', { static: true }) total: TemplateRef<any> | null = null;

  customNumberFormat = customNumberFormat;
  showCollapse: boolean = false;

  TotalCustomerRatingData = {
    account: 4,
    totalNetAssets: **********,
    totalTransactionValue: **********,
    totalFeeRevenue: **********,
    netTransactionFees: **********,
    brokerageCommission: **********,
  };
  tags: string[] = [];

  fakeData: any = [
    {
      accountNumber: '069C-125485',
      customerName: 'Phạm Thị Thu Trang',
      totalNetAssets: {
        number: *********,
        percent: 80,
      },
      totalTransactionValue: {
        number: *********,
        percent: 25,
      },
      totalFeeRevenue: {
        number: *********,
        percent: 45.67,
      },
      netTransactionFees: {
        number: *********,
        percent: 60,
      },
      brokerageCommission: {
        number: *********,
        percent: 25,
      },
    },
    {
      accountNumber: '069C-586547',
      customerName: 'Đặng Hoàng An Nhiên',
      totalNetAssets: {
        number: *********,
        percent: 10,
      },
      totalTransactionValue: {
        number: *********,
        percent: 25,
      },
      totalFeeRevenue: {
        number: *********,
        percent: 14.4,
      },
      netTransactionFees: {
        number: *********,
        percent: 20,
      },
      brokerageCommission: {
        number: *********,
        percent: 25,
      },
    },
    {
      accountNumber: '069C-400190',
      customerName: 'Ngô Thị Hằng',
      totalNetAssets: {
        number: ********,
        percent: 5,
      },
      totalTransactionValue: {
        number: *********,
        percent: 25,
      },
      totalFeeRevenue: {
        number: *********,
        percent: 20,
      },
      netTransactionFees: {
        number: *********,
        percent: 10,
      },
      brokerageCommission: {
        number: *********,
        percent: 25,
      },
    },
    {
      accountNumber: '069C-918882',
      customerName: 'Phạm Tiến Nam Phương',
      totalNetAssets: {
        number: ********,
        percent: 5,
      },
      totalTransactionValue: {
        number: *********,
        percent: 25,
      },
      totalFeeRevenue: {
        number: *********,
        percent: 20,
      },
      netTransactionFees: {
        number: *********,
        percent: 10,
      },
      brokerageCommission: {
        number: *********,
        percent: 25,
      },
    },
  ];

  /**
   * Constructor
   * @param popoverRef : PopoverRef
   */
  constructor(@Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.data = deepClone(this.fakeData);
    this.initialData = deepClone(this.data);

    const cellTemplate = this.total;

    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 136,
        width: 136,
        tag: 'accountNumber',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        align: 'start',
      },
      {
        name: 'Tên khách hàng',
        minWidth: 200,
        width: 200,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => v,
        dynamicClass: (v) => v,
        align: 'start',
      },
      {
        name: 'Tổng TS ròng (NAV)',
        minWidth: 300,
        width: 300,
        tag: 'totalNetAssets',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
      {
        name: 'Tổng GTGD',
        minWidth: 300,
        width: 300,
        tag: 'totalTransactionValue',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
      {
        name: 'Tổng doanh thu phí',
        minWidth: 300,
        width: 300,
        tag: 'totalFeeRevenue',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
      {
        name: 'Net phí giao dịch',
        minWidth: 300,
        width: 300,
        tag: 'netTransactionFees',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
      {
        name: 'Hoa hồng MG',
        minWidth: 300,
        width: 300,
        tag: 'brokerageCommission',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
    ];

    const tags = this.TotalCustomerRatingData;
    const { account, totalNetAssets, totalTransactionValue, totalFeeRevenue, netTransactionFees, brokerageCommission } =
      tags;
    this.tags = [
      `${account} Tài khoản`,
      `TS ròng (NAV): ${customNumberFormat(totalNetAssets)}`,
      `Tổng GTGD: ${customNumberFormat(totalTransactionValue)}`,
      `Tổng doanh thu phí:  ${customNumberFormat(totalFeeRevenue)}`,
      `Net phí giao dịch:  ${customNumberFormat(netTransactionFees)}`,
      `Hoa hồng MG: ${customNumberFormat(brokerageCommission)}`,
    ];
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    console.log('open Filter');
  }

  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreAction(event: { html: HTMLElement; element: any }) {
    console.log(event);
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }
}
