.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.customer-rating-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-customer-rating {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;

      app-action-btn {
        &.not-accept-save-cls {
          ::ng-deep {
            #box-id--save {
              opacity: 0.5;
            }
          }
        }
        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }

  .tab-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
    height: 56px;
    overflow: hidden;
    transition: transform 0.5s ease;
    white-space: nowrap;
    text-wrap: nowrap;
    padding: 12px 0px;
    margin: 0 12px;
    position: relative;

    .tab {
      width: 100%;
      display: inline-block;
      vertical-align: top;

      .box-info {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 16px;
        background-color: #f8fafd;
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
        color: #808080;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        white-space: nowrap;
        text-wrap: nowrap;
      }
    }
  }

  // Hide the tabContainer
  .hidden {
    display: none;
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
      }
    }
  }
}

// Template
.wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 8px;

  .number {
    min-width: 80px;
    max-width: 150px;
  }

  .range {
    width: 120px;
    height: 20px;
    position: relative;

    .children {
      position: absolute;
      top: 0;
      left: 0;
      background-color: var(--color--accents--green);
      height: 100%;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
}
