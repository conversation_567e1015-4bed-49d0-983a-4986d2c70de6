<div class="customer-rating-container">
  <div class="header-customer-rating">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-252' | translate}}</div>
      <div class="number-info-cls" *ngIf="!showCollapse">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[0]}}
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[1]}}
        </div>
        <div class="box-info typo-body-11"><img src="./assets/icons/table_sum.svg" alt="table_sum" /> {{tags[2]}}</div>
        <div class="dropdown-btn" *ngIf="!showCollapse" (click)="toggleButtons()">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div>
      </div>

      <div class="collapse-btn" *ngIf="showCollapse" (click)="toggleButtons()">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>

  <div [ngClass]="{'hidden': !showCollapse}">
    <app-slide-tag [tags]="tags" [classParent]="'asset-info-container'"></app-slide-tag>
  </div>

  <div class="table-view-container">
    <sha-grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls"
    >
    </sha-grid>
  </div>
</div>

<ng-template #total let-total="templateInfo" let-element="element">
  <ng-container>
    <div class="wrapper">
      <div class="number typo-body-12">{{ customNumberFormat(total.number) }}</div>
      <div class="range">
        <div class="children" [ngStyle]="{ 'width': total.percent + '%' }"></div>
      </div>
      <div class="typo-body-12">{{ customNumberFormat(total.percent) }}%</div>
    </div>
  </ng-container>
</ng-template>
