export const fakeData: any[] = [
  {
    date: '05/06/2024',
    approvedPayment: **********,
    approvedWithdrawalAmount: ***********,
    newAccountsOpened: 5,
    accountManage: 130,
    accountHaveTransaction: 38,
    accountHaventTransaction: 71,
    nav: **********00,
    gmv: 1**********0,
    buyGmv: ***********,
    sellGmv: ***********,
    revenueFees: ************,
    transactionFees: *********,
    mgCommission: ********,
    name: null,
    children: [
      {
        date: '05/06/2024',
        approvedPayment: **********,
        approvedWithdrawalAmount: ***********,
        newAccountsOpened: 5,
        accountManage: 130,
        accountHaveTransaction: 38,
        accountHaventTransaction: 71,
        nav: **********00,
        gmv: 1**********0,
        buyGmv: ***********,
        sellGmv: ***********,
        revenueFees: ************,
        transactionFees: *********,
        mgCommission: ********,
        name: '<PERSON>òng MG 01',
        children: [
          {
            date: '05/06/2024',
            approvedPayment: **********,
            approvedWithdrawalAmount: ***********,
            newAccountsOpened: 5,
            accountManage: 130,
            accountHaveTransaction: 38,
            accountHaventTransaction: 71,
            nav: **********00,
            gmv: 1**********0,
            buyGmv: ***********,
            sellGmv: ***********,
            revenueFees: ************,
            transactionFees: *********,
            mgCommission: ********,
            name: 'MG-01: Đinh Sỹ Dũng',
          },
          {
            date: '05/06/2024',
            approvedPayment: **********,
            approvedWithdrawalAmount: ***********,
            newAccountsOpened: 5,
            accountManage: 130,
            accountHaveTransaction: 38,
            accountHaventTransaction: 71,
            nav: **********00,
            gmv: 1**********0,
            buyGmv: ***********,
            sellGmv: ***********,
            revenueFees: ************,
            transactionFees: *********,
            mgCommission: ********,
            name: 'MG-04: Nguyễn Hoàng Cảnh',
          },
        ],
      },
      {
        date: '05/06/2024',
        approvedPayment: **********,
        approvedWithdrawalAmount: ***********,
        newAccountsOpened: 5,
        accountManage: 130,
        accountHaveTransaction: 38,
        accountHaventTransaction: 71,
        nav: **********00,
        gmv: 1**********0,
        buyGmv: ***********,
        sellGmv: ***********,
        revenueFees: ************,
        transactionFees: *********,
        mgCommission: ********,
        name: 'Phòng MG 02',
      },
    ],
  },
  {
    date: '04/06/2024',
    approvedPayment: **********,
    approvedWithdrawalAmount: **********0,
    newAccountsOpened: 10,
    accountManage: 125,
    accountHaveTransaction: 36,
    accountHaventTransaction: 68,
    nav: **********00,
    gmv: 1**********0,
    buyGmv: ***********,
    sellGmv: ***********,
    revenueFees: ************,
    transactionFees: *********,
    mgCommission: ********,
    name: null,
    children: [
      {
        date: '04/06/2024',
        approvedPayment: **********,
        approvedWithdrawalAmount: **********0,
        newAccountsOpened: 10,
        accountManage: 125,
        accountHaveTransaction: 36,
        accountHaventTransaction: 68,
        nav: **********00,
        gmv: 1**********0,
        buyGmv: ***********,
        sellGmv: ***********,
        revenueFees: ************,
        transactionFees: *********,
        mgCommission: ********,
        name: 'Phòng MG 07',
        children: [
          {
            date: '04/06/2024',
            approvedPayment: **********,
            approvedWithdrawalAmount: **********0,
            newAccountsOpened: 10,
            accountManage: 125,
            accountHaveTransaction: 36,
            accountHaventTransaction: 68,
            nav: **********00,
            gmv: 1**********0,
            buyGmv: ***********,
            sellGmv: ***********,
            revenueFees: ************,
            transactionFees: *********,
            mgCommission: ********,
            name: 'MG-05: Nguyễn Tuấn Dương',
          },
        ],
      },
      {
        date: '04/06/2024',
        approvedPayment: **********,
        approvedWithdrawalAmount: **********0,
        newAccountsOpened: 10,
        accountManage: 125,
        accountHaveTransaction: 36,
        accountHaventTransaction: 68,
        nav: **********00,
        gmv: 1**********0,
        buyGmv: ***********,
        sellGmv: ***********,
        revenueFees: ************,
        transactionFees: *********,
        mgCommission: ********,
        name: 'Phòng MG 06',
        chidren: [
          {
            date: '04/06/2024',
            approvedPayment: **********,
            approvedWithdrawalAmount: **********0,
            newAccountsOpened: 10,
            accountManage: 125,
            accountHaveTransaction: 36,
            accountHaventTransaction: 68,
            nav: **********00,
            gmv: 1**********0,
            buyGmv: ***********,
            sellGmv: ***********,
            revenueFees: ************,
            transactionFees: *********,
            mgCommission: ********,
            name: 'MG-10: Lê Ngọc Hà',
          },
        ],
      },
    ],
  },
  {
    date: '03/06/2024',
    approvedPayment: 1**********,
    approvedWithdrawalAmount: **********,
    newAccountsOpened: 2,
    accountManage: 115,
    accountHaveTransaction: 33,
    accountHaventTransaction: 72,
    nav: **********00,
    gmv: 1**********0,
    buyGmv: ***********,
    sellGmv: ***********,
    revenueFees: ************,
    transactionFees: *********,
    mgCommission: ********,
    name: null,
    children: [
      {
        date: '03/06/2024',
        approvedPayment: **********,
        approvedWithdrawalAmount: **********0,
        newAccountsOpened: 10,
        accountManage: 125,
        accountHaveTransaction: 36,
        accountHaventTransaction: 68,
        nav: **********00,
        gmv: 1**********0,
        buyGmv: ***********,
        sellGmv: ***********,
        revenueFees: ************,
        transactionFees: *********,
        mgCommission: ********,
        name: 'Phòng MG 07',
        chidren: [
          {
            date: '03/06/2024',
            approvedPayment: **********,
            approvedWithdrawalAmount: **********0,
            newAccountsOpened: 10,
            accountManage: 125,
            accountHaveTransaction: 36,
            accountHaventTransaction: 68,
            nav: **********00,
            gmv: 1**********0,
            buyGmv: ***********,
            sellGmv: ***********,
            revenueFees: ************,
            transactionFees: *********,
            mgCommission: ********,
            name: 'MG-05: Nguyễn Tuấn Dương',
          },
        ],
      },
    ],
  },
  {
    date: '02/06/2024',
    approvedPayment: 15000000000,
    approvedWithdrawalAmount: 3000000000,
    newAccountsOpened: 1,
    accountManage: 113,
    accountHaveTransaction: 32,
    accountHaventTransaction: 65,
    nav: **********00,
    gmv: 1**********0,
    buyGmv: ***********,
    sellGmv: ***********,
    revenueFees: ************,
    transactionFees: *********,
    mgCommission: ********,
    name: null,
    children: [
      {
        date: '02/06/2024',
        approvedPayment: 15000000000,
        approvedWithdrawalAmount: 3000000000,
        newAccountsOpened: 1,
        accountManage: 113,
        accountHaveTransaction: 32,
        accountHaventTransaction: 65,
        nav: **********00,
        gmv: 1**********0,
        buyGmv: ***********,
        sellGmv: ***********,
        revenueFees: ************,
        transactionFees: *********,
        mgCommission: ********,
        name: 'Phòng MG 09',
        children: [
          {
            date: '02/06/2024',
            approvedPayment: 15000000000,
            approvedWithdrawalAmount: 3000000000,
            newAccountsOpened: 1,
            accountManage: 113,
            accountHaveTransaction: 32,
            accountHaventTransaction: 65,
            nav: **********00,
            gmv: 1**********0,
            buyGmv: ***********,
            sellGmv: ***********,
            revenueFees: ************,
            transactionFees: *********,
            mgCommission: ********,
            name: 'MG-11: Phạm Thị Thu Trang',
          },
        ],
      },
    ],
  },
];
