import { Component, Inject, OnD<PERSON>roy, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { DestroyService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { PerformanceFilterComponent } from '../../components/work-performance-filter/performance-filter.component';
import {
  IFilterWorkPerformanceParam,
  ITypeOptionView,
} from '../../models/performance';
import { Store } from '@ngrx/store';
import { selectFilterPerformance$, selectSearchValueTextPerform$ } from '../../stores/performance.selectors';
import { take, takeUntil } from 'rxjs';
import { resetFilterPerformance, resetPerformance, setFilterPerformance } from '../../stores/performance.actions';
import { fakeData } from './fakedata';
import { IColumnConfig } from '@shared/models';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { EViewPerformance, ListOptionsChangeView } from '../../constants/performance';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { deepClone } from '../../../../shared/utils/utils';
import { convertDataForAdmin, updateOptionSelection } from '../../helpers/helpers';

/**
 * WorkPerformanceContainer
 */
@Component({
  selector: 'app-work-performance-container',
  templateUrl: './work-performance.container.html',
  styleUrl: './work-performance.container.scss',
})
export class WorkPerformanceContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('approvedPayment', { static: true }) approvedPayment: TemplateRef<any> | null = null;
  @ViewChild('approvedWithdrawalAmount', { static: true }) approvedWithdrawalAmount: TemplateRef<any> | null = null;
  @ViewChild('newAccountsOpened', { static: true }) newAccountsOpened: TemplateRef<any> | null = null;
  @ViewChild('accountTransaction', { static: true }) accountTransaction: TemplateRef<any> | null = null;
  @ViewChild('accountHaventTransaction', { static: true }) accountHaventTransaction: TemplateRef<any> | null = null;
  @ViewChild('sellGmv', { static: true }) sellGmv: TemplateRef<any> | null = null;
  @ViewChild('buyGmv', { static: true }) buyGmv: TemplateRef<any> | null = null;
  customNumberFormat = customNumberFormat;

  tags = [
    `Tiền nộp: ${this.customNumberFormat(***********)}`,
    `Tiền rút: ${customNumberFormat(***********)}`,
    `TK mở mới:  ${customNumberFormat(18)}`,
    `GTGD: ${customNumberFormat(************)}`,
    `GTGD mua: ${customNumberFormat(************)}`,
    `GTGD bán: ${customNumberFormat(************)}`,
    `Doanh thu phí: ${customNumberFormat(************)}`,
    `Net phí GD: ${customNumberFormat(*********)}`,
  ];

  showCollapse: boolean = true;
  fakeDataWork: any[] = fakeData;
  filterOptions!: IFilterWorkPerformanceParam;

  isAdminWork = true;

  currentViewWork = EViewPerformance.TABLE;

  EViewPerformance = EViewPerformance;

  optionSelect!: ITypeOptionView;

  isDisableButton = false;

  /**
   *Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param store :Ngrx-store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly store: Store
  ) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);
    this.data = this.fakeDataWork;
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    this.data = deepClone(this.fakeDataWork);
    this.initialData = deepClone(this.data);
    this.columnConfigs = [
      {
        name: 'Ngày',
        width: 136,
        minWidth: 136,
        tag: 'date',
        pinned: 'left',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
      },
      {
        name: 'Tổng tiền nộp đã duyệt',
        tag: 'approvedPayment',
        width: 180,
        minWidth: 30,
        cellTemplate: this.approvedPayment,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Tổng tiền rút đã duyệt',
        tag: 'approvedWithdrawalAmount',
        width: 180,
        minWidth: 30,
        isDisplay: true,
        resizable: true,
        cellTemplate: this.approvedWithdrawalAmount,
        align: 'start',
      },
      {
        name: 'SL TK mở mới',
        width: 144,
        minWidth: 30,
        tag: 'newAccountsOpened',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.newAccountsOpened,
        align: 'start',
      },
      {
        name: 'Tổng SL TK quản lý',
        width: 144,
        minWidth: 30,
        tag: 'accountManage',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return `${v} tài khoản`;
        },
        align: 'start',
      },
      {
        name: 'SL TK có giao dịch',
        width: 144,
        minWidth: 30,
        tag: 'accountHaveTransaction',
        cellTemplate: this.accountTransaction,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'SL TK không giao dịch',
        minWidth: 30,
        width: 169,
        tag: 'accountHaventTransaction',
        cellTemplate: this.accountHaventTransaction,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Tài sản ròng(NAV)',
        minWidth: 30,
        width: 144,
        tag: 'nav',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'Tổng GTGD',
        minWidth: 30,
        width: 144,
        tag: 'gmv',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'GTGD (mua)',
        minWidth: 30,
        width: 144,
        tag: 'buyGmv',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.buyGmv,
        align: 'end',
      },
      {
        name: 'GTGD (bán)',
        minWidth: 30,
        width: 144,
        tag: 'sellGmv',
        cellTemplate: this.sellGmv,
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'Tổng doanh thu phí',
        minWidth: 30,
        width: 144,
        tag: 'revenueFees',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'Net phí giao dịch',
        minWidth: 30,
        width: 144,
        tag: 'transactionFees',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'Hoa hồng MG',
        minWidth: 30,
        width: 144,
        tag: 'mgCommission',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
    ];

    const brokerColumn: IColumnConfig = {
      name: 'Môi giới',
      width: 160,
      minWidth: 30,
      tag: 'broker',
      dragDisabled: true,
      isDisplay: true,
      resizable: true,
      disable: true,
      pinned: 'left',
    };

    if (this.isAdminWork) {
      this.columnConfigs.splice(1, 0, brokerColumn);
      convertDataForAdmin(this.data);
      this.initialData = structuredClone(this.data);
    }
    this.store
      .select(selectFilterPerformance$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchValueTextPerform$)
      .pipe(takeUntil(this._destroy))
      .subscribe((params) => {
        this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
      });

    this.updateOptionSelect();
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
    setTimeout(() => {
      if (this.data.length > 0) {
        this.addItemsAtMutipleLevel({ index: 0, items: this.data[0].children });
      }
      this.initialData = structuredClone(this.data);
    }, 0);
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetPerformance());
  }

  /**
   *Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    const roomMgWork: string[] = [];
    const brokerWork: string[] = [];
    this.fakeDataWork.forEach((item) => {
      item?.children.forEach((i: any) => {
        if (!roomMgWork.includes(i.name.replace('MG ', ''))) {
          roomMgWork.push(i.name.replace('MG ', ''));
          i.children?.forEach((d: any) => brokerWork.push(d.name));
        }
      });
    });

    if (this.isDisableButton) return;

    if (tag === 'filter') {
      const refWork = this.openFilter(PerformanceFilterComponent, {
        width: '400px',
        data: {
          filterOptions: this.filterOptions,
          room: roomMgWork,
          brokerWork,
        },
      });
      refWork
        .afterClosed()
        .pipe(take(1))
        .subscribe((v: any) => {
          if (!v) return;
          this.applyFilterWork(v);
        });
    }
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  handleSaveFilterWork(optionFilter: any) {
    // Destructure the filter options
    const { optionFilterRange, optionSelection } = optionFilter;

    // Dispatch the filter data to the store
    this.store.dispatch(setFilterPerformance({ params: optionFilter }));
    // Filter the data based on the filter options
    const newListFilterWork = (
      this.isSearch
        ? updateOptionSelection(this.searchedData, optionSelection)
        : updateOptionSelection(this.initialData, optionSelection)
    ).filter((d) => {
      const dobDateWork = new Date(d.date.split('/').reverse().join('-')).getTime();
      const startDateWork = new Date(optionFilterRange.dateRange?.start ?? '').getTime();
      const endDateWork = new Date(optionFilterRange.dateRange?.end ?? '').getTime();

      return (
        (optionFilterRange.dateRange.start ? dobDateWork >= startDateWork : true) &&
        (optionFilterRange.dateRange.end ? dobDateWork <= endDateWork : true)
      );
    });
    // Add the 'isExpanded' property to each item in the filtered data
    newListFilterWork.forEach((item) => this.traverseNodeAddExpanded(item));
    // Flatten the data with multiple levels of children
    for (let i = 0; i < newListFilterWork.length; i++) {
      const value = newListFilterWork[i];
      if (value.isExpanded) {
        const childrenCount = value.children ? value.children.length : 0;
        this.flattenMultipleLevelChildrenItems(newListFilterWork, i + 1, 0, value.children);
        i += childrenCount;
      }
    }

    // Update the data and filtered data with the new filtered data
    this.data = newListFilterWork;
    this.filteredData = newListFilterWork;
  }

  /**
   * Applies the filter to the data.
   * @param {any} data - The filter data.
   */
  applyFilterWork(data: any) {
    // Destructure the filter data
    const { type, optionFilter } = data;

    // If the filter type is 'save'
    if (type === 'save') {
      this.handleSaveFilterWork(optionFilter);
    }
    // If the filter type is 'default'
    else if (type === 'default') {
      // If search is enabled
      if (this.isSearch) {
        // Subscribe to the search value text perform observable
        this.store
          .select(selectSearchValueTextPerform$)
          .pipe(takeUntil(this._destroy))
          .subscribe((params) => {
            // Perform the search
            this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
          });
      }
      // If search is disabled
      else {
        // Update the data with the initial data
        this.data = this.initialData;
      }

      // Reset the filter in the store
      this.store.dispatch(resetFilterPerformance());
    }
  }

  /**
   * ChangeViewTime
   * @param event
   */
  changeViewTime(event: MouseEvent) {
    let origin = event.target as HTMLElement;

    if (origin.nodeName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }
    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: ListOptionsChangeView,
        displayOptionFn: (v: ITypeOptionView) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item }: { item: ITypeOptionView[] } = data;
      this.currentViewWork = item[0].value;
      this.updateOptionSelect();
      if (this.currentViewWork !== EViewPerformance.TABLE) {
        this.showCollapse = false;
        this.isDisableButton = true;
      } else {
        this.showCollapse = true;
        this.isDisableButton = false;
      }
    });
  }

  /**
   * UpdateOptionSelect
   */
  updateOptionSelect() {
    this.optionSelect = ListOptionsChangeView.find((t) => t.value === this.currentViewWork)!;
  }
}
