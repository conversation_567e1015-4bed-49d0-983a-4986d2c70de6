<div class="work-performance-statistical-container">
  <div class="header-work-performance-statistical">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-255' | translate}}</div>
      <div class="number-info-cls" *ngIf="!showCollapse && currentViewWork === EViewPerformance.TABLE">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[0]}}
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[1]}}
        </div>
        <div class="box-info typo-body-11"><img src="./assets/icons/table_sum.svg" alt="table_sum" /> {{tags[2]}}</div>

        <div class="dropdown-btn" *ngIf="!showCollapse" (click)="toggleButtons()">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div>
      </div>
      <div class="collapse-btn" *ngIf="showCollapse" (click)="toggleButtons()">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div>
    </div>

    <div class="right-box">
      <div class="btn-action-cls-1 typo-button-3" (click)="changeViewTime($event)">
        <img [src]="optionSelect.asset" alt="logo" />
        {{optionSelect.name | translate}}
        <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
      </div>

      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [isClick]="!isDisableButton"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter,
          'disable-btn-cls': isDisableButton
        }"
      >
      </app-action-btn>
    </div>
  </div>

  <!-- <mat-tab-group class="box-info-wrapper" [ngClass]="{'hidden': !showCollapse}">
    @for(tag of tags; track tags) {
    <mat-tab>
      <ng-template mat-tab-label>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tag}}
        </div>
      </ng-template>
    </mat-tab>
    }
  </mat-tab-group> -->
  <div [ngClass]="{'hidden': !showCollapse}" *ngIf="currentViewWork === EViewPerformance.TABLE">
    <app-slide-tag [tags]="tags" [classParent]="'work-performance-statistical-container'"></app-slide-tag>
  </div>
  <ng-container [ngSwitch]="currentViewWork">
    <ng-container *ngSwitchCase="EViewPerformance.TABLE">
      <div class="table-view-container">
        <sha-grid
          #grid
          class="table-custom-cls"
          [columnConfigs]="columnConfigs"
          [data]="data"
          [expandableIconPosition]="'broker'"
          (clickColumn)="clickOnColumn($event)"
          (resizeColumn)="resizeColumn($event)"
          (addItemsAt)="addItemsAt($event)"
          (removeItemsAt)="removeItemsAt($event)"
        >
        </sha-grid>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="EViewPerformance.CHART_TIME">
      <app-chart-by-time-component></app-chart-by-time-component>
    </ng-container>

    <ng-container *ngSwitchCase="EViewPerformance.CHART_MG">
      <app-chart-by-angecy-component></app-chart-by-angecy-component>
    </ng-container>
  </ng-container>
</div>

<!-- Tổng tiền nộp đã duyệt -->
<ng-template #approvedPayment let-approvedPayment="templateInfo" let-element="element">
  @if(approvedPayment){
  <div>
    <ng-container *ngIf="approvedPayment > 0">
      <div class="price-increase typo-body-12">
        <img src="./assets/icons/up.svg" alt="up-icon" />
        <span>+{{customNumberFormat(approvedPayment)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="approvedPayment === 0">
      <div class="price-stable typo-body-12">
        <img src="./assets/icons/minus.svg" alt="stable-icon" />
        <span> {{ customNumberFormat(approvedPayment)}}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>

  }
</ng-template>

<!-- Tổng tiền rút đã duyệt -->
<ng-template #approvedWithdrawalAmount let-approvedWithdrawalAmount="templateInfo" let-element="element">
  @if(approvedWithdrawalAmount){
  <div>
    <ng-container *ngIf="approvedWithdrawalAmount > 0">
      <div class="price-reduce typo-body-12">
        <img src="./assets/icons/down.svg" alt="up-icon" />
        <span>-{{customNumberFormat(approvedWithdrawalAmount)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="approvedWithdrawalAmount === 0">
      <div class="price-stable typo-body-12">
        <img src="./assets/icons/minus.svg" alt="stable-icon" />
        <span> {{ customNumberFormat(approvedWithdrawalAmount)}}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>

  }
</ng-template>

<!-- SL TK mở mới -->
<ng-template #newAccountsOpened let-newAccountsOpened="templateInfo" let-element="element">
  @if(newAccountsOpened){
  <div>
    <ng-container *ngIf="newAccountsOpened > 0">
      <div class="price-increase typo-body-12">
        <img src="./assets/icons/up.svg" alt="up-icon" />
        <span>+{{newAccountsOpened}} tài khoản</span>
      </div>
    </ng-container>
    <ng-container *ngIf="newAccountsOpened === 0">
      <div class="price-stable typo-body-12">
        <img src="./assets/icons/minus.svg" alt="stable-icon" />
        <span>{{newAccountsOpened}} tài khoản</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>

  }
</ng-template>

<!-- ST TK có giao dịch -->
<ng-template #accountTransaction let-accountTransaction="templateInfo" let-element="element">
  @if(accountTransaction){
  <div>
    <ng-container *ngIf="accountTransaction > 0">
      <div class="price-increase typo-body-12">
        <span>{{accountTransaction}} tài khoản</span>
      </div>
    </ng-container>
    <ng-container *ngIf="accountTransaction === 0">
      <div class="price-stable typo-body-12">
        <span>{{accountTransaction}} tài khoản</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>

  }
</ng-template>

<!-- ST TK không có giao dịch -->

<ng-template #accountHaventTransaction let-accountHaventTransaction="templateInfo" let-element="element">
  @if(accountHaventTransaction){
  <div>
    <ng-container *ngIf="accountHaventTransaction > 0">
      <div class="price-reduce typo-body-12">
        <span>{{accountHaventTransaction}} tài khoản</span>
      </div>
    </ng-container>
    <ng-container *ngIf="accountHaventTransaction === 0">
      <div class="price-stable typo-body-12">
        <span>{{accountHaventTransaction}} tài khoản</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>
  }
</ng-template>

<!-- GTGD(mua) -->

<ng-template #sellGmv let-sellGmv="templateInfo" let-element="element">
  @if(sellGmv){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="sellGmv > 0">
      <div class="price-increase sell-gmv typo-body-12">
        <span>{{customNumberFormat(sellGmv)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="sellGmv === 0">
      <div class="price-stable sell-gmv typo-body-12">
        <span>{{sellGmv}}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>

<!-- GTGD(bán) -->

<ng-template #buyGmv let-buyGmv="templateInfo" let-element="element">
  @if(buyGmv){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="buyGmv > 0">
      <div class="price-reduce buy-gmv typo-body-12">
        <span>{{customNumberFormat(buyGmv)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="buyGmv === 0">
      <div class="price-stable buy-gmv typo-body-12">
        <span>{{buyGmv}}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>
