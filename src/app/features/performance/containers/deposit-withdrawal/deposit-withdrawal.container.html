<div class="deposit-withdrawal-container">
  <div class="header-deposit-withdrawal-container">
    <div class="left-box">
      <div class="typo-heading-9 text-header">{{'MES-265' | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[0]}}
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[1]}}
        </div>
        <div class="box-info typo-body-11"><img src="./assets/icons/table_sum.svg" alt="table_sum" /> {{tags[2]}}</div>
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter
        }"
      >
      </app-action-btn>
    </div>
  </div>
  <div class="table-view-container">
    <sha-grid
      #grid
      class="table-custom-cls"
      [columnConfigs]="columnConfigs"
      [data]="data"
      [expandableIconPosition]="'broker'"
      (clickColumn)="clickOnColumn($event); showInfoDetailDep($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreActionDep($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
    >
    </sha-grid>
  </div>
</div>

<ng-template #paymentApproved let-paymentApproved="templateInfo" let-element="element">
  @if(paymentApproved){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="paymentApproved > 0">
      <div class="price-increase typo-body-12">
        <span>+{{customNumberFormat(paymentApproved)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="paymentApproved === 0">
      <div class="price-stable typo-body-12">
        <span>{{paymentApproved}}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>

  }
</ng-template>

<!-- Tiền nộp chưa duyệt -->
<ng-template #paymentUnapproved let-paymentUnapproved="templateInfo" let-element="element">
  @if(paymentUnapproved){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="paymentUnapproved > 0">
      <div class="price-reduce typo-body-12">
        <span>{{customNumberFormat(paymentUnapproved)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="paymentUnapproved === 0 || paymentApproved === null || paymentApproved === undefined">
      <div class="price-stable typo-body-12">
        <span>-</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div style="color: var(--color--accents--red)" class="box-flex-1-cls">-</div>
  }
</ng-template>

<ng-template #withdrawalApproved let-withdrawalApproved="templateInfo" let-element="element">
  @if(withdrawalApproved){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="withdrawalApproved > 0">
      <div class="price-reduce typo-body-12">
        <span>-{{customNumberFormat(withdrawalApproved)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="withdrawalApproved === 0">
      <div class="price-stable typo-body-12">
        <span>-</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>

<ng-template #withdrawalUnapproved let-withdrawalUnapproved="templateInfo" let-element="element">
  @if(withdrawalUnapproved){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="withdrawalUnapproved > 0">
      <div class="price-stable typo-body-12">
        <span>-{{customNumberFormat(withdrawalUnapproved)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="withdrawalUnapproved === 0">
      <div class="price-stable typo-body-12">
        <span>-</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>
