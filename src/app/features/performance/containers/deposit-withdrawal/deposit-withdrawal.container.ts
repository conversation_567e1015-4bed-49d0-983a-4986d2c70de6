import { Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { OptionsRecommendationComponent } from '../../../recommendations/components/options-recommendation/options-recommendation.component';
import { DestroyService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { IFilterWorkPerformanceParam} from '../../models/performance';
import { take, takeUntil } from 'rxjs';
import { resetFilterPerformance, resetItemSelected, setFilterPerformance } from '../../stores/performance.actions';
import { Store } from '@ngrx/store';
import {
  selectFilterPerformance$,
  selectItemSelected$,
  selectSearchValueTextPerform$,
} from '../../stores/performance.selectors';
import { IClickOnColumnEvent, IColumnConfig } from '@shared/models';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { fakeData } from './fakedata';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { PerformanceFilterComponent } from '../../components/work-performance-filter/performance-filter.component';
import { Router } from '@angular/router';
import { convertDataForAdmin, updateOptionSelection } from '../../helpers/helpers';

/**
 * DepositWithDrawalContainer
 */
@Component({
  selector: 'app-deposit-withdrawal-container',
  templateUrl: './deposit-withdrawal.container.html',
  styleUrl: './deposit-withdrawal.container.scss',
})
export class DepositWithDrawalContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('paymentApproved', { static: true }) paymentApproved: TemplateRef<any> | null = null;
  @ViewChild('paymentUnapproved', { static: true }) paymentUnapproved: TemplateRef<any> | null = null;
  @ViewChild('withdrawalApproved', { static: true }) withdrawalApproved: TemplateRef<any> | null = null;
  @ViewChild('withdrawalUnapproved', { static: true }) withdrawalUnapproved: TemplateRef<any> | null = null;

  fakeDataDep: any[] = fakeData;
  filterOptionsDep!: IFilterWorkPerformanceParam;

  isAdminDep = true;
  isExpanded = false;

  customNumberFormat = customNumberFormat;
  tags = [
    `Số TK nộp : ${customNumberFormat(77)}`,
    `Tiền nộp đã duyệt : ${customNumberFormat(50000000000)}`,
    `Tiền rút đã duyệt : ${customNumberFormat(100000000000)}`,
  ];

  showCollapse: boolean = true;

  /**
   *Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param store :Ngrx-store
   * @param router Router
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly store: Store,
    private readonly router: Router
  ) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);
    this.data = this.fakeDataDep;
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }
  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    const roomMgDep: string[] = [];
    const brokerDep: string[] = [];
    this.fakeDataDep.forEach((item) => {
      item?.children.forEach((i: any) => {
        if (!roomMgDep.includes(i.name.replace('MG ', ''))) {
          roomMgDep.push(i.name.replace('MG ', ''));
          i.children?.forEach((d: any) => brokerDep.push(d.name));
        }
      });
    });

    if (tag === 'filter') {
      const refDep = this.openFilter(PerformanceFilterComponent, {
        width: '400px',
        data: {
          filterOptions: this.filterOptionsDep,
          room: roomMgDep,
          brokerDep,
        },
      });
      this.closeApplyFilterDep(refDep);
    }
  }

  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreActionDep(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElementDep = html;
    this.popoverService.open({
      origin: originElementDep,
      content: OptionsRecommendationComponent,
      width: 184,
      hasBackdrop: true,
      position: 1,
      componentConfig: { status: element.status },
    });
  }
  /**
   * The OnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeDataDep);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        tag: 'date',
        name: 'Ngày',
        minWidth: 136,
        width: 136,
        dragDisabled: true,
        isDisplay: true,
        resizable: true,
        pinned: 'left',
      },
      {
        tag: 'accountManage',
        name: 'Tổng SL TK quản lý',
        minWidth: 30,
        width: 160,
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return `${v} tài khoản`;
        },
        align: 'start',
      },
      {
        tag: 'accountDeposit',
        name: 'SL tài khoản nộp',
        minWidth: 30,
        width: 140,
        displayValueFn: (v: number) => {
          return `${v} tài khoản`;
        },
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        tag: 'paymentApproved',
        name: 'Tiền nộp đã duyệt',
        minWidth: 30,
        width: 160,
        isDisplay: true,
        resizable: true,
        cellTemplate: this.paymentApproved,
        align: 'end',
      },
      {
        tag: 'paymentUnapproved',
        name: 'Tiền nộp chưa duyệt',
        minWidth: 30,
        width: 160,
        isDisplay: true,
        resizable: true,
        cellTemplate: this.paymentUnapproved,
        align: 'end',
      },
      {
        tag: 'accountWithdrawal',
        name: 'SL tài khoản rút',
        minWidth: 30,
        width: 160,
        displayValueFn: (v: number) => {
          return v ? `${v} tài khoản` : '-';
        },
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        tag: 'withdrawalApproved',
        name: 'Tiền rút đã duyệt',
        minWidth: 30,
        width: 160,
        cellTemplate: this.withdrawalApproved,
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'Tiền rút chưa duyệt',
        minWidth: 30,
        tag: 'withdrawalUnapproved',
        width: 160,
        cellTemplate: this.withdrawalUnapproved,
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
    ];

    const brokerColumn: IColumnConfig = {
      name: 'Môi giới',
      width: 160,
      tag: 'broker',
      minWidth: 30,
      isDisplay: true,
      resizable: true,
      dragDisabled: true,
      disable: true,
      pinned: 'left',
    };

    if (this.isAdminDep) {
      this.columnConfigs.splice(1, 0, brokerColumn);
      convertDataForAdmin(this.data);
      this.initialData = structuredClone(this.data);
    }

    this.store
      .select(selectFilterPerformance$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptionsDep = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchValueTextPerform$)
      .pipe(takeUntil(this._destroy))
      .subscribe((params) => {
        this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
    this.updateViewListDep();
    if (!this.isExpanded) {
      setTimeout(() => {
        if (this.data.length > 0) {
          this.addItemsAtMutipleLevel({ index: 0, items: this.data[0].children });
        }
        this.initialData = structuredClone(this.data);
      }, 0);
    }
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetItemSelected());
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtonsDep() {
    this.showCollapse = !this.showCollapse;
  }
  /**
   *  CloseApplyFilter
   * @param {any} ref ref
   */
  closeApplyFilterDep(ref: any) {
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        this.applyFilterDep(v);
      });
  }

  /**
   * @param {any} data
   */
  applyFilterDep(data: any) {
    const { type, optionFilter } = data;
    if (type === 'save') {
      const { optionFilterRange,  optionSelection } = optionFilter;

      this.store.dispatch(setFilterPerformance({ params: optionFilter }));

      const newListFilterDep = (
        this.isSearch
          ? updateOptionSelection(this.searchedData, optionSelection)
          : updateOptionSelection(this.initialData, optionSelection)
      ).filter((d) => {
        const dobDateDep = new Date(d.date.split('/').reverse().join('-')).getTime();
        const startDateDep = new Date(optionFilterRange.dateRange?.start ?? '').getTime();
        const endDateDep = new Date(optionFilterRange.dateRange?.end ?? '').getTime();

        return (
          (optionFilterRange.dateRange.start ? dobDateDep >= startDateDep : true) &&
          (optionFilterRange.dateRange.end ? dobDateDep <= endDateDep : true)
        );
      });
      newListFilterDep.forEach((item) => this.traverseNodeAddExpanded(item));

      let i = 0;
      while(i < newListFilterDep.length){
        const value = newListFilterDep[i];
        if (value.isExpanded) {
          const childrenCountDep = value.children ? value.children.length : 0;
          this.flattenMultipleLevelChildrenItems(newListFilterDep, i + 1, 0, value.children);
          i += childrenCountDep;
        }
        i ++;
      }

      this.data = newListFilterDep;
      this.filteredData = newListFilterDep;
    } else if (type === 'default') {
      this.isSearch
        ? this.store
            .select(selectSearchValueTextPerform$)
            .pipe(takeUntil(this._destroy))
            .subscribe((params) => {
              this.searchMultipleLevel(params.searchText ?? '', ['date', 'broker']);
            })
        : (() => {
            this.data = this.initialData;
          })();

      this.store.dispatch(resetFilterPerformance());
    }
  }

  /**
   * ShowInfoDetail
   * @param event
   * @returns {void} prevent event
   */
  showInfoDetailDep(event: IClickOnColumnEvent) {
    const { element, _event } = event;
    if (element.level === 0) return;
    if ((_event.target as HTMLElement).nodeName === 'svg' || (_event.target as HTMLElement).nodeName === 'path') return;
    this.router.navigate(['/job-performance/deposit-withdrawal/detail'], {
      queryParams: {
        date: element.date,
        id: element.id,
        name: element.broker,
      },
    });
  }

  /**
   * UpdateViewListDep
   */
  updateViewListDep() {
    this.store
      .select(selectItemSelected$)
      .pipe(take(1))
      .subscribe((v) => {
        if ((v && !v.id) || !v) return;
        const { date, name } = v;
        let originElementDep: any  = null;
        const findItemSelectedDep = (listData: any) => {
          for (const itemDep of listData) {
            if (itemDep.broker === name && itemDep.date === date) {
              originElementDep = itemDep;
              return;
            }
            if (itemDep.children && itemDep.children.length > 0) {
              findItemSelectedDep(itemDep.children);
              if (originElementDep) return;
            }
          }
        };
        findItemSelectedDep(this.data);
        if (!originElementDep) return;
        let parentElementDep: any  = null;
        const getParentElement = (element: any) => {
          if (!element.parent) {
            parentElementDep = element;
            return;
          }
          getParentElement(element.parent);
        };
        getParentElement(originElementDep);
        const indexDep = this.data.findIndex((t) => t.id === parentElementDep.id);
        if (!parentElementDep.isExpanded) {
          this.isExpanded = true;
          setTimeout(() => {
            this.addItemsAtMutipleLevel({ index: indexDep, items: this.data[indexDep].children });
          });
        }

        setTimeout(() => {
          const elementRefDep = document.querySelector(`[data-id="${originElementDep.id}"]`);
          if (elementRefDep) elementRefDep.scrollIntoView({ block: 'center', behavior: 'smooth' });
        }, 100);
      });
  }
}
