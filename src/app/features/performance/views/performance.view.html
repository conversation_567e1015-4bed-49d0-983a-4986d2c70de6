<div class="performance-view-containers">
  <div class="performance-header-cls">
    <div class="header-txt typo-heading-8">{{'MES-04' | translate}}</div>
    <div class="header-btn-box">
      <a class="header-btn-attach-broker typo-button-5" (click)="createBrokerPopup()">
        <img src="./assets/icons/profile-two-user.svg" alt="profile-two-user" />
        {{'MES-253' | translate}}
      </a>
      <div class="header-btn typo-button-5">
        <img src="./assets/icons/profile-add.svg" alt="profile-two-user" />
        {{'MES-254' | translate}}
      </div>
    </div>
  </div>

  <div class="sub-menu-cls">
    <mat-tab-group animationDuration="200ms" [selectedIndex]="activeTab" (selectedTabChange)="onTabChange()">
      <mat-tab *ngFor="let item of menuPerformance" isActice="item.isActice">
        <ng-template mat-tab-label>
          <div class="menu-performance-cls">
            <a class="box-selection" mat-list-item routerLinkActive="isSelect" [routerLink]="item.router">
              <mat-icon
                class="mat-icon-cls"
                aria-hidden="false"
                aria-label="icon"
                [svgIcon]="item.nameIcon"
                [ngClass]="item.router"
              >
              </mat-icon>
              <div class="typo-body-6">{{item.name | translate}}</div>
            </a>
          </div>
        </ng-template>
      </mat-tab>
    </mat-tab-group>
    <div class="search-cls">
      <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
      <input
        [formControl]="searchTextControl"
        class="input-cls input-style-common typo-body-12"
        type="text"
        [placeholder]="'MES-14' | translate"
      />
    </div>
  </div>
  <div class="router-container-cls"><router-outlet></router-outlet></div>
</div>
