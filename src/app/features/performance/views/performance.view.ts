import { Component } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { ISearchTextValuePerformance } from '../models/performance';
import { FormControl } from '@angular/forms';
import { debounceTime, filter, takeUntil } from 'rxjs';
import { DestroyService, DialogService } from '../../../core/services';
import { resetPerformance, setSearchValueTextPerformance } from '../stores/performance.actions';
import { CreateBrokerPopupComponent } from '../components/create-broker-popup/create-broker-popup.component';

/**
 *
 */
@Component({
  selector: 'app-performance-view',
  templateUrl: './performance.view.html',
  styleUrls: ['./performance.view.scss'],
})
export class PerformanceView {
  menuPerformance = [
    {
      router: 'work-performance',
      name: 'MES-246',
      nameIcon: 'icon:trade-order',
    },
    {
      router: 'fees-commission',
      name: 'MES-247',
      nameIcon: 'icon:fee-commission',
    },
    {
      router: 'deposit-withdrawal',
      name: 'MES-248',
      nameIcon: 'icon:deposit-withdrawal',
    },
    {
      router: 'buy-sell',
      name: 'MES-249',
      nameIcon: 'icon:deposit-withdrawal',
    },
    {
      router: 'open-new-account',
      name: 'MES-250',
      nameIcon: 'icon:new-account',
    },
    {
      router: 'nav-management',
      name: 'MES-251',
      nameIcon: 'icon:nav-management',
    },
    {
      router: 'customer-ratings',
      name: 'MES-252',
      nameIcon: 'icon:customer-ratings',
    },
  ];

  activeTab = 0;
  searchTextControl = new FormControl();

  /**
   * Find active route on page reload
   * @param router
   * @param store Store
   * @param _destroy Destroy
   * @param dialogService DialogService
   */
  constructor(
    private readonly router: Router,
    private readonly store: Store,
    private readonly _destroy: DestroyService,
    private readonly dialogService: DialogService
  ) {
    this.updateActiveTab(router.url);

    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this._destroy)
      )
      .subscribe((event: any) => {
        this.updateActiveTab(event.urlAfterRedirects);
      });

    this.searchTextControl.valueChanges.pipe(debounceTime(300), takeUntil(this._destroy)).subscribe((term) => {
      const params: ISearchTextValuePerformance = {
        searchText: term,
      };
      this.store.dispatch(setSearchValueTextPerformance({ params }));
    });
  }

  /**
   * when change tab
   */
  onTabChange() {
    this.store.dispatch(resetPerformance());
    this.searchTextControl.patchValue('');
  }

  /**
   * Update active tab index based on current URL
   * @param url - Current URL
   */
  updateActiveTab(url: string) {
    this.activeTab = this.menuPerformance.findIndex((item) => url.includes(item.router));
  }

  /**
   * createBrokerPopup
   */
  createBrokerPopup() {
    this.dialogService.openRightDialog(CreateBrokerPopupComponent, {
      width: '450px',
    });
  }
}
