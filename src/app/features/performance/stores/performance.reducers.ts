import { createReducer, on } from '@ngrx/store';
import { IPerformanceState } from '../models/performance';
import * as PerformAction from './performance.actions';
export const inititalPerformanceState: IPerformanceState = {
  filterPerformance: {
    optionFilterRange: {
      dateRange: {
        start: null,
        end: null,
      },
    },
    tag: null,
    isFilter: false,
    optionSelection: {
      roomSelect: null,
      brokerSelect: null,
    },
  },
  filterFeesCommistion: {
    optionFilterRange: {
      dateRange: {
        start: null,
        end: null,
      },
    },
    tag: null,
    isFilter: false,
    optionSelection: {
      roomSelect: null,
      brokerSelect: null,
    },
  },
  filterDepositWithDrawal: {
    optionFilterRange: {
      dateRange: {
        start: null,
        end: null,
      },
    },
    tag: null,
    isFilter: false,
    optionSelection: {
      roomSelect: null,
      brokerSelect: null,
    },
  },
  filterBuySell: {
    optionFilterRange: {
      dateRange: {
        start: null,
        end: null,
      },
    },
    tag: null,
    isFilter: false,
    optionSelection: {
      roomSelect: null,
      brokerSelect: null,
    },
  },
  filterOpenNewAccount: {
    optionFilterRange: {
      dateRange: {
        start: null,
        end: null,
      },
    },
    tag: null,
    isFilter: false,
    optionSelection: {
      roomSelect: null,
      brokerSelect: null,
    },
  },
  filterNavManagement: {
    optionFilterRange: {
      dateRange: {
        start: null,
        end: null,
      },
    },
    tag: null,
    isFilter: false,
    optionSelection: {
      roomSelect: null,
      brokerSelect: null,
    },
  },
  filterSearchValuePerformance: {
    searchText: null,
  },

  // other options

  itemSelected: null,
};

export const performanceReducers = createReducer<IPerformanceState>(
  inititalPerformanceState,
  on(
    PerformAction.setFilterPerformance,
    (state, action): IPerformanceState => ({
      ...state,
      filterPerformance: { ...action.params },
    })
  ),

  on(
    PerformAction.resetFilterPerformance,
    (state): IPerformanceState => ({
      ...state,
      filterPerformance: { ...inititalPerformanceState.filterPerformance },
    })
  ),

  on(
    PerformAction.setFilterFeesCommission,
    (state, action): IPerformanceState => ({
      ...state,
      filterFeesCommistion: { ...action.params },
    })
  ),
  on(
    PerformAction.resetFilterFeesCommission,
    (state): IPerformanceState => ({
      ...state,
      filterFeesCommistion: { ...inititalPerformanceState.filterFeesCommistion },
    })
  ),
  on(
    PerformAction.setFilterDepositWithdrawal,
    (state, action): IPerformanceState => ({
      ...state,
      filterDepositWithDrawal: { ...action.params },
    })
  ),
  on(
    PerformAction.resetFilterDepositWithdrawal,
    (state): IPerformanceState => ({
      ...state,
      filterDepositWithDrawal: { ...inititalPerformanceState.filterDepositWithDrawal },
    })
  ),

  on(
    PerformAction.setFilterBuySell,
    (state, action): IPerformanceState => ({
      ...state,
      filterBuySell: { ...action.params },
    })
  ),
  on(
    PerformAction.resetFilterBuySell,
    (state): IPerformanceState => ({
      ...state,
      filterBuySell: { ...inititalPerformanceState.filterBuySell },
    })
  ),
  on(
    PerformAction.setFilterOpenNewAccount,
    (state, action): IPerformanceState => ({
      ...state,
      filterOpenNewAccount: { ...action.params },
    })
  ),

  on(
    PerformAction.resetFilterOpenNewAccount,
    (state): IPerformanceState => ({
      ...state,
      filterOpenNewAccount: { ...inititalPerformanceState.filterOpenNewAccount },
    })
  ),

  on(
    PerformAction.setFilterNavManagement,
    (state, action): IPerformanceState => ({
      ...state,
      filterNavManagement: { ...action.params },
    })
  ),
  on(
    PerformAction.resetFilterNavManagement,
    (state): IPerformanceState => ({
      ...state,
      filterNavManagement: { ...inititalPerformanceState.filterNavManagement },
    })
  ),
  on(
    PerformAction.setSearchValueTextPerformance,
    (state, action): IPerformanceState => ({
      ...state,
      filterSearchValuePerformance: { ...action.params },
    })
  ),
  on(
    PerformAction.resetSearchValueTextPerformance,
    (state): IPerformanceState => ({
      ...state,
      filterSearchValuePerformance: { ...inititalPerformanceState.filterSearchValuePerformance },
    })
  ),
  on(
    PerformAction.setItemSelected,
    (state, action): IPerformanceState => ({
      ...state,
      itemSelected: { ...action },
    })
  ),
  on(
    PerformAction.resetItemSelected,
    (state): IPerformanceState => ({
      ...state,
      itemSelected: {
        id: '',
        name: '',
        date: '',
      },
    })
  )
);
