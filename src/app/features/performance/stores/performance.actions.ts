import { createAction, props } from '@ngrx/store';
import { IFilterWorkPerformanceParam, ISearchTextValuePerformance } from '../models/performance';

export const setFilterPerformance = createAction(
  '[Work performance] set filter work performance',
  props<{ params: IFilterWorkPerformanceParam }>()
);

export const resetFilterPerformance = createAction('[Work performance] reset filter work performance');

export const setFilterFeesCommission = createAction(
  '[Fees commission] set filter fees commission',
  props<{ params: IFilterWorkPerformanceParam }>()
);

export const resetFilterFeesCommission = createAction('[Fees commission] reset filter fees commission');

export const setFilterDepositWithdrawal = createAction(
  '[Deposit withdrawal] set filter deposit withdrawal',
  props<{ params: IFilterWorkPerformanceParam }>()
);

export const resetFilterDepositWithdrawal = createAction('[Deposit withdrawal] reset filter deposit withdrawal');

export const setFilterBuySell = createAction(
  '[Buy sell] set filter buy sell',
  props<{ params: IFilterWorkPerformanceParam }>()
);

export const resetFilterBuySell = createAction('[Buy sell] reset filter buy sell');

export const setFilterOpenNewAccount = createAction(
  '[Open new account] set filter open new account',
  props<{ params: IFilterWorkPerformanceParam }>()
);

export const resetFilterOpenNewAccount = createAction('[Open new account] reset filter open new account');

export const setFilterNavManagement = createAction(
  '[NAV management] set filter nav management',
  props<{ params: IFilterWorkPerformanceParam }>()
);

export const resetFilterNavManagement = createAction('[NAV management] reset filter nav management');

export const setSearchValueTextPerformance = createAction(
  '[Performance] set search value text performance',
  props<{ params: ISearchTextValuePerformance }>()
);

export const resetSearchValueTextPerformance = createAction('[Performance] reset search value text performance');

export const resetPerformance = createAction('[Performance] Reset performance state');

export const setItemSelected = createAction(
  '[Perfomance] set item is selected',
  props<{ id: string; date: string; name: string }>()
);

export const resetItemSelected = createAction('[Perfomance] reset item is selected');
