import { createFeatureSelector, createSelector } from '@ngrx/store';
import { IPerformanceState } from '../models/performance';

export const PERFORMANCE_STATE_NAME = 'PERFORMANCE';

export const selectPerformanceState = createFeatureSelector<IPerformanceState>(PERFORMANCE_STATE_NAME);

export const selectFilterPerformance$ = createSelector(selectPerformanceState, (state) => state?.filterPerformance);

export const selectFilterFeesCommission$ = createSelector(
  selectPerformanceState,
  (state) => state?.filterFeesCommistion
);

export const selectFilterDespositWithdrawal$ = createSelector(
  selectPerformanceState,
  (state) => state?.filterDepositWithDrawal
);

export const selectFilterBuySell$ = createSelector(selectPerformanceState, (state) => state?.filterBuySell);

export const selectFilterOpenNewAccount$ = createSelector(
  selectPerformanceState,
  (state) => state?.filterOpenNewAccount
);

export const selectFilterNavManagement$ = createSelector(selectPerformanceState, (state) => state?.filterNavManagement);

export const selectSearchValueTextPerform$ = createSelector(
  selectPerformanceState,
  (state) => state?.filterSearchValuePerformance
);

export const selectItemSelected$ = createSelector(selectPerformanceState, (state) => state?.itemSelected);
