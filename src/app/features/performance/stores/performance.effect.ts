import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import {
  resetFilterPerformance,
  resetItemSelected,
  resetPerformance,
  resetSearchValueTextPerformance,
} from './performance.actions';
import { of, switchMap } from 'rxjs';
/**
 * PerformanceEffects
 */
@Injectable()
export class PerformanceEffects {
  /**
   * @param {Actions} actions$
   * @param {Store} store
   */
  constructor(private readonly actions$: Actions, private readonly store: Store) {}

  resetFilterPerformance$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(resetPerformance),
      switchMap(() => {
        return of(resetFilterPerformance());
      })
    );
  });

  resetSearchPerformance$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(resetPerformance),
      switchMap(() => {
        return of(resetSearchValueTextPerformance());
      })
    );
  });

  resetItemSelectedItem$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(resetPerformance),
      switchMap(() => {
        return of(resetItemSelected());
      })
    );
  });
}
