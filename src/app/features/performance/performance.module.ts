import { NgModule } from '@angular/core';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { ActionBtnComponent } from '../../shared/components/action-btn/action-btn.component';
import { DraggableListComponent } from '../../shared/components/dragable-list/draggable-list.component';
import { CalendarCustomComponent } from '../../shared/components/calendar-custom/calendar-custom.component';
import { PhoneNumberTableComponent } from '../../shared/components/phone-number-table/phone-number-table.component';
import { InputProportionComponent } from '../../shared/components/input-custom-for-table/input-proportion/input-proportion.component';
import { InputNumberCustomComponent } from '../../shared/components/input-custom-for-table/input-number-custom/input-number-custom.component';
import { PerformanceView } from './views/performance.view';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { FilterComponent } from '../../shared/components/filter/filter.component';
import { MatTabsModule } from '@angular/material/tabs';
import { PerformanceRoutingModule } from './performance.routing.module';
import { WorkPerformanceContainer } from './containers/work-performance/work-performance.container';
import { FeesCommissionContainer } from './containers/fees-commission/fees-commission.container';
import { BuySellContainer } from './containers/buy-sell-container/buy-sell.container';
import { DepositWithDrawalContainer } from './containers/deposit-withdrawal/deposit-withdrawal.container';
import { OpenNewAccountContainer } from './containers/open-new-account/open-new-account.container';
import { NavManagementContainer } from './containers/nav-management/nav-management.container';
import { NumberFormatPipe } from '../../shared/pipes/format-number/format-number.pipe';
import { SlideTagComponent } from 'src/app/shared/components/slide-tag/slide-tag.component';
import { PerformanceFilterComponent } from './components/work-performance-filter/performance-filter.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { StoreModule } from '@ngrx/store';
import { PERFORMANCE_STATE_NAME } from './stores/performance.selectors';
import { performanceReducers } from './stores/performance.reducers';
import { EffectsModule } from '@ngrx/effects';
import { PerformanceEffects } from './stores/performance.effect';

import { ChartByTimeComponent } from './components/performance-by-chart/chart-by-time/chart-by-time.component';
import { BarChartComponent } from './components/performance-by-chart/bar-chart/bar-chart.component';
import { DoughnutChartComponent } from './components/performance-by-chart/doughnut-chart/doughnut-chart.component';
import { LineChartComponent } from './components/performance-by-chart/line-chart/line-chart.component';
import { ChartByAngecyComponent } from './components/performance-by-chart/chart-by-angecy/chart-by-angecy.component';
import { PieChartComponent } from './components/performance-by-chart/pie-chart/pie-chart.component';
import { CustomerRatingContainer } from './containers/customer-rating/customer-rating.container';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FeesCommissionDetailComponent } from './components/fee-commission-detail/fee-commission-detail.component';
import { DepositWithDrawalDetailComponent } from './components/deposit-withdrawal/deposit-withdrawal.component';
import { BuySellDetailComponent } from './components/buy-sell-detail/buy-sell-detail.component';
import { OpenNewAccountDetailComponent } from './components/open-new-account-detail/open-new-account-detail.component';
import { BuySellDetailFilterComponent } from './components/buy-sell-detail/buy-sell-detail-filter/buy-sell-detail-filter.component';
import { LayoutFilterComponent } from './components/layout-filter/layout-filter.component';
import { NgxMaskDirective } from 'src/app/shared/directives/mask/ngx-mask.directive';
import { FeeComimissionDetailFilterComponent } from './components/fee-commission-detail/fee-commission-detail-filter/fee-commission-detail-filter.component';
import { OpenNewAccountDetailFilterComponent } from './components/open-new-account-detail/open-new-account-detail-filter/open-new-account-detail-filter.component';
import { DepositWithDrawalDetailFilterComponent } from './components/deposit-withdrawal/deposit-withdrawal-detail-filter/deposit-withdrawal-detail-filter.component';
import { CreateBrokerPopupComponent } from './components/create-broker-popup/create-broker-popup.component';

const SHARED = [
  GridComponent,
  ActionBtnComponent,
  DraggableListComponent,
  CalendarCustomComponent,
  PhoneNumberTableComponent,
  InputProportionComponent,
  InputNumberCustomComponent,
  NumberFormatPipe,
  SlideTagComponent,
];

const VIEWS = [PerformanceView];

const CONTAINERS = [
  WorkPerformanceContainer,
  FeesCommissionContainer,
  BuySellContainer,
  DepositWithDrawalContainer,
  OpenNewAccountContainer,
  NavManagementContainer,
  CustomerRatingContainer,
];

const COMPONENT = [
  PerformanceFilterComponent,
  ChartByTimeComponent,
  BarChartComponent,
  DoughnutChartComponent,
  LineChartComponent,
  ChartByAngecyComponent,
  PieChartComponent,
  FeesCommissionDetailComponent,
  DepositWithDrawalDetailComponent,
  BuySellDetailComponent,
  OpenNewAccountDetailComponent,
  BuySellDetailFilterComponent,
  LayoutFilterComponent,
  FeeComimissionDetailFilterComponent,
  OpenNewAccountDetailFilterComponent,
  DepositWithDrawalDetailFilterComponent,
  CreateBrokerPopupComponent,
];
/**
 *
 */
@NgModule({
  declarations: [...VIEWS, ...CONTAINERS, ...COMPONENT],
  imports: [
    ...SHARED,
    CommonModule,
    FilterComponent,
    TranslateModule,
    PerformanceRoutingModule,
    NgxMaskDirective,
    //Material Module
    MatIconModule,
    FormsModule,
    MatDialogModule,
    MatMenuModule,
    ReactiveFormsModule,
    MatTabsModule,
    MatFormFieldModule,
    MatDatepickerModule,
    FormsModule,
    MatInputModule,
    MatCheckboxModule,
    StoreModule.forFeature(PERFORMANCE_STATE_NAME, performanceReducers),
    EffectsModule.forFeature([PerformanceEffects]),
  ],
})
export class PerformanceModule {}
