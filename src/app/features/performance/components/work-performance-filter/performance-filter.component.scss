.work-performance-filter-component {
  display: flex;
  flex-direction: column;
  height: 100%;
  .work-performance-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid var(--color--other--divider);
    img[alt='x-cross'] {
      cursor: pointer;
    }
  }

  .work-performance-body {
    flex: 1;
    overflow: auto;
    .day-header {
      padding: 12px 24px;
    }
    .date-time-cls {
      padding: 8px 24px 16px 24px;
      display: flex;
      flex-direction: column;
      gap: 17px;
      align-items: flex-start;

      .pick-range-time {
        display: flex;
        gap: 10px;

        .btn-date-time {
          padding: 2px 8px;
          background-color: #dbdee0;
          border-radius: 16px;
        }

        .selected {
          background-color: var(--color--accents--yellow);
        }
      }
    }

    .box-calendar {
      display: flex;
      gap: 24px;
      .content-from,
      .content-to {
        display: flex;
        flex-direction: column;
        gap: 8px;
        .input-wrapper {
          position: relative;
          input {
            padding: 10px 28px 10px 16px;
            border-radius: 8px;
            height: 40px;
            width: 100%;
            border: 1px solid var(--Neutral-100, #dbdee0);
          }
          img {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 10px;
            cursor: pointer;
          }
        }
      }
    }

    .title-dropdown {
      padding: 12px 24px;
    }
    // Phòng
    .searchbox-wrap {
      padding: 8px 24px 16px;
      display: flex;
      flex-direction: column;
      gap: 17px;
      overflow: hidden;
      border-bottom: 1px solid var(--color--other--divider);
      min-height: 245px;

      .search-box {
        display: flex;
        position: relative;
        .input-cls-custom {
          padding: 10px 16px;
          padding-left: 32px;
          width: 100%;
        }
        .icon-search-cls {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 10px;
        }
      }
      .option-list-cls {
        height: 164px;
        overflow: auto;
        display: flex;
        flex-direction: column;
        gap: 16px;

        .checkbox-cls {
          ::ng-deep {
            .mdc-label {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;
            }
          }

          .img-cls {
            width: 18px;
            height: 18px;
            object-fit: contain;
            vertical-align: middle;
            border-radius: 50%;
          }
        }
      }
    }
  }

  .work-performance-footer {
    display: flex;
    padding: 16px 24px;
    align-items: center;
    gap: 10px;
    justify-content: space-between;
    border-top: 1px solid var(--color--other--divider);
    .btn {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      cursor: pointer;
      &.primary {
        color: var(--color--neutral--white);
        border: 1px solid var(--color--brand--500);
        background-color: var(--color--brand--500);
      }
    }
  }
}
