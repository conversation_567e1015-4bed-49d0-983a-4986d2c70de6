import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IFilterWorkPerformanceParam, IOptionSelection } from '../../models/performance';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { FormControl } from '@angular/forms';
import { MY_DATE_FORMAT } from '../../../../shared/constants/date-picker';
import { debounceTime, filter, startWith, takeUntil, tap } from 'rxjs';
import { DestroyService } from '../../../../core/services';
import { DatePickerNavigationFullDateComponent } from '../../../../shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';
import { Store } from '@ngrx/store';
import { selectFilterPerformance$ } from '../../stores/performance.selectors';
import { IOptionList } from '../../../../shared/models/dropdown-item.model';
import { IRangeFilter } from '../../../assets/models/asset';

/**
 * WorkPerformanceFilterCompoenent
 */
@Component({
  selector: 'app-performance-filter-component',
  templateUrl: './performance-filter.component.html',
  styleUrl: './performance-filter.component.scss',
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE],
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
  ],
})
export class PerformanceFilterComponent implements OnInit {
  timeTags = ['1 ngày', '1 tuần', '1 tháng', '6 tháng', '1 năm'];
  selectedTag: string | null = null;
  isSelectAllRoom = true;
  isSelectAllBroker = true;
  listBrokerOptions: string[] = [];
  listFilterBrokerOptions: IOptionList[] = [];
  listRoomOptions: string[] = [];
  listFilterRoomOptions: IOptionList[] = [];
  listFilterRoomStore: IOptionList[] = [];
  listFilterBrokerStore: IOptionList[] = [];
  listRoomMg: IOptionList[] = [];
  listBroker: IOptionList[] = [];
  /**
   * Constructor
   * @param {any} data
   * @param dialogRef  MatDialogRef<PerformanceFilterComponent>
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<PerformanceFilterComponent>,
    private readonly _destroy: DestroyService,
    private readonly store: Store
  ) {
    const { filterOptions, room, broker } = data;
    const { optionFilterRange, optionSelection } = filterOptions;
    const { dateRange } = optionFilterRange;
    console.log(dateRange);
    this.listRoomMg = room.map((r: string, index: number) => {
      return {
        id: index + 1,
        label: r,
        value: r,
      };
    });

    this.listBroker = broker.map((b: string, index: number) => {
      return {
        id: index + 1,
        label: b,
        value: b,
      };
    });
    this.updateFormControlValue(dateRange, this.dateStartControl, this.dateEndControl);
    this.updateRoomList(optionSelection.roomSelect);
    this.updateBrokerList(optionSelection.brokerSelect);
  }

  dateStartControl = new FormControl();

  dateEndControl = new FormControl();

  searchRoomControl = new FormControl();

  searchBrokerControl = new FormControl();

  headerCalendar = DatePickerNavigationFullDateComponent;

  /**
   * pickDate then off tag
   */
  onDateToChange() {
    this.selectedTag = null;
  }

  /**
   * The Oninit
   */
  ngOnInit(): void {
    this.store
      .select(selectFilterPerformance$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.selectedTag = filter.tag;
      });
    this.listFilterRoomStore = this.listFilterRoomOptions;
    this.searchRoomControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterRoomOptions = this._filter(value ?? '', this.listFilterRoomStore);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.listFilterBrokerStore = this.listFilterBrokerOptions;
    this.searchBrokerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterBrokerOptions = this._filter(value ?? '', this.listFilterBrokerOptions);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * select khoảng thời gian
   * @param {string} tag
   */
  selectTag(tag: string) {
    this.selectedTag = tag;
    const newStartDate = new Date();
    switch (tag) {
      case '1 ngày':
        newStartDate.setDate(newStartDate.getDate());
        break;
      case '1 tuần':
        newStartDate.setDate(newStartDate.getDate() - 6);
        break;
      case '1 tháng':
        newStartDate.setMonth(newStartDate.getMonth() - 1);
        break;
      case '6 tháng':
        newStartDate.setMonth(newStartDate.getMonth() - 6);
        break;
      case '1 năm':
        newStartDate.setFullYear(newStartDate.getFullYear() - 1);
        break;
      default:
        break;
    }
    this.dateEndControl.patchValue(new Date());
    this.dateStartControl.patchValue(newStartDate);
  }

  /**
   *
   */
  // ngOnInit(): void {}

  /**
   * SubmitFilter
   */
  submitFilter() {
    const dateRange = {
      start: this.dateStartControl.value?._d ?? this.dateStartControl.value,
      end: this.dateEndControl.value?._d ?? this.dateEndControl.value,
    } as IRangeFilter;
    const roomSelect = this.listFilterRoomOptions.filter((t) => t.isSelect).map((t) => t.value);
    const brokerSelect = this.listFilterBrokerOptions.filter((t) => t.isSelect).map((t) => t.value);
    const isFilter =
      dateRange.start ||
      dateRange.end ||
      roomSelect.length < this.listFilterRoomOptions.length ||
      brokerSelect.length < this.listFilterBrokerOptions.length
        ? true
        : false;
    const optionSelection = {
      roomSelect,
      brokerSelect,
    } as IOptionSelection;
    const optionFilter = {
      optionFilterRange: {
        dateRange,
      },
      isFilter,
      tag: this.selectedTag,
      optionSelection,
    } as IFilterWorkPerformanceParam;
    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * SetDefaultFilter
   */
  setDefaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * @param {IRangeFilter} dateRange
   * @param {FormControl} startControl
   * @param {FormControl} endControl
   */
  updateFormControlValue(dateRange: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    console.log(dateRange);
    const { start, end } = dateRange;
    startControl.patchValue(start);
    endControl.patchValue(end);
  }

  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param type Kiểu giá trị được thay đổi
   * @param section
   * @param item Item thay đổi
   */
  changeSections(checked: boolean, type: string, section: string, item?: IOptionList) {
    if (type == 'all') {
      this.updateSelectAll(checked, section);
    } else if (type === 'item' && item) {
      item['isSelect'] = checked;
      this.updateSelectAllStatus(section);
    }
  }

  /**
   * Cập nhật trạng thái chọn tất cả
   * @param {boolean} checked
   * @param {string} section
   */
  updateSelectAll(checked: boolean, section: string) {
    switch (section) {
      case 'room':
        this.isSelectAllRoom = checked;

        this.listFilterRoomOptions.forEach((i) => {
          i.isSelect = checked;
        });
        break;

      case 'broker':
        this.isSelectAllBroker = checked;
        this.listFilterBrokerOptions.forEach((i) => {
          i.isSelect = checked;
        });
        break;
    }
  }

  /**
   * Cập nhật trạng thái của SelectAll checkbox
   * @param section
   */
  updateSelectAllStatus(section: string) {
    switch (section) {
      case 'room':
        this.isSelectAllRoom = this.listFilterRoomOptions.every((t) => t.isSelect);
        break;
      case 'broker':
        this.isSelectAllBroker = this.listFilterBrokerOptions.every((t) => t.isSelect);
        break;
    }
  }

  /**
   * Inner filter function
   * @param {string} value search value
   * @param {IListOptions[]} options
   */
  private _filter(value: string, options: IOptionList[]): IOptionList[] {
    const filterValue = value.toString().toLowerCase();

    return options.filter((option) => option.value?.toString().toLowerCase()?.includes(filterValue));
  }

  /**
   * @param room
   */
  updateRoomList(room: string[] | null) {
    const isSelect = (value: string) => room === null || room.includes(value);

    this.listFilterRoomOptions = this.listRoomMg.map((code) => ({
      label: code.label,
      value: code.value,
      id: code.id,
      isSelect: isSelect(code.label),
    }));
    this.isSelectAllRoom = this.listFilterRoomOptions.every((t) => t.isSelect === true);
  }

  /**
   * @param broker
   */
  updateBrokerList(broker: string[] | null) {
    const isSelect = (value: string) => broker === null || broker.includes(value);
    this.listFilterBrokerOptions = this.listBroker.map((code) => ({
      label: code.label,
      value: code.value,
      id: code.id,
      isSelect: isSelect(code.label),
    }));
    this.isSelectAllBroker = this.listFilterBrokerOptions.every((t) => t.isSelect === true);
  }
}
