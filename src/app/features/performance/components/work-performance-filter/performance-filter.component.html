<div class="work-performance-filter-component">
  <div class="work-performance-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="work-performance-body">
    <div class="day-header typo-body-15">Ngày</div>

    <div class="date-time-cls">
      <div class="pick-range-time">
        <div class="time-group-cls" *ngFor="let tag of timeTags">
          <button
            class="btn btn-date-time typo-body-12"
            mat-raised-button
            (click)="selectTag(tag)"
            [ngClass]="{ selected: selectedTag === tag }"
          >
            {{ tag }}
          </button>
        </div>
      </div>

      <!-- Calendar -->
      <div class="box-calendar">
        <!-- Từ ngày -->
        <div class="content-from">
          <div class="from-label typo-body-12">{{ 'MES-209' | translate }} ngày</div>
          <div class="input-wrapper">
            <input
              type="text"
              class="fs-12 calendar-input typo-body-12"
              matInput
              [matDatepicker]="dateFrom"
              [placeholder]="'DD/MM/YYYY'"
              [formControl]="dateStartControl"
              [max]="dateEndControl.value ? dateEndControl.value : null"
              (dateInput)="onDateToChange()"
            />
            <img src="./assets/icons/calendar.svg" alt="" (click)="dateFrom.open()" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              #dateFrom
              panelClass="calendar-cls"
            ></mat-datepicker>
          </div>
        </div>

        <!-- Tới ngày -->
        <div class="content-to">
          <div class="to-label typo-body-12">{{ 'MES-210' | translate }} ngày</div>
          <div class="input-wrapper">
            <input
              type="text"
              class="fs-12 calendar-input typo-body-12"
              matInput
              [matDatepicker]="dateTo"
              [placeholder]="'DD/MM/YYYY'"
              [formControl]="dateEndControl"
              [min]="dateStartControl.value ? dateStartControl.value : null"
              (dateInput)="onDateToChange()"
            />
            <img src="./assets/icons/calendar.svg" alt="" (click)="dateTo.open()" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              #dateTo
              panelClass="calendar-cls"
            ></mat-datepicker>
          </div>
        </div>
      </div>
    </div>
    <div class="title-dropdown typo-body-15">{{ 'MES-38' | translate }}</div>

    <!-- dropdown -->
    <div class="searchbox-wrap">
      <div class="search-box">
        <input
          type="text"
          class="input-cls-custom input-style-common typo-body-11 fs-12"
          [placeholder]="'MES-14' | translate"
          [formControl]="searchRoomControl"
        />
        <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
      </div>
      <div class="option-list-cls">
        <mat-checkbox
          (change)="changeSections($event.checked, 'all', 'room')"
          [checked]="isSelectAllRoom"
          class="checkbox-cls"
        >
          {{ 'MES-58' | translate }}</mat-checkbox
        >
        @for (item of listFilterRoomOptions; let i = $index; track item) {
        <div class="checkbox-cls-item typo-body-15">
          <mat-checkbox
            (change)="changeSections($event.checked, 'item', 'room', item)"
            [checked]="item.isSelect"
            class="checkbox-cls"
          >
            {{ item.value }}</mat-checkbox
          >
        </div>
        }
      </div>
    </div>

    <!-- dropdown -->

    <div class="title-dropdown typo-body-15">{{ 'MES-471' | translate }}</div>
    <div class="searchbox-wrap">
      <div class="search-box">
        <input
          type="text"
          class="input-cls-custom input-style-common typo-body-11 fs-12"
          [placeholder]="'MES-14' | translate"
          [formControl]="searchBrokerControl"
        />
        <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
      </div>
      <div class="option-list-cls">
        <mat-checkbox
          (change)="changeSections($event.checked, 'all', 'broker')"
          [checked]="isSelectAllBroker"
          class="checkbox-cls"
        >
          {{ 'MES-58' | translate }}</mat-checkbox
        >
        @for (item of listFilterBrokerOptions; let i = $index; track item) {
        <div class="checkbox-cls-item typo-body-15">
          <mat-checkbox
            (change)="changeSections($event.checked, 'item', 'broker', item)"
            [checked]="item.isSelect"
            class="checkbox-cls"
          >
            {{ item.value }}</mat-checkbox
          >
        </div>
        }
      </div>
    </div>
  </div>

  <div class="work-performance-footer">
    <div (click)="setDefaultFilter()" class="btn typo-button-3">{{ 'MES-32' | translate }}</div>
    <div (click)="submitFilter()" class="btn primary typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
