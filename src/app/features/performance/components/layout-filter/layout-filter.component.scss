.layout-performance-filter-cls {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .layout-performance-header {
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--color--other--divider);
    img[alt='x-cross'] {
      cursor: pointer;
    }
  }
  .layout-performance-body {
    flex: 1;
    overflow: auto;
  }
  .layout-performance-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--color--other--divider);
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn-cls {
      padding: 12px 16px;
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      cursor: pointer;
      &.primary-cls {
        color: var(--color--neutral--white);
        background-color: var(--color--brand--500);
        border-color: var(--color--brand--500);
      }
    }
  }
}

:host {
  width: 100%;
  height: 100%;
}
