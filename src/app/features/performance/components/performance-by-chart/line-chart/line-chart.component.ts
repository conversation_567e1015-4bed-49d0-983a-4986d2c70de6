import { AfterViewInit, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { IDataSetChart } from '../../../models/performance';
import { Chart } from 'chart.js/auto';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { take } from 'rxjs';
import { I18nService } from 'src/app/core/services';

/**
 * LineChartComponent
 */
@Component({
  selector: 'app-line-chart-component',
  templateUrl: './line-chart.component.html',
  styleUrl: './line-chart.component.scss',
})
export class LineChartComponent implements AfterViewInit, OnChanges {
  @Input() id = 'myChart';

  @Input() labels = [''];

  @Input() datasets: IDataSetChart[] = [];

  @Input() labelDropdown = 'MES-566';

  @Input() labelTime = 'MES-377';

  @Input() subText = '';

  chart!: any;

  @Input() dropdownListTime: any[] = [];

  @Input() dropdownList: any[] = [];

  @Output() selectOptionTimeEvent = new EventEmitter<number>();

  @Output() selectOptionOtherEvent = new EventEmitter<number>();

  /**
   * Constructor
   * @param popoverService
   * @param i18n
   */
  constructor(private readonly popoverService: PopoverService, private readonly i18n: I18nService) {}

  /**
   * NgAfterViewInit
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.createChart();
    });
  }

  /**
   * NgOnChanges
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    const { datasets, labels } = changes;
    if (datasets && labels) {
      setTimeout(() => {
        this.chart.data.datasets = this.datasets;
        this.chart.data.labels = this.labels;
        this.chart.update();
      }, 200);
    }
  }

  /**
   * CreateChart
   */
  createChart(): void {
    const ctx = document.getElementById(`${this.id}`) as HTMLCanvasElement;
    const container = ctx.closest('.line-chart-body') as HTMLElement;

    const containerWidth = container.offsetWidth;
    const containerHeight = container.offsetHeight;
    ctx.width = containerWidth;
    ctx.height = containerHeight;
    this.chart = new Chart(ctx, {
      type: 'line',

      data: {
        labels: this.labels,
        datasets: this.datasets,
      },
      options: {
        indexAxis: 'x',
        scales: {
          x: {
            stacked: true,
            grid: {
              display: false,
            },
          },
          y: {
            stacked: true,
            beginAtZero: true,
            display: true,
            grid: {
              display: true,
              tickLength: 0,
              lineWidth: 0.5,
              color: 'rgba(0, 0, 0, 0)',
              drawTicks: false,
            },
          },
        },
        plugins: {
          legend: {
            display: false,
            title: {
              display: false,
            },
          },
          tooltip: {
            enabled: true,
            position: 'nearest',
            // external: (arg) => this.handerExternalTooltip(arg),
            callbacks: {
              label: (tool: any) => {
                const value = tool.raw;
                return `${this.i18n.translate('MES-565')}: ${value}`;
              },
            },
          },
        },
      },
    });
  }

  /**
   * OpenShowOption
   * @param event
   */
  openShowOption(event: MouseEvent) {
    let origin = event.target as HTMLElement;

    if (origin.nodeName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: this.dropdownList,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.labelDropdown = item[0].name;
      this.selectOptionOtherEvent.emit(item[0].value);
    });
  }

  /**
   * OpenShowOptionTime
   * @param event
   */
  openShowOptionTime(event: MouseEvent) {
    let origin = event.target as HTMLElement;

    if (origin.nodeName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: this.dropdownListTime,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.labelTime = item[0].name;
      this.selectOptionTimeEvent.emit(item[0].value);
    });
  }
}
