.line-chart-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  .line-chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-side {
      display: flex;
      flex-direction: column;
      .time-cls {
        color: var(--color--text--subdued);
      }
    }
    .right-side {
      display: flex;
      align-items: center;
      gap: 10px;
      .dropdown-cls {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border: 1px solid var(--color--other--divider);
        border-radius: 8px;
        cursor: pointer;
      }
    }
  }
  .line-chart-body {
    display: flex;
    flex: 1;
  }
}

:host {
  height: 100%;
  width: 100%;
}
