.chart-by-time-container {
  display: flex;
  gap: 16px;
  height: 100%;
  width: 100%;
  overflow: auto;
  .flex-1 {
    flex: 1;
    height: 100%;
    padding: 6px;
  }

  .left-box-cls,
  .right-box-cls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    .chart-box {
      min-height: 400px;
      height: 100%;
      flex: 1;
      display: flex;
      // border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      padding: 4px 8px;
    }
  }
}

:host {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
