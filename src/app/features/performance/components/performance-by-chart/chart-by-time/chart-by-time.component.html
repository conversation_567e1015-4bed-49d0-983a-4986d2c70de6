<div class="chart-by-time-container">
  <div class="left-box-cls flex-1">
    <div class="chart-box">
      <app-bar-chart-component
        [labels]="revenueFeeDataLabels"
        [datasets]="revenueFeeDataSets"
        [subText]="rangeTimeRevenueFeeText"
        [labelTime]="'MES-377'"
        [headerText]="headerTextRevenueFee"
        [id]="'revenue-fee-id'"
        [dropdownList]="ListRevenueFeeOption"
        [dropdownListTime]="ListRevenueFeeTimeOption"
        (selectOptionOtherEvent)="selectRevenueFee($event)"
        (selectOptionTimeEvent)="selectTimeRevenueFee($event)"
      ></app-bar-chart-component>
    </div>

    <div class="chart-box">
      <app-bar-chart-component
        [labelDropdown]="'MES-376'"
        [headerText]="'MES-281'"
        [labelTime]="'MES-377'"
        [labels]="totalMoneyDataLabels"
        [subText]="rangeTimeTotalMoneyText"
        [datasets]="totalMoneyDataSets"
        [id]="'total-money-id'"
        [dropdownListTime]="ListRevenueFeeTimeOption"
        [dropdownList]="RankAccountTransactionOption"
        (selectOptionOtherEvent)="selectTypeMoney($event)"
        (selectOptionTimeEvent)="selectTimeTotalMoney($event)"
      ></app-bar-chart-component>
    </div>
  </div>
  <div class="right-box-cls flex-1">
    <div class="chart-box">
      <app-doughnut-chart-component
        [datasets]="transactionCustomerDataSets"
        [labels]="transactionCustomerDataLabels"
        [dropdownListTime]="ListRevenueFeeTimeOption"
        [id]="'doughnut-chart-id'"
        (selectOptionTimeEvent)="selectTimeTransactionCustomer($event)"
        [subText]="rangeTimeTransactionText"
      ></app-doughnut-chart-component>
    </div>
    <div class="chart-box">
      <app-line-chart-component
        [id]="'line-chart-id'"
        [datasets]="amountNewAccountDataset"
        [labels]="amountNewAccountDataLabels"
        [dropdownListTime]="ListRevenueFeeTimeOption"
        [dropdownList]="NewAccountOption"
        [subText]="rangeTimeNewAccountText"
        (selectOptionTimeEvent)="selectTimeAccount($event)"
        (selectOptionOtherEvent)="selectTypeAccount($event)"
      ></app-line-chart-component>
    </div>
  </div>
</div>
