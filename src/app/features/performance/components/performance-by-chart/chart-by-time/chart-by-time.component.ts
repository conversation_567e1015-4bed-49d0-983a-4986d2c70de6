import { Component, OnInit } from '@angular/core';
import { AmountNewAccountData, RevenueFeeData, TotalMoneyData, TransactionCustomersData } from './fakedata';
import {
  ETypeRevenueFeeOption,
  ETypeTimeOption,
  ETypeTotalMoney,
  IDataSetChart,
  INewAccountData,
  IRevenueFeeData,
  ITotalMoneyData,
  ITransactionData,
} from '../../../models/performance';
import {
  ListRevenueFeeOption,
  ListRevenueFeeTimeOption,
  NewAccountOption,
  RankAccountTransactionOption,
} from '../../../constants/performance';
import { dateToDM } from 'src/app/shared/utils/date';

/**
 * ChartByTimeComponent
 */
@Component({
  selector: 'app-chart-by-time-component',
  templateUrl: './chart-by-time.component.html',
  styleUrl: './chart-by-time.component.scss',
})
export class ChartByTimeComponent implements OnInit {
  revenueFeeDataLabels: string[] = [];

  revenueFeeDataSets: IDataSetChart[] = [];

  totalMoneyDataLabels: string[] = [];

  totalMoneyDataSets: IDataSetChart[] = [];

  transactionCustomerDataLabels: string[] = [];

  transactionCustomerDataSets: IDataSetChart[] = [];

  amountNewAccountDataLabels: string[] = [];

  amountNewAccountDataset: IDataSetChart[] = [];

  ListRevenueFeeOption = ListRevenueFeeOption;

  ListRevenueFeeTimeOption = ListRevenueFeeTimeOption;

  RankAccountTransactionOption = RankAccountTransactionOption;

  NewAccountOption = NewAccountOption;

  rangeTimeRevenueFeeText = '';

  rangeTimeTotalMoneyText = '';

  rangeTimeNewAccountText = '';

  rangeTimeTransactionText = '';

  headerTextRevenueFee = 'MES-374';

  typeTotalMoney = ETypeTotalMoney.DEPOSIT_WITHDRAWAL;
  /**
   * Bỏ khi có api
   */
  typeTimeRevenueFee = ETypeTimeOption.WEEK;
  typeTimeTotalMoney = ETypeTimeOption.WEEK;
  typeTimeAccount = ETypeTimeOption.WEEK;

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.updateRevenueFeeData();
    this.updateTotalMoney();
    this.updateTransactionCustomer();
    this.updateAmountNewAccount();

    const date = new Date();
    const dateprevious = new Date();
    const dateView = dateToDM(date);
    dateprevious.setDate(dateprevious.getDate() - 7);
    const datePreviousView = dateToDM(dateprevious);
    this.rangeTimeTransactionText = `1 tuần (${datePreviousView} - ${dateView})`;
  }

  /**
   * UpdateRevenueFeeData
   * @param isUpdateTime
   * @param newList
   */
  updateRevenueFeeData(isUpdateTime: boolean = true, newList?: IRevenueFeeData[]) {
    const revenueList = newList ?? RevenueFeeData;
    const currentDate = new Date();
    if (isUpdateTime) {
      currentDate.setDate(currentDate.getDate() + 1);
      this.revenueFeeDataLabels = revenueList
        .map((t, index) => {
          const date = new Date(currentDate);
          date.setDate(date.getDate() - index - 1);
          return dateToDM(date);
        })
        .reverse(); // FIXME: remove reverse when have api
    } else {
      this.revenueFeeDataLabels = revenueList.map((t, index) => t.date).reverse();
    }
    this.rangeTimeRevenueFeeText = this.updateLabelRangeTime(this.revenueFeeDataLabels);

    this.revenueFeeDataSets = [
      {
        label: 'Cổ phiếu',
        backgroundColor: '#BAE7A3',
        data: revenueList.map((t) => t.share).reverse(), // FIXME: remove reverse when have api

        borderColor: '#BAE7A3',
        borderWidth: 1,
        maxBarThickness: 40,
        borderSkipped: false,
        categoryPercentage: 1,
      },
      {
        label: 'Phái sinh',
        backgroundColor: '#FDE68A',
        data: revenueList.map((t) => t.derivative).reverse(), // FIXME: remove reverse when have api

        borderColor: '#FDE68A',
        borderWidth: 1,
        maxBarThickness: 40,
        borderSkipped: false,
        categoryPercentage: 1,
      },
      {
        label: 'Trái phiếu',
        backgroundColor: '#FCA5A5',
        data: revenueList.map((t) => t.bonds).reverse(), // FIXME: remove reverse when have api

        borderColor: '#FCA5A5',
        borderWidth: 1,
        maxBarThickness: 40,
        borderSkipped: false,
        categoryPercentage: 1,
      },
    ] as any;
  }

  /**
   * UpdateTotalMoney
   * @param isUpdateTime
   * @param newList
   */
  updateTotalMoney(isUpdateTime: boolean = true, newList?: ITotalMoneyData[]) {
    const totalMoney = newList ?? TotalMoneyData;
    const currentDate = new Date();
    if (isUpdateTime) {
      currentDate.setDate(currentDate.getDate() + 1);
      this.totalMoneyDataLabels = totalMoney
        .map((t, index) => {
          const date = new Date(currentDate);
          date.setDate(date.getDate() - index - 1);
          return dateToDM(date);
        })
        .reverse(); // FIXME: remove reverse when have api
    } else {
      this.totalMoneyDataLabels = totalMoney.map((t, index) => t.date).reverse();
    }
    this.rangeTimeTotalMoneyText = this.updateLabelRangeTime(this.totalMoneyDataLabels);

    this.totalMoneyDataSets = [
      {
        label: this.typeTotalMoney ? 'Tiền mua' : 'Tiền nộp',
        backgroundColor: '#09E384',
        data: totalMoney.map((t) => t.income).reverse(),
        borderColor: '#09E384',
        borderWidth: 1,
        maxBarThickness: 40,
        borderSkipped: false,
        categoryPercentage: 1,
      },
      {
        label: this.typeTotalMoney ? 'Tiền bán' : 'Tiền rút',
        backgroundColor: '#F53126',
        data: totalMoney.map((t) => t.outcome).reverse(),
        borderColor: '#F53126',
        borderWidth: 1,
        maxBarThickness: 40,
        borderSkipped: false,
        categoryPercentage: 1,
      },
    ];
  }

  /**
   * UpdateTransactionCustomer
   * @param newList
   */
  updateTransactionCustomer(newList?: ITransactionData[]) {
    this.transactionCustomerDataLabels = TransactionCustomersData.map((t) => t.name);
    const transactionList = newList ?? TransactionCustomersData;
    this.transactionCustomerDataSets = [
      {
        data: transactionList.map((t) => t.data),
        backgroundColor: ['#00C7BE', '#FF3B30'],
        label: '',
      },
    ];
  }

  /**
   * UpdateAmountNewAccount
   * @param isUpdateTime
   * @param newList
   */
  updateAmountNewAccount(isUpdateTime: boolean = true, newList?: INewAccountData[]) {
    const newAccount = newList ?? AmountNewAccountData;
    const currentDate = new Date();
    if (isUpdateTime) {
      currentDate.setDate(currentDate.getDate() + 1);
      this.amountNewAccountDataLabels = newAccount
        .map((t, index) => {
          const date = new Date(currentDate);
          date.setDate(date.getDate() - index - 1);
          return dateToDM(date);
        })
        .reverse(); // FIXME: remove reverse when have api
    } else {
      this.amountNewAccountDataLabels = newAccount.map((t, index) => t.date).reverse();
    }
    this.rangeTimeNewAccountText = this.updateLabelRangeTime(this.amountNewAccountDataLabels);

    this.amountNewAccountDataset = [
      {
        label: '',
        data: newAccount.map((t) => t.data),
        borderColor: '#55BEF0',
        fill: false,
        tension: 0.1,
      },
    ];
  }

  /**
   * SelectTypeMoney
   * @param type
   */
  selectTypeMoney(type: ETypeTotalMoney) {
    this.typeTotalMoney = type;
    this.selectTimeTotalMoney(this.typeTimeTotalMoney);
  }

  /**
   * SelectRevenueFee
   * @param type
   */
  selectRevenueFee(type: ETypeRevenueFeeOption) {
    switch (type) {
      case ETypeRevenueFeeOption.REVENUE_FEE:
        this.headerTextRevenueFee = 'MES-374';
        break;
      case ETypeRevenueFeeOption.NET:
        this.headerTextRevenueFee = 'MES-195';
        break;
      case ETypeRevenueFeeOption.COMMISSION_FEE:
        this.headerTextRevenueFee = 'MES-196';
        break;
      default:
        break;
    }

    this.selectTimeRevenueFee(this.typeTimeRevenueFee);
  }

  /**
   * SelectTimeRevenueFee
   * @param type
   */
  selectTimeRevenueFee(type: ETypeTimeOption) {
    this.typeTimeRevenueFee = type;
    /**
     * Hàm fake data
     * xóa khi có api
     * @param date
     * @returns {any}data
     */
    const generateRandomRevenueData = (date: Date): IRevenueFeeData => ({
      date: dateToDM(date),
      share: this.getRandomNumberWithDigits(7, 9),
      derivative: this.getRandomNumberWithDigits(7, 9),
      bonds: this.getRandomNumberWithDigits(7, 9),
    });

    /**
     * Xóa khi có api
     * @param length
     * @param currentDate
     * @param dateFunc
     * @returns {any} new list
     */
    const generateDataList = (
      length: number,
      currentDate: Date,
      dateFunc: (date: Date, index: number) => void
    ): IRevenueFeeData[] => {
      const newList: IRevenueFeeData[] = [];
      for (let i = 0; i < length; i++) {
        const date = new Date(currentDate);
        dateFunc(date, i);
        newList.push(generateRandomRevenueData(date));
      }
      return newList;
    };

    switch (type) {
      case ETypeTimeOption.DAY:
        {
          const currentDate = new Date();
          currentDate.setDate(currentDate.getDate() + 1);
          const newList = generateDataList(1, currentDate, (date, i) => date.setDate(date.getDate() - i - 1));
          this.updateRevenueFeeData(false, newList);
        }
        break;

      case ETypeTimeOption.WEEK:
        {
          const currentDate = new Date();
          currentDate.setDate(currentDate.getDate() + 1);
          const newList = generateDataList(7, currentDate, (date, i) => date.setDate(date.getDate() - i - 1));
          this.updateRevenueFeeData(false, newList);
        }
        break;

      case ETypeTimeOption.ONE_MONTH:
        {
          const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
          const currentDate = new Date();
          currentDate.setDate(currentDate.getDate() + 1);
          const newList = generateDataList(daysInMonth, currentDate, (date, i) => date.setDate(date.getDate() - i - 1));
          this.updateRevenueFeeData(false, newList);
        }
        break;

      case ETypeTimeOption.HALF_OF_YEAR:
        {
          const currentDate = new Date();
          currentDate.setMonth(currentDate.getMonth() + 1);
          const newList = generateDataList(6, currentDate, (date, i) => date.setMonth(date.getMonth() - i - 1));
          this.updateRevenueFeeData(false, newList);
        }
        break;

      case ETypeTimeOption.YEAR:
        {
          const currentDate = new Date();
          currentDate.setMonth(currentDate.getMonth() + 1);
          const newList = generateDataList(12, currentDate, (date, i) => date.setMonth(date.getMonth() - i - 1));
          this.updateRevenueFeeData(false, newList);
        }
        break;

      default:
        break;
    }
  }

  /**
   * SelectTimeTotalMoney
   * @param type
   */
  selectTimeTotalMoney(type: ETypeTimeOption) {
    /**
     * Hàm fake data
     * xóa khi có api
     * @param date
     * @returns {any}data
     */
    const generateRandomTotalMoney = (date: Date): ITotalMoneyData => ({
      date: dateToDM(date),
      income: this.getRandomNumberWithDigits(7, 8),
      outcome: -this.getRandomNumberWithDigits(7, 8),
    });

    /**
     * Xóa khi có api
     * @param length
     * @param currentDate
     * @param dateFunc
     * @returns {any} new list
     */
    const generateDataList = (
      length: number,
      currentDate: Date,
      dateFunc: (date: Date, index: number) => void
    ): ITotalMoneyData[] => {
      const newList: ITotalMoneyData[] = [];
      for (let i = 0; i < length; i++) {
        const date = new Date(currentDate);
        dateFunc(date, i);
        newList.push(generateRandomTotalMoney(date));
      }
      return newList;
    };

    switch (type) {
      case ETypeTimeOption.DAY:
        {
          const currentDate = new Date();
          currentDate.setDate(currentDate.getDate() + 1);
          const newList = generateDataList(1, currentDate, (date, i) => date.setDate(date.getDate() - i - 1));
          this.updateTotalMoney(true, newList);
        }
        break;

      case ETypeTimeOption.WEEK:
        {
          const currentDate = new Date();
          currentDate.setDate(currentDate.getDate() + 1);
          const newList = generateDataList(7, currentDate, (date, i) => date.setDate(date.getDate() - i - 1));
          this.updateTotalMoney(true, newList);
        }
        break;

      case ETypeTimeOption.ONE_MONTH:
        {
          const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
          const currentDate = new Date();
          currentDate.setDate(currentDate.getDate() + 1);
          const newList = generateDataList(daysInMonth, currentDate, (date, i) => date.setDate(date.getDate() - i - 1));
          this.updateTotalMoney(false, newList);
        }
        break;

      case ETypeTimeOption.HALF_OF_YEAR:
        {
          const currentDate = new Date();
          currentDate.setMonth(currentDate.getMonth() + 1);
          const newList = generateDataList(6, currentDate, (date, i) => date.setMonth(date.getMonth() - i - 1));
          this.updateTotalMoney(false, newList);
        }
        break;

      case ETypeTimeOption.YEAR:
        {
          const currentDate = new Date();
          currentDate.setMonth(currentDate.getMonth() + 1);
          const newList = generateDataList(12, currentDate, (date, i) => date.setMonth(date.getMonth() - i - 1));
          this.updateTotalMoney(false, newList);
        }
        break;

      default:
        break;
    }
  }

  /**
   * SelectTypeAccount
   * @param type
   */
  selectTypeAccount(type: number) {
    this.selectTimeAccount(this.typeTimeAccount);
  }
  /**
   * Hàm random data số từ 7 -> 9 số
   * Xóa khi có API
   * @param minDigits
   * @param maxDigits
   * @returns {number} data
   */
  getRandomNumberWithDigits(minDigits: number, maxDigits: number) {
    const numDigits = Math.floor(Math.random() * (maxDigits - minDigits + 1)) + minDigits;

    const min = Math.pow(10, numDigits - 1);
    const max = Math.pow(10, numDigits) - 1;

    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * SelectTimeTransactionCustomer
   * @param type
   */
  selectTimeTransactionCustomer(type: ETypeTimeOption) {
    switch (type) {
      case ETypeTimeOption.DAY:
        {
          const date = dateToDM(new Date());
          this.rangeTimeTransactionText = `1 ngày (${date})`;
          const newList = TransactionCustomersData.map((t) => ({ ...t, data: Math.floor(Math.random() * 50) }));
          this.updateTransactionCustomer(newList);
        }
        break;
      case ETypeTimeOption.WEEK:
        {
          const date = new Date();
          const dateprevious = new Date();
          const dateView = dateToDM(date);
          dateprevious.setDate(dateprevious.getDate() - 7);
          const datePreviousView = dateToDM(dateprevious);
          this.rangeTimeTransactionText = `1 tuần (${datePreviousView} - ${dateView})`;
          const newList = TransactionCustomersData.map((t) => ({ ...t, data: Math.floor(Math.random() * 150) }));
          this.updateTransactionCustomer(newList);
        }
        break;
      case ETypeTimeOption.ONE_MONTH:
        {
          const date = new Date();
          const dateprevious = new Date();
          const dateView = dateToDM(date);
          const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
          dateprevious.setDate(dateprevious.getDate() - daysInMonth);
          const datePreviousView = dateToDM(dateprevious);
          this.rangeTimeTransactionText = `1 tháng (${datePreviousView} - ${dateView})`;
          const newList = TransactionCustomersData.map((t) => ({ ...t, data: this.getRandomNumberWithDigits(2, 3) }));
          this.updateTransactionCustomer(newList);
        }
        break;
      case ETypeTimeOption.HALF_OF_YEAR:
        {
          const date = new Date();
          const dateprevious = new Date();
          const dateView = dateToDM(date);
          dateprevious.setMonth(dateprevious.getMonth() - 6);
          const datePreviousView = dateToDM(dateprevious);
          this.rangeTimeTransactionText = `6 tháng (${datePreviousView} - ${dateView})`;
          const newList = TransactionCustomersData.map((t) => ({ ...t, data: this.getRandomNumberWithDigits(3, 4) }));
          this.updateTransactionCustomer(newList);
        }
        break;
      case ETypeTimeOption.YEAR:
        {
          const date = new Date();
          const dateprevious = new Date();
          const dateView = dateToDM(date);
          dateprevious.setMonth(dateprevious.getMonth() - 12);
          const datePreviousView = dateToDM(dateprevious);
          this.rangeTimeTransactionText = `1 năm (${datePreviousView} - ${dateView})`;
          const newList = TransactionCustomersData.map((t) => ({ ...t, data: this.getRandomNumberWithDigits(4, 5) }));
          this.updateTransactionCustomer(newList);
        }
        break;

      default:
        break;
    }
  }

  /**
   * SelectTimeAccount
   * @param type
   */
  selectTimeAccount(type: ETypeTimeOption) {
    this.typeTimeAccount = type;
    /**
     * Hàm fake data
     * xóa khi có api
     * @param date
     * @returns {any}data
     */
    const generateRandomRevenueData = (date: Date): INewAccountData => ({
      date: dateToDM(date),
      data: this.getRandomNumberWithDigits(1, 3),
    });

    /**
     * Xóa khi có api
     * @param length
     * @param currentDate
     * @param dateFunc
     * @returns {any} new list
     */
    const generateDataList = (
      length: number,
      currentDate: Date,
      dateFunc: (date: Date, index: number) => void
    ): INewAccountData[] => {
      const newList: INewAccountData[] = [];
      for (let i = 0; i < length; i++) {
        const date = new Date(currentDate);
        dateFunc(date, i);
        newList.push(generateRandomRevenueData(date));
      }
      return newList;
    };

    switch (type) {
      case ETypeTimeOption.DAY:
        {
          const currentDate = new Date();
          currentDate.setDate(currentDate.getDate() + 1);
          const newList = generateDataList(1, currentDate, (date, i) => date.setDate(date.getDate() - i - 1));
          this.updateAmountNewAccount(false, newList);
        }
        break;

      case ETypeTimeOption.WEEK:
        {
          const currentDate = new Date();
          currentDate.setDate(currentDate.getDate() + 1);
          const newList = generateDataList(7, currentDate, (date, i) => date.setDate(date.getDate() - i - 1));
          this.updateAmountNewAccount(false, newList);
        }
        break;

      case ETypeTimeOption.ONE_MONTH:
        {
          const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
          const currentDate = new Date();
          currentDate.setDate(currentDate.getDate() + 1);
          const newList = generateDataList(daysInMonth, currentDate, (date, i) => date.setDate(date.getDate() - i - 1));
          this.updateAmountNewAccount(false, newList);
        }
        break;

      case ETypeTimeOption.HALF_OF_YEAR:
        {
          const currentDate = new Date();
          currentDate.setMonth(currentDate.getMonth() + 1);
          const newList = generateDataList(6, currentDate, (date, i) => date.setMonth(date.getMonth() - i - 1));
          this.updateAmountNewAccount(false, newList);
        }
        break;

      case ETypeTimeOption.YEAR:
        {
          const currentDate = new Date();
          currentDate.setMonth(currentDate.getMonth() + 1);
          const newList = generateDataList(12, currentDate, (date, i) => date.setMonth(date.getMonth() - i - 1));
          this.updateAmountNewAccount(false, newList);
        }
        break;

      default:
        break;
    }
  }

  /**
   * UpdateLabelRangeTime
   * @param list
   * @returns {string} label
   */
  updateLabelRangeTime(list: string[]) {
    const lenghtData = list.length;
    const label = `${list[0]} - ${list[lenghtData - 1]}`;
    if (lenghtData > 28) {
      return `1 tháng (${label})`;
    } else if (lenghtData === 6) {
      return `6 tháng (${label})`;
    } else if (lenghtData === 12) {
      return `12 tháng (${label})`;
    } else if (lenghtData === 7) {
      return `1 tuần (${label})`;
    } else {
      return `1 ngày (${list[0]})`;
    }
  }
}
