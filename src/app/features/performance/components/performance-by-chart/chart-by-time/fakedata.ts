import { INewAccountData, IRevenueFeeData, ITotalMoneyData, ITransactionData } from '../../../models/performance';

export const RevenueFeeData: IRevenueFeeData[] = [
  {
    date: '20/03',
    share: 6000000,
    derivative: 3000000,
    bonds: 4000000,
  },
  {
    date: '21/03',
    share: 3452345,
    derivative: 8503456,
    bonds: 3934721,
  },
  {
    date: '22/03',
    share: 6432028,
    derivative: 2403284,
    bonds: 1923945,
  },
  {
    date: '23/03',
    share: 3843023,
    derivative: 3000000,
    bonds: 5490235,
  },
  {
    date: '24/03',
    share: 9341213,
    derivative: ********,
    bonds: ********,
  },
  {
    date: '25/03',
    share: ********,
    derivative: ********,
    bonds: ********,
  },
  {
    date: '26/03',
    share: ********,
    derivative: ********,
    bonds: 2352346,
  },
];

export const TotalMoneyData: ITotalMoneyData[] = [
  {
    date: '20/03',
    income: 1100000,
    outcome: -********,
  },
  {
    date: '21/03',
    income: ********,
    outcome: -34568346,
  },
  {
    date: '22/03',
    income: 25343465,
    outcome: -********,
  },
  {
    date: '23/03',
    income: ********,
    outcome: -********,
  },
  {
    date: '24/03',
    income: ********,
    outcome: -********,
  },
  {
    date: '25/03',
    income: ********,
    outcome: -********,
  },
  {
    date: '26/03',
    income: ********,
    outcome: -********,
  },
];

export const TransactionCustomersData: ITransactionData[] = [
  {
    name: 'Có giao dịch',
    data: 50,
  },
  {
    name: 'Không giao dịch',
    data: 50,
  },
];

export const AmountNewAccountData: INewAccountData[] = [
  {
    date: '20/03',
    data: 65,
  },
  {
    date: '21/03',
    data: 59,
  },
  {
    date: '22/03',
    data: 80,
  },
  {
    date: '23/03',
    data: 40,
  },
  {
    date: '24/03',
    data: 56,
  },
  {
    date: '25/03',
    data: 55,
  },
  {
    date: '26/03',
    data: 40,
  },
];
