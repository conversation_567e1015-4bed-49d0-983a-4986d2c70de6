import { AfterViewInit, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { Chart } from 'chart.js';
import { IDataSetChart } from '../../../models/performance';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { take } from 'rxjs';
import { CONVERT_PROPORTIONREVENUEFEE_TO_NUMBER, EProportionRevenueFee } from '../../../constants/performance';

/**
 * PieChartComponent
 */
@Component({
  selector: 'app-pie-chart-component',
  templateUrl: './pie-chart.component.html',
  styleUrl: './pie-chart.component.scss',
})
export class PieChartComponent implements AfterViewInit, OnChanges {
  @Input() id = 'myChart';

  @Input() labels = [''];

  @Input() datasets: IDataSetChart[] = [];

  chart!: any;

  @Input() dropdownListTime: any[] = [];

  @Input() dropdownList: any[] = [];

  @Input() labelDropdown = 'MES-374';

  @Input() labelTime = 'MES-377';

  @Output() selectOptionTimeEvent = new EventEmitter<number>();

  @Output() selectOptionOtherEvent = new EventEmitter<number>();

  /**
   * constructor
   * @param popoverService
   */
  constructor(private readonly popoverService: PopoverService) {}

  /**
   * NgAfterViewInit
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.createChart();
    });
  }

  /**
   * NgOnChanges
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    const { datasets, labels } = changes;
    if (datasets && labels) {
      setTimeout(() => {
        // pie-chart
        this.chart.data.labels = this.labels;
        this.chart.data.datasets = this.datasets;
        this.chart.update();
      }, 200);
    }
  }

  /**
   * CreateChart
   */
  createChart(): void {
    const ctxPie = document.getElementById(`${this.id}`) as HTMLCanvasElement;

    const containerWidth = 700;
    ctxPie.width = 700;
    ctxPie.height = 700;

    /**
     * Tìm vector cho phương trình đường thẳng
     * @param {any} point
     * @param {any} center
     * @returns {any} vector
     */
    const vectorDirection = (point: any, center: any) => {
      const { x: xA, y: yA } = point;
      const { x: xCenter, y: yCenter } = center;
      return { x: xA - xCenter, y: yA - yCenter };
    };

    /**
     * Tìm tạo độ giao điểm của đường thẳng và đường tròn
     * @param {any} vector
     * @param radius
     * @param {any} point
     * @returns {any} tọa độ
     */
    const coordinateIntersectionCircleAndLine = (vector: any, radius: number, point: any) => {
      const { x: xPoint, y: yPoint } = point;
      const { x: xVector, y: yVector } = vector;

      /**
       * Phương trình đường thẳng là
       * mx + b = y
       * m là parameterXLine
       * b là valueEquationLine
       */
      const parameterXLine = yVector / xVector;
      const valueEquationLine = (xVector * yPoint - xPoint * yVector) / xVector;

      /**
       * Thay phương trình đường thằng vào phương trình đường tròn ta được
       * tọa độ tâm là độ dài của bán kính R = radius
       * (x - R)^2 + (mx + b - R)^2 = R^2
       * phương trình mới Ax^2 +  Bx + C = 0
       */
      const A = 1 + parameterXLine * parameterXLine;
      const B = 2 * (parameterXLine * (valueEquationLine - radius) - radius);

      const C =
        valueEquationLine ** 2 - 2 * radius * valueEquationLine + 2 * radius ** 2 - ((radius * 2 - 200) / 2) ** 2;
      const delta = B * B - 4 * A * C;

      /**
       * Tọa độ phương trình có 2 điểm
       */
      const x1 = (-B + Math.sqrt(delta)) / (2 * A);
      const coordinateFirst = {
        x: x1,
        y: parameterXLine * x1 + valueEquationLine,
      };
      const x2 = (-B - Math.sqrt(delta)) / (2 * A);
      const coordinateSecond = {
        x: x2,
        y: parameterXLine * x2 + valueEquationLine,
      };

      /**
       * Tìm tọa độ thỏa mãn bằng cách tìm khoảng cách từ 1 điểm đến điểm tìm được
       * Tọa độ thỏa mãn là khoảng cách nhỏ hơn
       */
      const distanceFirst = calculateDistance(coordinateFirst, point);
      const distanceSecond = calculateDistance(coordinateSecond, point);

      if (distanceFirst < distanceSecond) {
        return coordinateFirst;
      } else {
        return coordinateSecond;
      }
    };

    /**
     * CalculateDistance
     * @param {any} pointA
     * @param {any} pointB
     * @returns {any} coordinate
     */
    const calculateDistance = (pointA: any, pointB: any) => {
      const { x: xA, y: yA } = pointA;
      const { x: xB, y: yB } = pointB;

      return Math.sqrt((xB - xA) * (xB - xA) + (yB - yA) * (yB - yA));
    };

    const customChart = {
      id: 'customChartid',
      /**
       * AfterDraw
       * @param chart
       * @param args
       * @param options
       */
      afterDraw(chart: any) {
        const {
          ctx,
        } = chart;
        chart.data.datasets.forEach((dataset: any, index: number) => {
          chart.getDatasetMeta(index).data.forEach((datapoint: any, i: number) => {
            const { x, y } = datapoint.tooltipPosition();
            const radius = containerWidth / 2;
            const { x: xIntersection, y: yIntersection } = coordinateIntersectionCircleAndLine(
              vectorDirection({ x, y }, { x: radius, y: radius }),
              radius,
              { x, y }
            );
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(xIntersection, yIntersection);
            ctx.strokeStyle = dataset.backgroundColor[i];
            ctx.stroke();

            let xLine = xIntersection;
            let yLine = yIntersection;

            // Xét vị trí của text được thêm vào
            // Góc phần tư thứ nhất
            if (x > radius && y < radius) {
              xLine = xIntersection + 5;
              yLine = yIntersection - 10;
            } else if (x > radius && y > radius) {
              // Góc phần tư thứ 4
              yLine = yIntersection - 10;
              xLine = xIntersection - 10;
            } else if (x < radius && y > radius) {
              // Góc phần tư thứ 3
              yLine = yIntersection + 15;
              xLine = xIntersection - 10;
            } else if (x < radius && y < radius) {
              // Góc phần tư thứ 2
              yLine = yIntersection - 15;
              xLine = xIntersection - 5;
            }
            const text = `${dataset.dataConfig.name[i]}`;
            ctx.font = '14px Inter';
            ctx.fillStyle = '#33343e';
            ctx.fillText(text, xLine, yLine);
          });
        });
      },
    };

    this.chart = new Chart(ctxPie, {
      type: 'pie',

      data: {
        labels: this.labels,
        datasets: this.datasets,
      },
      options: {
        responsive: true,
        layout: {
          padding: {
            top: 150,
            bottom: 150,
            left: 150,
            right: 150,
          },
        },
        plugins: {
          legend: {
            display: false,
            position: 'top',
          },
          title: {
            display: false,
          },
          tooltip: {
            enabled: false,
            position: 'nearest',
            external: (arg: any) => this.handerExternalTooltip(arg),
          },
          datalabels: {
            color: '#33343E',
            font: {
              size: 14,
            },
            formatter: (value: any, context: any) => {
              const datapoints = context.chart.data.datasets[0].data;
              /**
               * TotalSum
               * @param total
               * @param datapoints
               * @returns {number} total
               */
              const totalSum = (total: number, datapoints: number) => {
                return total + datapoints;
              };
              const totalValue = datapoints.reduce(totalSum, 0);
              if ((value / totalValue) * 100 < 5) {
                return '';
              }
              const percentageValue = ((value / totalValue) * 100).toFixed(2);
              return `${percentageValue}%`;
            },
          },
        },
        onHover: (event: Event, activeElements: any) => {
          if (activeElements.length > 0) {
            const activeIndex = activeElements[0].index;
            this.chart!.data.datasets[0].backgroundColor = this.chart!.data.datasets[0].backgroundColor.map(
              (color: string, index: number) => {
                return index === activeIndex ? color : color.replace(', 1)', ', 0.5)');
              }
            );
          } else {
            this.chart!.data.datasets[0].backgroundColor = this.chart!.data.datasets[0].backgroundColor.map(
              (color: string) => {
                return color.replace('0.5', '1');
              }
            );
          }
          this.chart!.update();
        },
        onLeave: () => {
          this.resetColors();
        },
        events: ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'],

        rotation: 0,
        cutout: '10%',
      },

      plugins: [ChartDataLabels, customChart],
    } as any);
  }

  /**
   * ResetColors
   */
  resetColors() {
    this.chart!.data.datasets[0].backgroundColor = [...['#FFD60A', '#E28FFF', '#FCA5A5', '#93C5FD', '#98989D']];
    this.chart!.update();
  }

  /**
   * HanderExternalTooltip
   * @param context
   * @returns {any} data
   */
  handerExternalTooltip(context: any) {
    const { chart, tooltip } = context;
    const tooltipEl = this.getOrCreateTooltip(chart);
    if (tooltip.opacity === 0) {
      tooltipEl.style.opacity = 0;
      return;
    }
    if (tooltip.dataPoints) {
      const { label, parsed, dataset } = tooltip.dataPoints[0];
      const divElement = document.createElement('div');
      divElement.style.display = 'flex';
      divElement.style.gap = '6px';
      divElement.style.padding = '2px 4px';

      const totalSum = (total: number, datapoints: number) => {
        return total + datapoints;
      };

      const totalValue = dataset.data.reduce(totalSum, 0);
      const percent = ((parsed / totalValue) * 100).toFixed(2);
      const indexName = dataset.dataConfig.name.findIndex((t: string) => t === label);

      let textDetail: any;
      switch (CONVERT_PROPORTIONREVENUEFEE_TO_NUMBER[this.labelDropdown]) {
        case EProportionRevenueFee.TOPBROKERROOMS:
          textDetail = document.createTextNode(`${label}: ${dataset.dataConfig.broker[indexName]} - ${percent}%`);

          break;
        case EProportionRevenueFee.TOPBROKERS:
          textDetail = document.createTextNode(`${label}: ${percent}%`);
          break;
      }

      const textBox = document.createElement('span');
      textBox.appendChild(textDetail);
      textBox.style.fontSize = '12px';
      divElement.appendChild(textBox);
      const divTooltip = tooltipEl.querySelector('.divElementTooltip');
      while (divTooltip.firstChild) {
        divTooltip.firstChild.remove();
      }

      divTooltip.appendChild(divElement);
    }
    const { offsetLeft: positionX, offsetTop: positionY } = chart.canvas;

    // Display, position, and set styles for font
    tooltipEl.style.opacity = 1;
    tooltipEl.style.left = positionX + tooltip.caretX + 'px';
    tooltipEl.style.top = positionY + tooltip.caretY + 'px';
    tooltipEl.style.font = tooltip.options.bodyFont.string;
    tooltipEl.style.padding = tooltip.options.padding + 'px ' + tooltip.options.padding + 'px';
  }

  /**
   * GetOrCreateTooltip
   * @param chart
   * @returns {any} tooltip
   */
  getOrCreateTooltip(chart: any) {
    let tooltipEl = chart.canvas.parentNode.querySelector('div');

    if (!tooltipEl) {
      tooltipEl = document.createElement('div');
      tooltipEl.style.background = 'rgba(51, 52, 62, 0.80)';
      tooltipEl.style.borderRadius = '6px';
      tooltipEl.style.color = 'white';
      tooltipEl.style.opacity = 1;
      tooltipEl.style.pointerEvents = 'none';
      tooltipEl.style.position = 'absolute';
      tooltipEl.style.transform = 'translate(-50%, 0)';
      tooltipEl.style.transition = 'all .1s ease';

      const divElement = document.createElement('div');
      divElement.style.margin = '0px';
      divElement.classList.add('divElementTooltip');

      tooltipEl.appendChild(divElement);
      chart.canvas.parentNode.appendChild(tooltipEl);
    }

    return tooltipEl;
  }
  /**
   * OpenShowOption
   * @param event
   */
  openShowOption(event: MouseEvent) {
    let origin = event.target as HTMLElement;

    if (origin.nodeName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: this.dropdownList,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.labelDropdown = item[0].name;
      this.selectOptionOtherEvent.emit(item[0].value);
    });
  }

  /**
   * OpenShowOptionTime
   * @param event
   */
  openShowOptionTime(event: MouseEvent) {
    let origin = event.target as HTMLElement;

    if (origin.nodeName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: this.dropdownListTime,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.labelTime = item[0].name;
      this.selectOptionTimeEvent.emit(item[0].value);
    });
  }
}
