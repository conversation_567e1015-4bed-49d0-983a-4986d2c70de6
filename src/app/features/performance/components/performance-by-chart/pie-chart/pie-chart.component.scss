.pie-chart-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  padding-bottom: 30px;
  .pie-chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-side {
      display: flex;
      flex-direction: column;
      .time-cls {
        color: var(--color--text--subdued);
      }
    }
    .right-side {
      display: flex;
      align-items: center;
      gap: 10px;
      .dropdown-cls {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border: 1px solid var(--color--other--divider);
        border-radius: 8px;
        cursor: pointer;
      }
    }
  }
  .pie-chart-body {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pie-chart-note {
    display: flex;
    align-items: center;
    gap: 20px;
    justify-content: center;

    .box-color-note {
      display: flex;
      flex-direction: column;
      gap: 4px;
      align-items: center;
      .color-box {
        height: 12px;
        width: 12px;
      }
    }
  }
}

:host {
  height: 100%;
  width: 100%;
}
