<div class="pie-chart-container">
  <div class="pie-chart-header">
    <div class="left-side">
      <div class="title-cls typo-body-3">{{ 'MES-393' | translate }}</div>
    </div>
    <div class="right-side">
      <div class="dropdown-cls typo-body-7" (click)="openShowOption($event)">
        {{ labelDropdown | translate }}
        <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
      </div>
      <div class="dropdown-cls typo-body-7" (click)="openShowOptionTime($event)">
        {{ labelTime | translate }}

        <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
      </div>
    </div>
  </div>
  <div class="pie-chart-body">
    <div class="chart">
      <canvas [id]="id"></canvas>
    </div>
  </div>
  <div class="pie-chart-note">
    <div *ngFor="let label of labels; let i = index">
      <div class="box-color-note">
        <div class="color-box" [style.background]="datasets[0].backgroundColor?.[i]"></div>
        <div class="typo-body-9">{{ label }}</div>
      </div>
    </div>
  </div>
</div>
