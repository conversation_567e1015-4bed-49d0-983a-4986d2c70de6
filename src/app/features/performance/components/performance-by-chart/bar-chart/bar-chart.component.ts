import { AfterViewInit, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { Chart } from 'chart.js/auto';
import { IDataSetChart } from '../../../models/performance';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { take } from 'rxjs';
import { customNumberFormat } from 'src/app/shared/utils/currency';

/**
 * BarChartComponent
 */
@Component({
  selector: 'app-bar-chart-component',
  templateUrl: './bar-chart.component.html',
  styleUrl: './bar-chart.component.scss',
})
export class BarChartComponent implements AfterViewInit, OnChanges {
  @Input() id = 'myChart';

  @Input() labels = [''];

  @Input() labelDropdown = 'MES-374';

  @Input() labelTime = 'MES-375';

  @Input() headerText = '';

  @Input() subText = '1 ngày (25/03)';

  @Input() datasets: IDataSetChart[] = [];

  @Input() typeShowValue: 'money' | 'amount' = 'money';

  chart!: Chart;

  @Input() dropdownList: any[] = [];

  @Input() dropdownListTime: any[] = [];

  @Output() selectOptionTimeEvent = new EventEmitter<number>();

  @Output() selectOptionOtherEvent = new EventEmitter<number>();

  @Input() notNagativeNumber = false;

  /**
   * Constructor
   * @param popoverService
   */
  constructor(private readonly popoverService: PopoverService) {}

  /**
   * NgAfterViewInit
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.createChart();
    });
  }

  /**
   * NgOnChanges
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    const { datasets, labels } = changes;
    // bar chart
    if (datasets && labels) {
      setTimeout(() => {
        this.chart.data.datasets = this.datasets;
        this.chart.data.labels = this.labels;
        this.chart.update();
      }, 200);
    }
  }

  /**
   * CreateChart
   */
  createChart(): void {
    const ctxBar = document.getElementById(`${this.id}`) as HTMLCanvasElement;
    const container = ctxBar.closest('.statistic-chart-cls') as HTMLElement;

    const containerWidth = container.offsetWidth;
    const containerHeight = container.offsetHeight;
    ctxBar.width = containerWidth;
    ctxBar.height = containerHeight;

    this.chart = new Chart(ctxBar, {
      type: 'bar',

      data: {
        labels: this.labels,
        datasets: this.datasets,
      },
      options: {
        indexAxis: 'x',
        scales: {
          x: {
            stacked: true,
            grid: {
              display: false,
            },
          },
          y: {
            stacked: true,
            beginAtZero: true,
            display: true,
            ticks: {
              /**
               * Callback
               * @param value
               * @returns {string} label
               */
              callback: (value) => this.convertTimeLable(value),
            },
            grid: {
              display: true,
              tickLength: 0,
              lineWidth: 0.5,
              /**
               * Color
               * @param context
               * @returns {string} color
               */
              color: function (context) {
                if (context.tick.value === 0) {
                  return 'rgba(0, 0, 0, 0.2)';
                }
                return 'transparent';
              },
              drawTicks: false,
            },
          },
        },
        plugins: {
          legend: {
            display: false,
            title: {
              display: false,
            },
          },
          tooltip: {
            enabled: true,
            position: 'nearest',
            callbacks: {
              /**
               * Label
               * @param tool
               * @returns {string} new label
               */
              label: (tool: any) => {
                let label = tool.dataset.label ?? '';

                const value = customNumberFormat(this.notNagativeNumber ? Math.abs(tool.raw) : tool.raw);

                if (label) {
                  label += ': ';
                }
                label += value;
                return label;
              },
            },
            // external: (arg) => this.handerExternalTooltip(arg),
          },
        },
      },
    });
  }

  /**
   * ConvertTimeLable
   * @param value
   * @returns {any} value
   */
  convertTimeLable(value: number | string) {
    if (!value) return '';
    if (this.typeShowValue === 'money') {
      if (value.toString().length > 6) return value.toString().slice(0, -6) + 'tr';
      else return value;
    } else {
      return +value < 0 ? +value * -1 : value;
    }
  }

  /**
   * OpenShowOption
   * @param event
   */
  openShowOption(event: MouseEvent) {
    let origin = event.target as HTMLElement;

    if (origin.nodeName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: this.dropdownList,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.labelDropdown = item[0].name;
      this.selectOptionOtherEvent.emit(item[0].value);
    });
  }

  /**
   * OpenShowOptionTime
   * @param event
   */
  openShowOptionTime(event: MouseEvent) {
    let origin = event.target as HTMLElement;

    if (origin.nodeName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }
    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: this.dropdownListTime,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.labelTime = item[0].name;
      this.selectOptionTimeEvent.emit(item[0].value);
    });
  }
}
