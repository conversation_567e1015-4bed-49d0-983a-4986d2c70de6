.bar-chart-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  .header-bar-chart {
    display: flex;
    align-items: center;
    .left-side {
      display: flex;
      flex-direction: column;
      flex: 1;
      .time {
        color: var(--color--text--subdued);
      }
    }
    .right-side {
      display: flex;
      align-items: center;
      gap: 10px;
      justify-content: flex-end;
      flex: 1;
      .select-box {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border: 1px solid var(--color--other--divider);
        border-radius: 8px;
        cursor: pointer;
      }
    }
  }
  .note-cls {
    display: flex;
    align-items: center;
    gap: 8px;
    .box-show {
      display: flex;
      align-items: center;
      gap: 4px;
      .color-box {
        height: 10px;
        width: 10px;
        border-radius: 50%;
      }
    }
  }
  .statistic-chart-cls {
    flex: 1;
  }
}

:host {
  height: 100%;
  width: 100%;
}
