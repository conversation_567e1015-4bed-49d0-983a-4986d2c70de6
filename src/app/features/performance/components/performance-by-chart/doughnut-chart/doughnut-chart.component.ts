import { AfterViewInit, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { Chart } from 'chart.js';
import { IDataSetChart } from '../../../models/performance';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { take } from 'rxjs';

/**
 * DoughnutChartComponent
 */
@Component({
  selector: 'app-doughnut-chart-component',
  templateUrl: './doughnut-chart.component.html',
  styleUrl: './doughnut-chart.component.scss',
})
export class DoughnutChartComponent implements AfterViewInit, OnChanges {
  @Input() id = 'myChart';

  @Input() labels = [''];

  @Input() datasets: IDataSetChart[] = [];

  @Input() labelTime = 'MES-377';

  chart!: any;

  @Input() dropdownListTime: any[] = [];

  @Input() subText = '';

  @Output() selectOptionTimeEvent = new EventEmitter<number>();

  /**
   * Constructor
   * @param popoverService
   */
  constructor(private readonly popoverServiceDoughnut: PopoverService) {}

  /**
   * NgAfterViewInit
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.createChart();
    });
  }

  /**
   * NgOnChanges
   * @param changes
   */
  ngOnChanges(douChanges: SimpleChanges): void {
    const { datasets, labels } = douChanges;
    // doughnu chart
    if (datasets && labels) {
      setTimeout(() => {
        this.chart.data.datasets = this.datasets;
        this.chart.data.labels = this.labels;
        this.chart.update();
      }, 200);
    }
  }

  /**
   * CreateChart
   */
  createChart(): void {
    const ctxDoughChart = document.getElementById(`${this.id}`) as HTMLCanvasElement;
    const container = ctxDoughChart.closest('.chart') as HTMLElement;

    const containerWidth = container.offsetWidth;
    const containerHeight = container.offsetHeight;
    ctxDoughChart.width = containerWidth;
    ctxDoughChart.height = containerHeight;
    this.chart = new Chart(ctxDoughChart, {
      type: 'doughnut',

      data: {
        datasets: this.datasets,
        labels: this.labels,
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: false,
            position: 'top',
          },
          title: {
            display: false,
          },
          tooltip: {
            enabled: false,
          },
        },
        rotation: 270,
        cutout: '80%',
      },
    });
  }

  /**
   * OpenShowOptionTime
   * @param event
   */
  openShowOptionTime(event: MouseEvent) {
    let origin = event.target as HTMLElement;

    if (origin.nodeName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }

    const ref = this.popoverServiceDoughnut.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: this.dropdownListTime,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.labelTime = item[0].name;
      this.selectOptionTimeEvent.emit(item[0].value);
    });
  }

  /**
   * CalculatorPercent
   * @returns {any} data
   */
  calculatorPercent() {
    const value = this.datasets[0].data[0];
    const total = this.datasets[0].data[0] + this.datasets[0].data[1];
    return { hasTrans: ((value / total) * 100).toFixed(0), notHasTrans: ((1 - value / total) * 100).toFixed(0) };
  }
}
