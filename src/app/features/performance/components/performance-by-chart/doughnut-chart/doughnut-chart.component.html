<div class="doughnut-chart-container">
  <div class="doughnut-chart-header">
    <div class="left-side">
      <div class="title-cls typo-body-3">{{ 'MES-378' | translate }}</div>
      <div class="time-cls typo-body-9">{{ subText }}</div>
    </div>
    <div class="right-side">
      <div class="dropdown-cls typo-body-7" (click)="openShowOptionTime($event)">
        {{ labelTime | translate }}
        <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
      </div>
    </div>
  </div>
  <div class="doughnut-chart-body">
    <div class="box-info-cls">
      <div class="title-box-info typo-body-6">{{ 'MES-379' | translate }}</div>
      <div class="amount-account-cls typo-heading-6">{{ datasets[0].data[0] }}</div>
      <div class="subscript-cls typo-body-9">{{ calculatorPercent().hasTrans }}% {{ 'MES-383' | translate }}</div>
    </div>
    <div class="chart">
      <canvas [id]="id"></canvas>
      <div class="info-chart">
        <div class="typo-body-6">{{ 'MES-381' | translate }}</div>
        <div class="typo-heading-7">{{ datasets[0].data[0] + datasets[0].data[1] }}</div>
      </div>
    </div>
    <div class="box-info-cls">
      <div class="title-box-info typo-body-6 other-title">{{ 'MES-380' | translate }}</div>
      <div class="amount-account-cls typo-heading-6">{{ datasets[0].data[1] }}</div>
      <div class="subscript-cls typo-body-9">{{ calculatorPercent().notHasTrans }}% {{ 'MES-383' | translate }}</div>
    </div>
  </div>
</div>
