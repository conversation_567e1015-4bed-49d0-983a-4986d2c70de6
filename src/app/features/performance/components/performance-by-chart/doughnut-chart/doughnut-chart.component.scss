.doughnut-chart-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  .doughnut-chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-side {
      display: flex;
      flex-direction: column;
      .time-cls {
        color: var(--color--text--subdued);
      }
    }
    .right-side {
      display: flex;
      align-items: center;
      gap: 10px;
      .dropdown-cls {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border: 1px solid var(--color--other--divider);
        border-radius: 8px;
        cursor: pointer;
      }
    }
  }
  .doughnut-chart-body {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: space-around;
    gap: 10px;
    .box-info-cls {
      border-radius: 8px;
      background-color: var(--color--background--1);
      display: flex;
      flex-direction: column;
      gap: 18px;
      padding: 14px;
      align-items: center;
      max-width: 240px;
      .title-box-info {
        color: var(--color--accents--mint);
        white-space: nowrap;
        text-wrap: nowrap;
        &.other-title {
          color: var(--color--accents--pink);
        }
      }
      .subscript-cls {
        color: var(--color--text--subdued);
      }
    }
    .chart {
      position: relative;
      .info-chart {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
  }
}

:host {
  height: 100%;
  width: 100%;
}
