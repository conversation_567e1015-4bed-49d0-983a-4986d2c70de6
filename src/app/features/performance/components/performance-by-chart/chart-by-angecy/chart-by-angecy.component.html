<div class="chart-by-angecy-container">
  <div class="left-box-cls flex-1">
    <div class="chart-box">
      <app-bar-chart-component
        [headerText]="'MES-385'"
        [subText]="subTextRankRevenueFee"
        [labelTime]="'MES-377'"
        [labelDropdown]="'MES-387'"
        [id]="'ranking-fee-id'"
        [labels]="rankingRevenueFeeDataLabels"
        [datasets]="rankingRevenueFeeDatasets"
        [dropdownList]="ProportionRevenueFeeOption"
        [dropdownListTime]="ListRevenueFeeTimeOption"
        (selectOptionOtherEvent)="selectRankRevenueFee($event)"
        (selectOptionTimeEvent)="selectTimeRankRevenueFee($event)"
      ></app-bar-chart-component>
    </div>

    <div class="chart-box">
      <app-bar-chart-component
        [labelDropdown]="'MES-387'"
        [subText]="subTextRankAccount"
        [headerText]="'MES-386'"
        [labelTime]="'MES-377'"
        [id]="'total-money-id'"
        [labels]="rankingAccountDataLabels"
        [datasets]="rankingAccountDatasets"
        [typeShowValue]="'amount'"
        [dropdownListTime]="ListRevenueFeeTimeOption"
        [dropdownList]="ProportionRevenueFeeOption"
        [notNagativeNumber]="true"
        (selectOptionOtherEvent)="selectRankAccount($event)"
        (selectOptionTimeEvent)="selectTimeRankAccount($event)"
      ></app-bar-chart-component>
    </div>
  </div>
  <div class="right-box-cls flex-1">
    <app-pie-chart-component
      [labelTime]="'MES-377'"
      [labelDropdown]="'MES-387'"
      [labels]="proportionRevenueFeeDataLabels"
      [datasets]="proportionRevenueFeeDatasets"
      [dropdownListTime]="ListRevenueFeeTimeOption"
      [dropdownList]="ProportionRevenueFeeOption"
      (selectOptionOtherEvent)="selectProportionRevenue($event)"
      (selectOptionTimeEvent)="selectTimeProportionRevenue($event)"
    ></app-pie-chart-component>
  </div>
</div>
