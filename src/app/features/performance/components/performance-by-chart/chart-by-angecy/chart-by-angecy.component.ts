import { Component, OnInit } from '@angular/core';
import {
  EBrokerAndRoomType,
  ETypeTimeOption,
  IDataSetChart,
  IProportionRevenueData,
  IRankingAccountData,
  IRankingRevenueFeeData,
} from '../../../models/performance';
import {
  List_of_broker_proportion_revenue,
  List_of_room_broker,
  List_of_room_proportion_revenue,
  ProportionRevenueFeeData,
  RankingAccountData,
  RankingRevenueFeeData,
} from './fakedata';
import {
  ListRevenueFeeOption,
  ListRevenueFeeTimeOption,
  ProportionRevenueFeeOption,
  RankAccountTransactionOption,
} from '../../../constants/performance';
import { dateToDM, dateToDMY } from 'src/app/shared/utils/date';

/**
 * ChartByAngecyComponent
 */
@Component({
  selector: 'app-chart-by-angecy-component',
  templateUrl: './chart-by-angecy.component.html',
  styleUrl: './chart-by-angecy.component.scss',
})
export class ChartByAngecyComponent implements OnInit {
  rankingRevenueFeeDataLabels: string[] = [];

  rankingRevenueFeeDatasets: IDataSetChart[] = [];

  rankingAccountDataLabels: string[] = [];

  rankingAccountDatasets: IDataSetChart[] = [];

  proportionRevenueFeeDataLabels: string[] = [];

  proportionRevenueFeeDatasets: IDataSetChart[] = [];

  ListRevenueFeeOption = ListRevenueFeeOption;

  ListRevenueFeeTimeOption = ListRevenueFeeTimeOption;

  RankAccountTransactionOption = RankAccountTransactionOption;

  ProportionRevenueFeeOption = ProportionRevenueFeeOption;

  subTextRankRevenueFee = '';
  subTextRankAccount = '';

  typeTimeRankRevenueFee = ETypeTimeOption.WEEK;
  typeTimeRankAccount = ETypeTimeOption.WEEK;
  typeTimeProportionRevenue = ETypeTimeOption.WEEK;

  /**
   * Xóa khi có api
   */
  nameListBroker: string[] = [];

  nameListRoom: string[] = [];

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.updateRankingRevenueFeeData();
    this.updateRankingAccountData();
    this.updateProportionRevenueFeeData();

    this.subTextRankRevenueFee = this.updateLabelRangeTime(this.typeTimeRankRevenueFee);
    this.subTextRankAccount = this.updateLabelRangeTime(this.typeTimeRankAccount);
    /**
     * Xóa khi có api
     */
    this.nameListBroker = RankingRevenueFeeData.map((t) => t.name);
    this.nameListRoom = [...List_of_room_broker];
  }

  /**
   * UpdateRankingRevenueFeeData
   * @param newList
   */
  updateRankingRevenueFeeData(newList?: IRankingRevenueFeeData[]) {
    this.rankingRevenueFeeDataLabels = RankingRevenueFeeData.map((t) => t.name);
    const newListData = newList ?? RankingRevenueFeeData;
    this.rankingRevenueFeeDatasets = [
      {
        label: 'Net phí GD',
        backgroundColor: '#28CD41',
        data: newListData.map((t) => t.transactionFee),
        borderColor: '#28CD41',
        borderWidth: 1,
        maxBarThickness: 40,
        borderSkipped: false,
        categoryPercentage: 1,
      },
      {
        label: 'Hoa hồng MG',
        backgroundColor: '#FFCC00',
        data: newListData.map((t) => t.commissionBroker),
        borderColor: '#FFCC00',
        borderWidth: 1,
        maxBarThickness: 40,
        borderSkipped: false,
        categoryPercentage: 1,
      },
    ];
  }

  /**
   * UpdateRankingAccountData
   * @param newList
   */
  updateRankingAccountData(newList?: IRankingAccountData[]) {
    this.rankingAccountDataLabels = RankingAccountData.map((t) => t.name);
    const newListData = newList ?? RankingAccountData;

    this.rankingAccountDatasets = [
      {
        label: 'Tài khoản có GD',
        backgroundColor: '#97DC76',
        data: newListData.map((t) => t.hasTransaction),
        borderColor: '#97DC76',
        borderWidth: 1,
        maxBarThickness: 40,
        borderSkipped: false,
        categoryPercentage: 1,
      },
      {
        label: 'Tài khoản không có GD',
        backgroundColor: '#F53126',
        data: newListData.map((t) => t.notTransaction),
        borderColor: '#F53126',
        borderWidth: 1,
        maxBarThickness: 40,
        borderSkipped: false,
        categoryPercentage: 1,
      },
    ];
  }

  /**
   * UpdateProportionRevenueFeeData
   * @param newList
   */
  updateProportionRevenueFeeData(newList?: IProportionRevenueData[]) {
    this.proportionRevenueFeeDataLabels = ProportionRevenueFeeData.map((t) => t.name);
    const listData = newList ?? ProportionRevenueFeeData;
    this.proportionRevenueFeeDatasets = [
      {
        label: 'Tỉ trọng doanh thu phí',
        data: listData.map((t) => t.data),
        backgroundColor: [
          'rgba(255, 214, 10, 1)',
          'rgba(226, 143, 255, 1)',
          'rgba(252, 165, 165, 1)',
          'rgba(147, 197, 253, 1)',
          'rgba(152, 152, 157, 1)',
        ],
        dataConfig: {
          name: ProportionRevenueFeeData.map((t) => t.name),
          broker: ['Lê Ngọc Hà', 'Nguyễn Hoàng Cảnh', 'Nguyễn Tuấn Dương', 'Ngô Thị Thúy Hạnh', 'Khác'],
        },
        hoverBackgroundColor: ['#FFD60A', '#E28FFF', '#FCA5A5', '#93C5FD', '#98989D'],
      },
    ];
  }

  /**
   * SelectTimeRankRevenueFee
   * @param type
   */
  selectTimeRankRevenueFee(type: ETypeTimeOption) {
    this.typeTimeRankRevenueFee = type;

    /**
     * Hàm fake data
     * xóa khi có api
     * @param name
     * @param min
     * @param max
     * @returns {any}data
     */
    const generateRandomRankRevenueData = (name: string, min: number, max: number): IRankingRevenueFeeData => ({
      name: name,
      transactionFee: this.getRandomNumberWithDigits(min, max),
      commissionBroker: this.getRandomNumberWithDigits(min, max),
    });
    this.subTextRankRevenueFee = this.updateLabelRangeTime(type);
    switch (type) {
      case ETypeTimeOption.DAY:
        {
          const newList = RankingRevenueFeeData.map((t) => generateRandomRankRevenueData(t.name, 4, 5));
          this.updateRankingRevenueFeeData(newList);
        }
        break;

      case ETypeTimeOption.WEEK:
        {
          const newList = RankingRevenueFeeData.map((t) => generateRandomRankRevenueData(t.name, 4, 6));
          this.updateRankingRevenueFeeData(newList);
        }
        break;

      case ETypeTimeOption.ONE_MONTH:
        {
          const newList = RankingRevenueFeeData.map((t) => generateRandomRankRevenueData(t.name, 5, 7));
          this.updateRankingRevenueFeeData(newList);
        }
        break;

      case ETypeTimeOption.YEAR:
      case ETypeTimeOption.HALF_OF_YEAR:
        {
          const newList = RankingRevenueFeeData.map((t) => generateRandomRankRevenueData(t.name, 7, 9));
          this.updateRankingRevenueFeeData(newList);
        }
        break;

      default:
        break;
    }
  }

  /**
   * SelectRankRevenueFee
   * @param type
   */
  selectRankRevenueFee(type: EBrokerAndRoomType) {
    RankingRevenueFeeData.forEach((t, i) => {
      if (type === EBrokerAndRoomType.BROKER) {
        t.name = this.nameListBroker[i];
      } else {
        t.name = this.nameListRoom[i];
      }
    });
    this.selectTimeRankRevenueFee(this.typeTimeRankRevenueFee);
  }

  /**
   * Hàm random data số từ 7 -> 9 số
   * Xóa khi có API
   * @param minDigits
   * @param maxDigits
   * @returns {number} data
   */
  getRandomNumberWithDigits(minDigits: number, maxDigits: number) {
    const numDigits = Math.floor(Math.random() * (maxDigits - minDigits + 1)) + minDigits;

    const min = Math.pow(10, numDigits - 1);
    const max = Math.pow(10, numDigits) - 1;

    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * SelectRankAccount
   * @param type
   */
  selectRankAccount(type: EBrokerAndRoomType) {
    RankingAccountData.forEach((t, i) => {
      if (type === EBrokerAndRoomType.BROKER) {
        t.name = this.nameListBroker[i];
      } else {
        t.name = this.nameListRoom[i];
      }
    });
    this.selectTimeRankAccount(this.typeTimeRankAccount);
  }

  /**
   * SelectTimeRankAccount
   * @param type
   */
  selectTimeRankAccount(type: ETypeTimeOption) {
    this.typeTimeRankAccount = type;

    /**
     * Hàm fake data
     * xóa khi có api
     * @param name
     * @param min
     * @param max
     * @returns {any}data
     */
    const generateRandomRankAccount = (name: string, min: number, max: number): IRankingAccountData => ({
      name: name,
      hasTransaction: this.getRandomNumberWithDigits(min, max),
      notTransaction: -this.getRandomNumberWithDigits(min, max),
    });
    this.subTextRankAccount = this.updateLabelRangeTime(type);
    switch (type) {
      case ETypeTimeOption.DAY:
      case ETypeTimeOption.WEEK:
        {
          const newList = RankingAccountData.map((t) => generateRandomRankAccount(t.name, 1, 2));
          this.updateRankingAccountData(newList);
        }
        break;

      case ETypeTimeOption.ONE_MONTH:
        {
          const newList = RankingAccountData.map((t) => generateRandomRankAccount(t.name, 1, 3));
          this.updateRankingAccountData(newList);
        }
        break;

      case ETypeTimeOption.HALF_OF_YEAR:
        {
          const newList = RankingAccountData.map((t) => generateRandomRankAccount(t.name, 2, 3));
          this.updateRankingAccountData(newList);
        }
        break;

      case ETypeTimeOption.YEAR:
        {
          const newList = RankingAccountData.map((t) => generateRandomRankAccount(t.name, 3, 3));
          this.updateRankingAccountData(newList);
        }
        break;

      default:
        break;
    }
  }

  /**
   * SelectTimeProportionRevenue
   * @param type
   */
  selectTimeProportionRevenue(type: ETypeTimeOption) {
    this.typeTimeProportionRevenue = type;

    /**
     * Hàm fake data
     * xóa khi có api
     * @param name
     * @param min
     * @param max
     * @returns {any}data
     */
    const generateRandomProportionRevenueData = (name: string, min: number, max: number): IProportionRevenueData => ({
      name: name,
      data: this.getRandomNumberWithDigits(min, max),
    });
    switch (type) {
      case ETypeTimeOption.DAY:
      case ETypeTimeOption.WEEK:
      case ETypeTimeOption.ONE_MONTH:
      case ETypeTimeOption.HALF_OF_YEAR:
      case ETypeTimeOption.YEAR:
        {
          const newList = ProportionRevenueFeeData.map((t) => generateRandomProportionRevenueData(t.name, 4, 5));
          this.updateProportionRevenueFeeData(newList);
        }
        break;

      default:
        break;
    }
  }

  /**
   * SelectProportionRevenue
   * @param type
   */
  selectProportionRevenue(type: EBrokerAndRoomType) {
    ProportionRevenueFeeData.forEach((t, i) => {
      if (type === EBrokerAndRoomType.BROKER) {
        t.name = List_of_broker_proportion_revenue[i];
      } else {
        t.name = List_of_room_proportion_revenue[i];
      }
    });
    this.selectTimeProportionRevenue(this.typeTimeProportionRevenue);
  }

  /**
   * UpdateLabelRangeTime
   * @param type
   * @returns {void} data
   */
  updateLabelRangeTime(type: ETypeTimeOption) {
    const currentDate = new Date();
    const otherDate = new Date();
    let labels = '';
    switch (type) {
      case ETypeTimeOption.DAY:
        labels = `1 ngày (${dateToDM(currentDate)})`;
        break;

      case ETypeTimeOption.WEEK:
        otherDate.setDate(otherDate.getDate() - 7);
        labels = `1 tuần (${dateToDM(otherDate)} - ${dateToDM(currentDate)} )`;
        break;

      case ETypeTimeOption.ONE_MONTH:{
        const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
        otherDate.setDate(otherDate.getDate() - daysInMonth);
        labels = `1 tháng (${dateToDM(otherDate)} - ${dateToDM(currentDate)} )`;
        break;}

      case ETypeTimeOption.HALF_OF_YEAR:
        otherDate.setMonth(otherDate.getMonth() - 6);
        labels = `6 tháng (${dateToDMY(otherDate)} - ${dateToDMY(currentDate)} )`;
        break;

      case ETypeTimeOption.YEAR:
        otherDate.setMonth(otherDate.getMonth() - 12);
        labels = `1 năm (${dateToDMY(otherDate)} - ${dateToDMY(currentDate)} )`;
        break;
      default:
        break;
    }

    return labels;
  }
}
