<div class="customer-group-create-wrapper">
  <!-- HEADER -->
  <div class="header-create">
    <div class="typo-body-16">{{ 'MES-253' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <!-- CONTENT -->
  <div class="body-create">
    <form [formGroup]="brokerForm" class="form-group">
      <!-- Phong moi gioi -->
      <div class="item brokerage-room">
        <div class="label-form typo-body-17">{{ 'MES-191' | translate }}</div>
        <a class="customer-input-wrapper" (click)="openPopoverBrokerRoom($event)">
          <input
            type="text"
            class="customer-input typo-body-12 fs-12"
            [placeholder]="'MES-191' | translate"
            formControlName="brokerRoom"
            readonly
          />
          <div class="img-wrapper"><img src="./assets/icons/arrow-down.svg" alt="" /></div>
        </a>
      </div>

      <!-- Moi gioi quan ly -->
      <div class="item management-broker">
        <div class="label-form typo-body-17">{{ 'MES-192' | translate }}</div>
        <a class="customer-input-wrapper" (click)="openPopoverManagementBroker($event)">
          <input
            type="text"
            class="customer-input typo-body-12 fs-12"
            [placeholder]="'MES-192' | translate"
            formControlName="managementBroker"
            readonly
          />
          <div class="img-wrapper"><img src="./assets/icons/arrow-down.svg" alt="" /></div>
        </a>
      </div>
    </form>

    <div class="customer-list">
      <div class="title-customer typo-body-12">{{ 'MES-96' | translate }}</div>

      <div class="search-box">
        <input
          type="text"
          class="input-search input-style-common typo-body-12 fs-12"
          [formControl]="groupNameControl"
          [placeholder]="'MES-14' | translate"
        />
        <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
      </div>

      <div class="option-list-cls">
        @for (item of listFilterOptions; let i = $index; track item) {
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="changeSections($event.checked, item)" [checked]="item.isSelect" class="checkbox-cls">
            {{ item.name | translate }} - {{ item.group | translate }}</mat-checkbox
          >
        </div>
        }
      </div>
    </div>
  </div>

  <!-- FOOTER -->
  <div class="footer-create">
    <button [disabled]="!isValid()" (click)="submit()" class="btn apply typo-button-3">
      {{ 'MES-89' | translate }}
    </button>
    <button mat-dialog-close class="btn default typo-button-3">{{ 'MES-74' | translate }}</button>
  </div>
</div>
