import { Component, Inject, OnInit, Optional } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { debounceTime, startWith, take, takeUntil, tap } from 'rxjs';
import { DestroyService, MessageService } from 'src/app/core/services';
import { LIST_OF_CUSTOMER } from 'src/app/features/assets/constant/assets';
import { IOptionList } from 'src/app/features/customers/components/customer-group-filter/customer-group-filter.component';
import { CustomDropdownPopupFilterComponent } from 'src/app/shared/components/custom-dropdown-popup-filter/custom-dropdown-popup-filter.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';

const LIST_MG_ROOM_OPTIONS = [
  {
    name: 'Phòng MG 05',
    id: 1,
  },
  {
    name: 'Phòng MG 08',
    id: 2,
  },
  {
    name: 'Phòng MG 07',
    id: 3,
  },
  {
    name: 'Phòng MG 06',
    id: 4,
  },
  {
    name: 'Phòng MG 09',
    id: 5,
  },
];

const LIST_MG_OPTIONS = [
  {
    id: 1,
    name: 'MG-015: Nguyễn Tuấn Dương',
  },
  {
    id: 2,
    name: 'MG-05: Cao Tuấn Nghĩa',
  },
  {
    id: 3,
    name: 'MG-05: Mai Tiến Đạt',
  },
  {
    id: 4,
    name: 'MG-020: Vũ Minh Chiến',
  },
  {
    id: 5,
    name: 'MG-016: Nguyễn Hoàng Cảnh',
  },
  {
    id: 6,
    name: 'MG-019: Đinh Sỹ Dũng',
  },
  {
    id: 7,
    name: 'MG-021: Lê Ngọc Hà',
  },
  {
    id: 8,
    name: 'MG-018: Phạm Văn Tây',
  },
];

/**
 * Gắn môi giới
 */
@Component({
  selector: 'app-create-broker-popup',
  templateUrl: './create-broker-popup.component.html',
  styleUrl: './create-broker-popup.component.scss',
})
export class CreateBrokerPopupComponent implements OnInit {
  listFilterOptions: any[] = [];

  brokerForm: FormGroup;
  // FormControl
  groupNameControl = new FormControl();
  private _groupNameOptions!: IOptionList[];

  /**
   * Constructor
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param messageService
   * @param dialogRef
   * @param fb FormBuilder
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly messageService: MessageService,
    public dialogRef: MatDialogRef<CreateBrokerPopupComponent>,
    private readonly fb: FormBuilder
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.brokerForm = this.fb.group({
      brokerRoom: [null, Validators.required],
      managementBroker: [null, Validators.required],
    });
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.groupNameControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterOptions = this._filter(value ?? '', this._groupNameOptions);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this._groupNameOptions = LIST_OF_CUSTOMER;

    this.listFilterOptions = LIST_OF_CUSTOMER.map((customer) => ({
      name: customer.name,
      group: customer.group,
      isSelect: false,
    }));
  }

  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param item Item thay đổi
   */
  changeSections(checked: boolean, item?: IOptionList) {
    if (!item) return;
    item.isSelect = checked;
  }

  /**
   *
   * @param event - event
   */
  openPopoverBrokerRoom(event: Event) {
    const originElement = event.target as HTMLElement;

    const popupRef = this.popoverService.open({
      origin: originElement,
      content: CustomDropdownPopupFilterComponent,
      position: 0,
      width: originElement.offsetWidth,
      height: 280,
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100'],
      data: LIST_MG_ROOM_OPTIONS,
      componentConfig: {
        searchKey: ['name'],
        displayFnc: (v: any) => v.name,
        listFilterOptions: LIST_MG_ROOM_OPTIONS,
        allOptions: false,
      },
    });

    popupRef.afterClosed$.pipe(take(1)).subscribe({
      next: (value) => {
        this.brokerForm.patchValue({
          brokerRoom: value.data?.[0]?.name ?? '',
        });
      },
    });
  }

  /**
   * OpenPopoverCustomerGroup
   * @param event -Event
   */
  openPopoverManagementBroker(event: Event) {
    const originElement = event.target as HTMLElement;

    const popupRef = this.popoverService.open({
      origin: originElement,
      content: CustomDropdownPopupFilterComponent,
      position: 0,
      width: originElement.offsetWidth,
      height: 280,
      data: LIST_MG_OPTIONS,
      componentConfig: {
        searchKey: ['name'],
        displayFnc: (v: any) => v.name,
        listFilterOptions: LIST_MG_OPTIONS,
        allOptions: false,
      },
    });

    popupRef.afterClosed$.pipe(take(1)).subscribe({
      next: (value) => {
        if (!value.data?.length) return;
        this.brokerForm.patchValue({
          brokerRoom: 'Phòng MG 05',
          managementBroker: value.data?.[0]?.name ?? '',
        });
      },
    });
  }

  /**
   * isValid
   */
  isValid() {
    const selectCustomer = this.listFilterOptions.filter((customer) => customer.isSelect);

    return this.brokerForm.valid && selectCustomer.length;
  }

  /**
   * Apply Filter
   */
  submit() {
    const selectedCustomers = this.listFilterOptions.filter((option) => option.isSelect);
    const selectedCustomerGroups = selectedCustomers.map((customer) => ({
      name: customer.name,
      group: customer.group,
    }));

    const submitData = {
      brokenRoom: this.brokerForm.get('brokerRoom')?.value,
      managementBroker: this.brokerForm.get('managementBroker')?.value,
      selectedCustomers: selectedCustomerGroups,
    };

    this.dialogRef.close(submitData);
    this.messageService.success('Gắn môi giới thành công');
  }

  /**
   * Inner filter function
   * @param {string} value search value
   * @param {IOptionList[]} options
   */
  private _filter(value: string, options: IOptionList[]): IOptionList[] {
    const filterValue = value.toLowerCase();

    return options.filter(
      (option) =>
        option.name?.toLowerCase().includes(filterValue) || option.accountNumber?.toLowerCase().includes(filterValue)
    );
  }
}
