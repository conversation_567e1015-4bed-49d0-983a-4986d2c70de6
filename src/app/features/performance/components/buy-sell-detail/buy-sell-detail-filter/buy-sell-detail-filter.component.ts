import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { IListOptions } from '../../../models/performance';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { LIST_OF_BROKER, LIST_OF_CUSTOMER } from '../../../constants/performance';

/**
 * BuySellDetailFilterComponent
 */
@Component({
  selector: 'app-buy-sell-detail-filter-component',
  templateUrl: './buy-sell-detail-filter.component.html',
  styleUrl: './buy-sell-detail-filter.component.scss',
})
export class BuySellDetailFilterComponent implements OnInit {
  searchCustomerControl = new FormControl();
  searchBrokerControl = new FormControl();

  isSelectAllCustomerBuy = true;

  isSelectAllBrokerBuy = true;

  listFilterCustomerOptionsBuy: IListOptions[] = [];
  listFilterCustomerStoreBuy: IListOptions[] = [];

  listFilterBrokerOptionsBuy: IListOptions[] = [];
  listFilterBrokerStorebuy: IListOptions[] = [];

  buySellFilterForm: FormGroup;

  /**
   * Constructor
   * @param fb FormBuilder
   * @param _destroy DestroyService
   */
  constructor(private readonly fb: FormBuilder, private readonly _destroy: DestroyService) {
    this.buySellFilterForm = this.fb.group({
      matchedTotalRevenueFrom: [null],
      matchedTotalRevenueTo: [null],
      valueBuyMatchingFrom: [null],
      valueBuyMatchingTo: [null],
      valueSellMatchingFrom: [null],
      valueSellMatchingTo: [null],
      notMatchTotalValueFrom: [null],
      notMatchTotalValueTo: [null],
      notMatchValueBuyFrom: [null],
      notMatchValueBuyTo: [null],
      notMatchValueSellFrom: [null],
      notMatchValueSellTo: [null],
    });

    this.updateListBuy(null, 'customer');
    this.updateListBuy(null, 'broker');
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.listFilterCustomerStoreBuy = this.listFilterCustomerOptionsBuy;
    this.listFilterBrokerStorebuy = this.listFilterBrokerOptionsBuy;

    this.searchCustomerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterCustomerOptionsBuy = this._filterBuy(value ?? '', this.listFilterCustomerStoreBuy);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.searchBrokerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterBrokerOptionsBuy = this._filterBuy(value ?? '', this.listFilterBrokerStorebuy);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * updateListBuy
   * @param data
   * @param type
   */
  updateListBuy(data: (string | number)[] | null, type: string) {
    const isSelectBuy = (v: string | number) => data === null || data?.includes(v);
    switch (type) {
      case 'customer':
        this.listFilterCustomerOptionsBuy = LIST_OF_CUSTOMER.map((customer) => ({
          name: customer.name,
          accountNumber: customer.accountNumber,
          isSelect: isSelectBuy(customer.name),
        }));
        this.isSelectAllCustomerBuy = this.listFilterCustomerOptionsBuy.every((t) => t.isSelect === true);
        break;

      case 'broker':
        this.listFilterBrokerOptionsBuy = LIST_OF_BROKER.map((customer) => ({
          name: customer.name,
          isSelect: isSelectBuy(customer.name),
        }));
        this.isSelectAllBrokerBuy = this.listFilterBrokerOptionsBuy.every((t) => t.isSelect === true);
        break;
    }
  }

  /**
   *
   * @param {string} value
   * @param {IListOptions} options
   */
  private _filterBuy(value: string, options: IListOptions[]): IListOptions[] {
    const filterValueBuy = value.toString().toLowerCase();

    return options.filter(
      (option) =>
        option?.name?.toLowerCase()?.includes(filterValueBuy) ||
        option?.accountNumber?.toLowerCase()?.includes(filterValueBuy)
    );
  }
}
