<div class="buy-sell-detail-filter-cls">
  <app-layout-filter-component>
    <form [formGroup]="buySellFilterForm">
      <div class="buy-sell-detail-body">
        <div class="box-container">
          <div class="box-content">
            <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
            <div class="searchbox-wrap">
              <div class="search-box">
                <input
                  type="text"
                  class="input-cls-custom input-style-common typo-body-11 fs-12"
                  [placeholder]="'MES-295' | translate"
                  [formControl]="searchCustomerControl"
                />
                <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
              </div>
              <div class="option-list-cls">
                <!-- (change)="changeSections($event.checked, 'all', 'customer')" -->
                <mat-checkbox [checked]="isSelectAllCustomerBuy" class="checkbox-cls">
                  {{ 'MES-58' | translate }}</mat-checkbox
                >
                @for (item of listFilterCustomerOptionsBuy; let i = $index; track item) {
                <div class="checkbox-cls-item typo-body-15">
                  <!-- (change)="changeSections($event.checked, 'item', 'customer', item)" -->
                  <mat-checkbox [checked]="item.isSelect" class="checkbox-cls">
                    {{ item.name }} - {{ item.accountNumber }}</mat-checkbox
                  >
                </div>
                }
              </div>
            </div>
          </div>
          <!-- Tổng GTGD đã khớp -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-512' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="matchedTotalRevenueFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="matchedTotalRevenueTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!-- GTGD (mua đã khớp) -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-513' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="valueBuyMatchingFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="valueBuyMatchingTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!-- GTGD (bán đã khớp) -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-513' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="valueSellMatchingFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="valueSellMatchingTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="box-container">
          <!-- Mô giới -->
          <div class="box-content">
            <div class="title-left typo-body-15">{{ 'MES-518' | translate }}</div>
            <div class="searchbox-wrap">
              <div class="search-box">
                <input
                  type="text"
                  class="input-cls-custom input-style-common typo-body-11 fs-12"
                  [placeholder]="'MES-14' | translate"
                  [formControl]="searchBrokerControl"
                />
                <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
              </div>
              <div class="option-list-cls">
                <!-- (change)="changeSections($event.checked, 'all', 'customer')" -->
                <mat-checkbox [checked]="isSelectAllBrokerBuy" class="checkbox-cls">
                  {{ 'MES-58' | translate }}</mat-checkbox
                >
                @for (item of listFilterBrokerOptionsBuy; let i = $index; track item) {
                <div class="checkbox-cls-item typo-body-15">
                  <!-- (change)="changeSections($event.checked, 'item', 'customer', item)" -->
                  <mat-checkbox [checked]="item.isSelect" class="checkbox-cls"> {{ item.name }}</mat-checkbox>
                </div>
                }
              </div>
            </div>
          </div>
          <!-- Tổng GTGD chưa khớp -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-515' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="notMatchTotalValueFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="notMatchTotalValueTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!-- GTGD (mua chưa khớp) -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-516' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="notMatchValueBuyFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="notMatchValueBuyTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!-- GTGD (bán chưa khớp) -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-517' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="notMatchValueSellFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="notMatchValueSellTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </app-layout-filter-component>
</div>
