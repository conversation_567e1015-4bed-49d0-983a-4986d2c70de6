<div class="buy-sell-detail-component">
  <div class="header-buy-sell-component">
    <div class="left-box">
      <div class="typo-heading-9 box-header-cls">
        <div
          class="text-show"
          *ngFor="let item of listBreadcrumbBuy; let i = index"
          (click)="navigateFeeCommissionListBuy(item, i)"
        >
          <span class="typo-heading-9">{{ item | translate }}</span>
          <img src="./assets/icons/arrow-right.svg" alt="arrow-right" />
        </div>
      </div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{ tags[0] }}
        </div>
        <div class="dropdown-btn" *ngIf="!showCollapse">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div>
      </div>
      <div class="collapse-btn" *ngIf="showCollapse">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div>
    </div>
    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls': isFilter
        }"
      >
      </app-action-btn>
    </div>
  </div>

  <!-- <div [ngClass]="{'hidden': !showCollapse}">
    <app-slide-tag [tags]="tags" [classParent]="'buy-sell-container'"></app-slide-tag>
  </div> -->

  <div class="table-view-container">
    <sha-grid
      #grid
      class="table-custom-cls"
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
    >
    </sha-grid>
  </div>
</div>

<!-- Lệnh mua đã khớp, Lệnh mua chưa khớp -->
<ng-template #buyEntries let-buyEntries="templateInfo" let-element="element">
  @if(buyEntries){
  <div>
    <ng-container *ngIf="buyEntries > 0">
      <div class="price-increase typo-body-12">
        <span>{{ buyEntries }} lệnh</span>
      </div>
    </ng-container>
    <ng-container *ngIf="buyEntries === 0">
      <div class="price-stable typo-body-12">
        <span>{{ buyEntries }} lệnh</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>

  }
</ng-template>

<!-- Lệnh bán đã khớp, lệnh bán chưa khớp -->

<ng-template #sellEntries let-sellEntries="templateInfo" let-element="element">
  @if(sellEntries){
  <div>
    <ng-container *ngIf="sellEntries > 0">
      <div class="price-reduce typo-body-12">
        <span>{{ sellEntries }} lệnh</span>
      </div>
    </ng-container>
    <ng-container *ngIf="sellEntries === 0">
      <div class="price-stable typo-body-12">
        <span>{{ sellEntries }} lệnh</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>
  }
</ng-template>

<!-- GTGD(mua đã khớp) -->
<ng-template #matchBuyGmv let-matchBuyGmv="templateInfo" let-element="element">
  @if(matchSellGmv){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="matchBuyGmv > 0">
      <div class="price-increase typo-body-12">
        <span>{{ customNumberFormat(matchBuyGmv) }}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="matchBuyGmv === 0">
      <div class="price-stable typo-body-12">
        <span>{{ matchSellGmv }}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>

<!-- GTGD(bán) -->

<ng-template #matchSellGmv let-matchSellGmv="templateInfo" let-element="element">
  @if(matchSellGmv ){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="matchSellGmv > 0">
      <div class="price-reduce typo-body-12">
        <span>{{ customNumberFormat(matchSellGmv) }}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="matchSellGmv === 0">
      <div class="price-stable typo-body-12">
        <span>{{ matchSellGmv }}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>
