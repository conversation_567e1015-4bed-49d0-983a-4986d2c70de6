import { Component, Inject, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { fakeData } from './fakedata';
import { BuySellDetailFilterComponent } from './buy-sell-detail-filter/buy-sell-detail-filter.component';
import { Store } from '@ngrx/store';
import { setItemSelected } from '../../stores/performance.actions';

/**
 * BuySellDetailComponent
 */
@Component({
  selector: 'app-buy-sell-detail-component',
  templateUrl: './buy-sell-detail.component.html',
  styleUrl: './buy-sell-detail.component.scss',
})
export class BuySellDetailComponent extends BaseTableComponent<any> implements OnInit {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('buyEntries', { static: true }) buyEntries: TemplateRef<any> | null = null;
  @ViewChild('sellEntries', { static: true }) sellEntries: TemplateRef<any> | null = null;
  @ViewChild('matchBuyGmv', { static: true }) matchBuyGmv: TemplateRef<any> | null = null;
  @ViewChild('matchSellGmv', { static: true }) matchSellGmv: TemplateRef<any> | null = null;

  customNumberFormat = customNumberFormat;

  tags = [`Lệnh khớp: ${customNumberFormat(1200)}`];
  showCollapse: boolean = true;
  fakeData: any[] = fakeData;
  listBreadcrumbBuy: string[] = [];

  itemSelectedBuy = {
    id: '',
    name: '',
    date: '',
  };
  /**
   *Constructor
   * @param popoverRef : PopoverRef
   * @param router Router
   * @param store Store
   */
  constructor(
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly router: Router,
    private readonly store: Store
  ) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);
    this.data = this.fakeData;
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.listBreadcrumbBuy = ['MES-256'];

    const paramsBuy = this.route.snapshot.queryParams;
    this.itemSelectedBuy = {
      id: paramsBuy['id'],
      name: paramsBuy['name'],
      date: paramsBuy['date'],
    };
    this.listBreadcrumbBuy = [...this.listBreadcrumbBuy, paramsBuy['date'], paramsBuy['name']];
  }
  /**
   * The Oninit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        width: 200,
        name: 'Môi giới',
        minWidth: 30,
        tag: 'broker',
        isDisplay: true,
        resizable: true,
      },
      {
        width: 120,
        name: 'Số tài khoản',
        minWidth: 30,
        tag: 'accountNumber',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        width: 160,
        name: 'Tên khách hàng',
        minWidth: 30,
        tag: 'name',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        width: 140,
        name: 'TSL lệnh đã khớp',
        minWidth: 30,
        tag: 'numberMatchEntries',
        displayValueFn: (v: number) => `${v} lệnh`,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        width: 145,
        name: 'Lệnh MUA đã khớp',
        minWidth: 30,
        tag: 'matchBuyEntries',
        cellTemplate: this.buyEntries,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        width: 145,
        name: 'Lệnh Bán đã khớp',
        minWidth: 30,
        tag: 'matchSellEntries',
        cellTemplate: this.sellEntries,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Tổng GTGD đã khớp',
        minWidth: 30,
        width: 140,
        tag: 'matchGmv',
        displayValueFn: (v) => customNumberFormat(v),
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'GTGD(mua đã khớp)',
        minWidth: 30,
        width: 140,
        tag: 'matchBuyGmv',
        cellTemplate: this.matchBuyGmv,
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'GTGD(bán đã khớp)',
        minWidth: 30,
        width: 140,
        tag: 'matchSellGmv',
        cellTemplate: this.matchSellGmv,

        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'TSL lệnh chưa khớp',
        minWidth: 30,
        width: 160,
        tag: 'numberNotMatchEntries',
        displayValueFn: (v: number) => `${v} lệnh`,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Lệnh MUA chưa khớp',
        minWidth: 30,
        width: 156,
        tag: 'numberNotMatchBuyEntries',
        cellTemplate: this.buyEntries,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Lệnh BÁN chưa khớp',
        minWidth: 30,
        width: 156,
        tag: 'numberNotMatchSellEntries',
        cellTemplate: this.sellEntries,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Tổng GTGD chưa khớp',
        minWidth: 30,
        width: 150,
        tag: 'notMatchGmv',
        displayValueFn: (v: number) => customNumberFormat(v),
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'GTGD(mua chưa khớp)',
        minWidth: 30,
        width: 150,
        tag: 'notMatchBuyGmv',
        cellTemplate: this.matchBuyGmv,
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'GTGD(bán chưa khớp)',
        minWidth: 30,
        width: 150,
        tag: 'notMatchSellGmv',
        cellTemplate: this.matchSellGmv,
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
    ];
  }

  /**
   * ClickButton
   * @param tag
   */
  override clickButton(tag: string): void {
    if (tag === 'filter') {
      this.openFilter(BuySellDetailFilterComponent, {
        width: '800px',
      });
    }
  }

  /**
   * NavigateFeeCommissionList
   * @param item
   * @param index
   * @returns {void} prevent event
   */
  navigateFeeCommissionListBuy(item: string, indexBuy: number) {
    if (indexBuy === this.listBreadcrumbBuy.length - 1) return;
    if (indexBuy !== 0) {
      this.store.dispatch(setItemSelected({ ...this.itemSelectedBuy }));
    } else if (indexBuy === 0) this.store.dispatch(setItemSelected({ id: '', name: '', date: '' }));

    this.router.navigate(['/job-performance/buy-sell']);
  }
}
