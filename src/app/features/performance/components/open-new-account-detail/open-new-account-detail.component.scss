.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.open-new-account-detail-component {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-open-new-account-component {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;
      .box-header-cls {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .text-show {
        display: flex;
        align-items: center;
        gap: 4px;
        & > span {
          cursor: pointer;
          color: var(--color--text--subdued);
          &:hover {
            color: var(--color--text--default);
          }
        }
        &:last-child {
          & > span {
            cursor: default;
            color: var(--color--text--default);
          }
          img[alt='arrow-right'] {
            display: none;
          }
        }
      }

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
      }
    }
    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;
      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        .type-account {
          .box-show {
            padding: 4px 0px;
            border-radius: 16px;
            justify-content: center;
            background-color: #32d74b;
            display: inline-flex;
            padding: 0px 12px;
            white-space: nowrap;
            text-wrap: nowrap;

            max-width: 120px;
            margin: 0 auto;

            .input-table-view {
              text-align: center;
            }

            .isEditMode {
              border-color: transparent !important;
              text-align: unset;
            }
          }

          &.individual-cls {
            .box-show {
              background-color: #ffd60a;
            }
          }
        }

        .status-account {
          .box-show {
            padding: 4px 0px;
            border-radius: 16px;
            justify-content: center;
            background-color: var(--color--neutral--100);
            display: inline-flex;
            padding: 0px 12px;
            white-space: nowrap;
            text-wrap: nowrap;

            max-width: 120px;
            margin: 0 auto;

            .input-table-view {
              text-align: center;
            }

            .isEditMode {
              border-color: transparent !important;
              text-align: unset;
            }
          }

          &.activate-cls {
            .box-show {
              background-color: var(--color--accents--green-dark);
            }
          }
        }
      }
    }
  }
}
