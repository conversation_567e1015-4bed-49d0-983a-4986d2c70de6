<div class="open-new-account-detail-filter-cls">
  <app-layout-filter-component>
    <div class="open-new-account-detail-body">
      <div class="box-container">
        <!-- Loại tài kho<PERSON>n -->
        <div class="box-content">
          <div class="typo-body-15">{{ 'MES-25' | translate }}</div>
          <div class="check-box-list">
            <div class="checkbox-cls-item">
              <mat-checkbox [checked]="true" class="checkbox-cls">{{ 'MES-26' | translate }}</mat-checkbox>
            </div>
            <div class="checkbox-cls-item">
              <mat-checkbox [checked]="false" class="checkbox-cls">{{ 'MES-27' | translate }}</mat-checkbox>
            </div>
          </div>
        </div>
        <!-- Trạng thái -->
        <div class="box-content">
          <div class="typo-body-15">{{ 'MES-221' | translate }}</div>
          <div class="check-box-list">
            <div class="checkbox-cls-item">
              <mat-checkbox [checked]="true" class="checkbox-cls">{{ 'MES-520' | translate }}</mat-checkbox>
            </div>
            <div class="checkbox-cls-item">
              <mat-checkbox [checked]="false" class="checkbox-cls">{{ 'MES-521' | translate }}</mat-checkbox>
            </div>
          </div>
        </div>
        <div class="box-content">
          <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
          <div class="searchbox-wrap">
            <div class="search-box">
              <input
                type="text"
                class="input-cls-custom input-style-common typo-body-11 fs-12"
                [placeholder]="'MES-295' | translate"
                [formControl]="searchCustomerControl"
              />
              <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
            </div>
            <div class="option-list-cls">
              <!-- (change)="changeSections($event.checked, 'all', 'customer')" -->
              <mat-checkbox [checked]="isSelectAllCustomerOpen" class="checkbox-cls">
                {{ 'MES-58' | translate }}</mat-checkbox
              >
              @for (item of listFilterCustomerOptionsOpen; let i = $index; track item) {
              <div class="checkbox-cls-item typo-body-15">
                <!-- (change)="changeSections($event.checked, 'item', 'customer', item)" -->
                <mat-checkbox [checked]="item.isSelect" class="checkbox-cls">
                  {{ item.name }} - {{ item.accountNumber }}</mat-checkbox
                >
              </div>
              }
            </div>
          </div>
        </div>
      </div>
      <div class="box-container">
        <!-- Mô giới -->
        <div class="box-content">
          <div class="title-left typo-body-15">{{ 'MES-518' | translate }}</div>
          <div class="searchbox-wrap">
            <div class="search-box">
              <input
                type="text"
                class="input-cls-custom input-style-common typo-body-11 fs-12"
                [placeholder]="'MES-14' | translate"
                [formControl]="searchBrokerControl"
              />
              <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
            </div>
            <div class="option-list-cls">
              <!-- (change)="changeSections($event.checked, 'all', 'customer')" -->
              <mat-checkbox [checked]="isSelectAllBrokerOpen" class="checkbox-cls">
                {{ 'MES-58' | translate }}</mat-checkbox
              >
              @for (item of listFilterBrokerOptionsOpen; let i = $index; track item) {
              <div class="checkbox-cls-item typo-body-15">
                <!-- (change)="changeSections($event.checked, 'item', 'customer', item)" -->
                <mat-checkbox [checked]="item.isSelect" class="checkbox-cls"> {{ item.name }}</mat-checkbox>
              </div>
              }
            </div>
          </div>
        </div>
        <!-- HĐ Cơ sở -->
        <div class="box-content">
          <div class="typo-body-15">{{ 'MES-522' | translate }}</div>
          <div class="check-box-list">
            <div class="checkbox-cls-item">
              <mat-checkbox [checked]="true" class="checkbox-cls">{{ 'MES-46' | translate }}</mat-checkbox>
            </div>
            <div class="checkbox-cls-item">
              <mat-checkbox [checked]="false" class="checkbox-cls">{{ 'MES-47' | translate }}</mat-checkbox>
            </div>
          </div>
        </div>
        <!-- HĐ Ký Quỹ -->
        <div class="box-content">
          <div class="typo-body-15">{{ 'MES-523' | translate }}</div>
          <div class="check-box-list">
            <div class="checkbox-cls-item">
              <mat-checkbox [checked]="true" class="checkbox-cls">{{ 'MES-46' | translate }}</mat-checkbox>
            </div>
            <div class="checkbox-cls-item">
              <mat-checkbox [checked]="false" class="checkbox-cls">{{ 'MES-47' | translate }}</mat-checkbox>
            </div>
          </div>
        </div>
        <!-- HĐ Phái sinh -->
        <div class="box-content">
          <div class="typo-body-15">{{ 'MES-524' | translate }}</div>
          <div class="check-box-list">
            <div class="checkbox-cls-item">
              <mat-checkbox [checked]="true" class="checkbox-cls">{{ 'MES-46' | translate }}</mat-checkbox>
            </div>
            <div class="checkbox-cls-item">
              <mat-checkbox [checked]="false" class="checkbox-cls">{{ 'MES-47' | translate }}</mat-checkbox>
            </div>
          </div>
        </div>
      </div>
    </div>
  </app-layout-filter-component>
</div>
