import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { IListOptions } from '../../../models/performance';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { LIST_OF_BROKER, LIST_OF_CUSTOMER } from '../../../constants/performance';

/**
 * OpenNewAccountDetailFilterComponent
 */
@Component({
  selector: 'app-open-new-account-detail-filter-component',
  templateUrl: './open-new-account-detail-filter.component.html',
  styleUrl: './open-new-account-detail-filter.component.scss',
})
export class OpenNewAccountDetailFilterComponent implements OnInit {
  searchCustomerControl = new FormControl();
  searchBrokerControl = new FormControl();

  isSelectAllCustomerOpen = true;

  isSelectAllBrokerOpen = true;

  listFilterCustomerOptionsOpen: IListOptions[] = [];
  listFilterCustomerStoreOpen: IListOptions[] = [];

  listFilterBrokerOptionsOpen: IListOptions[] = [];
  listFilterBrokerStoreOpen: IListOptions[] = [];

  /**
   * Constructor
   * @param _destroy DestroyService
   */
  constructor(private readonly _destroy: DestroyService) {
    this.updateListOpen(null, 'customer');
    this.updateListOpen(null, 'broker');
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.listFilterCustomerStoreOpen = this.listFilterCustomerOptionsOpen;
    this.listFilterBrokerStoreOpen = this.listFilterBrokerOptionsOpen;

    this.searchCustomerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterCustomerOptionsOpen = this._filterOpen(value ?? '', this.listFilterCustomerStoreOpen);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.searchBrokerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterBrokerOptionsOpen = this._filterOpen(value ?? '', this.listFilterBrokerStoreOpen);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * updateList
   * @param data
   * @param type
   */
  updateListOpen(data: (string | number)[] | null, type: string) {
    const isSelectOpen = (v: string | number) => data === null || data?.includes(v);
    switch (type) {
      case 'customer':
        this.listFilterCustomerOptionsOpen = LIST_OF_CUSTOMER.map((customer) => ({
          name: customer.name,
          accountNumber: customer.accountNumber,
          isSelect: isSelectOpen(customer.name),
        }));
        this.isSelectAllCustomerOpen = this.listFilterCustomerOptionsOpen.every((t) => t.isSelect === true);
        break;

      case 'broker':
        this.listFilterBrokerOptionsOpen = LIST_OF_BROKER.map((customer) => ({
          name: customer.name,
          isSelect: isSelectOpen(customer.name),
        }));
        this.isSelectAllBrokerOpen = this.listFilterBrokerOptionsOpen.every((t) => t.isSelect === true);
        break;
    }
  }

  /**
   *
   * @param {string} value
   * @param {IListOptions} options
   */
  private _filterOpen(value: string, options: IListOptions[]): IListOptions[] {
    const filterValueOpen = value.toString().toLowerCase();

    return options.filter(
      (option) =>
        option?.name?.toLowerCase()?.includes(filterValueOpen) ||
        option?.accountNumber?.toLowerCase()?.includes(filterValueOpen)
    );
  }
}
