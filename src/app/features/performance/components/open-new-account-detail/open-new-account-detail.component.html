<div class="open-new-account-detail-component">
  <div class="header-open-new-account-component">
    <div class="left-box">
      <div class="typo-heading-9 box-header-cls">
        <div
          *ngFor="let item of listBreadcrumb; let i = index"
          class="text-show"
          (click)="navigateFeeCommissionList(item, i)"
        >
          <span class="typo-heading-9">{{ item | translate }}</span>
          <img src="./assets/icons/arrow-right.svg" alt="arrow-right" />
        </div>
      </div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{ tags[0] }}
        </div>
        <!-- (click)="toggleButtons()" -->
        <div class="dropdown-btn" *ngIf="!showCollapse">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div>
      </div>
      <div class="collapse-btn" *ngIf="showCollapse">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls': isFilter
        }"
      >
      </app-action-btn>
    </div>
  </div>

  <!-- <div [ngClass]="{ hidden: !showCollapse }">
    <app-slide-tag [tags]="tags" [classParent]="'fees-commission-detail-component'"></app-slide-tag>
  </div> -->

  <div class="table-view-container">
    <sha-grid
      #grid
      class="table-custom-cls"
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
    >
    </sha-grid>
  </div>
</div>
