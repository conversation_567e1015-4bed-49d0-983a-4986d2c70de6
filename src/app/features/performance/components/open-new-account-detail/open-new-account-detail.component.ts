import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { IFilterFeesCommission } from '../../models/performance';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { Store } from '@ngrx/store';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { Router } from '@angular/router';
import { fakeData } from './fakeData';
import { CELL_TYPE } from '@shared/models';
import { CONVERT_STATUS_ACCOUNT_TO_LABLE, CONVERT_TYPE_ACCOUNT_TO_LABLE } from '../../constants/performance';
import { OpenNewAccountDetailFilterComponent } from './open-new-account-detail-filter/open-new-account-detail-filter.component';
import { setItemSelected } from '../../stores/performance.actions';

/**
 * OpenNewAccountDetailComponent
 */
@Component({
  selector: 'app-open-new-account-detail-component',
  templateUrl: './open-new-account-detail.component.html',
  styleUrl: './open-new-account-detail.component.scss',
})
export class OpenNewAccountDetailComponent extends BaseTableComponent<any> implements OnInit {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  customNumberFormat = customNumberFormat;

  filterOptions!: IFilterFeesCommission;
  tags = [`Số TK : ${customNumberFormat(100)}`];

  fakeData: any[] = fakeData;

  showCollapse: boolean = false;

  listBreadcrumb: string[] = [];

  itemSelected = {
    id: '',
    name: '',
    date: '',
  };
  /**
   *Constructor
   * @param dialogService DialogService
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param cdf ChangeDetectorRef
   * @param route ActivatedRoute
   * @param router Router
   * @param store Store
   */
  constructor(
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly router: Router,
    private readonly store: Store
  ) {
    super();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.export, ActionButton.display, ActionButton.filter]);

    this.data = this.fakeData;
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.listBreadcrumb = ['MES-256'];

    const params = this.route.snapshot.queryParams;
    this.itemSelected = {
      id: params['id'],
      name: params['name'],
      date: params['date'],
    };

    this.listBreadcrumb = [...this.listBreadcrumb, params['date'], params['name']];
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        name: 'Tên khách hàng',
        width: 160,
        minWidth: 30,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
        pinned: 'left',
        dragDisabled: true,
        disable: true,
      },
      {
        name: 'Số CMND/CCCD/HC',
        minWidth: 30,
        width: 170,
        tag: 'identificationId',
        isDisplay: true,
        resizable: true,
        url: './assets/icons/file-extention-blue.svg',
        displayValueFn: (v) => {
          if (!v) return '-';
          return v.name;
        },
        typeValue: CELL_TYPE.FILE,
      },
      {
        name: 'Loại tài khoản',
        minWidth: 30,
        width: 140,
        tag: 'accountType',
        isDisplay: true,
        resizable: true,
        displayValueFn: (value) => CONVERT_TYPE_ACCOUNT_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === 0) {
            return 'type-account individual-cls';
          } else return 'type-account';
        },
        align: 'center',
      },
      {
        name: 'Trạng thái',
        minWidth: 30,
        width: 140,
        tag: 'status',
        isDisplay: true,
        resizable: true,
        displayValueFn: (value) => CONVERT_STATUS_ACCOUNT_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === 0) {
            return 'status-account activate-cls';
          } else return 'status-account';
        },
        align: 'center',
      },
      {
        name: 'Số tài khoản',
        minWidth: 30,
        width: 140,
        tag: 'accountNumber',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Phòng MG quản lý',
        minWidth: 30,
        width: 140,
        tag: 'brokerRoom',
        isDisplay: true,
        tagRelated: ['brokerId'],
        displayValueFn: (v) => {
          if (!v) return '-';
          return v.name;
        },
        typeValue: CELL_TYPE.DROPDOWN_MULTIPLE_LEVEL,
        isEdit: true,
        componentConfig: {
          displayOptionFn: (v: any) => v.name,
          searchKey: 'name',
          key: 'children',
          isUpdateList: true,
          isShowBtn: true,
          isShowSearch: true,
          // options: structuredClone(this.LIST_MG_ROOM_OPTIONS),
          // initialOptions: structuredClone(this.LIST_MG_ROOM_OPTIONS),
          configChildren: {
            level1: {
              isShowSearch: false,
              isShowBtn: false,
              searchKey: 'name',
              placeholder: 'Tên Phòng',
              displayOptionFn: (v: any) => v.name,
            },
          },
          filterKey: 'parentId',
        },
        resizable: true,
      },
      {
        name: 'Mã MG quản lý',
        minWidth: 30,
        width: 210,
        tag: 'brokerId',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Ngày mở tài khoản',
        minWidth: 30,
        width: 160,
        tag: 'dateOpenAccount',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
      },
      {
        name: 'Ngày cuối giao dịch',
        minWidth: 30,
        width: 160,
        tag: 'dateEnd',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
      },
      {
        name: 'HĐ cơ sở',
        minWidth: 30,
        width: 170,
        tag: 'contractBase',
        isDisplay: true,
        resizable: true,
        url: './assets/icons/file-extention-blue.svg',
        displayValueFn: (v) => {
          if (!v) return '-';
          return v.name;
        },
        typeValue: CELL_TYPE.FILE,
      },
      {
        name: 'HĐ ký quỹ',
        minWidth: 30,
        width: 170,
        tag: 'contractDeposit',
        isDisplay: true,
        resizable: true,
        url: './assets/icons/file-extention-blue.svg',
        displayValueFn: (v) => {
          if (!v) return '-';
          return v.name;
        },
        typeValue: CELL_TYPE.FILE,
      },
      {
        name: 'HĐ phái sinh',
        minWidth: 30,
        width: 170,
        tag: 'contractDerivative',
        isDisplay: true,
        resizable: true,
        url: './assets/icons/file-extention-blue.svg',
        displayValueFn: (v) => {
          if (!v) return '-';
          return v.name;
        },
        typeValue: CELL_TYPE.FILE,
      },
      {
        name: 'Chữ ký',
        minWidth: 30,
        width: 200,
        tag: 'signature',
        isDisplay: true,
        resizable: true,
        url: './assets/icons/file-extention-blue.svg',
        displayValueFn: (v) => {
          if (!v) return '-';
          return v.name;
        },
        typeValue: CELL_TYPE.FILE,
      },
    ];
  }

  /**
   *Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    if (tag === 'filter') {
      this.openFilter(OpenNewAccountDetailFilterComponent, {
        width: '800px',
        // data: this.filterOptions,
      });
    }
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * NavigateFeeCommissionList
   * @param item
   * @param index
   * @returns {void} prevent event
   */
  navigateFeeCommissionList(item: string, index: number) {
    if (index === this.listBreadcrumb.length - 1) return;
    if (index !== 0) {
      this.store.dispatch(setItemSelected({ ...this.itemSelected }));
    } else if (index === 0) this.store.dispatch(setItemSelected({ id: '', name: '', date: '' }));

    this.router.navigate(['/job-performance/open-new-account']);
  }
}
