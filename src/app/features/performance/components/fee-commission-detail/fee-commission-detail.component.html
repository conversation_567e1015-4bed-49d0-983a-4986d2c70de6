<div class="fees-commission-detail-component">
  <div class="header-fees-commission-component">
    <div class="left-box">
      <div class="typo-heading-9 box-header-cls">
        <div
          *ngFor="let item of listBreadcrumbFee; let i = index"
          class="text-show"
          (click)="navigateFeeCommissionListFee(item, i)"
        >
          <span class="typo-heading-9">{{ item | translate }}</span>
          <img src="./assets/icons/arrow-right.svg" alt="arrow-right" />
        </div>
      </div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{ tags[0] }}
        </div>
        <!-- (click)="toggleButtons()" -->
        <div class="dropdown-btn" *ngIf="!showCollapse">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div>
      </div>
      <div class="collapse-btn" *ngIf="showCollapse">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls': isFilter
        }"
      >
      </app-action-btn>
    </div>
  </div>

  <!-- <div [ngClass]="{ hidden: !showCollapse }">
    <app-slide-tag [tags]="tags" [classParent]="'fees-commission-detail-component'"></app-slide-tag>
  </div> -->

  <div class="table-view-container">
    <sha-grid
      #grid
      class="table-custom-cls"
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
    >
    </sha-grid>
  </div>
</div>

<!-- GTGD(mua) -->

<ng-template #sellGmv let-sellGmv="templateInfo" let-element="element">
  @if(sellGmv){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="sellGmv > 0">
      <div class="price-increase typo-body-12">
        <span>{{ customNumberFormat(sellGmv) }}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="sellGmv === 0">
      <div class="price-stable typo-body-12">
        <span>{{ sellGmv }}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>

<!-- GTGD(bán) -->

<ng-template #buyGmv let-buyGmv="templateInfo" let-element="element">
  @if(buyGmv){
  <div class="box-flex-1-cls">
    <ng-container *ngIf="buyGmv > 0">
      <div class="price-reduce typo-body-12">
        <span>{{ customNumberFormat(buyGmv) }}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="buyGmv === 0">
      <div class="price-stable typo-body-12">
        <span>{{ buyGmv }}</span>
      </div>
    </ng-container>
  </div>
  }@else {
  <div class="box-flex-1-cls">-</div>
  }
</ng-template>
