<div class="fee-commission-detail-filter-cls">
  <app-layout-filter-component>
    <form [formGroup]="feeCommissionFilterForm">
      <div class="fee-commission-detail-body">
        <div class="box-container">
          <div class="box-content">
            <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
            <div class="searchbox-wrap">
              <div class="search-box">
                <input
                  type="text"
                  class="input-cls-custom input-style-common typo-body-11 fs-12"
                  [placeholder]="'MES-295' | translate"
                  [formControl]="searchCustomerControl"
                />
                <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
              </div>
              <div class="option-list-cls">
                <!-- (change)="changeSections($event.checked, 'all', 'customer')" -->
                <mat-checkbox [checked]="isSelectAllCustomerFee" class="checkbox-cls">
                  {{ 'MES-58' | translate }}</mat-checkbox
                >
                @for (item of listFilterCustomerOptionsFee; let i = $index; track item) {
                <div class="checkbox-cls-item typo-body-15">
                  <!-- (change)="changeSections($event.checked, 'item', 'customer', item)" -->
                  <mat-checkbox [checked]="item.isSelect" class="checkbox-cls">
                    {{ item.name }} - {{ item.accountNumber }}</mat-checkbox
                  >
                </div>
                }
              </div>
            </div>
          </div>
          <!-- Tổng GTGD -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-441' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="totalValueFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="totalValueTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!-- GTGD (mua) -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-442' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="valueBuyFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="valueBuyTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!-- GTGD (bán) -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-443' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="valueSellFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="valueSellTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="box-container">
          <!-- Mô giới -->
          <div class="box-content">
            <div class="title-left typo-body-15">{{ 'MES-518' | translate }}</div>
            <div class="searchbox-wrap">
              <div class="search-box">
                <input
                  type="text"
                  class="input-cls-custom input-style-common typo-body-11 fs-12"
                  [placeholder]="'MES-14' | translate"
                  [formControl]="searchBrokerControl"
                />
                <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
              </div>
              <div class="option-list-cls">
                <!-- (change)="changeSections($event.checked, 'all', 'customer')" -->
                <mat-checkbox [checked]="isSelectAllBrokerFee" class="checkbox-cls">
                  {{ 'MES-58' | translate }}</mat-checkbox
                >
                @for (item of listFilterBrokerOptionsFee; let i = $index; track item) {
                <div class="checkbox-cls-item typo-body-15">
                  <!-- (change)="changeSections($event.checked, 'item', 'customer', item)" -->
                  <mat-checkbox [checked]="item.isSelect" class="checkbox-cls"> {{ item.name }}</mat-checkbox>
                </div>
                }
              </div>
            </div>
          </div>
          <!-- Tổng doanh thu phí -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-194' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="totalRevenueFeeFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="totalRevenueFeeTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!-- Net phí giao dịch -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-195' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="netTransactionFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="netTransactionTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!-- Hoa hồng MG -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-196' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="commissionFeeFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="commissionFeeTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </app-layout-filter-component>
</div>
