import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { IListOptions } from '../../../models/performance';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { LIST_OF_BROKER, LIST_OF_CUSTOMER } from '../../../constants/performance';

/**
 * FeeComimissionDetailFilterComponent
 */
@Component({
  selector: 'app-fee-commission-detail-filter-component',
  templateUrl: './fee-commission-detail-filter.component.html',
  styleUrl: './fee-commission-detail-filter.component.scss',
})
export class FeeComimissionDetailFilterComponent implements OnInit {
  searchCustomerControl = new FormControl();
  searchBrokerControl = new FormControl();

  isSelectAllCustomerFee = true;

  isSelectAllBrokerFee = true;

  listFilterCustomerOptionsFee: IListOptions[] = [];
  listFilterCustomerStoreFee: IListOptions[] = [];

  listFilterBrokerOptionsFee: IListOptions[] = [];
  listFilterBrokerStoreFee: IListOptions[] = [];

  feeCommissionFilterForm: FormGroup;

  /**
   * Constructor
   * @param fb FormBuilder
   * @param _destroy DestroyService
   */
  constructor(private readonly fb: FormBuilder, private readonly _destroy: DestroyService) {
    this.feeCommissionFilterForm = this.fb.group({
      totalRevenueFeeFrom: [null],
      totalRevenueFeeTo: [null],
      totalValueFrom: [null],
      totalValueTo: [null],
      valueBuyFrom: [null],
      valueBuyTo: [null],
      valueSellFrom: [null],
      valueSellTo: [null],
      netTransactionFrom: [null],
      netTransactionTo: [null],
      commissionFeeFrom: [null],
      commissionFeeTo: [null],
    });

    this.updateListFee(null, 'customer');
    this.updateListFee(null, 'broker');
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.listFilterCustomerStoreFee = this.listFilterCustomerOptionsFee;
    this.listFilterBrokerStoreFee = this.listFilterBrokerOptionsFee;

    this.searchCustomerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterCustomerOptionsFee = this._filterFee(value ?? '', this.listFilterCustomerStoreFee);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.searchBrokerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterBrokerOptionsFee = this._filterFee(value ?? '', this.listFilterBrokerStoreFee);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * updateListFee
   * @param data
   * @param type
   */
  updateListFee(data: (string | number)[] | null, type: string) {
    const isSelectFee = (v: string | number) => data === null || data?.includes(v);
    switch (type) {
      case 'customer':
        this.listFilterCustomerOptionsFee = LIST_OF_CUSTOMER.map((customer) => ({
          name: customer.name,
          accountNumber: customer.accountNumber,
          isSelect: isSelectFee(customer.name),
        }));
        this.isSelectAllCustomerFee = this.listFilterCustomerOptionsFee.every((t) => t.isSelect === true);
        break;

      case 'broker':
        this.listFilterBrokerOptionsFee = LIST_OF_BROKER.map((customer) => ({
          name: customer.name,
          isSelect: isSelectFee(customer.name),
        }));
        this.isSelectAllBrokerFee = this.listFilterBrokerOptionsFee.every((t) => t.isSelect === true);
        break;
    }
  }

  /**
   *
   * @param {string} value
   * @param {IListOptions} options
   */
  private _filterFee(value: string, options: IListOptions[]): IListOptions[] {
    const filterValueFee = value.toString().toLowerCase();

    return options.filter(
      (option) =>
        option?.name?.toLowerCase()?.includes(filterValueFee) ||
        option?.accountNumber?.toLowerCase()?.includes(filterValueFee)
    );
  }
}
