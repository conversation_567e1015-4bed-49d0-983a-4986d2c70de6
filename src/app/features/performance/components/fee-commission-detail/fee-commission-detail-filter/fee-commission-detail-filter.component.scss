.fee-commission-detail-filter-cls {
  height: 100%;
  width: 100%;
  .fee-commission-detail-body {
    display: flex;
    height: 100%;
    .box-container {
      flex: 1;
      height: 100%;
      border-right: 1px solid var(--color--other--divider);
      &:last-child {
        border: 0;
      }
      .box-content {
        padding: 12px 24px 16px;
        border-bottom: 1px solid var(--color--other--divider);
        .title-left {
          margin-bottom: 16px;
        }
        .searchbox-wrap {
          display: flex;
          flex-direction: column;
          gap: 17px;
          overflow: hidden;
          min-height: 245px;
          height: 100%;
          .search-box {
            display: flex;
            position: relative;

            .input-cls-custom {
              padding: 10px 16px;
              padding-left: 32px;
              width: 100%;
            }

            .icon-search-cls {
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              left: 10px;
            }
          }

          .option-list-cls {
            height: 164px;
            overflow: auto;
            display: flex;
            flex-direction: column;
            gap: 16px;

            .checkbox-cls {
              ::ng-deep {
                .mdc-label {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  font-size: 12px;
                  white-space: unset !important;
                  text-wrap: unset !important;
                }
              }

              .img-cls {
                width: 18px;
                height: 18px;
                object-fit: contain;
                vertical-align: middle;
                border-radius: 50%;
              }
            }
          }
        }

        .box-range {
          display: flex;
          gap: 8px;
          margin-top: 16px;
          .content-from,
          .content-to {
            width: 50%;
            display: flex;
            flex-direction: column;
            gap: 8px;

            .input-wrapper {
              position: relative;
              cursor: pointer;

              .calendar-input {
                padding: 10px 28px 10px 16px;
                border: 1px solid var(--color--neutral--100);
                border-radius: 8px;
                height: 40px;
                width: 100%;
              }

              img {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                right: 12px;
              }
            }

            .holding-period-input {
              border: 1px solid var(--color--neutral--100);
              padding: 10px 16px;
              border-radius: 8px;
              height: 40px;
              width: 100%;
            }
          }
        }
      }
    }
  }
  form {
    &:has(.fee-commission-detail-body) {
      height: 100%;
      width: 100%;
    }
  }
}

:host {
  width: 100%;
  height: 100%;
}
