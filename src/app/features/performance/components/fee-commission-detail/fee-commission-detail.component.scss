.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.fees-commission-detail-component {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-fees-commission-component {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;
      .box-header-cls {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .text-show {
        display: flex;
        align-items: center;
        gap: 4px;
        & > span {
          cursor: pointer;
          color: var(--color--text--subdued);
          &:hover {
            color: var(--color--text--default);
          }
        }
        &:last-child {
          & > span {
            cursor: default;
            color: var(--color--text--default);
          }
          img[alt='arrow-right'] {
            display: none;
          }
        }
      }

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
      }
    }
    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;
      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        // Trạng thái

        .closed {
          max-width: 156px;
          margin: 0 auto;
          span {
            border-radius: 16px;
            background-color: var(--color--neutral--100);

            input {
              padding: 2px 14px;
            }
          }
        }

        .following {
          max-width: 156px;
          margin: 0 auto;
          span {
            border-radius: 16px;
            background-color: var(--color--accents--orange);

            input {
              padding: 2px 14px;
            }
          }
        }

        .opened {
          max-width: 156px;
          margin: 0 auto;
          span {
            border-radius: 16px;
            background-color: var(--color--accents--green);

            input {
              padding: 2px 14px;
            }
          }
        }

        th {
          input {
            width: 100%;
          }
        }
        .box-flex-1-cls {
          flex: 1;
        }
      }
    }
  }

  ::ng-deep {
    .mat-mdc-tab-group {
      --mdc-tab-indicator-active-indicator-color: #fff;
      --mat-tab-header-disabled-ripple-color: rgba(0, 0, 0, 0.38);
      --mat-tab-header-pagination-icon-color: black;
      --mat-tab-header-inactive-label-text-color: rgba(0, 0, 0, 0.6);
      --mat-tab-header-active-label-text-color: #fff;
      --mat-tab-header-active-ripple-color: #fff;
      --mat-tab-header-inactive-ripple-color: #fff;
      --mat-tab-header-inactive-focus-label-text-color: rgba(0, 0, 0, 0.6);
      --mat-tab-header-inactive-hover-label-text-color: rgba(0, 0, 0, 0.6);
      --mat-tab-header-active-focus-label-text-color: #fff;
      --mat-tab-header-active-hover-label-text-color: #fff;
      --mat-tab-header-active-focus-indicator-color: #fff;
      --mat-tab-header-active-hover-indicator-color: #fff;
    }
  }

  ::ng-deep {
    .mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs > .mat-mdc-tab-header .mat-mdc-tab {
      flex-grow: 0 !important;
    }
  }

  ::ng-deep {
    .mat-mdc-tab-header {
      --mdc-secondary-navigation-tab-container-height: 32px !important;
    }
  }

  ::ng-deep {
    .mdc-tab {
      padding: 0 8px !important;
    }
  }
}

//ngTemplate

.price-increase,
.price-reduce,
.price-stable {
  &.price-increase {
    span {
      color: var(--color--accents--green);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      text-wrap: nowrap;
    }
  }
  &.price-reduce {
    span {
      color: var(--color--accents--red);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      text-wrap: nowrap;
    }
  }
  &.price-stable {
    span {
      color: var(--color--warning--500);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      text-wrap: nowrap;
    }
  }
}

.box-info-wrapper {
  margin: 12px 12px;

  .box-info {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 12px 4px 8px;
    border-radius: 16px;
    background-color: #f8fafd;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    color: #808080;
    // text
    white-space: nowrap;
    text-wrap: nowrap;
  }
}

// Hide the tabContainer
.hidden {
  display: none;
}

.dropdown-btn,
.collapse-btn {
  display: flex;
  align-items: center;
  background-color: var(--color--brand--50);
  border-radius: 16px;

  img {
    padding: 4px, 8px, 4px, 8px;
    width: 20px;
    height: 20px;
  }
}
