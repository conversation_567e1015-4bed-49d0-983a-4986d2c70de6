import { Component, Inject, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { IFilterFeesCommission } from '../../models/performance';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { Store } from '@ngrx/store';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { fakeData } from './fakedata';
import { Router } from '@angular/router';
import { FeeComimissionDetailFilterComponent } from './fee-commission-detail-filter/fee-commission-detail-filter.component';
import { setItemSelected } from '../../stores/performance.actions';

/**
 * FeesCommissionDetailComponent
 */
@Component({
  selector: 'app-fee-commission-detail-component',
  templateUrl: './fee-commission-detail.component.html',
  styleUrl: './fee-commission-detail.component.scss',
})
export class FeesCommissionDetailComponent extends BaseTableComponent<any> implements OnInit {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('sellGmv', { static: true }) sellGmv: TemplateRef<any> | null = null;
  @ViewChild('buyGmv', { static: true }) buyGmv: TemplateRef<any> | null = null;

  customNumberFormat = customNumberFormat;

  filterOptions!: IFilterFeesCommission;
  tags = [`Số TK : ${customNumberFormat(100)}`];

  fakeDataFee: any[] = fakeData;

  showCollapse: boolean = false;

  listBreadcrumbFee: string[] = [];

  itemSelectedFee = {
    id: '',
    name: '',
    date: '',
  };
  /**
   *Constructor
   * @param dialogService DialogService
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param cdf ChangeDetectorRef
   * @param route ActivatedRoute
   * @param router Router
   * @param store Store
   */
  constructor(
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly router: Router,
    private readonly store: Store
  ) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);

    this.data = this.fakeDataFee;
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.listBreadcrumbFee = ['MES-256'];

    const paramsFee = this.route.snapshot.queryParams;
    this.itemSelectedFee = {
      id: paramsFee['id'],
      name: paramsFee['name'],
      date: paramsFee['date'],
    };

    this.listBreadcrumbFee = [...this.listBreadcrumbFee, paramsFee['date'], paramsFee['name']];
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeDataFee);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        tag: 'broker',
        name: 'Môi giới',
        width: 200,
        minWidth: 30,
        isDisplay: true,
        resizable: true,
      },
      {
        tag: 'accountNumber',
        name: 'Số tài khoản',
        minWidth: 30,
        width: 120,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        tag: 'name',
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 140,
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        tag: 'gmv',
        name: 'Tổng GTGD',
        minWidth: 30,
        width: 140,
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        align: 'end',
      },
      {
        tag: 'buyGmv',
        name: 'GTGD (mua)',
        minWidth: 30,
        width: 140,
        isDisplay: true,
        resizable: true,
        cellTemplate: this.buyGmv,
        align: 'end',
      },
      {
        name: 'GTGD (bán)',
        minWidth: 30,
        width: 140,
        tag: 'sellGmv',
        cellTemplate: this.sellGmv,
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        tag: 'revenueFees',
        name: 'Tổng doanh thu phí',
        minWidth: 30,
        width: 160,
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'Net phí giao dịch',
        minWidth: 30,
        width: 140,
        tag: 'transactionFees',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'Hoa hồng MG',
        minWidth: 30,
        width: 120,
        tag: 'mgCommission',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(v) : '-';
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
    ];
  }

  /**
   *Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    if (tag === 'filter') {
      this.openFilter(FeeComimissionDetailFilterComponent, {
        width: '800px',
        // data: this.filterOptions,
      });
    }
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * NavigateFeeCommissionList
   * @param item
   * @param index
   * @returns {void} prevent event
   */
  navigateFeeCommissionListFee(item: string, index: number) {
    if (index === this.listBreadcrumbFee.length - 1) return;
    if (index !== 0) {
      this.store.dispatch(setItemSelected({ ...this.itemSelectedFee }));
    } else if (index === 0) this.store.dispatch(setItemSelected({ id: '', name: '', date: '' }));

    this.router.navigate(['/job-performance/fees-commission']);
  }
}
