import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { IListOptions } from '../../../models/performance';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { LIST_OF_BROKER, LIST_OF_CUSTOMER } from '../../../constants/performance';

/**
 * DepositWithDrawalDetailFilterComponent
 */
@Component({
  selector: 'app-deposit-withdrawal-detail-filter-component',
  templateUrl: './deposit-withdrawal-detail-filter.component.html',
  styleUrl: './deposit-withdrawal-detail-filter.component.scss',
})
export class DepositWithDrawalDetailFilterComponent implements OnInit {
  searchCustomerControl = new FormControl();
  searchBrokerControl = new FormControl();

  isSelectAllCustomerDep = true;

  isSelectAllBrokerDep = true;

  listFilterCustomerOptionsDep: IListOptions[] = [];
  listFilterCustomerStoreDep: IListOptions[] = [];

  listFilterBrokerOptionsDep: IListOptions[] = [];
  listFilterBrokerStore: IListOptions[] = [];

  depositWithDrawalFilterForm: FormGroup;

  /**
   * Constructor
   * @param fb FormBuilder
   * @param _destroy DestroyService
   */
  constructor(private readonly fb: FormBuilder, private readonly _destroy: DestroyService) {
    this.depositWithDrawalFilterForm = this.fb.group({
      depositApprovalFrom: [null],
      depositApprovalTo: [null],
      depositNotApprovalFrom: [null],
      depositNotApprovalTo: [null],
      withDrawalApprovalFrom: [null],
      withDrawalApprovalTo: [null],
      withDrawalNotApprovalFrom: [null],
      withDrawalNotApprovalTo: [null],
    });

    this.updateListDep(null, 'customer');
    this.updateListDep(null, 'broker');
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.listFilterCustomerStoreDep = this.listFilterCustomerOptionsDep;
    this.listFilterBrokerStore = this.listFilterBrokerOptionsDep;

    this.searchCustomerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterCustomerOptionsDep = this._filterDep(value ?? '', this.listFilterCustomerStoreDep);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.searchBrokerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterBrokerOptionsDep = this._filterDep(value ?? '', this.listFilterBrokerStore);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * updateList
   * @param data
   * @param type
   */
  updateListDep(data: (string | number)[] | null, type: string) {
    const isSelectDep = (v: string | number) => data === null || data?.includes(v);
    switch (type) {
      case 'customer':
        this.listFilterCustomerOptionsDep = LIST_OF_CUSTOMER.map((customer) => ({
          name: customer.name,
          accountNumber: customer.accountNumber,
          isSelect: isSelectDep(customer.name),
        }));
        this.isSelectAllCustomerDep = this.listFilterCustomerOptionsDep.every((t) => t.isSelect === true);
        break;

      case 'broker':
        this.listFilterBrokerOptionsDep = LIST_OF_BROKER.map((customer) => ({
          name: customer.name,
          isSelect: isSelectDep(customer.name),
        }));
        this.isSelectAllBrokerDep = this.listFilterBrokerOptionsDep.every((t) => t.isSelect === true);
        break;
    }
  }

  /**
   *
   * @param {string} value
   * @param {IListOptions} options
   */
  private _filterDep(value: string, options: IListOptions[]): IListOptions[] {
    const filterValueDep = value.toString().toLowerCase();

    return options.filter(
      (option) =>
        option?.name?.toLowerCase()?.includes(filterValueDep) ||
        option?.accountNumber?.toLowerCase()?.includes(filterValueDep)
    );
  }
}
