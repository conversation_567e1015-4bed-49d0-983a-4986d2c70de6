<div class="deposit-with-drawal-detail-filter-cls">
  <app-layout-filter-component>
    <form [formGroup]="depositWithDrawalFilterForm">
      <div class="deposit-with-drawal-detail-body">
        <div class="box-container">
          <div class="box-content">
            <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
            <div class="searchbox-wrap">
              <div class="search-box">
                <input
                  type="text"
                  class="input-cls-custom input-style-common typo-body-11 fs-12"
                  [placeholder]="'MES-295' | translate"
                  [formControl]="searchCustomerControl"
                />
                <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
              </div>
              <div class="option-list-cls">
                <!-- (change)="changeSections($event.checked, 'all', 'customer')" -->
                <mat-checkbox [checked]="isSelectAllCustomerDep" class="checkbox-cls">
                  {{ 'MES-58' | translate }}</mat-checkbox
                >
                @for (item of listFilterCustomerOptionsDep; let i = $index; track item) {
                <div class="checkbox-cls-item typo-body-15">
                  <!-- (change)="changeSections($event.checked, 'item', 'customer', item)" -->
                  <mat-checkbox [checked]="item.isSelect" class="checkbox-cls">
                    {{ item.name }} - {{ item.accountNumber }}</mat-checkbox
                  >
                </div>
                }
              </div>
            </div>
          </div>
          <!-- Mô giới -->
          <div class="box-content">
            <div class="title-left typo-body-15">{{ 'MES-518' | translate }}</div>
            <div class="searchbox-wrap">
              <div class="search-box">
                <input
                  type="text"
                  class="input-cls-custom input-style-common typo-body-11 fs-12"
                  [placeholder]="'MES-14' | translate"
                  [formControl]="searchBrokerControl"
                />
                <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
              </div>
              <div class="option-list-cls">
                <!-- (change)="changeSections($event.checked, 'all', 'customer')" -->
                <mat-checkbox [checked]="isSelectAllBrokerDep" class="checkbox-cls">
                  {{ 'MES-58' | translate }}</mat-checkbox
                >
                @for (item of listFilterBrokerOptionsDep; let i = $index; track item) {
                <div class="checkbox-cls-item typo-body-15">
                  <!-- (change)="changeSections($event.checked, 'item', 'customer', item)" -->
                  <mat-checkbox [checked]="item.isSelect" class="checkbox-cls"> {{ item.name }}</mat-checkbox>
                </div>
                }
              </div>
            </div>
          </div>
          <!-- Tiền nộp đã duyệt -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-525' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="depositApprovalFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="depositApprovalTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!--Tiền nộp chưa duyệt-->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-526' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="depositNotApprovalFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="depositNotApprovalTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!-- Tiền rút đã duyệt -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-528' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="withDrawalApprovalFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="withDrawalApprovalTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
          <!-- Tiền rút chưa duyệt -->
          <div class="box-content">
            <div class="typo-body-15">{{ 'MES-527' | translate }}</div>
            <div class="box-range">
              <div class="content-from">
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="withDrawalNotApprovalFrom"
                  [placeholder]="'0'"
                />
              </div>

              <div class="content-to">
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'000 000 000 000 000 000'"
                  [allowNegativeNumbers]="true"
                  formControlName="withDrawalNotApprovalTo"
                  [placeholder]="'∞'"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </app-layout-filter-component>
</div>
