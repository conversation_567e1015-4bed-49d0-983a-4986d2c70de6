import { Component, Inject, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { fakeData } from './fakedata';
import { DepositWithDrawalDetailFilterComponent } from './deposit-withdrawal-detail-filter/deposit-withdrawal-detail-filter.component';
import { setItemSelected } from '../../stores/performance.actions';
import { Store } from '@ngrx/store';

/**
 * DepositWithDrawalDetailComponent
 */
@Component({
  selector: 'app-deposit-withdrawal-component',
  templateUrl: './deposit-withdrawal.component.html',
  styleUrl: './deposit-withdrawal.component.scss',
})
export class DepositWithDrawalDetailComponent extends BaseTableComponent<any> implements OnInit {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('paymentApproved', { static: true }) paymentApproved: TemplateRef<any> | null = null;
  @ViewChild('paymentUnapproved', { static: true }) paymentUnapproved: TemplateRef<any> | null = null;
  @ViewChild('withdrawalApproved', { static: true }) withdrawalApproved: TemplateRef<any> | null = null;
  @ViewChild('withdrawalUnapproved', { static: true }) withdrawalUnapproved: TemplateRef<any> | null = null;

  fakeData: any[] = fakeData;

  customNumberFormat = customNumberFormat;
  tags = [`Số TK nộp : ${customNumberFormat(100)}`];

  showCollapse: boolean = true;
  listBreadcrumbDep: string[] = [];
  itemSelectedDep = {
    id: '',
    name: '',
    date: '',
  };
  /**
   *Constructor
   * @param popoverRef : PopoverRef
   * @param router Router
   * @param store Store
   */
  constructor(
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly router: Router,
    private readonly store: Store
  ) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);
    this.data = this.fakeData;
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.listBreadcrumbDep = ['MES-256'];

    const paramsDep = this.route.snapshot.queryParams;
    this.itemSelectedDep = {
      id: paramsDep['id'],
      name: paramsDep['name'],
      date: paramsDep['date'],
    };

    this.listBreadcrumbDep = [...this.listBreadcrumbDep, paramsDep['date'], paramsDep['name']];
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    if (tag === 'filter') {
      this.openFilter(DepositWithDrawalDetailFilterComponent, {
        width: '400px',
        // data: this.filterOptions,
      });
    }
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        minWidth: 30,
        name: 'Môi giới',
        width: 200,
        tag: 'broker',
        isDisplay: true,
        resizable: true,
      },
      {
        minWidth: 30,
        name: 'Số tài khoản',
        width: 120,
        tag: 'accountNumber',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        minWidth: 30,
        name: 'Tên khách hàng',
        width: 140,
        tag: 'name',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        minWidth: 30,
        name: 'Tiền nộp đã duyệt',
        width: 160,
        tag: 'paymentApproved',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.paymentApproved,
        align: 'end',
      },
      {
        minWidth: 30,
        name: 'Tiền nộp chưa duyệt',
        width: 160,
        tag: 'paymentUnapproved',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.paymentUnapproved,
        align: 'end',
      },
      {
        minWidth: 30,
        name: 'Tiền rút đã duyệt',
        width: 160,
        tag: 'withdrawalApproved',
        cellTemplate: this.withdrawalApproved,
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: 'Tiền rút chưa duyệt',
        minWidth: 30,
        width: 160,
        tag: 'withdrawalUnapproved',
        cellTemplate: this.withdrawalUnapproved,
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
    ];
  }

  /**
   * NavigateFeeCommissionList
   * @param item
   * @param index
   * @returns {void} prevent event
   */
  navigateFeeCommissionListDep(item: string, index: number) {
    if (index === this.listBreadcrumbDep.length - 1) return;
    if (index !== 0) {
      this.store.dispatch(setItemSelected({ ...this.itemSelectedDep }));
    } else if (index === 0) this.store.dispatch(setItemSelected({ id: '', name: '', date: '' }));

    this.router.navigate(['/job-performance/deposit-withdrawal']);
  }
}
