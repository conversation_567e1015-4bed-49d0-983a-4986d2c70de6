import {
  EBrokerAndRoomType,
  ENewAccountType,
  ETypeRevenueFeeOption,
  ETypeTimeOption,
  ETypeTotalMoney,
  ITypeOptionView,
} from '../models/performance';

export enum EViewPerformance {
  TABLE,
  CHART_TIME,
  CHART_MG,
}

export const ListOptionsChangeView: ITypeOptionView[] = [
  {
    name: 'MES-372',
    asset: './assets/icons/chart-by-time.svg',
    value: EViewPerformance.CHART_TIME,
  },
  {
    name: 'MES-373',
    asset: './assets/icons/chart-by-time.svg',
    value: EViewPerformance.CHART_MG,
  },
  {
    name: 'MES-371',
    asset: './assets/icons/table-icon.svg',
    value: EViewPerformance.TABLE,
  },
];

export const ListRevenueFeeOption = [
  { name: 'Doanh thu phí', value: ETypeRevenueFeeOption.REVENUE_FEE },
  { name: 'Net phí GD', value: ETypeRevenueFeeOption.NET },
  { name: 'Hoa hồng MG', value: ETypeRevenueFeeOption.COMMISSION_FEE },
];
export const ListRevenueFeeTimeOption = [
  { name: 'Ngày', value: ETypeTimeOption.DAY },
  { name: 'Tuần', value: ETypeTimeOption.WEEK },
  { name: '1 tháng', value: ETypeTimeOption.ONE_MONTH },
  { name: '6 tháng', value: ETypeTimeOption.HALF_OF_YEAR },
  { name: 'Năm', value: ETypeTimeOption.YEAR },
];
export const RankAccountTransactionOption = [
  { name: 'Tiền nộp / rút', value: ETypeTotalMoney.DEPOSIT_WITHDRAWAL },
  { name: 'Tiền mua / bán', value: ETypeTotalMoney.BUY_SELL },
];
export const ProportionRevenueFeeOption = [
  { name: 'MES-546', value: EBrokerAndRoomType.ROOM },
  { name: 'MES-387', value: EBrokerAndRoomType.BROKER },
];
export const NewAccountOption = [
  { name: 'Tài khoản mở mới', value: ENewAccountType.NEW },
  { name: 'Tổng số TK', value: ENewAccountType.TOTAL },
];

export enum EProportionRevenueFee {
  TOPBROKERROOMS,
  TOPBROKERS,
}

export const CONVERT_PROPORTIONREVENUEFEE_TO_NUMBER: { [key: string]: number } = {
  'MES-387': EProportionRevenueFee.TOPBROKERROOMS,
  'MES-546': EProportionRevenueFee.TOPBROKERS,
};

export enum ETypeAccount {
  INDIVIDUAL,
  ORGANIZATION,
}

export const CONVERT_TYPE_ACCOUNT_TO_LABLE: { [key: number]: string } = {
  [ETypeAccount.INDIVIDUAL]: 'Cá nhân',
  [ETypeAccount.ORGANIZATION]: 'Tổ chức',
};

export enum EStatusAccount {
  ACTIVE,
  DEACTIVE,
}

export const CONVERT_STATUS_ACCOUNT_TO_LABLE: { [key: number]: string } = {
  [EStatusAccount.ACTIVE]: 'ĐÃ DUYỆT',
  [EStatusAccount.DEACTIVE]: 'CHƯA DUYỆT',
};

export const LIST_OF_CUSTOMER = [
  {
    accountNumber: '069C-125485',
    name: 'Phạm Thị Thu Trang',
  },
  {
    accountNumber: '069C-586547',
    name: 'Đặng Hoàng An Nhiên',
  },
  {
    accountNumber: '069C-918882',
    name: 'Phạm Tiến Nam Phương',
  },
  {
    accountNumber: '069C-891135',
    name: 'Trần Văn Hậu',
  },
  {
    accountNumber: '069C-251114',
    name: 'Công ty cổ phần địa ốc Ngọc Minh Huy',
  },
  {
    accountNumber: '069C-637085',
    name: 'Công ty TNHH Tigon 68',
  },
  {
    accountNumber: '069C-316087',
    name: 'Công ty TNHH Mica Group',
  },
  {
    accountNumber: '069C-388482',
    name: 'Công ty cổ phần Money Max',
  },
  {
    accountNumber: '069C-862656',
    name: 'Công ty TNHH du lịch Cá Voi Xanh',
  },
  {
    accountNumber: '069C-252138',
    name: 'Công ty TNHH xây dựng và đầu tư Phú Khang',
  },
  {
    accountNumber: '069C-400190',
    name: 'Ngô Thị Hằng',
  },
  {
    accountNumber: '069C-883962',
    name: 'Bùi Thị Hạnh',
  },
];

export const LIST_OF_BROKER = [
  {
    name: 'MG-01: Nguyễn Tuấn Dương',
  },
  {
    name: 'MG-02: Nguyễn Tuấn Anh',
  },
  {
    name: 'MG-03: Nguyễn Dương Quang',
  },
  {
    name: 'MG-04: Phạm Văn Tây',
  },
  {
    name: 'MG-05: Đinh Quang Anh',
  },
  {
    name: 'MG-06: Lưu Quang Tuấn',
  },
  {
    name: 'MG-07: Phạm Tiến Nam Phương',
  },
  {
    name: 'MG-08: Phạm Thị Thu Trang',
  },
  {
    name: 'MG-09: Ngô Thị Thúy Hạnh',
  },
];
