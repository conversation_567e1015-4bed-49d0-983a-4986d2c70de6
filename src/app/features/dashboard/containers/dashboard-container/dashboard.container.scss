:host{
  width: 100%;
  height: 100%;
  padding:  16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: auto;
  background-color: var(--color--neutral--white);
}


.top_row{
  display: flex;
  width: 100%;
  gap: 16px;
  flex: 1;
  min-width: 1460px;
}

.bottom_row{
  min-width: 1460px;
}

.customer_box{
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  background-color: var(--color--neutral--50);
  cursor: pointer;

  &__chart{
    flex: 1;
  }

  .customer_chart{
    display: flex;
    width: 100%;
    height: 100%;
    gap: 24px;  }


    &:hover{
      background-color: var(--color--brand--50);
    }
}



.box_cls{
  flex: 1;
  padding: 24px 16px;
  background-color: var(--color--neutral--white);
  display: flex;
  flex-direction: column;
  gap: 24px;
  border-radius: 8px;
  border: 1px solid var(--color--other--divider);



  &.customer_content{
    gap: 16px;

    .customer_box__chart{
      margin-top: 8px;
    }
  }

  .box-content_cls{
    display: flex;
    gap: 16px;
    height: 100%;

    .content_cls{
      border-radius: 8px;
      background-color: var(--color--neutral--50);
      display: flex;
      flex-direction: column;
      height: 100%;
      cursor: pointer;

      &.big{
        flex: 2;
      }
      &.small{
        flex: 1
      }

      &:hover{
        background-color: var(--color--brand--50);
      }
    }
  }
}


.header_cls{
  display: flex;
  gap: 12px;
  align-items: center;
  color: var(--color--text--subdued);
  justify-content: center;
  cursor: pointer;

  img{
    width: 16px;
    height: 16px;
  }
}

.value_cls{
  text-align: center;
}



