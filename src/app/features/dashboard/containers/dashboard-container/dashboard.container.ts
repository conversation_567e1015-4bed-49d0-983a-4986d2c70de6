import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { combineLatest, takeUntil } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { getDashboardTradeOrderInfo } from '../../stores/dashboard.actions';
import { selectCustomerData$, selectNavValue$, selectRecommendationInfo$, selectTopBuyOrders$, selectTopSellOrders$, selectTradeOrderCount$, selectTradeOrderTotalValue$ } from '../../stores/dashboard.selections';
import { selectAllBrokerLevelListByBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { getAllChildBrokerCodes } from 'src/app/shared/utils/trading';


/**
 * Customer View
 */
@Component({
  selector: 'app-dashboard-container',
  templateUrl: './dashboard.container.html',
  styleUrls: ['./dashboard.container.scss'],
  providers: [DestroyService],
})
export class DashboardContainer implements OnInit, OnDestroy {

  brokerId = '';

  tradeOrderTotalValue$ = this.store.select(selectTradeOrderTotalValue$);

  tradeOrderCount$ = this.store.select(selectTradeOrderCount$)

  topBuyOrders$ = this.store.select(selectTopBuyOrders$);

  topSellOrders$ = this.store.select(selectTopSellOrders$);

  recommendation$ = this.store.select(selectRecommendationInfo$);

  customerData$ = this.store.select(selectCustomerData$);

  navValue = 0;

  constructor(
        private readonly route: ActivatedRoute,
        private readonly _destroy: DestroyService,
        private readonly store: Store,
  ) {
  }

  ngOnInit(): void {
        this.route.queryParams.pipe(takeUntil(this._destroy)).subscribe((param) => {
          this.brokerId = param['brokerId'];
        });

        combineLatest([this.store.select(selectInfoUserLogin$),
          this.store.select(selectAllBrokerLevelListByBrokerView$)
        ]).pipe(takeUntil(this._destroy)).subscribe(([users, allOpenTradeBrokerLevelList] )=> {
          if(!users?.length || !allOpenTradeBrokerLevelList.length) return;
          const {brokerCode, userName} = users[0];
          const brokerCodes = getAllChildBrokerCodes(allOpenTradeBrokerLevelList, brokerCode);
            this.store.dispatch(getDashboardTradeOrderInfo({ brokerCode, emNo: userName, brokerCodes}))
        })

            this.store.select(selectNavValue$).pipe(takeUntil(this._destroy)).subscribe(res =>{
      this.navValue = res;
    })

  }
  ngOnDestroy(): void {


  }


}
