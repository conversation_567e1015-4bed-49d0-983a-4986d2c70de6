<div class="top_row">

  <div class="box_cls customer_content">
    <div class="typo-heading-8"> {{'MES-674' | translate}}</div>

    <div
    [routerLink]="['/customers/personal-info']"
          [queryParams]="brokerId ? { brokerId } : null"
    class="customer_box customer_box__chart">
      <div class="header_cls">
        <div class="typo-body-4">{{'MES-676' | translate}}</div>
        <img src="./assets/icons/info-circle.svg" alt="info-down" tooltipText [text]="'MES-685' | translate" [maxWidth]="260" />
      </div>
      <div class="customer_chart">
        <app-customer-pie-chart-component
        [data$]="customerData$"
        [id]="'dashboard-customer-pie-chart'"
        ></app-customer-pie-chart-component>
      </div>
    </div>

    <div
     [routerLink]="['/asset/asset-info']"
          [queryParams]="brokerId ? { brokerId } : null"
    class="customer_box">
      <div
      class="header_cls"
      >
        <div class="typo-body-4">{{'MES-677' | translate}}</div>
        <img src="./assets/icons/info-circle.svg" alt="info-down"
        tooltipText [text]="'MES-686' | translate" [maxWidth]="260"
        />
      </div>

      <div class="typo-heading-6 value_cls">{{navValue | numberFormat}} VND</div>
    </div>

  </div>
  <div class="recommendation_info box_cls">
    <app-recommendation-info-chart-component
        [brokerId]="brokerId"
        [data$]="recommendation$"
    ></app-recommendation-info-chart-component>
  </div>

</div>

<div class="bottom_row box_cls">
    <div class="typo-heading-8"> {{'MES-679' | translate}}</div>
  <div class="box-content_cls">
    <div
    [routerLink]="['/trade-order/match-orders']" class="header_cls"
          [queryParams]="brokerId ? { brokerId } : null"
    class="content_cls big">
      <app-order-trade-value-chart-component
        [brokerId]="brokerId"
        [data$]="tradeOrderTotalValue$"
      ></app-order-trade-value-chart-component>
    </div>
    <div class="content_cls small">
      <app-top-item-info-component
      [data$]="topBuyOrders$"
      ></app-top-item-info-component>
    </div>
    <div class="content_cls small">
      <app-top-item-info-component
      [data$]="topSellOrders$"
      [labelHeader]="'MES-682'"
      [iconHeader]="'./assets/icons/arrow-top-right.svg'"
      [color]="'#eb2018'"
      ></app-top-item-info-component>

    </div>
    <div
    [routerLink]="['/trade-order/trade-order-management']" class="header_cls"
          [queryParams]="brokerId ? { brokerId } : null"
    class="content_cls big">
      <app-order-trade-customer-chart-component
        [brokerId]="brokerId"
        [data$]="tradeOrderCount$"
      ></app-order-trade-customer-chart-component>
    </div>

  </div>
</div>


