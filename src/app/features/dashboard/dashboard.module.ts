import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';

import { MatTabsModule } from '@angular/material/tabs';
import { UnsaveChangeGuard } from 'src/app/core/guards/unsaved-changes.guard';
import { ActionBtnComponent } from 'src/app/shared/components/action-btn/action-btn.component';
import { CalendarCustomComponent } from 'src/app/shared/components/date-picker/date-picker/date-picker.component';
import { FilterComponent } from 'src/app/shared/components/filter/filter.component';
import { InputNumberCustomComponent } from 'src/app/shared/components/input-custom-for-table/input-number-custom/input-number-custom.component';
import { InputProportionComponent } from 'src/app/shared/components/input-custom-for-table/input-proportion/input-proportion.component';
import { PhoneNumberTableComponent } from 'src/app/shared/components/phone-number-table/phone-number-table.component';
import { AmountSelectorComponent } from 'src/app/shared/components/stock/amount-selector/amount-selector.component';
import { StockInfoComponent } from 'src/app/shared/components/stock/stock-info/stock-info.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { FalsyToHyphenPipe } from 'src/app/shared/pipes/falsy-to-hyphen/falsy-to-hyphen.pipe';
import { NumberFormatPipe } from 'src/app/shared/pipes/format-number/format-number.pipe';
import { NgxMaskDirective } from 'src/app/shared/directives/mask/ngx-mask.directive';
import { StoreModule } from '@ngrx/store';
import { DASHBOARD_STATE_NAME } from './stores/dashboard.selections';
import { EffectsModule } from '@ngrx/effects';
import { RangeDatePickerComponent } from 'src/app/shared/components/date-picker/range-date-picker/range-date-picker.component';
import { FormControlComponent } from 'src/app/shared/components/form-control/form-control.component';
import { TableScrollingViewportModule } from '../../shared/components/table-custom/table-scrolling-viewport/table-scrolling-viewport.module';
import { PeopleTableComponent } from 'src/app/shared/components/table-custom/test/test.component';
import { AmountSelectorNoBorderComponent } from 'src/app/shared/components/stock/amount-selector-no-border/amount-selector-no-border.component';
import { StockInfoV2Component } from 'src/app/shared/components/stock/stock-info-v2/stock-info-v2.component';
import { MatRadioModule } from '@angular/material/radio';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { DashboardView } from './views/dashboard-view/dashboard.view';
import { DashboardEffects } from './stores/dashboard.effects';
import { dashboardReducers } from './stores/dashboard.reducers';
import { DashboardRoutingModule } from './dashboard-routing.module';
import { DashboardContainer } from './containers/dashboard-container/dashboard.container';
import { CustomerPieChartComponent } from './components/customer-pie-chart/customer-pie-chart.component';
import { RecommendationInfoChartComponent } from './components/recommendation-info/recommendation-info.component';
import { OrderTradeValueChartComponent } from './components/order-trade-value-chart/order-trade-value-chart.component';
import { DoughnutChartComponent } from 'src/app/shared/components/doughnut-chart/doughnut-chart.component';
import { OrderTradeCustomerChartComponent } from './components/order-trade-customer-chart/order-trade-customer-chart.component';
import { TopItemInfoComponent } from './components/top-item-info/top-item-info.component';
import { ToolTipTextDirective } from 'src/app/shared/directives/tooltip/tooltip-text.directive';
import { FormatDatePipe } from 'src/app/shared/pipes/format-date/format-date.pipe';
import { CreatePlaceOrderButton } from 'src/app/shared/components/create-place-order-button/create-place-order-button.component';

const SHARED = [
  GridComponent,
  ActionBtnComponent,
  DraggableListComponent,
  CalendarCustomComponent,
  PhoneNumberTableComponent,
  InputProportionComponent,
  InputNumberCustomComponent,
  AmountSelectorComponent,
  StockInfoComponent,
  RangeDatePickerComponent,
  FormControlComponent,
  AmountSelectorNoBorderComponent,
  PeopleTableComponent,
  StockInfoV2Component,
  VirtualScrollListComponent,
  DoughnutChartComponent,
  CreatePlaceOrderButton,
];

const VIEWS = [DashboardView];

const CONTAINERS = [DashboardContainer];

const PIPES = [FalsyToHyphenPipe, NumberFormatPipe];

const COMPONENTS = [
  CustomerPieChartComponent,
  RecommendationInfoChartComponent,
  OrderTradeValueChartComponent,
  OrderTradeCustomerChartComponent,
  TopItemInfoComponent,
];

/**
 * Customer Module
 */
@NgModule({
  declarations: [...VIEWS, ...CONTAINERS, ...COMPONENTS],
  imports: [
    ...SHARED,
    ...PIPES,
    CommonModule,
    TranslateModule,
    DashboardRoutingModule,
    NgxMaskDirective,
    FilterComponent,
    ToolTipTextDirective,
    FormatDatePipe,
    // Material Module
    MatTableModule,
    MatTabsModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    FormsModule,
    MatDividerModule,
    MatDatepickerModule,
    MatMenuModule,
    MatDialogModule,
    OverlayModule,
    MatIconModule,
    MatCheckboxModule,
    MatTooltipModule,
    MatSelectModule,
    MatRadioModule,
    MatProgressSpinnerModule,
    MatAutocompleteModule,
    // Store
    StoreModule.forFeature(DASHBOARD_STATE_NAME, dashboardReducers),
    EffectsModule.forFeature([DashboardEffects]),
    TableScrollingViewportModule,
  ],
  providers: [UnsaveChangeGuard],
})
export class DashboardModule {}
