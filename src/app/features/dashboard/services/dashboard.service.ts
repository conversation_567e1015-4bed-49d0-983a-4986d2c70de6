import { Injectable } from '@angular/core';
import { ApiService } from 'src/app/core/services';
import { IDasboardNavPayload, IDasboardRecommendationPayload, IDashboardCustomer, IDashboardNav, IDashboardRecommendation, IDashboardTradeOrder, IDashboardTradeOrderCount, IDashboardTradeOrderTotalValue, ITradeOrderCountPayload, ITradeOrderTotalValuePayload } from '../models/dashboard';
import { ApiResponse } from 'src/app/core/models/api-response';
import { map } from 'rxjs';


@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  url = 'v1/order';
  urlAdivice = 'v1/advice';
  urlAssets = 'v1/assets';
  urlCustomer = 'v1/customer';


  /**
   *constructor
   * @param apiService apiService
   */
  constructor(private readonly apiService: ApiService) {

  }


    getDashboardTradeOrderTotalValue(payload: ITradeOrderTotalValuePayload) {
      return this.apiService
        .post<ApiResponse<IDashboardTradeOrderTotalValue>>(`${this.url}/dashboard/total-value`, payload)
        .pipe(map((res) => res.data));
    }

        getDashboardTradeOrderCount(payload: ITradeOrderCountPayload) {
      return this.apiService
        .post<ApiResponse<IDashboardTradeOrderCount>>(`${this.url}/dashboard/count-order`, payload)
        .pipe(map((res) => res.data));
    }


            getTopBuyOrders(payload: ITradeOrderTotalValuePayload) {
      return this.apiService
        .post<ApiResponse<IDashboardTradeOrder[]>>(`${this.url}/dashboard/top-purchased`, payload)
        .pipe(map((res) => res.data));
    }


                getTopSellOrders(payload: ITradeOrderTotalValuePayload) {
      return this.apiService
        .post<ApiResponse<IDashboardTradeOrder[]>>(`${this.url}/dashboard/top-sell`, payload)
        .pipe(map((res) => res.data));
    }


    getDashboardRecommendationInfo(payload: IDasboardRecommendationPayload) {
      return this.apiService
        .post<ApiResponse<IDashboardRecommendation>>(`${this.urlAdivice}/dashboard/info-advice`, payload)
        .pipe(map((res) => res.data));
    }


        getDashboardNavValue(payload: IDasboardNavPayload) {
      return this.apiService
        .post<ApiResponse<IDashboardNav>>(`${this.urlAssets}/dashboard/nav`, payload)
        .pipe(map((res) => res.data));
    }


            getDashboardCustomer(brokerCode: string) {
      return this.apiService
        .get<ApiResponse<IDashboardCustomer>>(`${this.urlCustomer}/total-customer/${brokerCode}`)
        .pipe(map((res) => res.data));
    }





}
