:host{
  display: flex;
  padding: 16px;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  // background-color: var(--color--neutral--50);
  border-radius: 8px;
  height: 100%;
}


.total_value{
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;

  .header_cls{
  display: flex;
  gap: 12px;
  align-items: center;
  color: var(--color--text--subdued);
  justify-content: center;
  cursor: pointer;

  img{
    width: 16px;
    height: 16px;
  }
}
}

.chart-container{
  height: 100%;
}
