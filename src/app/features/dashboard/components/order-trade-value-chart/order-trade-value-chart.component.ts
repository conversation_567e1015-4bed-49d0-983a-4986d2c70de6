import {
  Component,
  Input,
  OnInit,
  ViewContainerRef,
} from '@angular/core';
import { ArcElement, Chart, DoughnutController, Legend, Title, Tooltip } from 'chart.js';
import { Overlay,  } from '@angular/cdk/overlay';
import { DestroyService, I18nService } from 'src/app/core/services';
import { Observable, takeUntil } from 'rxjs';
import { IDashboardTradeOrderTotalValue, IDataChart, ILabelChart } from '../../models/dashboard';
import { calculatorPercent } from '../../helpers/helpers';

Chart.register(
  ArcElement,
  DoughnutController,
  Tooltip,
  Legend,
  Title,
);

/**
 * DoughnutChartComponent
 */
@Component({
  selector: 'app-order-trade-value-chart-component',
  templateUrl: './order-trade-value-chart.component.html',
  styleUrl: './order-trade-value-chart.component.scss',
  providers: [DestroyService]
})
export class OrderTradeValueChartComponent implements OnInit {

  @Input() brokerId = ''

  @Input() data$!: Observable<IDashboardTradeOrderTotalValue | null>;

  datasets : IDataChart[] = [
      ];


      labels: ILabelChart[] = [
            {
      label: 'MES-186',
      color: '#28cd41',
      value: 0,
      percentage: '0'
    },

       {
      label: 'MES-185',
      color: '#ff453a',
      value: 0,
      percentage: '0'
    },
      ]


      totalValue = 0;

  /**
   * Constructor
   * @param popoverService
   */
  constructor(
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
    private i18n: I18nService,
    private _destroy: DestroyService
  ) {}


  ngOnInit(): void {
    this.data$?.pipe(takeUntil(this._destroy)).subscribe(res => {
      if(!res)  return;
      const {totalBuyTransactionValue, totalSellTransactionValue, totalTransactionValue}  = res;

      this.totalValue = +totalTransactionValue;
      this.datasets = [
            {
      label: 'totalValue',
      data: [+totalSellTransactionValue, +totalBuyTransactionValue],
      backgroundColor: ['#ff453a', '#28cd41', ],
          hoverBackgroundColor: ['#ff453a', '#28cd41', ],
          borderWidth: 0,
        },
      ]

      const total = +totalBuyTransactionValue + +totalSellTransactionValue;


      this.labels = [
            {
      label: 'MES-186',
      color: '#28cd41',
      value: +totalBuyTransactionValue,
      percentage: calculatorPercent(+totalBuyTransactionValue, +total)
    },
       {
      label: 'MES-185',
      color: '#ff453a',
      value: +totalSellTransactionValue,
      percentage: calculatorPercent(+totalSellTransactionValue, +total)
    },
      ]
    })
  }


}
