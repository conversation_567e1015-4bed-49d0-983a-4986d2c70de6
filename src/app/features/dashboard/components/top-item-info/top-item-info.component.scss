:host{
  display: flex;
  flex-direction: column;
  padding: 16px;
  height: 100%;
}

.header_cls{
  display: flex;
  gap: 8px;
  align-items: center;

  color: var(--color--text--subdued)
}

.content{
  display: flex;
  flex-direction: column;
  flex: 1;
  .item-cls{
    margin-top: 12px;
    flex: 1;
    border-bottom: 1px solid var(--color--other--divider);
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: center;


    .value-line{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .info-lien{
      display: flex;
      justify-content: space-between;
      color: var(--color--text--subdued)
    }


    &:last-child{
      border-bottom: none;
    }
  }

}
