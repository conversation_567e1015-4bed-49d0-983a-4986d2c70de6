import {
  Component,
  Input,

  OnInit,

  ViewContainerRef,
} from '@angular/core';
import { Overlay,  } from '@angular/cdk/overlay';
import { DestroyService, I18nService } from 'src/app/core/services';
import { Observable, takeUntil } from 'rxjs';
import { IDashboardTradeOrder } from '../../models/dashboard';

export interface ITopItemInfo{
  label: string;
  value: number;
}

const defaultItem :ITopItemInfo = {
  label: '-',
  value: 0,
}

/**
 * DoughnutChartComponent
 */
@Component({
  selector: 'app-top-item-info-component',
  templateUrl: './top-item-info.component.html',
  styleUrl: './top-item-info.component.scss',
  providers: [DestroyService]
})
export class TopItemInfoComponent implements OnInit {
@Input() labelHeader = 'MES-681'

@Input() iconHeader = './assets/icons/arrow-bottom-left.svg';

@Input() color = '#28cd41';

@Input() data$!: Observable<IDashboardTradeOrder[]>;

items : ITopItemInfo[] = [
  // {label: 'SHS : HNX', value: '500.000.000VND', shortedLabel: 'GTGD'},
  // {label: 'SHS : HNX', value: '500.000.000VND', shortedLabel: 'GTGD'},
  // {label: 'SHS : HNX', value: '500.000.000VND', shortedLabel: 'GTGD'}
]



  /**
   * Constructor
   * @param popoverService
   */
  constructor(
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
    private i18n: I18nService,
    private _destroy: DestroyService,
  ) {}

  ngOnInit(): void {
    this.data$?.pipe(takeUntil(this._destroy)).subscribe(res => {
      for(let i = 0; i < 3; i ++){
        if(res[i])  this.items[i] = {label: res[i].stockCode, value: res[i].tradeValue};
        else this.items[i] = defaultItem;
      }
    })
  }


}
