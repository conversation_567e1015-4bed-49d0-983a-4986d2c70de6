:host{
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
  gap: 24px;
}

.content_cls{
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .item_cls{
    border-radius: 8px;
    background-color: var(--color--neutral--50);
    display: flex;
    flex: 1;

    .value_text{
      cursor: pointer;
    }

    div{
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &:hover{
      background-color: var(--color--brand--50);
      cursor: pointer;
    }
  }
}
