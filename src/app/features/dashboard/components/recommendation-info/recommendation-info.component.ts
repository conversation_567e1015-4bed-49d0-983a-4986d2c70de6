import {
  Component,
  Input,
  OnInit,
  ViewContainerRef,
} from '@angular/core';
import { ArcElement, Chart, DoughnutController, Legend, Title, Tooltip } from 'chart.js';
import { Overlay,  } from '@angular/cdk/overlay';
import { DestroyService, I18nService } from 'src/app/core/services';
import { IDashboardRecommendation, } from '../../models/dashboard';
import { Observable, takeUntil } from 'rxjs';

Chart.register(
  ArcElement,
  DoughnutController,
  Tooltip,
  Legend,
  Title,
);

/**
 * DoughnutChartComponent
 */
@Component({
  selector: 'app-recommendation-info-chart-component',
  templateUrl: './recommendation-info.component.html',
  styleUrl: './recommendation-info.component.scss',
  providers: [DestroyService]
})
export class RecommendationInfoChartComponent implements OnInit {
  @Input() brokerId = '';

  @Input() data$!: Observable<IDashboardRecommendation | null>;


  totalAmount  = 0;

  totalAmountFollowing = 0;

  totalAmountOpen = 0;

  /**
   * Constructor
   * @param popoverService
   */
  constructor(
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
    private i18n: I18nService,
            private readonly _destroy: DestroyService,

  ) {}

  ngOnInit(): void {
    this.data$.pipe(takeUntil(this._destroy)).subscribe(res => {
      if(!res)  return;
      const {totalAmount, totalAmountFollowing, totalAmountOpen} = res;
      this.totalAmount = totalAmount;
      this.totalAmountFollowing = totalAmountFollowing;
      this.totalAmountOpen = totalAmountOpen;
    })
  }



}
