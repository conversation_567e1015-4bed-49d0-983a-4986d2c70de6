import {
  Component,
  Input,
  OnInit,
  ViewContainerRef,
} from '@angular/core';
import { ArcElement, Chart, DoughnutController, Legend, Title, Tooltip } from 'chart.js';
import { Overlay,  } from '@angular/cdk/overlay';
import { DestroyService, I18nService } from 'src/app/core/services';
import { Observable, takeUntil } from 'rxjs';
import { IDashboardTradeOrderCount, IDataChart, ILabelChart } from '../../models/dashboard';
import { calculatorPercent } from '../../helpers/helpers';

Chart.register(
  ArcElement,
  DoughnutController,
  Tooltip,
  Legend,
  Title,
);

/**
 * DoughnutChartComponent
 */
@Component({
  selector: 'app-order-trade-customer-chart-component',
  templateUrl: './order-trade-customer-chart.component.html',
  styleUrl: './order-trade-customer-chart.component.scss',
  providers: [DestroyService]
})
export class OrderTradeCustomerChartComponent implements OnInit {
  @Input() brokerId = '';

    @Input() data$!: Observable<IDashboardTradeOrderCount | null>;

  datasets : IDataChart[] = [
      ];

            labels: ILabelChart[] = [
                  {
            label: 'MES-186',
            color: '#28cd41',
            value: 0,
            percentage: '0'
          },

             {
            label: 'MES-185',
            color: '#ff453a',
            value: 0,
            percentage: '0'
          },
            ]


      totalCountCustomerUnconfirmedOrder = 0;

      unconfirmedCount = 0;


  /**
   * Constructor
   * @param popoverService
   */
  constructor(
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
    private i18n: I18nService,
    private _destroy: DestroyService
  ) {}

  ngOnInit(): void {
    this.data$.pipe(takeUntil(this._destroy)).subscribe(res => {
      if(!res)  return;

      const {unconfirmedCount, unconfirmedBuyCount, unconfirmedSellCount, totalCountCustomerUnconfirmedOrder, matchedCount} = res;
      this.totalCountCustomerUnconfirmedOrder = totalCountCustomerUnconfirmedOrder;
      this.unconfirmedCount = unconfirmedCount;

      this.datasets = [
    {
      label: 'countCustomer',
      data: [unconfirmedSellCount, unconfirmedBuyCount],
      backgroundColor: ['#ff453a', '#28cd41', ],
          hoverBackgroundColor: ['#ff453a', '#28cd41', ],
          borderWidth: 0,
        },
      ];

            this.labels = [
                  {
            label: 'MES-186',
            color: '#28cd41',
            value: unconfirmedBuyCount,
            percentage: calculatorPercent(unconfirmedBuyCount, unconfirmedCount)
          },
             {
            label: 'MES-185',
            color: '#ff453a',
            value: unconfirmedSellCount,
            percentage: calculatorPercent(unconfirmedSellCount, unconfirmedCount)
          },
            ]
    })

  }



}
