:host{
  display: flex;
  padding: 16px;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  border-radius: 8px;
  height: 100%;

}


.total_value{
  display: flex;
  width: 100%;

  .side{
    flex: 1;
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }


}



  .header_cls{
  display: flex;
  gap: 12px;
  align-items: center;
  color: var(--color--text--subdued);
  justify-content: center;
  cursor: pointer;

  img{
    width: 16px;
    height: 16px;
  }
}


.trade-customer-chart{
  height: 100%;
}
