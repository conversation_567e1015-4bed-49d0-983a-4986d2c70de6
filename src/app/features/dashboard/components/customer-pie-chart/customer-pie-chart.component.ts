import {
  AfterViewInit,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  ViewContainerRef,
} from '@angular/core';
import { ArcElement, Chart, DoughnutController, Legend, Title, Tooltip } from 'chart.js';
import { Overlay,  } from '@angular/cdk/overlay';
import { DestroyService, I18nService } from 'src/app/core/services';
import { IDashboardCustomer, IDataChart, ILabelChart } from '../../models/dashboard';
import { Observable, takeUntil } from 'rxjs';
import { calculatorPercent } from '../../helpers/helpers';

Chart.register(
  ArcElement,
  DoughnutController,
  Tooltip,
  Legend,
  Title,
);

/**
 * DoughnutChartComponent
 */
@Component({
  selector: 'app-customer-pie-chart-component',
  templateUrl: './customer-pie-chart.component.html',
  styleUrl: './customer-pie-chart.component.scss',
  providers: [DestroyService]
})
export class CustomerPieChartComponent implements AfterViewInit, OnInit {
  @Input() id = 'myChart';

  @Input() data$!: Observable<IDashboardCustomer | null>;

  @Input() labels : ILabelChart[] = [
    {
      label: 'MES-26',
      color: '#5e5ce6',
      value: 0,
      percentage: '0'
    },
    {
      label: 'MES-27',
      color: '#ff9f0a',
      value: 0,
      percentage: '0'
    }
  ];

  @Input() datasets: IDataChart[] = []


  totalOrder = 0;
  chart!: any;

  /**
   * Constructor
   * @param popoverService
   */
  constructor(
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
    private i18n: I18nService,
        private _destroy: DestroyService

  ) {}

  ngOnInit(): void {
    this.data$.pipe(takeUntil(this._destroy)).subscribe(data => {
      if(!data) return;
      const { amount, individualCount, organizationCount } = data;

      this.datasets =   [{
        data: [individualCount, organizationCount],
        backgroundColor: ['#5e5ce6', '#eff0f0'],
        borderWidth: 0,
        cutout: '75%',
        label: 'label1',
        radius: '100%',
        borderRadius: 20,
      },
      {
        data: [organizationCount, individualCount],
        backgroundColor: ['#ff9f0a', '#eff0f0'],
        borderWidth: 0,
        cutout: '70%',
        label: 'label2',
        radius: '90%',
        borderRadius: 20,
      }];

      this.labels =   [{
      label: 'MES-26',
      color: '#5e5ce6',
      value: individualCount,
      percentage: calculatorPercent(individualCount, amount)
    },
       {
      label: 'MES-27',
      color: '#ff9f0a',
      value: organizationCount,
      percentage: calculatorPercent(organizationCount, amount)
    },]

    this.totalOrder = amount;


      if(this.chart){
        this.chart.data.datasets = this.datasets;
        this.chart.data.labels = this.labels;
        this.chart.update()
      }
    }
    )
  }

  /**
   * NgAfterViewInit
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.createChart();
    });
  }

  /**
   * NgOnChanges
   * @param changes
   */


  /**
   * CreateChart
   */
  createChart(): void {
    const ctx = document.getElementById(`${this.id}`) as HTMLCanvasElement;

    ctx.width = 162;
    ctx.height = 162;
    this.chart = new Chart(ctx, {
      type: 'doughnut',

      data: {
        labels: this.labels,
        datasets: this.datasets,
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: false,
            position: 'top',
          },
          title: {
            display: false,
          },
          tooltip: {
            enabled: false,
          },
          datalabels: {
            display: false,
          },
        },
        rotation: 0,
        cutout: '52%',
      },
    } as any);
  }

  /**
   * CalculatorPercent
   * @returns {any} data
   */
  calculatorPercent() {
    const value = this.datasets[0].data[0];
    const total = this.datasets[0].data[0] + this.datasets[0].data[1];
    return { hasTrans: ((value / total) * 100).toFixed(0), notHasTrans: ((1 - value / total) * 100).toFixed(0) };
  }

}
