<div class="doughnut-chart-container">
  <div class="doughnut-chart-body">
    <div class="chart">
      <div class="chart-canvas"><canvas [id]="id"></canvas></div>
      <div
       class="info-chart">
        <div class="typo-body-6">{{'MES-689' | translate }}</div>
        <div class="typo-heading-7">{{ totalOrder | numberFormat }}</div>
      </div>
    </div>
    <div class="note-chart">
      <div *ngFor="let item of labels" class="note-item">
        <div class="line">
          <div class="label">
            <div class="circle" [style.backgroundColor]="item.color"></div>
            <div class="typo-body-6">{{ item.label | translate }}</div>
          </div>
          <div class="typo-body-6">{{ item.value | numberFormat}}</div>
        </div>
        <div class="line">
          <div class="percen_label typo-body-6" [style.color]="item.color">{{ 'MES-234' | translate }}</div>
          <div class="typo-body-6" [style.color]="item.color">{{item.percentage}}%</div>
        </div>
      </div>
    </div>
  </div>
</div>
