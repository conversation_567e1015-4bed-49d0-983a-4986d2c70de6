.doughnut-chart-container {
  height: 100%;

  .doughnut-chart-body {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    height: 100%;

    .chart {
      position: relative;
      .info-chart {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;

        .typo-body-6{
          color: var(--color--text--subdued)
        }
      }
    }
    .note-chart {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .note-item{
        display: flex;
        flex-direction: column;
        min-width: 250px;

        .line{
          display: flex;
          justify-content: space-between;
          .percen_label{
            margin-left: 28px;
          }
          .label{
            display: flex;
            gap: 10px;
            align-items: center;

              .circle{
            width: 12px;
            height: 12px;
            border-radius: 100%;
        }
          }
        }
      }


    }
  }
}

:host {
  height: 100%;
  width: 100%;
}
