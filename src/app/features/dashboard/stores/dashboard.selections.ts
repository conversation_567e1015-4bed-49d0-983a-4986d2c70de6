import { createFeatureSelector, createSelector } from '@ngrx/store';
import { IDashboardState } from '../models/dashboard';

export const DASHBOARD_STATE_NAME = 'DASHBOARD';

export const selectTradesOrderState = createFeatureSelector<IDashboardState>(DASHBOARD_STATE_NAME);

export const selectTradeOrderBrokerCode$ = createSelector(selectTradesOrderState, (state) => state.brokerCode);

export const selectTradeOrderBrokerCodes$ = createSelector(selectTradesOrderState, (state) => state.brokerCodes);

export const selectTradeorderEmNo$ = createSelector(selectTradesOrderState, (state) => state.emNo);

export const selectTradeorderDate$ = createSelector(selectTradesOrderState, (state) => state.date);

export const selectTradeOrderTotalValue$ = createSelector(selectTradesOrderState, (state) => state.tradeOrderTotalValue);

export const selectTradeOrderCount$ = createSelector(selectTradesOrderState, (state) => state.tradeOrderCount);

export const selectTopBuyOrders$ = createSelector(selectTradesOrderState, (state) => state.topBuyOrders);

export const selectTopSellOrders$ = createSelector(selectTradesOrderState, (state) => state.topSellOrders);

export const selectRecommendationInfo$ = createSelector(selectTradesOrderState, (state) => state.recommendationInfo);

export const selectNavValue$ = createSelector(selectTradesOrderState, (state) => state.nav);

export const selectCustomerData$ = createSelector(selectTradesOrderState, (state) => state.customerData);


