import { createAction, props } from '@ngrx/store';
import { IDashboardCustomer, IDashboardRecommendation, IDashboardTradeOrder, IDashboardTradeOrderCount, IDashboardTradeOrderTotalValue,  } from '../models/dashboard';


export const getDashboardTradeOrderInfo = createAction('[Dashboard] get dashboard trade order info', props<{  brokerCode: string , emNo: string, brokerCodes: string[]}>());

export const getDashboardTradeOrderTotalValueSuccess = createAction('[Dashboard] get dashboard trade order total value success', props<{ data: IDashboardTradeOrderTotalValue }>());

export const getDashboardTradeOrderCountSuccess = createAction('[Dashboard] get dashboard trade order count success', props<{ data: IDashboardTradeOrderCount }>());

export const getTopBuyOrdersSuccess = createAction('[Dashboard] get dashboard top buy trade orders', props<{ data: IDashboardTradeOrder[] }>());

export const getTopSellOrdersSuccess = createAction('[Dashboard] get dashboard top sell trade orders', props<{ data: IDashboardTradeOrder[] }>());

export const getDashboardRecommendationInfoSuccess = createAction('[Dashboard] get dashboard recommendation info success', props<{ data: IDashboardRecommendation }>());

export const getDashboardNavValueSuccess = createAction('[Dashboard] get dashboard nav value success', props<{ data: number }>());

export const getDashboardCustomerSuccess = createAction('[Dashboard] get dashboard customer success', props<{ data: IDashboardCustomer }>());



