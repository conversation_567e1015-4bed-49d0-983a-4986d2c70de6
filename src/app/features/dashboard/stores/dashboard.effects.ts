import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { LoadingService, MessageService } from 'src/app/core/services';

import {
  catchError,
  finalize,
  map,
  of,
  switchMap,
  tap,
} from 'rxjs';

import { getDashboardCustomerSuccess, getDashboardNavValueSuccess, getDashboardRecommendationInfoSuccess, getDashboardTradeOrderCountSuccess, getDashboardTradeOrderInfo, getDashboardTradeOrderTotalValueSuccess, getTopBuyOrdersSuccess, getTopSellOrdersSuccess } from './dashboard.actions';
import { DashboardService } from '../services/dashboard.service';
import { IDasboardNavPayload, IDasboardRecommendationPayload, ITradeOrderCountPayload, ITradeOrderTotalValuePayload } from '../models/dashboard';
import {  selectTradeOrderBrokerCode$, selectTradeOrderBrokerCodes$, selectTradeorderDate$, selectTradeorderEmNo$ } from './dashboard.selections';
import { concatLatestFrom } from '@ngrx/operators';
import { getFirstDayOfMonth } from 'src/app/shared/utils/date';

/**
 * DashboardEffects
 */
@Injectable()
export class DashboardEffects {
  constructor(
    private readonly actions$: Actions,
    private readonly loadingService: LoadingService,
    private readonly store: Store,
    private readonly messageService: MessageService,
    private readonly dashboardService: DashboardService
  ) {}


    getTradeOrderTotalValueInfo$ = createEffect(() => {
      return this.actions$.pipe(
        ofType(getDashboardTradeOrderInfo),
        concatLatestFrom(() => [ this.store.select(selectTradeOrderBrokerCodes$)
          ,this.store.select(selectTradeorderDate$)
        ]),
        tap(() => this.loadingService.show()),
        switchMap(([_, brokerCodes, date]) => {
          const payload : ITradeOrderTotalValuePayload = {
            // date: '17/04/2025',
            date,
            brokerCodes: brokerCodes
          }
          return this.dashboardService.getDashboardTradeOrderTotalValue(payload).pipe(
            finalize(() => this.loadingService.hide()),
          );
        }),
        map((res) => {
          return getDashboardTradeOrderTotalValueSuccess({data: res});
        }),
        catchError((error) => {
          this.messageService.error(error.error.message);
          return of(error);
        })
      );
    });


        getTradeOrderCountInfo$ = createEffect(() => {
      return this.actions$.pipe(
        ofType(getDashboardTradeOrderInfo),
        concatLatestFrom(() => [ this.store.select(selectTradeorderEmNo$)
          ,this.store.select(selectTradeorderDate$)

        ]),
        tap(() => this.loadingService.show()),
        switchMap(([_, emNo, date]) => {
          const fromDate = getFirstDayOfMonth();

          const payload : ITradeOrderCountPayload = {
            fromDate, toDate: date, emNo
          }
          return this.dashboardService.getDashboardTradeOrderCount(payload).pipe(
            finalize(() => this.loadingService.hide()),
          );
        }),
        map((res) => {
          return getDashboardTradeOrderCountSuccess({data: res});
        }),
        catchError((error) => {
          this.messageService.error(error.error.message);
          return of(error);
        })
      );
    });



            getTopBuyOrder$ = createEffect(() => {
      return this.actions$.pipe(
        ofType(getDashboardTradeOrderInfo),
        concatLatestFrom(() => [ this.store.select(selectTradeOrderBrokerCodes$)
          ,this.store.select(selectTradeorderDate$)

        ]),
        tap(() => this.loadingService.show()),
        switchMap(([_, brokerCodes, date]) => {
          const payload : ITradeOrderTotalValuePayload = {
            date,
            // date: '17/04/2025',

             brokerCodes
          }
          return this.dashboardService.getTopBuyOrders(payload).pipe(
            finalize(() => this.loadingService.hide()),
          );
        }),
        map((res) => {
          return getTopBuyOrdersSuccess({data: res});
        }),
        catchError((error) => {
          this.messageService.error(error.error.message);
          return of(error);
        })
      );
    });


                getTopSellOrder$ = createEffect(() => {
      return this.actions$.pipe(
        ofType(getDashboardTradeOrderInfo),
        concatLatestFrom(() => [ this.store.select(selectTradeOrderBrokerCodes$)
          ,this.store.select(selectTradeorderDate$)

        ]),
        tap(() => this.loadingService.show()),
        switchMap(([_, brokerCodes, date]) => {
          const payload : ITradeOrderTotalValuePayload = {
            date,
            // date: '17/04/2025',

            brokerCodes
          }
          return this.dashboardService.getTopSellOrders(payload).pipe(
            finalize(() => this.loadingService.hide()),
          );
        }),
        map((res) => {
          return getTopSellOrdersSuccess({data: res});
        }),
        catchError((error) => {
          this.messageService.error(error.error.message);
          return of(error);
        })
      );
    });

    getDashboardRecommendationInfo$ = createEffect(() => {
      return this.actions$.pipe(
        ofType(getDashboardTradeOrderInfo),
        concatLatestFrom(() => [ this.store.select(selectTradeOrderBrokerCode$)
        ]),
        tap(() => this.loadingService.show()),
        switchMap(([_, brokerCode]) => {
          const payload : IDasboardRecommendationPayload = {
            // date,
            brokerCodes: [brokerCode]
          }
          return this.dashboardService.getDashboardRecommendationInfo(payload).pipe(
            finalize(() => this.loadingService.hide()),
          );
        }),
        map((res) => {
          return getDashboardRecommendationInfoSuccess({data: res});
        }),
        catchError((error) => {
          this.messageService.error(error);
          return of(error);
        })
      );
    });


        getDashboardNavValue$ = createEffect(() => {
      return this.actions$.pipe(
        ofType(getDashboardTradeOrderInfo),
        concatLatestFrom(() => [ this.store.select(selectTradeOrderBrokerCodes$)
          ,this.store.select(selectTradeorderDate$)

        ]),
        tap(() => this.loadingService.show()),
        switchMap(([_, brokerCodes, date]) => {
          const payload : IDasboardNavPayload = {
            date,
            brokerCodes
          }
          return this.dashboardService.getDashboardNavValue(payload).pipe(
            finalize(() => this.loadingService.hide()),
          );
        }),
        map((res) => {
          return getDashboardNavValueSuccess({data: +(res.totalNav ?? 0)});
        }),
        catchError((error) => {
          this.messageService.error(error);
          return of(error);
        })
      );
    });


    getDashboardCustomer$ = createEffect(() => {
      return this.actions$.pipe(
        ofType(getDashboardTradeOrderInfo),
        concatLatestFrom(() => [ this.store.select(selectTradeOrderBrokerCode$)
        ]),
        tap(() => this.loadingService.show()),
        switchMap(([_, brokerCode]) => {
          return this.dashboardService.getDashboardCustomer(brokerCode).pipe(
            finalize(() => this.loadingService.hide()),
          );
        }),
        map((res) => {
          return getDashboardCustomerSuccess({data: res});
        }),
        catchError((error) => {
          this.messageService.error(error);
          return of(error);
        })
      );
    });

}
