import { createReducer, on } from '@ngrx/store';
import * as TradesOrderAction from './dashboard.actions';
import { dateToDMY } from 'src/app/shared/utils/date';
import { IDashboardState } from '../models/dashboard';


export const initialDashboardState: IDashboardState = {
  brokerCode: '',
  brokerCodes: [],
  emNo: '',
  date: '',
  tradeOrderTotalValue: null,
  tradeOrderCount: null,
  topSellOrders: [],
  topBuyOrders: [],
  recommendationInfo: null,
  nav: 0,
  customerData: null,
};

export const dashboardReducers = createReducer<IDashboardState>(
  initialDashboardState,

      on(
      TradesOrderAction.getDashboardTradeOrderInfo,
      (state, action): IDashboardState => ({
        ...state,
        brokerCode: action.brokerCode,
        emNo: action.emNo,
        date: dateToDMY(new Date()),
        brokerCodes: action.brokerCodes
      })
    ),

    on(
      TradesOrderAction.getDashboardTradeOrderTotalValueSuccess,
      (state, action): IDashboardState => ({
        ...state,
        tradeOrderTotalValue: action.data
      })
    ),

        on(
      TradesOrderAction.getDashboardTradeOrderCountSuccess,
      (state, action): IDashboardState => ({
        ...state,
        tradeOrderCount: action.data
      })
    ),

            on(
      TradesOrderAction.getTopBuyOrdersSuccess,
      (state, action): IDashboardState => ({
        ...state,
        topBuyOrders: action.data
      })
    ),

            on(
      TradesOrderAction.getTopSellOrdersSuccess,
      (state, action): IDashboardState => ({
        ...state,
        topSellOrders: action.data
      })
    ),

            on(
      TradesOrderAction.getDashboardRecommendationInfoSuccess,
      (state, action): IDashboardState => ({
        ...state,
        recommendationInfo: action.data
      })
    ),


            on(
      TradesOrderAction.getDashboardNavValueSuccess,
      (state, action): IDashboardState => ({
        ...state,
        nav: action.data
      })
    ),


                on(
      TradesOrderAction.getDashboardCustomerSuccess,
      (state, action): IDashboardState => ({
        ...state,
        customerData: action.data
      })
    ),


);
