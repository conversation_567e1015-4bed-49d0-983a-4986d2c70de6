

export interface IDashboardState {
  brokerCode: string;
  brokerCodes: string[];
  emNo: string;
  date: string;
  tradeOrderTotalValue: IDashboardTradeOrderTotalValue | null;
  tradeOrderCount: IDashboardTradeOrderCount | null;
  topSellOrders: IDashboardTradeOrder[];
  topBuyOrders: IDashboardTradeOrder[];
  recommendationInfo: IDashboardRecommendation | null;
  nav: number;
  customerData: IDashboardCustomer | null;
}



export interface IDataChart {
  label: string;
  backgroundColor?: string | string[];
  hoverBackgroundColor?: string | string[];
  data: number[];
  borderColor?: string | string[];
  borderWidth?: number;
  maxBarThickness?: number;
  borderSkipped?: 'start' | 'end' | 'left' | 'right' | 'bottom' | 'top' | 'middle' | boolean;
  barPercentage?: number;
  categoryPercentage?: number;
  fill?: boolean;
  tension?: number;
  dataConfig?: string[];
  cutout?: string;
  radius?: string;
  borderRadius?: number;
}


export interface ILabelChart{
  label: string;
  color: string;
  value: number;
  percentage: string;
}

export interface ITradeOrderTotalValuePayload{
  date: string;
  brokerCodes: string[];
}


export interface IDashboardTradeOrderTotalValue{
  accountNumber: string;
  brokerCode: string;
totalBuyTransactionValue: number;
totalSellTransactionValue: number;
totalTransactionValue: number;
}


export interface ITradeOrderCountPayload{
  emNo: string;
  fromDate: string;
  toDate: string;
}

export interface IDashboardTradeOrderCount{
          unconfirmedCount: 2;
        unconfirmedSellCount: 0;
        unconfirmedBuyCount: 2;
        matchedCount: 0;
        totalCountCustomerUnconfirmedOrder: 1
}

export interface IDashboardTradeOrder{
  code: string;
  count: number;
  stockCode: string;
  tradeValue: number;
}

export interface IDasboardRecommendationPayload{
  brokerCodes: string[];
}

export interface IDashboardRecommendation {
          totalAmount: number;
        totalAmountOpen: number;
        totalAmountClose: number;
        totalAmountFollowing: number
}

export interface IDasboardNavPayload{
  brokerCodes: string[];
  date: string;
}

export interface IDashboardNav{
  totalNav: number;
}


export interface IDashboardCustomer{
  amount: number;
  individualCount: number;
organizationCount: number;

}
