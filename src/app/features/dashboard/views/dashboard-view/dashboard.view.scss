:host {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color--neutral--50);
}

.header {
  padding: 10px 16px 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  background-color: var(--color--neutral--white);
  border-bottom: 1px solid var(--color--other--divider);

  .left-cls {
    display: flex;
    gap: 40px;
    .date-cls {
      display: flex;
      gap: 12px;
      height: fit-content;
      align-items: center;

      img {
        width: 24px;
        height: 24px;
      }
    }
  }
}
