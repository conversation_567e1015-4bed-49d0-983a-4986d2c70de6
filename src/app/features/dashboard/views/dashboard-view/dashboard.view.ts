import { DatePipe } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationStart, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { filter, take, takeUntil } from 'rxjs';
import { DestroyService, DialogService } from 'src/app/core/services';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { getCurrentBrokerView } from 'src/app/stores/shared/shared.actions';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';


/**
 * Customer View
 */
@Component({
  selector: 'app-dashboard-view',
  templateUrl: './dashboard.view.html',
  styleUrls: ['./dashboard.view.scss'],
  providers: [DestroyService, DatePipe],
})
export class DashboardView implements OnInit, OnDestroy {

  today = new Date();

  constructor(
        private readonly _destroy: DestroyService,
        private readonly router: Router,
        private readonly dialogService: DialogService,
        private readonly store: Store,
        private readonly route: ActivatedRoute
  ) {
  }

  ngOnInit(): void {
    this.updateBrokerId()


    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationStart),
        takeUntil(this._destroy)
      )
      .subscribe((event: any) => {
        this.updateBrokerId();
      });
  }
  ngOnDestroy(): void {

  }

  updateBrokerId() {
    const queryParamsCu = this.route.snapshot.queryParams;
    if (queryParamsCu['brokerId']) {
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userListCus) => {
          if (!userListCus) return;
          const currentBroker = userListCus.find((user) => user.brokerCode === queryParamsCu['brokerId']);
          if (!currentBroker) return;
          this.store.dispatch(getCurrentBrokerView({ data: currentBroker }));
        });
    } else {
      this.store
        .select(selectCurrentBrokerView$)
        .pipe(take(1))
        .subscribe((brokerCu) => {
          this.router.navigate([], {
            queryParams: {
              ...queryParamsCu,
              brokerId: brokerCu.brokerCode,
            },
            queryParamsHandling: 'merge',
          });
        });
    }
  }


}
