import { IFieldColumnExtend, IFileRespone } from 'src/app/shared/models/global';
import { IAssetInfoResponse } from '../../assets/models/asset';
import { EAccountGrade, EAccountStatus } from '../constants/customers';

export interface IFilterPersonalInfoParam extends ICustomersParam, ICommonFilterParam {
  typeAccount: string;
  startYear: string;
  endYear: string;
}

export interface ICustomerState {
  searchValue: string | null;
  // PERSONAL
  customerPersonalData: ICustomerDataResponse[];
  filterPersonalInfo: IFilterPersonalInfoParam;
  filteredPersonalData: ICustomerDataResponse[];
  pageIndexPersonalInfo: number;

  filterBankInfo: IFilterBankInfoParam;
  filteredBankData: any[];

  filterDocumentInfo: IFilterDocumentInfoParam;
  filteredDocumentData: any[];

  filterAccountInfo: IFilterAccountInfoParam;
  filteredAccountData: any[];

  filterInterestRateInfo: IFilterInterestRateInfoParam;
  filteredInterestRateData: any[];

  // AUTHORITY
  customerAuthorityData: ICustomerAuthority[];
  filterAuthorityInfo: IFilterAuthorityInfoParam;
  filteredAuthorityData: any[];

  filterCustomerGroup: IFilterCustomerGroupParam;
  filteredCustomerGroupData: any[];

  // Customer Bank info
  customerBankData: ICustomerBank[];

  // customer group info
  customerGroupData: ICusomterGroup[];
  assetInfoByAccountNumber: IAssetInfoResponse[];

  // customer document info
  customerDocumentData: ICustomerDocument[];

  // customer account info
  customerAccountData: ICustomerAccount[];
  customerAccountDetailData: ICustomerAccount[];

  listAllBankInfo: IBankInfo[];
}

export interface IFilterBankInfoParam extends ITypeAccountParam, ICommonFilterParam {
  bankIds: string[];
}

export interface ITypeAccountParam {
  typeAccount: number[];
}

export interface ICustomersParam {
  customers: string[] | null;
}

export interface ICommonFilterParam {
  isFilter: boolean;
}

export interface ICommonKeytParam {
  [key: string]: number[] | boolean;
}

export interface IFilterDocumentInfoParam extends ITypeAccountParam, ICommonFilterParam, ICommonKeytParam {}

export interface IFilterAccountInfoParam extends ITypeAccountParam, ICommonFilterParam {
  levelAccount: string[];
  accountGroups: string[];
  rooms: string[];
}

export interface IFilterInterestRateInfoParam extends ITypeAccountParam, ICommonFilterParam {
  levelAccount: number[];
}

export interface IFilterAuthorityInfoParam extends ITypeAccountParam, ICommonFilterParam {
  authorityStatus: number[];
}

export interface IFilterCustomerGroupParam extends ICommonFilterParam {
  accountNumber: string[] | null;
  // brokenRoom: string[];
  // broker: string[];
}

export interface ICusomterGroupPayload {
  name: string;
  brokerDebtId?: string[];
  brokerCode?: string;
  members?: IMembersPayloadCreateCustomerGroup[];
}

export interface ICustomerGroupTransferPayload {
  fromId: string;
  toId: string;
  accountNos: string[];
  brokerCode: string;
}

export interface IMembersPayloadCreateCustomerGroup {
  name: string;
  accountNo: string;
}

export interface IPayloadAddCustomerInGroup {
  groupId: string;
  members: IMembersPayloadCreateCustomerGroup[];
  brokerCode: string;
}

export interface ICustomerBank {
  customerName: string;
  accountNumber: string;
  accountType: number;
  bankAccounts: IBankAccounts[];
  [key: string]: string | number | IBankAccounts[];
}

export interface IBankAccounts {
  accountNumber: string;
  customerName: string;
  beneficiaryBank: IBeneficiaryBank;
}

export interface IBeneficiaryBank {
  id: string;
  name: string;
  bankName: string;
  shortName: string;
  logo: string;
}

export interface ICusomterGroup {
  id: string;
  name: string;
  brokerDebtId: string[];
  brokerId: string[];
  customers: ICustomerForCustomerGroup[];
  isActive?: boolean;
  groupId?: string;
  members: IMembers[];
  groupCode: string;
  isShow?: boolean;
}

export interface ICusomterGroupExtends extends ICusomterGroup {
  customers: ICustomerExtends[];
}

interface ICustomerExtends extends ICustomerForCustomerGroup {
  accountNo: string;
}

export interface IMembers {
  id: string;
  name: string;
  accountNo: string;
  isActive: boolean;
}
export interface ICustomerForCustomerGroup {
  accountNumber: string;
  id?: string;
  customerName?: string;
  name?: string;
  netAsset?: number;
  totalValueCK?: number;
  total?: number;
  proportion?: number;
  totalDebt?: number;
  isCallApiShowMore?: boolean;
}

export interface ICustomerDocument extends IFieldColumnExtend {
  customerName: string;
  accountNumber: string;
  accountType: number;
  identification: IFileIndetificaion;
  registrationNo: string;
  registration: IFileRegistration;
  signature: IFileSignature;
  contracts: IFileContract[];
}

export interface IFileIndetificaion extends IFileRespone {
  identificationFiles: IIDentificationFiles[];
}

export interface IFileRegistration extends IFileRespone {
  registrationFiles: IIDentificationFiles[];
}

export interface IFileSignature extends IFileRespone {
  signatureFiles: IIDentificationFiles[];
}

export interface IFileContract extends IFileRespone {
  contractFiles: IIDentificationFiles[];
  contractType: string;
}

export interface IIDentificationFiles {
  accountNo: string;
  fileType: string;
  id: string;
  name: string;
  size: number;
  url: string;
}

export interface ICustomerAccount {
  accountNumber: string;
  customerName: string;
  customerLevel: string;
  customerGroup: string;
  brokerName: string;
  brokerCode: string;
  saleGroupName: string;
  subAccount?: string;
  accountType?: number;
  accountOpeningDate?: Date;
  accountClosingDate?: Date;
  lastTransactionDate?: Date;
  brokerId?: string;
  baseTransactionFee?: {
    value: number;
    group: string;
  };
  bondTransactionFee?: {
    value: number;
    group: string;
  };
  derivativesTransactionFee?: {
    value: number;
    group: string;
  };
  children: ICustomerAccount[];
  id?: string;
}

export interface IProportionValue {
  value: number;
  group: string;
}

export interface IPayloadTranferEachOfCustomers {
  fromId: string;
  toId: string;
  accountNos: string[];
  brokerCode: string;
}

export interface IPersonalInfoList {
  accountNumber: string;
  customerName: string;
  name: string;
  accountType: number;
  identity: string;
  identityDate: string;
  identityIssuer: string;
  nationalityId: string;
  birthday: string;
  sexId: number;
  telephone: string;
  email: string;
  address: string;
  registrationNo: string;
  registrationDate: string;
  registrationRep: string;
  signatureImg: string;
  accountLevelId: string;
  accountOpenDate: string;
  accountCloseDate: string;
  accountLasttxnDate: string;
  brokerId: string;
  brokerCode: string;
  cusGroups?: ICusGroups[];
  brokerName: string;
  customerLevel: string;
  saleGroupName: string;
  accountNo: string;
}

export interface ICusGroups {
  id: string;
  name: string;
  brokerId: string[];
  customers: string[];
}

export interface ICustomerAuthority {
  id: string;
  accountNumber: string;
  customerName: string;
  authPerson: string;
  authTelephone: string;
  authEmail: string;
  authAddress: string;
  contractNo: string;
  accountSignature: string;
  authSignature: string;
  authIdentity: string;
  authIdentityDate: string;
  authIdentityIssuer: string;
  authFromDate: string;
  authToDate: string;
  authAll: boolean;
  authCommand: boolean;
  authMoney: boolean;
  authAdvance: boolean;
  authDepository: boolean;
  authFinanceService: boolean;
  authStock: boolean;
  authStatus: string;
  contractImg: string;
  accountSignatureImg: string;
  authSignatureImg: string;
  authIdentityImg: string;
}

export interface IBankInfo {
  id: string;
  name: string;
  bankName: string;
  shortName: string;
  logo: string;
}

export interface ICustomerPersonal {
  accountNumber: string;
  customerName: string;
  accountType: number;
  identity: string;
  identityDate: string;
  identityIssuer: string;
  nationalityId: string;
  birthday: string;
  sexId: number;
  telephone: string;
  email: string;
  address: string;
  registrationNo: string;
  registrationDate: string;
  registrationRep: string;
  signatureImg: string;
  accountLevelId: string;
  accountOpenDate: string;
  accountCloseDate: string;
  accountLasttxnDate: string;
  brokerId: string;
  brokerCode: string;
  cusGroups?: ICusGroups[];
}

export interface TotalTags {
  totalAccount: number;
  [key: string]: number;
}

export interface ICustomerDataResponse {
  accountNumber: string;
  name: string;
  customerName: string;
  accountType: number;
  identity: string;
  identityDate: string;
  identifierNumber: string;
  identityIssuer: string;
  nationalityId: string;
  idNumberType: string;
  idNumberTypeName: string;
  authorizedIdNumber: string;
  birthday: string;
  sexId: number;
  telephone: string;
  email: string;
  address: string;
  registrationNo: string;
  registrationDate: string;
  registrationRep: string;
  signatureImg: string;
  accountLevelId: string;
  accountOpenDate: string;
  accountCloseDate: string;
  accountLasttxnDate: string;
  brokerId: string;
  brokerName: string;
  brokerCode: string;
  saleGroupName: string;
  cusGroups: ICusGroups[];
  customerLevel: string;
  lastSyncDate: string;
  feeBasic: {
    value: number;
    group: string;
  };
  feeDerivative: {
    value: number;
    group: string;
  };
  feeBond: {
    value: number;
    group: string;
  };
  subAccounts: ISubAccounts[];
  accountStatus: EAccountStatus;
  customerGradeName: EAccountGrade;
}

export interface ISubAccounts {
  id: string;
  subNo: string;
  status: string;
  accountNo: string;
}
