.personal-info-filter-component {
  display: flex;
  flex-direction: column;
  height: 100%;
  .personal-infor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid var(--color--other--divider);
    .img-cls {
      cursor: pointer;
    }
  }
  .type-account-header {
    padding: 12px 24px;
  }
  .personal-infor-body {
    flex: 1;
    overflow: auto;
  }
  .type-account-cls {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--color--other--divider);

    .option-list-cls {
      display: flex;
      flex-direction: column;
      .checkbox-cls {
        padding: 8px 24px;
        ::ng-deep {
          .mdc-label {
            font-size: 12px;
          }
        }
      }
    }
  }

  .personal-infor-footer {
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    border-top: 1px solid var(--color--other--divider);
    .btn {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      cursor: pointer;
      &.primary {
        color: var(--color--neutral--white);
        border: 1px solid var(--color--brand--500);
        background-color: var(--color--brand--500);
      }
    }
  }
}

:host {
  height: 100%;
  width: 100%;
}
