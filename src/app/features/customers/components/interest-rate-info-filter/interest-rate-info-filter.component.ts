import { Component, Inject } from '@angular/core';
import { IFilterInterestRateInfoParam } from '../../model/customer';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DestroyService } from 'src/app/core/services';

/**
 * PersonalInfoFilterComponent
 */
@Component({
  selector: 'app-interest-rate-info-filtercomponent',
  templateUrl: './interest-rate-info-filter.component.html',
  styleUrls: ['./interest-rate-info-filter.component.scss'],
})
export class InterestRateInfoFilterComponent {
  isIndividual = false;
  isOrganization = false;

  isNormal = false;
  isPlatinum = false;
  isGold = false;
  isDiamond = false;

  /**
   * Constructor
   * @param data IFilterDocumentInfoParam
   * @param dialogRef MatDialogRef<DocumentInfoFilterComponent>
   * @param _destroy DestroyService
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: IFilterInterestRateInfoParam,
    public dialogRef: MatDialogRef<InterestRateInfoFilterComponent>,
    private readonly _destroy: DestroyService
  ) {
    const { typeAccount, levelAccount } = data;
    typeAccount.forEach((type) => {
      if (type === 0) this.isIndividual = true;
      if (type === 1) this.isOrganization = true;
    });

    levelAccount.forEach((level) => {
      if (level === 0) this.isNormal = true;
      if (level === 1) this.isPlatinum = true;
      if (level === 2) this.isGold = true;
      if (level === 3) this.isDiamond = true;
    });
  }
  /**
   * ApplyFilter
   */
  applyFilter() {
    const accountType: number[] = [];
    if (this.isIndividual && this.isOrganization) {
      accountType.push(0, 1);
    } else if (this.isIndividual) {
      accountType.push(0);
    } else if (this.isOrganization) {
      accountType.push(1);
    }

    const levelAccount: number[] = [];
    if (this.isNormal && this.isPlatinum && this.isGold && this.isDiamond) {
      levelAccount.push(0, 1, 2, 3);
    } else if (this.isNormal) {
      levelAccount.push(0);
    } else if (this.isPlatinum) {
      levelAccount.push(1);
    } else if (this.isGold) {
      levelAccount.push(2);
    } else if (this.isDiamond) {
      levelAccount.push(3);
    }

    const optionFilter = {
      accountType,
      levelAccount,
      isFilter: !(accountType.length == 2 && levelAccount.length == 4),
    };

    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }
}
