import { Component, Inject, Input } from '@angular/core';
import { DestroyService, DialogService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { Store } from '@ngrx/store';
import { selectSHSStock$ } from 'src/app/stores/shared/shared.selectors';
import { takeUntil } from 'rxjs';
import { IAllStockList } from 'src/app/shared/models/global';
import { IOptionStock } from 'src/app/features/trade-order/models/trade-order';
import { PlaceOrderPopupComponent } from 'src/app/features/trade-order/components/place-order-popup/place-order-popup.component';
import { CustomerDetailPopupComponent } from '../customer-detail-popup/customer-detail-popup.component';

/**
 * OptionsRecommendationComponent
 */
@Component({
  selector: 'app-options-customer',
  templateUrl: './options-customer.component.html',
  styleUrls: ['./options-customer.component.scss'],
  providers: [DestroyService],
})
export class OptionsCustomerComponent {
  @Input() element: any;

  @Input() data: any;

  initialStock!: IOptionStock;

  /**
   * Constructor
   * @param popoverRef - PopoverRef
   * @param dialogService - DialogService
   * @param messageService - MessageService
   * @param store Store
   */
  constructor(
    @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly dialogService: DialogService,
    private store: Store,
    private _destroy: DestroyService
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.store
      .select(selectSHSStock$)
      .pipe(takeUntil(this._destroy))
      .subscribe((SHSstock: IAllStockList | null) => {
        if (!SHSstock) return;
        const { shortName, name, id, stock } = SHSstock;
        this.initialStock = {
          value: shortName,
          label: name,
          id: id,
          stoke: stock,
        };
      });
  }

  openCustomerInfoDetail() {
    this.popoverRef.close();
    this.dialogService.open(CustomerDetailPopupComponent, {
      width: '71vw',
      height: '94vh',
      data: {
        element: this.element,
        route: 'personal',
        data: this.data,
      },
    });
  }

  openPlaceOrderDialog() {
    this.popoverRef.close();
    const { customerName, accountNumber } = this.element;
    const infoCustomer = {
      accountNumber: accountNumber.split('-')[0].trim(),
      customerName,
    } as any;
    this.dialogService.openRightDialog(PlaceOrderPopupComponent, {
      width: '1200px',
      panelClass: 'overlay-place-order',
      data: {
        title: 'MES-651',
        initialStock: this.initialStock,
        isDontResetPage: true,
        infoCustomer,
      },
    });
  }
}
