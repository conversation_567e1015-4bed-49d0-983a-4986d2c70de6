.setting-recommend-container {
  padding: 8px 0;
  border-radius: 6px;
  display: flex;
  flex-direction: column;

  .option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 13px;
    cursor: pointer;

    .red {
      color: var(--color--danger--600);
    }

    img {
      width: 18px;
      height: 18px;
    }

    &:hover {
      background: var(--color--background--hover);
    }
  }
}

.opacity-50 {
  opacity: 0.5;
  pointer-events: none;
}

::ng-deep {
  .popover {
    padding: 0 !important;
    width: 184px;
  }
}
