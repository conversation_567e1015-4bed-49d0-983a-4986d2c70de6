import { Component, Inject, OnInit } from '@angular/core';
import {
  CONVERT_CUSTOMER_GROUP_TO_LABLE,
  CONVERT_CUSTOMER_LEVEL_TO_LABLE,
  ETypeAccount,
} from '../../constants/customers';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AccountInfoCustomerContainer } from '../../containers/account-info/account-info.container';
import { DestroyService } from 'src/app/core/services';
import { IFilterAccountInfoParam } from '../../model/customer';
import { FormControl } from '@angular/forms';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { Store } from '@ngrx/store';
import { selectCustomerAccountInfo$ } from '../../stores/customer.selector';

interface IOptionList {
  name: string;
  id?: number;
  isSelect: boolean;
}

/**
 * AccountInfoFilterComponent
 */
@Component({
  selector: 'app-account-info-filter',
  templateUrl: './account-info-filter.component.html',
  styleUrl: './account-info-filter.component.scss',
})
export class AccountInfoFilterComponent implements OnInit {
  accountLevels: string[] = [];
  accountGroups: { name: string; isSelect: boolean }[] = [];

  isIndividual!: boolean;
  isOrganization!: boolean;

  listCustomerLevelOptions!: IOptionList[];
  listAccountGroupOptions!: IOptionList[];
  listRoomOptions!: IOptionList[];
  private _customerLevelOptions!: IOptionList[];
  private _groupOptions!: IOptionList[];
  private _roomOptions!: IOptionList[];

  listAccountGroup!: string[];
  listAccountLevel!: string[];
  listAccountRoom!: string[];

  CONVERT_CUSTOMER_GROUP_TO_LABLE = CONVERT_CUSTOMER_GROUP_TO_LABLE;
  CONVERT_CUSTOMER_LEVEL_TO_LABLE = CONVERT_CUSTOMER_LEVEL_TO_LABLE;

  accountGroupControl = new FormControl();
  roomControl = new FormControl();

  /**
   * Constructor
   * @param data IFilterDocumentInfoParam
   * @param dialogRef MatDialogRef<DocumentInfoFilterComponent>
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: IFilterAccountInfoParam,
    public dialogRef: MatDialogRef<AccountInfoCustomerContainer>,
    private readonly _destroy: DestroyService,
    private readonly store: Store
  ) {
    const { typeAccount } = data;
    typeAccount.forEach((type) => {
      if (type === ETypeAccount.INDIVIDUAL) this.isIndividual = true;
      else if (type === ETypeAccount.ORGANIZATION) this.isOrganization = true;
    });
  }

  /**
   * NgOninit
   */
  ngOnInit(): void {
    this.accountGroupControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listAccountGroupOptions = this._filter(value ?? '', this._groupOptions);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.roomControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listRoomOptions = this._filter(value ?? '', this._roomOptions);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.getListOption();

    // Take data to fill selection
    this.fillSelection(this.data.levelAccount, this.listCustomerLevelOptions);
    this.fillSelection(this.data.accountGroups, this.listAccountGroupOptions);
    this.fillSelection(this.data.rooms, this.listRoomOptions);
  }

  /**
   *
   * @param checked
   * @param item
   * @param item.name
   * @param item.isSelect
   */
  changeSections(checked: boolean, item: IOptionList) {
    item.isSelect = checked;
  }

  getListOption() {
    this.store
      .select(selectCustomerAccountInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((account) => {
        // Hạng tài khoản
        this.listAccountLevel = Array.from(
          new Set(
            account
              .filter((item) => item.customerLevel)
              .flatMap((item) => item.customerLevel?.split(','))
              .map((str) => str.trim())
          )
        );
        this._customerLevelOptions = this.listAccountLevel.map((customerLevel) => ({
          name: customerLevel,
          isSelect: true,
        }));
        this.listCustomerLevelOptions = this._customerLevelOptions;

        // Nhóm tài khoản
        this.listAccountGroup = Array.from(
          new Set(
            account
              .filter((item) => item.customerGroup)
              .flatMap((item) => item.customerGroup?.split(','))
              .map((str) => str.trim())
          )
        );

        this._groupOptions = this.listAccountGroup.map((group) => ({
          name: group,
          isSelect: true,
        }));
        this.listAccountGroupOptions = this._groupOptions;

        // Phòng
        this.listAccountRoom = Array.from(
          new Set(account.flatMap((item) => item.saleGroupName?.split(',')).map((str) => str.trim()))
        );
        this._roomOptions = this.listAccountRoom.map((room) => ({
          name: room,
          isSelect: true,
        }));
        this.listRoomOptions = this._roomOptions;
      });
  }

  /**
   * fillSelection
   * @param data
   * @param listOption
   */
  fillSelection(data: string[], listOption: IOptionList[]) {
    if (data.length > 0) {
      listOption.forEach((r) => {
        const itemSelected = data.includes(r.name);
        r.isSelect = itemSelected;
        return r;
      });
    }
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    const accountType: number[] = [];
    if (this.isIndividual && this.isOrganization) accountType.push(ETypeAccount.INDIVIDUAL, ETypeAccount.ORGANIZATION);
    else if (this.isIndividual) accountType.push(ETypeAccount.INDIVIDUAL);
    else if (this.isOrganization) accountType.push(ETypeAccount.ORGANIZATION);

    const levelAccount: string[] = this.listCustomerLevelOptions
      .filter((level) => level.isSelect)
      .map((level) => level.name);

    const accountGroups: string[] = this.listAccountGroupOptions
      .filter((group) => group.isSelect)
      .map((group) => group.name);

    const rooms: string[] = this.listRoomOptions.filter((room) => room.isSelect).map((room) => room.name);

    const optionFilter = {
      accountType,
      levelAccount,
      accountGroups,
      rooms,
      isFilter: !(
        accountType.length == 2 &&
        levelAccount.length == 4 &&
        accountGroups.length == 3 &&
        rooms.length == 5
      ),
    };

    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }
  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * Inner filter function
   * @param {string} value search value
   * @param {IOptionList[]} options
   */
  private _filter(value: string, options: IOptionList[]): IOptionList[] {
    const filterValue = value.toLowerCase();

    return options.filter((option) => option.name?.toLowerCase().includes(filterValue));
  }
}
