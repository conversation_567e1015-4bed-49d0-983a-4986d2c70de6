.account-info-filter-component {
  display: flex;
  flex-direction: column;
  height: 100%;

  .account-infor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid var(--color--other--divider);

    .img-cls {
      cursor: pointer;
    }
  }

  .type-account-cls {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--color--other--divider);
  }

  .account-infor-filter-body {
    overflow: auto;
    flex: 1;

    .type-account-cls,
    .level-account-cls,
    .group-account-cls,
    .room-account-cls {
      display: flex;
      flex-direction: column;
      border-bottom: 1px solid var(--color--other--divider);

      .type-account-header,
      .level-account-header,
      .group-account-header,
      .room-account-header {
        padding: 12px 24px;
      }

      .option-list-cls {
        display: flex;
        flex-direction: column;

        .checkbox-cls {
          padding: 8px 0;

          ::ng-deep {
            .mdc-label {
              font-size: 12px;
            }
          }
        }
      }
    }

    .search-cls {
      display: flex;
      align-items: center;
      position: relative;
      margin-bottom: 6px;
      margin: 0 24px;

      .search-icon {
        position: absolute;
        top: 50%;
        left: 10px;
        transform: translateY(-50%);
      }
    }

    .input-cls {
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
      padding: 10px 16px;
      padding-left: 34px;
      min-width: 350px;
    }

    .mb-17 {
      margin-bottom: 17px;
    }
  }

  .account-infor-footer {
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    border-top: 1px solid var(--color--other--divider);

    .btn {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      cursor: pointer;

      &.primary {
        color: var(--color--neutral--white);
        border: 1px solid var(--color--brand--500);
        background-color: var(--color--brand--500);
      }
    }
  }
}

.option-list-cls {
  display: flex;
  flex-direction: column;
  padding: 0 24px;

  .checkbox-cls {
    padding: 8px 0;
  }
}

:host {
  height: 100%;
  width: 100%;
}

.uppercase {
  text-transform: uppercase;
}
