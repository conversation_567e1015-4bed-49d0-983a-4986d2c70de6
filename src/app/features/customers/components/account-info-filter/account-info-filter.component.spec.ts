import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { AccountInfoFilterComponent } from './account-info-filter.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DestroyService } from 'src/app/core/services';
import {  ReactiveFormsModule } from '@angular/forms';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { selectCustomerAccountInfo$ } from '../../stores/customer.selector';
import { ETypeAccount } from '../../constants/customers';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ICustomerAccount } from '../../model/customer';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { of } from 'rxjs';

// Custom TranslateLoader for tests
export class MockTranslateLoader implements TranslateLoader {
  getTranslation(lang: string) {
    return of({
      'MES-14': 'Search',
      'MES-19': 'Filter',
      'MES-21': 'Apply',
      'MES-25': 'Account Type',
      'MES-26': 'Individual',
      'MES-27': 'Organization',
      'MES-32': 'Default',
      'MES-36': 'Account Level',
      'MES-37': 'Account Group',
      'MES-38': 'Room'
    });
  }
}

describe('AccountInfoFilterComponent', () => {
  let component: AccountInfoFilterComponent;
  let fixture: ComponentFixture<AccountInfoFilterComponent>;
  let dialogRefSpy: jasmine.SpyObj<MatDialogRef<any>>;
  let store: MockStore;

  // Define mock data
  const mockFilterData = {
    typeAccount: [ETypeAccount.INDIVIDUAL],
    levelAccount: ['Gold'],
    accountGroups: ['Group1'],
    rooms: ['Room1'],
    isFilter: true
  };

  const mockAccountData: Partial<ICustomerAccount>[] = [
    {
      customerLevel: 'Gold, Silver',
      customerGroup: 'Group1, Group2',
      saleGroupName: 'Room1, Room2',
      accountNumber: '001',
      customerName: 'Customer 1',
      brokerName: 'Broker 1',
      brokerCode: 'B001',
      children: []
    },
    {
      customerLevel: 'Platinum, Diamond',
      customerGroup: 'Group3',
      saleGroupName: 'Room3',
      accountNumber: '002',
      customerName: 'Customer 2',
      brokerName: 'Broker 2',
      brokerCode: 'B002',
      children: []
    }
  ];

  beforeEach(async () => {
    dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      declarations: [AccountInfoFilterComponent],
      imports: [
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        BrowserAnimationsModule,
        TranslateModule.forRoot({
          loader: { provide: TranslateLoader, useClass: MockTranslateLoader }
        })
      ],
      providers: [
        DestroyService,
        provideMockStore(),
        { provide: MAT_DIALOG_DATA, useValue: mockFilterData },
        { provide: MatDialogRef, useValue: dialogRefSpy }
      ]
    }).compileComponents();

    store = TestBed.inject(MockStore);
    store.overrideSelector(selectCustomerAccountInfo$, mockAccountData as ICustomerAccount[]);

    fixture = TestBed.createComponent(AccountInfoFilterComponent);
    component = fixture.componentInstance;
  });

  afterEach(() => {
    store.resetSelectors();
  });

  // No.1: should create component successfully with dependencies injected
  it('should create component successfully with dependencies injected', () => {
    // Arrange - Component is created in beforeEach

    // Act
    fixture.detectChanges();

    // Assert
    expect(component).toBeTruthy();
  });

  // No.2: should properly initialize filter data from input params
  it('should properly initialize filter data from input params', () => {
    // Arrange

    // Act
    fixture.detectChanges();

    // Assert
    expect(component.isIndividual).toBeTrue();
    expect(component.isOrganization).toBeFalsy();
  });

  // No.3: should properly initialize subscriptions and load options on init
  it('should properly initialize subscriptions and load options on init', () => {
    // Arrange
    const getListOptionSpy = spyOn(component, 'getListOption').and.callThrough();
    const fillSelectionSpy = spyOn(component, 'fillSelection').and.callThrough();

    // Act
    fixture.detectChanges();

    // Assert
    expect(getListOptionSpy).toHaveBeenCalledTimes(1);
    expect(fillSelectionSpy).toHaveBeenCalledTimes(3);
  });

  // No.4: should filter account group options based on search term
  it('should filter account group options based on search term', fakeAsync(() => {
    // Arrange
    fixture.detectChanges();
    component['_groupOptions'] = [
      { name: 'Group1', isSelect: true },
      { name: 'Group2', isSelect: true },
      { name: 'Other', isSelect: true }
    ];

    // Act
    component.accountGroupControl.setValue('Group');
    tick(300); // Wait for debounce time

    // Assert
    expect(component.listAccountGroupOptions.length).toBe(2);
    expect(component.listAccountGroupOptions[0].name).toBe('Group1');
    expect(component.listAccountGroupOptions[1].name).toBe('Group2');
  }));

  // No.5: should filter room options based on search term
  it('should filter room options based on search term', fakeAsync(() => {
    // Arrange
    fixture.detectChanges();
    component['_roomOptions'] = [
      { name: 'Room1', isSelect: true },
      { name: 'Room2', isSelect: true },
      { name: 'Area51', isSelect: true }
    ];

    // Act
    component.roomControl.setValue('Room');
    tick(300); // Wait for debounce time

    // Assert
    expect(component.listRoomOptions.length).toBe(2);
    expect(component.listRoomOptions[0].name).toBe('Room1');
    expect(component.listRoomOptions[1].name).toBe('Room2');
  }));

  // No.6: should change selection state when user selects/deselects an option
  it('should change selection state when user selects/deselects an option', () => {
    // Arrange
    const option = { name: 'Test', isSelect: false };

    // Act
    component.changeSections(true, option);

    // Assert
    expect(option.isSelect).toBeTrue();

    // Act again to toggle
    component.changeSections(false, option);

    // Assert again
    expect(option.isSelect).toBeFalse();
  });

  // No.7: should load and process options from store data
  it('should load and process options from store data', () => {
    // Arrange
    fixture.detectChanges();

    // Act
    component.getListOption();

    // Assert
    expect(component.listAccountLevel.length).toBe(4);
    expect(component.listAccountLevel).toContain('Gold');
    expect(component.listAccountLevel).toContain('Silver');
    expect(component.listAccountLevel).toContain('Platinum');
    expect(component.listAccountLevel).toContain('Diamond');

    expect(component.listAccountGroup.length).toBe(3);
    expect(component.listAccountGroup).toContain('Group1');
    expect(component.listAccountGroup).toContain('Group2');
    expect(component.listAccountGroup).toContain('Group3');

    expect(component.listAccountRoom.length).toBe(3);
    expect(component.listAccountRoom).toContain('Room1');
    expect(component.listAccountRoom).toContain('Room2');
    expect(component.listAccountRoom).toContain('Room3');
  });

  // No.8: should fill selections based on provided filter data
  it('should fill selections based on provided filter data', () => {
    // Arrange
    const options = [
      { name: 'Option1', isSelect: true },
      { name: 'Option2', isSelect: true },
      { name: 'Option3', isSelect: true }
    ];
    const filterData = ['Option1', 'Option3'];

    // Act
    component.fillSelection(filterData, options);

    // Assert
    expect(options[0].isSelect).toBeTrue();
    expect(options[1].isSelect).toBeFalse();
    expect(options[2].isSelect).toBeTrue();
  });

  // No.9: should apply filters with correct values and close dialog
  it('should apply filters with correct values and close dialog', () => {
    // Arrange
    fixture.detectChanges();
    component.isIndividual = true;
    component.isOrganization = true;
    component.listCustomerLevelOptions = [
      { name: 'Gold', isSelect: true },
      { name: 'Silver', isSelect: false }
    ];
    component.listAccountGroupOptions = [
      { name: 'Group1', isSelect: true },
      { name: 'Group2', isSelect: false }
    ];
    component.listRoomOptions = [
      { name: 'Room1', isSelect: true },
      { name: 'Room2', isSelect: false }
    ];

    // Act
    component.applyFilter();

    // Assert
    expect(dialogRefSpy.close).toHaveBeenCalledWith({
      type: 'save',
      optionFilter: {
        accountType: [ETypeAccount.INDIVIDUAL, ETypeAccount.ORGANIZATION],
        levelAccount: ['Gold'],
        accountGroups: ['Group1'],
        rooms: ['Room1'],
        isFilter: true
      }
    });
  });

  // No.10: should reset to default filters when requested
  it('should reset to default filters when requested', () => {
    // Arrange
    fixture.detectChanges();

    // Act
    component.defaultFilter();

    // Assert
    expect(dialogRefSpy.close).toHaveBeenCalledWith({
      type: 'default'
    });
  });
});
