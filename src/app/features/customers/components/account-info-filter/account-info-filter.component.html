<div class="account-info-filter-component">
  <div class="account-infor-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>
  <div class="account-infor-filter-body">
    <!-- Loại tài khoản -->
    <div class="type-account-cls">
      <div class="type-account-header typo-body-15">{{ 'MES-25' | translate }}</div>
      <div class="option-list-cls">
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isIndividual = $event.checked" [checked]="isIndividual" class="checkbox-cls">{{
            'MES-26' | translate
          }}</mat-checkbox>
        </div>
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isOrganization = $event.checked" [checked]="isOrganization" class="checkbox-cls">{{
            'MES-27' | translate
          }}</mat-checkbox>
        </div>
      </div>
    </div>

    <!-- Hạng tài khoản -->
    <div class="level-account-cls">
      <div class="level-account-header typo-body-15">{{ 'MES-36' | translate }}</div>
      <div class="option-list-cls">
        @for (item of listCustomerLevelOptions; track item.name) {
        <div class="checkbox-cls-item">
          <mat-checkbox
            (change)="changeSections($event.checked, item)"
            [checked]="item.isSelect"
            class="checkbox-cls"
            >{{ CONVERT_CUSTOMER_LEVEL_TO_LABLE[item.name] }}</mat-checkbox
          >
        </div>
        }
      </div>
    </div>

    <!-- Nhóm tài khoản -->
    <div class="group-account-cls">
      <div class="group-account-header typo-body-15">{{ 'MES-37' | translate }}</div>
      <div class="search-cls mb-17">
        <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
        <input
          class="input-cls input-style-common"
          type="text"
          [formControl]="accountGroupControl"
          [placeholder]="'MES-14' | translate"
        />
      </div>
      <div class="option-list-cls">
        @for (item of listAccountGroupOptions; track item.name) {
        <div class="checkbox-cls-item">
          <mat-checkbox
            (change)="changeSections($event.checked, item)"
            [checked]="item.isSelect"
            class="checkbox-cls"
            >{{ item.name }}</mat-checkbox
          >
        </div>
        }
      </div>
    </div>

    <!-- Phòng -->
    <div class="room-account-cls">
      <div class="room-account-header typo-body-15">{{ 'MES-38' | translate }}</div>
      <div class="search-cls mb-17">
        <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
        <input
          class="input-cls input-style-common"
          type="text"
          [placeholder]="'MES-14' | translate"
          [formControl]="roomControl"
        />
      </div>
      <div class="option-list-cls">
        @for (item of listRoomOptions; track item) {
        <div class="checkbox-cls-item">
          <mat-checkbox
            (change)="changeSections($event.checked, item)"
            [checked]="item.isSelect"
            class="checkbox-cls"
            >{{ item.name }}</mat-checkbox
          >
        </div>
        }
      </div>
    </div>
  </div>

  <div class="account-infor-footer">
    <div (click)="defaultFilter()" class="btn typo-button-3">{{ 'MES-32' | translate }}</div>
    <div (click)="applyFilter()" class="btn primary typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
