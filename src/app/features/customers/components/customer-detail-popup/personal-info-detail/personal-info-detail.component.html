<div class="personal-detail-wrapper">
  <!-- THÔNG TIN CÁ NHÂN -->
  <form [formGroup]="personalInfoForm" class="personal-info">
    <div class="title-header">
      <div class="typo-body-19 title">{{ 'MES-339' | translate }}</div>
      <!-- Button chỉnh sửa
      @if(!editMode) {
      <div class="edit-btn-wrapper" (click)="toggleEditMode()">
        <mat-icon class="edit-btn" [svgIcon]="'icon:edit-2'"></mat-icon>
        <div class="edit-content-btn typo-body-9">{{ 'MES-340' | translate }}</div>
      </div>
      } @else {
      Button Lưu/Huỷ
      <div class="edit-btn-wrapper-save">
        <div class="button save typo-body-9" (click)="save()">
          Lưu
          <mat-icon [svgIcon]="'icon:tick-circle-icon'"></mat-icon>
        </div>
        <div class="button cancel typo-body-9" (click)="cancel()">
          Huỷ
          <mat-icon [svgIcon]="'icon:x-cross-red-icon'"></mat-icon>
        </div>
      </div>
      } -->
    </div>

    <div class="content">
      <div class="row">
        <!-- Họ tên -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-348' | translate }}</div>
          @if(editMode) {
          <app-input type="input" formControlName="customerName"></app-input>
          } @else {
          <div class="info typo-body-23">{{ personalInfoForm.get('customerName')?.value }}</div>
          }
        </div>

        <!-- Loại tài khoản -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-25' | translate }}</div>
          @if(editMode) {
          <app-input type="input" formControlName="accountType" [ngClass]="'disable'" [readonly]="true"></app-input>
          } @else {
          <div class="info typo-body-23">{{ personalInfoForm.get('accountType')?.value ?? '-' }}</div>
          }
        </div>
      </div>

      <div class="row">
        <!-- Ngày sinh -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-350' | translate }}</div>
          @if(editMode) {
          <app-input type="calendar" formControlName="birthday"></app-input>
          } @else {
          <div class="info typo-body-23">
            {{
              personalInfoForm.get('birthday')?.value
                ? (personalInfoForm.get('birthday')?.value | date : 'dd/MM/yyyy')
                : '-'
            }}
          </div>
          }
        </div>

        <!-- Số điện thoại -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-349' | translate }}</div>
          @if(editMode) {
          <div class="input-wrapper">
            <app-phone-number-table-component
              [data]="personalInfoForm.get('areaCode')?.value"
              [isEdit]="editMode"
              [element]="data"
              [tag]="'areaCode'"
              (dateChangeEvent)="changeDateEvent($event)"
              [isShowArrowDown]="true"
            ></app-phone-number-table-component>
          </div>
          } @else {
          <!-- <div class="info phone-number typo-body-23">
            <img class="icon" src="./assets/icons/phone.svg" alt="" />
            {{ personalInfoForm.get('areaCode')?.value ?? '-' }}
          </div> -->

          <div class="info phone-number typo-body-23">
            {{ personalInfoForm.get('areaCode')?.value ?? '-' }}
          </div>
          }
        </div>
      </div>

      <div class="row">
        <!-- Giới tính -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-351' | translate }}</div>
          @if(editMode) {
          <app-input type="dropdown" formControlName="sexId" (clickEvent)="openSearchList($event)"></app-input>
          } @else {
          <div class="info typo-body-23">{{ personalInfoForm.get('sexId')?.value ?? '-' }}</div>
          }
        </div>

        <!-- Email -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-394' | translate }}</div>
          @if(editMode) {
          <app-input type="suffix-icon" formControlName="email" [iconSuffix]="'icon:sms-subdued'"></app-input>
          <mat-error class="typo-body-9" *ngIf="getFormControl('email').invalid">{{ 'MES-102' | translate }}</mat-error>
          } @else {
          <div class="info email typo-body-23">
            <img *ngIf="personalInfoForm.get('email')?.value" class="icon" src="./assets/icons/sms.svg" alt="" />
            {{ personalInfoForm.get('email')?.value ?? '-' }}
          </div>
          }
        </div>
      </div>

      <div class="row">
        <!-- Địa chỉ chi tiết -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-469' | translate }}</div>
          @if(editMode) {
          <app-input type="input" formControlName="address"></app-input>
          } @else {
          <div class="info typo-body-23">{{ personalInfoForm.get('address')?.value ?? '-' }}</div>
          }
        </div>

        <!-- Quốc tich -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-352' | translate }}</div>
          @if(editMode) {
          <app-input type="dropdown" formControlName="nationalityId" (clickEvent)="openSearchList($event)"></app-input>
          } @else {
          <div class="info typo-body-23">
            {{ CONVERT_COUNTRY_CODE_TO_LABEL[personalInfoForm.get('nationalityId')?.value] }}
          </div>
          }
        </div>
      </div>

      <!-- <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-354' | translate }}</div>
        <div class="section">
          <div class="row">
            Quốc gia
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-353' | translate }}</div>
              @if(editMode) {
              <app-input type="dropdown" formControlName="nation" (clickEvent)="openSearchList($event)"></app-input>
              } @else {
              <div class="info typo-body-23">{{ personalInfoForm.get('nation')?.value ?? '-' }}</div>
              }
            </div>

            Tỉnh / Thành Phố
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-357' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="city"
                (clickEvent)="getFormControl('nation').value === 'Việt Nam' ? openSearchList($event) : null"
              ></app-input>
              } @else {
              <div class="info typo-body-23">{{ personalInfoForm.get('city')?.value ?? '-' }}</div>
              }
            </div>
          </div>

          <div class="row">
            Quận / Huyện
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-356' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="district"
                (clickEvent)="getFormControl('nation').value === 'Việt Nam' ? openSearchList($event) : null"
              ></app-input>
              } @else {
              <div class="info typo-body-23">{{ personalInfoForm.get('district')?.value ?? '-' }}</div>
              }
            </div>

            Xã / Phường
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-355' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="ward"
                (clickEvent)="getFormControl('nation').value === 'Việt Nam' ? openSearchList($event) : null"
              ></app-input>
              } @else {
              <div class="info typo-body-23">{{ personalInfoForm.get('ward')?.value ?? '-' }}</div>
              }
            </div>
          </div>

          <div class="row">
            Địa chỉ chi tiết
            <div class="item width-100">
              <div class="label typo-body-12">{{ 'MES-469' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="address"></app-input>
              } @else {
              <div class="info typo-body-23">{{ personalInfoForm.get('address')?.value ?? '-' }}</div>
              }
            </div>
          </div>
        </div>
      </app-section-wrapper-layout> -->

      <!-- CMND / CCCD / HC (Người đại diện PL) -->
      <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-561' | translate }}</div>
        <div class="section">
          <div class="row">
            <!-- Số CMND / CCCD / HC -->
            <div class="item">
              <div class="label typo-body-12">Số {{ 'MES-358' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="identity"></app-input>
              } @else {
              <div class="info typo-body-23">{{ personalInfoForm.get('identity')?.value ?? '-' }}</div>
              }
            </div>

            <!-- Kiểu loại-->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-359' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="idNumberType"
                (clickEvent)="openSearchList($event)"
              ></app-input>
              } @else {
              <div class="info typo-body-23">
                {{ CONVERT_ID_NO_TYPE_TO_LABEL[personalInfoForm.get('idNumberType')?.value] || '-' }}
              </div>
              }
            </div>
          </div>

          <div class="row">
            <!-- Ngày cấp -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-360' | translate }}</div>
              @if(editMode) {
              <app-input type="calendar" formControlName="identityDate"></app-input>
              } @else {
              <div class="info typo-body-23">
                {{ (personalInfoForm.get('identityDate')?.value | date : 'dd/MM/yyyy') ?? '-' }}
              </div>
              }
            </div>

            <!-- Nơi cấp -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-361' | translate }}</div>
              @if(editMode) {
              <!-- <app-input
                type="multiple-dropdown-province"
                formControlName="identityIssuer"
                (clickEvent)="openSearchList($event)"
              ></app-input> -->
              } @else {
              <!-- <div class="info typo-body-23">
                {{
                  Object.keys(personalInfoForm.get('identityIssuer')?.value.children).length
                    ? personalInfoForm.get('identityIssuer')?.value.name +
                      ' - ' +
                      (getProvinceName(personalInfoForm.get('identityIssuer')?.value.children.id) || '')
                    : personalInfoForm.get('identityIssuer')?.value.name
                }}
              </div> -->
              <div class="info typo-body-23">
                {{ personalInfoForm.get('identityIssuer')?.value ?? '-' }}
              </div>
              }
            </div>
          </div>

          <!-- <div class="row">
            Đính kèm CMND / CCCD / HC
            <div class="item item-upload-file-cls">
              <div class="label flex typo-body-15">
                <img src="./assets/icons/paperclip.svg" alt="" />
                {{ 'MES-391' | translate }} ({{ personalInfoForm.get('fileIDNumber')?.value.length }})
              </div>
              @if(editMode) {
              <app-dropzone-form
                [uploadedFiles]="personalInfoForm.get('fileIDNumber')?.value"
                (fileAdded)="addFilesToForm($event)"
                (fileRemoved)="removeFileFromForm($event)"
                formArrayName="fileIDNumber"
              ></app-dropzone-form>
              } @else {
              Nếu có file
              @if(personalInfoForm.get('fileIDNumber')?.value.length) {
              <div *ngFor="let file of personalInfoForm.get('fileIDNumber')?.value" class="info typo-body-23 file">
                <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                <div class="file-info">
                  <div class="name typo-body-12">
                    <div class="file-name">{{ file.name }}</div>
                    <div class="size typo-body-22">
                      {{ file.size }}
                    </div>
                  </div>
                </div>
                <img class="download" src="./assets/icons/download.svg" alt="" />
              </div>
              } @else {
              Nếu không có file
              <div class="info typo-body-23 file">
                <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                <div class="file-info">{{ 'MES-23' | translate }}</div>
              </div>
              } }
            </div>
          </div> -->
        </div>
      </app-section-wrapper-layout>

      <!-- THÔNG TIN TÀI KHOẢN TỔ CHỨC -->
      <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-388' | translate }}</div>
        <div class="section">
          <div class="row">
            <!-- Số đăng ký kinh doanh -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-390' | translate }}</div>
              @if(editMode) {
              <app-input
                type="input"
                [ngClass]="personalInfoForm.get('registrationNo')?.value ? '' : 'disable'"
                [readonly]="personalInfoForm.get('registrationNo')?.value ? false : true"
                formControlName="registrationNo"
              ></app-input>
              } @else {
              <div class="info typo-body-23">
                {{ personalInfoForm.get('registrationNo')?.value ?? '-' }}
              </div>
              }
            </div>

            <!-- Người đại diện pháp luật -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-562' | translate }}</div>
              @if(editMode) {
              <app-input
                type="input"
                formControlName="registrationRep"
                [ngClass]="personalInfoForm.get('registrationRep')?.value ? '' : 'disable'"
                [readonly]="personalInfoForm.get('registrationRep')?.value ? false : true"
              ></app-input>
              } @else {
              <div class="info typo-body-23">{{ personalInfoForm.get('registrationRep')?.value ?? '-' }}</div>
              }
            </div>
          </div>

          <div class="row">
            <!-- Ngày cấp ĐKKD -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-620' | translate }}</div>
              <div class="info typo-body-23">
                {{ personalInfoForm.get('dateOfBusinessRegistration')?.value ?? '-' }}
              </div>
            </div>
            <!-- Đính kèm đăng ký kinh doanh
            <div class="item item-upload-file-cls" *ngIf="personalInfoForm.get('accountType')?.value === 'Tổ chức'">
              <div class="label flex typo-body-15">
                <img src="./assets/icons/paperclip.svg" alt="" />
                {{ 'MES-392' | translate }} ({{ personalInfoForm.get('fileBusinessRegistration')?.value.length }})
              </div>
              @if(editMode) {
              <app-dropzone-form
                [uploadedFiles]="personalInfoForm.get('fileBusinessRegistration')?.value"
                (fileAdded)="addFilesToForm($event)"
                (fileRemoved)="removeFileFromForm($event)"
                formArrayName="fileBusinessRegistration"
              ></app-dropzone-form>
              } @else {
              Nếu có file
              @if(personalInfoForm.get('fileBusinessRegistration')?.value.length) {
              <div
                *ngFor="let file of personalInfoForm.get('fileBusinessRegistration')?.value"
                class="info typo-body-23 file"
              >
                <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                <div class="file-info">
                  <div class="name typo-body-12">
                    <div class="file-name">{{ file.name }}</div>
                    <div class="size typo-body-22">
                      {{ file.size }}
                    </div>
                  </div>
                </div>
                <img class="download" src="./assets/icons/download.svg" alt="" />
              </div>
              } @else {
              Nếu không có file
              <div class="info typo-body-23 file">
                <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                <div class="file-info">{{ 'MES-23' | translate }}</div>
              </div>
              } }
            </div> -->
          </div>
        </div>
      </app-section-wrapper-layout>
    </div>
  </form>
</div>
