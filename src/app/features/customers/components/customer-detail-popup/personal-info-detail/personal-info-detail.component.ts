import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { take, takeUntil } from 'rxjs';
import { DatePickerNavigationFullDateComponent } from 'src/app/shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { MY_DATE_FORMAT } from 'src/app/shared/constants/date-picker';
import {
  CONVERT_GENDER_TO_LABLE,
  CONVERT_ID_NO_TYPE_TO_LABEL,
  CONVERT_LABEL_GENDER_TO_NUMBER,
  CONVERT_TYPE_ACCOUNT_TO_LABLE,
  ListOfTypeID,
  ListOptionGender,
} from '../../../constants/customers';
import { LIST_OF_NATIONALITY } from 'src/app/shared/constants/nationality';
import { NationalityDropdownComponent } from 'src/app/shared/components/nationality-dropdown/nationality-dropdown.component';
import { LIST_PROVINCE } from 'src/app/shared/constants/province';
import { DropdownMultipleLevelV2Component } from 'src/app/shared/components/dropdown-multiple-level/dropdown-multiple-level-v2/dropdown-multiple-level-v2.component';
import { DestroyService, DialogService } from 'src/app/core/services';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { LocationService } from 'src/app/core/services/location.service';

export interface PopoverConfig {
  multiple: boolean;
  searchKey?: string;
  isSearch?: boolean;
  isShowLogo?: boolean;
  isShowAreaCode?: boolean;
  displayOptionFn?: (v: any) => string;
  options?: any[]; // Adjust the type according to your options' structure
  displayValueFn?: any;
  key?: string;
  isShowSearch?: boolean;
  isUpdateList?: boolean;
  configChildren?: any;
  initialOptions?: any;
  value?: any;
  placeholder?: string | null;
  isShowBotBtn?: boolean;
  parentList?: any;
  isShowBtn?: boolean;
  filterKey?: string;
  type?: string;
  initialValue?: any;
  beneficiaryBank?: any;
}

// Fake Country
enum ECountry {
  VIETNAM = 'VIET NAM',
}
const CONVERT_COUNTRY_CODE_TO_LABEL: { [key: string]: string } = {
  [ECountry.VIETNAM]: 'Việt Nam',
};

/**
 * DetailPersonalComponent
 */
@Component({
  selector: 'app-personal-info-detail',
  templateUrl: './personal-info-detail.component.html',
  styleUrl: './personal-info-detail.component.scss',
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE],
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
    DestroyService,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PersonalInfoDetailComponent implements OnInit, AfterViewChecked {
  @Input() data: any;

  @Output() formChanged = new EventEmitter<void>();
  editMode = false;

  headerCalendar = DatePickerNavigationFullDateComponent;

  CONVERT_GENDER_TO_LABLE = CONVERT_GENDER_TO_LABLE;

  initialFormValues: any;
  personalInfoForm!: FormGroup;

  Object = Object;

  optionList = [
    {
      name: 'Cục Cảnh sát ĐKQL cư trú và DLQG về dân cư',
      id: '1',
      children: [],
      config: 'level1',
    },
    {
      name: 'Cục Cảnh sát QLHC về TTXH',
      id: '2',
      children: [],
      config: 'level1',
    },
    {
      name: 'Công an Tỉnh/Thành phố',
      id: '3',
      children: LIST_PROVINCE,
      config: 'level1',
    },
    {
      name: 'Cục Quản lý xuất nhập cảnh',
      id: '4',
      children: [],
      config: 'level1',
    },
  ];

  locations: any[] = [];
  districts: any[] = [];
  wards: any[] = [];
  CONVERT_COUNTRY_CODE_TO_LABEL = CONVERT_COUNTRY_CODE_TO_LABEL;
  CONVERT_ID_NO_TYPE_TO_LABEL = CONVERT_ID_NO_TYPE_TO_LABEL;

  /**
   * Constructor
   * @param fb FormBuilder
   * @param cdr cdr
   * @param popoverService popoverService
   * @param dialogService
   * @param _destroy
   * @param locationService
   */
  constructor(
    private readonly fb: FormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly popoverService: PopoverService,
    private readonly dialogService: DialogService,
    private readonly _destroy: DestroyService,
    private readonly locationService: LocationService
  ) {}

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.initForm();
    this.personalInfoForm.valueChanges.pipe(takeUntil(this._destroy)).subscribe(() => {
      if (this.hasFormChanged()) {
        this.formChanged.emit();
      }
    });

  }

  /**
   * ngAfterViewChecked
   */
  ngAfterViewChecked() {
    this.cdr.detectChanges();
  }

  /**
   * initForm
   */
  initForm() {
    const {
      customerName,
      telephone,
      // areaCode,
      // phoneNumber,
      birthday,
      email,
      sexId,
      nationalityId,
      address,
      idNumberType,
      identity,
      identityDate,
      identityIssuer,
      accountType,
      registrationRep,
      registrationNo,
      registrationDate,
    } = this.data;

    const convertIsoDate = (date: string): string => {
      const [year, month, day] = date.split('-');
      return `${day}/${month}/${year}`;
    };

    // nation,
    //   city,
    //   district,
    //   ward,

    this.personalInfoForm = this.fb.group({
      customerName,
      // areaCode: areaCode + ' ' + phoneNumber,
      areaCode: telephone,
      birthday: this.convertToDate(birthday),
      email: [email, [Validators.email, Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')]],
      sexId: CONVERT_GENDER_TO_LABLE[sexId],
      nationalityId,
      address,
      identity,
      idNumberType,
      identityDate: this.convertToDate(identityDate),
      identityIssuer,
      // fileIDNumber: this.fb.array(
      //   (identity?.files || []).map((file: any) =>
      //     this.fb.group({
      //       name: `${file.name}.${file.type}`,
      //       size: `${(file.size / 1024).toFixed(2)} kB`,
      //     })
      //   )
      // ),
      fileIDNumber: [],
      accountType: CONVERT_TYPE_ACCOUNT_TO_LABLE[accountType],
      registrationRep,
      registrationNo,
      dateOfBusinessRegistration: accountType === 2 ? convertIsoDate(registrationDate) : null,
      // fileBusinessRegistration: this.fb.array(
      //   (registrationNo?.files || []).map((file: any) =>
      //     this.fb.group({
      //       name: `${file.name}.${file.type}`,
      //       size: `${(file.size / 1024).toFixed(2)} kB`,
      //     })
      //   )
      // ),
      fileBusinessRegistration: [],
    });

    this.initialFormValues = this.personalInfoForm.getRawValue();
  }

  /**
   * updateDistricts
   * @param cityName cityName
   */
  updateDistricts(cityName: string): void {
    const city = this.locations.find((location) => location.name === cityName);
    this.districts = city ? city.district : [];
    this.getFormControl('district').setValue(null);
    this.wards = [];
    this.getFormControl('ward').setValue(null);
  }

  /**
   * updateWards
   * @param districtName districtName
   */
  updateWards(districtName: string): void {
    const district = this.districts.find((d) => d.name === districtName);
    this.wards = district ? district.ward : [];
    this.getFormControl('ward').setValue(null);
  }

  /**
   * hasFormChanged
   */
  hasFormChanged() {
    const currentValues = this.personalInfoForm.getRawValue();
    return !this.isEqual(this.initialFormValues, currentValues);
  }

  /**
   * Check if two objects are equal
   * @param obj1
   * @param obj2
   */
  private isEqual(obj1: any, obj2: any): boolean {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  }

  /**
   * save
   */
  save() {
    if (this.hasFormChanged()) {
      const dialogRef = this.dialogService.openPopUp(DeleteNotificationComponent, {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-88',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      });
      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((result) => {
          if (result) {
            this.saveFormData();
            this.toggleEditMode();
          }
        });
    } else {
      this.toggleEditMode();
    }
  }

  /**
   * cancel
   */
  cancel() {
    if (this.hasFormChanged()) {
      const dialogRef = this.dialogService.openPopUp(DeleteNotificationComponent, {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-97',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
          isHideMessage: true,
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      });
      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((result) => {
          if (result) {
            this.personalInfoForm.patchValue(this.initialFormValues);
            this.patchFormArray('fileIDNumber', this.initialFormValues.fileIDNumber);
            this.patchFormArray('fileBusinessRegistration', this.initialFormValues.fileBusinessRegistration);
            this.toggleEditMode();
          }
        });
    } else {
      this.toggleEditMode();
    }
  }

  /**
   * @param arrayName arrayName
   * @param values values
   */
  patchFormArray(arrayName: string, values: any[]) {
    const formArray = this.personalInfoForm.get(arrayName) as FormArray;
    formArray.clear();
    values.forEach((value) => formArray.push(this.fb.group(value)));
  }

  /**
   * saveFormData
   */
  saveFormData() {
    this.initialFormValues = this.personalInfoForm.getRawValue();
  }

  /**
   * convertToDate
   * @param dateString
   */
  convertToDate(dateString: string): Date | null {
    return dateString ? new Date(dateString) : null;
  }

  /**
   * toggleEditMode
   */
  toggleEditMode() {
    this.editMode = !this.editMode;
  }

  /**
   * openSearchList
   * @param data
   * @param data.event
   * @param data.element
   * @param data.formControlName
   */
  openSearchList(data: { event: Event; element: HTMLElement; formControlName: string }) {
    const { element, formControlName } = data;
    const originWidth = element.getBoundingClientRect().width;
    const config = this.getPopoverConfig(formControlName);

    const popoverData = {
      origin: element,
      width: `${originWidth}px`,
      data: this.personalInfoForm.value,
      componentConfig: config,
    };

    let ref: any;
    switch (formControlName) {
      case 'sexId':
      case 'idNumberType':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: SearchListComponent,
        });
        break;

      case 'nationalityId':
      case 'nation':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: NationalityDropdownComponent,
          position: 2,
        });
        break;

      case 'city':
      case 'district':
      case 'ward':
        ref = this.popoverService.open<any>({
          ...popoverData,
          position: 2,
          content: SearchListComponent,
          height: '200px',
          panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
        });
        break;

      case 'identityIssuer':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: DropdownMultipleLevelV2Component,
        });
        break;
    }

    ref.afterClosed$.pipe(take(1)).subscribe((result: any) => this.handlePopoverResult(result, formControlName));
  }

  /**
   * Returns a PopoverConfig object based on the form control name provided.
   * @param {string} formControlName
   */
  private getPopoverConfig(formControlName: string): PopoverConfig {
    const config: PopoverConfig = { multiple: false };
    switch (formControlName) {
      case 'sexId':
        return {
          ...config,
          searchKey: 'value',
          isSearch: false,
          value: [CONVERT_LABEL_GENDER_TO_NUMBER[this.getFormControl('sexId')?.value]],
          displayOptionFn: (v: any) => CONVERT_GENDER_TO_LABLE[v.value],
          options: ListOptionGender,
        };

      case 'nationalityId':
        return {
          ...config,
          searchKey: 'name',
          isSearch: true,
          isShowLogo: false,
          isShowAreaCode: false,
          displayOptionFn: (v: any) => v.name,
          value: [this.getFormControl('nationalityId')?.value],
          options: LIST_OF_NATIONALITY,
        };

      case 'nation':
        return {
          ...config,
          searchKey: 'name',
          isSearch: true,
          isShowLogo: false,
          isShowAreaCode: false,
          displayOptionFn: (v: any) => v.name,
          value: [this.getFormControl('nation')?.value],
          options: LIST_OF_NATIONALITY,
        };

      case 'city':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          displayOptionFn: (v: any) => {
            return v.name;
          },
          value: [this.getFormControl('city')?.value],
          options: this.locations,
        };

      case 'district':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          displayOptionFn: (v: any) => {
            return v.name;
          },
          value: [this.getFormControl('district')?.value],
          options: this.districts,
        };

      case 'ward':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          displayOptionFn: (v: any) => {
            return v.name;
          },
          value: [this.getFormControl('ward')?.value],
          options: this.wards,
        };

      case 'idNumberType':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          displayOptionFn: (v: any) => {
            return v.name;
          },
          value: [this.getFormControl('idNumberType')?.value],
          options: ListOfTypeID,
        };

      case 'identityIssuer':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          displayOptionFn: (v: any) => v.name,
          displayValueFn: (v: any) =>
            Object.keys(v.children).length
              ? v.name + ' - ' + LIST_PROVINCE.find((p) => p.id === v.children.id)!.name
              : v.name,
          key: 'children',
          isShowSearch: false,
          isUpdateList: true,
          options: this.optionList,
          value: [this.getFormControl('identityIssuer')?.value],
          configChildren: {
            level1: {
              isShowSearch: true,
              isShowBtn: false,
              searchKey: 'name',
              placeholder: 'Tìm kiếm',
              displayOptionFn: (v: any) => v.name,
            },
            level2: {
              isShowSearch: false,
              isShowBtn: true,
              searchKey: 'name',
              placeholder: '',
              displayOptionFn: (v: any) => v.name,
            },
          },
          initialOptions: this.optionList,
        };

      default:
        return config;
    }
  }

  /**
   * handlePopoverResult
   * @param result
   * @param formControlName
   */
  private handlePopoverResult(result: any, formControlName: string): void {
    if (!result.data) return;

    const selectedValue = result?.data;
    let value: any;
    switch (formControlName) {
      case 'sexId': {
        value = CONVERT_GENDER_TO_LABLE[selectedValue.item[0].value];

        this.personalInfoForm.patchValue({
          sexId: value,
        });
        return value;
      }

      case 'nationalityId': {
        value = selectedValue.item[0].name;
        this.personalInfoForm.patchValue({
          nationalityId: value,
        });
        return value;
      }

      case 'nation': {
        value = selectedValue.item[0].name;
        // FIX ME: Đợi Api của các nước

        this.personalInfoForm.patchValue({
          nation: value,
          city: null,
          district: null,
          ward: null,
        });
        return value;
      }

      case 'city': {
        value = selectedValue.item[0].name;
        this.personalInfoForm.patchValue({
          city: value,
        });
        return value;
      }

      case 'district': {
        value = selectedValue.item[0].name;
        this.personalInfoForm.patchValue({
          district: value,
        });
        return value;
      }

      case 'ward': {
        value = selectedValue.item[0].name;
        this.personalInfoForm.patchValue({
          ward: value,
        });
        return value;
      }

      case 'idNumberType': {
        value = selectedValue.item[0].name;

        this.personalInfoForm.patchValue({
          idNumberType: value,
        });
        return value;
      }

      case 'identityIssuer': {
        const itemSelected = !result.data?.children?.length ? {} : result.data.children.find((c: any) => c.isSelected);

        const dataConfig = {
          name: result.data?.name,
          children: !result.data?.children?.length
            ? {}
            : {
                id: itemSelected.id,
              },
        };
        value = dataConfig;

        this.personalInfoForm.patchValue({
          identityIssuer: value,
        });

        return value;
      }
    }
  }

  /**
   * changeDateEvent
   * @param event
   */
  changeDateEvent(event: any) {
    const newAreaCode = event.date;
    this.personalInfoForm.patchValue({ areaCode: newAreaCode });
  }

  /**
   * addFilesToForm
   * @param event
   * @param event.files
   * @param event.formArrayName
   */
  addFilesToForm(event: { files: any[]; formArrayName: string }) {
    const { files, formArrayName } = event;
    if (!Array.isArray(files)) {
      console.error('Files is not an array:', files);
      return;
    }
    const formArray = this.personalInfoForm.get(formArrayName) as FormArray;
    files.forEach((file) => {
      formArray.push(
        this.fb.group({
          name: file.name,
          size: file.size,
        })
      );
    });
  }

  /**
   * removeFileFromForm
   * @param event
   * @param event.idx
   * @param event.formArrayName
   */
  removeFileFromForm(event: { idx: number; formArrayName: string }) {
    const { idx, formArrayName } = event;
    const formArray = this.personalInfoForm.get(formArrayName) as FormArray;

    // Ensure formArray is properly initialized
    if (!formArray) {
      console.error('FormArray not found:', formArrayName);
      return;
    }

    // Validate index
    if (idx >= 0 && idx < formArray.length) {
      formArray.removeAt(idx);
    }
  }

  /**
   * getProvinceName
   * @param {string} provinceId
   */
  getProvinceName(provinceId: string): string | undefined {
    const province = LIST_PROVINCE.find((p) => p.id === provinceId);
    return province ? province.name : undefined;
  }

  /**
   * GetFormControl
   * @param field
   * @returns {any} control from form
   */
  getFormControl(field: string) {
    return this.personalInfoForm.get(field) as FormControl;
  }
}
