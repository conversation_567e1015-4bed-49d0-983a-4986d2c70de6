.personal-detail-wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow: hidden;

  & > form {
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .title-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;

    .title {
      color: var(--color--text-vibrant--secondary);
    }

    .edit-btn-wrapper {
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;

      .edit-btn {
        ::ng-deep {
          path {
            stroke: var(--color--brand--500);
          }
        }
      }

      .edit-content-btn {
        color: var(--color--brand--500);
      }
    }

    .edit-btn-wrapper-save {
      display: flex;
      align-items: center;
      gap: 8px;

      .button {
        padding: 6px 10px;
        border-radius: 8px;
        background-color: var(--color--neutral--white);
        cursor: pointer;
        display: flex;
        gap: 8px;

        &.save {
          border: 1px solid var(--color--brand--500);
          color: var(--color--brand--500);
        }

        &.cancel {
          border: 1px solid var(--color--danger--600);
          color: var(--color--danger--600);
        }
      }
    }
  }

  .content {
    margin-top: 4px;
    padding: 16px 24px 16px 24px;
    border: 1px solid var(--color--other--divider);
    background-color: var(--color--background--1);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    align-self: stretch;
    overflow: auto;

    .row {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      align-self: stretch;

      .item {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
        &.item-upload-file-cls {
          width: calc(100% / 2 - 8px);
        }

        &.width-100 {
          width: 100%;
        }

        .input-wrapper {
          &:has(app-phone-number-table-component) {
            border-radius: 8px;
            border: 1px solid var(--color--neutral--100);
          }
          ::ng-deep {
            .phone-number-table-cls {
              width: 100%;
              height: 48px;
              background-color: var(--color--neutral--white);
              border-radius: 8px;
              .phone-number-cls {
                &:focus {
                  border: 1px solid transparent;
                }
              }
            }

            .area-code-cls {
              height: 100%;
              .area-code {
                height: 18px;
              }
            }

            input {
              height: 100%;
            }
          }
        }

        // disable input in app-input
        .disable {
          ::ng-deep {
            input {
              pointer-events: none;
              background: var(--color--neutral--50);
            }
          }
        }
      }
    }

    .label {
      color: var(--color--text-vibrant--secondary);

      &.flex {
        display: flex;
        align-items: center;
        gap: 8px;

        img {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}

.section {
  margin-top: 4px;
  display: flex;
  padding: 16px 24px 16px 24px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid var(--color--other--divider);
  background: var(--color--background--1);
}

.section-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.info {
  display: flex;
  height: 36px;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  color: var(--color--text--default);

  &.phone-number,
  &.email {
    display: flex;
    align-items: center;
    gap: 8px;

    .icon {
      width: 20px;
      height: 20px;
    }
  }

  &.file {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px 6px 4px;
    border-radius: 6px;
    border: 1px solid var(--color--cyan--600);
    height: 44px;

    .image-icon {
      width: 32px;
      height: 32px;
    }

    .file-info {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .name {
        display: flex;
        flex-direction: column;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .file-name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .size {
          color: var(--color--text--subdued);
        }
      }
    }

    .download {
      width: 18px !important;
      height: 18px !important;
      cursor: pointer;
      margin-bottom: auto;
    }
  }
}

::ng-deep {
  ng-component {
    width: 100%;
  }
}
