import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { take, takeUntil } from 'rxjs';
import { DestroyService, DialogService } from 'src/app/core/services';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import {
  CONVERT_CUSTOMER_LEVEL_TO_LABLE,
  LIST_MG_OPTIONS,
  LIST_MG_ROOM_OPTIONS,
  ListOptionCustomerGroup,
  ListOptionCustomerLevel,
} from '../../../constants/customers';
import { PopoverConfig } from '../personal-info-detail/personal-info-detail.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { DropdownMultipleLevelV2Component } from 'src/app/shared/components/dropdown-multiple-level/dropdown-multiple-level-v2/dropdown-multiple-level-v2.component';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { NumberFormatPipe } from 'src/app/shared/pipes/format-number/format-number.pipe';

/**
 * AccountInfoDetailComponent
 */
@Component({
  selector: 'app-account-info-detail',
  templateUrl: './account-info-detail.component.html',
  styleUrl: './account-info-detail.component.scss',
})
export class AccountInfoDetailComponent implements OnInit {
  @Input() data: any;
  @Output() formChanged = new EventEmitter<void>();

  editMode = false;
  initialFormValues: any;

  accountInfoForm!: FormGroup;

  numberFormatPipe = new NumberFormatPipe();

  CONVERT_CUSTOMER_LEVEL_TO_LABLE = CONVERT_CUSTOMER_LEVEL_TO_LABLE;

  /**
   * Constructor
   * @param fb FormBuilder
   * @param dialogService
   * @param _destroy
   * @param popoverService
   */
  constructor(
    private readonly fb: FormBuilder,
    private readonly dialogService: DialogService,
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService
  ) {}

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.initForm();
    this.accountInfoForm.valueChanges.pipe(takeUntil(this._destroy)).subscribe(() => {
      if (this.hasFormChanged()) {
        this.formChanged.emit();
      }
    });
  }

  /**
   * initForm
   */
  initForm() {
    const {
      customerLevel,
      customerGroup,
      brokerRoom,
      baseTransactionFee,
      bondTransactionFee,
      derivativesTransactionFee,
      accountOpeningDate,
      lastTransactionDate,
      accountClosingDate,
      brokerInfo,
    } = this.data;

    this.accountInfoForm = this.fb.group({
      customerLevel: CONVERT_CUSTOMER_LEVEL_TO_LABLE[customerLevel],
      customerGroup,
      brokerRoom: brokerRoom.name ?? brokerRoom,
      brokerInfo,

      // Phí GD cơ sở
      baseTransactionFee: {
        group: baseTransactionFee ? baseTransactionFee.group : null,
        value: baseTransactionFee ? baseTransactionFee.value : null,
      },
      labelBaseTransactionFee: null,

      // Phí giao dịch trái phiếu
      bondTransactionFee: {
        group: bondTransactionFee ? bondTransactionFee.group : null,
        value: bondTransactionFee ? bondTransactionFee.value : null,
      },
      labelBondTransactionFee: null,

      // Phí GD phái sinh
      derivativesTransactionFee: {
        group: derivativesTransactionFee ? derivativesTransactionFee.group : null,
        value: derivativesTransactionFee ? derivativesTransactionFee.value : null,
      },
      labelDerivativesTransactionFee: null,

      accountOpeningDate: accountOpeningDate ?? null,
      lastTransactionDate: lastTransactionDate ?? null,
      accountClosingDate: accountClosingDate ?? null,
    });

    this.accountInfoForm.patchValue({
      labelBaseTransactionFee:
        this.getFormControl('baseTransactionFee')?.value.group && this.getFormControl('baseTransactionFee')?.value.value
          ? `${this.getFormControl('baseTransactionFee')?.value.group} - ${this.convertPercentData(
              this.getFormControl('baseTransactionFee')?.value.value
            )}%/ giao dịch`
          : null,
      labelBondTransactionFee:
        this.getFormControl('bondTransactionFee')?.value.group && this.getFormControl('bondTransactionFee')?.value.value
          ? `${this.getFormControl('bondTransactionFee')?.value.group} - ${this.convertPercentData(
              this.getFormControl('bondTransactionFee')?.value.value
            )}%/ giao dịch`
          : null,
      labelDerivativesTransactionFee:
        this.getFormControl('derivativesTransactionFee')?.value.group &&
        this.getFormControl('derivativesTransactionFee')?.value.value
          ? `${this.getFormControl('derivativesTransactionFee')?.value.group} - ${this.numberFormatPipe.transform(
              this.getFormControl('derivativesTransactionFee')?.value.value
            )}/ hợp đồng`
          : null,
    });

    this.initialFormValues = this.accountInfoForm.getRawValue();
  }

  /**
   * hasFormChanged
   */
  hasFormChanged() {
    const currentValues = this.accountInfoForm.getRawValue();
    return !this.isEqual(this.initialFormValues, currentValues);
  }

  /**
   * Check if two objects are equal
   * @param obj1
   * @param obj2
   */
  private isEqual(obj1: any, obj2: any): boolean {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  }

  /**
   * save
   */
  save() {
    if (this.hasFormChanged()) {
      const dialogRef = this.dialogService.openPopUp(DeleteNotificationComponent, {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-88',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      });
      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((result) => {
          if (result) {
            this.saveFormData();
            this.toggleEditMode();
          }
        });
    } else {
      this.toggleEditMode();
    }
  }

  /**
   * cancel
   */
  cancel() {
    if (this.hasFormChanged()) {
      const dialogRef = this.dialogService.openPopUp(DeleteNotificationComponent, {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-97',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
          isHideMessage: true,
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      });
      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((result) => {
          if (result) {
            this.accountInfoForm.patchValue(this.initialFormValues);
            this.toggleEditMode();
          }
        });
    } else {
      this.toggleEditMode();
    }
  }

  /**
   * saveFormData
   */
  saveFormData() {
    this.initialFormValues = this.accountInfoForm.getRawValue();
  }

  /**
   * toggleEditMode
   */
  toggleEditMode() {
    if (this.editMode) {
      this.saveFormData();
    }

    this.editMode = !this.editMode;
  }

  /**
   * convertPercentData
   * @param number
   * @returns {number} - percent Number
   */
  convertPercentData(number: number) {
    if (number || number === 0) {
      const percentageValue = number * 100;
      const formattedValue = customNumberFormat(percentageValue, 'decimal', 'en-US', 2);
      return `${formattedValue}`;
    } else {
      return '-';
    }
  }

  /**
   * getCustomerLevel
   * @param level customerLevel
   */
  getCustomerLevel(level: string) {
    if (level === 'PLATINUM') return 'platinum-cls';
    else if (level === 'DIAMOND') return 'diamond-cls';
    else if (level === 'GOLD') return 'gold-cls';
    else if (level === 'NORMAL') return 'normal-cls';
    else return '';
  }

  /**
   * openSearchList
   * @param data
   * @param data.event
   * @param data.element
   * @param data.formControlName
   */
  openSearchList(data: { event: Event; element: HTMLElement; formControlName: string }) {
    const { element, formControlName } = data;
    const originWidth = element.getBoundingClientRect().width;
    const config = this.getPopoverConfig(formControlName);

    const popoverData = {
      origin: element,
      width: `${originWidth}px`,
      data: this.accountInfoForm.value,
      componentConfig: config,
    };

    let ref: any;
    switch (formControlName) {
      case 'customerLevel':
      case 'customerGroup':
      case 'brokerInfo':
      case 'labelBaseTransactionFee':
      case 'labelBondTransactionFee':
      case 'labelDerivativesTransactionFee':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: SearchListComponent,
          panelClass: ['dropdown-overlay-common', 'dropdown-width-100'],
        });
        break;

      case 'brokerRoom':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: DropdownMultipleLevelV2Component,
          panelClass: ['dropdown-overlay-common', 'dropdown-width-100'],
        });
        break;
    }
    ref.afterClosed$.pipe(take(1)).subscribe((result: any) => {
      this.handlePopoverResult(result, formControlName);
    });
  }

  /**
   * Returns a PopoverConfig object based on the form control name provided.
   * @param {string} formControlName
   */
  private getPopoverConfig(formControlName: string): PopoverConfig {
    const config: PopoverConfig = { multiple: false };
    switch (formControlName) {
      case 'customerLevel':
        return {
          ...config,
          searchKey: 'value',
          isSearch: false,
          value: [this.getFormControl('customerLevel')?.value],
          options: ListOptionCustomerLevel,
          displayOptionFn: (v: any) => v.value,
        };

      case 'customerGroup':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          value: [this.getFormControl('customerGroup')?.value],
          options: ListOptionCustomerGroup,
          displayOptionFn: (v: any) => v.name,
        };

      case 'brokerRoom':
        return {
          ...config,
          displayOptionFn: (v: any) => v.name,
          displayValueFn: (v: any) => {
            return Object.keys(v.children).length
              ? v.name + ' - ' + structuredClone(LIST_MG_ROOM_OPTIONS).find((p) => p.id === v.children.id)!.name
              : v;
          },
          searchKey: 'name',
          key: 'children',
          isShowSearch: true,
          isUpdateList: true,
          isShowBtn: true,
          value: [this.getFormControl('brokerRoom')?.value],
          options: structuredClone(LIST_MG_ROOM_OPTIONS),
          initialOptions: structuredClone(LIST_MG_ROOM_OPTIONS),
          configChildren: {
            level1: {
              isShowSearch: false,
              isShowBtn: false,
              searchKey: 'name',
              placeholder: 'Tên Phòng',
              displayOptionFn: (v: any) => v.name,
            },
          },
          filterKey: 'parentId',
        };

      case 'brokerInfo':
        return {
          ...config,
          searchKey: 'name',
          isSearch: true,
          placeholder: 'Mã, tên MG',
          isShowBotBtn: true,
          value: [this.getFormControl('brokerInfo')?.value],
          displayOptionFn: (v: any) => v.name,
          options: LIST_MG_OPTIONS,
        };

      case 'labelBaseTransactionFee':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          value: [this.getFormControl('labelBaseTransactionFee')?.value],
          displayOptionFn: (v: any) => v.name,
          options: [
            {
              name: `VIP - ${this.convertPercentData(
                this.getFormControl('baseTransactionFee')?.value.value
              )}%/ giao dịch`,
            },
            {
              name: `VVIP - ${this.convertPercentData(
                this.getFormControl('baseTransactionFee')?.value.value
              )}%/ giao dịch`,
            },
            {
              name: `SVIP - ${this.convertPercentData(
                this.getFormControl('baseTransactionFee')?.value.value
              )}%/ giao dịch`,
            },
            {
              name: `STANDARD - ${this.convertPercentData(
                this.getFormControl('baseTransactionFee')?.value.value
              )}%/ giao dịch`,
            },
          ],
        };

      case 'labelBondTransactionFee':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          value: [this.getFormControl('labelBondTransactionFee')?.value],
          displayOptionFn: (v: any) => v.name,
          options: [
            {
              name: `VIP - ${this.convertPercentData(
                this.getFormControl('bondTransactionFee')?.value.value
              )}%/ giao dịch`,
            },
            {
              name: `VVIP - ${this.convertPercentData(
                this.getFormControl('bondTransactionFee')?.value.value
              )}%/ giao dịch`,
            },
            {
              name: `SVIP - ${this.convertPercentData(
                this.getFormControl('bondTransactionFee')?.value.value
              )}%/ giao dịch`,
            },
            {
              name: `STANDARD - ${this.convertPercentData(
                this.getFormControl('bondTransactionFee')?.value.value
              )}%/ giao dịch`,
            },
          ],
        };

      case 'labelDerivativesTransactionFee':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          value: [this.getFormControl('labelDerivativesTransactionFee')?.value],
          displayOptionFn: (v: any) => v.name,
          options: [
            {
              name: `VIP - ${this.numberFormatPipe.transform(
                this.getFormControl('derivativesTransactionFee')?.value.value
              )}/ hợp đồng`,
            },
            {
              name: `VVIP - ${this.numberFormatPipe.transform(
                this.getFormControl('derivativesTransactionFee')?.value.value
              )}/ hợp đồng`,
            },
            {
              name: `SVIP - ${this.numberFormatPipe.transform(
                this.getFormControl('derivativesTransactionFee')?.value.value
              )}/ hợp đồng`,
            },
            {
              name: `STANDARD - ${this.numberFormatPipe.transform(
                this.getFormControl('derivativesTransactionFee')?.value.value
              )}/ hợp đồng`,
            },
          ],
        };

      default:
        return config;
    }
  }

  /**
   * handlePopoverResult
   * @param result
   * @param formControlName
   */
  private handlePopoverResult(result: any, formControlName: string): void {
    if (!result.data) return;
    const selectedValue = result?.data;
    let valueReturn: any;

    switch (formControlName) {
      case 'customerLevel': {
        valueReturn = selectedValue.item[0].value;

        this.accountInfoForm.patchValue({
          customerLevel: valueReturn,
        });
        return valueReturn;
      }

      case 'customerGroup': {
        valueReturn = selectedValue?.item[0]?.name;

        this.accountInfoForm.patchValue({
          customerGroup: valueReturn,
        });

        return valueReturn;
      }
      case 'brokerRoom': {
        const itemSelected = !result.data?.children?.length ? {} : result.data.children.find((c: any) => c.isSelected);

        const dataConfig = {
          name: result.data?.name,
          children: !result.data?.children?.length
            ? {}
            : {
                id: itemSelected.id,
              },
        };
        valueReturn = dataConfig;

        this.accountInfoForm.patchValue({
          brokerRoom: valueReturn,
          brokerInfo: itemSelected.name,
        });

        return valueReturn;
      }

      case 'brokerInfo': {
        valueReturn = selectedValue?.item[0]?.name;

        this.accountInfoForm.patchValue({
          brokerInfo: valueReturn,
        });

        return valueReturn;
      }

      case 'labelBaseTransactionFee': {
        valueReturn = selectedValue?.item[0]?.name;
        const groupTransactionFeeValue = valueReturn?.split(' - ')[0];

        this.accountInfoForm.patchValue({
          labelBaseTransactionFee: valueReturn,
          baseTransactionFee: {
            group: groupTransactionFeeValue,
            value: this.getFormControl('baseTransactionFee')?.value.value,
          },
        });

        return valueReturn;
      }

      case 'labelBondTransactionFee': {
        valueReturn = selectedValue?.item[0]?.name;
        const groupBondTransactionFee = valueReturn?.split(' - ')[0];

        this.accountInfoForm.patchValue({
          labelBondTransactionFee: valueReturn,
          bondTransactionFee: {
            group: groupBondTransactionFee,
            value: this.getFormControl('bondTransactionFee')?.value.value,
          },
        });

        return valueReturn;
      }

      case 'labelDerivativesTransactionFee': {
        valueReturn = selectedValue?.item[0]?.name;
        const groupDerivativesTransactionFee = valueReturn?.split(' - ')[0];

        this.accountInfoForm.patchValue({
          labelDerivativesTransactionFee: valueReturn,
          derivativesTransactionFee: {
            group: groupDerivativesTransactionFee,
            value: this.getFormControl('derivativesTransactionFee')?.value.value,
          },
        });

        return valueReturn;
      }
    }
  }

  /**
   * getBrokerId
   * @param {string} brokerRoom
   */
  getBrokerRoom(brokerRoom: string): string | undefined {
    const broker = LIST_MG_ROOM_OPTIONS.find((p) => p.id === brokerRoom);
    return broker ? broker.name : undefined;
  }

  /**
   * GetFormControl
   * @param field
   * @returns {any} control from form
   */
  getFormControl(field: string) {
    return this.accountInfoForm.get(field) as FormControl;
  }
}
