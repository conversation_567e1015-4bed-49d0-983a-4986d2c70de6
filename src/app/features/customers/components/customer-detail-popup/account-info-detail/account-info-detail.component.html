<div class="account-info-wrapper">
  <div [formGroup]="accountInfoForm" class="account-info">
    <div class="title-header">
      <div class="typo-body-19 title">{{ 'MES-10' | translate }}</div>
      <!-- Button chỉnh sửa
      @if(!editMode) {
      <div class="edit-btn-wrapper" (click)="toggleEditMode()">
        <mat-icon class="edit-btn" [svgIcon]="'icon:edit-2'"></mat-icon>
        <div class="edit-content-btn typo-body-9">{{ 'MES-340' | translate }}</div>
      </div>
      } @else {
      Button Lưu/Huỷ
      <div class="edit-btn-wrapper-save">
        <div class="button save typo-body-9" (click)="save()">
          Lưu
          <mat-icon [svgIcon]="'icon:tick-circle-icon'"></mat-icon>
        </div>
        <div class="button cancel typo-body-9" (click)="cancel()">
          Huỷ
          <mat-icon [svgIcon]="'icon:x-cross-red-icon'"></mat-icon>
        </div>
      </div>
      } -->
    </div>

    <div class="content">
      <div class="section">
        <div class="row">
          <!-- Hạng khách hàng -->
          <div class="item">
            <div class="label typo-body-12">{{ 'MES-310' | translate }}</div>
            @if(editMode) {
            <app-input
              type="dropdown"
              formControlName="customerLevel"
              (clickEvent)="openSearchList($event)"
            ></app-input>
            } @else {
            <div class="info typo-body-23">
              <div class="status" [ngClass]="getCustomerLevel(accountInfoForm.get('customerLevel')?.value)">
                {{ accountInfoForm.get('customerLevel')?.value ?? '-' }}
              </div>
            </div>
            }
          </div>

          <!-- Nhóm khách hàng -->
          <div class="item">
            <div class="label typo-body-12">{{ 'MES-13' | translate }}</div>
            @if(editMode) {
            <app-input
              type="dropdown"
              formControlName="customerGroup"
              (clickEvent)="openSearchList($event)"
            ></app-input>
            } @else {
            <div class="info typo-body-23">
              {{ accountInfoForm.get('customerGroup')?.value ?? '-' }}
            </div>
            }
          </div>
        </div>

        <div class="row">
          <!-- Phòng MG quản lý -->
          <div class="item">
            <div class="label typo-body-12">{{ 'MES-398' | translate }}</div>
            @if(editMode) {
            <app-input
              type="multiple-dropdown-brokerRoom"
              formControlName="brokerRoom"
              (clickEvent)="openSearchList($event)"
            ></app-input>
            } @else {
            <div class="info typo-body-23">
              {{ accountInfoForm.get('brokerRoom')?.value ?? '-' }}
            </div>
            }
          </div>

          <!-- Mã MG quản lý -->
          <div class="item">
            <div class="label typo-body-12">{{ 'MES-399' | translate }}</div>
            @if(editMode) {
            <app-input type="dropdown" formControlName="brokerInfo" (clickEvent)="openSearchList($event)"></app-input>
            } @else {
            <div class="info typo-body-23">
              {{ accountInfoForm.get('brokerInfo')?.value ?? '-' }}
            </div>
            }
          </div>
        </div>

        <div class="row">
          <!-- Phí GD cơ sở -->
          <div class="item">
            <div class="label typo-body-12">{{ 'MES-400' | translate }} cơ sở</div>
            @if(editMode) {
            <app-input
              type="dropdown"
              formControlName="labelBaseTransactionFee"
              (clickEvent)="openSearchList($event)"
            ></app-input>
            } @else {
            <div class="info typo-body-23">
              @if(accountInfoForm.get('baseTransactionFee')?.value.group &&
              accountInfoForm.get('baseTransactionFee')?.value.value) {
              <div class="groupFee">
                <div class="group">{{ accountInfoForm.get('baseTransactionFee')?.value.group }}</div>
                <div>{{ convertPercentData(accountInfoForm.get('baseTransactionFee')?.value.value) }}% / giao dịch</div>
              </div>
              } @else {
              <span>-</span>
              }
            </div>
            }
          </div>

          <!-- Phí GD phái sinh -->
          <div class="item">
            <div class="label typo-body-12">{{ 'MES-400' | translate }} phái sinh</div>
            @if(editMode) {
            <app-input
              type="dropdown"
              formControlName="labelDerivativesTransactionFee"
              (clickEvent)="openSearchList($event)"
            ></app-input>
            } @else {

            <div class="info typo-body-23">
              @if(accountInfoForm.get('derivativesTransactionFee')?.value.group &&
              accountInfoForm.get('derivativesTransactionFee')?.value.value) {
              <div class="groupFee">
                <div class="group">{{ accountInfoForm.get('derivativesTransactionFee')?.value.group }}</div>
                <div>
                  {{
                    accountInfoForm.get('derivativesTransactionFee')?.value.value | numberFormat : 'decimal' : 'en-US'
                  }}/ hợp đồng
                </div>
              </div>
              } @else {
              <span>-</span>
              }
            </div>
            }
          </div>
        </div>

        <div class="row">
          <!-- Phí giao dịch trái phiếu -->
          <div class="item half-50-item-cls">
            <div class="label typo-body-12">{{ 'MES-400' | translate }} trái phiếu</div>
            @if(editMode) {
            <app-input
              type="dropdown"
              formControlName="labelBondTransactionFee"
              (clickEvent)="openSearchList($event)"
            ></app-input>
            } @else {

            <div class="info typo-body-23">
              @if(accountInfoForm.get('bondTransactionFee')?.value.group &&
              accountInfoForm.get('bondTransactionFee')?.value.value) {
              <div class="groupFee">
                <div class="group">{{ accountInfoForm.get('bondTransactionFee')?.value.group }}</div>
                <div>{{ convertPercentData(accountInfoForm.get('bondTransactionFee')?.value.value) }}% / giao dịch</div>
              </div>
              } @else {
              <span>-</span>
              }
            </div>
            }
          </div>
        </div>

        <div class="row">
          <!-- Ngày mở tài khoản -->
          <div class="item">
            <div class="label typo-body-12">{{ 'MES-401' | translate }}</div>
            @if(editMode) {
            <app-input type="input" formControlName="accountOpeningDate" [readonly]="true"></app-input>
            } @else {
            <div class="info typo-body-23">
              {{ (accountInfoForm.get('accountOpeningDate')?.value | date : 'yyyy/MM/dd') ?? '-' }}
            </div>
            }
          </div>

          <!-- Ngày cuối giao dịch -->
          <div class="item">
            <div class="label typo-body-12">{{ 'MES-403' | translate }}</div>
            @if(editMode) {
            <app-input type="input" formControlName="lastTransactionDate" [readonly]="true"></app-input>
            } @else {
            <div class="info typo-body-23">
              {{ (accountInfoForm.get('lastTransactionDate')?.value | date : 'yyyy/MM/dd') ?? '-' }}
            </div>
            }
          </div>
        </div>

        <div class="row">
          <!-- Ngày đóng tài khoản -->
          <div class="item half-50-item-cls">
            <div class="label typo-body-12">{{ 'MES-402' | translate }}</div>
            @if(editMode) {
            <app-input type="input" formControlName="accountClosingDate" [readonly]="true"></app-input>
            } @else {
            <div class="info typo-body-23">
              {{ (accountInfoForm.get('accountClosingDate')?.value | date : 'yyyy/MM/dd') ?? '-' }}
            </div>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
