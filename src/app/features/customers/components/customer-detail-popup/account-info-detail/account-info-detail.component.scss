.account-info-wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow: hidden;
  & > form {
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .title-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;

    .title {
      color: var(--color--text-vibrant--secondary);
    }

    .edit-btn-wrapper {
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;

      .edit-btn {
        ::ng-deep {
          path {
            stroke: var(--color--brand--500);
          }
        }
      }

      .edit-content-btn {
        color: var(--color--brand--500);
      }
    }

    .edit-btn-wrapper-save {
      display: flex;
      align-items: center;
      gap: 8px;

      .button {
        padding: 6px 10px;
        border-radius: 8px;
        background-color: var(--color--neutral--white);
        cursor: pointer;
        display: flex;
        gap: 8px;

        &.save {
          border: 1px solid var(--color--brand--500);
          color: var(--color--brand--500);
        }

        &.cancel {
          border: 1px solid var(--color--danger--600);
          color: var(--color--danger--600);
        }
      }
    }
  }

  .content {
    margin-top: 4px;
    padding: 16px 24px 24px 24px;
    border: 1px solid var(--color--other--divider);
    background-color: var(--color--background--1);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    align-self: stretch;
    overflow: auto;

    .row {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      align-self: stretch;

      .item {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
        &.half-50-item-cls {
          width: calc(100% / 2 - 8px);
        }

        .info {
          display: flex;
          height: 36px;
          align-items: center;
          gap: 10px;
          align-self: stretch;
          color: var(--color--text--default);

          .status {
            width: fit-content;
            padding: 2px 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 16px;

            &.platinum-cls {
              background-color: var(--color--accents--orange-dark) !important;
            }

            &.diamond-cls {
              background-color: var(--color--accents--mint-dark) !important;
            }

            &.gold-cls {
              background-color: var(--color--accents--yellow-dark) !important;
            }

            &.normal-cls {
              background-color: var(--color--neutral--100) !important;
            }
          }

          .groupFee {
            display: flex;
            align-items: center;
            gap: 10px;

            .group {
              padding: 2px 8px;
              width: fit-content;
              border-radius: 16px;
              background-color: var(--color--accents--yellow-dark);
            }
          }
        }
      }
    }

    .label {
      color: var(--color--text-vibrant--secondary);

      &.flex {
        display: flex;
        align-items: center;
        gap: 8px;

        img {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}

.section {
  display: flex;
  // padding: 16px 24px 24px 24px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
  border-radius: 8px;
  // border: 1px solid var(--color--other--divider);
  background: var(--color--background--1);
}
