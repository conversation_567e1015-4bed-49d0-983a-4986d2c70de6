import { Component, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DestroyService, DialogService } from 'src/app/core/services';
import {
  CONVERT_CUSTOMER_LEVEL_TO_LABLE,
  CONVERT_TYPE_FILE_DOCMENT_INFO_TO_NAME_CONFIG,
  ECustomerLevel,
} from 'src/app/features/customers/constants/customers';
import { AccountInfoDetailComponent } from './account-info-detail/account-info-detail.component';
import { AuthorityInfoDetailComponent } from './authority-info-detail/authority-info-detail.component';
import { PersonalInfoDetailComponent } from './personal-info-detail/personal-info-detail.component';
import { BankInfoDetailComponent } from './bank-info-detail/bank-info-detail.component';
import { DocumentInfoDetailComponent } from './document-info-detail/document-info-detail.component';
import { Store } from '@ngrx/store';
import {
  selecAllBankList$,
  selectCustomerAccountDetailInfo$,
  selectCustomerAuthority$,
  selectCustomerBankInfo$,
  selectCustomerDocumentInfo$,
} from '../../stores/customer.selector';
import { combineLatest, takeUntil } from 'rxjs';
import {
  ICustomerAuthority,
  ICustomerAccount,
  ICustomerBank,
  ICustomerDocument,
  ICustomerPersonal,
  IBeneficiaryBank,
  IIDentificationFiles,
} from '../../model/customer';
import { DatePipe } from '@angular/common';
import { selectCustomerList$ } from 'src/app/stores/shared/shared.selectors';
import { LIST_OF_BANK } from 'src/app/shared/constants/bank';
import { IAllAccountNumber } from 'src/app/shared/models/global';

enum ERoute {
  PERSONAL = 'personal',
  BANK = 'bank',
  DOCUMENT = 'document',
  ACCOUNT = 'account',
  AUTHORITY = 'authority',
}

const CONVERT_ROUTE_TO_INDEX: { [key: string]: number } = {
  [ERoute.PERSONAL]: 0,
  [ERoute.BANK]: 1,
  [ERoute.DOCUMENT]: 2,
  [ERoute.ACCOUNT]: 3,
  [ERoute.AUTHORITY]: 4,
};
/**
 * CustomerDetailPopupComponent
 */
@Component({
  selector: 'app-customer-detail-popup',
  templateUrl: './customer-detail-popup.component.html',
  styleUrl: './customer-detail-popup.component.scss',
  providers: [DatePipe, DestroyService],
})
export class CustomerDetailPopupComponent {
  @ViewChild(PersonalInfoDetailComponent, { static: false }) personalInfoDetailComponent!: PersonalInfoDetailComponent;
  @ViewChild(BankInfoDetailComponent, { static: false }) bankInfoDetailComponent!: BankInfoDetailComponent;
  @ViewChild(DocumentInfoDetailComponent, { static: false }) documentInfoDetailComponent!: DocumentInfoDetailComponent;
  @ViewChild(AccountInfoDetailComponent, { static: false }) accountInfoDetailComponent!: AccountInfoDetailComponent;
  @ViewChild(AuthorityInfoDetailComponent, { static: false })
  authorityInfoDetailComponent!: AuthorityInfoDetailComponent;

  detailData!: any;
  customerName!: string;
  accountNumber!: string;
  customerLevel!: string;

  personalInfoData!: ICustomerPersonal[];
  bankInfoData!: ICustomerBank[];
  documentInfoData!: ICustomerDocument[];
  accountInfoData!: ICustomerAccount[];
  authorityInfoData!: ICustomerAuthority[];

  CONVERT_CUSTOMER_LEVEL_TO_LABLE = CONVERT_CUSTOMER_LEVEL_TO_LABLE;

  isFormChange = false;

  menuItems = [
    {
      label: 'Thông tin cá nhân',
      icon: 'menu:customer-management',
      activeIcon: 'menu:customer-management-active',
      route: 'personal',
    },
    // { label: 'Thông tin ngân hàng', icon: 'icon:bank-subdued', activeIcon: 'icon:bank-subdued-active', route: 'bank' },
    // {
    //   label: 'Thông tin tài liệu',
    //   icon: 'icon:document-subdued',
    //   activeIcon: 'icon:document-active',
    //   route: 'document',
    // },
    // { label: 'Thông tin tài khoản', icon: 'icon:profile-subdued', activeIcon: 'icon:profile-active', route: 'account' },
    // { label: 'Thông tin lãi suất', icon: 'icon:group-subdued', activeIcon: 'icon:dollar-active' },
    // { label: 'Thông tin uỷ quyền', icon: 'icon:user-tick-subdued', activeIcon: 'icon:user-tick-active', route: 'auth' },
  ];

  selectedMenuTab!: any;

  infoCustomer!: any;

  /**
   *
   * @param data
   * @param data.allocatedSource
   * @param data.allocatedTarget
   * @param dialogService
   * @param store
   * @param destroyService
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private readonly dialogService: DialogService,
    private readonly store: Store,
    private readonly _destroy: DestroyService
  ) {
    this.selectedMenuTab = this.menuItems[CONVERT_ROUTE_TO_INDEX[data.route]];
    this.detailData = data.element;
    const { customerName, customerLevel, accountNumber } = this.detailData;
    this.customerName = customerName;
    this.customerLevel = customerLevel;
    this.accountNumber = accountNumber;
    this.infoCustomer = {
      accountNumber: accountNumber.split('-')[0].trim(),
      customerName,
    } as any;

    this.getCustomerDetail();
  }

  /**
   * getCustomerDetail
   */
  getCustomerDetail() {
    this.store
      .select(selectCustomerList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((data) => {
        this.personalInfoData = data;
      });

    combineLatest([this.store.select(selectCustomerBankInfo$), this.store.select(selecAllBankList$)])
      .pipe(takeUntil(this._destroy))
      .subscribe(([data, bankList]) => {
        const dataClone = structuredClone(data);
        this.bankInfoData = dataClone.map((d) => {
          d.bankAccounts.forEach((i: any, index: number) => {
            d[`bankAccounts${index + 1}`] = {
              ...i,
              beneficiaryBank: {
                ...i.beneficiaryBank,
                logo: this.getLogoBank(i.beneficiaryBank),
              },
            };
          });
          return d;
        });
      });

    this.store
      .select(selectCustomerDocumentInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((data) => {
        const dataClone = structuredClone(data);
        this.documentInfoData = dataClone.map((d) => {
          const configTagColumns = [
            'contractId00',
            'contractId01',
            'contractId02',
            'contractId03',
            'contractId04',
            'contractId05',
            'contractId80',
          ];

          configTagColumns.forEach((tag) => (d[tag] = null));
          d.contracts.forEach((contract, index) => {
            d[CONVERT_TYPE_FILE_DOCMENT_INFO_TO_NAME_CONFIG[contract.contractType]] = {
              name: contract.name,
              type: contract.fileType,
              files: this.mapFiles(contract.contractFiles),
            };

            d['businessCode'] = !d.registration
              ? null
              : {
                  name: d.registration.name,
                  type: d.registration.type,
                  files: this.mapFiles(d.registration.registrationFiles),
                };
          });

          return d;
        });
      });

    this.store
      .select(selectCustomerAccountDetailInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((data) => {
        if (!data.length) return;
        this.accountInfoData = structuredClone(data).map((d) => ({
          ...d,
          brokerInfo: d.brokerCode + ': ' + d.brokerName,
          brokerRoom: d.saleGroupName,
        }));
      });

    this.store
      .select(selectCustomerAuthority$)
      .pipe(takeUntil(this._destroy))
      .subscribe((data) => {
        this.authorityInfoData = data;
      });
  }

  getLogoBank(beneficiaryBank: IBeneficiaryBank) {
    return LIST_OF_BANK.find((b) => b.name === beneficiaryBank.name)?.logo;
  }

  mapFiles(files: IIDentificationFiles[]) {
    return files.map((file) => ({
      name: file.name,
      type: file.fileType,
      url: file.url,
      size: file.size,
    }));
  }

  /**
   * getCustomerLevel
   * @param level customerLevel
   */
  getCustomerLevel(level: string) {
    if (level === ECustomerLevel.PLATINUM) return 'platinum-cls';
    else if (level === ECustomerLevel.DIAMOND) return 'diamond-cls';
    else if (level === ECustomerLevel.GOLD) return 'gold-cls';
    else return '';
  }

  /**
   * checkTabToGetData
   * @param item
   * @param accountNumber
   */
  checkTabToGetData(item: any, accountNumber: string) {
    switch (item.route) {
      case ERoute.PERSONAL:
        this.detailData = this.personalInfoData.find((d) => d.accountNumber === accountNumber);
        break;

      case ERoute.BANK:
        this.detailData = this.bankInfoData.find((d) => d.accountNumber === accountNumber);
        break;

      case ERoute.DOCUMENT:
        {
          const identifyInfo = this.personalInfoData.find((d) => d.accountNumber === accountNumber);
          this.detailData = this.documentInfoData.find((d) => d.accountNumber === accountNumber);
          this.detailData = {
            ...this.detailData,
            identifyInfo,
          };
        }
        break;

      case ERoute.ACCOUNT:
        this.detailData = this.accountInfoData.find((d) => d.accountNumber === accountNumber);
        break;

      case ERoute.AUTHORITY:
        this.detailData = this.authorityInfoData.find((d) => d.accountNumber === accountNumber);
        break;

      default:
        break;
    }
  }

  /**
   * selectTab
   * @param item
   */
  selectTab(item: any, accountNumber: string) {
    this.checkTabToGetData(item, accountNumber);
    // Check nếu form thay đổi sẽ hiện popup save trong các component
    if (this.isFormChange) {
      const components = [
        this.personalInfoDetailComponent,
        this.bankInfoDetailComponent,
        this.documentInfoDetailComponent,
        this.accountInfoDetailComponent,
        this.authorityInfoDetailComponent,
      ];

      for (const component of components) {
        if (component?.hasFormChanged()) {
          component.save();
          this.isFormChange = false;

          return;
        }
      }
    }

    this.selectedMenuTab = item;
  }

  /**
   * onFormChanged
   * Nếu form thay đổi sẽ đỏi isFomrChange = true
   * @param event
   */
  onFormChanged(event: any) {
    this.isFormChange = true;
  }
}
