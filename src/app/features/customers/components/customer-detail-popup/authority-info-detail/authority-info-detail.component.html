<div class="authority-detail-wrapper">
  <!-- THÔNG TIN UỶ QUYỀN -->
  <form [formGroup]="authorityInfoForm" class="authority-info">
    <div class="title-header">
      <div class="typo-body-19 title">{{ 'MES-404' | translate }}</div>

      <!-- Button chỉnh sửa
      @if(!editMode) {
      <div class="edit-btn-wrapper" (click)="toggleEditMode()">
        <mat-icon class="edit-btn" [svgIcon]="'icon:edit-2'"></mat-icon>
        <div class="edit-content-btn typo-body-9">{{ 'MES-340' | translate }}</div>
      </div>
      } @else {
      Button Lưu/Huỷ
      <div class="edit-btn-wrapper-save">
        <div class="button save typo-body-9" (click)="save()">
          Lưu
          <mat-icon [svgIcon]="'icon:tick-circle-icon'"></mat-icon>
        </div>
        <div class="button cancel typo-body-9" (click)="cancel()">
          Huỷ
          <mat-icon [svgIcon]="'icon:x-cross-red-icon'"></mat-icon>
        </div>
      </div>
      } -->
    </div>

    <div class="content">
      <div class="row">
        <!-- Trạng thái uỷ quyền -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-405' | translate }}</div>
          @if(editMode) {
          <app-input type="dropdown" formControlName="authStatus" (clickEvent)="openSearchList($event)"></app-input>
          } @else {
          <div class="info typo-body-23">
            <div
              class="status"
              [ngClass]="{
                authority: authorityInfoForm.get('authStatus')?.value === 'Có uỷ quyền',
                'not-authority': authorityInfoForm.get('authStatus')?.value === 'Không uỷ quyền'
              }"
            >
              {{ authorityInfoForm.get('authStatus')?.value ?? '-' }}
            </div>
          </div>
          }
        </div>

        <!-- Người được uỷ quyền -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-406' | translate }}</div>
          @if(editMode) {
          <app-input type="input" formControlName="customerName"></app-input>
          } @else {
          <div class="info typo-body-23">
            {{ authorityInfoForm.get('customerName')?.value ?? '-' }}
          </div>
          }
        </div>
      </div>

      <div class="row">
        <!-- Số điện thoại -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-349' | translate }}</div>
          @if(editMode) {
          <div class="input-wrapper">
            <app-phone-number-table-component
              [data]="authorityInfoForm.get('areaCode')?.value"
              [isEdit]="editMode"
              [element]="data"
              [tag]="'areaCode'"
              (dateChangeEvent)="changeDateEvent($event)"
              [isShowArrowDown]="true"
            ></app-phone-number-table-component>
          </div>
          } @else {
          <div class="info phone-number typo-body-23">
            <img class="icon" src="./assets/icons/phone.svg" alt="" />
            {{ authorityInfoForm.get('areaCode')?.value ?? '-' }}
          </div>
          }
        </div>

        <!-- Email -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-394' | translate }}</div>
          @if(editMode) {
          <app-input type="suffix-icon" formControlName="authEmail" [iconSuffix]="'icon:sms-subdued'"></app-input>
          <mat-error class="typo-body-9" *ngIf="getFormControl('authEmail').invalid">{{
            'MES-102' | translate
          }}</mat-error>
          } @else {
          <div class="info phone-number typo-body-23">
            <img class="icon" src="./assets/icons/sms.svg" alt="" />
            {{ authorityInfoForm.get('authEmail')?.value ?? '-' }}
          </div>
          }
        </div>
      </div>

      <!-- ĐỊA CHỈ -->
      <div class="row">
        <!-- Địa chỉ -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-469' | translate }}</div>
          @if(editMode) {
          <app-input type="input" formControlName="authAddress"></app-input>
          } @else {
          <div class="info typo-body-23">{{ authorityInfoForm.get('authAddress')?.value ?? '-' }}</div>
          }
        </div>
      </div>
      <!-- <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-354' | translate }}</div>
        <div class="section section-config">
          <div class="row">
            Quốc gia
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-353' | translate }}</div>
              @if(editMode) {
              <app-input type="dropdown" formControlName="nation" (clickEvent)="openSearchList($event)"></app-input>
              } @else {
              <div class="info typo-body-23">{{ authorityInfoForm.get('nation')?.value ?? '-' }}</div>
              }
            </div>

            Tỉnh / Thành Phố
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-357' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="city"
                (clickEvent)="getFormControl('nation').value === 'Việt Nam' ? openSearchList($event) : null"
              ></app-input>
              } @else {
              <div class="info typo-body-23">{{ authorityInfoForm.get('city')?.value ?? '-' }}</div>
              }
            </div>
          </div>

          <div class="row">
            Quận / Huyện
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-356' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="district"
                (clickEvent)="getFormControl('nation').value === 'Việt Nam' ? openSearchList($event) : null"
              ></app-input>
              } @else {
              <div class="info typo-body-23">{{ authorityInfoForm.get('district')?.value ?? '-' }}</div>
              }
            </div>

            Xã / Phường
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-355' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="ward"
                (clickEvent)="getFormControl('nation').value === 'Việt Nam' ? openSearchList($event) : null"
              ></app-input>
              } @else {
              <div class="info typo-body-23">{{ authorityInfoForm.get('ward')?.value ?? '-' }}</div>
              }
            </div>
          </div>

          <div class="row">
            Địa chỉ
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-354' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="address"></app-input>
              } @else {
              <div class="info typo-body-23">{{ authorityInfoForm.get('address')?.value ?? '-' }}</div>
              }
            </div>
          </div>
        </div>
      </app-section-wrapper-layout> -->

      <div class="row">
        <!-- Số hợp đồng uỷ quyền -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-407' | translate }}</div>
          @if(editMode) {
          <app-input type="input" formControlName="contractID"></app-input>
          } @else {
          <div class="info typo-body-23">{{ authorityInfoForm.get('contractID')?.value ?? '-' }}</div>
          }
        </div>

        <!-- Chữ ký người uỷ quyền-->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-408' | translate }}</div>
          @if(editMode) {
          <app-input type="input" formControlName="customerSignature"></app-input>
          } @else {
          <div class="info typo-body-23">{{ authorityInfoForm.get('customerSignature')?.value ?? '-' }}</div>
          }
        </div>
      </div>

      <div class="row">
        <!-- Đính kèm hợp đồng uỷ quyền -->
        <div class="item half-50-item-cls">
          <div class="label flex typo-body-12">
            <img src="./assets/icons/paperclip.svg" alt="" />
            {{ 'MES-409' | translate }} ({{ authorityInfoForm.get('fileContractID')?.value.length }})
          </div>
          @if(editMode) {
          <app-dropzone-form
            [uploadedFiles]="authorityInfoForm.get('fileContractID')?.value"
            (fileAdded)="addFilesToForm($event)"
            (fileRemoved)="removeFileFromForm($event)"
            formArrayName="fileContractID"
          ></app-dropzone-form>
          } @else {
          <!-- Nếu có value -->
          @if(authorityInfoForm.get('fileContractID')?.value) {
          <div *ngFor="let file of authorityInfoForm.get('fileContractID')?.value" class="info typo-body-23 file">
            <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
            <div class="file-info">
              <div class="name typo-body-12">
                <div class="file-name">{{ file.name }}</div>
                <div class="size typo-body-22">
                  {{ file.size }}
                </div>
              </div>
            </div>
            <img class="download" src="./assets/icons/download.svg" alt="" />
          </div>
          } @else {
          <div class="info typo-body-23 file">
            <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
            <div class="file-info">{{ 'MES-23' | translate }}</div>
          </div>
          } }
        </div>

        <!-- Đính kèm chữ ký người uỷ quyền -->
        <div class="item half-50-item-cls">
          <div class="label flex typo-body-12">
            <img src="./assets/icons/paperclip.svg" alt="" />
            {{ 'MES-410' | translate }} ({{ authorityInfoForm.get('fileCustomerSignature')?.value.length }})
          </div>
          @if(editMode) {
          <app-dropzone-form
            [uploadedFiles]="authorityInfoForm.get('fileCustomerSignature')?.value"
            (fileAdded)="addFilesToForm($event)"
            (fileRemoved)="removeFileFromForm($event)"
            formArrayName="fileCustomerSignature"
          ></app-dropzone-form>
          } @else {
          <!-- Nếu có file -->
          @if(authorityInfoForm.get('fileCustomerSignature')?.value) {
          <div
            *ngFor="let file of authorityInfoForm.get('fileCustomerSignature')?.value"
            class="info typo-body-23 file"
          >
            <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
            <div class="file-info">
              <div class="name typo-body-12">
                <div class="file-name">{{ file.name }}</div>
                <div class="size typo-body-22">
                  {{ file.size }}
                </div>
              </div>
            </div>
            <img class="download" src="./assets/icons/download.svg" alt="" />
          </div>
          } @else {
          <div class="info typo-body-23 file">
            <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
            <div class="file-info">{{ 'MES-23' | translate }}</div>
          </div>
          } }
        </div>
      </div>

      <div class="row">
        <!-- Chữ ký người được uỷ quyền -->
        <div class="item half-50-item-cls">
          <div class="label typo-body-12">{{ 'MES-411' | translate }}</div>
          @if(editMode) {
          <app-input type="input" formControlName="authorizedPersonSignature"></app-input>
          } @else {
          <div class="info typo-body-23">{{ authorityInfoForm.get('authorizedPersonSignature')?.value ?? '-' }}</div>
          }
        </div>
      </div>

      <div class="row">
        <!-- Đính kèm chữ ký người được UQ -->
        <div class="item half-50-item-cls">
          <div class="label flex typo-body-12">
            <img src="./assets/icons/paperclip.svg" alt="" />
            {{ 'MES-412' | translate }} ({{ authorityInfoForm.get('fileAuthorizedPersonSignature')?.value.length }})
          </div>
          @if(editMode) {
          <app-dropzone-form
            [uploadedFiles]="authorityInfoForm.get('fileAuthorizedPersonSignature')?.value"
            (fileAdded)="addFilesToForm($event)"
            (fileRemoved)="removeFileFromForm($event)"
            formArrayName="fileAuthorizedPersonSignature"
          ></app-dropzone-form>
          } @else {
          <!-- Nếu có files -->
          @if(authorityInfoForm.get('fileAuthorizedPersonSignature')?.value.length) {
          <div
            *ngFor="let file of authorityInfoForm.get('fileAuthorizedPersonSignature')?.value"
            class="info typo-body-23 file"
          >
            <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
            <div class="file-info">
              <div class="name typo-body-12">
                <div class="file-name">{{ file.name }}</div>
                <div class="size typo-body-22">
                  {{ file.size }}
                </div>
              </div>
            </div>
            <img class="download" src="./assets/icons/download.svg" alt="" />
          </div>
          } @else {
          <!-- Nếu không có files -->
          <div class="info typo-body-23 file">
            <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
            <div class="file-info">{{ 'MES-23' | translate }}</div>
          </div>
          } }
        </div>
      </div>

      <!-- CMND / CCCD / HC -->
      <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-563' | translate }}</div>
        <div class="section">
          <div class="row">
            <!-- Số CMND / CCCD / HC -->
            <div class="item">
              <div class="label typo-body-12">Số {{ 'MES-358' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="identificationId"></app-input>
              } @else {
              <div class="info typo-body-23">{{ authorityInfoForm.get('identificationId')?.value ?? '-' }}</div>
              }
            </div>

            <!-- Kiểu loại-->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-359' | translate }}</div>
              @if(editMode) {
              <app-input type="dropdown" formControlName="typeID" (clickEvent)="openSearchList($event)"></app-input>
              } @else {
              <div class="info typo-body-23">
                {{ authorityInfoForm.get('typeID')?.value ?? '-' }}
              </div>
              }
            </div>
          </div>

          <div class="row">
            <!-- Ngày cấp -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-360' | translate }}</div>
              @if(editMode) {
              <app-input type="calendar" formControlName="authIdentityDate"></app-input>
              } @else {
              <div class="info typo-body-23">
                {{ authorityInfoForm.get('authIdentityDate')?.value | date : 'dd/MM/yyyy' }}
              </div>
              }
            </div>

            <!-- Nơi cấp -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-361' | translate }}</div>
              @if(editMode) {
              <app-input
                type="multiple-dropdown-province"
                formControlName="authIdentityIssuer"
                (clickEvent)="openSearchList($event)"
              ></app-input>
              } @else {
              <!-- <div class="info typo-body-23">
                {{
                  Object.keys(authorityInfoForm.get('authIdentityIssuer')?.value.children).length
                    ? authorityInfoForm.get('authIdentityIssuer')?.value.name +
                      ' - ' +
                      getProvinceName(authorityInfoForm.get('authIdentityIssuer')?.value.children.id || '')
                    : authorityInfoForm.get('authIdentityIssuer')?.value.name
                }}
              </div> -->
              <div class="info typo-body-23">
                {{ authorityInfoForm.get('authIdentityIssuer')?.value ?? '-' }}
              </div>
              }
            </div>
          </div>

          <div class="row">
            <!-- Đính kèm CMND / CCCD / HC -->
            <div class="item half-50-item-cls">
              <div class="label flex typo-body-12">
                <img src="./assets/icons/paperclip.svg" alt="" />
                {{ 'MES-391' | translate }} ({{ authorityInfoForm.get('fileIdentificationId')?.value.length }})
              </div>
              @if(editMode) {
              <app-dropzone-form
                [uploadedFiles]="authorityInfoForm.get('fileIdentificationId')?.value"
                (fileAdded)="addFilesToForm($event)"
                (fileRemoved)="removeFileFromForm($event)"
                formArrayName="fileIdentificationId"
              ></app-dropzone-form>
              } @else {
              <!-- Nếu có files -->
              @if(authorityInfoForm.get('fileIdentificationId')?.value.length) {
              <div
                *ngFor="let file of authorityInfoForm.get('fileIdentificationId')?.value"
                class="info typo-body-23 file"
              >
                <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                <div class="file-info">
                  <div class="name typo-body-12">
                    <div class="file-name">{{ file.name }}</div>
                    <div class="size typo-body-22">
                      {{ file.size }}
                    </div>
                  </div>
                </div>
                <img class="download" src="./assets/icons/download.svg" alt="" />
              </div>
              } @else {
              <div class="info typo-body-23 file">
                <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                <div class="file-info">{{ 'MES-23' | translate }}</div>
              </div>
              } }
            </div>
          </div>
        </div>
      </app-section-wrapper-layout>

      <div class="row">
        <!-- Thời gian uỷ quyền -->
        <div class="item">
          <div class="label typo-body-12">{{ 'MES-413' | translate }}</div>
          @if(editMode) {
          <div [ngStyle]="{ display: 'flex', 'align-items': 'center', width: '100%', gap: '16px' }">
            <!-- Nếu giá trị là "Có uỷ quyền" -->
            @if(CONVERT_AUTHORITY_TO_BOOLEAN[authorityInfoForm.get('authStatus')?.value]) {
            <app-input [style.flex]="'1'" type="calendar" formControlName="authFromDate"></app-input>
            <app-input [style.flex]="'1'" type="calendar" formControlName="authToDate"></app-input>
            } @else {
            <!-- Nếu giá trị là "Không uỷ quyền" -->
            <app-input [style.flex]="'1'" type="input" [readonly]="true" formControlName="authFromDate"></app-input>
            <app-input [style.flex]="'1'" type="input" [readonly]="true" formControlName="authFromDate"></app-input>
            }
          </div>
          } @else {
          <div class="info typo-body-23">
            {{ authorityInfoForm.get('authFromDate')?.value | date : 'dd/MM/yyyy' }} -
            {{ authorityInfoForm.get('authToDate')?.value | date : 'dd/MM/yyyy' }}
          </div>
          }
        </div>
      </div>

      <!-- Uỷ quyền -->
      <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-309' | translate }}</div>
        <div class="section">
          <div class="row">
            <!-- UQ - Toàn bộ -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-414' | translate }} - Toàn bộ</div>
              <div class="input-wrapper">
                <div class="info typo-body-23">
                  <mat-checkbox
                    [ngClass]="{
                    'disable-checkbox': !editMode,
                  }"
                    (click)="editMode ? null : $event.preventDefault()"
                    (change)="onAllAuthorityChange($event)"
                    formControlName="authAll"
                    class="checkbox-cls"
                    >{{ 'MES-309' | translate }}</mat-checkbox
                  >
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <!-- UQ - Đặt lệnh GD -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-414' | translate }} - Đặt lệnh GD</div>
              <div class="input-wrapper">
                <div class="info typo-body-23">
                  <mat-checkbox
                    [ngClass]="{
                    'disable-checkbox': !editMode || editMode && !CONVERT_AUTHORITY_TO_BOOLEAN[authorityInfoForm.get('status')?.value]  ,
                  }"
                    (change)="onIndividualAuthorityChange()"
                    formControlName="authCommand"
                    class="checkbox-cls"
                    >{{ 'MES-309' | translate }}</mat-checkbox
                  >
                </div>
              </div>
            </div>

            <!-- UQ - Gửi rút tiền -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-414' | translate }} - Gửi rút tiền</div>
              <div class="input-wrapper">
                <div class="info typo-body-23">
                  <mat-checkbox
                    [ngClass]="{
                    'disable-checkbox': !editMode || editMode && !CONVERT_AUTHORITY_TO_BOOLEAN[authorityInfoForm.get('status')?.value]  ,
                  }"
                    (change)="onIndividualAuthorityChange()"
                    formControlName="authMoney"
                    class="checkbox-cls"
                    >{{ 'MES-309' | translate }}</mat-checkbox
                  >
                </div>
              </div>
            </div>

            <!-- UQ - Ứng trước -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-414' | translate }} - Ứng trước</div>
              <div class="input-wrapper">
                <div class="info typo-body-23">
                  <mat-checkbox
                    [ngClass]="{
                    'disable-checkbox': !editMode || editMode && !CONVERT_AUTHORITY_TO_BOOLEAN[authorityInfoForm.get('status')?.value]  ,
                  }"
                    (change)="onIndividualAuthorityChange()"
                    formControlName="authAdvance"
                    class="checkbox-cls"
                    >{{ 'MES-309' | translate }}</mat-checkbox
                  >
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <!-- UQ - Lưu ký -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-414' | translate }} - Lưu ký</div>
              <div class="input-wrapper">
                <div class="info typo-body-23">
                  <mat-checkbox
                    [ngClass]="{
                    'disable-checkbox': !editMode || editMode && !CONVERT_AUTHORITY_TO_BOOLEAN[authorityInfoForm.get('status')?.value]  ,
                  }"
                    (change)="onIndividualAuthorityChange()"
                    formControlName="authDepository"
                    class="checkbox-cls"
                    >{{ 'MES-309' | translate }}</mat-checkbox
                  >
                </div>
              </div>
            </div>

            <!-- UQ - Dịch vụ tài chính -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-414' | translate }} - Dịch vụ tài chính</div>
              <div class="input-wrapper">
                <div class="info typo-body-23">
                  <mat-checkbox
                    [ngClass]="{
                    'disable-checkbox': !editMode || editMode && !CONVERT_AUTHORITY_TO_BOOLEAN[authorityInfoForm.get('status')?.value]  ,
                  }"
                    (change)="onIndividualAuthorityChange()"
                    formControlName="authFinanceService"
                    class="checkbox-cls"
                    >{{ 'MES-309' | translate }}</mat-checkbox
                  >
                </div>
              </div>
            </div>

            <!-- UQ - Gửi rút Ck -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-414' | translate }} - Gửi rút CK</div>
              <div class="input-wrapper">
                <div class="info typo-body-23">
                  <mat-checkbox
                    [ngClass]="{
                    'disable-checkbox': !editMode || editMode && !CONVERT_AUTHORITY_TO_BOOLEAN[authorityInfoForm.get('status')?.value]  ,
                  }"
                    (change)="onIndividualAuthorityChange()"
                    formControlName="authStock"
                    class="checkbox-cls"
                    >{{ 'MES-309' | translate }}</mat-checkbox
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </app-section-wrapper-layout>
    </div>
  </form>
</div>
<!--                     [disabled]="
                      editMode === false &&
                      CONVERT_AUTHORITY_TO_BOOLEAN[authorityInfoForm.get('status')?.value] === false
                    "
                    (click)="editMode && $event.preventDefault()" -->
