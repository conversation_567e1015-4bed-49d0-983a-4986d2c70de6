import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { take, takeUntil } from 'rxjs';
import { DatePickerNavigationFullDateComponent } from 'src/app/shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';
import { DropdownMultipleLevelV2Component } from 'src/app/shared/components/dropdown-multiple-level/dropdown-multiple-level-v2/dropdown-multiple-level-v2.component';
import { NationalityDropdownComponent } from 'src/app/shared/components/nationality-dropdown/nationality-dropdown.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { MY_DATE_FORMAT } from 'src/app/shared/constants/date-picker';
import {
  CONVERT_AUTHORITY_STATUS_TO_LABEL,
  CONVERT_AUTHORITY_TO_BOOLEAN,
  ListOfTypeID,
  ListOptionAuthority,
} from '../../../constants/customers';
import { LIST_OF_NATIONALITY } from 'src/app/shared/constants/nationality';
import { LIST_PROVINCE } from 'src/app/shared/constants/province';
import { DestroyService, DialogService } from 'src/app/core/services';
import { PopoverConfig } from '../personal-info-detail/personal-info-detail.component';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { LocationService } from 'src/app/core/services/location.service';

/**
 * AuthorityInfoDetailComponent
 */
@Component({
  selector: 'app-authority-info-detail',
  templateUrl: './authority-info-detail.component.html',
  styleUrl: './authority-info-detail.component.scss',
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE],
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
  ],
})
export class AuthorityInfoDetailComponent implements OnInit {
  @Input() data: any;
  @Output() formChanged = new EventEmitter<void>();
  editMode = false;

  Object = Object;
  headerCalendar = DatePickerNavigationFullDateComponent;
  CONVERT_AUTHORITY_TO_BOOLEAN = CONVERT_AUTHORITY_TO_BOOLEAN;
  CONVERT_AUTHORITY_STATUS_TO_LABEL = CONVERT_AUTHORITY_STATUS_TO_LABEL;

  initialFormValues: any;
  authorityInfoForm!: FormGroup;

  optionList = [
    {
      name: 'Cục Cảnh sát ĐKQL cư trú và DLQG về dân cư',
      id: '1',
      children: [],
      config: 'level1',
    },
    {
      name: 'Cục Cảnh sát QLHC về TTXH',
      id: '2',
      children: [],
      config: 'level1',
    },
    {
      name: 'Công an Tỉnh/Thành phố',
      id: '3',
      children: LIST_PROVINCE,
      config: 'level1',
    },
    {
      name: 'Cục Quản lý xuất nhập cảnh',
      id: '4',
      children: [],
      config: 'level1',
    },
  ];

  locations: any[] = [];
  districts: any[] = [];
  wards: any[] = [];

  /**
   * Constructor
   * @param fb fb
   * @param popoverService popoverService
   * @param cdf
   * @param destroy
   * @param dialogService
   * @param _destroy
   * @param locationService
   */
  constructor(
    private readonly fb: FormBuilder,
    private readonly popoverService: PopoverService,
    public cdf: ChangeDetectorRef,
    private readonly destroy: DestroyService,
    private readonly dialogService: DialogService,
    private readonly _destroy: DestroyService,
    private readonly locationService: LocationService
  ) {}

  /**
   * ngOninit
   */
  ngOnInit(): void {
    this.initForm();

    this.authorityInfoForm.valueChanges.pipe(takeUntil(this._destroy)).subscribe(() => {
      if (this.hasFormChanged()) {
        this.formChanged.emit();
      }
    });

    // Get locations
    // this.locationService
    //   .getLocations()
    //   .pipe(takeUntil(this._destroy))
    //   .subscribe((data) => {
    //     this.locations = data;
    //     // Check initial value of city and update districts
    //     const initialCity = this.getFormControl('city')?.value;
    //     if (initialCity) {
    //       const city = this.locations.find((location) => location.name === initialCity);
    //       this.districts = city ? city.district : [];
    //     }
    //     // Check initial value of district and update ward
    //     const initialDistrict = this.getFormControl('district')?.value;
    //     if (initialDistrict) {
    //       const district = this.districts.find((d) => d.name === initialDistrict);
    //       this.wards = district ? district.ward : [];
    //     }
    //   });

    this.authorityInfoForm
      .get('city')
      ?.valueChanges.pipe(takeUntil(this._destroy))
      .subscribe((city) => {
        this.updateDistricts(city);
      });

    this.authorityInfoForm
      .get('district')
      ?.valueChanges.pipe(takeUntil(this._destroy))
      .subscribe((district) => {
        this.updateWards(district);
      });
  }

  /**
   * initForm
   */
  initForm() {
    const {
      customerName,
      authStatus,
      authEmail,
      authTelephone,
      authAddress,
      contractID,
      customerSignature,
      authorizedPersonSignature,
      identificationId,
      typeID,
      authIdentityDate,
      authIdentityIssuer,
      authFromDate,
      authToDate,
      authCommand,
      authMoney,
      authAdvance,
      authDepository,
      authFinanceService,
      authStock,
      authAll,
    } = this.data;

    // nation,
    // city,
    // district,
    // ward,

    this.authorityInfoForm = this.fb.group({
      authStatus: authStatus ? CONVERT_AUTHORITY_STATUS_TO_LABEL[authStatus.toString()] : null,
      customerName: customerName ?? null,
      // areaCode: areaCode + ' ' + phoneNumber,
      areaCode: authTelephone ?? null, // SĐT
      authEmail: [
        authEmail ?? null,
        [Validators.email, Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')],
      ],
      authAddress,
      contractID: contractID?.name ?? null, // Hợp đồng uỷ quyền
      fileContractID: this.fb.array(
        contractID
          ? contractID.files.map((file: any) =>
              this.fb.group({
                name: `${file.name}.${file.type}`,
                size: file.size,
              })
            )
          : []
      ),
      customerSignature: customerSignature?.name ?? null, // Chữ ký người uỷ quyển
      fileCustomerSignature: this.fb.array(
        customerSignature
          ? customerSignature.files.map((file: any) =>
              this.fb.group({
                name: `${file.name}.${file.type}`,
                size: file.size,
              })
            )
          : []
      ),
      authorizedPersonSignature: authorizedPersonSignature?.name, // Chữ ký người được uỷ quyển
      fileAuthorizedPersonSignature: this.fb.array(
        authorizedPersonSignature
          ? authorizedPersonSignature.files.map((file: any) =>
              this.fb.group({
                name: `${file.name}.${file.type}`,
                size: file.size,
              })
            )
          : []
      ),
      typeID,
      authIdentityDate: this.convertToDate(authIdentityDate),
      authIdentityIssuer,
      identificationId: identificationId?.name ?? '-', // Đính kèm CMND / CCCD / HC
      fileIdentificationId: this.fb.array(
        identificationId
          ? identificationId.files.map((file: any) =>
              this.fb.group({
                name: `${file.name}.${file.type}`,
                size: file.size,
              })
            )
          : []
      ),
      authFromDate: this.convertToDate(authFromDate),
      authToDate: this.convertToDate(authToDate),
      authCommand, // UQ - Đặt lệnh GD
      authMoney, // UQ - Gửi rút tiền
      authAdvance, // UQ - Ứng trước
      authDepository, // UQ - Lưu ký
      authFinanceService, // UQ - Dịch vụ tài chính
      authStock, // UQ - Gửi rút CK
      authAll, // UQ - Toàn bộ
    });

    this.initialFormValues = this.authorityInfoForm.getRawValue();
    console.log('initialFormValues', this.initialFormValues);
  }

  /**
   * updateDistricts
   * @param cityName cityName
   */
  updateDistricts(cityName: string): void {
    const city = this.locations.find((location) => location.name === cityName);
    this.districts = city ? city.district : [];
    this.resetDataDistrict();
    this.wards = [];
    this.resetDataWard();
  }

  /**
   * updateWards
   * @param districtName districtName
   */
  updateWards(districtName: string): void {
    const district = this.districts.find((d) => d.name === districtName);
    this.wards = district ? district.ward : [];
    this.resetDataWard();
  }

  /**
   * hasFormChanged
   */
  hasFormChanged() {
    const currentValues = this.authorityInfoForm.getRawValue();
    return !this.isEqual(this.initialFormValues, currentValues);
  }

  /**
   * Check if two objects are equal
   * @param obj1
   * @param obj2
   */
  private isEqual(obj1: any, obj2: any): boolean {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  }

  /**
   * save
   */
  save() {
    if (this.hasFormChanged()) {
      const dialogRef = this.dialogService.openPopUp(DeleteNotificationComponent, {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-88',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      });
      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((result) => {
          if (result) {
            this.saveFormData();
            this.toggleEditMode();
          }
        });
    } else {
      this.toggleEditMode();
    }
  }

  /**
   * cancel
   */
  cancel() {
    if (this.hasFormChanged()) {
      const dialogRef = this.dialogService.openPopUp(DeleteNotificationComponent, {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-97',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
          isHideMessage: true,
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      });
      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((result) => {
          if (result) {
            this.authorityInfoForm.patchValue(this.initialFormValues);
            this.patchFormArray('fileContractID', this.initialFormValues.fileContractID);
            this.patchFormArray('fileCustomerSignature', this.initialFormValues.fileCustomerSignature);
            this.patchFormArray('fileAuthorizedPersonSignature', this.initialFormValues.fileAuthorizedPersonSignature);
            this.patchFormArray('fileIdentificationId', this.initialFormValues.fileIdentificationId);
            this.toggleEditMode();
          }
        });
    } else {
      this.toggleEditMode();
    }
  }

  /**
   * @param arrayName arrayName
   * @param values values
   */
  patchFormArray(arrayName: string, values: any[]) {
    const formArray = this.authorityInfoForm.get(arrayName) as FormArray;
    formArray.clear();
    values.forEach((value) => formArray.push(this.fb.group(value)));
  }

  /**
   * saveFormData
   */
  saveFormData() {
    this.initialFormValues = this.authorityInfoForm.getRawValue();
  }

  /**
   * toggleEditMode
   */
  toggleEditMode() {
    this.editMode = !this.editMode;
  }

  /**
   * convertToDate
   * @param dateString
   */
  convertToDate(dateString: string): Date | null {
    return dateString ? new Date(dateString) : null;
  }

  /**
   * onAllAuthorityChange
   * @param event
   */
  onAllAuthorityChange(event: any): void {
    if (!this.editMode) return;
    const isCheck = event.checked;
    this.authorityInfoForm.patchValue({
      status: CONVERT_AUTHORITY_STATUS_TO_LABEL[isCheck],
      entryTrading: isCheck,
      sendWithdrawal: isCheck,
      advance: isCheck,
      depository: isCheck,
      financeService: isCheck,
      sendWithdrawalOfSecurities: isCheck,
    });

    if (!isCheck) {
      this.authorityInfoForm.patchValue({
        authFromDate: '',
        authToDate: '',
      });
    }
  }

  /**
   * check all or not selection
   */
  onIndividualAuthorityChange(): void {
    if (!this.editMode) return;

    const { entryTrading, sendWithdrawal, advance, depository, financeService, sendWithdrawalOfSecurities } =
      this.authorityInfoForm.value;

    const allChecked =
      entryTrading && sendWithdrawal && advance && depository && financeService && sendWithdrawalOfSecurities;

    // Uncheck the allAuthority checkbox if not all individual checkboxes are checked
    if (!allChecked) {
      const isUnCheckAll =
        entryTrading ?? sendWithdrawal ?? advance ?? depository ?? financeService ?? sendWithdrawalOfSecurities;
      this.authorityInfoForm.patchValue({
        allAuthority: false,
        status: isUnCheckAll ? 'Có uỷ quyền' : 'Không uỷ quyền',
      });
    } else {
      this.authorityInfoForm.patchValue({ allAuthority: true, status: 'Có uỷ quyền' });
    }
  }

  /**
   * changeDateEvent
   * @param event
   */
  changeDateEvent(event: any) {
    const newAreaCode = event.date;
    this.authorityInfoForm.patchValue({ areaCode: newAreaCode });
  }

  /**
   * openSearchList
   * @param data
   * @param data.event
   * @param data.element
   * @param data.formControlName
   */
  openSearchList(data: { event: Event; element: HTMLElement; formControlName: string }) {
    const { element, formControlName } = data;
    const originWidth = element.getBoundingClientRect().width;
    const config = this.getPopoverConfig(formControlName);

    const popoverData = {
      origin: element,
      width: `${originWidth}px`,
      data: this.authorityInfoForm.value,
      componentConfig: config,
    };

    let ref: any;
    switch (formControlName) {
      case 'status':
      case 'gender':
      case 'typeID':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: SearchListComponent,
        });
        break;

      case 'nation':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: NationalityDropdownComponent,
          position: 2,
        });
        break;

      case 'city':
      case 'district':
      case 'ward':
        ref = this.popoverService.open<any>({
          ...popoverData,
          position: 2,
          content: SearchListComponent,
          height: '200px',
          panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
        });
        break;

      case 'authIdentityIssuer':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: DropdownMultipleLevelV2Component,
        });
        break;
    }

    ref.afterClosed$.pipe(take(1)).subscribe((result: any) => this.handlePopoverResult(result, formControlName));
  }

  /**
   * Returns a PopoverConfig object based on the form control name provided.
   * @param {string} formControlName
   */
  private getPopoverConfig(formControlName: string): PopoverConfig {
    const config: PopoverConfig = { multiple: false };
    switch (formControlName) {
      case 'status':
        return {
          ...config,
          searchKey: 'value',
          isSearch: false,
          value: [this.getFormControl('status')?.value === 'Có uỷ quyền'],
          displayOptionFn: (v: any) => v.name,
          options: ListOptionAuthority,
        };

      case 'nation':
        return {
          ...config,
          searchKey: 'name',
          isSearch: true,
          isShowLogo: false,
          isShowAreaCode: false,
          displayOptionFn: (v: any) => v.name,
          value: [this.getFormControl('nation')?.value],
          options: LIST_OF_NATIONALITY,
        };

      case 'city':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          displayOptionFn: (v: any) => {
            return v.name;
          },
          value: [this.getFormControl('city')?.value],
          options: this.locations,
        };

      case 'district':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          displayOptionFn: (v: any) => {
            return v.name;
          },
          value: [this.getFormControl('district')?.value],
          options: this.districts,
        };

      case 'ward':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          displayOptionFn: (v: any) => {
            return v.name;
          },
          value: [this.getFormControl('ward')?.value],
          options: this.wards,
        };

      case 'typeID':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          displayOptionFn: (v: any) => {
            return v.name;
          },
          value: [this.getFormControl('typeID')?.value],
          options: ListOfTypeID,
        };

      case 'authIdentityIssuer':
        return {
          ...config,
          searchKey: 'name',
          isSearch: false,
          displayOptionFn: (v: any) => v.name,
          displayValueFn: (v: any) =>
            Object.keys(v.children).length
              ? v.name + ' - ' + LIST_PROVINCE.find((p) => p.id === v.children.id)!.name
              : v.name,
          key: 'children',
          isShowSearch: false,
          isUpdateList: true,
          options: this.optionList,
          value: [this.getFormControl('authIdentityIssuer')?.value],
          configChildren: {
            level1: {
              isShowSearch: true,
              isShowBtn: false,
              searchKey: 'name',
              placeholder: 'Tìm kiếm',
              displayOptionFn: (v: any) => v.name,
            },
            level2: {
              isShowSearch: false,
              isShowBtn: true,
              searchKey: 'name',
              placeholder: '',
              displayOptionFn: (v: any) => v.name,
            },
          },
          initialOptions: this.optionList,
        };

      default:
        return config;
    }
  }

  /**
   * handlePopoverResult
   * @param result
   * @param formControlName
   */
  private handlePopoverResult(result: any, formControlName: string): void {
    if (!result.data) return;
    const selectedValue = result?.data;
    let valueReturn: any;
    switch (formControlName) {
      case 'status': {
        const itemValue = selectedValue.item[0].value;
        valueReturn = itemValue ? 'Có uỷ quyền' : 'Không uỷ quyền';

        this.authorityInfoForm.patchValue({
          status: valueReturn,
          entryTrading: !!itemValue,
          sendWithdrawal: !!itemValue,
          advance: !!itemValue,
          depository: !!itemValue,
          financeService: !!itemValue,
          sendWithdrawalOfSecurities: !!itemValue,
          allAuthority: !!itemValue,
        });

        if (!itemValue) {
          this.authorityInfoForm.patchValue({
            authFromDate: '',
            authToDate: '',
          });
        }
        return valueReturn;
      }

      case 'nation': {
        valueReturn = selectedValue.item[0].name;
        // FIX ME: Đợi Api của các nước

        this.authorityInfoForm.patchValue({
          nation: valueReturn,
        });
        this.resetDataCity();
        this.resetDataDistrict();
        this.resetDataWard();
        return valueReturn;
      }

      case 'city': {
        valueReturn = selectedValue.item[0].name;
        this.authorityInfoForm.patchValue({
          city: valueReturn,
        });
        return valueReturn;
      }

      case 'district': {
        valueReturn = selectedValue.item[0].name;
        this.authorityInfoForm.patchValue({
          district: valueReturn,
        });
        return valueReturn;
      }

      case 'ward': {
        valueReturn = selectedValue.item[0].name;
        this.authorityInfoForm.patchValue({
          ward: valueReturn,
        });
        return valueReturn;
      }

      case 'typeID': {
        valueReturn = selectedValue.item[0].name;

        this.authorityInfoForm.patchValue({
          typeID: valueReturn,
        });
        return valueReturn;
      }

      case 'authIdentityIssuer': {
        const itemSelected = !result.data?.children?.length ? {} : result.data.children.find((c: any) => c.isSelected);

        const dataConfig = {
          name: result.data?.name,
          children: !result.data?.children?.length
            ? {}
            : {
                id: itemSelected.id,
              },
        };
        valueReturn = dataConfig;

        this.authorityInfoForm.patchValue({
          authIdentityIssuer: valueReturn,
        });

        return valueReturn;
      }
    }
  }

  /**
   * addFilesToForm
   * @param event
   * @param event.files
   * @param event.formArrayName
   */
  addFilesToForm(event: { files: any[]; formArrayName: string }) {
    const { files, formArrayName } = event;
    if (!Array.isArray(files)) {
      console.error('Files is not an array:', files);
      return;
    }

    const formArray = this.authorityInfoForm.get(formArrayName) as FormArray;
    files.forEach((file) => {
      formArray.push(
        this.fb.group({
          name: file.name,
          size: file.size,
        })
      );
    });

    // Manually trigger change detection
    this.authorityInfoForm.setControl(formArrayName, formArray);
  }

  /**
   * removeFileFromForm
   * @param event
   * @param event.idx
   * @param event.formArrayName
   */
  removeFileFromForm(event: { idx: number; formArrayName: string }) {
    const { idx, formArrayName } = event;
    const formArray = this.authorityInfoForm.get(formArrayName) as FormArray;

    // Ensure formArray is properly initialized
    if (!formArray) {
      console.error('FormArray not found:', formArrayName);
      return;
    }

    // Validate index
    if (idx >= 0 && idx < formArray.length) {
      formArray.removeAt(idx);

      this.authorityInfoForm.setControl(formArrayName, formArray);
    } else {
      console.error('Invalid index:', idx);
    }
  }

  /**
   * getProvinceName
   * @param {string} provinceId
   */
  getProvinceName(provinceId: string): string | undefined {
    const province = LIST_PROVINCE.find((p) => p.id === provinceId);
    return province ? province.name : undefined;
  }

  /**
   * GetFormControl
   * @param field
   * @returns {any} control from form
   */
  getFormControl(field: string) {
    return this.authorityInfoForm.get(field) as FormControl;
  }

  /**
   * ResetDataCity
   */
  resetDataCity() {
    this.getFormControl('city').setValue(null);
  }

  /**
   * ResetDataDistrict
   */
  resetDataDistrict() {
    this.getFormControl('district').setValue(null);
  }

  /**
   * ResetDataWard
   */
  resetDataWard() {
    this.getFormControl('ward').setValue(null);
  }
}
