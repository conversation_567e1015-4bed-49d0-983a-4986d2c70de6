import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { PopoverConfig } from '../personal-info-detail/personal-info-detail.component';
import { take, takeUntil } from 'rxjs';
import { BankInfoPopUpComponent } from 'src/app/shared/components/bank-info-popup/bank-info-popup.component';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { DestroyService, DialogService } from 'src/app/core/services';
import { selecAllBankList$ } from '../../../stores/customer.selector';
import { Store } from '@ngrx/store';
import { IBankInfo } from '../../../model/customer';

const DEFAULT_BANK_DETAILS = {
  bankName: '',
  name: '',
  id: '',
  shortName: '',
};

/**
 * BankInfoDetailComponent
 */
@Component({
  selector: 'app-bank-info-detail',
  templateUrl: './bank-info-detail.component.html',
  styleUrl: './bank-info-detail.component.scss',
})
export class BankInfoDetailComponent implements OnInit {
  @Input() data: any;

  @Output() formChanged = new EventEmitter<void>();
  editMode = false;

  initialFormValues: any;
  bankInfoForm!: FormGroup;
  listBank!: IBankInfo[];

  updatedBankAccounts: any[] = [];

  /**
   * Constructor
   * @param fb FormBuilder
   * @param popoverService popoverService
   * @param dialogService  dialogService
   * @param _destroy _destroy
   * @param store Store
   */
  constructor(
    private readonly fb: FormBuilder,
    private readonly popoverService: PopoverService,
    private readonly dialogService: DialogService,
    private readonly _destroy: DestroyService,
    private readonly store: Store
  ) {}

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.initForm();
    this.getListBank();
    this.bankInfoForm.valueChanges.pipe(takeUntil(this._destroy)).subscribe(() => {
      if (this.hasFormChanged()) {
        this.formChanged.emit();
      }
    });
  }

  /**
   * getListBank
   */
  getListBank() {
    this.store
      .select(selecAllBankList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((list) => {
        this.listBank = list;
      });
  }

  /**
   * initForm
   */
  initForm() {
    const { bankAccounts1, bankAccounts2, bankAccounts3, bankAccounts4, bankAccounts5 } = this.data;

    this.bankInfoForm = this.fb.group({
      accountNumber01: bankAccounts1?.accountNumber,
      customerName01: bankAccounts1?.customerName,
      beneficiaryBank01: bankAccounts1?.beneficiaryBank ?? DEFAULT_BANK_DETAILS,
      labelBeneficiaryBank01: '',

      accountNumber02: bankAccounts2?.accountNumber,
      customerName02: bankAccounts2?.customerName,
      beneficiaryBank02: bankAccounts2?.beneficiaryBank ?? DEFAULT_BANK_DETAILS,
      labelBeneficiaryBank02: '',

      accountNumber03: bankAccounts3?.accountNumber,
      customerName03: bankAccounts3?.customerName,
      beneficiaryBank03: bankAccounts3?.beneficiaryBank ?? DEFAULT_BANK_DETAILS,
      labelBeneficiaryBank03: '',

      accountNumber04: bankAccounts4?.accountNumber,
      customerName04: bankAccounts4?.customerName,
      beneficiaryBank04: bankAccounts4?.beneficiaryBank ?? DEFAULT_BANK_DETAILS,
      labelBeneficiaryBank04: '',

      accountNumber05: bankAccounts5?.accountNumber,
      customerName05: bankAccounts5?.customerName,
      beneficiaryBank05: bankAccounts5?.beneficiaryBank ?? DEFAULT_BANK_DETAILS,
      labelBeneficiaryBank05: '',
    });

    const labels = ['01', '02', '03', '04', '05'];
    const patchValues: any = {};

    // patchValue of formControl label in input
    labels.forEach((label) => {
      const bankValue = this.getFormControl(`beneficiaryBank${label}`)?.value;
      patchValues[`labelBeneficiaryBank${label}`] = bankValue?.name ? `${bankValue.name} - ${bankValue.bankName}` : '';
    });
    this.bankInfoForm.patchValue(patchValues);

    this.initialFormValues = this.bankInfoForm.getRawValue();
  }

  /**
   * save
   */
  save() {
    if (this.hasFormChanged()) {
      const dialogRef = this.dialogService.openPopUp(DeleteNotificationComponent, {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-88',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      });
      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((result) => {
          if (result) {
            this.saveFormData();
            this.toggleEditMode();
          }
        });
    } else {
      this.toggleEditMode();
    }
  }

  /**
   * cancel
   */
  cancel() {
    if (this.hasFormChanged()) {
      const dialogRef = this.dialogService.openPopUp(DeleteNotificationComponent, {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-97',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
          isHideMessage: true,
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      });
      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((result) => {
          if (result) {
            this.bankInfoForm.patchValue(this.initialFormValues);
            this.toggleEditMode();
          }
        });
    } else {
      this.toggleEditMode();
    }
  }

  /**
   * saveFormData
   */
  saveFormData() {
    this.initialFormValues = this.bankInfoForm.getRawValue();
  }

  /**
   * hasFormChanged
   */
  hasFormChanged() {
    const currentValues = this.bankInfoForm.getRawValue();
    return !this.isEqual(this.initialFormValues, currentValues);
  }

  /**
   * Check if two objects are equal
   * @param obj1
   * @param obj2
   */
  private isEqual(obj1: any, obj2: any): boolean {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  }

  /**
   * toggleEditMode
   */
  toggleEditMode() {
    this.editMode = !this.editMode;
  }

  /**
   * openSearchList
   * @param data
   * @param data.event
   * @param data.element
   * @param data.formControlName
   */
  openSearchList(data: { event: Event; element: HTMLElement; formControlName: string }) {
    const { element, formControlName } = data;
    const originWidth = element.getBoundingClientRect().width;
    const config = this.getPopoverConfig(formControlName);

    const popoverData = {
      origin: element,
      width: `${originWidth}px`,
      componentConfig: config,
    };

    let ref: any;
    switch (formControlName) {
      case 'labelBeneficiaryBank01':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: BankInfoPopUpComponent,
          data: this.data.bankAccounts1,
          position: 2,
          height: 272,
          panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
        });
        break;

      case 'labelBeneficiaryBank02':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: BankInfoPopUpComponent,
          data: this.data.bankAccounts2,
          position: 2,
          height: 272,
          panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
        });
        break;

      case 'labelBeneficiaryBank03':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: BankInfoPopUpComponent,
          data: this.data.bankAccounts3,
          position: 2,
          height: 272,
          panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
        });
        break;

      case 'labelBeneficiaryBank04':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: BankInfoPopUpComponent,
          data: this.data.bankAccounts4,
          position: 2,
          height: 272,
          panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
        });
        break;

      case 'labelBeneficiaryBank05':
        ref = this.popoverService.open<any>({
          ...popoverData,
          content: BankInfoPopUpComponent,
          data: this.data.bankAccounts5,
          position: 2,
          height: 272,
          panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
        });
        break;
    }

    ref.afterClosed$.pipe(take(1)).subscribe((result: any) => this.handlePopoverResult(result, formControlName));
  }

  /**
   * Returns a PopoverConfig object based on the form control name provided.
   * @param {string} formControlName
   */
  private getPopoverConfig(formControlName: string): PopoverConfig {
    const config: PopoverConfig = { multiple: false };
    switch (formControlName) {
      case 'labelBeneficiaryBank01':
        return {
          ...config,
          type: 'dropdown-form',
          value: [{ beneficiaryBank: this.getFormControl('beneficiaryBank01')?.value }],
        };

      case 'labelBeneficiaryBank02':
        return {
          ...config,
          type: 'dropdown-form',
          value: [{ beneficiaryBank: this.getFormControl('beneficiaryBank02')?.value }],
        };

      case 'labelBeneficiaryBank03':
        return {
          ...config,
          type: 'dropdown-form',
          value: [{ beneficiaryBank: this.getFormControl('beneficiaryBank03')?.value }],
        };

      case 'labelBeneficiaryBank04':
        return {
          ...config,
          type: 'dropdown-form',
          value: [{ beneficiaryBank: this.getFormControl('beneficiaryBank04')?.value }],
        };

      case 'labelBeneficiaryBank05':
        return {
          ...config,
          type: 'dropdown-form',
          value: [{ beneficiaryBank: this.getFormControl('beneficiaryBank05')?.value }],
        };
      default:
        return config;
    }
  }

  /**
   * handlePopoverResult
   * @param result
   * @param formControlName
   */
  private handlePopoverResult(result: any, formControlName: string): void {
    if (!result.data) return;

    const selectedValue = result?.data.beneficiaryBank;

    switch (formControlName) {
      case 'labelBeneficiaryBank01': {
        this.handleBeneficiaryBank01(selectedValue);
        break;
      }

      case 'labelBeneficiaryBank02': {
        this.handleBeneficiaryBank02(selectedValue);
        break;
      }

      case 'labelBeneficiaryBank03': {
        this.handleBeneficiaryBank03(selectedValue);
        break;
      }

      case 'labelBeneficiaryBank04': {
        this.handleBeneficiaryBank04(selectedValue);
        break;
      }

      case 'labelBeneficiaryBank05': {
        this.handleBeneficiaryBank05(selectedValue);
        break;
      }
    }
  }

  private handleBeneficiaryBank01(selectedValue: any): void {
    this.bankInfoForm.patchValue({
      beneficiaryBank01: selectedValue.name ? selectedValue : DEFAULT_BANK_DETAILS,
      labelBeneficiaryBank01: selectedValue.name ? `${selectedValue.name} - ${selectedValue.bankName}` : '',
    });
  }

  private handleBeneficiaryBank02(selectedValue: any): void {
    this.bankInfoForm.patchValue({
      beneficiaryBank02: selectedValue.name ? selectedValue : DEFAULT_BANK_DETAILS,
      labelBeneficiaryBank02: selectedValue.name ? `${selectedValue.name} - ${selectedValue.bankName}` : '',
    });
  }

  private handleBeneficiaryBank03(selectedValue: any): void {
    this.bankInfoForm.patchValue({
      beneficiaryBank03: selectedValue.name ? selectedValue : DEFAULT_BANK_DETAILS,
      labelBeneficiaryBank03: selectedValue.name ? `${selectedValue.name} - ${selectedValue.bankName}` : '',
    });
  }

  private handleBeneficiaryBank04(selectedValue: any): void {
    this.bankInfoForm.patchValue({
      beneficiaryBank04: selectedValue.name ? selectedValue : DEFAULT_BANK_DETAILS,
      labelBeneficiaryBank04: selectedValue.name ? `${selectedValue.name} - ${selectedValue.bankName}` : '',
    });
  }

  private handleBeneficiaryBank05(selectedValue: any): void {
    this.bankInfoForm.patchValue({
      beneficiaryBank05: selectedValue.name ? selectedValue : DEFAULT_BANK_DETAILS,
      labelBeneficiaryBank05: selectedValue.name ? `${selectedValue.name} - ${selectedValue.bankName}` : '',
    });
  }

  /**
   * GetFormControl
   * @param field
   * @returns {any} control from form
   */
  getFormControl(field: string) {
    return this.bankInfoForm.get(field) as FormControl;
  }
}
