<div class="bank-detail-wrapper">
  <form [formGroup]="bankInfoForm" class="bank-info">
    <div class="title-header">
      <div class="typo-body-19 title">{{ 'MES-08' | translate }}</div>
      <!-- Button chỉnh sửa
      @if(!editMode) {
      <div class="edit-btn-wrapper" (click)="toggleEditMode()">
        <mat-icon class="edit-btn" [svgIcon]="'icon:edit-2'"></mat-icon>
        <div class="edit-content-btn typo-body-9">{{ 'MES-340' | translate }}</div>
      </div>
      } @else {
      Button Lưu/Huỷ
      <div class="edit-btn-wrapper-save">
        <div class="button save typo-body-9" (click)="save()">
          Lưu
          <mat-icon [svgIcon]="'icon:tick-circle-icon'"></mat-icon>
        </div>
        <div class="button cancel typo-body-9" (click)="cancel()">
          Huỷ
          <mat-icon [svgIcon]="'icon:x-cross-red-icon'"></mat-icon>
        </div>
      </div>
      } -->
    </div>

    <div class="content">
      <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-397' | translate }} (01)</div>
        <!-- TÀI KHOẢN NH (01) -->
        <div class="section">
          <div class="row">
            <!-- Số tài khoản -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-66' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="accountNumber01"></app-input>
              } @else {
              <div class="info typo-body-9">{{ bankInfoForm.get('accountNumber01')?.value ?? '-' }}</div>
              }
            </div>

            <!-- Ngân hàng -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-34' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="labelBeneficiaryBank01"
                (clickEvent)="openSearchList($event)"
              ></app-input>
              } @else {
              <div class="info typo-body-9 bank">
                <img
                  class="bank-icon"
                  [ngClass]="{ hidden: !bankInfoForm.get('beneficiaryBank01')?.value?.logo }"
                  [src]="bankInfoForm.get('beneficiaryBank01')?.value?.logo"
                  alt=""
                />
                <!-- Nếu formControl có giá trị  -->
                <span *ngIf="bankInfoForm.get('labelBeneficiaryBank01')?.value"
                  >{{ bankInfoForm.get('beneficiaryBank01')?.value.name }} -
                  {{ bankInfoForm.get('beneficiaryBank01')?.value.bankName }}</span
                >
                <!-- Nếu không -> '-'-->
                <span *ngIf="!bankInfoForm.get('labelBeneficiaryBank01')?.value">-</span>
              </div>
              }
            </div>
          </div>

          <div class="row">
            <!-- Chủ tài khoản -->
            <div class="item half-50-item-cls">
              <div class="label typo-body-12">{{ 'MES-67' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="customerName01"></app-input>
              } @else {
              <div class="info typo-body-9">{{ bankInfoForm.get('customerName01')?.value ?? '-' }}</div>
              }
            </div>
          </div>
        </div>
      </app-section-wrapper-layout>

      <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-397' | translate }} (02)</div>
        <!-- TÀI KHOẢN NH (02) -->
        <div class="section">
          <div class="row">
            <!-- Số tài khoản -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-66' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="accountNumber02"></app-input>
              } @else {
              <div class="info typo-body-9">{{ bankInfoForm.get('accountNumber02')?.value ?? '-' }}</div>
              }
            </div>

            <!-- Ngân hàng -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-34' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="labelBeneficiaryBank02"
                (clickEvent)="openSearchList($event)"
              ></app-input>
              } @else {
              <div class="info typo-body-9 bank">
                <img
                  class="bank-icon"
                  [ngClass]="{ hidden: !bankInfoForm.get('beneficiaryBank02')?.value?.logo }"
                  [src]="bankInfoForm.get('beneficiaryBank02')?.value.logo"
                  alt=""
                />
                <!-- Nếu có formControl -->
                <span *ngIf="bankInfoForm.get('labelBeneficiaryBank02')?.value">
                  {{ bankInfoForm.get('beneficiaryBank02')?.value.name }}
                  - {{ bankInfoForm.get('beneficiaryBank02')?.value.bankName }}
                </span>

                <!-- Nếu không => '-' -->
                <span *ngIf="!bankInfoForm.get('labelBeneficiaryBank02')?.value">-</span>
              </div>
              }
            </div>
          </div>

          <div class="row">
            <!-- Chủ tài khoản -->
            <div class="item half-50-item-cls">
              <div class="label typo-body-12">{{ 'MES-67' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="customerName02"></app-input>
              } @else {
              <div class="info typo-body-9">{{ bankInfoForm.get('customerName02')?.value ?? '-' }}</div>
              }
            </div>
          </div>
        </div>
      </app-section-wrapper-layout>

      <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-397' | translate }} (03)</div>
        <!-- TÀI KHOẢN NH (03) -->
        <div class="section">
          <div class="row">
            <!-- Số tài khoản -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-66' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="accountNumber03"></app-input>
              } @else {
              <div class="info typo-body-9">{{ bankInfoForm.get('accountNumber03')?.value ?? '-' }}</div>
              }
            </div>

            <!-- Ngân hàng -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-34' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="labelBeneficiaryBank03"
                (clickEvent)="openSearchList($event)"
              ></app-input>
              } @else {
              <div class="info typo-body-9 bank">
                <img
                  class="bank-icon"
                  [ngClass]="{ hidden: !bankInfoForm.get('beneficiaryBank03')?.value?.logo }"
                  [src]="bankInfoForm.get('beneficiaryBank03')?.value.logo"
                  alt=""
                />
                <!-- Nếu có formControl -->
                <span *ngIf="bankInfoForm.get('labelBeneficiaryBank03')?.value"
                  >{{ bankInfoForm.get('beneficiaryBank03')?.value.name }} -
                  {{ bankInfoForm.get('beneficiaryBank03')?.value.bankName }}</span
                >

                <!-- Nếu không -->
                <span *ngIf="!bankInfoForm.get('labelBeneficiaryBank03')?.value">-</span>
              </div>
              }
            </div>
          </div>

          <div class="row">
            <!-- Chủ tài khoản -->
            <div class="item half-50-item-cls">
              <div class="label typo-body-12">{{ 'MES-67' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="customerName03"></app-input>
              } @else {
              <div class="info typo-body-9">{{ bankInfoForm.get('customerName03')?.value ?? '-' }}</div>
              }
            </div>
          </div>
        </div>
      </app-section-wrapper-layout>

      <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-397' | translate }} (04)</div>
        <!-- TÀI KHOẢN NH (04) -->
        <div class="section">
          <div class="row">
            <!-- Số tài khoản -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-66' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="accountNumber04"></app-input>
              } @else {
              <div class="info typo-body-9">{{ bankInfoForm.get('accountNumber04')?.value ?? '-' }}</div>
              }
            </div>

            <!-- Ngân hàng -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-34' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="labelBeneficiaryBank04"
                (clickEvent)="openSearchList($event)"
              ></app-input>
              } @else {
              <div class="info typo-body-9 bank">
                <img
                  class="bank-icon"
                  [ngClass]="{ hidden: !bankInfoForm.get('beneficiaryBank04')?.value?.logo }"
                  [src]="bankInfoForm.get('beneficiaryBank04')?.value.logo"
                  alt=""
                />
                <!-- Nếu có formControl -->
                <span *ngIf="bankInfoForm.get('labelBeneficiaryBank04')?.value">
                  {{ bankInfoForm.get('beneficiaryBank04')?.value.name }} -
                  {{ bankInfoForm.get('beneficiaryBank04')?.value.bankName }}
                </span>
                <!-- Nếu không -->
                <span *ngIf="!bankInfoForm.get('labelBeneficiaryBank04')?.value">-</span>
              </div>
              }
            </div>
          </div>

          <div class="row">
            <!-- Chủ tài khoản -->
            <div class="item half-50-item-cls">
              <div class="label typo-body-12">{{ 'MES-67' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="customerName04"></app-input>
              } @else {
              <div class="info typo-body-9">{{ bankInfoForm.get('customerName04')?.value ?? '-' }}</div>
              }
            </div>
          </div>
        </div>
      </app-section-wrapper-layout>

      <app-section-wrapper-layout>
        <div class="label typo-body-12">{{ 'MES-397' | translate }} (05)</div>
        <!-- TÀI KHOẢN NH (05) -->
        <div class="section">
          <div class="row">
            <!-- Số tài khoản -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-66' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="accountNumber05"></app-input>
              } @else {
              <div class="info typo-body-9">{{ bankInfoForm.get('accountNumber05')?.value ?? '-' }}</div>
              }
            </div>

            <!-- Ngân hàng -->
            <div class="item">
              <div class="label typo-body-12">{{ 'MES-34' | translate }}</div>
              @if(editMode) {
              <app-input
                type="dropdown"
                formControlName="labelBeneficiaryBank05"
                (clickEvent)="openSearchList($event)"
              ></app-input>
              } @else {
              <div class="info typo-body-9 bank">
                <img
                  class="bank-icon"
                  [ngClass]="{ hidden: !bankInfoForm.get('beneficiaryBank05')?.value?.logo }"
                  [src]="bankInfoForm.get('beneficiaryBank05')?.value.logo"
                  alt=""
                />
                <!-- Nếu có formControl -->
                <span *ngIf="bankInfoForm.get('labelBeneficiaryBank05')?.value"
                  >{{ bankInfoForm.get('beneficiaryBank05')?.value.name }} -
                  {{ bankInfoForm.get('beneficiaryBank05')?.value.bankName }}</span
                >

                <!-- Nếu không -->
                <span *ngIf="!bankInfoForm.get('labelBeneficiaryBank05')?.value">-</span>
              </div>
              }
            </div>
          </div>

          <div class="row">
            <!-- Chủ tài khoản -->
            <div class="item half-50-item-cls">
              <div class="label typo-body-12">{{ 'MES-67' | translate }}</div>
              @if(editMode) {
              <app-input type="input" formControlName="customerName05"></app-input>
              } @else {
              <div class="info typo-body-9">{{ bankInfoForm.get('customerName05')?.value ?? '-' }}</div>
              }
            </div>
          </div>
        </div>
      </app-section-wrapper-layout>
    </div>
  </form>
</div>
