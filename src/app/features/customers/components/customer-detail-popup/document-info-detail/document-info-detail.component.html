<div class="document-detail-wrapper">
  <!-- THÔNG TIN TÀI LIỆU -->
  <form [formGroup]="documentInfoForm" class="document-info">
    <div class="title-header">
      <div class="typo-body-19 title">{{ 'MES-09' | translate }}</div>

      <!-- Button chỉnh sửa
      @if(!editMode) {
      <div class="edit-btn-wrapper" (click)="toggleEditMode()">
        <mat-icon class="edit-btn" [svgIcon]="'icon:edit-2'"></mat-icon>
        <div class="edit-content-btn typo-body-9">{{ 'MES-340' | translate }}</div>
      </div>
      } @else {
      Button Lưu/Huỷ
      <div class="edit-btn-wrapper-save">
        <div class="button save typo-body-9" (click)="save()">
          Lưu
          <mat-icon [svgIcon]="'icon:tick-circle-icon'"></mat-icon>
        </div>
        <div class="button cancel typo-body-9" (click)="cancel()">
          Huỷ
          <mat-icon [svgIcon]="'icon:x-cross-red-icon'"></mat-icon>
        </div>
      </div>
      } -->
    </div>

    <div class="content">
      <div class="section-wrapper">
        <app-section-wrapper-layout>
          <div class="label typo-body-12">{{ 'MES-388' | translate }}</div>
          <div class="section s-column border">
            <div class="column">
              <!-- Loại tài khoản -->
              <div class="item half-50-item-cls">
                <div class="label typo-body-12">{{ 'MES-25' | translate }}</div>
                @if(editMode) {
                <app-input type="input" formControlName="accountType" [readonly]="true"></app-input>
                } @else {
                <div class="info typo-body-23">
                  {{ documentInfoForm.get('accountType')?.value ?? '-' }}
                </div>
                }
              </div>

              <!-- Số đăng ký kinh doanh -->
              <div class="item">
                <div class="label typo-body-12">{{ 'MES-390' | translate }}</div>
                @if(editMode) {
                <app-input type="input" formControlName="businessCode" [readonly]="true"></app-input>
                } @else {
                <div class="info typo-body-23">
                  {{ documentInfoForm.get('businessCode')?.value ?? 'Không' }}
                </div>
                }
              </div>
            </div>

            <div class="column">
              <!-- Đính kèm đăng ký kinh doanh -->
              <div
                class="item"
                [ngClass]="{
                  bottom: documentInfoForm.get('accountType')?.value === ETypeAccount.INDIVIDUAL
                }"
              >
                <div class="label flex typo-body-12">
                  <img src="./assets/icons/paperclip.svg" alt="" />
                  {{ 'MES-392' | translate }} ({{ documentInfoForm.get('fileBusinessCode')?.value.length }})
                </div>
                @if(editMode) {
                <!-- Nếu loại tài khoản là 'Tổ chức' -->
                <div
                  *ngIf="
                    documentInfoForm.get('accountType')?.value ===
                    CONVERT_TYPE_ACCOUNT_TO_LABLE[ETypeAccount.ORGANIZATION]
                  "
                  class="input-wrapper"
                >
                  <app-dropzone-form
                    [uploadedFiles]="documentInfoForm.get('fileBusinessCode')?.value"
                    (fileAdded)="addFilesToForm($event)"
                    (fileRemoved)="removeFileFromForm($event)"
                    formArrayName="fileBusinessCode"
                  ></app-dropzone-form>
                </div>

                <!-- Nếu loại tài khoản là Cá nhân -->
                <app-input
                  *ngIf="
                    documentInfoForm.get('accountType')?.value ===
                    CONVERT_TYPE_ACCOUNT_TO_LABLE[ETypeAccount.INDIVIDUAL]
                  "
                  type="input-no-formControl"
                  [readonly]="true"
                ></app-input>
                } @else { @if(documentInfoForm.get('fileBusinessCode')?.value.length) {
                <div
                  *ngFor="let file of documentInfoForm.get('fileBusinessCode')?.value"
                  class="info typo-body-23 file"
                >
                  <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                  <div class="file-info">
                    <div class="name typo-body-12">
                      <div class="file-name">{{ file.name }}</div>
                      <div class="size typo-body-22">
                        {{ file.size | formatFileSize }}
                      </div>
                    </div>
                  </div>
                  <a [href]="file.url" [download]="file.name">
                    <img class="download" src="./assets/icons/download.svg" alt="download" />
                  </a>
                </div>
                } @else {
                <!-- Nếu loại tài khoản là 'Tổ chức' -->
                <div
                  *ngIf="documentInfoForm.get('accountType')?.value === ETypeAccount.ORGANIZATION"
                  class="info typo-body-23 file"
                >
                  <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                  <div class="file-info">{{ 'MES-23' | translate }}</div>
                </div>
                <!-- Nếu loại tài khoản là ETypeAccount.INDIVIDUAL -->
                <div
                  *ngIf="documentInfoForm.get('accountType')?.value === ETypeAccount.INDIVIDUAL"
                  class="typo-body-23"
                >
                  Không
                </div>
                } }
              </div>
            </div>
          </div>
        </app-section-wrapper-layout>
      </div>

      <div class="section-wrapper">
        <app-section-wrapper-layout>
          <div class="label typo-body-12">{{ 'MES-561' | translate }}</div>
          <div class="section s-column border">
            <div class="column">
              <!-- Số CMND / CCCD / HC -->
              <div class="item">
                <div class="label typo-body-12">Số {{ 'MES-358' | translate }}</div>
                @if(editMode) {
                <app-input type="input" formControlName="identity"></app-input>
                } @else {
                <div class="info typo-body-23">{{ documentInfoForm.get('identity')?.value ?? '-' }}</div>
                }
              </div>

              <!-- Ngày cấp -->
              <div class="item">
                <div class="label typo-body-12">{{ 'MES-360' | translate }}</div>
                @if(editMode) {
                <app-input type="calendar" formControlName="identityDate"></app-input>
                } @else {
                <div class="info typo-body-23">
                  {{ documentInfoForm.get('identityDate')?.value | date : 'dd/MM/yyyy' || '-' }}
                </div>
                }
              </div>
            </div>

            <div class="column">
              <!-- Kiểu loại-->
              <div class="item">
                <div class="label typo-body-12">{{ 'MES-359' | translate }}</div>
                @if(editMode) {
                <app-input
                  type="dropdown"
                  formControlName="idNumberTypeName"
                  (clickEvent)="openSearchList($event)"
                ></app-input>
                } @else {
                <div class="info typo-body-23">
                  {{ documentInfoForm.get('idNumberTypeName')?.value ?? '-' }}
                </div>
                }
              </div>

              <!-- Nơi cấp -->
              <div class="item">
                <div class="label typo-body-12">{{ 'MES-361' | translate }}</div>
                @if(editMode) {
                <!-- <app-input
                  type="multiple-dropdown-province"
                  formControlName="identityIssuer"
                  (clickEvent)="openSearchList($event)"
                ></app-input> -->
                } @else {
                <!-- <div class="info typo-body-23">
                  {{
                    Object.keys(documentInfoForm.get('identityIssuer')?.value.children).length
                      ? documentInfoForm.get('identityIssuer')?.value.name +
                        ' - ' +
                        (getProvinceName(documentInfoForm.get('identityIssuer')?.value.children.id) || '')
                      : documentInfoForm.get('identityIssuer')?.value.name
                  }}
                </div> -->
                <div class="info typo-body-23">
                  {{ documentInfoForm.get('identityIssuer')?.value ?? '-' }}
                </div>
                }
              </div>
            </div>

            <!-- <div class="row">
              Đính kèm CMND / CCCD / HC
              <div class="item item-upload-file-cls">
                <div class="label flex typo-body-15">
                  <img src="./assets/icons/paperclip.svg" alt="" />
                  {{ 'MES-391' | translate }} ({{ documentInfoForm.get('fileIDNumber')?.value.length }})
                </div>
                @if(editMode) {
                <app-dropzone-form
                  [uploadedFiles]="documentInfoForm.get('fileIDNumber')?.value"
                  (fileAdded)="addFilesToForm($event)"
                  (fileRemoved)="removeFileFromForm($event)"
                  formArrayName="fileIDNumber"
                ></app-dropzone-form>
                } @else {
                Nếu có file
                @if(documentInfoForm.get('fileIDNumber')?.value.length) {
                <div *ngFor="let file of documentInfoForm.get('fileIDNumber')?.value" class="info typo-body-23 file">
                  <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                  <div class="file-info">
                    <div class="name typo-body-12">
                      <div class="file-name">{{ file.name }}</div>
                      <div class="size typo-body-22">
                        {{ file.size }}
                      </div>
                    </div>
                  </div>
                  <img class="download" src="./assets/icons/download.svg" alt="" />
                </div>
                } @else {
                Nếu không có file
                <div class="info typo-body-23 file">
                  <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                  <div class="file-info">{{ 'MES-23' | translate }}</div>
                </div>
                } }
              </div>
            </div> -->
          </div>
        </app-section-wrapper-layout>
      </div>

      <div class="section-wrapper">
        <app-section-wrapper-layout>
          <div class="label typo-body-12">{{ 'MES-430' | translate }}</div>
          <div class="border">
            <div class="section s-column">
              <div class="column border">
                <!-- Số  hợp đồng cơ sở -->
                <div class="item">
                  <div class="label typo-body-12">{{ 'MES-431' | translate }} cơ sở</div>
                  @if(editMode) {
                  <app-input type="input" formControlName="contractId00"></app-input>
                  } @else {
                  <div class="info typo-body-23">
                    {{ documentInfoForm.get('contractId00')?.value ?? '-' }}
                  </div>
                  }
                </div>

                <!-- Đính kèm hợp đồng cơ sở -->
                <div class="item half-50-item-cls">
                  <div class="label flex typo-body-12">
                    <img src="./assets/icons/paperclip.svg" alt="" />
                    {{ 'MES-432' | translate }} hợp đồng cơ sở ({{
                      documentInfoForm.get('fileContractId00')?.value.length
                    }})
                  </div>
                  @if(editMode) {
                  <app-dropzone-form
                    [uploadedFiles]="documentInfoForm.get('fileContractId00')?.value"
                    (fileAdded)="addFilesToForm($event)"
                    (fileRemoved)="removeFileFromForm($event)"
                    formArrayName="fileContractId00"
                  ></app-dropzone-form>
                  } @else { @if(documentInfoForm.get('fileContractId00')?.value.length) {
                  <div
                    *ngFor="let file of documentInfoForm.get('fileContractId00')?.value"
                    class="info typo-body-23 file"
                  >
                    <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                    <div class="file-info">
                      <div class="name typo-body-12">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="size typo-body-22">
                          {{ file.size | formatFileSize }}
                        </div>
                      </div>
                    </div>
                    <a [href]="file.url" [download]="file.name"
                      ><img class="download" src="./assets/icons/download.svg" alt="download"
                    /></a>
                  </div>
                  } @else {
                  <div class="info typo-body-23 file">
                    <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                    <div class="file-info">{{ 'MES-23' | translate }}</div>
                  </div>

                  } }
                </div>
              </div>

              <div class="column border">
                <!-- Số  hợp đồng ký quỹ -->
                <div class="item">
                  <div class="label typo-body-12">{{ 'MES-431' | translate }} kỹ quỹ</div>
                  @if(editMode) {
                  <app-input type="input" formControlName="contractId01"></app-input>
                  } @else {
                  <div class="info typo-body-23">
                    {{ documentInfoForm.get('contractId01')?.value ?? '-' }}
                  </div>
                  }
                </div>

                <!-- Đính kèm hợp đồng kỹ quỹ -->
                <div class="item half-50-item-cls">
                  <div class="label flex typo-body-12">
                    <img src="./assets/icons/paperclip.svg" alt="" />
                    {{ 'MES-432' | translate }} hợp đồng kỹ quỹ ({{
                      documentInfoForm.get('fileContractId01')?.value.length
                    }})
                  </div>
                  @if(editMode) {
                  <app-dropzone-form
                    [uploadedFiles]="documentInfoForm.get('fileContractId01')?.value"
                    (fileAdded)="addFilesToForm($event)"
                    (fileRemoved)="removeFileFromForm($event)"
                    formArrayName="fileContractId01"
                  ></app-dropzone-form>
                  } @else {
                  <!-- Nếu có files -->
                  @if(documentInfoForm.get('fileContractId01')?.value.length) {
                  <div
                    *ngFor="let file of documentInfoForm.get('fileContractId01')?.value"
                    class="info typo-body-23 file"
                  >
                    <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                    <div class="file-info">
                      <div class="name typo-body-12">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="size typo-body-22">
                          {{ file.size | formatFileSize }}
                        </div>
                      </div>
                    </div>

                    <a [href]="file.url" [download]="file.name"
                      ><img class="download" src="./assets/icons/download.svg" alt="download"
                    /></a>
                  </div>
                  } @else {
                  <!-- Nếu không có files -->
                  <div class="info typo-body-23 file">
                    <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                    <div class="file-info">{{ 'MES-23' | translate }}</div>
                  </div>

                  } }
                </div>
              </div>
            </div>

            <div class="section s-column">
              <div class="column border">
                <!-- Số hợp đồng 3 bên BIDV -->
                <div class="item">
                  <div class="label typo-body-12">{{ 'MES-431' | translate }} 3 bên BIDV</div>
                  @if(editMode) {
                  <app-input type="input" formControlName="contractId02"></app-input>
                  } @else {
                  <div class="info typo-body-23">
                    {{ documentInfoForm.get('contractId02')?.value ?? '-' }}
                  </div>
                  }
                </div>

                <!-- Đính kèm hợp đồng 3 Bên BIDV -->
                <div class="item half-50-item-cls">
                  <div class="label flex typo-body-12">
                    <img src="./assets/icons/paperclip.svg" alt="" />
                    {{ 'MES-432' | translate }} hợp đồng 3 Bên BIDV ({{
                      documentInfoForm.get('fileContractId02')?.value.length
                    }})
                  </div>
                  @if(editMode) {
                  <app-dropzone-form
                    [uploadedFiles]="documentInfoForm.get('fileContractId02')?.value"
                    (fileAdded)="addFilesToForm($event)"
                    (fileRemoved)="removeFileFromForm($event)"
                    formArrayName="fileContractId02"
                  ></app-dropzone-form>
                  } @else {
                  <!-- Nếu có files -->
                  @if(documentInfoForm.get('fileContractId02')?.value.length) {
                  <div
                    *ngFor="let file of documentInfoForm.get('fileContractId02')?.value"
                    class="info typo-body-23 file"
                  >
                    <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                    <div class="file-info">
                      <div class="name typo-body-12">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="size typo-body-22">
                          {{ file.size | formatFileSize }}
                        </div>
                      </div>
                    </div>
                    <a [href]="file.url" [download]="file.name"
                      ><img class="download" src="./assets/icons/download.svg" alt="download"
                    /></a>
                  </div>
                  } @else {
                  <!-- Nếu không có files -->
                  <div class="info typo-body-23 file">
                    <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                    <div class="file-info">{{ 'MES-23' | translate }}</div>
                  </div>
                  } }
                </div>
              </div>

              <div class="column border">
                <!-- Số hợp đồng 3 bên SHB -->
                <div class="item">
                  <div class="label typo-body-12">{{ 'MES-431' | translate }} 3 bên SHB</div>
                  @if(editMode) {
                  <app-input type="input" formControlName="contractId03"></app-input>
                  } @else {
                  <div class="info typo-body-23">
                    {{ documentInfoForm.get('contractId03')?.value ?? '-' }}
                  </div>
                  }
                </div>

                <!-- Đính kèm hợp đồng 3 Bên SHB-->
                <div class="item half-50-item-cls">
                  <div class="label flex typo-body-12">
                    <img src="./assets/icons/paperclip.svg" alt="" />
                    {{ 'MES-432' | translate }} hợp đồng 3 Bên SHB ({{
                      documentInfoForm.get('fileContractId03')?.value.length
                    }})
                  </div>
                  @if(editMode) {
                  <app-dropzone-form
                    [uploadedFiles]="documentInfoForm.get('fileContractId03')?.value"
                    (fileAdded)="addFilesToForm($event)"
                    (fileRemoved)="removeFileFromForm($event)"
                    formArrayName="fileContractId03"
                  ></app-dropzone-form>
                  } @else {
                  <!-- Nếu có files -->
                  @if(documentInfoForm.get('fileContractId03')?.value.length) {
                  <div
                    *ngFor="let file of documentInfoForm.get('fileContractId03')?.value"
                    class="info typo-body-23 file"
                  >
                    <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                    <div class="file-info">
                      <div class="name typo-body-12">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="size typo-body-22">
                          {{ file.size | formatFileSize }}
                        </div>
                      </div>
                    </div>
                    <a [href]="file.url" [download]="file.name"
                      ><img class="download" src="./assets/icons/download.svg" alt="download"
                    /></a>
                  </div>
                  } @else {
                  <!-- Không có files -->
                  <div class="info typo-body-23 file">
                    <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                    <div class="file-info">{{ 'MES-23' | translate }}</div>
                  </div>

                  } }
                </div>
              </div>
            </div>

            <div class="section s-column">
              <div class="column border">
                <!-- Số hợp đồng Margin T+ -->
                <div class="item">
                  <div class="label typo-body-12">{{ 'MES-431' | translate }} Margin T+</div>
                  @if(editMode) {
                  <app-input type="input" formControlName="contractId04"></app-input>
                  } @else {
                  <div class="info typo-body-23">
                    {{ documentInfoForm.get('contractId04')?.value ?? '-' }}
                  </div>
                  }
                </div>

                <!-- Đính kèm hợp đồng Margin T+ -->
                <div class="item half-50-item-cls">
                  <div class="label flex typo-body-12">
                    <img src="./assets/icons/paperclip.svg" alt="" />
                    {{ 'MES-432' | translate }} hợp đồng Margin T+ ({{
                      documentInfoForm.get('fileContractId04')?.value.length
                    }})
                  </div>
                  @if(editMode) {
                  <app-dropzone-form
                    [uploadedFiles]="documentInfoForm.get('fileContractId04')?.value"
                    (fileAdded)="addFilesToForm($event)"
                    (fileRemoved)="removeFileFromForm($event)"
                    formArrayName="fileContractId04"
                  ></app-dropzone-form>
                  } @else {
                  <!-- Nếu có files -->
                  @if(documentInfoForm.get('fileContractId04')?.value.length) {
                  <div
                    *ngFor="let file of documentInfoForm.get('fileContractId04')?.value"
                    class="info typo-body-23 file"
                  >
                    <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                    <div class="file-info">
                      <div class="name typo-body-12">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="size typo-body-22">
                          {{ file.size | formatFileSize }}
                        </div>
                      </div>
                    </div>
                    <a [href]="file.url" [download]="file.name"
                      ><img class="download" src="./assets/icons/download.svg" alt="download"
                    /></a>
                  </div>
                  } @else {
                  <!-- Nếu không có files -->
                  <div class="info typo-body-23 file">
                    <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                    <div class="file-info">{{ 'MES-23' | translate }}</div>
                  </div>

                  } }
                </div>
              </div>

              <div class="column border">
                <!-- Số hợp đồng trái phiếu -->
                <div class="item">
                  <div class="label typo-body-12">{{ 'MES-431' | translate }} trái phiếu</div>
                  @if(editMode) {
                  <app-input type="input" formControlName="contractId05"></app-input>
                  } @else {
                  <div class="info typo-body-23">
                    {{ documentInfoForm.get('contractId05')?.value ?? '-' }}
                  </div>
                  }
                </div>

                <!-- Đính kèm hợp đồng trái phiếu -->
                <div class="item half-50-item-cls">
                  <div class="label flex typo-body-12">
                    <img src="./assets/icons/paperclip.svg" alt="" />
                    {{ 'MES-432' | translate }} hợp đồng trái phiếu ({{
                      documentInfoForm.get('fileContractId05')?.value.length
                    }})
                  </div>
                  @if(editMode) {
                  <app-dropzone-form
                    [uploadedFiles]="documentInfoForm.get('fileContractId05')?.value"
                    (fileAdded)="addFilesToForm($event)"
                    (fileRemoved)="removeFileFromForm($event)"
                    formArrayName="fileContractId05"
                  ></app-dropzone-form>
                  } @else {
                  <!-- Nếu có files -->
                  @if(documentInfoForm.get('fileContractId05')?.value.length) {
                  <div
                    *ngFor="let file of documentInfoForm.get('fileContractId05')?.value"
                    class="info typo-body-23 file"
                  >
                    <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                    <div class="file-info">
                      <div class="name typo-body-12">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="size typo-body-22">
                          {{ file.size | formatFileSize }}
                        </div>
                      </div>
                    </div>
                    <a [href]="file.url" [download]="file.name"
                      ><img class="download" src="./assets/icons/download.svg" alt="download"
                    /></a>
                  </div>
                  } @else {
                  <!-- Nếu không có files -->
                  <div class="info typo-body-23 file">
                    <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                    <div class="file-info">{{ 'MES-23' | translate }}</div>
                  </div>

                  } }
                </div>
              </div>
            </div>

            <div class="section s-column">
              <div class="column border">
                <!-- Số hợp đồng phái sinh -->
                <div class="item half-50-item-cls">
                  <div class="label typo-body-12">{{ 'MES-431' | translate }} phái sinh</div>
                  @if(editMode) {
                  <app-input type="input" formControlName="contractId80"></app-input>
                  } @else {
                  <div class="info typo-body-23">
                    {{ documentInfoForm.get('contractId80')?.value ?? '-' }}
                  </div>
                  }
                </div>

                <!-- Đính kèm hợp đồng phái sinh -->
                <div class="item half-50-item-cls">
                  <div class="label flex typo-body-12">
                    <img src="./assets/icons/paperclip.svg" alt="" />
                    {{ 'MES-432' | translate }} hợp đồng phái sinh ({{
                      documentInfoForm.get('fileContractId80')?.value.length
                    }})
                  </div>
                  @if(editMode) {
                  <app-dropzone-form
                    [uploadedFiles]="documentInfoForm.get('fileContractId80')?.value"
                    (fileAdded)="addFilesToForm($event)"
                    (fileRemoved)="removeFileFromForm($event)"
                    formArrayName="fileContractId80"
                  ></app-dropzone-form>
                  } @else {
                  <!-- Nếu có files -->
                  @if(documentInfoForm.get('fileContractId80')?.value.length) {
                  <div
                    *ngFor="let file of documentInfoForm.get('fileContractId80')?.value"
                    class="info typo-body-23 file"
                  >
                    <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
                    <div class="file-info">
                      <div class="name typo-body-12">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="size typo-body-22">
                          {{ file.size | formatFileSize }}
                        </div>
                      </div>
                    </div>
                    <a [href]="file.url" [download]="file.name"
                      ><img class="download" src="./assets/icons/download.svg" alt="download"
                    /></a>
                  </div>
                  } @else {
                  <!-- Nếu không có files -->
                  <div class="info typo-body-23 file">
                    <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
                    <div class="file-info">{{ 'MES-23' | translate }}</div>
                  </div>

                  } }
                </div>
              </div>
            </div>
          </div>
        </app-section-wrapper-layout>
      </div>

      <div class="section section-config s-row">
        <div class="row">
          <!-- Chữ ký -->
          <div class="item half-50-item-cls">
            <div class="label typo-body-12">{{ 'MES-433' | translate }}</div>
            @if(editMode) {
            <app-input type="input" formControlName="signature"></app-input>
            } @else {
            <div class="info typo-body-23">
              {{ documentInfoForm.get('signature')?.value ?? '-' }}
            </div>
            }
          </div>
        </div>

        <div class="row">
          <!-- Đính kèm chữ ký -->
          <div class="item half-50-item-cls">
            <div class="label flex typo-body-12">
              <img src="./assets/icons/paperclip.svg" alt="" />
              {{ 'MES-432' | translate }} chữ ký ({{ documentInfoForm.get('fileSignature')?.value.length }})
            </div>
            @if(editMode) {
            <app-dropzone-form
              [uploadedFiles]="documentInfoForm.get('fileSignature')?.value"
              (fileAdded)="addFilesToForm($event)"
              (fileRemoved)="removeFileFromForm($event)"
              formArrayName="fileSignature"
            ></app-dropzone-form>
            } @else {
            <!-- Nếu có files -->
            @if(documentInfoForm.get('fileSignature')?.value.length) {
            <div *ngFor="let file of documentInfoForm.get('fileSignature')?.value" class="info typo-body-23 file">
              <img class="image-icon" src="./assets/icons/file-extention-blue.svg" alt="" />
              <div class="file-info">
                <div class="name typo-body-12">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="size typo-body-22">
                    {{ file.size | formatFileSize }}
                  </div>
                </div>
              </div>
              <a [href]="file.url" [download]="file.name"
                ><img class="download" src="./assets/icons/download.svg" alt="download"
              /></a>
            </div>
            } @else {
            <!-- Nếu không có files -->
            <div class="info typo-body-23 file">
              <img class="image-icon" src="./assets/icons/add-file-icon.svg" alt="" />
              <div class="file-info">{{ 'MES-23' | translate }}</div>
            </div>
            } }
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
