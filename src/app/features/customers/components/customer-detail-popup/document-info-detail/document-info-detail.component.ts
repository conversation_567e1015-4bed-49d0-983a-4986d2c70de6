import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { CONVERT_TYPE_ACCOUNT_TO_LABLE, ETypeAccount } from '../../../constants/customers';
import { take, takeUntil } from 'rxjs';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { DestroyService, DialogService } from 'src/app/core/services';

/**
 * DocumentInfoDetailComponent
 */
@Component({
  selector: 'app-document-info-detail',
  templateUrl: './document-info-detail.component.html',
  styleUrl: './document-info-detail.component.scss',
})
export class DocumentInfoDetailComponent implements OnInit {
  @Input() data: any;
  @Output() formChanged = new EventEmitter<void>();
  editMode = false;

  initialFormValues!: any;
  documentInfoForm!: FormGroup;

  ETypeAccount = ETypeAccount;
  CONVERT_TYPE_ACCOUNT_TO_LABLE = CONVERT_TYPE_ACCOUNT_TO_LABLE;

  /**
   * Constructor
   * @param fb fb
   * @param popoverService popoverService
   * @param dialogService dialogService
   * @param cdr cdr
   * @param _destroy _destroy
   */
  constructor(
    private readonly fb: FormBuilder,
    private readonly popoverService: PopoverService,
    private readonly dialogService: DialogService,
    private readonly cdr: ChangeDetectorRef,
    private readonly _destroy: DestroyService
  ) {}

  /**
   * ngOninit
   */
  ngOnInit(): void {
    this.initForm();
    this.documentInfoForm.valueChanges.pipe(takeUntil(this._destroy)).subscribe(() => {
      if (this.hasFormChanged()) {
        this.formChanged.emit();
      }
    });
    this.cdr.detectChanges();
  }

  /**
   * initForm
   */
  initForm() {
    const { identity, identityDate, identityIssuer, idNumberTypeName } = this.data.identifyInfo;

    const {
      accountType,
      businessCode,
      contractId00,
      contractId01,
      contractId02,
      contractId03,
      contractId04,
      contractId05,
      contractId80,
      signature,
    } = this.data;

    this.documentInfoForm = this.fb.group({
      accountType: CONVERT_TYPE_ACCOUNT_TO_LABLE[accountType],
      businessCode: businessCode?.name, // Số ĐKKD
      fileBusinessCode: this.fb.array(
        businessCode
          ? businessCode.files.map((file: any) =>
              this.fb.group({
                name: `${file.name}.${file.type}`,
                size: file.size,
              })
            )
          : []
      ),
      identity,
      idNumberTypeName,
      identityDate: this.convertToDate(identityDate),
      identityIssuer,
      contractId00: contractId00?.name, // HĐ Cơ Sở
      fileContractId00: this.fb.array(
        (contractId00?.files ?? []).map((file: any) =>
          this.fb.group({
            name: `${file.name}.${file.type}`,
            size: file.size,
          })
        )
      ),
      contractId01: contractId01?.name, // HĐ Ký Quỹ
      fileContractId01: this.fb.array(
        (contractId01?.files ?? []).map((file: any) =>
          this.fb.group({
            name: `${file.name}.${file.type}`,
            size: file.size,
          })
        )
      ),
      contractId02: contractId02?.name, // HĐ 3 Bên BIDV
      fileContractId02: this.fb.array(
        (contractId02?.files ?? []).map((file: any) =>
          this.fb.group({
            name: `${file.name}.${file.type}`,
            size: file.size,
          })
        )
      ),

      contractId03: contractId03?.name, // HĐ 3 Bên SHB
      fileContractId03: this.fb.array(
        (contractId03?.files ?? []).map((file: any) =>
          this.fb.group({
            name: `${file.name}.${file.type}`,
            size: file.size,
          })
        )
      ),
      contractId04: contractId04?.name, // HĐ Margin T+
      fileContractId04: this.fb.array(
        (contractId04?.files ?? []).map((file: any) =>
          this.fb.group({
            name: `${file.name}.${file.type}`,
            size: file.size,
          })
        )
      ),

      contractId05: contractId05?.name, // HĐ Trái Phiếu
      fileContractId05: this.fb.array(
        (contractId05?.files ?? []).map((file: any) =>
          this.fb.group({
            name: `${file.name}.${file.type}`,
            size: file.size,
          })
        )
      ),

      contractId80: contractId80?.name, // HĐ Phái Sinh
      fileContractId80: this.fb.array(
        (contractId80?.files ?? []).map((file: any) =>
          this.fb.group({
            name: `${file.name}.${file.type}`,
            size: file.size,
          })
        )
      ),

      signature: signature?.name,
      fileSignature: this.fb.array(
        (signature?.signatureFiles ?? []).map((file: any) =>
          this.fb.group({
            name: `${file.name}.${file.type}`,
            size: file.size,
          })
        )
      ),
    });

    this.initialFormValues = this.documentInfoForm.getRawValue();
  }

  /**
   * hasFormChanged
   */
  hasFormChanged() {
    const currentValues = this.documentInfoForm.getRawValue();
    return !this.isEqual(this.initialFormValues, currentValues);
  }

  /**
   * Check if two objects are equal
   * @param obj1
   * @param obj2
   */
  private isEqual(obj1: any, obj2: any): boolean {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  }

  /**
   * save
   */
  save() {
    if (this.hasFormChanged()) {
      const dialogRef = this.dialogService.openPopUp(DeleteNotificationComponent, {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-88',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      });
      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((result) => {
          if (result) {
            this.saveFormData();
            this.toggleEditMode();
          }
        });
    } else {
      this.toggleEditMode();
    }
  }

  /**
   * cancel
   */
  cancel() {
    if (this.hasFormChanged()) {
      const dialogRef = this.dialogService.openPopUp(DeleteNotificationComponent, {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-97',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
          isHideMessage: true,
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      });
      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((result) => {
          if (result) {
            this.documentInfoForm.patchValue(this.initialFormValues);
            this.patchFormArray('fileBusinessCode', this.initialFormValues.fileBusinessCode);
            this.patchFormArray('fileContractId00', this.initialFormValues.fileContractId00);
            this.patchFormArray('fileContractId01', this.initialFormValues.fileContractId01);
            this.patchFormArray('fileContractId02', this.initialFormValues.fileContractId02);
            this.patchFormArray('fileContractId03', this.initialFormValues.fileContractId03);
            this.patchFormArray('fileContractId04', this.initialFormValues.fileContractId04);
            this.patchFormArray('fileContractId05', this.initialFormValues.fileContractId05);
            this.patchFormArray('fileContractId80', this.initialFormValues.fileContractId80);
            this.patchFormArray('fileSignature', this.initialFormValues.fileSignature);
            this.toggleEditMode();
          }
        });
    } else {
      this.toggleEditMode();
    }
  }

  /**
   * clear file vừa mới upload sau đó push lại file của initialFormValues
   * @param arrayName arrayName
   * @param values values
   */
  patchFormArray(arrayName: string, values: any[]) {
    const formArray = this.documentInfoForm.get(arrayName) as FormArray;
    formArray.clear();
    values.forEach((value) => formArray.push(this.fb.group(value)));
  }

  /**
   * saveFormData
   */
  saveFormData() {
    this.initialFormValues = this.documentInfoForm.getRawValue();
  }

  /**
   * toggleEditMode
   */
  toggleEditMode() {
    this.editMode = !this.editMode;
  }

  /**
   * convertToDate
   * @param dateString
   */
  convertToDate(dateString: string): Date | null {
    return dateString ? new Date(dateString) : null;
  }

  /**
   * addFilesToForm
   * @param event
   * @param event.files
   * @param event.formArrayName
   */
  addFilesToForm(event: { files: any[]; formArrayName: string }) {
    const { files, formArrayName } = event;
    if (!Array.isArray(files)) {
      console.error('Files is not an array:', files);
      return;
    }

    const formArray = this.documentInfoForm.get(formArrayName) as FormArray;
    files.forEach((file) => {
      formArray.push(
        this.fb.group({
          name: file.name,
          size: file.size,
        })
      );
    });

    // Manually trigger change detection
    this.documentInfoForm.setControl(formArrayName, formArray);
  }

  /**
   * removeFileFromForm
   * @param event
   * @param event.idx
   * @param event.formArrayName
   */
  removeFileFromForm(event: { idx: number; formArrayName: string }) {
    const { idx, formArrayName } = event;
    const formArray = this.documentInfoForm.get(formArrayName) as FormArray;

    // Ensure formArray is properly initialized
    if (!formArray) {
      console.error('FormArray not found:', formArrayName);
      return;
    }

    // Validate index
    if (idx >= 0 && idx < formArray.length) {
      formArray.removeAt(idx);

      this.documentInfoForm.setControl(formArrayName, formArray);
    } else {
      console.error('Invalid index:', idx);
    }
  }

  /**
   * GetFormControl
   * @param field
   * @returns {any} control from form
   */
  getFormControl(field: string) {
    return this.documentInfoForm.get(field) as FormControl;
  }

  /**
   * openSearchList
   * @param data
   * @param data.event
   * @param data.element
   * @param data.formControlName
   */
  openSearchList(data: { event: Event; element: HTMLElement; formControlName: string }) {
    console.log('searchList', data);
  }
}
