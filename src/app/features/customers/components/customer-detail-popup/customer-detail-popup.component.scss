.detail-header {
  padding: 24px 16px 16px 24px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--color--other--divider);
  background-color: var(--color--neutral--white);
  // position: sticky;
  // top: 0;
  // z-index: 1000;

  .header-left {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .name-level {
      display: flex;
      gap: 25px;

      .level {
        width: 120px;
        padding: 2px 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 16px;
        background-color: var(--color--neutral--100);

        &.platinum-cls {
          background-color: var(--color--accents--orange-dark) !important;
        }

        &.diamond-cls {
          background-color: var(--color--accents--mint-dark) !important;
        }

        &.gold-cls {
          background-color: var(--color--accents--yellow-dark) !important;
        }
      }
    }

    .account-number {
      color: var(--color--text-vibrant--secondary);
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .close-btn {
    cursor: pointer;
  }
}

.detail-body {
  flex: 1;
  height: 100%;
  overflow: auto;
  .popup-container {
    display: block;
    height: 100%;
    overflow: hidden;
    width: 100%;

    .menu {
      width: 17%;
      height: 100%;
      padding: 16px 8px;
      display: flex;
      flex-direction: column;
      gap: 12px;
      position: fixed;
      left: inherit;

      a {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        gap: 12px;

        .mdc-list-item__content {
          overflow: clip;
        }

        .menu-icon {
          width: 16px !important;
          height: 16px !important;
        }

        .label {
          color: var(--color--text-vibrant--secondary);
        }
      }
    }

    //Active
    .menu a.active {
      background-color: var(--color--background--selected);
      border-radius: 6px;

      .label {
        color: orange;
      }
    }
  }

  .content {
    border-left: 1px solid var(--color--other--divider);
    margin-left: 24%;
    height: 100%;
  }
}

.detail-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

.flex-customer-class {
  display: flex;
  height: 100%;
  overflow: hidden;
  flex-direction: column;
  padding: 24px 32px;
}

:host {
  width: 100%;
  height: 100%;
  display: block;
}
