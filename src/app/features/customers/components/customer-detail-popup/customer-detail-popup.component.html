<div class="detail-wrapper">
  <!-- HEADER -->
  <div class="detail-header">
    <div class="header-left">
      <div class="name-level">
        <div class="typo-heading-11">{{ customerName }}</div>
        <!-- FIX ME: Wait api -->
        <!-- <div class="level typo-body-12" [ngClass]="getCustomerLevel(customerLevel)">
          {{ CONVERT_CUSTOMER_LEVEL_TO_LABLE[customerLevel] }}
        </div> -->
      </div>
      <div class="account-number typo-body-23">{{ accountNumber }}</div>
    </div>

    <div class="header-right">
      <app-create-place-order-component [infoCustomer]="infoCustomer"></app-create-place-order-component>
      <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
    </div>
  </div>

  <!-- BODY -->
  <div class="detail-body">
    <div class="popup-container">
      <div class="menu">
        <a
          *ngFor="let item of menuItems"
          mat-list-item
          (click)="selectTab(item, accountNumber)"
          [class.active]="item === selectedMenuTab"
        >
          <mat-icon
            aria-hidden="false"
            [svgIcon]="item === selectedMenuTab ? item.activeIcon : item.icon"
            class="menu-icon"
          ></mat-icon>
          <div class="label typo-body-23">{{ item.label }}</div>
        </a>
      </div>

      <div class="content" [ngSwitch]="selectedMenuTab.label">
        <ng-container *ngSwitchCase="'Thông tin cá nhân'">
          <ng-template [ngTemplateOutlet]="personalInfo"></ng-template>
        </ng-container>

        <!-- Thông tin ngân hàng -->
        <ng-container *ngSwitchCase="'Thông tin ngân hàng'">
          <ng-template [ngTemplateOutlet]="bankInfo"></ng-template>
        </ng-container>

        <!-- Thông tin tài liệu -->
        <ng-container *ngSwitchCase="'Thông tin tài liệu'">
          <ng-template [ngTemplateOutlet]="docInfo"></ng-template>
        </ng-container>

        <!-- Thông tin tài khoản -->
        <ng-container *ngSwitchCase="'Thông tin tài khoản'">
          <ng-template [ngTemplateOutlet]="accountInfo"></ng-template>
        </ng-container>

        <!-- Thông tin lãi suất -->
        <!-- <ng-container *ngSwitchCase="'Thông tin lãi suất'">
          <ng-template [ngTemplateOutlet]="interestRateInfo"></ng-template>
        </ng-container> -->

        <!-- Thông tin uỷ quyền -->
        <ng-container *ngSwitchCase="'Thông tin uỷ quyền'">
          <ng-template [ngTemplateOutlet]="authorityInfo"></ng-template>
        </ng-container>
      </div>
    </div>
  </div>
</div>

<!-- Thông tin cá nhân -->
<ng-template #personalInfo>
  <app-personal-info-detail
    class="flex-customer-class"
    [data]="detailData"
    (formChanged)="onFormChanged($event)"
  ></app-personal-info-detail>
</ng-template>

<ng-template #bankInfo>
  <app-bank-info-detail
    class="flex-customer-class"
    [data]="detailData"
    (formChanged)="onFormChanged($event)"
  ></app-bank-info-detail>
</ng-template>

<ng-template #docInfo>
  <app-document-info-detail
    class="flex-customer-class"
    [data]="detailData"
    (formChanged)="onFormChanged($event)"
  ></app-document-info-detail>
</ng-template>

<ng-template #accountInfo>
  <app-account-info-detail
    class="flex-customer-class"
    [data]="detailData"
    (formChanged)="onFormChanged($event)"
  ></app-account-info-detail>
</ng-template>

<!-- <ng-template #interestRateInfo>
  <div>
    <h2>Thông Tin Lãi Suất</h2>
  </div>
</ng-template> -->

<ng-template #authorityInfo>
  <app-authority-info-detail
    class="flex-customer-class"
    [data]="detailData"
    (formChanged)="onFormChanged($event)"
  ></app-authority-info-detail>
</ng-template>
