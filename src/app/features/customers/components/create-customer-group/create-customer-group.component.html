<div class="customer-group-create-wrapper">
  <!-- HEADER -->
  <div class="header-create">
    <div class="typo-body-16">{{ 'MES-06' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <!-- CONTENT -->
  <div class="body-create">
    <form class="form-group" [formGroup]="createCustomerForm">
      <app-form-control>
        <mat-form-field class="item name-group">
          <div class="label-form typo-body-17">{{ 'MES-190' | translate }} <span [style.color]="'red'">*</span></div>
          <input
            matInput
            type="text"
            class="input-cls typo-body-12 fs-12"
            [placeholder]="'Tên nhóm'"
            formControlName="customerGroupName"
          />
        </mat-form-field>
      </app-form-control>
    </form>

    <div class="customer-list">
      <div class="title-customer typo-body-12">{{ 'MES-96' | translate }}</div>

      <div class="searchbox-wrap">
        @let customers = customers$ | async;

        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResults"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers ?? []"
          [maxSelectedItems]="MAX_CUSTOMER_SELECTED"
          [translateParams]="{ maxItem: MAX_CUSTOMER_SELECTED }"
          [warningMessage]="'MES-646'"
          [messageSelectionComplete]="'MES-669'"
          [displayFn]="displayCustomerFilter"
          [selectAllLabel]="'MES-58' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchPlaceholder]="'MES-295' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          (invalidSelection)="validateFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResults>
          <div class="typo-body-15">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>
    </div>
  </div>

  <!-- FOOTER -->
  <div class="footer-create">
    <button
      [disabled]="createCustomerForm.invalid || isDisableApply"
      (click)="applyFilter()"
      class="btn apply typo-button-3"
    >
      {{ 'MES-89' | translate }}
    </button>
    <button mat-dialog-close class="btn typo-button-3">{{ 'MES-74' | translate }}</button>
  </div>
</div>
