import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { ImmerComponentStore } from 'ngrx-immer/component-store';
import { catchError, finalize,  of, switchMap, tap } from 'rxjs';
import { DialogService, LoadingService, MessageService } from 'src/app/core/services';
import { CreateCustomerGroupComponent } from './create-customer-group.component';
import { CustomerService } from '../../services/customer.service';
import { ICusomterGroupPayload } from '../../model/customer';
import { ApiErrorResponse } from '@shared/models';
import { tapResponse } from '@ngrx/operators';
import { getCustomerGroupsList } from 'src/app/stores/shared/shared.actions';
import { ActivatedRoute } from '@angular/router';

interface IGroupCustomerState {}

@Injectable()
export class GroupCustomerCreateStore extends ImmerComponentStore<IGroupCustomerState> {
  readonly createCustomerGroup = this.effect<ICusomterGroupPayload>(($event) =>
    $event.pipe(
      tap(() => this.loadingService.show()),
      switchMap((body) => {
        return this.customerService.createCustomerGroup(body).pipe(
          finalize(() => this.loadingService.hide()),
          catchError((error) => of(new ApiErrorResponse<any>(error)))
        );
      }),
      tapResponse(
        (res) => {
          if (res instanceof ApiErrorResponse) {
            this.messageService.error(res.message);
            return;
          }
          this.messageService.success(res.message);
          this.dialogService.closeAll();
          const brokerCode = this.route.snapshot.queryParams['brokerId'];
          this.store.dispatch(getCustomerGroupsList({ brokerCode }));
        },
        (err: any) => {}
      )
    )
  );

  constructor(
    readonly loadingService: LoadingService,
    readonly messageService: MessageService,
    readonly dialogService: DialogService,
    readonly store: Store,
    readonly customerService: CustomerService,
    public dialogRef: MatDialogRef<CreateCustomerGroupComponent>,
    readonly route: ActivatedRoute
  ) {
    super();
  }
}
