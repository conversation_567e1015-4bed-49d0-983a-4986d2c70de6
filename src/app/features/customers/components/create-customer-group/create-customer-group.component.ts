import { ChangeDetectionStrategy, Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { debounceTime, Observable, startWith, take, takeUntil, tap } from 'rxjs';
import { DestroyService, MessageService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { CustomDropdownPopupFilterComponent } from 'src/app/shared/components/custom-dropdown-popup-filter/custom-dropdown-popup-filter.component';
import { IOptionList } from '../customer-group-filter/customer-group-filter.component';
import { MatDialogRef } from '@angular/material/dialog';
import { ICusomterGroupPayload } from '../../model/customer';
import { GroupCustomerCreateStore } from './customer-group-create-store';
import { Store } from '@ngrx/store';
import { deepClone } from 'src/app/shared/utils/utils';
import {
  selectAllAccountNumberListByBrokerView$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import { SelectionModel } from '@angular/cdk/collections';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { MAX_ITEM_ACCOUNT_SELECTED } from 'src/app/shared/constants/global';

const LIST_MG_ROOM_OPTIONS = [
  {
    name: 'Phòng MG 05',
    id: 1,
  },
  {
    name: 'Phòng MG 08',
    id: 2,
  },
  {
    name: 'Phòng MG 07',
    id: 3,
  },
  {
    name: 'Phòng MG 06',
    id: 4,
  },
  {
    name: 'Phòng MG 09',
    id: 5,
  },
];

const LIST_MG_OPTIONS = [
  {
    id: 1,
    name: 'MG-015: Nguyễn Tuấn Dương',
  },
  {
    id: 2,
    name: 'MG-05: Cao Tuấn Nghĩa',
  },
  {
    id: 3,
    name: 'MG-05: Mai Tiến Đạt',
  },
  {
    id: 4,
    name: 'MG-020: Vũ Minh Chiến',
  },
  {
    id: 5,
    name: 'MG-016: Nguyễn Hoàng Cảnh',
  },
  {
    id: 6,
    name: 'MG-019: Đinh Sỹ Dũng',
  },
  {
    id: 7,
    name: 'MG-021: Lê Ngọc Hà',
  },
  {
    id: 8,
    name: 'MG-018: Phạm Văn Tây',
  },
];

/**
 * CustomerGroupFilterComponent
 */
@Component({
  selector: 'app-create-customer-group',
  templateUrl: './create-customer-group.component.html',
  styleUrl: './create-customer-group.component.scss',
  providers: [GroupCustomerCreateStore],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateCustomerGroupComponent implements OnInit {
  @ViewChild(VirtualScrollListComponent) virtualScroll!: VirtualScrollListComponent;

  listFilterOptions: IOptionList[] = [];

  brokenRoom: string[] | undefined;
  managementBroker!: string[] | undefined;

  isAllBrokenRoom: boolean = false;
  isAllManagementBroker: boolean = false;

  // FormControl
  groupNameControl = new FormControl();
  private _groupNameOptions!: IOptionList[];
  createCustomerForm!: FormGroup;
  brokerCode = '';

  selectCompareWithFn = (o1: IOptionList, o2: IOptionList) => o1.accountNumber === o2.accountNumber;
  // Key identifier for customer objects
  readonly customerKey = 'accountNumber';
  customers$: Observable<IAllAccountNumber[]> = new Observable<IAllAccountNumber[]>();
  MAX_ITEM_ACCOUNT_SELECTED = MAX_ITEM_ACCOUNT_SELECTED;
  rowSelection: SelectionModel<IOptionList> = new SelectionModel<any>(true, [], true, this.selectCompareWithFn);
  isDisableApply = false;
  MAX_CUSTOMER_SELECTED = 30;

  /**
   * Constructor
   * @param _destroy
   * @param popoverService
   * @param fb - FormBuilder
   * @param popoverRef
   * @param messageService
   * @param dialogRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    private readonly fb: FormBuilder,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly messageService: MessageService,
    public dialogRef: MatDialogRef<CreateCustomerGroupComponent>,
    private readonly customerGroupStore: GroupCustomerCreateStore,
    private readonly store: Store
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.customers$ = this.store.select(selectAllAccountNumberListByBrokerView$);

    this.createCustomerForm = this.initForm();
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.groupNameControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterOptions = this._filter(value ?? '', this._groupNameOptions);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((allCustomers) => {
        this.listFilterOptions = allCustomers.map((customer) => ({
          name: customer.customerName,
          accountNumber: customer.accountNumber,
          value: customer.brokerCode,
          isSelect: false,
        }));

        this._groupNameOptions = deepClone(this.listFilterOptions);
      });

    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((info) => {
        if (!info) return;
        const { brokerCode } = info;
        this.brokerCode = brokerCode;
      });
  }

  /**
   * Khởi tạo Form
   */
  private initForm() {
    return this.fb.group({
      customerGroupName: [null, Validators.required],
    });
  }

  /**
   * Get form control "customerGroupName"
   */
  get allocatedTo() {
    return this.createCustomerForm.get('customerGroupName');
  }

  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param type Kiểu giá trị được thay đổi
   * @param item Item thay đổi
   */
  // changeSections(checked: boolean, type: string, item?: IOptionList) {
  //   if (type === 'all') {
  //     // this.isSelectAll = checked;
  //     this._groupNameOptions.forEach((i) => {
  //       i.isSelect = checked;
  //     });
  //   }
  //   if (type === 'item' && item) {
  //     item.isSelect = checked;
  //     // this.isSelectAll = this.checkIsShowAll();
  //   }
  // }

  public onCheckRowChange(row: IOptionList): void {
    this.rowSelection.toggle(row);
  }

  /**
   * Kiểm tra xem list có được show hết hay không?
   */
  // checkIsShowAll() {
  //   for (const obj of this.listFilterOptions) {
  //      Kiểm tra xem 'isSelect' có trong đối tượng không
  //     if (!('isSelect' in obj)) {
  //       return false; // Nếu không tồn tại, trả về false
  //     }

  //     Kiểm tra xem giá trị của thuộc tính isSelect là true hay không
  //     if (!obj.isSelect) {
  //       return false; // Nếu giá trị là false, trả về false
  //     }
  //   }
  //   // Nếu không có trường hợp nào gặp return false ở trên, mặc định trả về true
  //   return true;
  // }

  /**
   *
   * @param event - event
   */
  openPopoverCustomer(event: Event) {
    const originElement = event.target as HTMLElement;

    const popupRef = this.popoverService.open({
      origin: originElement,
      content: CustomDropdownPopupFilterComponent,
      position: 0,
      width: '410px',
      height: 280,
      data: LIST_MG_ROOM_OPTIONS,
      componentConfig: {
        searchKey: ['name'],
        displayFnc: (v: any) => v.name,
        listFilterOptions: LIST_MG_ROOM_OPTIONS,
      },
    });

    popupRef.afterClosed$.pipe(take(1)).subscribe({
      next: (value) => {
        if (value?.data && Array.isArray(value?.data) && value?.data.length > 0) {
          this.brokenRoom = value.data.map((item) => item.name);
          this.isAllBrokenRoom = false;
        } else {
          this.brokenRoom = [];
          this.isAllBrokenRoom = true;
        }
      },
    });
  }

  /**
   * OpenPopoverCustomerGroup
   * @param event -Event
   */
  openPopoverCustomerGroup(event: Event) {
    const originElement = event.target as HTMLElement;

    const popupRef = this.popoverService.open({
      origin: originElement,
      content: CustomDropdownPopupFilterComponent,
      position: 0,
      width: 410,
      height: 280,
      data: LIST_MG_OPTIONS,
      componentConfig: {
        searchKey: ['name'],
        displayFnc: (v: any) => v.name,
        listFilterOptions: LIST_MG_OPTIONS,
      },
    });

    popupRef.afterClosed$.pipe(take(1)).subscribe({
      next: (value) => {
        if (value?.data && Array.isArray(value?.data) && value?.data.length > 0) {
          this.managementBroker = value.data.map((item) => item.name);
          this.isAllManagementBroker = false;
        } else {
          this.managementBroker = [];
          this.isAllManagementBroker = true;
        }
      },
    });
  }

  /**
   * Apply Filter
   */
  applyFilter() {
    if (this.createCustomerForm.invalid) {
      return;
    }

    const formData = this.createCustomerForm.value;

    const customers = (this.virtualScroll.getChecked() as IAllAccountNumber[]).map((c) => ({
      name: c.customerName ?? '',
      accountNo: c.accountNumber ?? '',
    }));

    const payload: ICusomterGroupPayload = {
      name: (formData.customerGroupName as string)?.toUpperCase(),
      // brokerDebtId: this.brokenRoom,
      brokerCode: this.brokerCode,
      members: customers,
      // members: customerAccountNumbers,
      // customers: selectedCustomers.map((c) => c.accountNumber ?? ''),
    };
    this.customerGroupStore.createCustomerGroup(payload);
  }

  /**
   * Inner filter function
   * @param {string} value search value
   * @param {IOptionList[]} options
   */
  private _filter(value: string, options: IOptionList[]): IOptionList[] {
    const filterValue = value.toLowerCase();

    return options.filter(
      (option) =>
        option.name?.toLowerCase().includes(filterValue) || option.accountNumber?.toLowerCase().includes(filterValue)
    );
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customer: IAllAccountNumber): string {
    if (!customer) return '';
    return `${customer.accountNumber} - ${customer.customerName}`;
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }
}
