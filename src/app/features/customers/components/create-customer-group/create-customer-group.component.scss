.customer-group-create-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  width: 450px;

  .header-create {
    padding: 16px 16px 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--color--other--divider);
  }

  .body-create {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow: auto;

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 24px;

      .item {
        display: flex;
        flex-direction: column;
        gap: 4px;

        ::ng-deep {
          .mat-mdc-form-field-infix {
            padding: 0 !important;
          }

          .mat-mdc-text-field-wrapper {
            border: none !important;
          }
        }
        .customer-input-wrapper {
          position: relative;
          .customer-input {
            padding: 8px 45px 8px 16px;
            height: 48px;
            border-radius: 8px;
            border: 1px solid var(--color--neutral--100);
            background: var(--Colors-Grey-<PERSON>, #fff);
            width: 100%;
            cursor: pointer;
            text-overflow: ellipsis;
          }

          .img-wrapper {
            position: absolute;
            right: 15px;
            top: 50%;
            height: 29px;
            width: 29px;
            transform: translateY(-50%);
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          img {
            width: 18px;
            height: 18px;
          }
        }

        .input-cls {
          height: 48px;
          padding: 8px 16px;
          border-radius: 8px;
          border: 1px solid var(--color--neutral--100);
          text-transform: uppercase;
        }
      }
    }

    .customer-list {
      display: flex;
      flex-direction: column;
      min-height: 416px;

      .title {
        color: var(--color--text--default);
        font-feature-settings: 'clig' off, 'liga' off;

        padding: 12px 24px;
      }

      .title-customer {
        color: var(--color--text--default);
        font-feature-settings: 'clig' off, 'liga' off;

        padding: 12px 24px;
      }

      .searchbox-wrap {
        padding: 8px 24px 16px;
      }
    }
  }

  .footer-create {
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 1px solid var(--color--other--divider);
    padding: 16px 24px;
    gap: 24px;

    .btn {
      padding: 12px 16px;
      border-radius: 8px;
      border: 1px solid var(--color--other--divider);
      width: 160px;
      background-color: var(--color--neutral--white);
    }

    .default {
      background-color: var(--color--background--selected);
    }

    .apply {
      background-color: var(--color--brand--500);
      color: var(--color--neutral--white);
      border: none;
    }
  }
}

::ng-deep {
  .dropdown-checkbox-container {
    width: 410px;
  }
}
