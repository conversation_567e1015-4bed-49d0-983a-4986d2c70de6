:host {
  width: 100%;
  display: flex;
  flex-direction: column;

  .body {
    padding: 24px 24px 16px 24px;
    display: flex;
    gap: 4px;
    .required {
      color: #eb4146;
    }

    .form-label {
      padding-bottom: 4px;
    }

    ::ng-deep {
      .mat-mdc-form-field-infix {
        padding: 0 !important;
      }

      .mat-mdc-text-field-wrapper {
        border: none !important;
      }
    }

    .input-cls {
      height: 48px;
      padding: 8px 16px;
      border-radius: 8px;
      border: 1px solid var(--color--neutral--100);
      text-transform: uppercase;
    }

    app-form-control {
      &:has(mat-error) {
        ::ng-deep {
          .form_err_msg {
            display: none;
          }
        }
      }
    }
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    padding: 16px 24px;
    border-top: 1px solid var(--color--other--divider);
    .button {
      padding: 12px 16px;
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      cursor: pointer;

      &.accept-cls {
        color: var(--color--neutral--white);
        background-color: var(--color--brand--500);
        border: none;
      }

      &.disable {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
}
