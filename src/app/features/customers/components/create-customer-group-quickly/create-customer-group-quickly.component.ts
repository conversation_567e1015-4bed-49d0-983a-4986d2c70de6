import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormControl, Validators } from '@angular/forms';

interface IDataDeleteNotification {}

@Component({
  selector: 'app-create-customer-group-quickly',
  templateUrl: './create-customer-group-quickly.component.html',
  styleUrls: ['./create-customer-group-quickly.component.scss'],
})
export class CreateCustomerGroupQuicklyComponent {
  customerNameControl = new FormControl(null, Validators.required);

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: IDataDeleteNotification,
    public dialogRef: MatDialogRef<CreateCustomerGroupQuicklyComponent>
  ) {}

  submit() {
    if (!this.customerNameControl.valid) return;
    const name = this.customerNameControl.value;
    this.dialogRef.close({ name: (name ?? '')?.toUpperCase() });
  }

  /**
   * Cancel Dialog
   */
  cancel() {
    console.log('cancel');
  }
}
