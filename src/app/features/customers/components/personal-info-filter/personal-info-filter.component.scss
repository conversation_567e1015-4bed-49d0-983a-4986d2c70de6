.personal-info-filter-component {
  display: flex;
  flex-direction: column;
  height: 100%;
  .personal-infor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid var(--color--other--divider);
    .img-cls {
      cursor: pointer;
    }
  }
  .type-account-header {
    padding: 12px 24px;
  }
  .personal-infor-body {
    flex: 1;
    overflow: auto;
  }

  .customers-cls {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--color--other--divider);

    .title {
      padding: 12px 24px;
    }

    .searchbox-wrap {
      padding: 8px 24px 16px;
      display: flex;
      flex-direction: column;
      gap: 17px;
      overflow: hidden;
      border-bottom: 1px solid var(--color--other--divider);
      min-height: 245px;

      .search-box {
        display: flex;
        position: relative;

        .input-cls-custom {
          padding: 10px 16px;
          padding-left: 32px;
          width: 100%;
        }

        .icon-search-cls {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 10px;
        }
      }

      .error-message {
        color: var(--color--danger--600);
        background-color: var(--color--danger--100);
        padding: 6px 12px;
        text-align: center;
      }

      .option-list-cls {
        height: 170px;
        overflow: auto;
        display: flex;
        flex-direction: column;
        gap: 16px;

        .checkbox-cls {
          ::ng-deep {
            .mdc-label {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;
            }
          }

          .img-cls {
            width: 18px;
            height: 18px;
            object-fit: contain;
            vertical-align: middle;
            border-radius: 50%;
          }
        }
      }
    }
  }

  .type-account-cls {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--color--other--divider);

    .option-list-cls {
      display: flex;
      flex-direction: column;
      .checkbox-cls {
        padding: 8px 24px;

        ::ng-deep {
          .mdc-label {
            font-size: 12px;
          }
        }
      }
    }

    .type-account-error-wrap {
      display: flex;
      justify-content: left;
      padding-left: 24px;

      .type-account-error {
        display: flex;
        align-items: center;
        padding: 6px 12px;
        background-color: var(--color--danger--100);
        color: var(--color--danger--600);
      }
    }
  }
  .calendar-cls {
    border-bottom: 1px solid var(--color--other--divider);
    padding-bottom: 16px;
    .input-cls-box {
      display: flex;
      padding: 0 24px;
      align-items: center;
      gap: 24px;

      mat-form-field {
        ::ng-deep {
          .mat-mdc-form-field-infix {
            padding: 0;
          }
        }
      }
      .input-calendar {
        display: flex;
        flex-direction: column;
        gap: 8px;
        flex: 1;
      }
      .input-box {
        position: relative;
        .input-cls-custom {
          padding: 8px 16px;
          padding-right: 28px;
          width: 100%;
        }
        img {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: 10px;
          cursor: pointer;
        }
        .box-show-error {
          position: absolute;
          top: -35px;
          left: 0;
          padding: 6px 12px;
          background-color: var(--color--danger--100);
          color: var(--color--danger--600);
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          .box-text-cls {
            position: relative;
            &::after {
              content: '';
              width: 0;
              height: 0;
              border-left: 6px solid transparent;
              border-right: 6px solid transparent;
              border-top: 6px solid var(--color--danger--100);
              position: absolute;
              bottom: -12px;
              left: 30%;
              transform: translateX(-50%);
            }
          }
        }
      }
    }
  }

  .personal-infor-footer {
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    border-top: 1px solid var(--color--other--divider);
    .btn {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      cursor: pointer;
      &.primary {
        color: var(--color--neutral--white);
        border: 1px solid var(--color--brand--500);
        background-color: var(--color--brand--500);
      }
    }
  }
}

:host {
  height: 100%;
  width: 100%;
}
