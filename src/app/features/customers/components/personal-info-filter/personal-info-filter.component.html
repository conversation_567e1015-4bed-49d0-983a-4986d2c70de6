<div class="personal-info-filter-component">
  <div class="personal-infor-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>
  <div class="personal-infor-body">
    <div class="customers-cls">
      <div class="title typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        @let customers = customers$ | async;

        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResults"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers ?? []"
          [selectedKeys]="data.customers"
          [maxSelectedItems]="MAX_ITEM_ACCOUNT_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_ACCOUNT_SELECTED }"
          [warningMessage]="'MES-646'"
          [messageSelectionComplete]="'MES-669'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-295' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          [displayFn]="displayCustomerFilter"
          (invalidSelection)="validateFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResults>
          <div class="typo-body-15">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>
    </div>

    <div class="type-account-cls">
      <div class="type-account-header typo-body-15">{{ 'MES-25' | translate }}</div>
      <div class="option-list-cls">
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isIndividual = $event.checked" [checked]="isIndividual" class="checkbox-cls">{{
            'MES-26' | translate
          }}</mat-checkbox>
        </div>
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isOrganization = $event.checked" [checked]="isOrganization" class="checkbox-cls">{{
            'MES-27' | translate
          }}</mat-checkbox>
        </div>
      </div>
      <div class="type-account-error-wrap">
        <div class="type-account-error typo-body-15" *ngIf="isTypeAccountValid()">
          <span>{{ 'MES-103' | translate }}</span>
        </div>
      </div>
    </div>

    <div class="calendar-cls">
      <div class="type-account-header typo-body-15">{{ 'MES-31' | translate }}</div>
      <div class="input-cls-box">
        <div class="input-calendar">
          <div class="typo-body-12">{{ 'MES-29' | translate }}</div>
          <div class="input-box">
            <mat-form-field>
              <input
                class="input-cls-custom typo-body-11 fs-12"
                matInput
                [matDatepicker]="dateStart"
                [formControl]="dateStartControl"
                placeholder="YYYY"
                [max]="maxDate"
              />
              <mat-datepicker
                #dateStart
                startView="multi-year"
                (yearSelected)="setYear($event, dateStart, 'start')"
                panelClass="example-month-picker"
                [calendarHeaderComponent]="DatepickerCustomHeader"
              >
              </mat-datepicker>
            </mat-form-field>
            <img src="./assets/icons/calendar.svg" alt="calendar" (click)="dateStart.open()" />
            <div class="box-show-error" *ngIf="validText">
              <div class="typo-body-9 box-text-cls">{{ validText }}</div>
            </div>
          </div>
        </div>
        <div class="input-calendar">
          <div class="typo-body-12">{{ 'MES-30' | translate }}</div>
          <div class="input-box">
            <mat-form-field>
              <input
                class="input-cls-custom typo-body-11 fs-12"
                matInput
                [matDatepicker]="dateEnd"
                [formControl]="dateEndControl"
                placeholder="YYYY"
                [max]="maxDate"
                [min]="minDate"
              />
              <mat-datepicker
                #dateEnd
                startView="multi-year"
                (yearSelected)="setYear($event, dateEnd, 'end')"
                panelClass="example-month-picker"
                [calendarHeaderComponent]="DatepickerCustomHeader"
              >
              </mat-datepicker>
            </mat-form-field>
            <img src="./assets/icons/calendar.svg" alt="calendar" (click)="dateEnd.open()" />
            <div class="box-show-error" *ngIf="validTextEnd">
              <div class="typo-body-9 box-text-cls">{{ validTextEnd }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="personal-infor-footer">
    <div (click)="setDefaultFilter()" class="btn typo-button-3">{{ 'MES-32' | translate }}</div>
    <button [disabled]="hasValidationErrors() || isDisableApply" (click)="submit()" class="btn primary typo-button-3">
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
