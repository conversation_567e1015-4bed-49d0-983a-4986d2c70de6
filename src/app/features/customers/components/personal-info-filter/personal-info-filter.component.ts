import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  Ng<PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDatepicker } from '@angular/material/datepicker';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DatePickerNavigatorComponent } from 'src/app/shared/components/date-picker/date-picker-navigator/date-picker-navigator.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IFilterPersonalInfoParam } from '../../model/customer';
import { Observable, debounceTime, takeUntil, tap } from 'rxjs';
import { DestroyService, LoadingService } from 'src/app/core/services';

import { ETypeAccount } from '../../constants/customers';
import { Store } from '@ngrx/store';
import { selectAllAccountNumberListByBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { MAX_ITEM_ACCOUNT_SELECTED } from 'src/app/shared/constants/global';

export const CALENDAR_CONFIG = {
  parse: {
    dateInput: 'YYYY',
  },
  display: {
    dateInput: 'YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY',
  },
};

/**
 * PersonalInfoFilterComponent
 */
@Component({
  selector: 'app-personal-info-filter-component',
  templateUrl: './personal-info-filter.component.html',
  styleUrls: ['./personal-info-filter.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    { provide: MAT_DATE_FORMATS, useValue: CALENDAR_CONFIG },
    DestroyService,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PersonalInfoFilterComponent implements OnInit {
  @ViewChild(VirtualScrollListComponent) virtualScroll!: VirtualScrollListComponent;

  dateStartControl = new FormControl();
  dateEndControl = new FormControl();
  maxDate: Date | null = null;
  minDate: Date | null = null;
  validText = '';
  validTextEnd = '';
  DatepickerCustomHeader = DatePickerNavigatorComponent;
  isIndividual = false;
  isOrganization = false;
  // Key identifier for customer objects
  readonly customerKey = 'accountNumber';
  selectedCustomers: string[] = [];
  customers$: Observable<IAllAccountNumber[]> = new Observable<IAllAccountNumber[]>();
  isDisableApply = false;

  MAX_ITEM_ACCOUNT_SELECTED = MAX_ITEM_ACCOUNT_SELECTED;

  constructor(
    public dialogRef: MatDialogRef<PersonalInfoFilterComponent>,
    private readonly store: Store,
    private readonly loadingService: LoadingService,
    private readonly ngZone: NgZone,
    private readonly cdr: ChangeDetectorRef,
    @Inject(MAT_DIALOG_DATA) public data: IFilterPersonalInfoParam,
    private readonly _destroy: DestroyService
  ) {
    this.initializeFromData(data);

    this.customers$ = this.store.select(selectAllAccountNumberListByBrokerView$);
  }

  /**
   * Initialize component state from input data
   * @param data Filter parameters from dialog input
   */
  private initializeFromData(data: IFilterPersonalInfoParam): void {
    const { typeAccount, startYear, endYear } = data;

    // Set account type flags
    this.initializeAccountTypes(typeAccount);

    // Set date values if provided
    this.initializeDateValues(startYear, endYear);
  }

  /**
   * Initialize account type selection based on input
   * @param typeAccount Account type from filter parameters
   */
  private initializeAccountTypes(typeAccount: string): void {
    if (+typeAccount === ETypeAccount.INDIVIDUAL) {
      this.isIndividual = true;
    } else if (+typeAccount === ETypeAccount.ORGANIZATION) {
      this.isOrganization = true;
    } else {
      this.isOrganization = true;
      this.isIndividual = true;
    }
  }

  /**
   * Initialize date values if provided in input data
   * @param startYear Start year value
   * @param endYear End year value
   */
  private initializeDateValues(startYear?: string, endYear?: string): void {
    if (startYear) {
      const dateStart = new Date(+startYear, 0, 1);
      this.dateStartControl.patchValue(dateStart);
    }

    if (endYear) {
      const dateEnd = new Date(+endYear, 0, 1);
      this.dateEndControl.patchValue(dateEnd);
    }
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.initializeComponent();
  }

  private initializeComponent(): void {
    this.initializeDateValidation();
  }

  /**
   * Initialize date validation for start and end date controls
   */
  private initializeDateValidation(): void {
    const dateValidationPipe = (type: 'start' | 'end') => {
      return (source$: Observable<any>) =>
        source$.pipe(
          debounceTime(300),
          takeUntil(this._destroy),
          tap((value) => {
            // Handle moment.js date objects safely
            if (typeof value !== 'string' && value && typeof value === 'object' && '_d' in value) {
              this.checkValidDate(value._d, type);
            }
          })
        );
    };

    const startDatePipe = dateValidationPipe('start' as const);
    const endDatePipe = dateValidationPipe('end' as const);

    // Apply the pipes to date controls
    this.dateStartControl.valueChanges.pipe(startDatePipe).subscribe();
    this.dateEndControl.valueChanges.pipe(endDatePipe).subscribe();
  }

  /**
   * Function view year for calendar
   *  @param {any} year
   *  @param {MatDatepicker<any>} datepicker MatDatepicker
   *  @param {string} type start | end
   */
  setYear(year: any, datepicker: MatDatepicker<any>, type: 'start' | 'end') {
    const currentDate = (type === 'start' ? this.dateStartControl.value : this.dateEndControl.value) ?? new Date();
    const updatedDate = new Date(currentDate);
    updatedDate.setFullYear(year._i['year']);
    if (type === 'start') {
      this.dateStartControl.setValue(updatedDate);
    } else {
      this.dateEndControl.setValue(updatedDate);
    }
    this.checkValidDate(updatedDate, type);
    datepicker.close();
  }



  /**
   * Submit filter values and close dialog
   * @returns void (prevents event submit if validation fails)
   */
  submit(): void {
    // Validate before submission
    if (this.hasValidationErrors()) {
      return;
    }

    const optionFilter = this.buildFilterOptions();

    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * Check if there are any validation errors preventing submission
   * @returns True if errors exist
   */
  hasValidationErrors(): boolean {
    return Boolean(this.validText || this.validTextEnd || this.isTypeAccountValid());
  }

  /**
   * Build filter options object from component state
   * @returns Filter options object
   */
  private buildFilterOptions(): IFilterPersonalInfoParam {
    // Determine account type based on selection
    const accountType = this.getSelectedAccountTypes();

    // Get date values
    const startDate = this.dateStartControl.value;
    const endDate = this.dateEndControl.value;

    const parseYear = (date: any): string => {
      if (!date) return '';
      return new Date(date).getFullYear().toString();
    };

    const customers = ((this.virtualScroll?.getChecked() ?? []) as IAllAccountNumber[]).map((c) => c.accountNumber);

    return {
      customers,
      typeAccount: accountType,
      startYear: parseYear(startDate),
      endYear: parseYear(endDate),
      isFilter: true,
    };
  }

  /**
   * Determine account type based on selection
   * @returns Account type string
   */
  /**
   * Get selected account types based on checkbox selection
   * @returns String representation of account type selection
   */
  private getSelectedAccountTypes(): string {
    if (this.isIndividual && this.isOrganization) return '';
    if (this.isIndividual) return ETypeAccount.INDIVIDUAL.toString();
    if (this.isOrganization) return ETypeAccount.ORGANIZATION.toString();
    return '';
  }

  /**
   * SetDefaultFilter
   */
  setDefaultFilter() {
    this.loadingService.show();
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * Validate date ranges to ensure start date is before end date
   * @param date Date being validated
   * @param type 'start' or 'end' to identify which date is being validated
   */
  checkValidDate(date: Date, type: 'start' | 'end'): void {
    // Run validation outside Angular zone for better performance
    this.ngZone.runOutsideAngular(() => {
      // Extract logic for start date validation
      if (type === 'start' && this.dateEndControl.value) {
        this.validateStartDate(date);
      }
      // Extract logic for end date validation
      else if (type === 'end' && this.dateStartControl.value) {
        this.validateEndDate(date);
      }

      // Update UI only when necessary
      this.ngZone.run(() => this.cdr.markForCheck());
    });
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  /**
   * Validate start date is before end date
   * @param startDate Date to validate
   */
  private validateStartDate(startDate: Date): void {
    // Clear end date validation message first
    this.validTextEnd = '';

    const dateEnd = new Date(this.dateEndControl.value);
    const timeStartDate = startDate.getTime();
    const timeEndDate = dateEnd.getTime();

    // Validate start date is before end date
    this.validText = timeStartDate > timeEndDate ? 'Phải nhỏ hơn "Tới năm"' : '';
  }

  /**
   * Validate end date is after start date
   * @param endDate Date to validate
   */
  private validateEndDate(endDate: Date): void {
    // Clear start date validation message first
    this.validText = '';

    const dateStart = new Date(this.dateStartControl.value);
    const timeStartDate = dateStart.getTime();
    const timeEndDate = endDate.getTime();

    // Validate end date is after start date
    this.validTextEnd = timeStartDate > timeEndDate ? 'Phải lớn hơn "Từ năm"' : '';
  }

  isTypeAccountValid() {
    return !this.isIndividual && !this.isOrganization;
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customer: IAllAccountNumber): string {
    if (!customer) return '';
    return `${customer.accountNumber} - ${customer.customerName}`;
  }
}
