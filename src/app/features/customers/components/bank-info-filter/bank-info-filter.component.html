<div class="personal-info-filter-component">
  <div class="personal-infor-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="personal-infor-body">
    <div class="type-account-cls">
      <div class="type-account-header typo-body-15">{{ 'MES-25' | translate }}</div>
      <div class="option-list-cls">
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isIndividual = $event.checked" [checked]="isIndividual" class="checkbox-cls">{{
            'MES-26' | translate
          }}</mat-checkbox>
        </div>
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isOrganization = $event.checked" [checked]="isOrganization" class="checkbox-cls">{{
            'MES-27' | translate
          }}</mat-checkbox>
        </div>
      </div>
    </div>

    <div class="type-account-cls scroll-page">
      <div class="type-account-header typo-body-15">{{ 'MES-34' | translate }}</div>

      <div class="searchbox-wrap">
        <div class="search-box">
          <input
            type="text"
            class="input-search input-style-common typo-body-12"
            [placeholder]="'MES-14' | translate"
            [formControl]="control"
          />
          <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
        </div>
        <div class="option-list-cls">
          <mat-checkbox (change)="changeSections($event.checked, 'all')" [checked]="isSelectAll" class="checkbox-cls">
            {{ 'MES-58' | translate }}</mat-checkbox
          >
          @for (item of listFilterOptions; let i = $index; track item) {
          <div class="checkbox-cls-item">
            <mat-checkbox
              (change)="changeSections($event.checked, 'item', item)"
              [checked]="item.isSelect"
              class="checkbox-cls"
            >
              <img class="img-cls" [src]="item.url" [alt]="item.label" />
              {{ item.label | translate }}</mat-checkbox
            >
          </div>
          }
        </div>
      </div>
    </div>
  </div>
  <div class="personal-infor-footer">
    <div (click)="defaultFilter()" class="btn typo-button-3">{{ 'MES-32' | translate }}</div>
    <div (click)="applyFilter()" class="btn primary typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
