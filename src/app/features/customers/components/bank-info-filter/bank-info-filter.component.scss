.personal-info-filter-component {
  display: flex;
  flex-direction: column;
  height: 100%;

  .personal-infor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid var(--color--other--divider);

    .img-cls {
      cursor: pointer;
    }
  }

  .type-account-header {
    padding: 12px 24px;
  }

  .personal-infor-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .type-account-cls {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--color--other--divider);
    &.scroll-page {
      flex: 1;
      overflow: hidden;
    }
    .option-list-cls {
      display: flex;
      flex-direction: column;
      overflow: auto;
      .checkbox-cls {
        padding: 8px 24px;

        ::ng-deep {
          .mdc-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
          }
        }
      }
    }

    .searchbox-wrap {
      padding: 0 24px;
      margin-bottom: 20px;
      display: flex;
      flex-direction: column;
      flex: 1;
      gap: 16px;
      overflow: hidden;
      .search-box {
        display: flex;
        position: relative;
        .input-search {
          padding: 10px 16px;
          padding-left: 32px;
          width: 100%;
        }

        .icon-search-cls {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 10px;
        }
      }
      .checkbox-cls {
        padding: 8px 0px;
        ::ng-deep {
          .mdc-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
          }
        }
        .img-cls {
          width: 18px;
          height: 18px;
          object-fit: contain;
          vertical-align: middle;
          border-radius: 50%;
        }
      }
    }
  }

  .calendar-cls {
    border-bottom: 1px solid var(--color--other--divider);
    padding-bottom: 16px;

    .input-cls-box {
      display: flex;
      padding: 0 24px;
      align-items: center;
      gap: 24px;

      .input-calendar {
        display: flex;
        flex-direction: column;
        gap: 8px;
        flex: 1;
      }

      .input-box {
        position: relative;

        .input-cls-custom {
          padding: 8px 16px;
          padding-right: 28px;
          width: 100%;
          font-size: 12px;
        }

        img {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: 10px;
          cursor: pointer;
        }
      }
    }
  }

  .personal-infor-footer {
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    border-top: 1px solid var(--color--other--divider);

    .btn {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      cursor: pointer;

      &.primary {
        color: var(--color--neutral--white);
        border: 1px solid var(--color--brand--500);
        background-color: var(--color--brand--500);
      }
    }
  }
}

:host {
  height: 100%;
  width: 100%;
}
