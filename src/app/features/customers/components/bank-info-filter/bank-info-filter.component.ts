import { Component, EventEmitter, Inject, OnInit, Output, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SearchDropdownCustomComponent } from 'src/app/shared/components/search-dropdown-custom/search-dropdown-custom.component';
import { LIST_OF_BANK } from 'src/app/shared/constants/bank';
import { IOptionList } from 'src/app/shared/models/dropdown-item.model';
import { IFilterBankInfoParam } from '../../model/customer';
import { FormControl } from '@angular/forms';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { DestroyService, LoadingService } from 'src/app/core/services';
import { Store } from '@ngrx/store';
import { selectAllBankList$ } from 'src/app/stores/shared/shared.selectors';
import { ETypeAccount } from '../../constants/customers';

/**
 * BankInfoFilterComponent
 */
@Component({
  selector: 'app-personal-info-filter',
  templateUrl: './bank-info-filter.component.html',
  styleUrls: ['./bank-info-filter.component.scss'],
})
export class BankInfoFilterComponent implements OnInit {
  @ViewChild('searchDropdownCustom') searchDropdownCustom!: SearchDropdownCustomComponent;

  listFilterOptions: IOptionList[] = [];

  private _options: IOptionList[] = [];

  @Output() changeSearchValueEvent = new EventEmitter<string>();

  @Output() changeListEvent = new EventEmitter<IOptionList[]>();

  isSelectAll = true;

  isIndividual = false;

  isOrganization = false;

  control = new FormControl();
  /**
   * Constructor
   * @param {IFilterBankInfoParam} data
   * @param {MatDialogRef<BankInfoFilterComponent>} dialogRef
   * @param {DestroyService} _destroy
   * @param loadingService LoadingService
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: IFilterBankInfoParam,
    public dialogRef: MatDialogRef<BankInfoFilterComponent>,
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly loadingService: LoadingService
  ) {
    const { typeAccount } = data;
    typeAccount.forEach((t) => {
      if (t === ETypeAccount.INDIVIDUAL) this.isIndividual = true;
      else if (t === ETypeAccount.ORGANIZATION) this.isOrganization = true;
    });
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.control.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterOptions = this._filter(value ?? '');
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.store
      .select(selectAllBankList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((banks) => {
        this._options = banks.map((bank) => ({
          label: bank.name,
          value: bank.id,
          url: LIST_OF_BANK.find((b) => b.name === bank.name)?.logo,
          isSelect: true,
          id: bank.id,
        }));
        this.listFilterOptions = this._options;
      });

    if (this.data.bankIds.length > 0 && this.data.bankIds[0] !== 'all') {
      const newDataFilter = this.listFilterOptions.map((t) => {
        const isBankSelected = this.data.bankIds.includes(t.id ?? '');
        if (isBankSelected) {
          t.isSelect = true;
        } else t.isSelect = false;

        return t;
      });
      this.isSelectAll = newDataFilter.every((t) => t.isSelect);
      this.listFilterOptions = newDataFilter;
    }
  }

  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param type Kiểu giá trị được thay đổi
   * @param item Item thay đổi
   */
  changeSections(checked: boolean, type: string, item?: IOptionList) {
    if (type === 'all') {
      this.isSelectAll = checked;
      this.listFilterOptions.forEach((i) => {
        i.isSelect = checked;
      });
    }
    if (type === 'item' && item) {
      item.isSelect = checked;
      this.isSelectAll = this.checkIsShowAll();
    }
  }

  /**
   * Kiểm tra xem list có được show hết hay không?
   */
  checkIsShowAll() {
    for (const obj of this.listFilterOptions) {
      // Kiểm tra xem 'isSelect' có trong đối tượng không
      if (!('isSelect' in obj)) {
        return false; // Nếu không tồn tại, trả về false
      }

      // Kiểm tra xem giá trị của thuộc tính isSelect là true hay không
      if (!obj.isSelect) {
        return false; // Nếu giá trị là false, trả về false
      }
    }
    // Nếu không có trường hợp nào gặp return false ở trên, mặc định trả về true
    return true;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    this.loadingService.show();
    const accountType: number[] = [];
    if (this.isIndividual && this.isOrganization) accountType.push(ETypeAccount.INDIVIDUAL, ETypeAccount.ORGANIZATION);
    else if (this.isIndividual) accountType.push(ETypeAccount.INDIVIDUAL);
    else if (this.isOrganization) accountType.push(ETypeAccount.ORGANIZATION);

    const bankIds = this.listFilterOptions.filter((t) => t.isSelect).map((t) => t.id);
    this.dialogRef.close({
      type: 'save',
      optionFilter: {
        accountType,
        bankIds,
        isFilter: !(this.isSelectAll && accountType.length === 2),
      },
    });
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.loadingService.show();
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * Inner filter function
   * @param {string} value search value
   */
  private _filter(value: string): IOptionList[] {
    const filterValue = value.toString().toLowerCase();

    return this._options.filter((option) => {
      return option.label?.toString().toLowerCase()?.includes(filterValue);
    });
  }
}
