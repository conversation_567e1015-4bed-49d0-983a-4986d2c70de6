.customer-group-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  width: 400px;

  .header-filter {
    padding: 16px 16px 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--color--other--divider);
  }

  .searchbox-wrap {
    padding: 0 24px;

    ::ng-deep {
      .virtual-scroll-viewport {
        height: 500px !important;
      }
    }
  }

  .body-filter {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow: auto;

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 24px;
      border-bottom: 1px solid var(--color--other--divider);
      .item {
        display: flex;
        flex-direction: column;
        gap: 4px;

        ::ng-deep {
          .mat-mdc-form-field-infix {
            padding: 0 !important;
          }
        }
        .customer-input-wrapper {
          position: relative;
          .customer-input {
            padding: 8px 45px 8px 16px;
            height: 48px;
            border-radius: 8px;
            border: 1px solid var(--color--neutral--100);
            background: var(--Colors-Grey-White, #fff);
            width: 100%;
            cursor: pointer;
            text-overflow: ellipsis;
          }

          .img-wrapper {
            position: absolute;
            right: 15px;
            top: 50%;
            height: 29px;
            width: 29px;
            transform: translateY(-50%);
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          img {
            width: 18px;
            height: 18px;
          }
        }

        .input-cls {
          height: 48px;
          padding: 8px 16px;
          border-radius: 8px;
          border: 1px solid var(--color--neutral--100);
        }
      }
    }

    .customer-list {
      display: flex;
      flex-direction: column;
      min-height: 416px;

      .title {
        color: var(--color--text--default);
        font-feature-settings: 'clig' off, 'liga' off;
        padding: 12px 24px;
      }

      .search-box {
        margin: 8px 24px 17px;
        display: flex;
        position: relative;
        .input-search {
          padding: 10px 16px;
          padding-left: 32px;
          width: 100%;
        }

        .icon-search-cls {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 10px;
        }
      }

      .option-list-cls {
        display: flex;
        flex-direction: column;
        gap: 16px;
        overflow: auto;
        padding: 0 24px;

        .checkbox-cls-item {
          display: flex;
          align-items: center;

          ::ng-deep {
            .mdc-label {
              font-size: 12px;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          ::ng-deep {
            .checkbox-cls {
              width: 100% !important;

              .mdc-form-field {
                width: 100% !important;
              }
            }
          }
        }
      }
    }
  }

  .footer-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--color--other--divider);
    padding: 16px 24px;

    .btn {
      padding: 12px 16px;
      border-radius: 8px;
      border: 1px solid var(--color--other--divider);

      &.disable-btn {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .apply {
      background-color: var(--color--brand--500);
      color: var(--color--neutral--white);
    }
  }
}

::ng-deep {
  ng-component {
    width: 100% !important;
  }
}
