import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { take, tap } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { Store } from '@ngrx/store';
import { CustomerGroupContainer } from '../../containers/customer-group/customer-group.container';
import { selectAllAccountNumberListByBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { IAllAccountNumber, IFilterParamCustomerGroup } from 'src/app/shared/models/global';
import { MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';

export interface IOptionList {
  name?: string;
  accountNumber?: string;
  id?: number;
  isSelect?: boolean;
}

export interface IBrokenList {
  broker: string;
  brokenRoom: string;
  isSelect: boolean;
  name: string;
}

/**
 * CustomerGroupFilterComponent
 */
@Component({
  selector: 'app-customer-group-filter',
  templateUrl: './customer-group-filter.component.html',
  styleUrl: './customer-group-filter.component.scss',
})
export class CustomerGroupFilterComponent implements OnInit {
  @ViewChild(VirtualScrollListComponent) virtualScroll!: VirtualScrollListComponent;

  groupNameControl = new FormControl();

  // Key identifier for customer objects
  readonly customerKey = 'accountNumber';

  customers: IAllAccountNumber[] = [];

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  isDisableApply = false;

  /**
   *SelectionModel Constructor
   * @param _destroy
   * @param popoverService
   * @param dialogRef
   * @param fb - FormBuilder
   * @param popoverRef
   *  @param data IFilterParamCustomerGroup
   * @param store Store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    public dialogRef: MatDialogRef<CustomerGroupContainer>,
    private readonly fb: FormBuilder,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: IFilterParamCustomerGroup,
    private readonly store: Store
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.loadCustomerList();
  }

  private loadCustomerList() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(
        take(1),
        tap((customers) => {
          this.customers = customers;
        })
      )
      .subscribe();
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  /**
   * Apply Filter
   */
  applyFilter() {
    if (this.isDisableApply) return;
    const accountNumber = (this.virtualScroll.getChecked() as IAllAccountNumber[]).map((c) => c.accountNumber);

    const optionFilter = {
      accountNumber,
      isFilter: accountNumber.length > 0,
    };

    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customer: IAllAccountNumber): string {
    if (!customer) return '';
    return `${customer.accountNumber} - ${customer.customerName}`;
  }
}
