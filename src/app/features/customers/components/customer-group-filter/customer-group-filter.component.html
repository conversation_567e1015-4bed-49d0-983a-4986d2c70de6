<div class="customer-group-wrapper">
  <!-- HEADER -->
  <div class="header-filter">
    <div class="typo-body-16">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <!-- CONTENT -->
  <div class="body-filter">
    <div class="customer-list">
      <div class="title typo-body-15">{{ 'MES-96' | translate }}</div>

      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResults"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [selectedKeys]="data.accountNumbers"
          [messageSelectionComplete]="'MES-669'"
          (invalidSelection)="validateFilterValue($event)"
          [warningMessage]="'MES-668'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-295' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          [displayFn]="displayCustomerFilter"
        ></app-virtual-scroll-list>

        <ng-template #noResults>
          <div class="group-customer-cl typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>
    </div>
  </div>

  <!-- FOOTER -->
  <div class="footer-filter">
    <button mat-dialog-close (click)="defaultFilter()" class="btn default typo-button-3">
      {{ 'MES-32' | translate }}
    </button>
    <button [class.disable-btn]="isDisableApply" (click)="applyFilter()" class="btn apply typo-button-3">
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
