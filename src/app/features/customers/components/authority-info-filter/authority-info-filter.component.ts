import { Component, Inject } from '@angular/core';
import { IFilterAuthorityInfoParam } from '../../model/customer';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DestroyService } from 'src/app/core/services';

/**
 * PersonalInfoFilterComponent
 */
@Component({
  selector: 'app-authority-info-filter-component',
  templateUrl: './authority-info-filter.component.html',
  styleUrls: ['./authority-info-filter.component.scss'],
})
export class AuthorityInfoFilterComponent {
  isIndividual = false;
  isOrganization = false;

  authority = false;
  notAuthority = false;

  /**
   * Constructor
   * @param data IFilterAuthorityInfoParam
   * @param dialogRef MatDialogRef<DocumentInfoFilterComponent>
   * @param _destroy DestroyService
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: IFilterAuthorityInfoParam,
    public dialogRef: MatDialogRef<AuthorityInfoFilterComponent>,
    private readonly _destroy: DestroyService
  ) {
    const { typeAccount, authorityStatus } = data;
    typeAccount.forEach((type) => {
      if (type === 0) this.isIndividual = true;
      if (type === 1) this.isOrganization = true;
    });

    authorityStatus.forEach((status) => {
      if (status === 0) this.notAuthority = true;
      if (status === 1) this.authority = true;
    });
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    const accountType: number[] = [];
    if (this.isIndividual && this.isOrganization) {
      accountType.push(0, 1);
    } else if (this.isIndividual) {
      accountType.push(0);
    } else if (this.isOrganization) accountType.push(1);

    const authorityStatus: number[] = [];
    if (this.authority && this.notAuthority) {
      authorityStatus.push(0, 1);
    } else if (this.notAuthority) {
      authorityStatus.push(0);
    } else if (this.authority) authorityStatus.push(1);

    const optionFilter = {
      accountType,
      authorityStatus,
      isFilter: !(accountType.length == 2 && authorityStatus.length == 2),
    };

    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }
}
