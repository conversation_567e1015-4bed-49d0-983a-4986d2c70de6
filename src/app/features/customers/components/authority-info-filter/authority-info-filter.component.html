<div class="personal-info-filter-component">
  <div class="personal-infor-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>
  <div class="personal-infor-body">
    <div class="type-account-cls">
      <div class="type-account-header typo-body-15">{{ 'MES-25' | translate }}</div>
      <div class="option-list-cls">
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isIndividual = $event.checked" [checked]="isIndividual" class="checkbox-cls">{{
            'MES-26' | translate
          }}</mat-checkbox>
        </div>
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isOrganization = $event.checked" [checked]="isOrganization" class="checkbox-cls">{{
            'MES-27' | translate
          }}</mat-checkbox>
        </div>
      </div>
    </div>
    <div class="type-account-cls">
      <div class="type-account-header typo-body-15">{{ 'MES-61' | translate }}</div>
      <div class="option-list-cls">
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="authority = $event.checked" [checked]="authority" class="checkbox-cls">{{
            'MES-59' | translate
          }}</mat-checkbox>
        </div>
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="notAuthority = $event.checked" [checked]="notAuthority" class="checkbox-cls">{{
            'MES-60' | translate
          }}</mat-checkbox>
        </div>
      </div>
    </div>
  </div>
  <div class="personal-infor-footer">
    <div (click)="defaultFilter()" class="btn typo-button-3">{{ 'MES-32' | translate }}</div>
    <div (click)="applyFilter()" class="btn primary typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
