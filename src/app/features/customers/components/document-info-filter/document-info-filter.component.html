<div class="document-info-filter-component">
  <div class="document-infor-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>
  <div class="document-infor-body">
    <!--  Loại tà<PERSON>n -->
    <div class="type-account-cls">
      <div class="type-account-header typo-body-15">{{ 'MES-25' | translate }}</div>
      <div class="option-list-cls">
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isIndividual = $event.checked" [checked]="isIndividual" class="checkbox-cls">{{
            'MES-26' | translate
          }}</mat-checkbox>
        </div>
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isOrganization = $event.checked" [checked]="isOrganization" class="checkbox-cls">{{
            'MES-27' | translate
          }}</mat-checkbox>
        </div>
      </div>
    </div>

    <!-- Số Hợp Đồng -->
    @for(contractType of contractTypes; track contractType) {
    <ng-container>
      <div class="contract-cls">
        <div class="contract-header typo-body-15">{{ contractType.label | translate }}</div>
        <div class="option-list-cls">
          <div class="checkbox-cls-item">
            <mat-checkbox
              (change)="contractType.has = $event.checked"
              [checked]="contractType.has"
              class="checkbox-cls"
            >
              {{ 'MES-46' | translate }}
            </mat-checkbox>
          </div>
          <div class="checkbox-cls-item">
            <mat-checkbox
              (change)="contractType.notHas = $event.checked"
              [checked]="contractType.notHas"
              class="checkbox-cls"
            >
              {{ 'MES-47' | translate }}
            </mat-checkbox>
          </div>
        </div>
      </div>
    </ng-container>
    }
  </div>

  <div class="document-infor-footer">
    <div (click)="defaultFilter()" class="btn typo-button-3">{{ 'MES-32' | translate }}</div>
    <div (click)="applyFilter()" class="btn primary typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
