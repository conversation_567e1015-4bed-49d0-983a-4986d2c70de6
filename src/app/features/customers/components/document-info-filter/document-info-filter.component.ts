import { Component, Inject } from '@angular/core';
import { IFilterDocumentInfoParam } from '../../model/customer';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DestroyService } from 'src/app/core/services';
import { ETypeAccount } from '../../constants/customers';

interface IContractType {
  id: string;
  has?: boolean;
  notHas?: boolean;
  label: string;
}

/**
 * DocumentInfoFilterComponent
 */
@Component({
  selector: 'app-document-info-filter',
  templateUrl: './document-info-filter.component.html',
  styleUrl: './document-info-filter.component.scss',
})
export class DocumentInfoFilterComponent {
  isIndividual = false;
  isOrganization = false;
  contractTypes: IContractType[] = [
    { id: '00', label: 'HĐ Cơ Sở' },
    { id: '01', label: 'HĐ Ký Quỹ' },
    { id: '02', label: 'HĐ 3 Bên BIDV' },
    { id: '03', label: 'HĐ 3 Bên SHB' },
    { id: '04', label: 'HĐ Margin T+' },
    { id: '05', label: 'HĐ Trái Phiếu' },
    { id: '80', label: 'HĐ Phái Sinh' },
  ];

  /**
   * Constructor
   * @param data IFilterDocumentInfoParam
   * @param dialogRef MatDialogRef<DocumentInfoFilterComponent>
   * @param _destroy DestroyService
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: IFilterDocumentInfoParam,
    public dialogRef: MatDialogRef<DocumentInfoFilterComponent>,
    private readonly _destroy: DestroyService
  ) {
    this.initializeFilters(data);
  }

  /**
   * InitializeFilters
   * @param data IFilterDocumentInfoParam
   */
  private initializeFilters(data: IFilterDocumentInfoParam) {
    const { typeAccount } = data;
    typeAccount.forEach((type) => {
      if (type === ETypeAccount.INDIVIDUAL) this.isIndividual = true;
      else if (type === ETypeAccount.ORGANIZATION) this.isOrganization = true;
    });

    this.contractTypes.forEach((contractType) => {
      const typeContractKey = `contract${contractType.id}` as keyof IFilterDocumentInfoParam;
      const contractData = data[typeContractKey];

      if (Array.isArray(contractData)) {
        contractData.forEach((contract) => {
          if (contract === 0) contractType.notHas = true;
          if (contract === 1) contractType.has = true;
        });
      }
    });
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    const accountType = this.getAccountTypes();
    const contractTypes = this.getContractTypes();

    // Check if each contract type has both 0 and 1 selected
    const isContractTypesFullySelected = Object.keys(contractTypes).every((key) => {
      const contracts = contractTypes[key];
      return contracts.length === 2;
    });

    let isFilter = true;
    if (accountType.length === 2 && isContractTypesFullySelected) {
      isFilter = false;
    }

    this.dialogRef.close({
      type: 'save',
      optionFilter: {
        accountType,
        ...contractTypes,
        isFilter,
      },
    });
  }

  /**
   * getAccountTypes
   * @returns {Array} [0,1]
   */
  private getAccountTypes(): number[] {
    const accountType: number[] = [];
    if (this.isIndividual && this.isOrganization) accountType.push(ETypeAccount.INDIVIDUAL, ETypeAccount.ORGANIZATION);
    else if (this.isIndividual) accountType.push(ETypeAccount.INDIVIDUAL);
    else if (this.isOrganization) accountType.push(ETypeAccount.ORGANIZATION);
    return accountType;
  }

  /**
   * getContractTypes
   * @returns {string} contract + id
   */
  private getContractTypes(): { [key: string]: number[] } {
    const result: { [key: string]: number[] } = {};
    this.contractTypes.forEach((contractType) => {
      const key = `contract${contractType.id}`;
      result[key] = [];

      if (contractType.has && contractType.notHas) {
        result[key] = [0, 1];
      } else if (contractType.has) {
        result[key] = [1];
      } else if (contractType.notHas) {
        result[key] = [0];
      }
    });
    return result;
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }
}
