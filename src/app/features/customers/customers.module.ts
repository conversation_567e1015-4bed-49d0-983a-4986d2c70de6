import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { AccountInfoCustomerContainer } from './containers/account-info/account-info.container';
import { BankInfoCustomerContainer } from './containers/bank-info/bank-info.container';
import { PersonalInfoCustomerContainer } from './containers/personal-info/personal-info.container';
import { CustomersRoutingModule } from './customers-routing.module';
import { CustomersView } from './views/customers.view';

import { MatTabsModule } from '@angular/material/tabs';
import { UnsaveChangeGuard } from 'src/app/core/guards/unsaved-changes.guard';
import { ActionBtnComponent } from 'src/app/shared/components/action-btn/action-btn.component';
import { CalendarCustomComponent } from 'src/app/shared/components/date-picker/date-picker/date-picker.component';
import { FilterComponent } from 'src/app/shared/components/filter/filter.component';
import { InputProportionComponent } from 'src/app/shared/components/input-custom-for-table/input-proportion/input-proportion.component';
import { PhoneNumberTableComponent } from 'src/app/shared/components/phone-number-table/phone-number-table.component';
import { SearchDropdownCustomComponent } from 'src/app/shared/components/search-dropdown-custom/search-dropdown-custom.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { AccountInfoFilterComponent } from './components/account-info-filter/account-info-filter.component';
import { AuthorityInfoFilterComponent } from './components/authority-info-filter/authority-info-filter.component';
import { BankInfoFilterComponent } from './components/bank-info-filter/bank-info-filter.component';
import { DocumentInfoFilterComponent } from './components/document-info-filter/document-info-filter.component';
import { InterestRateInfoFilterComponent } from './components/interest-rate-info-filter/interest-rate-info-filter.component';
import { PersonalInfoFilterComponent } from './components/personal-info-filter/personal-info-filter.component';
import { AuthorityInfoCustomerContainer } from './containers/authority-info/authority-info.container';
import { CustomerGroupContainer } from './containers/customer-group/customer-group.container';
import { DocumentInfoContainer } from './containers/document-info/document-info.container';
import { InterestRateInfoCustomerContainer } from './containers/interest-rate-info/interest-rate-info.container';
import { InputNumberCustomComponent } from 'src/app/shared/components/input-custom-for-table/input-number-custom/input-number-custom.component';
import { CreateCustomerGroupComponent } from './components/create-customer-group/create-customer-group.component';
import { FormControlComponent } from 'src/app/shared/components/form-control/form-control.component';
import { StoreModule } from '@ngrx/store';
import { customerReducers } from './stores/customer.reducer';
import { CUSTOMER_STATE_NAME } from './stores/customer.selector';
import { EffectsModule } from '@ngrx/effects';
import { CustomersEffects } from './stores/customer.effect';
import { CustomerGroupFilterComponent } from './components/customer-group-filter/customer-group-filter.component';
import { InputProportionDropdownComponent } from 'src/app/shared/components/input-custom-for-table/input-proportion-dropdown/input-proportion-dropdown.component';
import { DropdownAndFileForTableComponent } from 'src/app/shared/components/dropdown-and-files-for-table/dropdown-and-files-for-table.component';
import { SlideTagComponent } from 'src/app/shared/components/slide-tag/slide-tag.component';
import { CheckBoxForTableComponent } from 'src/app/shared/components/check-box-for-table/check-box-for-table.component';
import { CustomerDetailPopupComponent } from './components/customer-detail-popup/customer-detail-popup.component';
import { PersonalInfoDetailComponent } from './components/customer-detail-popup/personal-info-detail/personal-info-detail.component';
import { BankInfoDetailComponent } from './components/customer-detail-popup/bank-info-detail/bank-info-detail.component';
import { AccountInfoDetailComponent } from './components/customer-detail-popup/account-info-detail/account-info-detail.component';
import { AuthorityInfoDetailComponent } from './components/customer-detail-popup/authority-info-detail/authority-info-detail.component';
import { DocumentInfoDetailComponent } from './components/customer-detail-popup/document-info-detail/document-info-detail.component';
import { InputComponent } from 'src/app/shared/components/input/input.component';
import { DropzoneFormComponent } from 'src/app/shared/components/dropzone-form/dropzone-form.component';
import { SectionWrapperLayoutComponent } from './components/customer-detail-popup/section-wrapper-layout/section-wrapper-layout.component';
import { NumberFormatPipe } from 'src/app/shared/pipes/format-number/format-number.pipe';
import { FormatFileSizePipe } from 'src/app/shared/pipes/format-file-size/format-file-size.pipe';
import { PaginatorComponent } from 'src/app/shared/components/paginator/paginator.component';
import { CreateCustomerGroupQuicklyComponent } from './components/create-customer-group-quickly/create-customer-group-quickly.component';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { NgxMaskDirective } from 'src/app/shared/directives/mask/ngx-mask.directive';
import { NoSpecialCharactersDirective } from 'src/app/shared/directives/no-special-characters/no-special-characters.directive';
import { CreatePlaceOrderButton } from 'src/app/shared/components/create-place-order-button/create-place-order-button.component';
import { OptionsCustomerComponent } from './components/options-customer/options-customer.component';

const SHARED = [
  GridComponent,
  ActionBtnComponent,
  DraggableListComponent,
  SearchDropdownCustomComponent,
  CalendarCustomComponent,
  PhoneNumberTableComponent,
  InputProportionComponent,
  InputNumberCustomComponent,
  FormControlComponent,
  InputProportionDropdownComponent,
  DropdownAndFileForTableComponent,
  SlideTagComponent,
  CheckBoxForTableComponent,
  InputComponent,
  DropzoneFormComponent,
  PaginatorComponent,

  // Pipe
  NumberFormatPipe,
  FormatFileSizePipe,
  CreatePlaceOrderButton,
];

const VIEWS = [CustomersView];

const CONTAINERS = [
  PersonalInfoCustomerContainer,
  BankInfoCustomerContainer,
  AccountInfoCustomerContainer,
  InterestRateInfoCustomerContainer,
  AuthorityInfoCustomerContainer,
  DocumentInfoContainer,
  CustomerGroupContainer,
];

const COMPONENTS = [
  PersonalInfoFilterComponent,
  BankInfoFilterComponent,
  AccountInfoFilterComponent,
  InterestRateInfoFilterComponent,
  DocumentInfoFilterComponent,
  AuthorityInfoFilterComponent,
  CreateCustomerGroupComponent,
  CustomerGroupFilterComponent,
  CustomerDetailPopupComponent,
  PersonalInfoDetailComponent,
  BankInfoDetailComponent,
  AccountInfoDetailComponent,
  AuthorityInfoDetailComponent,
  DocumentInfoDetailComponent,
  SectionWrapperLayoutComponent,
  CreateCustomerGroupQuicklyComponent,
  OptionsCustomerComponent,
];

/**
 * Customer Module
 */
@NgModule({
  declarations: [...VIEWS, ...CONTAINERS, ...COMPONENTS],
  imports: [
    ...SHARED,
    CommonModule,
    TranslateModule,
    CustomersRoutingModule,
    NgxMaskDirective,
    FilterComponent,
    NoSpecialCharactersDirective,

    // Material Module
    MatTableModule,
    MatTabsModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    FormsModule,
    MatDividerModule,
    MatDatepickerModule,
    MatMenuModule,
    MatDialogModule,
    OverlayModule,
    MatIconModule,
    MatCheckboxModule,
    MatTooltipModule,
    VirtualScrollListComponent,

    StoreModule.forFeature(CUSTOMER_STATE_NAME, customerReducers),
    EffectsModule.forFeature([CustomersEffects]),
  ],
  providers: [UnsaveChangeGuard],
})
export class CustomersModule {}
