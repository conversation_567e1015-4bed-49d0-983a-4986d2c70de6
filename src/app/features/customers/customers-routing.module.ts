import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CustomersView } from './views/customers.view';
import { PersonalInfoCustomerContainer } from './containers/personal-info/personal-info.container';
import { BankInfoCustomerContainer } from './containers/bank-info/bank-info.container';
import { AccountInfoCustomerContainer } from './containers/account-info/account-info.container';
import { AuthorityInfoCustomerContainer } from './containers/authority-info/authority-info.container';
import { DocumentInfoContainer } from './containers/document-info/document-info.container';
import { CustomerGroupContainer } from './containers/customer-group/customer-group.container';
import { UnsaveChangeGuard } from 'src/app/core/guards/unsaved-changes.guard';

const routes: Routes = [
  {
    path: '',
    component: CustomersView,
    children: [
      {
        path: '',
        redirectTo: 'personal-info',
        pathMatch: 'full',
      },
      {
        path: 'personal-info',
        component: PersonalInfoCustomerContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'bank-info',
        component: BankInfoCustomerContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      // {
      //   path: 'interest-rate-info',
      //   component: InterestRateInfoCustomerContainer,
      //   canDeactivate: [UnsaveChangeGuard],
      // },
      {
        path: 'account-info',
        component: AccountInfoCustomerContainer,
        // canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'authority-info',
        component: AuthorityInfoCustomerContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'document-info',
        component: DocumentInfoContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'group-customer-info',
        component: CustomerGroupContainer,
      },
    ],
  },
];
/**
 * Configures and manages customer routes.
 */
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CustomersRoutingModule {}
