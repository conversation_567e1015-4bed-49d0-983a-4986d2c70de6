.typo-text-table {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.document-info-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-document-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          white-space: nowrap;
          text-wrap: nowrap;
        }
        .text-edit-cls {
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          white-space: nowrap;
          text-wrap: nowrap;
          color: var(--color--brand--500);
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;

      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--cancel {
            color: var(--color--danger--600);
            border: 1px solid var(--color--danger--600);
          }

          #box-id--broker {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          mat-icon[data-mat-icon-name='people-icon'] {
            #Group,
            #Group_2,
            #Group_3 {
              ::ng-deep {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.not-accept-save-cls {
          ::ng-deep {
            #box-id--save {
              opacity: 0.5;
            }
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        .document-info {
          max-width: 100%;
          padding: 0 4px;
          display: inline-flex;
          height: 24px;
          align-items: center;
          vertical-align: middle;
          color: var(--color--accents--cyan-dark);
          border-radius: 16px;
          overflow: hidden;
          color: var(--MacOS-Accents-Cyan---Dark, #5ac8f5);
          text-overflow: ellipsis;
          cursor: pointer;

          img {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            object-fit: contain;
            vertical-align: middle;
          }

          span {
            width: 100%;
            margin-left: 8px;
            vertical-align: middle;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }

        .add-bank-trigger {
          cursor: pointer;
          display: inline-block;
          padding: 2px 4px;
          color: var(--color--text--subdued);
          border-radius: 16px;

          &.edit-mode-cls {
            color: var(--color--accents--cyan-dark);
          }

          img,
          span {
            vertical-align: middle;
          }

          img[alt='add-doc'] {
            height: 18px;
          }

          span {
            margin-left: 4px;
            font-size: 12px;
          }
        }

        .type-account {
          .box-show {
            padding: 4px 0px;
            border-radius: 16px;
            justify-content: center;
            background-color: #32d74b;
            display: inline-flex;
            padding: 0px 12px;
            white-space: nowrap;
            text-wrap: nowrap;

            max-width: 120px;
            margin: 0 auto;

            .input-table-view {
              text-align: center;
            }

            .isEditMode {
              border-color: transparent !important;
              text-align: unset;
            }
          }

          &.individual-cls {
            .box-show {
              background-color: #ffd60a;
            }
          }
        }
      }
    }
  }
}
