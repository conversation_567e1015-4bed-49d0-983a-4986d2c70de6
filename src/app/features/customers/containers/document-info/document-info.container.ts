import { Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnI<PERSON><PERSON>, TemplateRef, ViewChild } from '@angular/core';
import {
  CONVERT_TYPE_ACCOUNT_TO_LABLE,
  CONVERT_TYPE_FILE_DOCMENT_INFO_TO_NAME_CONFIG,
  ETypeAccount,
} from '../../constants/customers';
import { ActionButton, IActionBtn } from 'src/app/shared/components/action-btn/action-btn.component';
import { DestroyService } from 'src/app/core/services';
import { DocumentInfoFilterComponent } from '../../components/document-info-filter/document-info-filter.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { Observable, take, takeUntil } from 'rxjs';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import { CELL_TYPE, IClickOnColumnEvent } from '@shared/models';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { IFilterDocumentInfoParam, IIDentificationFiles, TotalTags } from '../../model/customer';
import { getCustomerDocumentInfo, resetFilterDocumentInfo, setFilterDocumentInfo } from '../../stores/customer.action';
import { Store } from '@ngrx/store';
import {
  selectCustomerDocumentInfo$,
  selectFilterDocumentInfo$,
  selectFilteredDataDocumentInfo$,
  selectSearchValue$,
} from '../../stores/customer.selector';
import { deepClone } from 'src/app/shared/utils/utils';
import { CustomerDetailPopupComponent } from '../../components/customer-detail-popup/customer-detail-popup.component';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import {
  selectAllAccountNumberListByBrokerView$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
/**
 * DocumentInfoContainer
 */
@Component({
  selector: 'app-document-info',
  templateUrl: './document-info.container.html',
  styleUrl: './document-info.container.scss',
})
export class DocumentInfoContainer
  extends BaseTableComponent<any>
  implements OnInit, ComponentCanDeactivate, OnDestroy
{
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('templateInfo', { static: true }) docInfo: TemplateRef<any> | null = null;

  @ViewChild('signatureTemplate', { static: true }) signatureTemplate: TemplateRef<any> | null = null;

  filterOptions!: IFilterDocumentInfoParam;

  configTagColumns = [
    'contractId00',
    'contractId01',
    'contractId02',
    'contractId03',
    'contractId04',
    'contractId05',
    'contractId80',
  ];

  LIST_MG: IListOptions[] = [];
  brokerButton: IActionBtn = {
    label: '',
    icons: 'icon:people-icon',
    name: 'broker',
    isDisplayed: false,
    tag: ActionButton.broker,
  };

  searchValue: string = '';
  totalTags: TotalTags = {
    totalAccount: 0,
  };

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   * @param popoverService PopoverService
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly popoverService: PopoverService
  ) {
    super();
    this.brokerInfo();
    this.actionButtons.unshift(this.brokerButton);
    this.toggleButtonByTags([
      ActionButton.broker,
      // ActionButton.edit,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);
  }
  /**
   *
   */
  ngOnInit(): void {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        if (!user) return;
        this.store.dispatch(getCustomerDocumentInfo({ id: user.brokerCode }));
      });

    this.store
      .select(selectCustomerDocumentInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((documents) => {
        const dataClone = structuredClone(documents);

        this.totalTags = {
          totalAccount: dataClone.length,
        };

        this.data = dataClone.map((d) => {
          this.configTagColumns.forEach((tag) => (d[tag] = null));

          d.contracts.forEach((contract) => {
            const files = this.mapContractFiles(contract.contractFiles);

            d[CONVERT_TYPE_FILE_DOCMENT_INFO_TO_NAME_CONFIG[contract.contractType]] = {
              name: contract.name,
              type: null,
              files,
            };
          });

          d['identificationId'] = !d.identification
            ? null
            : {
                name: d.identification.name,
                type: null,
                files: d.identification.identificationFiles.map((file) => ({
                  name: file.name,
                  type: file.fileType,
                  url: file.url,
                  size: file.size,
                })),
              };

          d['businessCode'] = !d.registration
            ? null
            : {
                name: d.registration.name,
                type: null,
                files: d.registration.registrationFiles.map((file) => ({
                  name: file.name,
                  type: file.fileType,
                  url: file.url,
                  size: file.size,
                })),
              };

          d['signatureCol'] = !d.signature
            ? null
            : {
                name: d.signature.name,
                type: null,
                files: d.signature.signatureFiles.map((file) => ({
                  type: file.fileType,
                  url: file.url,
                })),
              };

          if (d.accountType === ETypeAccount.INDIVIDUAL) {
            d.tagDisable = ['businessCode'];
          }
          return d;
        });

        this.data = dataClone;
        this.initialData = structuredClone(this.data);

        if (this.isSearch) {
          this.data = this.searchedData;
        }
      });

    const template = this.docInfo;
    const signatureTemplateRef = this.signatureTemplate;
    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 146,
        width: 146,
        tag: 'accountNumber',
        isDisplay: true,
        isEdit: false,
        pinned: 'left',
        dragDisabled: true,
        disable: true,
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 290,
        tag: 'customerName',
        isDisplay: true,
        sticky: false,
        isEdit: false,
        resizable: true,
      },
      {
        name: 'Loại tài khoản',
        minWidth: 30,
        width: 120,
        tag: 'accountType',
        isDisplay: true,
        displayValueFn: (value) => CONVERT_TYPE_ACCOUNT_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === ETypeAccount.INDIVIDUAL) {
            return 'type-account individual-cls';
          } else return 'type-account';
        },
        isEdit: false,
        align: 'center',
        resizable: true,
      },
      {
        name: 'Số ĐKKD',
        minWidth: 30,
        width: 170,
        tag: 'registrationNo',
        isDisplay: true,
        isEdit: true,
        url: './assets/icons/file-extention-blue.svg',
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        typeValue: CELL_TYPE.FILE,
        resizable: true,
      },
      {
        name: 'Số CMND / CCCD / HC',
        minWidth: 30,
        width: 190,
        tag: 'identificationId',
        isDisplay: true,
        url: './assets/icons/file-extention-blue.svg',
        typeValue: CELL_TYPE.FILE,
        isEdit: true,
        displayValueFn: (v) => v.name,
        cellTemplate: this.docInfo,
      },
      {
        name: 'HĐ Cơ Sở',
        minWidth: 30,
        width: 200,
        cellTemplate: template,
        tag: 'contractId00',
        isDisplay: true,
        typeValue: CELL_TYPE.FILE,
        resizable: true,
        isEdit: true,
      },
      {
        name: 'HĐ Ký Quỹ',
        minWidth: 30,
        width: 200,
        typeValue: CELL_TYPE.FILE,
        tag: 'contractId01',
        isDisplay: true,
        cellTemplate: template,
        resizable: true,
        isEdit: true,
      },
      {
        name: 'HĐ 3 Bên BIDV',
        minWidth: 30,
        resizable: true,
        width: 200,
        tag: 'contractId02',
        isDisplay: true,
        cellTemplate: template,
        typeValue: CELL_TYPE.FILE,
        isEdit: true,
      },
      {
        name: 'HĐ 3 Bên SHB',
        minWidth: 30,
        width: 200,
        cellTemplate: template,
        tag: 'contractId03',
        isDisplay: true,
        typeValue: CELL_TYPE.FILE,
        resizable: true,
        isEdit: true,
      },
      {
        name: 'HĐ Margin T+',
        minWidth: 30,
        width: 200,
        tag: 'contractId04',
        resizable: true,
        isDisplay: true,
        cellTemplate: template,
        typeValue: CELL_TYPE.FILE,
        isEdit: true,
      },
      {
        name: 'HĐ Trái Phiếu',
        width: 200,
        tag: 'contractId05',
        isDisplay: true,
        cellTemplate: template,
        typeValue: CELL_TYPE.FILE,
        resizable: true,
        isEdit: true,
        minWidth: 30,
      },
      {
        minWidth: 30,
        width: 200,
        tag: 'contractId80',
        isDisplay: true,
        cellTemplate: template,
        typeValue: CELL_TYPE.FILE,
        resizable: true,
        isEdit: true,
        name: 'HĐ Phái Sinh',
      },
      {
        minWidth: 30,
        width: 200,
        tag: 'signatureCol',
        name: 'Chữ ký',
        isDisplay: true,
        cellTemplate: signatureTemplateRef,
        typeValue: CELL_TYPE.FILE,
        resizable: true,
        isEdit: true,
      },
    ];

    this.store
      .select(selectFilteredDataDocumentInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filteredData) => {
        this.data = filteredData.length ? structuredClone(filteredData) : structuredClone(this.initialData);
      });

    this.store
      .select(selectFilterDocumentInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchValue = value ?? '';
        this.searchData();
      });
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterDocumentInfo());
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  brokerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        if (user) {
          this.brokerButton.label = `${user.brokerCode}: ${user.brokerName}`;

          const itemListMg = {
            name: this.brokerButton.label,
            isSelect: true,
          };
          this.LIST_MG.push(itemListMg);
        }
      });
  }

  mapContractFiles(contractFiles: IIDentificationFiles[]) {
    return contractFiles.map((file) => ({
      name: file.name,
      type: file.fileType,
      url: file.url,
      size: file.size,
    }));
  }

  /**
   * searchData
   */
  searchData() {
    let searchDataAuth = [];
    const initialDataClone = structuredClone(this.initialData);
    if (this.searchValue) {
      const searchValue = this.searchValue.toString().toLowerCase();
      if (!this.isFilter) {
        searchDataAuth = initialDataClone.filter((item) => {
          return this.containsSearchValue(item, searchValue);
        });
      } else {
        searchDataAuth = this.filteredData.filter((item) => {
          return this.containsSearchValue(item, searchValue);
        });
      }
      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchDataAuth = this.isFilter ? deepClone(this.filteredData) : deepClone(this.initialData);

      this.isSearch = false; // Set isSearch to false when not searching
    }
    this.searchedData = deepClone(searchDataAuth);
    this.data = deepClone(searchDataAuth);
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    return (
      item.accountNumber?.toString().toLowerCase().includes(searchValue) ??
      item.customerName.toString().toLowerCase().includes(searchValue)
    );
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'broker':
        this.changeViewBroker();
        break;
      case 'filter':
        {
          const ref = this.openFilter(DocumentInfoFilterComponent, {
            width: '450px',
            data: this.filterOptions,
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe((v) => {
              this.applyFilter(v);
            });
        }
        break;
      case 'edit':
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.save, ActionButton.cancel, ActionButton.export]);
  }

  /**
   * ConvertNameFile
   * @param {string} text
   * @returns {string} return
   */
  convertNameFile(text: string) {
    if (!text) return '';
    const textSpilt = text.split('/');
    const finalText = textSpilt[textSpilt.length - 1].split('?');
    return finalText[0];
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any): void {
    const { optionFilter, type } = data;
    const dataClone = deepClone(this.initialData);
    if (type === 'save') {
      const params: IFilterDocumentInfoParam = {
        typeAccount: optionFilter.accountType,
        isFilter: optionFilter.isFilter,
        contract00: optionFilter.contract00,
        contract01: optionFilter.contract01,
        contract02: optionFilter.contract02,
        contract03: optionFilter.contract03,
        contract04: optionFilter.contract04,
        contract05: optionFilter.contract05,
        contract80: optionFilter.contract80,
      };
      this.store.dispatch(setFilterDocumentInfo({ params }));

      const newListFilter = this.isSearch
        ? this.logicFilter(this.searchedData, optionFilter)
        : this.logicFilter(dataClone, optionFilter);
      this.filteredData = newListFilter;
      this.data = newListFilter;
      this.totalTags.totalAccount = this.data.length;
    } else if (type === 'default') {
      this.isFilter = false;
      this.filteredData = [];
      this.totalTags.totalAccount = this.initialData.length;
      this.isSearch
        ? this.store
            .select(selectSearchValue$)
            .pipe(takeUntil(this._destroy))
            .subscribe((value) => {
              this.searchValue = value ?? '';
              this.searchData();
            })
        : (() => {
            this.data = this.initialData;
          })();
      this.store.dispatch(resetFilterDocumentInfo());
    }
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    const newListFilter = data.filter((customer) => {
      return (
        this.isAccountTypeMatch(optionFilter, customer) &&
        this.isContractIdMatch(optionFilter.contract00, customer.contractId00) &&
        this.isContractIdMatch(optionFilter.contract01, customer.contractId01) &&
        this.isContractIdMatch(optionFilter.contract02, customer.contractId02) &&
        this.isContractIdMatch(optionFilter.contract03, customer.contractId03) &&
        this.isContractIdMatch(optionFilter.contract04, customer.contractId04) &&
        this.isContractIdMatch(optionFilter.contract05, customer.contractId05) &&
        this.isContractIdMatch(optionFilter.contract80, customer.contractId80)
      );
    });

    return newListFilter;
  }

  /**
   * IsAccountTypeMatch
   * @param optionFilter
   * @param customer
   */
  isAccountTypeMatch(optionFilter: any, customer: any): boolean {
    return optionFilter.accountType.length ? optionFilter.accountType.includes(customer.accountType) : true;
  }

  /**
   * IsContractIdMatch
   * @param optionFilterContract - 0: null, 1: !null
   * @param contractId
   */
  isContractIdMatch(optionFilterContract: number[], contractId: string | null): boolean {
    return (
      (optionFilterContract.includes(0) && contractId === null) ||
      (optionFilterContract.includes(1) && contractId !== null) ||
      !optionFilterContract.length
    );
  }

  /**
   * openDocumentInfoDetail
   * @param event
   */
  openDocumentInfoDetail(event: IClickOnColumnEvent) {
    if (this.onEditMode) return;

    let { element } = event;
    let identifyInfo: any;
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((personalInfo) => {
        identifyInfo = personalInfo.find((customer) => customer.accountNumber === element.accountNumber);
      });

    element = {
      ...element,
      identifyInfo,
    };

    this.dialogService.open(CustomerDetailPopupComponent, {
      width: '71vw',
      height: '94vh',
      data: {
        element,
        route: 'document',
      },
    });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    if (this.LIST_MG.length < 2) return;
    const elementRef = new ElementRef(document.querySelector(`#box-id--broker`));

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRef as any,
      width: '250px',
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(takeUntil(this._destroy)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect === true);
      this.brokerButton.label = itemSelected?.name ?? '';
    });
  }
}
