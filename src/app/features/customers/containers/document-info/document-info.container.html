<div class="document-info-container">
  <div class="header-document-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-09' | translate}}</div>

      <div class="number-info-cls">
        <!-- Tổng tài k<PERSON>n -->
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{totalTags.totalAccount}} {{'MES-15' | translate}}
        </div>
        <!-- <PERSON><PERSON> lượng chỉnh sửa -->
        <div *ngIf="onEditMode" class="text-edit-cls">{{'MES-156' | translate}}: {{getCountDataEdited()}}</div>
      </div>
    </div>
    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>
  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event);openDocumentInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [editable]="onEditMode"
      [isInValid]="isInValid"
    >
    </sha-grid>
  </div>
</div>

<ng-template #templateInfo let-docInfo="templateInfo" let-tag="tag" let-element="element">
  @if(docInfo?.name || docInfo?.files?.length){
  <div class="document-info typo-body-12">
    <img [src]="'./assets/icons/pdf.svg'" alt="document-logo" />
    @if(docInfo.name){
    <span>{{docInfo.name}}</span>
    }@else{
    <span>-</span>
    }
  </div>

  } @else if (checkTagDisable(tag, element.tagDisable)) {
  <div class="document-info typo-body-12">
    <span>-</span>
  </div>
  } @else {
  <div class="add-bank-trigger" [class.edit-mode-cls]="onEditMode">
    <img *ngIf="!onEditMode" src="assets/icons/add-file-icon.svg" alt="add-doc-gray" />
    <img *ngIf="onEditMode" src="assets/icons/file-extension-blue.svg" alt="add-doc" />
    <span>{{"MES-28" | translate}}</span>
  </div>
  }
</ng-template>

<ng-template #signatureTemplate let-signatureInfo="templateInfo">
  @if(signatureInfo) {
  <div class="document-info typo-body-12">
    <img src="./assets/icons/file-extention-blue.svg" alt="document-logo" />
    <span>{{convertNameFile(signatureInfo.name)}}</span>
  </div>
  } @if(!signatureInfo) {
  <div class="add-bank-trigger" [class.edit-mode-cls]="onEditMode">
    <img *ngIf="!onEditMode" src="assets/icons/add-file-icon.svg" alt="add-doc-gray" />
    <img *ngIf="onEditMode" src="assets/icons/file-extension-blue.svg" alt="add-doc" />
    <span>{{"MES-28" | translate}}</span>
  </div>
  }
</ng-template>
