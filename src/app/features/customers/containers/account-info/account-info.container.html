<div class="account-info-container">
  <div class="header-account-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-35' | translate}}</div>

      <div class="number-info-cls">
        <!-- Tổng tài k<PERSON>n -->
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{totalTags['totalAccount']}} {{'MES-15' | translate}}
        </div>
        <!-- Tổng nhóm -->
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{totalTags['totalGroup']}} {{'MES-567' | translate}}
        </div>

        <div *ngIf="onEditMode" class="text-edit-cls">{{'MES-156' | translate}}: {{getCountDataEdited()}}</div>
      </div>
    </div>
    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>
  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); openAccountInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
      [rowSelectionFormatter]="rowSelectionFormatter"
    >
    </sha-grid>
  </div>
</div>

<!-- <ng-template
  #inputProportionDropdown
  let-templateInfo="templateInfo"
  let-element="element"
  let-tag="tag"
  let-column="column"
>
  <app-input-proportion-dropdown-component
    [unit]="column.componentConfig.unit"
    [element]="element"
    [tag]="tag"
    [isEdit]="onEditMode"
    [value]="templateInfo"
    [column]="column"
    [typeValue]="column.componentConfig.type"
    [typeAccount]="element[tag].group"
    (dateChangeEvent)="changeDateEvent($event)"
    (unFocusElement)="elementSelectFunc($event)"
  ></app-input-proportion-dropdown-component>
</ng-template> -->

<!-- <ng-template #inputProportion let-templateInfo="templateInfo" let-element="element" let-tag="tag" let-column="column">
  <app-input-proportion-component
    [unit]="column.componentConfig.unit"
    [element]="element"
    [tag]="tag"
    [isEdit]="onEditMode"
    [value]="templateInfo"
    [column]="column"
    [typeValue]="column.componentConfig.type"
    [typeAccount]="element[tag].group"
    (dateChangeEvent)="changeDateEvent($event)"
    (unFocusElement)="elementSelectFunc($event)"
  ></app-input-proportion-component>
</ng-template> -->

<ng-template #calendarPicker let-templateInfo="templateInfo" let-element="element" let-tag="tag" let-column="column">
  @if(templateInfo) {
  <div #bankRef class="calendar-template-info-cls">
    <!-- <span (click)="calendarCustom.openCalendar()">{{calendar}}</span> -->
    <app-date-picker-component
      (dateChangeEvent)="changeDateEvent($event)"
      [isEdit]="onEditMode"
      [startDate]="templateInfo"
      [element]="element"
      [tag]="tag"
      [isExpiredDate]="column.componentConfig && column.componentConfig?.isExpiredDate ? true : false"
      [isValidateDate]="column.componentConfig && column.componentConfig?.isValidateDate ? true : false"
      [isValidBirthday]="column.componentConfig && column.componentConfig?.isValidBirthday ? true : false"
      (unFocusElement)="elementSelectFunc($event)"
      #calendarCustom
    ></app-date-picker-component>
  </div>
  } @else {
  <span>-</span>
  }
</ng-template>
