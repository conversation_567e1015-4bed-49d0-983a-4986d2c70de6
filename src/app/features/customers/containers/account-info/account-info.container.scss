.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.account-info-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-account-info {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          white-space: nowrap;
          text-wrap: nowrap;
        }
        .text-edit-cls {
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          text-wrap: nowrap;
          white-space: nowrap;
          color: var(--color--brand--500);
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;

      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--cancel {
            color: var(--color--danger--600);
            border: 1px solid var(--color--danger--600);
          }

          #box-id--broker {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          mat-icon[data-mat-icon-name='people-icon'] {
            #Group,
            #Group_2,
            #Group_3 {
              ::ng-deep {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.not-accept-save-cls {
          ::ng-deep {
            #box-id--save {
              opacity: 0.5;
            }
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        td {
          &:has(app-input-proportion-component) {
            padding: 0;
            vertical-align: top;
            height: 36px;
            app-input-proportion-component {
              display: flex;
              align-items: center;
              height: 100%;
              width: 100%;
              max-width: 208px;
              // .container-box-proportion{
              //   min-width: 204px !important;
              // }
            }
          }
          &:has(app-input-proportion-dropdown-component) {
            app-input-proportion-dropdown-component {
              width: 100%;
            }
            padding: 0;
          }
        }

        .custom-span-level {
          min-width: 120px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          text-transform: uppercase;
          background-color: var(--color--neutral--100);
          text-transform: uppercase;
          border-radius: 16px;

          max-width: 120px;
          margin: 0 auto;

          .input-table-view {
            text-align: center;
            text-transform: uppercase;
          }

          .isEditMode {
            border-color: transparent !important;
            text-align: unset;
          }

          &.platinum-cls {
            background-color: var(--color--accents--orange-dark) !important;
          }

          &.diamond-cls {
            background-color: var(--color--accents--mint-dark) !important;
          }

          &.gold-cls {
            background-color: var(--color--accents--yellow-dark) !important;
          }

          &.change-value-mode {
            border-radius: 16px;
            border: 1px solid var(--color--accents--yellow);
            background-color: unset !important;
          }
        }

        .transaction-fee-cls {
          // text-align: center !important;
          .container-box-proportion {
            padding: 0px 14px 0px 8px;
            background-color: var(--color--accents--yellow-dark);
            border-radius: 16px;
            text-wrap: nowrap;
            white-space: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 132px;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            width: 100%;
            .unit-cls {
              overflow: hidden;
              text-overflow: ellipsis;
              position: relative;
            }
            .input-cls {
              overflow: visible;
            }
          }
        }

        th#baseTransactionFee,
        th#derivativesTransactionFee {
          input {
            // text-align: center !important;
            text-align: start;
          }
        }
      }
    }
  }
}
