import { Component, ElementRef, <PERSON><PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { CONVERT_CUSTOMER_LEVEL_TO_LABLE, ECustomerGroup, ECustomerLevel } from '../../constants/customers';
import { DestroyService } from 'src/app/core/services';
import { AccountInfoFilterComponent } from '../../components/account-info-filter/account-info-filter.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { CELL_TYPE, GridRowSelectionFormatter, IClickOnColumnEvent } from '@shared/models';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import { Observable, take, takeUntil } from 'rxjs';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { IFilterAccountInfoParam, TotalTags } from '../../model/customer';
import {
  selectCustomerAccountInfo$,
  selectFilterAccountInfo$,
  selectFilteredDataAccountInfo$,
  selectSearchValue$,
} from '../../stores/customer.selector';
import { Store } from '@ngrx/store';
import { getCustomerAccountInfo, resetFilterAccountInfo, setFilterAccountInfo } from '../../stores/customer.action';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { deepClone } from 'src/app/shared/utils/utils';
import { CustomerDetailPopupComponent } from '../../components/customer-detail-popup/customer-detail-popup.component';
import { CreateCustomerGroupComponent } from '../../components/create-customer-group/create-customer-group.component';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { getCurrentBrokerView } from 'src/app/stores/shared/shared.actions';

/**
 * Thông tin tài khoản
 */
@Component({
  selector: 'app-account-info-container',
  templateUrl: './account-info.container.html',
  styleUrl: './account-info.container.scss',
})
export class AccountInfoCustomerContainer
  extends BaseTableComponent<any>
  implements OnInit, ComponentCanDeactivate, OnDestroy
{
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('inputProportion', { static: true }) inputProportion: TemplateRef<any> | null = null;

  @ViewChild('inputProportionDropdown', { static: true }) inputProportionDropdown: TemplateRef<any> | null = null;

  @ViewChild('calendarPicker', { static: true }) calendarPicker: TemplateRef<any> | null = null;

  LIST_MG_OPTIONS = [
    {
      id: '1',
      name: 'MG-015: Nguyễn Tuấn Dương',
      parentId: '1',
      config: 'level2',
    },
    {
      id: '2',
      name: 'MG-22: Cao Tuấn Nghĩa',
      parentId: '1',
      config: 'level2',
    },
    {
      id: '3',
      name: 'MG-05: Mai Tiến Đạt',
      parentId: '4',
      config: 'level2',
    },
    {
      id: '4',
      name: 'MG-020: Vũ Minh Chiến',
      parentId: '2',
      config: 'level2',
    },
    {
      id: '5',
      name: 'MG-016: Nguyễn Hoàng Cảnh',
      parentId: '4',
      config: 'level2',
    },
    {
      id: '6',
      name: 'MG-019: Đinh Sỹ Dũng',
      parentId: '3',
      config: 'level2',
    },
    {
      id: '7',
      name: 'MG-021: Lê Ngọc Hà',
      parentId: '5',
      config: 'level2',
    },
    {
      id: '8',
      name: 'MG-018: Phạm Văn Tây',
      parentId: '3',
      config: 'level2',
    },
  ];

  LIST_MG_ROOM_OPTIONS = [
    {
      id: '1',
      name: 'Phòng MG 05',
      children: [...structuredClone(this.LIST_MG_OPTIONS)],
      config: 'level1',
    },
    {
      id: '2',
      name: 'Phòng MG 08',
      children: structuredClone(this.LIST_MG_OPTIONS),
      config: 'level1',
    },
    {
      id: '3',
      name: 'Phòng MG 07',
      children: structuredClone(this.LIST_MG_OPTIONS),
      config: 'level1',
    },
    {
      id: '4',
      name: 'Phòng MG 06',
      children: structuredClone(this.LIST_MG_OPTIONS),
      config: 'level1',
    },
    {
      id: '5',
      name: 'Phòng MG 09',
      children: structuredClone(this.LIST_MG_OPTIONS),
      config: 'level1',
    },
  ];

  filterOptions!: IFilterAccountInfoParam;

  rowSelectionFormatter: GridRowSelectionFormatter = {
    disabled: (data, index) => data.accountClosingDate,
    hideCheckbox: (data, index) => data.accountClosingDate,
  };

  searchValue: string = '';
  totalTags: TotalTags = {
    totalAccount: 0,
    totalGroup: 0,
  };

  LIST_GROUP_CUSTOMER = [
    {
      name: ECustomerGroup.SURFING,
      value: ECustomerGroup.SURFING,
    },
    {
      name: ECustomerGroup.MEDIUMTERM,
      value: ECustomerGroup.MEDIUMTERM,
    },
    {
      name: ECustomerGroup.LONGTERM,
      value: ECustomerGroup.LONGTERM,
    },
    {
      name: ECustomerGroup.NOGROUP,
      value: ECustomerGroup.NOGROUP,
      classCustom: 'option-color-brand-600-cls',
    },
  ];

  LIST_MG: IListOptions[] = [];

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   * @param popoverService popoverService
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly popoverService: PopoverService
  ) {
    super();
    this.brokerInfoAccount();
    this.toggleButtonByTags([
      ActionButton.broker,
      // ActionButton.edit,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);
  }

  /**
   * OnInit
   */
  ngOnInit(): void {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        if (!user) return;
        this.store.dispatch(getCustomerAccountInfo({ id: user.brokerCode }));
      });

    this.store
      .select(selectCustomerAccountInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((accountInfoList) => {
        if (!accountInfoList.length) return;
        // FIX ME: Wait change value type of customerGroup
        const uniqueGroups = this.getUniqueGroup(accountInfoList);
        this.totalTags = {
          totalAccount: accountInfoList.length,
          totalGroup: uniqueGroups.length,
        };

        this.data = structuredClone(accountInfoList).map((d) => ({
          ...d,
          id: d.accountNumber,
          brokerInfo: d.brokerCode + ': ' + d.brokerName,
          brokerRoom: d.saleGroupName,
          isDisable: d.accountClosingDate,
          children: d.children.map((c) => {
            return {
              ...c,
              id: `${c.accountNumber} - ${c.subAccount}`,
              accountNumber: `${c.accountNumber} - ${c.subAccount}`,
              customerName: null,
              brokerRoom: c.saleGroupName,
              brokerInfo: c.brokerCode + ': ' + c.brokerName,
            };
          }),
        }));

        this.initialData = structuredClone(this.data);
        if (this.isSearch) {
          this.data = this.searchedData;
        }
      });

    const calendarTemplate = this.calendarPicker;
    const dropdownTemplate = this.inputProportionDropdown;
    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 146,
        width: 146,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        isEdit: false,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 290,
        tag: 'customerName',
        isDisplay: true,
        isEdit: false,
        resizable: true,
      },
      {
        name: 'Hạng khách hàng',
        minWidth: 30,
        width: 140,
        tag: 'customerLevel',
        isDisplay: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
        dynamicClass: (value) => {
          if (!value) return '';
          if (value === ECustomerLevel.PLATINUM) {
            return 'custom-span-level platinum-cls';
          } else if (value === ECustomerLevel.DIAMOND) {
            return 'custom-span-level diamond-cls';
          } else if (value === ECustomerLevel.GOLD) {
            return 'custom-span-level gold-cls';
          } else return 'custom-span-level';
        },
        align: 'center',
        typeValue: CELL_TYPE.DROPDOWN,
        isEdit: true,
        componentConfig: {
          multiple: false,
          isSearch: false,
          searchKey: 'value',
          displayOptionFn: (v: any) => v.name,
          options: [
            {
              name: CONVERT_CUSTOMER_LEVEL_TO_LABLE[ECustomerLevel.PLATINUM],
              value: ECustomerLevel.PLATINUM,
            },
            {
              name: CONVERT_CUSTOMER_LEVEL_TO_LABLE[ECustomerLevel.DIAMOND],
              value: ECustomerLevel.DIAMOND,
            },
            {
              name: CONVERT_CUSTOMER_LEVEL_TO_LABLE[ECustomerLevel.GOLD],
              value: ECustomerLevel.GOLD,
            },
            {
              name: CONVERT_CUSTOMER_LEVEL_TO_LABLE[ECustomerLevel.NORMAL],
              value: ECustomerLevel.NORMAL,
            },
          ],
        },
        resizable: true,
      },
      {
        name: 'Nhóm khách hàng',
        minWidth: 30,
        width: 180,
        tag: 'customerGroup',
        isDisplay: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        typeValue: CELL_TYPE.DROPDOWN,
        isEdit: true,
        componentConfig: {
          multiple: false,
          isSearch: false,
          searchKey: 'value',
          displayOptionFn: (v: any) => v.name,
          addItemButtonEvent: () => this.createGroupCustomer(),
          isShowBtn: true,
          options: this.LIST_GROUP_CUSTOMER,
        },
        resizable: true,
      },
      {
        name: 'Phòng MG quản lý',
        minWidth: 30,
        width: 200,
        tag: 'brokerRoom',
        isDisplay: true,
        // displayValueFn: (v) => {
        //   if (!v) return '-';
        //   return v.name;
        // },
        // typeValue: CELL_TYPE.DROPDOWN_MULTIPLE_LEVEL,
        isEdit: true,
        tagRelated: ['brokerInfo'],
        // componentConfig: {
        //   displayOptionFn: (v: any) => v.name,
        //   searchKey: 'name',
        //   key: 'children',
        //   isUpdateList: true,
        //   isShowBtn: true,
        //   isShowSearch: true,
        //   options: structuredClone(this.LIST_MG_ROOM_OPTIONS),
        //   initialOptions: structuredClone(this.LIST_MG_ROOM_OPTIONS),
        //   configChildren: {
        //     level1: {
        //       isShowSearch: false,
        //       isShowBtn: false,
        //       searchKey: 'name',
        //       placeholder: 'Tên Phòng',
        //       displayOptionFn: (v: any) => v.name,
        //     },
        //   },
        //   filterKey: 'parentId',
        // },
        resizable: true,
      },
      {
        name: 'Mã MG quản lý',
        minWidth: 30,
        width: 200,
        tag: 'brokerInfo',
        isEdit: true,
        isDisplay: true,
        typeValue: CELL_TYPE.DROPDOWN,
        tagRelated: ['brokerRoom'],
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        // componentConfig: {
        //   displayOptionFn: (v: any) => {
        //     return v.name;
        //   },
        //   multiple: false,
        //   searchKey: 'name',
        //   isSearch: true,
        //   placeholder: 'Mã, tên MG',
        //   isShowBotBtn: true,
        //   options: this.LIST_MG_OPTIONS,
        //   parentList: this.LIST_MG_ROOM_OPTIONS,
        // },
        resizable: true,
      },
      {
        name: 'Phí GD Cơ Sở',
        minWidth: 30,
        width: 220,
        tag: 'baseTransactionFee',
        isEdit: true,
        isDisplay: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          if (value.value || value.value === 0) {
            const percentageValue = value.value * 100;
            const formattedValue = customNumberFormat(percentageValue, 'decimal', 'en-US', 2);
            return `${formattedValue}`;
          } else {
            return '-';
          }
        },
        cellTemplate: dropdownTemplate,
        // componentConfig: {
        //   unit: '%' + '/ giao dịch',
        //   type: ETypeValueInput.PERCENT,
        // },
        panelClass: 'transaction-fee-cls',
        resizable: true,
        align: 'center',
      },
      {
        name: 'Phí GD Phái Sinh',
        minWidth: 30,
        width: 200,
        tag: 'derivativesTransactionFee',
        isEdit: true,
        isDisplay: true,
        align: 'center',
        displayValueFn: (value) => {
          if (!value) return '-';
          if (value.value || value.value === 0) {
            const formattedValue = value.value.toLocaleString('en-US');
            const formattedValueWithDot = formattedValue.replace(/,/g, '.');
            return formattedValueWithDot;
          } else return '-';
        },
        cellTemplate: dropdownTemplate,
        // componentConfig: {
        //   unit: '/ hợp đồng',
        //   type: ETypeValueInput.NUMBER,
        // },
        panelClass: 'transaction-fee-cls',
        resizable: true,
      },
      {
        name: 'Phí GD Trái Phiếu',
        minWidth: 30,
        width: 220,
        tag: 'bondTransactionFee',
        isEdit: true,
        isDisplay: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          if (value.value || value.value === 0) {
            const percentageValue = value.value * 100;
            const formattedValue = customNumberFormat(percentageValue, 'decimal', 'en-US', 2);
            return `${formattedValue}`;
          } else {
            return '-';
          }
        },
        cellTemplate: dropdownTemplate,
        // componentConfig: {
        //   unit: '%' + '/ giao dịch',
        //   type: ETypeValueInput.PERCENT,
        // },
        panelClass: 'transaction-fee-cls',
        resizable: true,
        align: 'center',
      },
      {
        name: 'Ngày mở tài khoản',
        minWidth: 30,
        width: 150,
        tag: 'accountOpeningDate',
        isDisplay: true,
        isEdit: true,
        typeValue: CELL_TYPE.CALENDAR,
        resizable: true,
        cellTemplate: calendarTemplate,
      },
      {
        name: 'Ngày cuối giao dịch',
        minWidth: 30,
        width: 160,
        tag: 'lastTransactionDate',
        isDisplay: true,
        isEdit: true,
        typeValue: CELL_TYPE.CALENDAR,
        resizable: true,
        cellTemplate: calendarTemplate,
      },
      {
        name: 'Ngày đóng tài khoản',
        minWidth: 30,
        width: 160,
        tag: 'accountClosingDate',
        isDisplay: true,
        isEdit: true,
        typeValue: CELL_TYPE.CALENDAR,
        resizable: true,
        cellTemplate: calendarTemplate,
      },
    ];

    this.store
      .select(selectFilteredDataAccountInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filteredData) => {
        this.data = filteredData.length ? deepClone(filteredData) : deepClone(this.initialData);
      });

    this.store
      .select(selectFilterAccountInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchValue = value ?? '';
        this.searchData();
      });
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterAccountInfo());

    this._destroy.next();
    this._destroy.complete();
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * Thông tin của broker
   */
  brokerInfoAccount() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBrokerAccount) => {
        if (!currentBrokerAccount) return;
        this.findButtonByTags(
          ActionButton.broker
        ).label = `${currentBrokerAccount.brokerCode}: ${currentBrokerAccount.brokerName}`;

        this.store
          .select(selectInfoUserLogin$)
          .pipe(takeUntil(this._destroy))
          .subscribe((userListAccount) => {
            if (!userListAccount) return;
            this.LIST_MG = userListAccount.map((user) => {
              return {
                brokerCode: `${user.brokerCode}`,
                name: `${user.brokerCode}: ${user.brokerName}`,
                isSelect: user.brokerCode === currentBrokerAccount.brokerCode,
              };
            });
          });
      });
  }

  /**
   * searchData
   */
  searchData() {
    let searchDataAccount = [];
    if (this.searchValue) {
      const searchValue = this.searchValue.toString().toLowerCase();
      if (!this.isFilter) {
        searchDataAccount = this.initialData.filter((item) => {
          return this.containsSearchValue(item, searchValue);
        });
      } else {
        searchDataAccount = this.filteredData.filter((item) => {
          return this.containsSearchValue(item, searchValue);
        });
      }
      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchDataAccount = this.isFilter ? deepClone(this.filteredData) : deepClone(this.initialData);
      this.isSearch = false; // Set isSearch to false when not searching
    }
    this.searchedData = deepClone(searchDataAccount);
    this.data = deepClone(searchDataAccount);
  }
  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    return (
      item.accountNumber.toString().toLowerCase().includes(searchValue) ??
      item.customerName.toString().toLowerCase().includes(searchValue)
    );
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'broker':
        this.changeViewBroker();
        break;
      case 'filter':
        {
          const ref = this.openFilter(AccountInfoFilterComponent, {
            width: '400px',
            data: this.filterOptions,
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe((v) => {
              if (!v) return;
              this.applyFilter(v);
            });
        }
        break;
      case 'edit':
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * ApplyFilter
   * @param data data
   */
  applyFilter(data: any): void {
    const { optionFilter, type } = data;
    const dataClone = deepClone(this.initialData);

    if (type === 'save') {
      const params: IFilterAccountInfoParam = {
        typeAccount: optionFilter.accountType,
        levelAccount: optionFilter.levelAccount,
        isFilter: optionFilter.isFilter,
        accountGroups: optionFilter.accountGroups,
        rooms: optionFilter.rooms,
      };

      this.store.dispatch(setFilterAccountInfo({ params }));
      const newListFilter = dataClone.filter((customer, index) => {
        const isAccountTypeMatch = optionFilter.accountType.length
          ? optionFilter.accountType.includes(customer.accountType)
          : true;

        const isLevelAccountMatch = optionFilter.levelAccount.length
          ? optionFilter.levelAccount?.includes(customer?.customerLevel)
          : true;

        // Convert customerGroup to array to compare
        const arrayCustomerGroup = customer.customerGroup
          ? customer.customerGroup.split(', ').map((group: any) => group.trim())
          : [];
        const isAccountGroupsMatch = optionFilter.accountGroups.length
          ? optionFilter.accountGroups.every((item: any) => arrayCustomerGroup.includes(item))
          : true;

        const isRoomsMatch = optionFilter.rooms.length
          ? customer.brokerRoom && optionFilter.rooms.includes(customer.brokerRoom)
          : true;
        return isAccountTypeMatch && isLevelAccountMatch && isAccountGroupsMatch && isRoomsMatch;
      });
      this.filteredData = newListFilter;
      this.data = deepClone(this.filteredData);
      const uniqueGroups = this.getUniqueGroup(this.data);

      this.totalTags = {
        totalAccount: this.data.length,
        totalGroup: uniqueGroups.length,
      };
    } else if (type === 'default') {
      this.isFilter = false;
      this.filteredData = [];
      const uniqueGroups = this.getUniqueGroup(this.initialData);
      this.totalTags = {
        totalAccount: this.initialData.length,
        totalGroup: uniqueGroups.length,
      };
      this.isSearch
        ? this.store
            .select(selectSearchValue$)
            .pipe(takeUntil(this._destroy))
            .subscribe((value) => {
              this.searchValue = value ?? '';
              this.searchData();
            })
        : (() => {
            this.data = this.initialData;
          })();
      this.store.dispatch(resetFilterAccountInfo());
    }
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([
      ActionButton.broker,
      ActionButton.edit,
      ActionButton.save,
      ActionButton.cancel,
      ActionButton.export,
    ]);
  }

  /**
   * getUniqueGroup
   */
  getUniqueGroup(data: any) {
    const uniqueGroups = [
      ...new Set(
        this.initialData
          .filter((item) => item.customerGroup) // Filter out items without a customerGroup
          .flatMap((item) => item.customerGroup.split(', ').map((group: any) => group.trim()))
      ),
    ];
    return uniqueGroups;
  }

  /**
   * openAccoutInfoDetail
   * @param event
   */
  openAccountInfoDetail(event: IClickOnColumnEvent) {
    const { element } = event;
    if (this.onEditMode || !element.children) return;

    this.dialogService.open(CustomerDetailPopupComponent, {
      width: '71vw',
      height: '94vh',
      data: {
        element,
        route: 'account',
      },
    });
  }

  /**
   * CreateGroupCustomer
   */
  createGroupCustomer() {
    const ref = this.dialogService.openRightDialog(CreateCustomerGroupComponent, {
      width: '450px',
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v) => {
        if (!v) return;
        const colum = this.columnConfigs.find((t) => t.tag === 'customerGroup');
        const newOption = {
          name: v.customerGroupName,
          value: v.customerGroupName,
        };
        this.LIST_GROUP_CUSTOMER.splice(this.LIST_GROUP_CUSTOMER.length - 1, 0, newOption);
        if (colum) colum.componentConfig.options = this.LIST_GROUP_CUSTOMER;
      });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    const elementRef = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidth = elementRef.nativeElement as HTMLElement;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRef as any,
      width: elementWidth.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBroker = userList.find((user) => user.brokerCode === itemSelected['brokerCode']);
          if (!currentBroker) return;
          this.store.dispatch(getCurrentBrokerView({ data: currentBroker }));
        });
    });
  }
}
