import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { DestroyService } from 'src/app/core/services';
import {
  CONVERT_CUSTOMER_LEVEL_TO_LABLE,
  CUSTOMER_LEVEL_MAPPING,
  ECustomerLevel,
  IInterestRateInfoData,
} from '../../constants/customers';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { InterestRateInfoFilterComponent } from '../../components/interest-rate-info-filter/interest-rate-info-filter.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { CELL_TYPE } from '@shared/models';
import { ETypeValueInput } from 'src/app/shared/components/input-custom-for-table/input-proportion/input-proportion';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import { Observable, take, takeUntil } from 'rxjs';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import {
  selectFilterInterestRateInfo$,
  selectFilteredDataInterestRateInfo$,
  selectSearchValue$,
} from '../../stores/customer.selector';
import { Store } from '@ngrx/store';
import { IFilterInterestRateInfoParam } from '../../model/customer';
import {
  resetFilterInterestRateInfo,
  setFilterInterestRateInfo,
  setFilteredDataInterestRateInfo,
} from '../../stores/customer.action';

/**
 * PersonalInfoCustomerContainer
 */
@Component({
  selector: 'app-interest-rate-info-container',
  templateUrl: './interest-rate-info.container.html',
  styleUrls: ['./interest-rate-info.container.scss'],
})
export class InterestRateInfoCustomerContainer
  extends BaseTableComponent<any>
  implements OnInit, ComponentCanDeactivate
{
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('inputProportion', { static: true }) inputProportion: TemplateRef<any> | null = null;

  @ViewChild('inputNumber', { static: true }) inputNumber: TemplateRef<any> | null = null;

  fakeData: IInterestRateInfoData[] = [
    {
      customerName: 'Phạm Thị Thu Trang',
      accountType: 0,
      accountNumber: '069C-125485',
      customerLevel: 'Platinum',
      interests: [
        {
          interestRate: 0.125,
          limit: *********,
        },
        {
          interestRate: 0.1356,
          limit: **********,
        },
        {
          interestRate: 0.0958,
          limit: *********,
        },
        {
          interestRate: 0.0825,
          limit: *********,
        },
      ],
    },
    {
      customerName: 'Đặng Hoàng An Nhiên',
      accountType: 0,
      accountNumber: '069C-586547',
      customerLevel: 'Diamond',
      interests: [
        {
          interestRate: 0.065,
          limit: 1*********,
        },
        {
          interestRate: 0.1487,
          limit: *********,
        },
        {
          interestRate: 0.2054,
          limit: **********,
        },
        {
          interestRate: 0.325,
          limit: *********,
        },
      ],
    },
    {
      customerName: 'Ngô Thị Hằng',
      accountType: 0,
      accountNumber: '069C-400190',
      customerLevel: 'Gold',
      interests: [
        {
          interestRate: 0.0825,
          limit: *********,
        },
        {
          interestRate: 0.0958,
          limit: *********,
        },
        {
          interestRate: 0.22,
          limit: **********,
        },
        {
          interestRate: 0.125,
          limit: **********,
        },
      ],
    },
    {
      customerName: 'Phạm Tiến Nam Phương',
      accountType: 0,
      accountNumber: '069C-918882',
      customerLevel: 'Normal',
      interests: [
        {
          interestRate: 0.325,
          limit: *********,
        },
        {
          interestRate: 0.2054,
          limit: **********,
        },
        {
          interestRate: 0.2014,
          limit: ***********,
        },
        {
          interestRate: 0.1475,
          limit: *********,
        },
      ],
    },
    {
      customerName: 'Bùi Thị Hạnh',
      accountType: 0,
      accountNumber: '069C-883962',
      customerLevel: 'Platinum',
      interests: [
        {
          interestRate: 0.125,
          limit: **********,
        },
        {
          interestRate: 0.22,
          limit: **********,
        },
        {
          interestRate: 0.125,
          limit: *********,
        },
        {
          interestRate: 0.1356,
          limit: **********,
        },
      ],
    },
    {
      customerName: 'Trần Văn Hậu',
      accountType: 0,
      accountNumber: '069C-891135',
      customerLevel: 'Gold',
      interests: [
        {
          interestRate: 0.1475,
          limit: *********,
        },
        {
          interestRate: 0.2014,
          limit: ***********,
        },
        {
          interestRate: 0.065,
          limit: 1*********,
        },
        {
          interestRate: 0.1487,
          limit: *********,
        },
      ],
    },
    {
      customerName: 'Công ty TNHH Mica Group',
      accountType: 1,
      accountNumber: '069C-316087',
      customerLevel: 'Diamond',
      interests: [
        {
          interestRate: 0.1356,
          limit: **********,
        },
        {
          interestRate: 0.125,
          limit: *********,
        },
        {
          interestRate: 0.0825,
          limit: *********,
        },
        {
          interestRate: 0.2014,
          limit: ***********,
        },
      ],
    },
    {
      customerName: 'Công ty cổ phần địa ốc Ngọc Minh Huy',
      accountType: 1,
      accountNumber: '069C-251114',
      customerLevel: 'Gold',
      interests: [
        {
          interestRate: 0.1487,
          limit: *********,
        },
        {
          interestRate: 0.065,
          limit: 1*********,
        },
        {
          interestRate: 0.325,
          limit: *********,
        },
        {
          interestRate: 0.125,
          limit: *********,
        },
      ],
    },
    {
      customerName: 'Công ty cổ phần Money Max',
      accountType: 1,
      accountNumber: '069C-388482',
      customerLevel: 'Normal',
      interests: [
        {
          interestRate: 0.0958,
          limit: *********,
        },
        {
          interestRate: 0.0825,
          limit: *********,
        },
        {
          interestRate: 0.125,
          limit: **********,
        },
        {
          interestRate: 0.065,
          limit: 1*********,
        },
      ],
    },
    {
      customerName: 'Công ty TNHH Tigon 68',
      accountType: 1,
      accountNumber: '069C-637085',
      customerLevel: 'Normal',
      interests: [
        {
          interestRate: 0.2054,
          limit: **********,
        },
        {
          interestRate: 0.325,
          limit: *********,
        },
        {
          interestRate: 0.1475,
          limit: *********,
        },
        {
          interestRate: 0.0825,
          limit: *********,
        },
      ],
    },
    {
      customerName: 'Công ty TNHH du lịch Cá Voi Xanh',
      accountType: 1,
      accountNumber: '069C-862656',
      customerLevel: 'Normal',
      interests: [
        {
          interestRate: 0.22,
          limit: **********,
        },
        {
          interestRate: 0.125,
          limit: **********,
        },
        {
          interestRate: 0.1356,
          limit: **********,
        },
        {
          interestRate: 0.325,
          limit: *********,
        },
      ],
    },
    {
      customerName: 'Công ty TNHH xây dựng và đầu tư Phú Khang',
      accountType: 1,
      accountNumber: '069C-252138',
      customerLevel: 'Diamond',
      interests: [
        {
          interestRate: 0.2014,
          limit: ***********,
        },
        {
          interestRate: 0.1475,
          limit: *********,
        },
        {
          interestRate: 0.1487,
          limit: *********,
        },
        {
          interestRate: 0.125,
          limit: **********,
        },
      ],
    },
  ];

  filterOptions!: IFilterInterestRateInfoParam;

  searchValue: string = '';

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(private readonly _destroy: DestroyService, private readonly store: Store) {
    super();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.export, ActionButton.display, ActionButton.filter]);
  }
  /**
   * NgOnInit
   */
  ngOnInit(): void {
    const dataClone = [...this.fakeData];

    this.data = dataClone.map((d) => {
      d.interests.forEach((i, index) => {
        d[`interestRate${index + 1}`] = i.interestRate;
        d[`limit${index + 1}`] = i.limit;
      });
      return d;
    });
    this.initialData = structuredClone(this.data);
    this.fakeData = dataClone;

    const template = this.inputProportion;
    const numberTemplate = this.inputNumber;

    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 146,
        width: 146,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        isEdit: false,
        disable: true,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 290,
        tag: 'customerName',
        isDisplay: true,
        isEdit: false,
        resizable: true,
      },
      {
        name: 'Hạng khách hàng',
        minWidth: 140,
        width: 140,
        tag: 'customerLevel',
        isDisplay: true,
        displayValueFn: (value) => CONVERT_CUSTOMER_LEVEL_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === ECustomerLevel.PLATINUM) {
            return 'custom-span-level platinum-cls';
          } else if (value === ECustomerLevel.DIAMOND) {
            return 'custom-span-level diamond-cls';
          } else if (value === ECustomerLevel.GOLD) {
            return 'custom-span-level gold-cls';
          } else return 'custom-span-level';
        },
        typeValue: CELL_TYPE.DROPDOWN,
        isEdit: true,
        align: 'center',
        componentConfig: {
          multiple: false,
          isSearch: false,
          searchKey: 'value',
          displayOptionFn: (v: any) => v.name,
          options: [
            {
              name: CONVERT_CUSTOMER_LEVEL_TO_LABLE[ECustomerLevel.PLATINUM],
              value: ECustomerLevel.PLATINUM,
            },
            {
              name: CONVERT_CUSTOMER_LEVEL_TO_LABLE[ECustomerLevel.DIAMOND],
              value: ECustomerLevel.DIAMOND,
            },
            {
              name: CONVERT_CUSTOMER_LEVEL_TO_LABLE[ECustomerLevel.GOLD],
              value: ECustomerLevel.GOLD,
            },
            {
              name: CONVERT_CUSTOMER_LEVEL_TO_LABLE[ECustomerLevel.NORMAL],
              value: ECustomerLevel.NORMAL,
            },
          ],
        },
        resizable: true,
      },
      {
        name: 'Lãi suất 01',
        minWidth: 30,
        width: 150,
        tag: 'interestRate1',
        isDisplay: true,
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          const formattedValue = (v * 100).toFixed(2);
          const finalValue = formattedValue;

          return finalValue;
        },
        isEdit: true,
        align: 'center',
        cellTemplate: template,
        componentConfig: {
          unit: '% ' + '/ năm',
          type: ETypeValueInput.PERCENT,
        },
        resizable: true,
      },
      {
        name: 'Hạn Mức 01',
        minWidth: 30,
        width: 155,
        tag: 'limit1',
        isDisplay: true,
        typeValue: CELL_TYPE.TEXT,
        isText: true,
        isEdit: true,
        align: 'end',
        cellTemplate: numberTemplate,
        resizable: true,
      },
      {
        name: 'Lãi suất 02',
        minWidth: 30,
        width: 170,
        tag: 'interestRate2',
        isDisplay: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          const formattedValue = (v * 100).toFixed(2);
          const finalValue = formattedValue;

          return finalValue;
        },
        isEdit: true,
        cellTemplate: template,
        componentConfig: {
          unit: '% ' + '/ năm',
          type: ETypeValueInput.PERCENT,
        },
        resizable: true,
      },
      {
        name: 'Hạn Mức 02',
        minWidth: 30,
        width: 155,
        tag: 'limit2',
        isDisplay: true,
        displayValueFn: (v) => {
          return customNumberFormat(v);
        },
        typeValue: CELL_TYPE.TEXT,
        isText: true,
        isEdit: true,
        cellTemplate: numberTemplate,
        align: 'end',
        resizable: true,
      },
      {
        name: 'Lãi suất 03',
        minWidth: 30,
        width: 170,
        tag: 'interestRate3',
        align: 'center',
        isDisplay: true,
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          const formattedValue = (v * 100).toFixed(2);
          const finalValue = formattedValue;

          return finalValue;
        },
        isEdit: true,
        cellTemplate: template,
        componentConfig: {
          unit: '% ' + '/ năm',
          type: ETypeValueInput.PERCENT,
        },
        resizable: true,
      },
      {
        name: 'Hạn Mức 03',
        minWidth: 30,
        width: 155,
        tag: 'limit3',
        isDisplay: true,
        displayValueFn: (v) => {
          return customNumberFormat(v);
        },
        typeValue: CELL_TYPE.TEXT,
        isText: true,
        isEdit: true,
        cellTemplate: numberTemplate,
        align: 'end',
        resizable: true,
      },
      {
        name: 'Lãi suất 04',
        minWidth: 30,
        width: 170,
        tag: 'interestRate4',
        align: 'center',
        isDisplay: true,
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          const formattedValue = (v * 100).toFixed(2);
          const finalValue = formattedValue;

          return finalValue;
        },
        isEdit: true,
        cellTemplate: template,
        componentConfig: {
          unit: '% ' + '/ năm',
          type: ETypeValueInput.PERCENT,
        },
        resizable: true,
      },
      {
        name: 'Hạn Mức 04',
        minWidth: 30,
        width: 155,
        tag: 'limit4',
        isDisplay: true,
        displayValueFn: (v) => {
          return customNumberFormat(v);
        },
        typeValue: CELL_TYPE.TEXT,
        isText: true,
        isEdit: true,
        cellTemplate: numberTemplate,
        align: 'end',
        resizable: true,
      },
    ];

    this.store
      .select(selectFilteredDataInterestRateInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filteredData) => {
        this.data = filteredData.length ? filteredData : this.initialData;
      });

    this.store
      .select(selectFilterInterestRateInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchValue = value ?? '';
        this.searchData();
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * searchData
   */
  searchData() {
    let searchData = [];
    if (this.searchValue) {
      const searchValue = this.searchValue.toString().toLowerCase();
      searchData = this.initialData.filter((item) => {
        return this.containsSearchValue(item, searchValue);
      });
      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchData = [...this.initialData];
      this.isSearch = false; // Set isSearch to false when not searching
    }

    this.data = [...searchData];
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    return (
      item.accountNumber.toString().toLowerCase().includes(searchValue) ??
      item.customerName.toString().toLowerCase().includes(searchValue)
    );
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'filter':
        {
          const ref = this.openFilter(InterestRateInfoFilterComponent, {
            width: '450px',
            data: this.filterOptions,
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe((v) => {
              this.applyFilter(v);
            });
        }

        break;
      case 'edit':
        this.toggleEditMode();
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.save, ActionButton.cancel, ActionButton.export]);
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any): void {
    const { optionFilter, type } = data;
    if (type === 'save') {
      const params: IFilterInterestRateInfoParam = {
        typeAccount: optionFilter.accountType,
        isFilter: optionFilter.isFilter,
        levelAccount: optionFilter.levelAccount,
      };
      this.store.dispatch(setFilterInterestRateInfo({ params }));

      const newListFilter = this.initialData.filter((customer, index) => {
        const isAccountTypeMatch = optionFilter.accountType.length
          ? optionFilter.accountType.includes(customer.accountType)
          : true;

        const isLevelAccountMatch = optionFilter.levelAccount.length
          ? optionFilter.levelAccount.includes(CUSTOMER_LEVEL_MAPPING[customer.customerLevel as ECustomerLevel])
          : true;
        return isAccountTypeMatch && isLevelAccountMatch;
      });

      this.data = newListFilter;
      this.store.dispatch(setFilteredDataInterestRateInfo({ data: this.data }));
    } else if (type === 'default') {
      this.data = this.initialData;
      this.store.dispatch(resetFilterInterestRateInfo());
    }
  }
}
