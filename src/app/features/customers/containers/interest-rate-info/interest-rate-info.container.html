<div class="personal-info-container">
  <div class="header-personal-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-33' | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          12 {{'MES-15' | translate}}
        </div>
        <div *ngIf="onEditMode" class="text-edit-cls">{{'MES-156' | translate}}: {{getCountDataEdited()}}</div>
      </div>
    </div>
    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>
  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
    >
    </sha-grid>
  </div>
</div>

<ng-template #inputNumber let-valueNumber="templateInfo" let-element="element" let-tag="tag" let-column="column">
  <app-input-number-custom-component
    [value]="valueNumber"
    [isEdit]="onEditMode"
    [element]="element"
    [tag]="tag"
    (dateChangeEvent)="changeDateEvent($event)"
    (unFocusElement)="elementSelectFunc($event)"
  ></app-input-number-custom-component>
</ng-template>

<ng-template #inputProportion let-templateInfo="templateInfo" let-element="element" let-tag="tag" let-column="column">
  <app-input-proportion-component
    [unit]="column.componentConfig.unit"
    [element]="element"
    [tag]="tag"
    [isEdit]="onEditMode"
    [value]="templateInfo"
    [column]="column"
    [typeValue]="column.componentConfig.type"
    (dateChangeEventInputProportion)="changeDateEvent($event)"
    (unFocusElement)="elementSelectFunc($event)"
  ></app-input-proportion-component>
</ng-template>
