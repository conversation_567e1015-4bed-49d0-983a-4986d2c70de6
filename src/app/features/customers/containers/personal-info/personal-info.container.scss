.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.personal-info-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-personal-info {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;
    white-space: nowrap;
    text-wrap: nowrap;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          white-space: nowrap;
          text-wrap: nowrap;
        }
        .text-edit-cls {
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          white-space: nowrap;
          text-wrap: nowrap;
          color: var(--color--brand--500);
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;

      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--cancel {
            color: var(--color--danger--600);
            border: 1px solid var(--color--danger--600);
          }

          #box-id--broker {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          mat-icon[data-mat-icon-name='people-icon'] {
            #Group,
            #Group_2,
            #Group_3 {
              ::ng-deep {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.not-accept-save-cls {
          ::ng-deep {
            #box-id--save {
              opacity: 0.5;
            }
          }
        }
        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }

  .table-view-container {
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;

    .table-custom-cls {
      height: calc(100% - 50px);
      ::ng-deep {
        .table-container {
          position: relative;
        }

        td {
          &:has(app-phone-number-table-component) {
            padding: 0;
            vertical-align: top;
            height: 36px;
            app-phone-number-table-component {
              display: flex;
              align-items: center;
              height: 100%;
              width: 100%;
            }
          }

          &:has(app-date-picker-component) {
            &:has(.isShowValid) {
              background-color: var(--color--danger--100);
            }
          }
        }

        .type-account {
          .box-show {
            border-radius: 16px;
            justify-content: center;
            background-color: #32d74b;
            display: inline-flex;
            padding: 0px 12px;
            white-space: nowrap;
            text-wrap: nowrap;

            max-width: 120px;
            margin: 0 auto;

            .input-table-view {
              text-align: center;
            }

            .isEditMode {
              border-color: transparent !important;
              text-align: unset;
            }
          }

          &.individual-cls {
            .box-show {
              background-color: #ffd60a;
            }
          }
        }

        .status-account {
          .box-show {
            border-radius: 16px;
            justify-content: center;
            background-color: var(--color--other--divider);
            display: inline-flex;
            padding: 2px 8px;
            white-space: nowrap;
            text-wrap: nowrap;

            max-width: 144px;
            margin: 0 auto;
          }

          &.active-cls {
            .box-show {
              background-color: #32d74b;
            }
          }
        }

        .rank-account {
          .box-show {
            border-radius: 16px;
            justify-content: center;
            background-color: var(--color--neutral--100);
            display: inline-flex;
            padding: 2px 8px;
            white-space: nowrap;
            text-wrap: nowrap;

            max-width: 144px;
            margin: 0 auto;
          }

          &.normal-cls {
            .box-show {
              background-color: var(--color--neutral--100);
            }
          }

          &.gold-cls {
            .box-show {
              background-color: #ffd60a;
            }
          }

          &.diamond-cls {
            .box-show {
              background-color: #66d4cf;
            }
          }
          &.platinum-cls {
            .box-show {
              background-color: #60a5fa;
            }
          }
          &.nvshs-cls {
            .box-show {
              background-color: #f58220;
            }
          }
          &.other-cls {
            .box-show {
              background-color: #fcd8ba;
            }
          }
        }

        .location-cls {
          .text-link {
            color: unset;
          }
        }

        .document-info {
          max-width: 100%;
          padding: 0 4px;
          display: inline-flex;
          height: 24px;
          align-items: center;
          vertical-align: middle;
          border-radius: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
          flex: 1;
          img {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            object-fit: contain;
            vertical-align: middle;
          }

          span {
            width: 100%;
            margin-left: 8px;
            vertical-align: middle;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            &.name {
              color: var(--color--accents--cyan-dark);
            }
          }
        }
        .add-bank-trigger {
          cursor: pointer;
          display: inline-block;
          padding: 2px 4px;
          color: var(--color--text--subdued);
          border-radius: 16px;
          flex: 1;
          &.edit-mode-cls {
            color: var(--color--accents--cyan-dark);
          }

          img,
          span {
            vertical-align: middle;
          }

          img[alt='add-doc'] {
            height: 18px;
          }

          span {
            margin-left: 4px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .type-account-cls {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 16px;
    background-color: #ffd60a;

    &.organize-cls {
      background-color: #32d74b;
    }
  }

  .file-extention-cls {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #5ac8f5;
  }
}

.calendar-template-info-cls {
  border-radius: 6px;
}
