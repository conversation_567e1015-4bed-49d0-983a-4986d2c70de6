<div class="personal-info-container">
  <div class="header-personal-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-07' | translate}}</div>

      <div class="number-info-cls">
        <!-- Tổng tài k<PERSON>n -->
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{data.length}} {{'MES-15' | translate}}
        </div>
        <!-- S<PERSON> lượng đã chỉnh sửa -->
        <div *ngIf="onEditMode" class="text-edit-cls personal-info-cls">
          {{'MES-156' | translate}}: {{getCountDataEdited()}}
        </div>
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        [listColumnInitial]="columnConfigsInitial"
        (eventClickEmit$)="clickButton($event)"
        class="personal-info-cls"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>
  <div class="table-view-container personal-info-cls">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); openPersonalInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      (clickMoreAction)="handleMoreAction($event)"
      [elementTagSelect]="elementTagSelect"
    >
    </sha-grid>

    <app-paginator (currentPageChanged)="changeCurrentPage($event)" [currentPage]="currentPage" [totalPage]="totalPage">
    </app-paginator>
  </div>
</div>

<ng-template #numberPhone let-templateInfo="templateInfo" let-element="element" let-tag="tag">
  <app-phone-number-table-component
    [data]="templateInfo"
    [isEdit]="onEditMode"
    [element]="element"
    (unFocusElement)="elementSelectFunc($event)"
    [tag]="tag"
    (dateChangeEvent)="changeDateEvent($event)"
  ></app-phone-number-table-component>
</ng-template>

<ng-template #calendarPicker let-calendar="templateInfo" let-element="element" let-tag="tag" let-column="column">
  @if(calendar) {
  <div #bankRef class="calendar-template-info-cls">
    <!-- <span (click)="calendarCustom.openCalendar()">{{calendar}}</span> -->
    <app-date-picker-component
      (dateChangeEvent)="changeDateEvent($event)"
      [isEdit]="onEditMode"
      [startDate]="calendar"
      [element]="element"
      [tag]="tag"
      [isExpiredDate]="column.componentConfig && column.componentConfig?.isExpiredDate ? true : false"
      [isValidateDate]="column.componentConfig && column.componentConfig?.isValidateDate ? true : false"
      [isValidBirthday]="column.componentConfig && column.componentConfig?.isValidBirthday ? true : false"
      (unFocusElement)="elementSelectFunc($event)"
      #calendarCustom
    ></app-date-picker-component>
  </div>
  } @else {
  <span>-</span>
  }
</ng-template>

<ng-template #fileTemplate let-file="templateInfo" let-element="element" let-tag="tag">
  @if(file?.name || file?.files?.length){
  <div class="document-info typo-body-12">
    <img [src]="'./assets/icons/pdf.svg'" alt="document-logo" />
    @if(file.name){
    <span class="name">{{file.name}}</span>
    }@else{
    <span>-</span>
    }
  </div>

  } @else if (checkTagDisable(tag, element.tagDisable)) {
  <div class="document-info typo-body-12">
    <span>-</span>
  </div>
  } @else {
  <div class="add-bank-trigger" [class.edit-mode-cls]="onEditMode">
    <img *ngIf="!onEditMode" src="assets/icons/add-file-icon.svg" alt="add-doc-gray" />
    <img *ngIf="onEditMode" src="assets/icons/file-extension-blue.svg" alt="add-doc" />
    <span>{{"MES-28" | translate}}</span>
  </div>
  }
</ng-template>
