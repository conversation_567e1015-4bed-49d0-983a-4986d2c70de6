import { Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import {
  CONVERT_GENDER_TO_LABLE,
  CONVERT_RANK_ACCOUNT_TO_CLASS,
  CONVERT_RANK_ACCOUNT_TO_LABEL,
  CONVERT_STATUS_ACCOUNT_TO_LABLE,
  CONVERT_TYPE_ACCOUNT_TO_LABLE,
  EAccountStatus,
  EGender,
  ETypeAccount,
} from '../../constants/customers';
import { DestroyService, LoadingService } from 'src/app/core/services';
import { PersonalInfoFilterComponent } from '../../components/personal-info-filter/personal-info-filter.component';
import { LIST_OF_NATIONALITY } from 'src/app/shared/constants/nationality';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import {
  combineLatest,
  distinctUntilChanged,
  Observable,
  startWith,
  Subject,
  take,
  takeUntil,
  withLatestFrom,
} from 'rxjs';
import { LIST_PROVINCE } from 'src/app/shared/constants/province';
import { Store } from '@ngrx/store';
import {
  getCustomerPersonalInfo,
  resetFilterPersonalInfo,
  setFilterPersonalInfo,
  updatePageIndexPersonalInfo,
} from '../../stores/customer.action';
import { IFilterPersonalInfoParam, TotalTags } from '../../model/customer';
import {
  pageIndexPersonalInfo$,
  selectCustomerList$,
  selectFilteredDataPersonalInfo$,
  selectFilterPersonalInfo$,
  selectSearchValue$,
} from '../../stores/customer.selector';
import { CELL_TYPE, IClickOnColumnEvent } from '@shared/models';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { updateBorkerName } from 'src/app/shared/utils/utils';
import { CustomerDetailPopupComponent } from '../../components/customer-detail-popup/customer-detail-popup.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import { Router } from '@angular/router';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { getCurrentBrokerView, getListAccountNumberAndLevelByBrokerView } from 'src/app/stores/shared/shared.actions';
import { IAllAccountNumber, IPayloadPersonalList } from 'src/app/shared/models/global';
import { initialCustomerState } from '../../stores/customer.reducer';
import { OptionsCustomerComponent } from '../../components/options-customer/options-customer.component';

/**
 * Thông tin cá nhân
 */
@Component({
  selector: 'app-personal-info-container',
  templateUrl: './personal-info.container.html',
  styleUrls: ['./personal-info.container.scss'],
  providers: [DestroyService],
})
export class PersonalInfoCustomerContainer
  extends BaseTableComponent<any>
  implements OnInit, ComponentCanDeactivate, OnDestroy
{
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('calendarPicker', { static: true }) calendarPicker: TemplateRef<any> | null = null;

  @ViewChild('numberPhone', { static: true }) numberPhoneTemplate: TemplateRef<any> | null = null;

  @ViewChild('fileTemplate', { static: true }) fileTemplate: TemplateRef<any> | null = null;

  isEdit = false;

  LIST_OF_NATIONALITY = LIST_OF_NATIONALITY;

  LIST_PROVINCE = LIST_PROVINCE;

  optionList = [
    {
      name: 'Cục Cảnh sát ĐKQL cư trú và DLQG về dân cư',
      id: '1',
      children: [],
      config: 'level1',
    },
    {
      name: 'Cục Cảnh sát QLHC về TTXH',
      id: '2',
      children: [],
      config: 'level1',
    },
    {
      name: 'Công an Tỉnh/Thành phố',
      id: '3',
      children: LIST_PROVINCE,
      config: 'level1',
    },
    {
      name: 'Cục Quản lý xuất nhập cảnh',
      id: '4',
      children: [],
      config: 'level1',
    },
  ];

  filterOptions!: IFilterPersonalInfoParam;

  LIST_MG: IListOptions[] = [];

  // searchValue: string = '';

  isFilterOrSearch = false;

  totalTags: TotalTags = {
    totalAccount: 0,
  };

  totalPage = 0;

  currentPage = 1;

  pageSize = 20;

  totalPageWithoutFilter = 0;

  userBrokerCode = '';

  allBrokerCode: IAllAccountNumber[] = [];

  payloadGetPersonalInfo!: IPayloadPersonalList;

  getCustomerInfo$ = new Subject<void>();

  // initialCustomer!: IAllAccountNumber[];

  /**
   * Constructor
   * @param store Store
   * @param _destroy DestroyService
   * @param popperService PopoverService
   * @param loadingService LoadingService
   */
  constructor(
    private readonly store: Store,
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    private readonly loadingService: LoadingService,
    private readonly router: Router
  ) {
    super();
    this.brokerInfo();
    this.toggleButtonByTags([ActionButton.broker, ActionButton.display, ActionButton.filter]);

    combineLatest([
      this.store.select(selectAllAccountNumberListByBrokerView$),
      this.store.select(selectSearchValue$),
      this.getCustomerInfo$.pipe(startWith('')),
    ])
      .pipe(
        withLatestFrom(this.store.select(selectFilterPersonalInfo$), this.store.select(pageIndexPersonalInfo$)),
        takeUntil(this._destroy)
      )
      .subscribe(([[allAccounts, searchValue], filter, pageIndex]) => {
        const params = this.route.snapshot.queryParams;
        this.allBrokerCode = [...allAccounts];
        if (!allAccounts.length) {
          this.loadingService.hide();
          this.data = [];
          this.initialData = [];
          this.isFilter = false;
          this.currentPage = 1;
          this.isFilterOrSearch = false;
          this.totalPageWithoutFilter = 1;
          this.filterOptions = { ...initialCustomerState.filterPersonalInfo };
          return;
        }
        this.totalPageWithoutFilter = Math.ceil(allAccounts.length / this.pageSize);

        let accountList = allAccounts
          .slice(this.pageSize * (+pageIndex - 1), this.pageSize * +pageIndex)
          ?.map((d) => d.accountNumber);

        if (searchValue) {
          accountList = [];
        }
        // filter accountList theo filter.customer khi ở trạng thái filter
        const { isFilter, customers, startYear, endYear, typeAccount } = filter;

        if (customers && isFilter) {
          accountList = customers;
        } else if (startYear || endYear || typeAccount) {
          accountList = [];
        }

        const payload = {
          accountNumbers: accountList,
          brokerCode: [params['brokerId']],
          userType: filter.typeAccount ?? '',
          fromBirthYear: filter.startYear ?? '',
          toBirthYear: filter.endYear ?? '',
          searchKey: searchValue ?? '',
        };

        this.payloadGetPersonalInfo = { ...payload };

        this.filterOptions = { ...filter };
        this.isFilter = isFilter;
        this.isFilterOrSearch = !!searchValue || isFilter;
        this.currentPage = +pageIndex;
        this.store.dispatch(getCustomerPersonalInfo({ payload, isSearchOrFilter: this.isFilterOrSearch }));
      });
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.store
      .select(selectCustomerList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((personalInfoList) => {
        if (!personalInfoList.length) {
          this.data = [];
          this.initialData = [];
          this.currentPage = 1;
          this.totalPage = 1;
          return;
        }
        this.data = structuredClone(personalInfoList) ?? [];
        this.totalTags.totalAccount = this.data.length;
        this.totalPage = this.totalPageWithoutFilter;
        this.initialData = structuredClone(this.data);
      });

    const template = this.calendarPicker;

    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 146,
        width: 146,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        isEdit: false,
        pinned: 'left',
        resizable: true,
      },

      {
        name: 'Trạng thái tài khoản',
        minWidth: 30,
        width: 132,
        tag: 'accountStatus',
        isDisplay: true,
        displayValueFn: (value) => CONVERT_STATUS_ACCOUNT_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === EAccountStatus.ACTIVE) {
            return 'status-account active-cls';
          } else return 'status-account';
        },
        isEdit: false,
        align: 'center',
        resizable: true,
      },

      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 200,
        tag: 'customerName',
        isDisplay: true,
        isEdit: false,
        resizable: true,
      },

      {
        name: 'Hạng khách hàng',
        minWidth: 30,
        width: 120,
        tag: 'customerGradeName',
        isDisplay: true,
        displayValueFn: (value) => CONVERT_RANK_ACCOUNT_TO_LABEL[value],
        dynamicClass: (value) => {
          return `rank-account ${CONVERT_RANK_ACCOUNT_TO_CLASS[value]}`;
        },
        isEdit: false,
        align: 'center',
        resizable: true,
      },

      {
        name: 'Loại tài khoản',
        minWidth: 30,
        width: 120,
        tag: 'accountType',
        isDisplay: true,
        displayValueFn: (value) => CONVERT_TYPE_ACCOUNT_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === ETypeAccount.INDIVIDUAL) {
            return 'type-account individual-cls';
          } else return 'type-account';
        },
        isEdit: false,
        align: 'center',
        resizable: true,
      },
      {
        name: 'Số CMND / CCCD / HC',
        minWidth: 30,
        width: 190,
        tag: 'identity',
        isDisplay: true,
        url: './assets/icons/file-extention-blue.svg',
        typeValue: CELL_TYPE.FILE,
        isEdit: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        // cellTemplate: this.fileTemplate,
      },
      {
        name: 'Ngày cấp',
        minWidth: 30,
        width: 130,
        tag: 'identityDate',
        isDisplay: true,
        typeValue: CELL_TYPE.CALENDAR,
        isEdit: true,
        cellTemplate: template,
        componentConfig: {
          isExpiredDate: true,
          isValidateDate: true,
        },
      },
      {
        name: 'Nơi cấp',
        minWidth: 30,
        width: 340,
        tag: 'identityIssuer',
        isDisplay: true,
        isEdit: true,
        // typeValue: CELL_TYPE.DROPDOWN_MULTIPLE_LEVEL,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
      },
      {
        name: 'Quốc tịch',
        minWidth: 30,
        width: 120,
        tag: 'nationalityId',
        isDisplay: true,
        isEdit: true,
        // typeValue: CELL_TYPE.NATIONALITY,
        // componentConfig: {
        //   displayOptionFn: (v: any) => v.name,
        //   multiple: false,
        //   searchKey: 'name',
        //   options: this.LIST_OF_NATIONALITY,
        //   isShowLogo: false,
        //   isShowAreaCode: false,
        // },
        typeValue: CELL_TYPE.TEXT,
        displayValueFn: (v) => {
          if (!v) {
            return '-';
          } else if (v === '001') {
            return 'Việt Nam';
          } else return v;
        },
        resizable: true,
      },
      {
        name: 'Ngày sinh',
        minWidth: 30,
        width: 130,
        tag: 'birthday',
        isDisplay: true,
        typeValue: CELL_TYPE.CALENDAR,
        isEdit: true,
        cellTemplate: template,
        resizable: true,
        componentConfig: {
          isValidBirthday: true,
        },
      },
      {
        name: 'Giới tính',
        minWidth: 30,
        width: 110,
        tag: 'sexId',
        isDisplay: true,
        displayValueFn: (value) => CONVERT_GENDER_TO_LABLE[value] ?? '-',
        typeValue: CELL_TYPE.DROPDOWN,
        isEdit: true,
        componentConfig: {
          displayOptionFn: (v: any) => {
            return CONVERT_GENDER_TO_LABLE[v.value];
          },
          multiple: false,
          searchKey: 'value',
          isSearch: false,
          options: [
            {
              name: 'Nam',
              value: EGender.MALE,
            },
            {
              name: 'Nữ',
              value: EGender.FEMALE,
            },
          ],
        },
        resizable: true,
      },
      {
        name: 'Số điện thoại',
        minWidth: 30,
        width: 170,
        tag: 'telephone',
        typeValue: CELL_TYPE.TEXT,
        type: 'text',
        isDisplay: true,
        isEdit: true,
        // cellTemplate: numberTemplate,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
      },
      {
        name: 'Email',
        minWidth: 30,
        width: 250,
        tag: 'email',
        type: 'text',
        isDisplay: true,
        url: './assets/icons/sms.svg',
        panelClass: 'location-cls',
        typeValue: CELL_TYPE.TEXT,
        isEdit: true,
        isEmail: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
      },
      {
        name: 'Địa chỉ',
        minWidth: 30,
        width: 460,
        tag: 'address',
        type: 'text',
        isDisplay: true,
        url: './assets/icons/location.svg',
        panelClass: 'location-cls',
        typeValue: CELL_TYPE.TEXT,
        isEdit: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
      },
      {
        name: 'Số ĐKKD',
        minWidth: 30,
        width: 170,
        tag: 'registrationNo',
        isDisplay: true,
        isEdit: true,
        url: './assets/icons/file-extention-blue.svg',
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        typeValue: CELL_TYPE.FILE,
        resizable: true,
        // cellTemplate: this.fileTemplate,
      },
      {
        name: 'Ngày cấp ĐKKD',
        minWidth: 30,
        width: 130,
        tag: 'registrationDate',
        isDisplay: true,
        typeValue: CELL_TYPE.CALENDAR,
        isEdit: true,
        cellTemplate: template,
      },
      {
        name: 'Đại diện PL',
        minWidth: 30,
        width: 150,
        tag: 'registrationRep',
        isDisplay: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        typeValue: CELL_TYPE.TEXT,
        isEdit: true,
        resizable: true,
        type: 'text',
      },
    ];

    this.store
      .select(selectFilteredDataPersonalInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((data) => {
        if (!this.isFilterOrSearch) return;
        this.filteredData = data.map((d) => ({
          ...d,
          parent: null,
        }));
        this.totalPage = Math.ceil(data.length ? data.length / this.pageSize : 1);
        this.currentPage = 1;
        this.data = this.filteredData.slice(0, this.pageSize);
        this.initialData = structuredClone(this.data);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
    this.cdf.detectChanges();
  }

  /**
   * Thông tin của broker
   */
  brokerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(
        takeUntil(this._destroy),
        distinctUntilChanged(() => false)
      )
      .subscribe((currentBroker) => {
        if (!currentBroker) return;

        this.userBrokerCode = currentBroker.brokerCode;

        combineLatest([
          this.store.select(selectInfoUserLogin$),
          this.store.select(selectAllBrokerLevelListByBrokerView$),
        ])
          .pipe(takeUntil(this._destroy))
          .subscribe(([userList, brokers]) => {
            const queryParams = this.route.snapshot.queryParams;

            if (!userList) return;

            const brokerConvert = updateBorkerName([...brokers], [...userList]);

            const subBroker = brokerConvert.map((broker) => {
              return {
                brokerCode: `${broker.brokerCode}`,
                name: `${broker.brokerName}`,
                isSelect: queryParams['brokerId']
                  ? broker.brokerCode === queryParams['brokerId']
                  : broker.brokerCode === currentBroker.brokerCode,
              };
            });

            this.LIST_MG = [...subBroker];
            const broker = this.LIST_MG.find((t) => t.isSelect);

            if (broker) {
              this.findButtonByTags(ActionButton.broker).label = broker['name'] ?? '';
            }
          });
      });
  }

  /**
   * searchData
   */
  // searchData() {
  //   let searchData = [];
  //   if (this.searchValue) {
  //     const searchValue = this.searchValue.toString().toLowerCase();
  //     if (!this.isFilter) {
  //       searchData = this.initialData.filter((item) => {
  //         return this.containsSearchValue(item, searchValue);
  //       });
  //     } else {
  //       searchData = this.filteredData.filter((item) => {
  //         return this.containsSearchValue(item, searchValue);
  //       });
  //     }
  //     this.isSearch = true; // Set isSearch to true when searching
  //   } else {
  //     this.isFilter ? (searchData = deepClone(this.filteredData)) : (searchData = deepClone(this.initialData));
  //     this.isSearch = false; // Set isSearch to false when not searching
  //   }
  //   this.searchedData = deepClone(searchData);
  //   this.data = deepClone(searchData);
  // }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterPersonalInfo());
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  // containsSearchValue(item: any, searchValue: string): boolean {
  //   return (
  //     (item.accountNumber.toString().toLowerCase().includes(searchValue)) ||
  //     (item.customerName.toString().toLowerCase().includes(searchValue)) ||
  //     (item.identificationId.name.toString().includes(searchValue)) ||
  //     (item.businessCode.name.toString().toLowerCase().includes(searchValue)) ||
  //     (item.email.toString().includes(searchValue)) ||
  //     (item.telephone.toString().includes(searchValue))
  //   );
  // }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   * changeCurrentPage
   * @param data
   */
  changeCurrentPage(currentPage: number) {
    this.currentPage = currentPage;
    if (this.isFilterOrSearch) {
      this.data = this.filteredData.slice(this.pageSize * (+currentPage - 1), this.pageSize * +currentPage);
      this.initialData = structuredClone(this.data);
      return;
    }
    this.store.dispatch(updatePageIndexPersonalInfo({ pageIndex: currentPage }));

    this.getCustomerInfo$.next();
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'broker':
        this.changeViewBrokerPer();
        break;
      case 'filter':
        {
          const ref = this.openFilter(PersonalInfoFilterComponent, {
            width: '450px',
            data: this.filterOptions,
          });
          this.closeApplyFilter(ref);
        }
        break;
      case 'edit':
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * CloseApplyFilter
   * @param {any} ref ref
   */
  closeApplyFilter(ref: any) {
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        this.applyFilter(v);
      });
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([
      ActionButton.broker,
      ActionButton.edit,
      ActionButton.save,
      ActionButton.cancel,
      ActionButton.export,
      ActionButton.import,
    ]);
  }

  /**
   * ApplyFilter
   * @param {any} data
   */
  applyFilter(data: any): void {
    const { optionFilter, type } = data;
    if (type === 'save') {
      const { customers, typeAccount, startYear, endYear } = optionFilter;
      this.isFilter = !!customers?.length || !!typeAccount || !!startYear || !!endYear;

      const params: IFilterPersonalInfoParam = {
        customers,
        typeAccount: typeAccount,
        startYear,
        endYear,
        isFilter: this.isFilter,
      };
      this.store.dispatch(setFilterPersonalInfo({ params }));
    } else if (type === 'default') {
      this.isFilter = false;
      this.store.dispatch(resetFilterPersonalInfo());
    }
    this.getCustomerInfo$.next();
  }

  getPersonalInfoByAccountNumberLevel() {
    const borkerId = this.route.snapshot.queryParams['brokerId'];

    this.store.dispatch(
      getListAccountNumberAndLevelByBrokerView({
        brokerCode: borkerId ?? this.userBrokerCode,
        isPrimaryBrokerCode: true,
      })
    );
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    let isDateInRange: boolean = true;
    const newListFilter = data.filter((customer) => {
      if (customer.birthday) {
        const dobYear = new Date(customer.birthday.split('/').reverse().join('-')).getFullYear();
        isDateInRange =
          (optionFilter.dateStart ? dobYear >= optionFilter.dateStart : true) &&
          (optionFilter.dateEnd ? dobYear <= optionFilter.dateEnd : true);
      }

      const isAccountTypeMatch = optionFilter.accountType.length
        ? optionFilter.accountType.includes(customer.accountType)
        : true;

      return isDateInRange && isAccountTypeMatch;
    });

    return newListFilter;
  }

  /**
   * openPersonalInfoDetail
   * @param event
   */
  openPersonalInfoDetail(event: IClickOnColumnEvent) {
    if (this.onEditMode) return;

    const { element } = event;
    this.dialogService.open(CustomerDetailPopupComponent, {
      width: '71vw',
      height: '94vh',
      data: {
        element,
        route: 'personal',
        data: this.data,
      },
    });
  }

  /**
   * changeViewBrokerPer
   */
  changeViewBrokerPer() {
    const queryParamsPer = this.route.snapshot.queryParams;
    const elementRefPer = new ElementRef(document.querySelector(`#box-id--broker`));

    const elementWidth = elementRefPer.nativeElement as HTMLElement;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRefPer as any,
      width: elementWidth.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelectedPer = res.data.find((i) => i.isSelect);
      if (!itemSelectedPer) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBrokerPer = userList.find((user) => user.brokerCode === itemSelectedPer['brokerCode']);

          const subBroker = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelectedPer['brokerCode']);
          if (queryParamsPer['brokerId'] === itemSelectedPer['brokerCode']) return;
          if (subBroker && !currentBrokerPer) {
            const brokerCode = subBroker['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (currentBrokerPer) {
            if (this.userBrokerCode === currentBrokerPer.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBrokerPer.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBrokerPer }));
          }

          if (subBroker) {
            this.LIST_MG = this.LIST_MG.map((brokerPer) => ({
              ...brokerPer,
              isSelect: brokerPer['brokerCode'] === subBroker['brokerCode'],
            }));

            this.findButtonByTags(ActionButton.broker).label = subBroker['name'] ?? '';
          }

          this.store.dispatch(resetFilterPersonalInfo());
          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));

          let brokerId: string | null;
          if (currentBrokerPer) {
            brokerId = currentBrokerPer.brokerCode;
          } else if (subBroker) {
            brokerId = subBroker['brokerCode'] as string;
          } else {
            brokerId = null;
          }

          this.router.navigate([], {
            queryParams: {
              ...queryParamsPer,
              brokerId,
            },
          });
        });
    });
  }

  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsCustomerComponent,
      width: 'fit-content',
      hasBackdrop: true,
      position: 1,
      componentConfig: {
        element: element,
        data: this.data,
      },
    });
  }
}
