import { Component, ElementRef, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { DestroyService, LoadingService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { BankInfoFilterComponent } from '../../components/bank-info-filter/bank-info-filter.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { CELL_TYPE, IClickOnColumnEvent } from '@shared/models';
import { LIST_OF_BANK } from 'src/app/shared/constants/bank';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import { Observable, combineLatest, take, takeUntil } from 'rxjs';
import { IBeneficiaryBank, IFilterBankInfoParam, TotalTags } from '../../model/customer';
import { Store } from '@ngrx/store';
import { getCustomersBankInfo, resetFilterBankInfo, setFilterBankInfo } from '../../stores/customer.action';
import {
  selectCustomerBankInfo$,
  selectFilterBankInfo$,
  selectFilteredDataBankInfo$,
  selectSearchValue$,
} from '../../stores/customer.selector';
import { CONVERT_TYPE_ACCOUNT_TO_LABLE, ETypeAccount } from '../../constants/customers';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { deepClone } from 'src/app/shared/utils/utils';
import { CustomerDetailPopupComponent } from '../../components/customer-detail-popup/customer-detail-popup.component';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { selectAllBankList$, selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { getCurrentBrokerView } from 'src/app/stores/shared/shared.actions';

/**
 * Thông tin ngân hàng
 */
@Component({
  selector: 'app-bank-info-container',
  templateUrl: './bank-info.container.html',
  styleUrls: ['./bank-info.container.scss'],
})
export class BankInfoCustomerContainer
  extends BaseTableComponent<any>
  implements OnInit, ComponentCanDeactivate, OnDestroy
{
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('bankInfo', { static: true }) bankInfo: TemplateRef<any> | null = null;

  customerData: any[] = [];

  LIST_OF_BANK = LIST_OF_BANK;

  filterOptions!: IFilterBankInfoParam;

  LIST_MG: IListOptions[] = [];

  searchValue: string = '';
  totalTags: TotalTags = {
    totalAccount: 0,
  };

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   * @param popoverService PopoverService
   * @param loadingService LoadingService
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly popoverService: PopoverService,
    private readonly loadingService: LoadingService
  ) {
    super();
    this.brokerInfoBank();
    this.toggleButtonByTags([
      ActionButton.broker,
      // ActionButton.edit,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);

    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        if (!user) return;
        this.store.dispatch(getCustomersBankInfo({ id: user.brokerCode }));
      });
  }

  /**
   * OnInit
   */
  ngOnInit(): void {
    combineLatest([this.store.select(selectCustomerBankInfo$), this.store.select(selectAllBankList$)])
      .pipe(takeUntil(this._destroy))
      .subscribe(([bankInfo, bankList]) => {
        const dataClone = structuredClone(bankInfo);

        this.totalTags = {
          totalAccount: dataClone.length,
        };

        this.data = dataClone.map((d) => {
          d.bankAccounts.forEach((i: any, index: number) => {
            d[`bankAccounts${index + 1}`] = {
              ...i,
              beneficiaryBank: {
                ...i.beneficiaryBank,
                logo: this.getLogoBank(i.beneficiaryBank),
              },
            };
          });
          return d;
        });
        this.initialData = structuredClone(this.data);
        if (this.isSearch) {
          this.data = this.searchedData;
        }
      });

    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 146,
        width: 146,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        isEdit: false,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 300,
        tag: 'customerName',
        isDisplay: true,
        isEdit: false,
        resizable: true,
      },
      {
        name: 'Loại tài khoản',
        minWidth: 30,
        width: 120,
        tag: 'accountType',
        isDisplay: true,
        displayValueFn: (value1) => CONVERT_TYPE_ACCOUNT_TO_LABLE[value1],
        dynamicClass: (value2) => {
          if (value2 === ETypeAccount.INDIVIDUAL) {
            return 'type-account individual-cls';
          } else return 'type-account';
        },
        align: 'center',
        isEdit: false,
        resizable: true,
      },
      {
        name: 'Tài khoản NH (01)',
        minWidth: 30,
        width: 265,
        isDisplay: true,
        cellTemplate: this.bankInfo,
        tag: 'bankAccounts1',
        resizable: true,
        isEdit: true,
        typeValue: CELL_TYPE.BANK,
      },
      {
        name: 'Tài khoản NH (02)',
        minWidth: 30,
        width: 265,
        isDisplay: true,
        cellTemplate: this.bankInfo,
        resizable: true,
        tag: 'bankAccounts2',
        isEdit: true,
        typeValue: CELL_TYPE.BANK,
      },
      {
        minWidth: 30,
        width: 265,
        tag: 'bankAccounts3',
        isDisplay: true,
        cellTemplate: this.bankInfo,
        resizable: true,
        isEdit: true,
        typeValue: CELL_TYPE.BANK,
        name: 'Tài khoản NH (03)',
      },
      {
        minWidth: 30,
        width: 265,
        tag: 'bankAccounts4',
        isDisplay: true,
        cellTemplate: this.bankInfo,
        name: 'Tài khoản NH (04)',
        resizable: true,
        isEdit: true,
        typeValue: CELL_TYPE.BANK,
      },
      {
        width: 265,
        name: 'Tài khoản NH (05)',
        minWidth: 30,
        tag: 'bankAccounts5',
        isDisplay: true,
        cellTemplate: this.bankInfo,
        resizable: true,
        isEdit: true,
        typeValue: CELL_TYPE.BANK,
      },
    ];

    this.store
      .select(selectFilteredDataBankInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((data) => {
        this.data = data.length ? deepClone(data) : deepClone(this.initialData);
      });

    this.store
      .select(selectFilterBankInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchValue = value ?? '';
        this.searchData();
      });
  }
  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterBankInfo());

    this._destroy.next();
    this._destroy.complete();
  }

  /**
   * Thông tin của broker
   */
  brokerInfoBank() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBrokerBank) => {
        if (!currentBrokerBank) return;
        this.findButtonByTags(ActionButton.broker).label = `${currentBrokerBank.brokerCode}: ${currentBrokerBank.brokerName}`;

        this.store
          .select(selectInfoUserLogin$)
          .pipe(takeUntil(this._destroy))
          .subscribe((userListBank) => {
            if (!userListBank) return;
            this.LIST_MG = userListBank.map((user) => {
              return {
                brokerCode: `${user.brokerCode}`,
                name: `${user.brokerCode}: ${user.brokerName}`,
                isSelect: user.brokerCode === currentBrokerBank.brokerCode,
              };
            });
          });
      });
  }

  getLogoBank(beneficiaryBank: IBeneficiaryBank) {
    return LIST_OF_BANK.find((b) => b.name === beneficiaryBank.name)?.logo;
  }

  /**
   * searchData
   */
  searchData() {
    let searchDataBank = [];
    if (this.searchValue) {
      const searchValue = this.searchValue.toString().toLowerCase();
      if (!this.isFilter) {
        searchDataBank = this.initialData.filter((item) => {
          return this.containsSearchValue(item, searchValue);
        });
      } else {
        searchDataBank = this.filteredData.filter((item) => {
          return this.containsSearchValue(item, searchValue);
        });
      }
      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchDataBank = this.isFilter ? deepClone(this.filteredData) : deepClone(this.initialData);

      this.isSearch = false; // Set isSearch to false when not searching
    }
    this.searchedData = deepClone(searchDataBank);
    this.data = deepClone(searchDataBank);
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    return (
      item.customerName.toString().toLowerCase().includes(searchValue) ??
      item.accountNumber.toString().toLowerCase().includes(searchValue)
    );
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'broker':
        this.changeViewBroker();
        break;
      case 'filter':
        {
          const ref = this.openFilter(BankInfoFilterComponent, {
            width: '450px',
            data: this.filterOptions,
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe((v) => {
              if (!v) return;
              this.applyFilter(v);
            });
        }
        break;
      case 'edit':
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * ApplyFilter
   * @param data data
   */
  applyFilter(data: any): void {
    const { optionFilter, type } = data;
    const dataClone = deepClone(this.initialData);

    if (type === 'save') {
      const params: IFilterBankInfoParam = {
        typeAccount: optionFilter.accountType,
        isFilter: optionFilter.isFilter,
        bankIds: optionFilter.bankIds,
      };
      this.store.dispatch(setFilterBankInfo({ params }));
      const newListFilter = this.isSearch
        ? this.logicFilter(this.searchedData, optionFilter)
        : this.logicFilter(dataClone, optionFilter);
      this.loadingService.hide();
      this.filteredData = newListFilter;
      this.data = newListFilter;
      this.totalTags.totalAccount = this.data.length;
    } else if (type === 'default') {
      this.isFilter = false;
      this.filteredData = [];
      this.totalTags.totalAccount = this.initialData.length;
      this.isSearch
        ? this.store
            .select(selectSearchValue$)
            .pipe(takeUntil(this._destroy))
            .subscribe((value) => {
              this.searchValue = value ?? '';
              this.searchData();
            })
        : (() => {
            this.data = this.initialData;
          })();
      this.store.dispatch(resetFilterBankInfo());
      this.loadingService.hide();
    }
  }
  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    const newListFilter = data.filter((customer, index) => {
      const isAccountTypeMatch = optionFilter.accountType.length
        ? optionFilter.accountType.includes(customer.accountType)
        : true;

      const bankAccounts = [
        customer.bankAccounts1,
        customer.bankAccounts2,
        customer.bankAccounts3,
        customer.bankAccounts4,
        customer.bankAccounts5,
      ];
      const isBankIdsMatch = bankAccounts.some(
        (account) => account && optionFilter.bankIds.includes(account.beneficiaryBank?.id)
      );
      return isBankIdsMatch && isAccountTypeMatch;
    });

    return newListFilter;
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.save, ActionButton.cancel, ActionButton.export]);
  }

  /**
   * openBankInfoDetail
   * @param event
   */
  openBankInfoDetail(event: IClickOnColumnEvent) {
    if (this.onEditMode) return;

    const { element } = event;

    this.dialogService.open(CustomerDetailPopupComponent, {
      width: '71vw',
      height: '94vh',
      data: {
        element,
        route: 'bank',
      },
    });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    const elementRef = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidth = elementRef.nativeElement as HTMLElement;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRef as any,
      width: elementWidth.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBroker = userList.find((user) => user.brokerCode === itemSelected['brokerCode']);
          if (!currentBroker) return;
          this.store.dispatch(getCurrentBrokerView({ data: currentBroker }));
        });
    });
  }
}
