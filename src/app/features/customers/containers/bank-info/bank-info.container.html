<div class="personal-info-container">
  <div class="header-personal-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-08' | translate}}</div>

      <div class="number-info-cls">
        <!-- Tổng tài k<PERSON>n -->
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{totalTags.totalAccount}} {{'MES-15' | translate}}
        </div>
        <!-- <PERSON><PERSON> lượng chỉnh sửa -->
        <div *ngIf="onEditMode" class="text-edit-cls">{{'MES-156' | translate}}: {{getCountDataEdited()}}</div>
      </div>
    </div>
    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>
  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); openBankInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [editable]="onEditMode"
      [isInValid]="isInValid"
    >
    </sha-grid>
  </div>
</div>

<ng-template #bankInfo let-bankInfo="templateInfo" let-element="element" let-tag="tag">
  @if(bankInfo) {
  <div
    class="bank-account typo-body-12"
    [matTooltip]="('MES-67' | translate) + ': ' + bankInfo.customerName"
    matTooltipPosition="above"
    matTooltipClass="custom-tooltip"
  >
    <img [src]="bankInfo.beneficiaryBank.logo" alt="bank-logo" />
    <span>{{bankInfo.beneficiaryBank.name}} - {{bankInfo.accountNumber}}</span>
  </div>
  } @if(!bankInfo) {
  <div class="add-bank-trigger typo-body-12" [class.edit-mode-cls]="onEditMode">
    <img *ngIf="!onEditMode" src="assets/icons/add-circle-gray.svg" alt="add-bank" />
    <img *ngIf="onEditMode" src="assets/icons/add-circle.svg" alt="add-bank" />
    <span>{{"MES-28" | translate}}</span>
  </div>
  }
</ng-template>
