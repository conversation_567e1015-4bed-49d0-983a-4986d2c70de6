.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.customer-group-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-customer-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;
    text-wrap: nowrap;
    white-space: nowrap;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
        .text-edit-cls {
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          white-space: nowrap;
          text-wrap: nowrap;
          color: var(--color--brand--500);
        }
      }

      .dropdown-btn,
      .collapse-btn {
        display: flex;
        align-items: center;
        background-color: var(--color--brand--50);
        border-radius: 16px;

        img {
          padding: 4px, 8px, 4px, 8px;
          width: 20px;
          height: 20px;
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;
      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }
          #box-id--cancel {
            color: var(--color--danger--600);
            border: 1px solid var(--color--danger--600);
          }

          #box-id--broker {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          mat-icon[data-mat-icon-name='people-icon'] {
            #Group,
            #Group_2,
            #Group_3 {
              ::ng-deep {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
        &.not-accept-save-cls {
          ::ng-deep {
            #box-id--save {
              opacity: 0.5;
            }
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }

  .tag-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
    height: 56px;
    overflow: hidden;
    transition: transform 0.5s ease;
    text-wrap: nowrap;
    white-space: nowrap;
    padding: 12px 0px;
    margin: 0 12px;
    position: relative;

    .box-info {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 8px;
      border-radius: 16px;
      background-color: #f8fafd;
      box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
      color: #808080;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      white-space: nowrap;
      text-wrap: nowrap;
      width: fit-content;
    }
  }

  // Hide the tabContainer
  .hidden {
    display: none;
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        td {
          &:has(.group-name-wrapper) {
            overflow: visible;
          }

          &:has(.isNotInBroker) {
            background-color: var(--color--danger--50);

            .select-text-cls {
              display: flex;
              align-items: center;
              gap: 8px;
            }

            img[alt='expired-date-icon'] {
              cursor: pointer;
            }
          }

          &.editable-mode-cls {
            &:hover {
              border: 1px solid transparent !important;
              border-bottom: 1px solid #f1f2f6 !important;
            }
          }
          &.focusElement {
            border: 1px solid transparent !important;
          }
        }
        app-dropdown-and-files-for-table {
          .file-box {
            width: 100%;
          }
        }
        .change-value-mode-box {
          border: 1px solid transparent !important;
        }
        .change-value-mode {
          border: 1px solid transparent !important;
        }
        .isEditMode {
          border: 1px solid transparent !important;
          padding-left: 0 !important;
          padding-right: 0 !important;
        }
        .fee-cls {
          span {
            background-color: var(--color--accents--yellow-dark);
            border-radius: 16px;
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            input {
              text-align: center;
            }
          }
        }

        &.base-transaction-fee,
        &.derivatives-transaction-fee,
        &.interest-rate-2,
        &.interest-rate-3,
        &.interest-rate-4 {
          span {
            max-width: 100px;
            margin: 0 auto;
          }
        }

        &.interest-rate-1 {
          span {
            max-width: 110px;
            margin: 0 auto;
          }
        }
      }

      &.isNotLoadingData {
        ::ng-deep {
          .col-action {
            cursor: not-allowed !important;
            & > button {
              cursor: not-allowed !important;
              & > mat-icon {
                cursor: not-allowed !important;
                user-select: unset !important;
              }
            }
          }
        }
      }
    }
  }
}

// ------------- Template --------------- //
.group-name-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;

  .group-name {
    text-wrap: nowrap;
    white-space: nowrap;
    border-radius: 6px;
    color: #ffffff;
    padding: 2px 8px;
    .input-cls-custom {
      border: 1px solid var(--color--brand--500);
      border-radius: 6px;
      &:hover,
      &:focus {
        border: 1px solid var(--color--brand--500);
      }
    }
  }

  .no-group {
    background-color: var(--color--neutral--100);
    color: var(--color--text--default);
  }

  .group {
    background-color: var(--color--brand--500);
  }

  &.change-mode-box {
    .group-name {
      background-color: transparent;
      border: 1px solid var(--color--brand--500);
      border-radius: 0;
      .input-cls-custom {
        border: 1px solid transparent;
      }
    }
  }
  &.edit-mode-box {
    .group-name {
      background-color: transparent;
      border: 1px solid var(--color--brand--500);
      border-radius: 0;
    }
  }

  &.invalid-mode-name-box {
    .group-name {
      border: 1px solid var(--color--danger--600);
      color: unset;
      background-color: var(--color--danger--50);
      border-radius: 0;
      .input-cls-custom {
        border: 1px solid transparent;
        background-color: var(--color--danger--50);
      }
    }
  }
  .icons-conatiner {
    // position: relative;
    // min-width: 10px;
    min-height: 34px;
    background-color: var(--color--neutral--white);
    display: flex;
    align-items: center;

    &.background-expanded-cls {
      background-color: #f6f6f6;
    }
    // .icons-box {
    // position: absolute;
    // top: 50%;
    // transform: translateY(-50%);
    // left: 0;
    // }
  }

  .icons-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;

    .x-cross-icon {
      ::ng-deep {
        path {
          stroke: var(--color--danger--600);
          fill: var(--color--danger--600);
        }
      }
    }
    .tick-icon {
      ::ng-deep {
        path {
          stroke: var(--color--brand--500);
        }
      }
    }

    .add-user-icon {
      ::ng-deep {
        path {
          stroke: #808080;
        }
      }
      &:hover {
        ::ng-deep {
          path {
            stroke: var(--color--brand--500);
          }
        }
      }
    }
    .edit-icon {
      &:hover {
        ::ng-deep {
          path {
            stroke: var(--color--brand--500);
          }
        }
      }
    }

    .copy-icon {
      ::ng-deep {
        path {
          fill: #808080;
        }
      }
      &:hover {
        ::ng-deep {
          path {
            fill: var(--color--brand--500);
          }
        }
      }
    }

    .delete-icon {
      ::ng-deep {
        path {
          fill: #808080;
        }
      }
      &:hover {
        ::ng-deep {
          path {
            fill: var(--color--danger--600);
          }
        }
      }
    }

    .icons-cls {
      cursor: pointer;
    }

    .copy-icon,
    .add-user-icon,
    .delete-icon,
    .x-cross-icon,
    .tick-icon {
      // display: none;
      margin-left: 10px;
      transition: transform 0.5s ease;
    }

    &.hide-button-cls {
      .edit-icon,
      .x-cross-icon,
      .tick-icon {
        display: none;
      }
      .add-user-icon {
        // display: inline-block;
        display: none;
        &:hover {
          ::ng-deep {
            path {
              stroke: var(--color--brand--500);
            }
          }
        }
      }
      .delete-icon {
        display: none;
      }
    }
    .x-cross-icon,
    .tick-icon {
      display: none;
    }

    &.show-button-confirm-input {
      .copy-icon,
      .add-user-icon,
      .delete-icon,
      .edit-icon {
        display: none;
      }

      .x-cross-icon,
      .tick-icon {
        display: inline-block;
      }

      .tick-icon {
        margin-left: 0px;
      }
    }
  }
}

.context-menu-container-cls {
  display: flex;
  flex-direction: column;
  .box-btn {
    padding: 10px 2px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
  }
}
