import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { ImmerComponentStore } from 'ngrx-immer/component-store';
import { catchError, finalize, of, switchMap, tap } from 'rxjs';
import { LoadingService, MessageService } from 'src/app/core/services';
import { CustomerService } from '../../services/customer.service';
import {
  ICusomterGroupPayload,
  ICustomerDataResponse,
  IPayloadAddCustomerInGroup,
  IPayloadTranferEachOfCustomers,
} from '../../model/customer';
import { ApiErrorResponse } from '@shared/models';
import { tapResponse } from '@ngrx/operators';
import { getCustomerGroupsList } from 'src/app/stores/shared/shared.actions';
import { ActivatedRoute } from '@angular/router';

interface IGroupCustomerState {
  customerList: ICustomerDataResponse[];
}

@Injectable()
export class GroupCustomerStore extends ImmerComponentStore<IGroupCustomerState> {
  readonly editNameCustomerGroup = this.effect<{ name: string; id: string; brokerCode: string }>(($event) =>
    $event.pipe(
      tap(() => this.loadingService.show()),
      switchMap(({ name, id, brokerCode }) => {
        return this.customerService.editNameCustomerGroup(name, id, brokerCode).pipe(
          finalize(() => this.loadingService.hide()),
          catchError((errorEditApi) => of(new ApiErrorResponse<any>(errorEditApi)))
        );
      }),
      tapResponse(
        (resEditApi) => {
          if (resEditApi instanceof ApiErrorResponse) {
            this.messageService.error(resEditApi.message);
            return;
          }
          this.messageService.success(resEditApi.message);
          const brokerCodeEditApi = this.route.snapshot.queryParams['brokerId'];
          this.store.dispatch(getCustomerGroupsList({ brokerCode:brokerCodeEditApi }));
        },
        (err: any) => {}
      )
    )
  );

  readonly deleteCustomerGroup = this.effect<{ ids: string[]; brokerCode: string }>(($event) =>
    $event.pipe(
      tap(() => this.loadingService.show()),
      switchMap(({ ids, brokerCode }) => {
        return this.customerService.deleteCustomerGroup(ids, brokerCode).pipe(
          finalize(() => this.loadingService.hide()),
          catchError((errorDe) => of(new ApiErrorResponse<any>(errorDe)))
        );
      }),
      tapResponse(
        (resDe) => {
          if (resDe instanceof ApiErrorResponse) {
            this.messageService.error(resDe.message);
            return;
          }
          const brokerCodeDe = this.route.snapshot.queryParams['brokerId'];
          this.store.dispatch(getCustomerGroupsList({ brokerCode: brokerCodeDe }));
          this.messageService.success(resDe.message);
        },
        (err: any) => {}
      )
    )
  );

  readonly addCustomerInGroup = this.effect<IPayloadAddCustomerInGroup>(($event) =>
    $event.pipe(
      tap(() => this.loadingService.show()),
      switchMap((payload) => {
        return this.customerService.addCustomerInGroup(payload).pipe(
          finalize(() => this.loadingService.hide()),
          catchError((errorAdd) => of(new ApiErrorResponse<any>(errorAdd)))
        );
      }),
      tapResponse(
        (resAdd) => {
          if (resAdd instanceof ApiErrorResponse) {
            this.messageService.error(resAdd.message);
            return;
          }
          this.messageService.success(resAdd.message);
          const brokerCodeAdd = this.route.snapshot.queryParams['brokerId'];
          this.store.dispatch(getCustomerGroupsList({ brokerCode: brokerCodeAdd }));
        },
        (err: any) => {}
      )
    )
  );

  readonly deleteCustomerInGroup = this.effect<{ groupId: string; accountNos: string[]; brokerCode: string }>(
    ($event) =>
      $event.pipe(
        tap(() => this.loadingService.show()),
        switchMap(({ groupId, accountNos, brokerCode }) => {
          return this.customerService.deleteCustomerInGroup(groupId, accountNos, brokerCode).pipe(
            finalize(() => this.loadingService.hide()),
            catchError((errorDele) => of(new ApiErrorResponse<any>(errorDele)))
          );
        }),
        tapResponse(
          (resDel) => {
            if (resDel instanceof ApiErrorResponse) {
              this.messageService.error(resDel.message);
              return;
            }
            this.messageService.success(resDel.message);
            const brokerCodeDel = this.route.snapshot.queryParams['brokerId'];
            this.store.dispatch(getCustomerGroupsList({ brokerCode: brokerCodeDel }));
          },
          (err: any) => {}
        )
      )
  );

  readonly transferEachOfCustomer = this.effect<IPayloadTranferEachOfCustomers>(($event) =>
    $event.pipe(
      tap(() => this.loadingService.show()),
      switchMap((payload) => {
        return this.customerService.transferEachOfCustomer(payload).pipe(
          finalize(() => this.loadingService.hide()),
          catchError((errorTrans) => of(new ApiErrorResponse<any>(errorTrans)))
        );
      }),
      tapResponse(
        (resTrans) => {
          // Tạm ẩn do API chưa hoàn thiện
          if (resTrans instanceof ApiErrorResponse) {
            this.messageService.error(resTrans.message);
            return;
          }
          this.messageService.success(resTrans.message);
          const brokerCodeTrans = this.route.snapshot.queryParams['brokerId'];
          this.store.dispatch(getCustomerGroupsList({ brokerCode: brokerCodeTrans }));
        },
        (err: any) => {}
      )
    )
  );

  // customerIds.length && oldGroupId !== CUSTOMERS_NO_GROUP_ID
  // ? this.customerService.deleteCustomerInGroup(oldGroupId, customerIds)
  // : of(true),

  readonly createCustomerGroupWithNoId = this.effect<ICusomterGroupPayload>(($event) =>
    $event.pipe(
      tap(() => this.loadingService.show()),
      switchMap((payload) => {
        return this.customerService.createCustomerGroup(payload).pipe(
          finalize(() => this.loadingService.hide()),
          catchError((errorCre) => of(new ApiErrorResponse<any>(errorCre)))
        );
      }),
      tapResponse(
        (resCre) => {
          if (resCre instanceof ApiErrorResponse) {
            this.messageService.error(resCre.message);
            return;
          }
          this.messageService.success(resCre.message);
          const brokerCodeCre = this.route.snapshot.queryParams['brokerId'];
          this.store.dispatch(getCustomerGroupsList({ brokerCode: brokerCodeCre }));
        },
        (err: any) => {}
      )
    )
  );

  readonly createCustomerGroup = this.effect<[ICusomterGroupPayload, string]>(($event) =>
    $event.pipe(
      tap(() => this.loadingService.show()),
      switchMap(([payload, oldGroupId]) => {
        const payloadCovert = {
          ...payload,
          members: [],
        };
        return this.customerService.createCustomerGroup(payloadCovert).pipe(
          switchMap((res) => {
            const toId = res.data.id;
            const fromId = oldGroupId;

            const payloadTransfer = {
              toId,
              fromId,
              accountNos: payload.members?.map((t) => t.accountNo) ?? [],
              brokerCode: payload.brokerCode ?? '',
            };

            return this.customerService.transferEachOfCustomer(payloadTransfer).pipe(
              finalize(() => this.loadingService.hide()),
              catchError((error) => of(new ApiErrorResponse<any>(error)))
            );
          }),
          finalize(() => this.loadingService.hide()),
          catchError((error) => of(new ApiErrorResponse<any>(error)))
        );
      }),
      tapResponse(
        (res) => {
          if (res instanceof ApiErrorResponse) {
            this.messageService.error(res.message);
            return;
          }
          this.messageService.success(res.message);
          const brokerCode = this.route.snapshot.queryParams['brokerId'];
          this.store.dispatch(getCustomerGroupsList({ brokerCode }));
        },
        (err: any) => {}
      )
    )
  );

  constructor(
    readonly loadingService: LoadingService,
    readonly messageService: MessageService,
    readonly store: Store,
    readonly customerService: CustomerService,
    readonly route: ActivatedRoute
  ) {
    super({
      customerList: [],
    });
  }
}
