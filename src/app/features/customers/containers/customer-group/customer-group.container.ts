import { Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { GridContextMenu, GridRowSelectionFormatter, IColumnConfig } from '@shared/models';
import { catchError, combineLatest, distinctUntilChanged, finalize, map, of, take, takeUntil } from 'rxjs';
import { DestroyService, I18nService, LoadingService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { DropdownCheckBoxComponent } from 'src/app/shared/components/dropdown-checkbox/dropdown-checkbox.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { CustomerGroupFilterComponent } from '../../components/customer-group-filter/customer-group-filter.component';
import {
  ICusomterGroup,
  ICusomterGroupExtends,
  ICusomterGroupPayload,
  IMembersPayloadCreateCustomerGroup,
  TotalTags,
} from '../../model/customer';
import { Store } from '@ngrx/store';
import { selectCustomerGroupInfo$, selectSearchValue$ } from '../../stores/customer.selector';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { GroupCustomerStore } from './customer-group-store';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectCurrentBrokerView$,
  selectCustomerGroupList$,
  selectFilterCustomerGroup$,
} from 'src/app/stores/shared/shared.selectors';
import { Router } from '@angular/router';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import {
  getCustomerGroupsList,
  getListAccountNumberAndLevelByBrokerView,
  resetFilterCustomerGroup,
  searchCustomerGroup,
  setFilterCustomerGroup,
} from 'src/app/stores/shared/shared.actions';
import { CreateCustomerGroupQuicklyComponent } from '../../components/create-customer-group-quickly/create-customer-group-quickly.component';
import { CUSTOMERS_NO_GROUP_ID, CUSTOMERS_NO_GROUP_NAME } from '../../constants/customers';
import { IAllAccountNumber, IAllLevelOfBroker, IFilterParamCustomerGroup } from 'src/app/shared/models/global';
import { AssetsService } from 'src/app/features/assets/services/assets.service';
import { DEFAULT_ROW_BACKGROUND_COLOR } from 'src/app/shared/constants/config';
import { getCustomerGroupInfo } from '../../stores/customer.action';

interface IFilterPopup {
  optionFilter: IFilterDataPopup;
  type: string;
}

interface IFilterDataPopup {
  isFilter: boolean;
  accountNumber: string[];
}

const LOADMORE_VOLUMN = 10;

/**
 * Nhóm khách hàng
 */
@Component({
  selector: 'app-customer-group',
  templateUrl: './customer-group.container.html',
  styleUrl: './customer-group.container.scss',
  providers: [GroupCustomerStore, DestroyService],
})
export class CustomerGroupContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('templateInfo', { static: true }) customerGroup: TemplateRef<any> | null = null;

  @ViewChild('contextMenuRef', { static: true }) contextMenuRef: TemplateRef<any> | null = null;

  @ViewChild('policyTemplate', { static: true }) policyTemplate: TemplateRef<any> | null = null;

  isLoading = true;

  showCollapse: boolean = true;
  filterOptions!: IFilterParamCustomerGroup;

  customerList: any[] = [];

  isValidName = false;

  messageText = '';

  idSelectElement = '';

  rowSelectionFormatter: GridRowSelectionFormatter = {
    hideCheckbox: (row: any, index: number) => row?.customerGroup?.name === CUSTOMERS_NO_GROUP_NAME,
  };

  totalTags: TotalTags = {
    totalAccount: 0,
    totalGroup: 0,
  };

  initialTotalTags: { totalAccount: number; totalGroup: number } = {
    totalAccount: 0,
    totalGroup: 0,
  };

  LIST_MG: IListOptions[] = [];

  brokerCode = '';

  index = 0;

  initialCustomers: IAllAccountNumber[] = [];

  isFirstTimeLoadPage = true;

  brokerCodeIds: string[] = [];

  assetInfoMap: Map<string, any> = new Map();

  statusRowExpandMap: Map<string, string> = new Map();

  isLoadingData = false;

  allGroupCustomer: ICusomterGroup[] = [];

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   * @param popoverService PopoverService
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly popoverService: PopoverService,
    private readonly customerGroupStore: GroupCustomerStore,
    private readonly router: Router,
    private readonly i18n: I18nService,
    private readonly assetService: AssetsService,
    private readonly loadingService: LoadingService
  ) {
    super();

    this.brokerInfo();
    this.toggleButtonByTags([
      ActionButton.broker,
      // ActionButton.edit,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);

    this.store
      .select(selectAllBrokerLevelListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((brokers) => {
        const brokerId = this.route.snapshot.queryParams['brokerId'];
        const indexBroker = brokers.findIndex((broker) => broker.brokerCode === brokerId);
        this.brokerCodeIds = [brokerId, ...this.getBrokerByParentBrokerId(brokers[indexBroker]?.children)];
      });
  }

  /**
   * The ngOnInit
   */
  ngOnInit(): void {
    // Template
    const cellTemplate = this.customerGroup;

    this.columnConfigs = [
      {
        name: 'Tên nhóm khách hàng',
        minWidth: 200,
        width: 300,
        tag: 'customerGroup',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        cellTemplate,
        isEdit: false,
        pinned: 'left',
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 200,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Tài sản ròng (NAV)',
        minWidth: 30,
        width: 160,
        tag: 'netAsset',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (v === 0) return '-';
          if (!v) return '';
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'Tổng giá trị CK',
        minWidth: 30,
        width: 160,
        tag: 'totalValueCK',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (v === 0) return '-';
          if (!v) return '';
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'Tổng tiền',
        minWidth: 30,
        width: 160,
        tag: 'total',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (v === 0) return '-';
          if (!v) return '';
          return customNumberFormat(v);
        },
        align: 'end',
      },
      // {
      //   name: 'Tỉ lệ ký quỹ',
      //   minWidth: 30,
      //   width: 130,
      //   tag: 'proportion',
      //   isDisplay: true,
      //   resizable: true,
      //   displayValueFn: (v: number) => {
      //     if (v === 0) return '-';
      //     if (!v) return '';
      //     return customNumberFormat(v * 100, 'percent');
      //   },
      //   dynamicClass: (v) => {
      //     if (!v) {
      //       return '';
      //     } else return 'fee-cls base-transaction-fee';
      //   },
      //   align: 'center',
      // },
      {
        name: 'Tổng dư nợ',
        minWidth: 30,
        width: 160,
        tag: 'totalDebt',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (+v === 0) return '-';
          if (!v) return '';
          return customNumberFormat(v);
        },
        align: 'end',
      },
      // {
      //   name: 'Chính sách 00',
      //   minWidth: 30,
      //   width: 240,
      //   tag: 'policy00',
      //   isDisplay: true,
      //   resizable: true,
      //   cellTemplate: this.policyTemplate,
      // },
      // {
      //   name: 'Chính sách 02',
      //   minWidth: 30,
      //   width: 240,
      //   tag: 'policy02',
      //   isDisplay: true,
      //   resizable: true,
      //   cellTemplate: this.policyTemplate,
      // },
      // {
      //   name: 'Chính sách 03',
      //   minWidth: 30,
      //   width: 240,
      //   tag: 'policy03',
      //   isDisplay: true,
      //   resizable: true,
      //   cellTemplate: this.policyTemplate,
      // },
      // {
      //   name: 'Chính sách 04',
      //   minWidth: 30,
      //   width: 240,
      //   tag: 'policy04',
      //   isDisplay: true,
      //   resizable: true,
      //   cellTemplate: this.policyTemplate,
      // },
      // {
      //   name: 'Chính sách 08',
      //   minWidth: 30,
      //   width: 240,
      //   tag: 'policy08',
      //   isDisplay: true,
      //   resizable: true,
      //   cellTemplate: this.policyTemplate,
      // },
      // {
      //   name: 'Chính sách 80',
      //   minWidth: 30,
      //   width: 240,
      //   tag: 'policy80',
      //   isDisplay: true,
      //   resizable: true,
      //   cellTemplate: this.policyTemplate,
      // },
    ];

    this.columnConfigsInitial = [...this.columnConfigs];

    combineLatest([this.store.select(selectCustomerGroupList$)])
      .pipe(distinctUntilChanged(), takeUntil(this._destroy))
      .subscribe(([customerGroups]) => {
        this.data = customerGroups
          .filter((g) => g.isActive)
          .map((groups) => {
            return {
              ...groups,
              customerGroup: {
                nameEdit: groups.name,
                groupCode: groups.groupCode,
                name: groups.groupCode ? `${groups.groupCode}: ${groups.name}` : groups.name,
                childrenCount: groups.members.length,
                groupId: groups.groupId,
              },
              id: groups.id,
              customerName: null,
              accountNumber: null,
              baseTransactionFee: null,
              derivativesTransactionFee: null,
              interests: null,
              interest1: null,
              interest2: null,
              interest3: null,
              interest4: null,
              netAsset: null,
              totalValueCK: null,
              total: null,
              proportion: null,
              policy00: null,
              policy02: null,
              policy03: null,
              policy04: null,
              policy08: null,
              policy80: null,
              children: groups.customers.map((child: any) => {
                return {
                  customerGroup: child.accountNo,
                  customerName: child.name,
                  // customerName: customers.find((c) => c.accountNumber === child.accountNumber)?.customerName ?? '',
                  accountNumber: child.accountNo,
                  indexChildrenExpanded: child.indexChildrenExpanded,
                  totalValueCK: 0,
                  total: 0,
                  derivativesTransactionFee: null,
                  proportion: 0,
                  baseTransactionFee: 0,
                  netAsset: null,
                  interest1: null,
                  interest2: null,
                  interest3: null,
                  interest4: null,
                  policy00: null,
                  policy02: null,
                  policy03: null,
                  policy04: null,
                  policy08: null,
                  policy80: null,
                  idParent: groups.id,
                  isInBroker: child.isInBroker ?? false,
                  id: `${groups.id}-${child.accountNo}`,
                };
              }),
            };
          });
        this.initialData = structuredClone(this.data);
        this.getTotalTags(this.initialData);

        this.checkStatusExpandRow();
        if (!this.data.some((d) => d.members.length)) return;
        this.loadingService.hide();
      });

    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((personalInfoList) => {
        this.customerList = personalInfoList.map((customer) => ({
          customerName: customer.customerName,
          accountNumber: customer.accountNumber,
          value: customer.brokerCode,
        }));
      });

    this.store
      .select(selectFilterCustomerGroup$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        if (!filter) {
          this.filterOptions = {
            accountNumbers: [],
            isFilter: false,
          };
          this.isFilter = false;
        } else {
          this.filterOptions = filter;
          this.isFilter = filter.isFilter;
        }

        if (this.grid) this.map.resetStateDropdownChildren();
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((search) => {
        if (search === null) return;
        this.isSearch = !!search;
        this.statusRowExpandMap.clear();
        this.map.resetExpandedRowAmountMap();
        this.store.dispatch(searchCustomerGroup({ search }));
        this.callCustomerGroup();
      });

    this.getAllCustomerGroup();
  }

  private getAllCustomerGroup() {
    this.store
      .select(selectCustomerGroupInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe(
        (res) =>
          (this.allGroupCustomer = [
            ...res,
            {
              id: CUSTOMERS_NO_GROUP_ID,
              customerGroup: {
                name: CUSTOMERS_NO_GROUP_NAME,
              },
            } as any,
          ])
      );
  }

  private checkStatusExpandRow() {
    if (this.statusRowExpandMap.size === 0) {
      this.loadingService.hide();
      // Automatically expand the first row with data in the grid
      // this.expandOneRowInGrid();
    } else {
      this.expandRowSelectedInMap();
    }
  }

  /**
   * Hàm mở row đầu tiên có dữ liệu khi vào màn
   * @returns
   */
  private expandOneRowInGrid() {
    const idx = this.data.findIndex((d) => d.members.length > 0);
    if (idx === -1) return;

    this.handleExpandRowData({ data: this.data[idx], index: idx });
  }

  /**
   * hàm mở lại những hàng đã mở được lưu trong map
   */
  private expandRowSelectedInMap() {
    const data = [...this.data];

    const reversedStatusRowExpandMap = new Map();
    for (const [originalKey, originalValue] of this.statusRowExpandMap) {
      reversedStatusRowExpandMap.set(originalValue, originalKey);
    }

    const idsSelected = Array.from(reversedStatusRowExpandMap.values());

    idsSelected.forEach((id, idx) => {
      const index = data.findIndex((d) => d.id === id);

      const dataMapBefore = this.statusRowExpandMap.get(id) ?? '1';

      const amountShowData = +dataMapBefore.split('_')[0];

      const endLength = amountShowData * LOADMORE_VOLUMN;

      const children = data[index]?.children;

      const endIndex = Math.min(endLength, children?.length);

      // const accountNumbers = children?.slice(0, endIndex).map((c: { accountNumber: string }) => c.accountNumber);

      const accountNumbers: string[] = [];

      const dataMap: Map<string, string> = new Map();
      children?.slice(0, endIndex)?.forEach((d: { accountNumber: string; id: string }) => {
        accountNumbers.push(d.accountNumber);
        dataMap.set(d.accountNumber, d.id);
      });

      this.divideToGetAssetInfo(index, accountNumbers, dataMap, idx === idsSelected.length - 1);
    });
  }

  private divideToGetAssetInfo(
    index: number,
    accountNumbers: string[],
    map: Map<string, string>,
    isWatitingPatchValue?: boolean
  ) {
    if (!accountNumbers) {
      this.loadingService.hide();
    }
    const accountDivider = this.splitArrayIntoChunks(accountNumbers);

    accountDivider?.forEach((accounts, idx) => {
      this.getDataAssetInfo(
        index,
        idx * accounts.length,
        idx * 20 + accounts.length + 1,
        accounts,
        map,
        idx !== accountDivider.length - 1 || !isWatitingPatchValue,
        true
      );
    });
  }

  /**
   * chia lại mảng để mỗi mảng có tối đa 20 phần tử
   * @param arr
   * @param minSize
   * @returns
   */
  private splitArrayIntoChunks(arr: string[], minSize: number = 20): string[][] {
    return arr?.reduce<string[][]>((result, current) => {
      let lastChunk: string[] | undefined = result[result.length - 1];

      if (!lastChunk || lastChunk.length >= minSize) {
        lastChunk = [];
        result.push(lastChunk);
      }

      lastChunk.push(current);

      return result;
    }, []);
  }

  /**
   * Hàm call asset info api lấy thông tin từng account number
   * @param index vị trí của item trong data
   * @param indexPosition vị trí bắt đầu show dữ liệu
   * @param indexSlice vị trị cắt dữ liệu
   * @param accountNumbers
   * @param data
   * @returns
   */
  private getDataAssetInfo(
    index: number,
    indexPosition: number,
    indexSlice: number,
    accountNumbers: string[],
    data: Map<string, string>,
    isWatitingPatchValue?: boolean,
    isPatchData?: boolean
  ) {
    const reversedMap = new Map();
    for (const [originalKey, originalValue] of data) {
      reversedMap.set(originalValue, originalKey);
    }
    const accountFilters = Array.from(reversedMap.values());
    const accIsFilter = accountFilters.filter((acc) => !this.assetInfoMap.has(acc));

    if (!accIsFilter.length) {
      if (isWatitingPatchValue) return;
      if (isPatchData) {
        this.updateAllRowExpanded();
        return;
      }
      this.patchAssetInfoInTable(index, indexSlice, indexPosition);
      this.loadingService.hide();
      return;
    }

    const accNumber = accIsFilter;

    const payload = {
      brokerCodes: this.brokerCodeIds,
      accountNumbers: accNumber.length ? accNumber : accountNumbers,
      filterAssetsInfo: null,
    };

    this.assetService
      .getAssetsInfo(payload)
      .pipe(
        take(1),
        map((assetData) => {
          assetData.forEach((asset) => {
            const assetInfo = {
              netAsset: asset?.nav ?? 0,
              totalValueCK: asset?.stock ?? 0,
              total: asset?.cash ?? 0,
              proportion: asset?.marginRate ?? 0,
              totalDebt: asset?.debt ?? 0,
            };
            this.assetInfoMap.set(asset.accountNumber, assetInfo);
          });
        }),
        catchError(() => {
          return of();
        }),
        finalize(() => {
          this.loadingService.hide();
          setTimeout(() => {
            if (isWatitingPatchValue) return;
            if (isPatchData) {
              this.updateAllRowExpanded();
              return;
            }
            this.patchAssetInfoInTable(index, indexSlice, indexPosition);
          });
        })
      )
      .subscribe();
  }

  /**
   * update lại các hàng đã  mở
   */
  private updateAllRowExpanded() {
    const data = [...this.data];

    const idxs: number[] = data
      .reduce((init, item, idx) => {
        if (this.statusRowExpandMap.has(item.id)) {
          init.push(idx);
        }

        return init;
      }, [] as number[])
      .sort((before: number, after: number) => before - after);

    let curIdx = 0;

    idxs.forEach((init, idx) => {
      const item = data[init].children;
      const id = data[init].id;

      const dataMapBefore = this.statusRowExpandMap.get(id) ?? '1';

      const amount = +dataMapBefore.split('_')[0];

      const endIndex = Math.min(item.length, amount * LOADMORE_VOLUMN);

      const dataSlice = item.slice(0, endIndex);

      const childrenData = dataSlice.map((d: any, i: number) => ({
        ...d,
        hasNextValue:
          data[init].children.length > LOADMORE_VOLUMN &&
          i + 1 >= dataSlice.length &&
          i + 1 !== data[init].children.length,
        isCallApiShowMore: true,
        indexChildrenExpanded: i,
        ...(this.assetInfoMap.has(d.accountNumber) && this.assetInfoMap.get(d.accountNumber)),
      }));

      // const indexToInsert = this.data.findIndex((d) => d.id === id);

      this.addItemsAt({ index: init + curIdx, items: childrenData, setItem: true });

      curIdx = curIdx + childrenData.length;
      // if (!init) {
      //   this.addItemsAt({ index: idx, items: childrenData, setItem: true });
      // } else {
      //   this.addItemsAt({ index: init + idx, items: childrenData, setItem: true });
      // }

      // return dataSlice.length + init;
    }, 0);
  }

  /**
   * Hàm patch dữ liệu vào table
   * @param idx
   * @param indexSlice
   * @param indexPostion
   */
  private patchAssetInfoInTable(idx: number, indexSlice: number, indexPostion: number) {
    const dataSlice = this.data[idx].children.slice(indexSlice - LOADMORE_VOLUMN, indexSlice);
    const chidlrenData = dataSlice.map((d: any, i: number) => ({
      ...d,
      hasNextValue:
        this.data[idx].children.length > LOADMORE_VOLUMN &&
        i + 1 === dataSlice.length &&
        indexSlice - LOADMORE_VOLUMN + i + 1 !== this.data[idx].children.length,
      isCallApiShowMore: true,
      indexChildrenExpanded: i,
      ...(this.assetInfoMap.has(d.accountNumber) && this.assetInfoMap.get(d.accountNumber)),
    }));
    this.updateDataView();
    this.addItemsAt({ index: indexPostion, items: chidlrenData, setItem: true });
    this.isLoadingData = false;
  }

  /**
   * Hàm click tải thêm dữ liệu
   * @param param0
   */
  getMoreData({ data }: { data: any }) {
    const dataStore = [...this.data];
    const indexPostion = dataStore.findIndex((d) => d.id === data.id);
    const item = { ...dataStore[indexPostion] };

    item.hasNextValue = false;
    item.isShowLoading = true;

    dataStore[indexPostion] = { ...item };
    this.data = [...dataStore];

    //>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

    const dataChildren = [...data.parent.children];
    const index = this.data.findIndex((d) => d.id === data.parent.id);
    const indexChildren = dataChildren.findIndex((d) => d.id === data.id);

    const indexSlice = indexChildren + 1;
    const accountNumbers = dataChildren
      .map((c: { accountNumber: string }) => c.accountNumber)
      .slice(indexSlice, indexSlice + LOADMORE_VOLUMN);
    const dataMap: Map<string, string> = new Map();
    dataChildren.slice(indexSlice, indexSlice + LOADMORE_VOLUMN).forEach((d: { accountNumber: string; id: string }) => {
      dataMap.set(d.accountNumber, d.id);
    });
    this.getDataAssetInfo(index, indexPostion, indexSlice + LOADMORE_VOLUMN, accountNumbers, dataMap);
    const id = data.parent.id;

    const dataBefore = this.statusRowExpandMap.get(id) ?? '';

    const amountExpandById = dataBefore.split('_')[0] ?? 0;

    const amountDataExpandById = `${+amountExpandById + 1}_${id}`;
    this.statusRowExpandMap.set(id, amountDataExpandById);
    this.map.addExpandedItems(data, accountNumbers.length);
  }

  private updateDataView() {
    this.data = this.data.map((t) => ({
      ...t,
      isShowLoading: false,
    }));
  }

  /**
   * Hàm hiện loading khi get thêm dữ liệu
   * @param idx
   */
  private showLoadingInTable(idx: number) {
    const dataStore = [...this.data];
    const item = { ...dataStore[idx] };
    item.isShowLoading = true;
    item.isExpanded = true;
    item.backgroundColor = DEFAULT_ROW_BACKGROUND_COLOR;

    dataStore[idx] = { ...item };

    this.data = [...dataStore];
  }

  handleExpandRowData({ data, index, isExpand }: { data: any; index: number; isExpand?: boolean }) {
    if (this.isLoadingData) return;
    if (!isExpand) {
      this.handleCollapseRowData({ data, index });
      return;
    }
    this.isLoadingData = true;
    const accountNo = data.children.slice(0, LOADMORE_VOLUMN).map((d: any) => d.accountNumber);
    const dataMap: Map<string, string> = new Map();
    data.children.slice(0, LOADMORE_VOLUMN).forEach((d: { accountNumber: string; id: string }) => {
      dataMap.set(d.accountNumber, d.id);
    });
    this.showLoadingInTable(index);
    this.getDataAssetInfo(index, index, LOADMORE_VOLUMN, accountNo, dataMap);

    this.statusRowExpandMap.set(data.id, `${1}_${data.id}`);
  }

  handleCollapseRowData({ data, index }: { data: any; index: number }) {
    this.statusRowExpandMap.delete(data.id);
    this.removeItemsAt(index);
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterCustomerGroup());
    this.resetMapStoreStatusRowInGrid();
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
    this.cdf.detectChanges();
  }

  /**
   *
   */
  getTotalTags(data: ICusomterGroupExtends[]) {
    const uniqueAccountNumbers = [
      ...new Set(
        data.filter((item) => item.customers).flatMap((item) => item.customers.map((customer) => customer.accountNo))
      ),
    ];
    const uniqueGroup = [...new Set(data.flatMap((item) => item.name))];

    this.totalTags = {
      totalAccount: uniqueAccountNumbers?.length ?? 0,
      totalGroup: uniqueGroup?.length ?? 0,
    };
  }

  /**
   * Thông tin của broker
   */
  brokerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBroker) => {
        if (!currentBroker) return;
        this.rowSelected = [];
        this.brokerCode = currentBroker.brokerCode;

        combineLatest([this.store.select(selectInfoUserLogin$)])
          .pipe(takeUntil(this._destroy))
          .subscribe(([userList]) => {
            if (!userList) return;

            const subBroker = userList.map((broker) => {
              return {
                brokerCode: `${broker.brokerCode}`,
                name: `${broker.brokerCode} : ${broker.brokerName}`,
                isSelect: broker.brokerCode === currentBroker.brokerCode,
              };
            });

            this.LIST_MG = [...subBroker];
            const broker = this.LIST_MG.find((t) => t.isSelect);

            if (broker) {
              const brokerCode = this.route.snapshot.queryParams['brokerId'];

              if (brokerCode !== broker['brokerCode']) {
                this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode }));
                this.router.navigate([], {
                  queryParams: { brokerId: broker['brokerCode'] },
                  queryParamsHandling: 'merge',
                });
              }

              this.router.navigate([], {
                queryParams: { brokerId: broker['brokerCode'] },
                queryParamsHandling: 'merge',
              });

              this.store.dispatch(getCustomerGroupInfo({ brokerId: broker['brokerCode'] as string }));

              this.findButtonByTags(ActionButton.broker).label = broker['name'] ?? '';
            }
          });
      });
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * containsSearchValue
   * @param child
   * @param searchValue
   */
  containsSearchValue(child: any, searchValue: string): boolean {
    return (
      child.accountNumber.toString().toLowerCase().includes(searchValue) ??
      child.customerName.toString().toLowerCase().includes(searchValue)
    );
  }

  /**
   * expandItems
   * @param data
   */
  expandItems(data: any) {
    return data.reduce((acc: any[], d: any) => {
      const rowData = {
        ...d,
        isExpanded: this.map.hasStateDropdownChildren(d.id),
      };

      acc.push(rowData);

      if (rowData.isExpanded && rowData.children?.length) {
        const childrenRow = rowData.children.map((child: any) => ({
          ...child,
          parent: rowData,
          backgroundColor: '#f6f6f6',
          isExpanded: false,
        }));

        acc.push(...childrenRow);
      }

      return acc;
    }, []);
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'broker':
        this.changeViewBroker();
        break;
      case 'filter':
        {
          const ref = this.openFilter(CustomerGroupFilterComponent, {
            width: '400px',
            data: this.filterOptions,
          });
          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe((v: IFilterPopup) => {
              if (!v) return;
              this.applyFilter(v);
            });
        }

        break;
      case 'edit':
        this.toggleEditMode();
        this.cancelEditModeExited();
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate(this.updateInitialDataView.bind(this));
        break;
      default:
        break;
    }
  }

  applyFilter(filter: IFilterPopup) {
    const isFilterStore = this.isFilter;
    const { optionFilter, type } = filter;

    if (type === 'save' && optionFilter) {
      const { isFilter, accountNumber } = optionFilter;
      const params = {
        accountNumbers: accountNumber,
        isFilter: isFilter,
      };
      this.store.dispatch(setFilterCustomerGroup({ data: params }));

      if (isFilter || isFilter !== isFilterStore) {
        this.resetMapStoreStatusRowInGrid();
        this.callCustomerGroup();
      }
    } else if (type === 'default') {
      this.store.dispatch(resetFilterCustomerGroup());
      this.map.resetExpandedRowAmountMap();

      if (isFilterStore) {
        this.resetMapStoreStatusRowInGrid();

        this.callCustomerGroup();
      }
    }
  }

  private callCustomerGroup() {
    this.loadingService.show();
    const brokerCode = this.route.snapshot.queryParams['brokerId'];
    this.store.dispatch(getCustomerGroupsList({ brokerCode, isPrimaryBrokerCode: true }));
  }

  /**
   * CancelEditModeExited
   */
  cancelEditModeExited() {
    this.data.forEach((t) => {
      t.checked = false;
      t.isChange = false;
      t.isEdit = false;
    });
    this.data = structuredClone(this.initialData);
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([
      ActionButton.broker,
      ActionButton.edit,
      ActionButton.save,
      ActionButton.cancel,
      ActionButton.export,
    ]);
  }

  /**
   * GetClassOfGroup
   * @param groupName - group.name
   * @returns {string} - class
   */
  getClassOfGroup(groupName: string) {
    if (groupName === CUSTOMERS_NO_GROUP_NAME) {
      return 'no-group';
    } else return 'group';
  }

  /**
   * UpdateInitialDataView
   * hàm update cho giá trị inital value
   */
  updateInitialDataView() {
    const dataExpand = this.data.filter((d) => d.isExpanded);
    const newData = structuredClone(this.data);
    dataExpand.forEach((d) => {
      const index = newData.findIndex((e) => e.id === d.id);
      this.flattenChildrenItems(newData, index + 1, newData[index]?.children?.length);
      this.flattenChildrenItems(newData, index + 1, 0, d.children);
    });
    this.initialData = structuredClone(newData);
  }

  /**
   * EditGroupCustomer
   * chuyển edit mode khi sửa name
   * @param {any} data data
   * @param {any} element element
   * @param {string} tag string
   */
  editGroupCustomer(data: any, element: any, tag: string) {
    this.data.forEach((e) => (e.isEdit = false));
    element.isEdit = true;
  }

  /**
   * CheckTagChanged
   * @param {any} element element
   * @param {string} tag string
   * @returns {boolean} true/fasle
   */
  checkTagChanged(element: any, tag: string): boolean {
    const isTag = element?.tagChange?.find((t: string) => t === tag);
    return !!isTag;
  }

  /**
   * UpdateDataToggle
   * @param {any} source source
   */
  updateDataToggle(source: any) {
    this.data = structuredClone(source);
  }

  /**
   * ChangeNameGroup
   * thay đổi tên group và kiểm tra validation của input
   * @param {Event} $event  event
   * @param {any} element element
   * @param {string} tag string
   * @param {IColumnConfig} column column
   */
  changeNameGroup($event: Event, element: any, tag: string, column: IColumnConfig) {
    const { value } = $event.target as HTMLInputElement;
    element.isEdit = false;
    const valueFinal = `${element.groupCode}: ${value}`;
    if (!value) {
      element.checked = false;
      this.messageText = 'MES-151';
      element.isChange = true;
      this.isValidName = true;
    } else if (valueFinal !== element[tag]) {
      const isSameValue = this.data.find(
        (d) => d.customerGroup?.nameEdit && d.customerGroup.nameEdit.toLowerCase() === value.toLowerCase()
      );
      if (isSameValue) {
        this.isValidName = true;
        element.checked = false;
        this.messageText = 'MES-150';
      } else {
        element.checked = true;
        this.messageText = '';
        this.isValidName = false;
      }
      element.checked = true;
      element.isChange = true;
    } else {
      element.isChange = false;
      this.messageText = '';
      element.checked = true;
    }
    if (!this.isValidName) {
      element[tag] = {
        ...element[tag],
        name: valueFinal ?? ' ',
        nameEdit: value,
      };
    }
    const isExitTag = element?.tagChange?.find((t: string) => t === column.tag);
    if (!isExitTag) {
      element.tagChange = [...(element.tagChange ?? []), column.tag];
    }
  }

  /**
   * SaveStatusEditor
   * @param element
   */
  saveStatusEditor(element: any) {
    if (this.isValidName) {
      return;
    }
    element.checked = false;
    element.isChange = false;
    element.isEdit = false;
    element.tagChange = element.tagChange?.filter((t: string) => t !== element.tag);
    const elementInitial = this.data.find((t) => t.id === element.id);
    const indexElementSelected = this.initialData.findIndex((t) => t.id === element.id);
    const newData = structuredClone(this.initialData);
    this.flattenChildrenItems(newData, indexElementSelected, 1, [elementInitial]);
    this.initialData = [...newData];
    const name = element[element.tagChange].nameEdit;
    const brokerCode = this.route.snapshot.queryParams['brokerId'] ?? this.brokerCode;
    this.customerGroupStore.editNameCustomerGroup({ name, id: element.id, brokerCode });
  }

  /**
   * CancelStatusEditor
   * @param element
   */
  cancelStatusEditor(element: any) {
    element.checked = false;
    element.isEdit = false;
    element.isChange = false;
    element.tagChange = element.tagChange?.filter((t: string) => t !== element.tag);
    this.isValidName = false;
    const elementInitial = this.initialData.find((t) => t.id === element.id);
    const indexElementSelected = this.data.findIndex((t) => t.id === element.id);
    const newData = structuredClone(this.data);
    this.flattenChildrenItems(newData, indexElementSelected, 1, [elementInitial]);
    this.data = [...newData];
  }

  /**
   * AddCustomer
   * thêm khách hàng vào nhóm
   * @param {Event} event event
   * @param {any} element element
   */
  addCustomer(event: Event, element: any) {
    this.idSelectElement = element.id;
    const origin = event.target as HTMLElement;
    const customers = this.getListCustomer(element.children);
    const ref = this.popoverService.open<any>({
      origin,
      data: customers,
      content: DropdownCheckBoxComponent,
      componentConfig: {
        listFilterOptions: customers,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe({
      next: (v) => {
        if (!v.data?.length) return;
        const message = this.i18n.translate('MES-621', { groupName: element.name ?? '' });
        const refConfirm = this.openConfirm(message);
        refConfirm.pipe(take(1)).subscribe((res) => {
          if (res !== 'save') return;
          this.idSelectElement = '';
          const payload = {
            members: v.data.map((c: any, index: number) => ({
              name: c.customerName ?? '',
              accountNo: c.accountNumber ?? '',
            })),
            groupId: element.id,
            brokerCode: this.route.snapshot.queryParams['brokerId'] ?? this.brokerCode,
          };
          this.customerGroupStore.addCustomerInGroup(payload);
          this.map.stateDropdownChildren.clear();
        });
      },
    });
  }

  /**
   * MoveGroupCustomer
   * chuyển nhóm khách hàng hiện tại sang nhóm khách hàng khác
   * @param {Event} event event
   * @param {any} element element
   */
  moveGroupCustomer(event: Event, element: any) {
    this.idSelectElement = element.id;
    const customer = element.customers.map((c: any) => ({
      name: c.name,
      accountNo: c.accountNumber ?? c.accountNo,
    }));

    const origin = event.target as HTMLElement;
    const options = this.data.filter((d) => d.children && d.customerGroup.name !== CUSTOMERS_NO_GROUP_NAME);
    const ref = this.popoverService.open<any>({
      origin,
      data: this.customerList,
      content: SearchListComponent,
      componentConfig: {
        multiple: false,
        isSearch: true,
        searchKey: 'name',
        value: [element?.customerGroup.name],
        headerText: 'MES-148',
        displayOptionFn: (v: any) => v.name,
        isShowBtn: true,
        isClose: false,
        isHasGroupButtons: true,
        isScrollList: true,
        addItemButtonEvent: () => this.openCustomerGroupPopup(customer, element.id),
        options: options
          .filter((t) => t.customerGroup.name !== 'CHƯA CÓ NHÓM')
          .map((d) => ({
            name: d.customerGroup.name,
            value: d.id,
            // classCustom: d.customerGroup.name === 'CHƯA CÓ NHÓM' ? 'option-color-brand-600-cls' : '',
          })),
      },
    });
    ref.afterClosed$.pipe(take(1)).subscribe({
      next: (v) => {
        if (!v.data) return;
        const { item } = v.data;
        if (item[0].name === element?.customerGroup.name) return;

        const message = this.i18n.translate('MES-622', { groupName: item[0].name });
        const refConfirm = this.openConfirm(message);
        refConfirm.pipe(take(1)).subscribe((res) => {
          if (res !== 'save') return;
          const brokerCode = this.route.snapshot.queryParams['brokerId'] ?? this.brokerCode;
          if (element.id === CUSTOMERS_NO_GROUP_ID) {
            const payload = {
              groupId: item[0].value,
              brokerCode,
              members: element.customers.map((item: any) => ({
                name: item.name,
                accountNo: item.accountNo,
              })),
            };

            this.customerGroupStore.addCustomerInGroup(payload);
          } else {
            const payload = {
              fromId: element.id,
              toId: item[0].value,
              accountNos: element.customers.map((item: any) => item.accountNumber),
              brokerCode,
            };
            this.customerGroupStore.transferEachOfCustomer(payload);
          }
          this.map.stateDropdownChildren.clear();

          // API
          const rowSelectedFilter = this.rowSelected.filter((t: any) => element.id !== t.id);
          this.rowSelected = rowSelectedFilter;
        });
      },
    });
  }

  /**
   * GetListCustomer
   * @param {any[]} children children
   * @returns {any[]} new data
   */
  getListCustomer(children: any[]) {
    const accountNumbers = new Set(children.map((item) => item.accountNumber));
    return this.customerList.filter((item) => !accountNumbers.has(item.accountNumber));
  }

  /**
   * DeleteGroup
   * @param {any} element element
   */
  deleteGroup(element: any) {
    this.idSelectElement = element.id;

    const ref = this.dialogService.openPopUp(DeleteNotificationComponent, {
      data: {
        isActive: true,
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-155',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
        isHideMessage: true,
      },
      height: '210px',
      width: '340px',
      panelClass: ['popup-confirm', 'not-padding-popup'],
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe({
        next: (v) => {
          this.idSelectElement = '';

          if (v !== 'save') return;

          const payload = {
            brokerCode: (this.route.snapshot.queryParams['borkerId'] as string) ?? this.brokerCode,
            ids: [element.id as string],
          };
          this.customerGroupStore.deleteCustomerGroup(payload);
          const x = this.rowSelected.filter((t: any) => element.id !== t.id);
          this.rowSelected = x;
        },
      });
  }

  /**
   * ContextMenu
   * @param {{event: MouseEvent, element: any}} param0 {event, element}
   * @returns {void}
   */
  contextMenu({ event, element, source }: GridContextMenu<any>) {
    if (this.onEditMode) return;
    event.preventDefault();

    const isGroupElement = element.children ?? element?.customerGroup?.name;
    let options = isGroupElement
      ? [
          {
            url: './assets/icons/clear-icon.svg',
            label: 'MES-630',
            tag: 'delete-group',
          },
        ]
      : [
          {
            url: './assets/icons/repeat.svg',
            label: 'MES-148',
            tag: 'change',
          },
          {
            url: './assets/icons/clear-icon.svg',
            label: 'MES-153',
            tag: 'delete',
          },
        ];

    if (!isGroupElement) {
      const parentElement = this.data.find((d) => d.id === element.idParent);

      if (parentElement.customerGroup.name === CUSTOMERS_NO_GROUP_NAME) options.splice(1, 1);
    }
    const isNotTransferCustomer = this.rowSelected.filter((t: any) => t.level !== 0).some((t: any) => !t.isInBroker);
    if (!isGroupElement && isNotTransferCustomer) {
      options = options.map((o) => ({
        ...o,
        ...(o.tag === 'change' ? { disabled: true, classCustom: 'disable' } : {}),
      }));
    }
    const origin = event.target as HTMLElement;
    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 3,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        templateRefExp: this.contextMenuRef,
        searchKey: 'tag',
        options,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe({
      next: (v) => {
        if (!v.data) return;
        const { item } = v.data;
        if (!item.length) return;
        const { tag } = item[0];
        if (tag === 'change') {
          if (isNotTransferCustomer) return;
          this.transferCustomerToGroup(origin, element, source);
        } else if (tag === 'delete') {
          this.removeCustomerFromGroup(element, source);
        } else {
          this.removeGroups();
        }
      },
    });
  }

  /**
   * TransferCustomerToGroup
   * @param {HTMLElement} origin
   * @param {any} element
   * @param {any[]} source
   */
  transferCustomerToGroup(origin: HTMLElement, element: any, source: any[]) {
    // const options = this.data.filter((d) => d.children && d.customerGroup.name !== CUSTOMERS_NO_GROUP_NAME);
    // const parentElement = this.data.find((d) => d.id === element.idParent);

    const options = this.allGroupCustomer
      .filter((d) => d.id !== CUSTOMERS_NO_GROUP_ID)
      .map((d) => ({
        ...d,
        customerGroup: {
          name: `${d.groupCode}: ${d.name}`,
        },
      }));
    const parentElement = options.find((d) => d.id === element.idParent);
    const members: IMembersPayloadCreateCustomerGroup[] = [];
    const accountNos: string[] = [];
    const setTranferedIds = new Set<string>();
    // Transfer customers in selected group
    this.rowSelected.forEach((row: any) => {
      if (row.idParent === element.idParent) {
        members.push({ accountNo: row.accountNumber, name: row.customerName });
        accountNos.push(row.accountNumber);
        setTranferedIds.add(row.id);
      }
    });
    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 3,
      componentConfig: {
        multiple: false,
        isSearch: true,
        searchKey: 'name',
        value: [parentElement?.customerGroup.name],
        headerText: 'MES-148',
        displayOptionFn: (v: any) => v.name,
        isShowBtn: true,
        isClose: false,
        isHasGroupButtons: true,
        isScrollList: true,
        addItemButtonEvent: () => this.openCustomerGroupPopup(members, parentElement!.id, setTranferedIds),
        options: options
          .filter((t) => t.customerGroup.name !== 'CHƯA CÓ NHÓM')
          .map((d) => ({
            name: d.customerGroup.name,
            value: d.id,
            // classCustom: d.customerGroup.name === 'CHƯA CÓ NHÓM' ? 'option-color-brand-600-cls' : '',
          })),
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe({
      next: (v) => {
        if (!v.data) return;
        const { item } = v.data;
        if (item[0].name === parentElement?.customerGroup?.name) return;

        const message = this.i18n.translate('MES-623', { groupName: item[0].name });
        const refConfirm = this.openConfirm(message);

        refConfirm.pipe(take(1)).subscribe((res) => {
          if (res !== 'save') return;

          // Call API
          // if transfer customes from group of customers without group
          const brokerCode = this.route.snapshot.queryParams['brokerId'] ?? this.brokerCode;

          if (element.idParent === CUSTOMERS_NO_GROUP_ID) {
            const payload = {
              groupId: item[0].value,
              brokerCode,
              members: members.map((item: any) => ({
                name: item.name,
                accountNo: item.accountNo,
              })),
            };

            this.customerGroupStore.addCustomerInGroup(payload);
          } else {
            const payload = {
              fromId: element.idParent,
              toId: item[0].value,
              accountNos,
              brokerCode: this.route.snapshot.queryParams['brokerId'] ?? this.brokerCode,
            };
            this.customerGroupStore.transferEachOfCustomer(payload);
          }
          this.map.stateDropdownChildren.clear();

          this.rowSelected = this.rowSelected.filter(
            (row: any) => !setTranferedIds.has(row.id) && row.id !== parentElement!.id
          );
        });
      },
    });
  }

  /**
   * RemoveCustomerFromGroup
   * @param {any} element
   * @param {any[]} source
   */
  removeCustomerFromGroup(element: any, source: any[]) {
    const parentElement = this.data.find((d) => d.id === element.idParent);
    const ref = this.dialogService.openPopUp(DeleteNotificationComponent, {
      data: {
        isActive: true,
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-152',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
        isHideMessage: true,
      },
      height: '210px',
      width: '340px',
      panelClass: ['popup-confirm', 'not-padding-popup'],
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe({
        next: (v) => {
          if (!v) return;

          const setRemovedIds = new Set<string>();
          const accountNos: string[] = [];

          // Delete customer in selected group
          this.rowSelected.forEach((r: any) => {
            if (r.idParent === parentElement.id) {
              setRemovedIds.add(r.id);
              accountNos.push(r.accountNumber);
            }
          });
          // API

          const payload = {
            groupId: parentElement.id,
            brokerCode: this.route.snapshot.queryParams['brokerId'] ?? this.brokerCode,
            accountNos,
          };

          this.customerGroupStore.deleteCustomerInGroup(payload);
          this.map.stateDropdownChildren.clear();

          // Bỏ chọn những khách hàng đã xóa
          this.rowSelected = this.rowSelected.filter((row: any) => {
            return !setRemovedIds.has(row.id) && row.id !== parentElement.id;
          });
        },
      });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    const queryParamsGC = this.route.snapshot.queryParams;

    const elementRef = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidth = elementRef.nativeElement as HTMLElement;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRef as any,
      width: elementWidth.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBrokerGc = userList.find((user) => user.brokerCode === itemSelected['brokerCode']);

          const subBroker = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelected['brokerCode']);

          if (queryParamsGC['brokerId'] === itemSelected['brokerCode']) return;
          this.data = [];
          this.initialData = [];

          if (subBroker && !currentBrokerGc) {
            const brokerCode = subBroker['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (subBroker && currentBrokerGc) {
            this.store.dispatch(
              getListAccountNumberAndLevelByBrokerView({
                brokerCode: (subBroker['brokerCode'] as string) ?? this.brokerCode,
                isPrimaryBrokerCode: true,
              })
            );
          }

          if (subBroker) {
            this.LIST_MG = this.LIST_MG.map((brokerGc) => ({
              ...brokerGc,
              isSelect: brokerGc['brokerCode'] === subBroker['brokerCode'],
            }));

            this.findButtonByTags(ActionButton.broker).label = subBroker['name'] ?? '';
          }
          this.map.resetStateDropdownChildren();
          if (this.isFilter || this.isSearch) this.store.dispatch(resetFilterCustomerGroup());
          else {
            this.isFirstTimeLoadPage = true;
          }

          this.resetMapStoreStatusRowInGrid();
          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));

          let brokerId: string | null;
          if (currentBrokerGc) {
            brokerId = currentBrokerGc.brokerCode;
          } else if (subBroker) {
            brokerId = subBroker['brokerCode'] as string;
          } else {
            brokerId = null;
          }

          this.router.navigate([], {
            queryParams: {
              ...queryParamsGC,
              brokerId,
            },
          });
        });
    });
  }

  private resetMapStoreStatusRowInGrid() {
    this.statusRowExpandMap.clear();
    this.assetInfoMap.clear();
  }

  openConfirm(message: string) {
    const refConfirm = this.dialogService.openPopUp(DeleteNotificationComponent, {
      data: {
        isActive: true,
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: message,
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
        isHideMessage: true,
      },
      height: 'fit-content',
      width: '360px',
      panelClass: ['popup-confirm', 'not-padding-popup'],
    });

    return refConfirm.afterClosed();
  }

  openCustomerGroupPopup(
    members: IMembersPayloadCreateCustomerGroup[],
    oldGroupId: string,
    setTranferedIds?: Set<string>
  ) {
    const ref = this.dialogService.openPopUp(CreateCustomerGroupQuicklyComponent, {
      width: '340px',
      height: 'fit-content',
      panelClass: ['popup-confirm', 'not-padding-popup'],
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((res) => {
        if (!res) return;
        if (!res.name) return;
        const { name } = res;
        const payload: ICusomterGroupPayload = {
          name,
          brokerCode: this.brokerCode,
          members,
        };
        if (oldGroupId === CUSTOMERS_NO_GROUP_ID) {
          this.customerGroupStore.createCustomerGroupWithNoId(payload);
        } else {
          this.customerGroupStore.createCustomerGroup([payload, oldGroupId]);
        }
        this.map.stateDropdownChildren.clear();

        if (setTranferedIds) {
          this.rowSelected = this.rowSelected.filter(
            (row: any) => !setTranferedIds.has(row.id) && row.id !== oldGroupId
          );
        }
      });
  }

  removeGroups() {
    const ref = this.dialogService.openPopUp(DeleteNotificationComponent, {
      data: {
        isActive: true,
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-155',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
        isHideMessage: true,
      },
      height: 'fit-content',
      width: '340px',
      panelClass: ['popup-confirm', 'not-padding-popup'],
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe({
        next: (v) => {
          if (!v) return;
          const setRemovedIds = new Set<string>();
          this.rowSelected.forEach((row: any) => {
            if (row.children?.length || row.customerGroup?.name) setRemovedIds.add(row?.id);
          });

          const ids = Array.from(setRemovedIds).filter((id) => id !== CUSTOMERS_NO_GROUP_ID);
          const payload = {
            ids,
            brokerCode: (this.route.snapshot.queryParams['brokerId'] as string) ?? this.brokerCode,
          };

          this.customerGroupStore.deleteCustomerGroup(payload);

          // bỏ chọn những nhóm đã xóa

          this.rowSelected = this.rowSelected.filter(
            (row: any) => !(setRemovedIds.has(row?.idParent) || setRemovedIds.has(row.id))
          );
        },
      });
  }

  /**
   * Tính lại tag tổng
   * @param {number} totalAccount Tổng số tài khoản
   * @param {number} totalGroup Tổng nhóm
   */
  countTotalTags(totalAccount: number, totalGroup: number) {
    this.totalTags = {
      totalAccount: totalAccount,
      totalGroup: totalGroup,
    };
  }

  getBrokerByParentBrokerId = (brokerObject: IAllLevelOfBroker[]) => {
    let brokerCodes: string[] = [];
    brokerObject?.forEach((broker) => {
      brokerCodes.push(broker.brokerCode);

      if (Array.isArray(broker.children) && broker.children.length > 0) {
        brokerCodes = brokerCodes.concat(this.getBrokerByParentBrokerId(broker.children));
      }
    });
    return brokerCodes;
  };

  keydownEvent() {
    console.log('keydown');
  }
}
