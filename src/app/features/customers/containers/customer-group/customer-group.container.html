<div class="customer-group-container">
  <div class="header-customer-group">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-13' | translate}}</div>
      <div class="number-info-cls">
        <!-- Tông tài khoản -->
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{totalTags.totalAccount}} {{"MES-15" | translate}}
        </div>
        <!-- Tổng nhóm -->
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{totalTags['totalGroup']}} {{ "MES-567" | translate}}
        </div>
        <div *ngIf="onEditMode" class="text-edit-cls">{{'MES-156' | translate}}: {{getCountDataEdited()}}</div>
      </div>


    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [listColumnInitial]="columnConfigsInitial"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      class="table-custom-cls"
      [columnConfigs]="columnConfigs"
      [data]="data"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      [multiSelectable]="true"
      [hideRowSelectionCheckbox]="false"
      [rowSelectable]="true"
      [rowSelected]="rowSelected"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      (contextMenu)="contextMenu($event)"
      (eventEmitData)="updateDataToggle($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
      (clickShowMore)="clickShowMore($event)"
      (rowSelectedChange)="selectRows($event)"
      [rowSelectionFormatter]="rowSelectionFormatter"
      (expandRowEvent)="handleExpandRowData($event)"
      (showMoreEvent)="getMoreData($event)"
      [isLoading]="isLoading"
      [class.isNotLoadingData]="isLoadingData"
    >
    </sha-grid>
  </div>
</div>

<ng-template #templateInfo let-group="templateInfo" let-element="element" let-tag="tag" let-column="column">
  @if(!!group?.name) {
  <div
    class="group-name-wrapper"
    [ngClass]="{
    'invalid-mode-name-box' : isValidName && checkTagChanged(element, tag)
  }"
  >
    <div
      *ngIf="(!element.isEdit && !element.isChange)  ||  (!element.isEdit && !checkTagChanged(element, tag))"
      class="group-name typo-body-12"
      [ngClass]="getClassOfGroup(group.name)"
    >
      {{group.groupId ? (group.groupId + ': ') : ''}} {{group.name}} ({{group.childrenCount}})
    </div>

    <div
      class="group-name"
      *ngIf="( element.isEdit) || (element.isChange  && checkTagChanged(element, tag))"
      [matTooltip]="(isValidName && checkTagChanged(element, tag) ? messageText : '') | translate"
      [matTooltipDisabled]="!(isValidName && checkTagChanged(element, tag))"
      matTooltipPosition="above"
    >
      <input
        [readOnly]="!element.isEdit"
        type="text"
        [value]="group.nameEdit"
        class="input-style-common input-cls-custom typo-body-12"
        (change)="changeNameGroup($event, element, tag, column)"
      />
    </div>
    <!-- Click Sửa show icon -->

    @if(!onEditMode){
    <div class="icons-conatiner" [class.background-expanded-cls]="element?.isExpanded">
      <div class="icons-box">
        <div
          class="icons-wrapper"
          [ngClass]="{
            'showButton-cls' : idSelectElement.toString() && idSelectElement === element.id,
            'hide-button-cls': group.name === 'CHƯA CÓ NHÓM',
            'show-button-confirm-input': ( element.isEdit) || (element.isChange  && checkTagChanged(element, tag))
          }"
        >
          <mat-icon
            *ngIf="group.name !== 'CHƯA CÓ NHÓM'"
            class="icons-cls edit-icon"
            aria-hidden="false"
            aria-label="icon edit"
            svgIcon="icon:edit-grey-icon"
            matTooltip="Sửa"
            matTooltipPosition="above"
            (click)="editGroupCustomer(group, element, tag)"
            (keydown)="keydownEvent()"
          ></mat-icon>
          <mat-icon
            *ngIf="group.name !== 'CHƯA CÓ NHÓM'"
            class="icons-cls add-user-icon"
            aria-hidden="false"
            aria-label="icon"
            svgIcon="icon:add-user-icon"
            matTooltip="Thêm khách hàng"
            matTooltipPosition="above"
            (click)="addCustomer($event, element)"
          (keydown)="keydownEvent()"
          ></mat-icon>
          <mat-icon
            class="icons-cls copy-icon"
            aria-hidden="false"
            aria-label="icon"
            svgIcon="icon:copy-icon"
            matTooltip="Chuyển toàn bộ KH trong nhóm"
            matTooltipPosition="above"
            (click)="moveGroupCustomer($event, element)"
        (keydown)="keydownEvent()"
          ></mat-icon>
          <mat-icon
            *ngIf="group.name !== 'CHƯA CÓ NHÓM'"
            class="icons-cls delete-icon"
            aria-hidden="false"
            aria-label="icon"
            svgIcon="icon:delete-icon"
            matTooltip="Xoá nhóm"
            matTooltipPosition="above"
            (click)="deleteGroup(element)"
           (keydown)="keydownEvent()"
          ></mat-icon>
          <mat-icon
            *ngIf="group.name !== 'CHƯA CÓ NHÓM'"
            class="icons-cls tick-icon"
            aria-hidden="false"
            aria-label="icon"
            svgIcon="icon:tick-icon"
            matTooltip="Lưu"
            matTooltipPosition="above"
            (click)="saveStatusEditor(element)"
            (keydown)="keydownEvent()"
          ></mat-icon>
          <mat-icon
            *ngIf="group.name !== 'CHƯA CÓ NHÓM'"
            class="icons-cls x-cross-icon"
            aria-hidden="false"
            aria-label="icon"
            svgIcon="icon:x-cross-icon"
            matTooltip="Hủy"
            matTooltipPosition="above"
            (click)="cancelStatusEditor(element)"
        (keydown)="keydownEvent()"
          ></mat-icon>
        </div>
      </div>
    </div>
    }
  </div>
  } @else {
  <div
    class="typo-body-12 select-text-cls"
    [class.isNotInBroker]="!element?.isInBroker"
    (click)="gridTemplate._toggleNormalCheckbox(element)"
 (keydown)="keydownEvent()"
  >
    <img
      *ngIf="!element?.isInBroker"
      [matTooltip]="'MES-666' | translate"
      matTooltipPosition="above"
      matTooltipClass="tooltip-hover-customer-in-group-cls"
      src="./assets/icons/expired-date-icon.svg"
      alt="expired-date-icon"
    />
    {{group}}
  </div>
  }
</ng-template>

<ng-template #contextMenuRef let-option="option">
  <div class="context-menu-container-cls">
    <div class="box-btn">
      <img [src]="option.url" [alt]="option.tag" />
      <div class="typo-body-12">{{ option.label | translate}}</div>
    </div>
  </div>
</ng-template>
