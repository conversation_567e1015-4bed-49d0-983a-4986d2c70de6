<div class="authority-info-container">
  <div class="header-authority-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-12' | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{totalTags.totalAccount}} {{'MES-15' | translate}}
        </div>
        <div *ngIf="onEditMode" class="text-edit-cls">{{'MES-156' | translate}}: {{getCountDataEdited()}}</div>
      </div>
    </div>
    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>
  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event);openAuthorityInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
      (rowDataChange)="dataUpdateEvent($event)"
    >
    </sha-grid>
  </div>
</div>

<ng-template #authorityInfo let-templateInfo="templateInfo" let-element="element" let-tag="tag">
  @if(templateInfo?.name || templateInfo?.files?.length){
  <div class="file-template-cls typo-body-12">
    <img [src]="'./assets/icons/pdf.svg'" alt="document-logo" />
    @if(templateInfo.name){
    <span>{{templateInfo.name}}</span>
    }@else{
    <span>-</span>
    }
  </div>

  } @else if (checkTagDisable(tag, element?.tagDisable ?? [])) {
  <div class="file-template-cls typo-body-12">
    <span>-</span>
  </div>
  } @else {
  <div class="file-empty-template-cls typo-body-12" [class.edit-mode-cls]="onEditMode">
    <img *ngIf="!onEditMode" src="assets/icons/add-file-icon.svg" alt="add-doc-gray" />
    <img *ngIf="onEditMode" src="assets/icons/file-extension-blue.svg" alt="add-doc" />
    <span>{{"MES-28" | translate}}</span>
  </div>
  }
</ng-template>

<ng-template
  #calendarRangePicker
  let-templateInfo="templateInfo"
  let-element="element"
  let-tag="tag"
  let-column="column"
>
  @if(templateInfo) {
  <div class="bank-account">
    <!-- <span (click)="calendarCustom.openCalendar()">{{calendar}}</span> -->
    <app-date-picker-component
      [isDateRange]="true"
      [rangeDate]="templateInfo"
      [tag]="tag"
      [isEdit]="onEditMode"
      (dateChangeEvent)="changeDateEvent($event)"
      [element]="element"
      (unFocusElement)="elementSelectFunc($event)"
      [isShowTime]="element.isShowTime"
      [isExpiredRangeDate]="column.componentConfig && column.componentConfig?.isExpiredRangeDate ? true : false"
    ></app-date-picker-component>
  </div>
  }
</ng-template>

<ng-template #calendarPicker let-templateInfo="templateInfo" let-element="element" let-tag="tag" let-column="column">
  <div class="bank-account">
    <app-date-picker-component
      [element]="element"
      [tag]="tag"
      [startDate]="templateInfo"
      [isEdit]="onEditMode"
      #calendarCustom
      (dateChangeEvent)="changeDateEvent($event)"
      (unFocusElement)="elementSelectFunc($event)"
      [isExpiredDate]="column.componentConfig && column.componentConfig?.isExpiredDate ? true : false"
    ></app-date-picker-component>
  </div>
</ng-template>

<ng-template #numberPhone let-templateInfo="templateInfo" let-element="element" let-tag="tag">
  <app-phone-number-table-component
    [data]="templateInfo"
    [isEdit]="onEditMode"
    [element]="element"
    (unFocusElement)="elementSelectFunc($event)"
    (dateChangeEvent)="changeDateEvent($event)"
    [tag]="tag"
  ></app-phone-number-table-component>
</ng-template>

<ng-template #checkBox let-checkBoxData="templateInfo" let-element="element" let-tag="tag">
  <app-check-box-for-table
    [isChecked]="checkBoxData"
    [isEdit]="onEditMode"
    [element]="element"
    [tag]="tag"
    (changeDataEvent)="changeDateEvent($event); updateDataViewCheckBox($event)"
  ></app-check-box-for-table>
</ng-template>
