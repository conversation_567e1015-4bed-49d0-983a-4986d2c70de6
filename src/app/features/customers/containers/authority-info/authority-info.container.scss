.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.authority-info-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-authority-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;
    white-space: nowrap;
    text-wrap: nowrap;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
        .text-edit-cls {
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          white-space: nowrap;
          text-wrap: nowrap;
          color: var(--color--brand--500);
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;

      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--cancel {
            color: var(--color--danger--600);
            border: 1px solid var(--color--danger--600);
          }

          #box-id--broker {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          mat-icon[data-mat-icon-name='people-icon'] {
            #Group,
            #Group_2,
            #Group_3 {
              ::ng-deep {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.not-accept-save-cls {
          ::ng-deep {
            #box-id--save {
              opacity: 0.5;
            }
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        .table-container {
          position: relative;
        }
        td {
          &:has(app-phone-number-table-component) {
            padding: 0;
            vertical-align: top;
            height: 36px;
            app-phone-number-table-component {
              display: flex;
              align-items: center;
              height: 100%;
              width: 100%;
            }
          }

          &:has(app-date-picker-component) {
            &:has(.isShowValid) {
              background-color: var(--color--danger--100);
            }
          }
        }

        .bank-account {
          width: 100%;
        }

        .status-authority {
          justify-content: center;

          .box-show {
            border-radius: 16px;
            justify-content: center;
            background-color: var(--color--neutral--100);
            display: inline-flex;
            white-space: nowrap;
            text-wrap: nowrap;
            width: unset !important;

            max-width: 140px;
            margin: 0 auto;

            .input-table-view {
              text-align: center;
            }

            .isEditMode {
              border-color: transparent !important;
              text-align: unset;
            }
          }

          &.has-authority {
            .box-show {
              background-color: var(--color--accents--yellow);
            }
          }

          &.change-value-mode {
            border-radius: 16px;
            border: 1px solid var(--color--accents--yellow);

            .box-show {
              background-color: unset;
            }
          }
        }

        .location-cls {
          .text-link {
            color: unset;
          }
        }

        .authority-box {
          justify-content: center;

          .box-show {
            border-radius: 16px;
            justify-content: center;
            background-color: var(--color--accents--red-dark);
            display: inline-flex;
            white-space: nowrap;
            text-wrap: nowrap;
            width: unset !important;

            .input-table-view {
              text-align: center;
              border: 1px solid transparent !important;
            }
          }

          &.has-authority {
            .box-show {
              background-color: var(--color--accents--green-dark);
              text-align: unset;
            }
          }

          &.change-value-mode {
            border-radius: 16px;
            border: 1px solid var(--color--accents--yellow);

            .box-show {
              background-color: unset;
            }
          }
        }

        .file-empty-template-cls {
          display: flex;
          align-items: center;
          gap: 6px;
          color: var(--color--neutral--300);

          &.edit-mode-cls {
            span {
              color: var(--color--accents--cyan-dark) !important;
            }
          }
        }

        .file-template-cls {
          display: flex;
          gap: 6px;
          align-items: center;
          color: var(--color--accents--cyan-dark);

          span {
            white-space: nowrap;
            text-wrap: nowrap;
          }
        }
      }
    }
  }

  .type-account-cls {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 16px;
    background-color: #ffd60a;

    &.organize-cls {
      background-color: #32d74b;
    }
  }

  .file-extention-cls {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #5ac8f5;
  }
}
