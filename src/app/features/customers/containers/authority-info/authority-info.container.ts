import { Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { DestroyService } from 'src/app/core/services';
import { AuthorityInfoFilterComponent } from '../../components/authority-info-filter/authority-info-filter.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { CELL_TYPE, IClickOnColumnEvent } from '@shared/models';
import { LIST_PROVINCE } from 'src/app/shared/constants/province';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import { Observable, take, takeUntil } from 'rxjs';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { IFilterAuthorityInfoParam, TotalTags } from '../../model/customer';

import { Store } from '@ngrx/store';
import {
  selectCustomerAuthority$,
  selectFilterAuthorityInfo$,
  selectFilteredDataAuthorityInfo$,
  selectSearchValue$,
} from '../../stores/customer.selector';
import {
  CONVERT_AUTH_STATUS_TO_CLASS,
  CONVERT_AUTHORIRY_STATUS,
  CONVERT_AUTHORITY_STATUS_TO_LABEL,
} from '../../constants/customers';
import {
  getCustomersAuthorityInfo,
  resetFilterAuthorityInfo,
  setFilterAuthorityInfo,
} from '../../stores/customer.action';
import { IDateChangeItem } from 'src/app/shared/components/date-picker/date-picker/date-picker.component';
import { deepClone } from 'src/app/shared/utils/utils';
import { CustomerDetailPopupComponent } from '../../components/customer-detail-popup/customer-detail-popup.component';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { getCurrentBrokerView } from 'src/app/stores/shared/shared.actions';

/**
 * Thông tin uỷ quyền
 */
@Component({
  selector: 'app-authority-info-container',
  templateUrl: './authority-info.container.html',
  styleUrls: ['./authority-info.container.scss'],
})
export class AuthorityInfoCustomerContainer
  extends BaseTableComponent<any>
  implements OnInit, ComponentCanDeactivate, OnDestroy
{
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('authorityInfo', { static: true }) authorityInfo: TemplateRef<any> | null = null;

  @ViewChild('calendarRangePicker', { static: true }) calendarRangePicker: TemplateRef<any> | null = null;

  @ViewChild('calendarPicker', { static: true }) calendarPicker: TemplateRef<any> | null = null;

  @ViewChild('numberPhone', { static: true }) numberPhoneTemplate: TemplateRef<any> | null = null;

  @ViewChild('checkBox', { static: true }) checkBoxTemplate: TemplateRef<any> | null = null;

  LIST_PROVINCE = LIST_PROVINCE;
  CONVERT_AUTHORITY_STATUS_TO_LABEL = CONVERT_AUTHORITY_STATUS_TO_LABEL;

  filterOptions!: IFilterAuthorityInfoParam;

  LIST_MG: IListOptions[] = [];

  searchValue: string = '';
  totalTags: TotalTags = {
    totalAccount: 0,
  };

  /**
   * Constructor
   * @param dialogService DialogService
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly popoverService: PopoverService
  ) {
    super();
    this.brokerInfoAuth();
    this.toggleButtonByTags([
      ActionButton.broker,
      // ActionButton.edit,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);

    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        if (!user) return;
        this.store.dispatch(getCustomersAuthorityInfo({ id: user.brokerCode }));
      });
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.store
      .select(selectCustomerAuthority$)
      .pipe(takeUntil(this._destroy))
      .subscribe((authContract) => {
        const dataClone = structuredClone(authContract);

        this.totalTags.totalAccount = dataClone?.length;

        dataClone.forEach((d) => {
          d.authFromDate = d.authFromDate + ' - ' + d.authToDate;
        });
        this.data = this.updateAllAuthorization(dataClone);
        this.initialData = structuredClone(this.data);
        if (this.isSearch) {
          this.data = this.searchedData;
        }
      });

    const template = this.authorityInfo;
    const templateRangeCalendar = this.calendarRangePicker;
    const templateCalendar = this.calendarPicker;
    const checkboxTemplate = this.checkBoxTemplate;

    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 146,
        width: 146,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        isEdit: false,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 290,
        tag: 'customerName',
        isDisplay: true,
        isEdit: false,
        resizable: true,
      },
      {
        name: 'Trạng thái ủy quyền',
        minWidth: 30,
        width: 170,
        tag: 'authStatus',
        isDisplay: true,
        displayValueFn: (value) => CONVERT_AUTHORITY_STATUS_TO_LABEL[value],
        dynamicClass: (value) => CONVERT_AUTH_STATUS_TO_CLASS[value],
        typeValue: CELL_TYPE.DROPDOWN,
        isEdit: true,
        align: 'center',
        resizable: true,
        componentConfig: {
          multiple: false,
          isSearch: false,
          searchKey: 'value',
          displayOptionFn: (v: any) => v.name,
          options: [
            {
              name: 'Không ủy quyền',
              value: false,
            },
            {
              name: 'Có ủy quyền',
              value: true,
            },
          ],
        },
      },
      {
        name: 'Người được ủy quyền',
        minWidth: 30,
        width: 200,
        tag: 'authPerson',
        isDisplay: true,
        isEdit: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        typeValue: CELL_TYPE.TEXT,
        resizable: true,
        type: 'text',
      },
      {
        name: 'Số điện thoại (Người được UQ)',
        minWidth: 30,
        width: 200,
        tag: 'authTelephone',
        isDisplay: true,
        typeValue: CELL_TYPE.TEXT,
        isEdit: true,
        type: 'text',
        // cellTemplate: numberTemplate,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
      },
      {
        name: 'Email (Người được UQ)',
        minWidth: 30,
        width: 220,
        tag: 'authEmail',
        isDisplay: true,
        url: './assets/icons/sms.svg',
        panelClass: 'location-cls',
        typeValue: CELL_TYPE.TEXT,
        isEdit: true,
        type: 'text',
        isEmail: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        resizable: true,
      },
      {
        name: 'Địa chỉ liên hệ (Người được UQ)',
        minWidth: 30,
        width: 460,
        tag: 'authAddress',
        isDisplay: true,
        url: './assets/icons/location.svg',
        panelClass: 'location-cls',
        typeValue: CELL_TYPE.TEXT,
        type: 'text',
        isEdit: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
      },
      {
        name: 'Số hợp đồng uỷ quyền',
        minWidth: 30,
        width: 170,
        tag: 'contractNo',
        cellTemplate: template,
        isDisplay: true,
        typeValue: CELL_TYPE.FILE,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        isEdit: true,
        resizable: true,
      },
      {
        name: 'Chữ ký người UQ',
        minWidth: 30,
        width: 170,
        tag: 'accountSignature',
        isDisplay: true,
        cellTemplate: template,
        typeValue: CELL_TYPE.FILE,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        isEdit: true,
        resizable: true,
      },
      {
        name: 'Chữ ký người được UQ',
        minWidth: 30,
        width: 170,
        tag: 'authSignature',
        isDisplay: true,
        cellTemplate: template,
        typeValue: CELL_TYPE.FILE,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        isEdit: true,
        resizable: true,
      },
      {
        name: 'Số CMND / CCCD / HC (Người được UQ)',
        minWidth: 30,
        width: 255,
        tag: 'authIdentity',
        isDisplay: true,
        cellTemplate: template,
        typeValue: CELL_TYPE.FILE,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        isEdit: true,
        resizable: true,
      },
      {
        name: 'Ngày cấp (Người được UQ)',
        minWidth: 30,
        width: 175,
        tag: 'authIdentityDate',
        isDisplay: true,
        typeValue: CELL_TYPE.CALENDAR,
        isEdit: true,
        cellTemplate: templateCalendar,
        resizable: true,
        componentConfig: {
          isExpiredDate: true,
          isValidateDate: true,
        },
      },
      {
        name: 'Nơi cấp (Người được UQ)',
        minWidth: 30,
        width: 340,
        tag: 'authIdentityIssuer',
        isDisplay: true,
        // typeValue: CELL_TYPE.DROPDOWN_MULTIPLE_LEVEL,
        isEdit: true,
        // displayValueFn: (v) =>
        //   Object.keys(v.children).length
        //     ? v.name + ' - ' + this.LIST_PROVINCE.find((p) => p.id === v.children.id)!.name
        //     : v.name,
        // componentConfig: {
        //   displayOptionFn: (v: any) => v.name,
        //   multiple: false,
        //   searchKey: 'name',
        //   options: [
        //     {
        //       name: 'Cục Cảnh sát ĐKQL cư trú và DLQG về dân cư',
        //       id: '1',
        //       children: [],
        //     },
        //     {
        //       name: 'Cục Cảnh sát QLHC về TTXH',
        //       id: '2',
        //       children: [],
        //     },
        //     {
        //       name: 'Công an Tỉnh/Thành phố',
        //       id: '3',
        //       children: LIST_PROVINCE,
        //     },
        //     {
        //       name: 'Cục Quản lý xuất nhập cảnh',
        //       id: '4',
        //       children: [],
        //     },
        //   ],
        // },
        typeValue: CELL_TYPE.TEXT,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
      },
      {
        name: 'Thời gian uỷ quyền',
        minWidth: 30,
        width: 230,
        tag: 'authFromDate',
        isDisplay: true,
        cellTemplate: templateRangeCalendar,
        typeValue: CELL_TYPE.CALENDAR,
        align: 'center',
        isEdit: true,
        resizable: true,
        componentConfig: {
          isExpiredRangeDate: true,
        },
      },
      {
        name: 'UQ - Toàn bộ',
        minWidth: 30,
        width: 170,
        tag: 'authAll',
        isDisplay: true,
        align: 'center',
        isEdit: true,
        cellTemplate: checkboxTemplate,
        resizable: true,
      },
      {
        name: 'UQ - Đặt lệnh GD',
        minWidth: 30,
        width: 170,
        tag: 'authCommand',
        isDisplay: true,
        align: 'center',
        isEdit: true,
        cellTemplate: checkboxTemplate,
        resizable: true,
      },
      {
        name: 'UQ - Gửi rút tiền',
        minWidth: 30,
        width: 170,
        tag: 'authMoney',
        isDisplay: true,
        align: 'center',
        isEdit: true,
        cellTemplate: checkboxTemplate,
        resizable: true,
      },
      {
        name: 'UQ - Ứng trước',
        minWidth: 30,
        width: 170,
        tag: 'authAdvance',
        isDisplay: true,
        align: 'center',
        isEdit: true,
        cellTemplate: checkboxTemplate,
        resizable: true,
      },
      {
        name: 'UQ - Lưu ký',
        minWidth: 30,
        width: 170,
        tag: 'authDepository',
        isDisplay: true,
        align: 'center',
        isEdit: true,
        cellTemplate: checkboxTemplate,
        resizable: true,
      },
      {
        name: 'UQ - Dịch vụ tài chính',
        minWidth: 30,
        width: 170,
        tag: 'authFinanceService',
        isDisplay: true,
        align: 'center',
        isEdit: true,
        cellTemplate: checkboxTemplate,
        resizable: true,
      },
      {
        name: 'UQ - Gửi rút CK',
        minWidth: 30,
        width: 170,
        tag: 'authStock',
        isDisplay: true,
        align: 'center',
        isEdit: true,
        cellTemplate: checkboxTemplate,
        resizable: true,
      },
    ];

    this.store
      .select(selectFilteredDataAuthorityInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filteredData) => {
        this.data = filteredData.length ? structuredClone(filteredData) : structuredClone(this.initialData);
      });

    this.store
      .select(selectFilterAuthorityInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchValue = value ?? '';
        this.searchData();
      });
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterAuthorityInfo());
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * Thông tin của broker
   */
  brokerInfoAuth() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBrokerAuth) => {
        if (!currentBrokerAuth) return;
        this.findButtonByTags(ActionButton.broker).label = `${currentBrokerAuth.brokerCode}: ${currentBrokerAuth.brokerName}`;

        this.store
          .select(selectInfoUserLogin$)
          .pipe(takeUntil(this._destroy))
          .subscribe((userListAuth) => {
            if (!userListAuth) return;
            this.LIST_MG = userListAuth.map((user) => {
              return {
                brokerCode: `${user.brokerCode}`,
                name: `${user.brokerCode}: ${user.brokerName}`,
                isSelect: user.brokerCode === currentBrokerAuth.brokerCode,
              };
            });
          });
      });
  }

  /**
   * UpdateAllAuthorization
   * @param data
   * @returns {any} new data
   */
  updateAllAuthorization(data: any[]) {
    const listField = ['authCommand', 'authMoney', 'authAdvance', 'authDepository', 'authFinanceService', 'authStock'];
    return data.map((t) => ({
      ...t,
      authAll: listField.every((field) => t[field] === true),
      isShowTime: listField.some((field) => t[field] === true),
    }));
  }

  /**
   * DataUpdateEvent
   * @param data
   */
  dataUpdateEvent(data: any) {
    const { element, tag } = data;

    if (tag === 'status') {
      const listField = [
        'authCommand',
        'authMoney',
        'authAdvance',
        'authDepository',
        'authFinanceService',
        'authStock',
        'authAll',
      ];
      this.data.forEach((d) => {
        if (d.accountNumber === element.accountNumber) {
          listField.forEach((field) => {
            d[field] = element[tag];
            const isExitTag = d.tagChange?.find((t: string) => t === field);
            if (!isExitTag) {
              d.tagChange = [...(d.tagChange ?? []), field];
            }
            this.grid.map.markElementAsDirty(element.id, field);
          });
          element.isShowTime = listField.some((field) => d[field] === true);
        }
      });
    }
  }

  /**
   * UpdateDataViewCheckBox
   * @param data
   */
  updateDataViewCheckBox(data: IDateChangeItem) {
    const listField = ['authCommand', 'authMoney', 'authAdvance', 'authDepository', 'authFinanceService', 'authStock'];
    const { date, tag, element } = data;

    /**
     * UpdateIsShowRangeDateTime
     */
    const updateIsShowRangeDateTime = () => {
      this.data.forEach((t) => {
        if (t.accountNumber === element.accountNumber) {
          t.isShowTime = listField.some((field) => t[field] === true);
          this.changeDateEvent({ date: listField.some((field) => t[field] === true), tag: 'isShowTime', element });
        }
      });
    };
    if (tag === 'authAll') {
      element['status'] = date;
      const isExitTag = element.tagChange?.find((t: string) => t === 'status');

      if (!isExitTag) {
        element.tagChange = [...(element.tagChange ?? []), 'status'];
      }
      this.changeDateEvent({ date, tag: 'status', element });

      this.grid.map.markElementAsDirty(element.id, 'status');
      listField.forEach((field) => {
        element[field] = date;
        const isExitTag = element.tagChange?.find((t: string) => t === field);
        if (!isExitTag) {
          element.tagChange = [...(element.tagChange ?? []), field];
        }
        this.changeDateEvent({ date, tag: field, element });
        this.grid.map.markElementAsDirty(element.id, field);
      });
    } else {
      this.data.forEach((t) => {
        const isAllAuthority = listField.every((field) => t[field] === true);
        const isAuthority = listField.some((field) => t[field] === true);

        if (t['authAll'] !== isAllAuthority) {
          t.authAll = isAllAuthority;

          const isExitTag = t.tagChange?.find((t: string) => t === 'authAll');
          if (!isExitTag) {
            t.tagChange = [...(t.tagChange ?? []), 'authAll'];
          }
          this.grid.map.markElementAsDirty(element.id, 'authAll');

          this.changeDateEvent({ date: isAllAuthority, tag: 'authAll', element });
        }
        if (t.status !== isAuthority) {
          t.status = isAuthority;
          const isExitTag = t.tagChange?.find((t: string) => t === 'status');
          if (!isExitTag) {
            t.tagChange = [...(t.tagChange ?? []), 'status'];
          }

          this.grid.map.markElementAsDirty(element.id, 'status');
          this.changeDateEvent({ date: isAuthority, tag: 'status', element });
        }
      });
    }
    updateIsShowRangeDateTime();

    this.cdf.detectChanges();
  }

  /**
   * searchData
   */
  searchData() {
    let searchDataAuth = [];
    if (this.searchValue) {
      const searchValue = this.searchValue.toString().toLowerCase();
      if (!this.isFilter) {
        searchDataAuth = deepClone(this.initialData).filter((item) => {
          return this.containsSearchValue(item, searchValue);
        });
      } else {
        searchDataAuth = this.filteredData.filter((item) => {
          return this.containsSearchValue(item, searchValue);
        });
      }
      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchDataAuth = this.isFilter ? deepClone(this.filteredData) : deepClone(this.initialData);

      this.isSearch = false; // Set isSearch to false when not searching
    }
    this.searchedData = deepClone(searchDataAuth);
    this.data = deepClone(searchDataAuth);
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    return (
      item.accountNumber.toString().toLowerCase().includes(searchValue) ??
      item.customerName.toString().toLowerCase().includes(searchValue) ??
      item.identificationId.name.toString().includes(searchValue) ??
      item.email.toString().includes(searchValue) ??
      item.telephone.toString().includes(searchValue)
    );
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'broker':
        this.changeViewBroker();
        break;
      case 'filter':
        {
          const ref = this.openFilter(AuthorityInfoFilterComponent, {
            width: '450px',
            data: this.filterOptions,
          });
          this.closeApplyFilter(ref);
        }

        break;
      case 'edit':
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * CloseApplyFilter
   * @param {any} ref ref
   */
  closeApplyFilter(ref: any) {
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        this.applyFilter(v);
      });
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([
      ActionButton.broker,
      ActionButton.edit,
      ActionButton.save,
      ActionButton.cancel,
      ActionButton.export,
    ]);
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any): void {
    const { optionFilter, type } = data;
    const dataClone = deepClone(this.initialData);

    if (type === 'save') {
      const params: IFilterAuthorityInfoParam = {
        typeAccount: optionFilter.accountType,
        isFilter: optionFilter.isFilter,
        authorityStatus: optionFilter.authorityStatus,
      };
      this.store.dispatch(setFilterAuthorityInfo({ params }));
      const newListFilter = this.isSearch
        ? this.logicFilter(this.searchedData, optionFilter)
        : this.logicFilter(dataClone, optionFilter);

      this.filteredData = newListFilter;
      this.data = deepClone(this.filteredData);
      this.totalTags.totalAccount = this.data?.length;
    } else if (type === 'default') {
      this.isFilter = false;
      this.filteredData = [];
      this.totalTags.totalAccount = this.initialData?.length;
      this.isSearch
        ? this.store
            .select(selectSearchValue$)
            .pipe(takeUntil(this._destroy))
            .subscribe((value) => {
              this.searchValue = value ?? '';
              this.searchData();
            })
        : (() => {
            this.data = this.initialData;
          })();
      this.store.dispatch(resetFilterAuthorityInfo());
    }
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    const newListFilter = data.filter((customer, index) => {
      const isAccountTypeMatch = optionFilter.accountType.length
        ? optionFilter.accountType.includes(customer.accountType)
        : true;

      const isAuthorityStatusMatch = optionFilter.authorityStatus.length
        ? optionFilter.authorityStatus.includes(
            CONVERT_AUTHORIRY_STATUS[String(customer.status) as keyof typeof CONVERT_AUTHORIRY_STATUS]
          )
        : true;

      return isAccountTypeMatch && isAuthorityStatusMatch;
    });

    return newListFilter;
  }

  /**
   * openAuthorityInfoDetail
   * @param event
   */
  openAuthorityInfoDetail(event: IClickOnColumnEvent) {
    if (this.onEditMode) return;

    const { element } = event;

    this.dialogService.openPopUp(CustomerDetailPopupComponent, {
      width: '71vw',
      height: '94vh',
      panelClass: ['popup-confirm', 'popup-height-fix-content', 'not-padding-popup'],
      data: {
        element,
        route: 'authority',
      },
    });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    const elementRef = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidth = elementRef.nativeElement as HTMLElement;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRef as any,
      width: elementWidth.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(takeUntil(this._destroy)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(takeUntil(this._destroy))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBroker = userList.find((user) => user.brokerCode === itemSelected['brokerCode']);
          if (!currentBroker) return;
          this.store.dispatch(getCurrentBrokerView({ data: currentBroker }));
        });
    });
  }
}
