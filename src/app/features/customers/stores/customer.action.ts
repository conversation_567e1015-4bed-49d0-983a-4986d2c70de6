import { createAction, props } from '@ngrx/store';
import {
  IFilterAccountInfoParam,
  IFilterAuthorityInfoParam,
  IFilterBankInfoParam,
  IFilterDocumentInfoParam,
  IFilterPersonalInfoParam,
  IFilterInterestRateInfoParam,
  IFilterCustomerGroupParam,
  ICustomerBank,
  ICusomterGroup,
  ICustomerDocument,
  ICustomerAccount,
  ICustomerAuthority,
  IBankInfo,
  ICustomerDataResponse,
} from '../model/customer';
import { IPayloadPersonalList } from 'src/app/shared/models/global';

export const search = createAction('[Customer] search', props<{ data: string }>());

export const resetSearch = createAction('[Customer] reset search');

// PERSONAL
export const getCustomerPersonalInfo = createAction(
  '[Personal info] Get List Personal Info',
  props<{ payload: IPayloadPersonalList; isSearchOrFilter: boolean }>()
);

export const getPersonalInfoListSuccess = createAction(
  '[Personal info] get personal info list success',
  props<{ data: ICustomerDataResponse[] }>()
);

export const setFilterPersonalInfo = createAction(
  '[Personal info] set filter personal info',
  props<{ params: IFilterPersonalInfoParam }>()
);
export const setFilteredDataPersonalInfo = createAction(
  '[Personal info] filtered data personal info',
  props<{ data: ICustomerDataResponse[] }>()
);
export const resetFilterPersonalInfo = createAction('[Personal info] reset filter personal info default');

export const updatePageIndexPersonalInfo = createAction(
  '[Personal info] update page index personal info ',
  props<{ pageIndex: number }>()
);

// BANK
export const setFilterBankInfo = createAction(
  '[Bank info] set filter bank info',
  props<{ params: IFilterBankInfoParam }>()
);

export const setFilteredDataBankInfo = createAction('[Bank info] filtered data bank info', props<{ data: any[] }>());

export const resetFilterBankInfo = createAction('[Bank info] reset filter bank info default');

// DOCUMENT
export const setFilterDocumentInfo = createAction(
  '[Document info] set filter document info',
  props<{ params: IFilterDocumentInfoParam }>()
);

export const setFilteredDataDocumentInfo = createAction(
  '[Document info] filtered data document info',
  props<{ data: any[] }>()
);

export const resetFilterDocumentInfo = createAction('[Document info] reset filter document info default');

// ACCOUNT
export const setFilterAccountInfo = createAction(
  '[Account info] set filter account info',
  props<{ params: IFilterAccountInfoParam }>()
);

export const setFilteredDataAccountInfo = createAction(
  '[Account info] filtered data account info',
  props<{ data: any[] }>()
);

export const resetFilterAccountInfo = createAction('[Account info] reset filter account info default');

// INTEREST-RATE
export const setFilterInterestRateInfo = createAction(
  '[Interest-rate info] set filter interest-rate info',
  props<{ params: IFilterInterestRateInfoParam }>()
);

export const setFilteredDataInterestRateInfo = createAction(
  '[Interest-rate info] filtered data interest-rate info',
  props<{ data: any[] }>()
);

export const resetFilterInterestRateInfo = createAction('[Interest-rate info] reset filter interest-rate info default');

// AUTHORITY
export const getCustomersAuthorityInfo = createAction(
  '[Authority info] Get List Authority Info By BrokerId',
  props<{ id: string }>()
);
export const getCustomersAuthorityInfoSuccess = createAction(
  '[Authority info] Get List Authority Info By BrokerId Success',
  props<{ data: ICustomerAuthority[] }>()
);

export const setFilterAuthorityInfo = createAction(
  '[Authority info] set filter authority info',
  props<{ params: IFilterAuthorityInfoParam }>()
);
export const setFilteredDataAuthorityInfo = createAction(
  '[Authority info] filtered data authority info',
  props<{ data: any[] }>()
);
export const resetFilterAuthorityInfo = createAction('[Authority info] reset filter authority info default');

// Customer Group
export const setFilterCustomerGroup = createAction(
  '[Authority info] set filter customer group',
  props<{ params: IFilterCustomerGroupParam }>()
);

export const setFilteredDataCustomerGroup = createAction(
  '[Customer group] filtered data customer group',
  props<{ data: any[] }>()
);

export const resetFilterCustomerGroup = createAction('[Customer group] reset filter customer group default');

// Bank Info with API

export const getCustomersBankInfo = createAction('[Customer] get customers bank info', props<{ id: string }>());

export const getCustomersBankInfoSuccess = createAction(
  '[Customer] get customers bank info success',
  props<{ data: ICustomerBank[] }>()
);

// Customer group with API
export const getCustomerGroupInfo = createAction('[Customer] get customer group info', props<{ brokerId?: string }>());

export const getCustomerGroupInfoSuccess = createAction(
  '[Customer] get customer group info success',
  props<{ data: ICusomterGroup[] }>()
);

// document info with API

export const getCustomerDocumentInfo = createAction('[Customer] get customer document info', props<{ id: string }>());

export const getCustomerDocumentInfoSuccess = createAction(
  '[Customer] get customer document info success',
  props<{ data: ICustomerDocument[] }>()
);

// customer account with api

export const getCustomerAccountInfo = createAction('[Customer] get customer account info', props<{ id: string }>());

export const getCustomerAccountInfoSuccess = createAction(
  '[Customer] get customer account info success',
  props<{ data: ICustomerAccount[] }>()
);

// FIX LATER: Tránh gọi lại api màn list đóng dropdown
export const getCustomerAccountInfoDetail = createAction(
  '[Customer] get customer account info detail',
  props<{ id: string }>()
);

export const getCustomerAccountInfoDetailSuccess = createAction(
  '[Customer] get customer account info detail success',
  props<{ data: ICustomerAccount[] }>()
);

export const getAllInfoBank = createAction('[Customer] get all info bank list');

export const getAllInfoBankSuccess = createAction(
  '[Customer] get all info bank list success',
  props<{ data: IBankInfo[] }>()
);

export const getAllDataCustomerInfo = createAction(
  '[Customer] get all data customer info',
  props<{ id: string; username: string }>()
);

export const getAllDataCustomerInfoNew = createAction(
  '[Customer] get all data customer info',
  props<{ payload: IPayloadPersonalList }>()
);

export const callApiError = createAction('[Customer] call api error');
