import * as CustomerSelectors from './customer.selector';
import { ICustomerState } from '../model/customer';

describe('Customer Selectors', () => {
  const mockCustomerState: ICustomerState = {
    searchValue: 'test search',
    customerPersonalData: [
      {
        accountNumber: 'ACC001',
        name: 'Customer 1',
        customerName: 'Customer 1',
        accountType: 1,
        identity: 'ID001',
        identityDate: '2023-01-01',
        identifierNumber: 'IDN001',
        identityIssuer: 'Issuer',
        nationalityId: 'VN',
        idNumberType: 'CMND',
        idNumberTypeName: 'Chứng minh nhân dân',
        authorizedIdNumber: 'AUTH001',
        birthday: '1990-01-01',
        sexId: 1,
        telephone: '**********',
        email: '<EMAIL>',
        address: 'Test Address',
        registrationNo: 'REG001',
        registrationDate: '2023-01-01',
        registrationRep: 'Rep',
        signatureImg: 'signature.jpg',
        accountLevelId: 'LEVEL1',
        accountOpenDate: '2023-01-01',
        accountCloseDate: '',
        accountLasttxnDate: '2023-12-01',
        brokerId: 'BR001',
        brokerName: 'Broker 1',
        brokerCode: 'BR001',
        saleGroupName: 'Sales 1',
        cusGroups: [],
        customerLevel: 'VIP',
        lastSyncDate: '2023-12-01',
        feeBasic: { value: 0.1, group: 'basic' },
        feeDerivative: { value: 0.2, group: 'derivative' },
        feeBond: { value: 0.15, group: 'bond' },
        subAccounts: []
      }
    ],
    filterPersonalInfo: {
      customers: ['CUST001'],
      typeAccount: 'individual',
      startYear: '1990',
      endYear: '2000',
      isFilter: true
    },
    filteredPersonalData: [],
    pageIndexPersonalInfo: 2,
    filterBankInfo: {
      isFilter: true,
      typeAccount: [1, 2],
      bankIds: ['bank1', 'bank2']
    },
    filteredBankData: [{ bankName: 'Bank 1' }],
    customerBankData: [
      {
        customerName: 'Customer 1',
        accountNumber: 'ACC001',
        accountType: 1,
        bankAccounts: []
      }
    ],
    filterDocumentInfo: {
      isFilter: true,
      typeAccount: [1, 2],
      contract00: [0, 1],
      contract01: [0, 1],
      contract02: [0, 1],
      contract03: [0, 1],
      contract04: [0, 1],
      contract05: [0, 1],
      contract80: [0, 1]
    },
    filteredDocumentData: [{ documentType: 'Contract' }],
    customerDocumentData: [],
    filterAccountInfo: {
      isFilter: true,
      typeAccount: [1, 2],
      levelAccount: ['VIP'],
      accountGroups: ['Group1'],
      rooms: ['Room1']
    },
    filteredAccountData: [{ accountNumber: 'ACC001' }],
    customerAccountData: [],
    customerAccountDetailData: [],
    filterInterestRateInfo: {
      isFilter: true,
      typeAccount: [0, 1],
      levelAccount: [0, 1, 2, 3]
    },
    filteredInterestRateData: [{ rate: 5.5 }],
    customerAuthorityData: [],
    filterAuthorityInfo: {
      isFilter: true,
      typeAccount: [0, 1],
      authorityStatus: [0, 1]
    },
    filteredAuthorityData: [{ authType: 'Full' }],
    filterCustomerGroup: {
      isFilter: true,
      accountNumber: ['ACC001']
    },
    filteredCustomerGroupData: [{ groupName: 'Group 1' }],
    customerGroupData: [],
    assetInfoByAccountNumber: [],
    listAllBankInfo: [
      {
        id: 'bank1',
        name: 'Bank 1',
        bankName: 'Bank Name 1',
        shortName: 'BN1',
        logo: 'logo1.jpg'
      }
    ]
  };

  const mockState = {
    [CustomerSelectors.CUSTOMER_STATE_NAME]: mockCustomerState
  };

  // Feature Selector
  it('No.1: should select customer state from store', () => {
    const result = CustomerSelectors.selectCustomerState(mockState);
    expect(result).toBe(mockCustomerState);
  });

  it('No.2: should have correct CUSTOMER_STATE_NAME constant', () => {
    expect(CustomerSelectors.CUSTOMER_STATE_NAME).toBe('CUSTOMER');
  });

  // Search Selectors
  it('No.3: should select search value', () => {
    const result = CustomerSelectors.selectSearchValue$(mockState);
    expect(result).toBe('test search');
  });

  it('No.4: should handle null search value', () => {
    const stateWithNullSearch = {
      [CustomerSelectors.CUSTOMER_STATE_NAME]: {
        ...mockCustomerState,
        searchValue: null
      }
    };
    const result = CustomerSelectors.selectSearchValue$(stateWithNullSearch);
    expect(result).toBeNull();
  });

  // Personal Info Selectors
  it('No.5: should select customer list', () => {
    const result = CustomerSelectors.selectCustomerList$(mockState);
    expect(result).toBe(mockCustomerState.customerPersonalData);
  });

  it('No.6: should select filter personal info', () => {
    const result = CustomerSelectors.selectFilterPersonalInfo$(mockState);
    expect(result).toBe(mockCustomerState.filterPersonalInfo);
  });

  it('No.7: should select filtered data personal info', () => {
    const result = CustomerSelectors.selectFilteredDataPersonalInfo$(mockState);
    expect(result).toBe(mockCustomerState.filteredPersonalData);
  });

  it('No.8: should select page index personal info', () => {
    const result = CustomerSelectors.pageIndexPersonalInfo$(mockState);
    expect(result).toBe(2);
  });

  it('No.9: should select personal info list', () => {
    const result = CustomerSelectors.selectPersonalInfoList$(mockState);
    expect(result).toBe(mockCustomerState.customerPersonalData);
  });

  // Bank Info Selectors
  it('No.10: should select filter bank info', () => {
    const result = CustomerSelectors.selectFilterBankInfo$(mockState);
    expect(result).toBe(mockCustomerState.filterBankInfo);
  });

  it('No.11: should select filtered data bank info', () => {
    const result = CustomerSelectors.selectFilteredDataBankInfo$(mockState);
    expect(result).toBe(mockCustomerState.filteredBankData);
  });

  it('No.12: should select customer bank info', () => {
    const result = CustomerSelectors.selectCustomerBankInfo$(mockState);
    expect(result).toBe(mockCustomerState.customerBankData);
  });

  // Document Info Selectors
  it('No.13: should select filter document info', () => {
    const result = CustomerSelectors.selectFilterDocumentInfo$(mockState);
    expect(result).toBe(mockCustomerState.filterDocumentInfo);
  });

  it('No.14: should select filtered data document info', () => {
    const result = CustomerSelectors.selectFilteredDataDocumentInfo$(mockState);
    expect(result).toBe(mockCustomerState.filteredDocumentData);
  });

  it('No.15: should select customer document info', () => {
    const result = CustomerSelectors.selectCustomerDocumentInfo$(mockState);
    expect(result).toBe(mockCustomerState.customerDocumentData);
  });

  // Account Info Selectors
  it('No.16: should select filter account info', () => {
    const result = CustomerSelectors.selectFilterAccountInfo$(mockState);
    expect(result).toBe(mockCustomerState.filterAccountInfo);
  });

  it('No.17: should select filtered data account info', () => {
    const result = CustomerSelectors.selectFilteredDataAccountInfo$(mockState);
    expect(result).toBe(mockCustomerState.filteredAccountData);
  });

  it('No.18: should select customer account info', () => {
    const result = CustomerSelectors.selectCustomerAccountInfo$(mockState);
    expect(result).toBe(mockCustomerState.customerAccountData);
  });

  it('No.19: should select customer account detail info', () => {
    const result = CustomerSelectors.selectCustomerAccountDetailInfo$(mockState);
    expect(result).toBe(mockCustomerState.customerAccountDetailData);
  });
});
