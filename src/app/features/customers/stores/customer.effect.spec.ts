import { TestBed } from '@angular/core/testing';
import { provideMockActions } from '@ngrx/effects/testing';
import { Action, Store } from '@ngrx/store';
import { Observable, of, throwError } from 'rxjs';
import { CustomersEffects } from './customer.effect';
import { LoadingService, MessageService } from 'src/app/core/services';
import { CustomerService } from '../services/customer.service';
import { AssetsService } from '../../assets/services/assets.service';
import * as CustomerActions from './customer.action';
import { IMembers, ICusomterGroup } from '../model/customer';

describe('CustomersEffects', () => {
  let actions$: Observable<Action>;
  let effects: CustomersEffects;
  let mockLoadingService: jasmine.SpyObj<LoadingService>;
  let mockMessageService: jasmine.SpyObj<MessageService>;
  let mockCustomerService: jasmine.SpyObj<CustomerService>;
  let mockAssetsService: jasmine.SpyObj<AssetsService>;
  let mockStore: jasmine.SpyObj<Store>;

  beforeEach(() => {
    const loadingServiceSpy = jasmine.createSpyObj('LoadingService', ['show', 'hide']);
    const messageServiceSpy = jasmine.createSpyObj('MessageService', ['error']);
    const customerServiceSpy = jasmine.createSpyObj('CustomerService', [
      'getPersonalInfoList',
      'getCustomerBankInfo',
      'getCustomerGroupInfo',
      'getCustomerDocumentInfo',
      'getCustomerAccountInfo',
      'getAuthContractList',
      'getAllBankList'
    ]);
    const assetsServiceSpy = jasmine.createSpyObj('AssetsService', ['getAssets']);
    const storeSpy = jasmine.createSpyObj('Store', ['select', 'dispatch']);

    TestBed.configureTestingModule({
      providers: [
        CustomersEffects,
        provideMockActions(() => actions$),
        { provide: LoadingService, useValue: loadingServiceSpy },
        { provide: MessageService, useValue: messageServiceSpy },
        { provide: CustomerService, useValue: customerServiceSpy },
        { provide: AssetsService, useValue: assetsServiceSpy },
        { provide: Store, useValue: storeSpy }
      ]
    });

    effects = TestBed.inject(CustomersEffects);
    mockLoadingService = TestBed.inject(LoadingService) as jasmine.SpyObj<LoadingService>;
    mockMessageService = TestBed.inject(MessageService) as jasmine.SpyObj<MessageService>;
    mockCustomerService = TestBed.inject(CustomerService) as jasmine.SpyObj<CustomerService>;
    mockAssetsService = TestBed.inject(AssetsService) as jasmine.SpyObj<AssetsService>;
    mockStore = TestBed.inject(Store) as jasmine.SpyObj<Store>;
  });

  // Service Initialization
  it('No.1: should be created successfully', () => {
    expect(effects).toBeTruthy();
  });

  // Personal Info Effects
  it('No.2: should handle getPersonalList$ effect successfully', (done) => {
    const mockPayload = {
      accountNumbers: ['ACC001'],
      brokerCode: ['BR001'],
      userType: 'individual',
      fromBirthYear: '1990',
      toBirthYear: '2000',
      searchKey: 'test'
    };
    const mockResponse: any[] = [
      {
        accountNumber: 'ACC001',
        name: 'Customer 1',
        customerName: 'Customer 1'
      }
    ];
    const action = CustomerActions.getCustomerPersonalInfo({
      payload: mockPayload,
      isSearchOrFilter: false
    });

    mockCustomerService.getPersonalInfoList.and.returnValue(of(mockResponse));
    actions$ = of(action);

    effects.getPersonalList$.subscribe(result => {
      expect(result.type).toBe('[Personal info] get personal info list success');
      expect(mockLoadingService.show).toHaveBeenCalled();
      expect(mockCustomerService.getPersonalInfoList).toHaveBeenCalledWith(mockPayload);

      // Use setTimeout to check loading.hide after finalize
      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  it('No.3: should handle getPersonalList$ effect with search/filter flag', () => {
    const mockPayload = {
      accountNumbers: ['ACC001'],
      brokerCode: ['BR001'],
      userType: 'individual',
      fromBirthYear: '1990',
      toBirthYear: '2000',
      searchKey: 'test'
    };
    const mockResponse: any[] = [{ accountNumber: 'ACC001', name: 'Customer 1' }];
    const action = CustomerActions.getCustomerPersonalInfo({
      payload: mockPayload,
      isSearchOrFilter: true
    });

    mockCustomerService.getPersonalInfoList.and.returnValue(of(mockResponse));
    actions$ = of(action);

    effects.getPersonalList$.subscribe(result => {
      expect(result.type).toBe('[Personal info] filtered data personal info');
    });
  });

  it('No.4: should handle getPersonalList$ effect error', () => {
    const mockPayload = {
      accountNumbers: ['ACC001'],
      brokerCode: ['BR001'],
      userType: 'individual',
      fromBirthYear: '1990',
      toBirthYear: '2000',
      searchKey: 'test'
    };
    const errorResponse = { error: { message: 'API Error' } };
    const action = CustomerActions.getCustomerPersonalInfo({
      payload: mockPayload,
      isSearchOrFilter: false
    });

    mockCustomerService.getPersonalInfoList.and.returnValue(throwError(() => errorResponse));
    actions$ = of(action);

    effects.getPersonalList$.subscribe(result => {
      expect(mockMessageService.error).toHaveBeenCalledWith('API Error');
      expect(mockLoadingService.hide).toHaveBeenCalled();
    });
  });

  // Bank Info Effects
  it('No.5: should handle getCustomerBankInfo$ effect successfully', (done) => {
    const mockId = 'broker123';
    const mockResponse = [
      {
        customerName: 'Customer 1',
        accountNumber: 'ACC001',
        accountType: 1,
        bankAccounts: []
      }
    ];
    const action = CustomerActions.getCustomersBankInfo({ id: mockId });

    mockCustomerService.getCustomerBankInfo.and.returnValue(of(mockResponse));
    actions$ = of(action);

    effects.getCustomerBankInfo$.subscribe(result => {
      expect(result.type).toBe('[Customer] get customers bank info success');
      expect(mockLoadingService.show).toHaveBeenCalled();
      expect(mockCustomerService.getCustomerBankInfo).toHaveBeenCalledWith(mockId);

      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  it('No.6: should handle getCustomerBankInfo$ effect with getAllDataCustomerInfo action', () => {
    const mockId = 'broker123';
    const mockResponse: any[] = [];
    const action = CustomerActions.getAllDataCustomerInfo({ id: mockId, username: 'user' });

    mockCustomerService.getCustomerBankInfo.and.returnValue(of(mockResponse));
    actions$ = of(action);

    effects.getCustomerBankInfo$.subscribe(result => {
      expect(result.type).toBe('[Customer] get customers bank info success');
      expect(mockCustomerService.getCustomerBankInfo).toHaveBeenCalledWith(mockId);
    });
  });

  it('No.7: should handle getCustomerBankInfo$ effect error', (done) => {
    const mockId = 'broker123';
    const errorResponse = { error: { message: 'Bank API Error' } };
    const action = CustomerActions.getCustomersBankInfo({ id: mockId });

    mockCustomerService.getCustomerBankInfo.and.returnValue(throwError(() => errorResponse));
    actions$ = of(action);

    effects.getCustomerBankInfo$.subscribe(() => {
      expect(mockMessageService.error).toHaveBeenCalledWith('Bank API Error');

      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  // Helper Methods
  it('No.17: should map customers correctly in mapCustomers method', () => {
    const mockMembers: IMembers[] = [
      {
        id: '1',
        name: 'Customer 1',
        accountNo: 'ACC001',
        isActive: true
      }
    ];

    const result = effects.mapCustomers(mockMembers);

    expect(result).toEqual([
      {
        id: '1',
        name: 'Customer 1',
        accountNo: 'ACC001',
        isActive: true,
        accountNumber: 'ACC001',
        customerName: 'Customer 1',
        isCallApiShowMore: true
      }
    ]);
  });

  it('No.18: should update customer group global state in updateCustomerGroupGlobal method', () => {
    const mockGroups: ICusomterGroup[] = [
      {
        id: '1',
        name: 'Group 1',
        isActive: true,
        brokerDebtId: [],
        brokerId: [],
        customers: [],
        members: [],
        groupCode: 'GRP001'
      },
      {
        id: '2',
        name: 'Group 2',
        isActive: false,
        brokerDebtId: [],
        brokerId: [],
        customers: [],
        members: [],
        groupCode: 'GRP002'
      }
    ];

    effects.updateCustomerGroupGlobal(mockGroups);

    expect(mockStore.dispatch).toHaveBeenCalledWith(
      jasmine.objectContaining({
        type: '[GLOBAL] get customer group info success'
      })
    );
  });

  it('No.19: should handle loading service calls correctly across all effects', (done) => {
    const mockId = 'test123';
    const mockResponse: any[] = [];
    const action = CustomerActions.getCustomersBankInfo({ id: mockId });

    mockCustomerService.getCustomerBankInfo.and.returnValue(of(mockResponse));
    actions$ = of(action);

    effects.getCustomerBankInfo$.subscribe(() => {
      expect(mockLoadingService.show).toHaveBeenCalled();

      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  // Additional Document Info Effects
  it('No.20: should handle getCustomerDocumentInfo$ effect with getAllDataCustomerInfo action', (done) => {
    const mockId = 'customer123';
    const mockResponse: any[] = [];
    const action = CustomerActions.getAllDataCustomerInfo({ id: mockId, username: 'user' });

    mockCustomerService.getCustomerDocumentInfo.and.returnValue(of(mockResponse));
    actions$ = of(action);

    effects.getCustomerDocumentInfo$.subscribe(result => {
      expect(result.type).toBe('[Customer] get customer document info success');
      expect(mockCustomerService.getCustomerDocumentInfo).toHaveBeenCalledWith(mockId);

      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  it('No.21: should handle getCustomerDocumentInfo$ effect error', (done) => {
    const mockId = 'customer123';
    const errorResponse = { error: { message: 'Document API Error' } };
    const action = CustomerActions.getCustomerDocumentInfo({ id: mockId });

    mockCustomerService.getCustomerDocumentInfo.and.returnValue(throwError(() => errorResponse));
    actions$ = of(action);

    effects.getCustomerDocumentInfo$.subscribe(() => {
      expect(mockMessageService.error).toHaveBeenCalledWith('Document API Error');

      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  // Additional Account Info Effects
  it('No.22: should handle getCustomerAccountInfo$ effect error', (done) => {
    const mockId = 'customer123';
    const errorResponse = { error: { message: 'Account API Error' } };
    const action = CustomerActions.getCustomerAccountInfo({ id: mockId });

    mockCustomerService.getCustomerAccountInfo.and.returnValue(throwError(() => errorResponse));
    actions$ = of(action);

    effects.getCustomerAccountInfo$.subscribe(() => {
      expect(mockMessageService.error).toHaveBeenCalledWith('Account API Error');

      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  it('No.23: should handle getCustomerAccountDetailInfo$ effect error', (done) => {
    const mockId = 'customer123';
    const errorResponse = { error: { message: 'Account Detail API Error' } };
    const action = CustomerActions.getCustomerAccountInfoDetail({ id: mockId });

    mockCustomerService.getCustomerAccountInfo.and.returnValue(throwError(() => errorResponse));
    actions$ = of(action);

    effects.getCustomerAccountDetailInfo$.subscribe(() => {
      expect(mockMessageService.error).toHaveBeenCalledWith('Account Detail API Error');

      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  // Additional Authority Info Effects
  it('No.24: should handle getAuthContractList$ effect with getAllDataCustomerInfo action', (done) => {
    const mockId = 'customer123';
    const mockResponse: any[] = [];
    const action = CustomerActions.getAllDataCustomerInfo({ id: mockId, username: 'user' });

    mockCustomerService.getAuthContractList.and.returnValue(of(mockResponse));
    actions$ = of(action);

    effects.getAuthContractList$.subscribe(result => {
      expect(result.type).toBe('[Authority info] Get List Authority Info By BrokerId Success');
      expect(mockCustomerService.getAuthContractList).toHaveBeenCalledWith(mockId);

      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  it('No.25: should handle getAuthContractList$ effect error', (done) => {
    const mockId = 'customer123';
    const errorResponse = { error: { message: 'Authority API Error' } };
    const action = CustomerActions.getCustomersAuthorityInfo({ id: mockId });

    mockCustomerService.getAuthContractList.and.returnValue(throwError(() => errorResponse));
    actions$ = of(action);

    effects.getAuthContractList$.subscribe(() => {
      expect(mockMessageService.error).toHaveBeenCalledWith('Authority API Error');

      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  // Additional Bank List Effects
  it('No.26: should handle getAllBankList$ effect error', (done) => {
    const errorResponse = { error: { message: 'Bank List API Error' } };
    const action = CustomerActions.getAllInfoBank();

    mockCustomerService.getAllBankList.and.returnValue(throwError(() => errorResponse));
    actions$ = of(action);

    effects.getAllBankList$.subscribe(() => {
      expect(mockMessageService.error).toHaveBeenCalledWith('Bank List API Error');

      setTimeout(() => {
        expect(mockLoadingService.hide).toHaveBeenCalled();
        done();
      }, 0);
    });
  });

  // Data Transformation Tests
  it('No.27: should transform personal info data correctly with id mapping', (done) => {
    const mockPayload = {
      accountNumbers: ['ACC001'],
      brokerCode: ['BR001'],
      userType: 'individual',
      fromBirthYear: '1990',
      toBirthYear: '2000',
      searchKey: 'test'
    };
    const mockResponse: any[] = [
      {
        accountNumber: 'ACC001',
        name: 'Customer 1'
      }
    ];
    const action = CustomerActions.getCustomerPersonalInfo({
      payload: mockPayload,
      isSearchOrFilter: false
    });

    mockCustomerService.getPersonalInfoList.and.returnValue(of(mockResponse));
    actions$ = of(action);

    effects.getPersonalList$.subscribe(result => {
      expect(result.type).toBe('[Personal info] get personal info list success');
      expect((result as any).data[0].id).toBe('ACC001');
      expect((result as any).data[0].accountNumber).toBe('ACC001');
      done();
    });
  });

  it('No.28: should transform customer group data correctly with additional properties', (done) => {
    const mockGroups: ICusomterGroup[] = [
      {
        id: 'group1',
        name: 'Group 1',
        brokerDebtId: [],
        brokerId: [],
        customers: [],
        members: [
          {
            id: 'member1',
            name: 'Member 1',
            accountNo: 'ACC001',
            isActive: true
          }
        ],
        groupCode: 'GRP001'
      }
    ];
    const action = CustomerActions.getCustomerGroupInfo({ brokerId: 'broker1' });

    mockStore.select.and.returnValue(of({ brokerCode: 'BR001' }));
    mockCustomerService.getCustomerGroupInfo.and.returnValue(of(mockGroups));
    actions$ = of(action);

    effects.getCustomerGroupInfo$.subscribe(result => {
      expect(result.type).toBe('[Customer] get customer group info success');
      const resultData = (result as any).data[0];
      expect(resultData.isCallAPIChildren).toBe(true);
      expect(resultData.customers[0].accountNumber).toBe('ACC001');
      expect(resultData.customers[0].customerName).toBe('Member 1');
      expect(resultData.customers[0].isCallApiShowMore).toBe(true);
      done();
    });
  });

  it('No.29: should handle empty members array in mapCustomers method', () => {
    const result = effects.mapCustomers([]);
    expect(result).toEqual([]);
  });

  it('No.30: should handle null/undefined members in mapCustomers method', () => {
    // Test with undefined - will throw error as expected since the method doesn't handle null checks
    expect(() => effects.mapCustomers(undefined as any)).toThrow();
    expect(() => effects.mapCustomers(null as any)).toThrow();
  });
});
