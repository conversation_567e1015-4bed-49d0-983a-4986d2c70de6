import { customerReducers, initialCustomerState } from './customer.reducer';
import * as CustomerActions from './customer.action';
import { ICustomerState, IFilterPersonalInfoParam, IFilterBankInfoParam } from '../model/customer';

describe('Customer Reducer', () => {
  // Initial State
  it('No.1: should return the initial state', () => {
    const action = { type: 'Unknown' } as any;
    const state = customerReducers(undefined, action);

    expect(state).toBe(initialCustomerState);
  });

  it('No.2: should have correct initial state structure', () => {
    expect(initialCustomerState).toEqual({
      searchValue: null,
      customerPersonalData: [],
      filterPersonalInfo: {
        customers: null,
        typeAccount: '',
        startYear: '',
        endYear: '',
        isFilter: false,
      },
      filteredPersonalData: [],
      pageIndexPersonalInfo: 1,
      filterBankInfo: {
        isFilter: false,
        typeAccount: [1, 2],
        bankIds: ['all'],
      },
      filteredBankData: [],
      customerBankData: [],
      filterDocumentInfo: {
        isFilter: false,
        typeAccount: [1, 2],
        contract00: [0, 1],
        contract01: [0, 1],
        contract02: [0, 1],
        contract03: [0, 1],
        contract04: [0, 1],
        contract05: [0, 1],
        contract80: [0, 1],
      },
      filteredDocumentData: [],
      customerDocumentData: [],
      filterAccountInfo: {
        isFilter: false,
        typeAccount: [1, 2],
        levelAccount: [],
        accountGroups: [],
        rooms: [],
      },
      filteredAccountData: [],
      customerAccountData: [],
      customerAccountDetailData: [],
      filterInterestRateInfo: {
        isFilter: false,
        typeAccount: [0, 1],
        levelAccount: [0, 1, 2, 3],
      },
      filteredInterestRateData: [],
      customerAuthorityData: [],
      filterAuthorityInfo: {
        isFilter: false,
        typeAccount: [0, 1],
        authorityStatus: [0, 1],
      },
      filteredAuthorityData: [],
      filterCustomerGroup: {
        isFilter: false,
        accountNumber: null,
      },
      filteredCustomerGroupData: [],
      customerGroupData: [],
      assetInfoByAccountNumber: [],
      listAllBankInfo: [],
    });
  });

  // Search Actions
  it('No.3: should handle search action', () => {
    const searchData = 'test search';
    const action = CustomerActions.search({ data: searchData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.searchValue).toBe(searchData);
    expect(state.pageIndexPersonalInfo).toBe(initialCustomerState.pageIndexPersonalInfo);
    expect(state.filterPersonalInfo).toEqual(initialCustomerState.filterPersonalInfo);
  });

  it('No.4: should handle resetSearch action', () => {
    const currentState: ICustomerState = {
      ...initialCustomerState,
      searchValue: 'existing search',
      pageIndexPersonalInfo: 3
    };
    const action = CustomerActions.resetSearch();
    const state = customerReducers(currentState, action);

    expect(state.searchValue).toBe(initialCustomerState.searchValue);
    expect(state.pageIndexPersonalInfo).toBe(initialCustomerState.pageIndexPersonalInfo);
  });

  // Personal Info Actions
  it('No.5: should handle getPersonalInfoListSuccess action', () => {
    const mockData = [
      {
        accountNumber: 'ACC001',
        name: 'Customer 1',
        customerName: 'Customer 1',
        accountType: 1,
        identity: 'ID001',
        identityDate: '2023-01-01',
        identifierNumber: 'IDN001',
        identityIssuer: 'Issuer',
        nationalityId: 'VN',
        idNumberType: 'CMND',
        idNumberTypeName: 'Chứng minh nhân dân',
        authorizedIdNumber: 'AUTH001',
        birthday: '1990-01-01',
        sexId: 1,
        telephone: '0*********',
        email: '<EMAIL>',
        address: 'Test Address',
        registrationNo: 'REG001',
        registrationDate: '2023-01-01',
        registrationRep: 'Rep',
        signatureImg: 'signature.jpg',
        accountLevelId: 'LEVEL1',
        accountOpenDate: '2023-01-01',
        accountCloseDate: '',
        accountLasttxnDate: '2023-12-01',
        brokerId: 'BR001',
        brokerName: 'Broker 1',
        brokerCode: 'BR001',
        saleGroupName: 'Sales 1',
        cusGroups: [],
        customerLevel: 'VIP',
        lastSyncDate: '2023-12-01',
        feeBasic: { value: 0.1, group: 'basic' },
        feeDerivative: { value: 0.2, group: 'derivative' },
        feeBond: { value: 0.15, group: 'bond' },
        subAccounts: []
      }
    ];
    const action = CustomerActions.getPersonalInfoListSuccess({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.customerPersonalData).toEqual(mockData);
  });

  it('No.6: should handle setFilterPersonalInfo action', () => {
    const params: IFilterPersonalInfoParam = {
      customers: ['CUST001'],
      typeAccount: 'individual',
      startYear: '1990',
      endYear: '2000',
      isFilter: true
    };
    const currentState: ICustomerState = {
      ...initialCustomerState,
      searchValue: 'existing search',
      pageIndexPersonalInfo: 3
    };
    const action = CustomerActions.setFilterPersonalInfo({ params });
    const state = customerReducers(currentState, action);

    expect(state.filterPersonalInfo).toEqual(params);
    expect(state.pageIndexPersonalInfo).toBe(initialCustomerState.pageIndexPersonalInfo);
    expect(state.searchValue).toBeNull();
  });

  it('No.7: should handle setFilteredDataPersonalInfo action', () => {
    const mockData = [
      {
        accountNumber: 'ACC001',
        name: 'Customer 1',
        customerName: 'Customer 1',
        accountType: 1
      } as any
    ];
    const action = CustomerActions.setFilteredDataPersonalInfo({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filteredPersonalData).toEqual(mockData);
  });

  it('No.8: should handle resetFilterPersonalInfo action', () => {
    const currentState: ICustomerState = {
      ...initialCustomerState,
      filterPersonalInfo: {
        customers: ['CUST001'],
        typeAccount: 'individual',
        startYear: '1990',
        endYear: '2000',
        isFilter: true
      },
      pageIndexPersonalInfo: 3,
      searchValue: 'existing search',
      customerPersonalData: [{ accountNumber: 'ACC001' } as any],
      filteredPersonalData: [{ accountNumber: 'ACC002' } as any]
    };
    const action = CustomerActions.resetFilterPersonalInfo();
    const state = customerReducers(currentState, action);

    expect(state.filterPersonalInfo).toEqual(initialCustomerState.filterPersonalInfo);
    expect(state.pageIndexPersonalInfo).toBe(initialCustomerState.pageIndexPersonalInfo);
    expect(state.searchValue).toBeNull();
    expect(state.customerPersonalData).toEqual([]);
    expect(state.filteredPersonalData).toEqual([]);
  });

  it('No.9: should handle updatePageIndexPersonalInfo action', () => {
    const pageIndex = 5;
    const action = CustomerActions.updatePageIndexPersonalInfo({ pageIndex });
    const state = customerReducers(initialCustomerState, action);

    expect(state.pageIndexPersonalInfo).toBe(pageIndex);
  });

  // Bank Info Actions
  it('No.10: should handle setFilterBankInfo action', () => {
    const params: IFilterBankInfoParam = {
      typeAccount: [1],
      bankIds: ['bank1', 'bank2'],
      isFilter: true
    };
    const action = CustomerActions.setFilterBankInfo({ params });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filterBankInfo).toEqual(params);
  });

  it('No.11: should handle setFilteredDataBankInfo action', () => {
    const mockData = [{ bankName: 'Bank 1' }, { bankName: 'Bank 2' }];
    const action = CustomerActions.setFilteredDataBankInfo({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filteredBankData).toEqual(mockData);
  });

  it('No.12: should handle resetFilterBankInfo action', () => {
    const currentState: ICustomerState = {
      ...initialCustomerState,
      filterBankInfo: {
        typeAccount: [1],
        bankIds: ['bank1'],
        isFilter: true
      }
    };
    const action = CustomerActions.resetFilterBankInfo();
    const state = customerReducers(currentState, action);

    expect(state.filterBankInfo).toEqual(initialCustomerState.filterBankInfo);
  });

  it('No.13: should handle getCustomersBankInfoSuccess action', () => {
    const mockData = [
      {
        customerName: 'Customer 1',
        accountNumber: 'ACC001',
        accountType: 1,
        bankAccounts: []
      }
    ];
    const action = CustomerActions.getCustomersBankInfoSuccess({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.customerBankData).toEqual(mockData);
  });

  // Document Info Actions
  it('No.14: should handle setFilterDocumentInfo action', () => {
    const params = {
      isFilter: true,
      typeAccount: [1],
      contract00: [1],
      contract01: [0]
    };
    const action = CustomerActions.setFilterDocumentInfo({ params });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filterDocumentInfo).toEqual(params);
  });

  it('No.15: should handle getCustomerDocumentInfoSuccess action', () => {
    const mockData = [
      {
        customerName: 'Customer 1',
        accountNumber: 'ACC001',
        accountType: 1,
        identification: {},
        registrationNo: 'REG001',
        registration: {},
        signature: {},
        contracts: []
      } as any
    ];
    const action = CustomerActions.getCustomerDocumentInfoSuccess({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.customerDocumentData).toEqual(mockData);
  });

  it('No.16: should handle setFilteredDataDocumentInfo action', () => {
    const mockData = [{ documentType: 'Contract' }];
    const action = CustomerActions.setFilteredDataDocumentInfo({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filteredDocumentData).toEqual(mockData);
  });

  it('No.17: should handle resetFilterDocumentInfo action', () => {
    const currentState: ICustomerState = {
      ...initialCustomerState,
      filterDocumentInfo: {
        isFilter: true,
        typeAccount: [1],
        contract00: [1]
      }
    };
    const action = CustomerActions.resetFilterDocumentInfo();
    const state = customerReducers(currentState, action);

    expect(state.filterDocumentInfo).toEqual(initialCustomerState.filterDocumentInfo);
  });

  // Account Info Actions
  it('No.18: should handle getCustomerAccountInfoSuccess action', () => {
    const mockData = [
      {
        accountNumber: 'ACC001',
        customerName: 'Customer 1',
        customerLevel: 'VIP',
        customerGroup: 'Group1',
        brokerName: 'Broker 1',
        brokerCode: 'BR001',
        saleGroupName: 'Sales 1',
        children: []
      }
    ];
    const action = CustomerActions.getCustomerAccountInfoSuccess({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.customerAccountData).toEqual(mockData);
  });

  it('No.19: should handle getCustomerAccountInfoDetailSuccess action', () => {
    const mockData = [
      {
        accountNumber: 'ACC001',
        customerName: 'Customer 1',
        customerLevel: 'VIP',
        customerGroup: 'Group1',
        brokerName: 'Broker 1',
        brokerCode: 'BR001',
        saleGroupName: 'Sales 1',
        children: []
      }
    ];
    const action = CustomerActions.getCustomerAccountInfoDetailSuccess({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.customerAccountDetailData).toEqual(mockData);
  });

  // Additional Account Info Actions
  it('No.20: should handle setFilterAccountInfo action', () => {
    const params = {
      isFilter: true,
      typeAccount: [1],
      levelAccount: ['VIP'],
      accountGroups: ['Group1'],
      rooms: ['Room1']
    };
    const action = CustomerActions.setFilterAccountInfo({ params });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filterAccountInfo).toEqual(params);
  });

  it('No.21: should handle setFilteredDataAccountInfo action', () => {
    const mockData = [{ accountNumber: 'ACC001', customerName: 'Customer 1' }];
    const action = CustomerActions.setFilteredDataAccountInfo({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filteredAccountData).toEqual(mockData);
  });

  it('No.22: should handle resetFilterAccountInfo action', () => {
    const currentState: ICustomerState = {
      ...initialCustomerState,
      filterAccountInfo: {
        isFilter: true,
        typeAccount: [1],
        levelAccount: ['VIP'],
        accountGroups: ['Group1'],
        rooms: ['Room1']
      }
    };
    const action = CustomerActions.resetFilterAccountInfo();
    const state = customerReducers(currentState, action);

    expect(state.filterAccountInfo).toEqual(initialCustomerState.filterAccountInfo);
  });

  // Interest Rate Actions
  it('No.23: should handle setFilterInterestRateInfo action', () => {
    const params = {
      isFilter: true,
      typeAccount: [1],
      levelAccount: [1, 2]
    };
    const action = CustomerActions.setFilterInterestRateInfo({ params });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filterInterestRateInfo).toEqual(params);
  });

  it('No.24: should handle setFilteredDataInterestRateInfo action', () => {
    const mockData = [{ rate: 5.5, accountType: 1 }];
    const action = CustomerActions.setFilteredDataInterestRateInfo({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filteredInterestRateData).toEqual(mockData);
  });

  it('No.25: should handle resetFilterInterestRateInfo action', () => {
    const currentState: ICustomerState = {
      ...initialCustomerState,
      filterInterestRateInfo: {
        isFilter: true,
        typeAccount: [1],
        levelAccount: [1, 2]
      }
    };
    const action = CustomerActions.resetFilterInterestRateInfo();
    const state = customerReducers(currentState, action);

    expect(state.filterInterestRateInfo).toEqual(initialCustomerState.filterInterestRateInfo);
  });

  // Authority Actions
  it('No.26: should handle getCustomersAuthorityInfoSuccess action', () => {
    const mockData = [
      {
        id: '1',
        accountNumber: 'ACC001',
        customerName: 'Customer 1',
        authPerson: 'Auth Person',
        authTelephone: '*********',
        authEmail: '<EMAIL>',
        authAddress: 'Auth Address',
        contractNo: 'CONTRACT001',
        accountSignature: 'signature1',
        authSignature: 'signature2',
        authIdentity: 'ID001',
        authIdentityDate: '2023-01-01',
        authIdentityIssuer: 'Issuer',
        authFromDate: '2023-01-01',
        authToDate: '2024-01-01',
        authAll: true,
        authCommand: false,
        authMoney: true,
        authAdvance: false,
        authDepository: true,
        authFinanceService: false,
        authStock: true,
        authStatus: 'Active',
        contractImg: 'contract.jpg',
        accountSignatureImg: 'acc_sig.jpg',
        authSignatureImg: 'auth_sig.jpg',
        authIdentityImg: 'auth_id.jpg'
      }
    ];
    const action = CustomerActions.getCustomersAuthorityInfoSuccess({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.customerAuthorityData).toEqual(mockData);
  });

  it('No.27: should handle setFilterAuthorityInfo action', () => {
    const params = {
      isFilter: true,
      typeAccount: [1],
      authorityStatus: [1]
    };
    const action = CustomerActions.setFilterAuthorityInfo({ params });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filterAuthorityInfo).toEqual(params);
  });

  it('No.28: should handle setFilteredDataAuthorityInfo action', () => {
    const mockData = [{ authType: 'Full', status: 'Active' }];
    const action = CustomerActions.setFilteredDataAuthorityInfo({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filteredAuthorityData).toEqual(mockData);
  });

  it('No.29: should handle resetFilterAuthorityInfo action', () => {
    const currentState: ICustomerState = {
      ...initialCustomerState,
      filterAuthorityInfo: {
        isFilter: true,
        typeAccount: [1],
        authorityStatus: [1]
      }
    };
    const action = CustomerActions.resetFilterAuthorityInfo();
    const state = customerReducers(currentState, action);

    expect(state.filterAuthorityInfo).toEqual(initialCustomerState.filterAuthorityInfo);
  });

  // Customer Group Actions
  it('No.30: should handle setFilterCustomerGroup action', () => {
    const params = {
      isFilter: true,
      accountNumber: ['ACC001', 'ACC002']
    };
    const action = CustomerActions.setFilterCustomerGroup({ params });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filterCustomerGroup).toEqual(params);
  });

  it('No.31: should handle setFilteredDataCustomerGroup action', () => {
    const mockData = [{ groupName: 'Group 1', memberCount: 5 }];
    const action = CustomerActions.setFilteredDataCustomerGroup({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filteredCustomerGroupData).toEqual(mockData);
  });

  it('No.32: should handle resetFilterCustomerGroup action', () => {
    const currentState: ICustomerState = {
      ...initialCustomerState,
      filterCustomerGroup: {
        isFilter: true,
        accountNumber: ['ACC001']
      }
    };
    const action = CustomerActions.resetFilterCustomerGroup();
    const state = customerReducers(currentState, action);

    expect(state.filterCustomerGroup).toEqual(initialCustomerState.filterCustomerGroup);
  });

  it('No.33: should handle getCustomerGroupInfoSuccess action', () => {
    const mockData = [
      {
        id: 'group1',
        name: 'Group 1',
        brokerDebtId: ['debt1'],
        brokerId: ['broker1'],
        customers: [],
        members: [],
        groupCode: 'GRP001'
      }
    ];
    const action = CustomerActions.getCustomerGroupInfoSuccess({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.customerGroupData).toEqual(mockData);
  });

  it('No.34: should handle getAllInfoBankSuccess action', () => {
    const mockData = [
      {
        id: 'bank1',
        name: 'Bank 1',
        bankName: 'Bank Name 1',
        shortName: 'BN1',
        logo: 'logo1.jpg'
      }
    ];
    const action = CustomerActions.getAllInfoBankSuccess({ data: mockData });
    const state = customerReducers(initialCustomerState, action);

    expect(state.listAllBankInfo).toEqual(mockData);
  });

  // State Immutability Tests
  it('No.35: should maintain state immutability for all actions', () => {
    const originalState = { ...initialCustomerState };
    const action = CustomerActions.search({ data: 'test' });
    const newState = customerReducers(originalState, action);

    expect(newState).not.toBe(originalState);
    expect(originalState.searchValue).toBeNull();
    expect(newState.searchValue).toBe('test');
  });

  it('No.36: should handle array mutations immutably', () => {
    const originalData: any[] = [{ accountNumber: 'ACC001' }];
    const currentState: ICustomerState = {
      ...initialCustomerState,
      customerPersonalData: originalData
    };

    const newData: any[] = [{ accountNumber: 'ACC002' }];
    const action = CustomerActions.getPersonalInfoListSuccess({ data: newData as any });
    const newState = customerReducers(currentState, action);

    expect(newState.customerPersonalData).not.toBe(originalData);
    expect(newState.customerPersonalData).toEqual(newData);
    expect(originalData).toEqual([{ accountNumber: 'ACC001' }]);
  });

  // Edge Cases
  it('No.37: should handle empty data arrays correctly', () => {
    const action = CustomerActions.getPersonalInfoListSuccess({ data: [] });
    const state = customerReducers(initialCustomerState, action);

    expect(state.customerPersonalData).toEqual([]);
  });

  it('No.38: should handle null filter parameters', () => {
    const params = {
      customers: null,
      typeAccount: '',
      startYear: '',
      endYear: '',
      isFilter: false
    };
    const action = CustomerActions.setFilterPersonalInfo({ params });
    const state = customerReducers(initialCustomerState, action);

    expect(state.filterPersonalInfo.customers).toBeNull();
  });

  it('No.39: should reset multiple properties simultaneously', () => {
    const currentState: ICustomerState = {
      ...initialCustomerState,
      searchValue: 'test search',
      pageIndexPersonalInfo: 5,
      customerPersonalData: [{ accountNumber: 'ACC001' } as any],
      filteredPersonalData: [{ accountNumber: 'ACC002' } as any]
    };

    const action = CustomerActions.resetFilterPersonalInfo();
    const state = customerReducers(currentState, action);

    expect(state.searchValue).toBeNull();
    expect(state.pageIndexPersonalInfo).toBe(1);
    expect(state.customerPersonalData).toEqual([]);
    expect(state.filteredPersonalData).toEqual([]);
    expect(state.filterPersonalInfo).toEqual(initialCustomerState.filterPersonalInfo);
  });
});
