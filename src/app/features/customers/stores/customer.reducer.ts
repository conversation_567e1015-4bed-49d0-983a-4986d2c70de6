import { createReducer, on } from '@ngrx/store';
import * as CustomerAction from './customer.action';
import { ICustomerState } from '../model/customer';

export const initialCustomerState: ICustomerState = {
  searchValue: null,
  // Personal
  customerPersonalData: [],
  filterPersonalInfo: {
    customers: null,
    typeAccount: '',
    startYear: '',
    endYear: '',
    isFilter: false,
  },
  filteredPersonalData: [],
  pageIndexPersonalInfo: 1,

  // Bank
  filterBankInfo: {
    isFilter: false,
    typeAccount: [1, 2],
    bankIds: ['all'],
  },
  filteredBankData: [],
  customerBankData: [],

  // Document
  filterDocumentInfo: {
    isFilter: false,
    typeAccount: [1, 2],
    contract00: [0, 1],
    contract01: [0, 1],
    contract02: [0, 1],
    contract03: [0, 1],
    contract04: [0, 1],
    contract05: [0, 1],
    contract80: [0, 1],
  },
  filteredDocumentData: [],
  customerDocumentData: [],

  // Account
  filterAccountInfo: {
    isFilter: false,
    typeAccount: [1, 2],
    levelAccount: [],
    accountGroups: [],
    rooms: [],
  },
  filteredAccountData: [],
  customerAccountData: [],
  customerAccountDetailData: [],

  // InterestRate
  filterInterestRateInfo: {
    isFilter: false,
    typeAccount: [0, 1],
    levelAccount: [0, 1, 2, 3],
  },
  filteredInterestRateData: [],

  // Authority
  customerAuthorityData: [],
  filterAuthorityInfo: {
    isFilter: false,
    typeAccount: [0, 1],
    authorityStatus: [0, 1],
  },
  filteredAuthorityData: [],

  // Customer Group
  filterCustomerGroup: {
    isFilter: false,
    accountNumber: null,
    // brokenRoom: [],
    // broker: [],
  },
  filteredCustomerGroupData: [],
  customerGroupData: [],
  assetInfoByAccountNumber: [],

  listAllBankInfo: [],
};

export const customerReducers = createReducer<ICustomerState>(
  initialCustomerState,

  on(
    CustomerAction.search,
    (state, action): ICustomerState => ({
      ...state,
      searchValue: action.data,
      pageIndexPersonalInfo: initialCustomerState.pageIndexPersonalInfo,
      filterPersonalInfo: initialCustomerState.filterPersonalInfo,
    })
  ),

  on(
    CustomerAction.resetSearch,
    (state): ICustomerState => ({
      ...state,
      searchValue: initialCustomerState.searchValue,
      pageIndexPersonalInfo: initialCustomerState.pageIndexPersonalInfo,
    })
  ),
  // PERSONAL
  on(
    CustomerAction.getPersonalInfoListSuccess,
    (state, action): ICustomerState => ({
      ...state,
      customerPersonalData: action.data,
    })
  ),
  on(
    CustomerAction.setFilterPersonalInfo,
    (state, action): ICustomerState => ({
      ...state,
      filterPersonalInfo: { ...action.params },
      pageIndexPersonalInfo: initialCustomerState.pageIndexPersonalInfo,
      searchValue: null,
    })
  ),

  on(
    CustomerAction.setFilteredDataPersonalInfo,
    (state, action): ICustomerState => ({
      ...state,
      filteredPersonalData: [...action.data],
    })
  ),

  on(
    CustomerAction.resetFilterPersonalInfo,
    (state): ICustomerState => ({
      ...state,
      filterPersonalInfo: {
        ...initialCustomerState.filterPersonalInfo,
      },
      pageIndexPersonalInfo: initialCustomerState.pageIndexPersonalInfo,
      searchValue: null,
      customerPersonalData: [],
      filteredPersonalData: [],
    })
  ),

  on(
    CustomerAction.updatePageIndexPersonalInfo,
    (state, action): ICustomerState => ({
      ...state,
      pageIndexPersonalInfo: action.pageIndex,
    })
  ),

  // BANK
  on(
    CustomerAction.setFilterBankInfo,
    (state, action): ICustomerState => ({
      ...state,
      filterBankInfo: { ...action.params },
    })
  ),

  on(
    CustomerAction.setFilteredDataBankInfo,
    (state, action): ICustomerState => ({
      ...state,
      filteredBankData: [...action.data],
    })
  ),

  on(
    CustomerAction.resetFilterBankInfo,
    (state): ICustomerState => ({
      ...state,
      filterBankInfo: {
        ...initialCustomerState.filterBankInfo,
      },
    })
  ),

  on(
    CustomerAction.getCustomersBankInfoSuccess,
    (state, action): ICustomerState => ({
      ...state,
      customerBankData: action.data,
    })
  ),

  // DOCUMENT
  on(
    CustomerAction.setFilterDocumentInfo,
    (state, action): ICustomerState => ({
      ...state,
      filterDocumentInfo: { ...action.params },
    })
  ),

  on(
    CustomerAction.getCustomerDocumentInfoSuccess,
    (state, action): ICustomerState => ({
      ...state,
      customerDocumentData: action.data,
    })
  ),

  on(
    CustomerAction.setFilteredDataDocumentInfo,
    (state, action): ICustomerState => ({
      ...state,
      filteredDocumentData: [...action.data],
    })
  ),

  on(
    CustomerAction.resetFilterDocumentInfo,
    (state): ICustomerState => ({
      ...state,
      filterDocumentInfo: {
        ...initialCustomerState.filterDocumentInfo,
      },
    })
  ),

  // ACCOUNT
  on(
    CustomerAction.setFilterAccountInfo,
    (state, action): ICustomerState => ({
      ...state,
      filterAccountInfo: { ...action.params },
    })
  ),

  on(
    CustomerAction.setFilteredDataAccountInfo,
    (state, action): ICustomerState => ({
      ...state,
      filteredAccountData: [...action.data],
    })
  ),

  on(
    CustomerAction.resetFilterAccountInfo,
    (state): ICustomerState => ({
      ...state,
      filterAccountInfo: {
        ...initialCustomerState.filterAccountInfo,
      },
    })
  ),

  on(
    CustomerAction.getCustomerAccountInfoSuccess,
    (state, action): ICustomerState => ({
      ...state,
      customerAccountData: action.data,
    })
  ),

  on(
    CustomerAction.getCustomerAccountInfoDetailSuccess,
    (state, action): ICustomerState => ({
      ...state,
      customerAccountDetailData: action.data,
    })
  ),

  // INTEREST-RATE
  on(
    CustomerAction.setFilterInterestRateInfo,
    (state, action): ICustomerState => ({
      ...state,
      filterInterestRateInfo: { ...action.params },
    })
  ),

  on(
    CustomerAction.setFilteredDataInterestRateInfo,
    (state, action): ICustomerState => ({
      ...state,
      filteredInterestRateData: [...action.data],
    })
  ),

  on(
    CustomerAction.resetFilterInterestRateInfo,
    (state): ICustomerState => ({
      ...state,
      filterInterestRateInfo: {
        ...initialCustomerState.filterInterestRateInfo,
      },
    })
  ),

  // AUTHORITY
  on(
    CustomerAction.getCustomersAuthorityInfoSuccess,
    (state, action): ICustomerState => ({
      ...state,
      customerAuthorityData: action.data,
    })
  ),
  on(
    CustomerAction.setFilterAuthorityInfo,
    (state, action): ICustomerState => ({
      ...state,
      filterAuthorityInfo: { ...action.params },
    })
  ),

  on(
    CustomerAction.setFilteredDataAuthorityInfo,
    (state, action): ICustomerState => ({
      ...state,
      filteredAuthorityData: [...action.data],
    })
  ),

  on(
    CustomerAction.resetFilterAuthorityInfo,
    (state): ICustomerState => ({
      ...state,
      filterAuthorityInfo: {
        ...initialCustomerState.filterAuthorityInfo,
      },
    })
  ),

  // CUSTOMER GROUP
  on(
    CustomerAction.setFilterCustomerGroup,
    (state, action): ICustomerState => ({
      ...state,
      filterCustomerGroup: { ...action.params },
    })
  ),

  on(
    CustomerAction.setFilteredDataCustomerGroup,
    (state, action): ICustomerState => ({
      ...state,
      filteredCustomerGroupData: [...action.data],
    })
  ),

  on(
    CustomerAction.resetFilterCustomerGroup,
    (state): ICustomerState => ({
      ...state,
      filterCustomerGroup: {
        ...initialCustomerState.filterCustomerGroup,
      },
    })
  ),

  on(
    CustomerAction.getCustomerGroupInfoSuccess,
    (state, action): ICustomerState => ({
      ...state,
      customerGroupData: action.data,
    })
  ),

  on(
    CustomerAction.getAllInfoBankSuccess,
    (state, action): ICustomerState => ({
      ...state,
      listAllBankInfo: action.data,
    })
  )
);
