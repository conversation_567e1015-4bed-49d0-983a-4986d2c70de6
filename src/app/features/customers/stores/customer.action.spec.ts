import * as CustomerActions from './customer.action';
import {
  IFilterPersonalInfoParam,
  IFilterBankInfoParam,
  ICustomerDataResponse,
  ICustomerBank,
  ICusomterGroup,
} from '../model/customer';
import { IPayloadPersonalList } from 'src/app/shared/models/global';

describe('Customer Actions', () => {
  // Search Actions
  it('No.1: should create search action with data payload', () => {
    const data = 'test search';
    const action = CustomerActions.search({ data });

    expect(action.type).toBe('[Customer] search');
    expect(action.data).toBe(data);
  });

  it('No.2: should create resetSearch action without payload', () => {
    const action = CustomerActions.resetSearch();

    expect(action.type).toBe('[Customer] reset search');
    expect(action).toEqual({ type: '[Customer] reset search' });
  });

  // Personal Info Actions
  it('No.3: should create getCustomerPersonalInfo action with payload and flag', () => {
    const payload: IPayloadPersonalList = {
      accountNumbers: ['ACC001'],
      brokerCode: ['BR001'],
      userType: 'individual',
      fromBirthYear: '1990',
      toBirthYear: '2000',
      searchKey: 'test'
    };
    const isSearchOrFilter = true;
    const action = CustomerActions.getCustomerPersonalInfo({ payload, isSearchOrFilter });

    expect(action.type).toBe('[Personal info] Get List Personal Info');
    expect(action.payload).toEqual(payload);
    expect(action.isSearchOrFilter).toBe(isSearchOrFilter);
  });

  it('No.4: should create getPersonalInfoListSuccess action with data', () => {
    const data: ICustomerDataResponse[] = [
      {
        accountNumber: 'ACC001',
        name: 'Customer 1',
        customerName: 'Customer 1',
        accountType: 1,
        identity: 'ID001',
        identityDate: '2023-01-01',
        identifierNumber: 'IDN001',
        identityIssuer: 'Issuer',
        nationalityId: 'VN',
        idNumberType: 'CMND',
        idNumberTypeName: 'Chứng minh nhân dân',
        authorizedIdNumber: 'AUTH001',
        birthday: '1990-01-01',
        sexId: 1,
        telephone: '**********',
        email: '<EMAIL>',
        address: 'Test Address',
        registrationNo: 'REG001',
        registrationDate: '2023-01-01',
        registrationRep: 'Rep',
        signatureImg: 'signature.jpg',
        accountLevelId: 'LEVEL1',
        accountOpenDate: '2023-01-01',
        accountCloseDate: '',
        accountLasttxnDate: '2023-12-01',
        brokerId: 'BR001',
        brokerName: 'Broker 1',
        brokerCode: 'BR001',
        saleGroupName: 'Sales 1',
        cusGroups: [],
        customerLevel: 'VIP',
        lastSyncDate: '2023-12-01',
        feeBasic: { value: 0.1, group: 'basic' },
        feeDerivative: { value: 0.2, group: 'derivative' },
        feeBond: { value: 0.15, group: 'bond' },
        subAccounts: []
      }
    ];
    const action = CustomerActions.getPersonalInfoListSuccess({ data });

    expect(action.type).toBe('[Personal info] get personal info list success');
    expect(action.data).toEqual(data);
  });

  it('No.5: should create setFilterPersonalInfo action with params', () => {
    const params: IFilterPersonalInfoParam = {
      customers: ['CUST001'],
      typeAccount: 'individual',
      startYear: '1990',
      endYear: '2000',
      isFilter: true
    };
    const action = CustomerActions.setFilterPersonalInfo({ params });

    expect(action.type).toBe('[Personal info] set filter personal info');
    expect(action.params).toEqual(params);
  });

  it('No.6: should create setFilteredDataPersonalInfo action with data', () => {
    const data: ICustomerDataResponse[] = [];
    const action = CustomerActions.setFilteredDataPersonalInfo({ data });

    expect(action.type).toBe('[Personal info] filtered data personal info');
    expect(action.data).toEqual(data);
  });

  it('No.7: should create resetFilterPersonalInfo action without payload', () => {
    const action = CustomerActions.resetFilterPersonalInfo();

    expect(action.type).toBe('[Personal info] reset filter personal info default');
    expect(action).toEqual({ type: '[Personal info] reset filter personal info default' });
  });

  it('No.8: should create updatePageIndexPersonalInfo action with pageIndex', () => {
    const pageIndex = 2;
    const action = CustomerActions.updatePageIndexPersonalInfo({ pageIndex });

    expect(action.type).toBe('[Personal info] update page index personal info ');
    expect(action.pageIndex).toBe(pageIndex);
  });

  // Bank Info Actions
  it('No.9: should create setFilterBankInfo action with params', () => {
    const params: IFilterBankInfoParam = {
      typeAccount: [1, 2],
      bankIds: ['bank1', 'bank2'],
      isFilter: true
    };
    const action = CustomerActions.setFilterBankInfo({ params });

    expect(action.type).toBe('[Bank info] set filter bank info');
    expect(action.params).toEqual(params);
  });

  it('No.10: should create setFilteredDataBankInfo action with data', () => {
    const data: any[] = [{ test: 'data' }];
    const action = CustomerActions.setFilteredDataBankInfo({ data });

    expect(action.type).toBe('[Bank info] filtered data bank info');
    expect(action.data).toEqual(data);
  });

  it('No.11: should create resetFilterBankInfo action without payload', () => {
    const action = CustomerActions.resetFilterBankInfo();

    expect(action.type).toBe('[Bank info] reset filter bank info default');
    expect(action).toEqual({ type: '[Bank info] reset filter bank info default' });
  });

  // API-related Actions
  it('No.12: should create getCustomersBankInfo action with id', () => {
    const id = 'broker123';
    const action = CustomerActions.getCustomersBankInfo({ id });

    expect(action.type).toBe('[Customer] get customers bank info');
    expect(action.id).toBe(id);
  });

  it('No.13: should create getCustomersBankInfoSuccess action with data', () => {
    const data: ICustomerBank[] = [
      {
        customerName: 'Customer 1',
        accountNumber: 'ACC001',
        accountType: 1,
        bankAccounts: []
      }
    ];
    const action = CustomerActions.getCustomersBankInfoSuccess({ data });

    expect(action.type).toBe('[Customer] get customers bank info success');
    expect(action.data).toEqual(data);
  });

  it('No.14: should create getCustomerGroupInfo action with optional brokerId', () => {
    const brokerId = 'broker123';
    const action = CustomerActions.getCustomerGroupInfo({ brokerId });

    expect(action.type).toBe('[Customer] get customer group info');
    expect(action.brokerId).toBe(brokerId);
  });

  it('No.15: should create getCustomerGroupInfoSuccess action with data', () => {
    const data: ICusomterGroup[] = [
      {
        id: 'group1',
        name: 'Group 1',
        brokerDebtId: [],
        brokerId: [],
        customers: [],
        members: [],
        groupCode: 'GRP001'
      }
    ];
    const action = CustomerActions.getCustomerGroupInfoSuccess({ data });

    expect(action.type).toBe('[Customer] get customer group info success');
    expect(action.data).toEqual(data);
  });

  // Error Handling Actions
  it('No.16: should create callApiError action without payload', () => {
    const action = CustomerActions.callApiError();

    expect(action.type).toBe('[Customer] call api error');
    expect(action).toEqual({ type: '[Customer] call api error' });
  });

  // Action Type Verification
  it('No.17: should have unique action types for all actions', () => {
    const actionTypes = [
      CustomerActions.search({ data: '' }).type,
      CustomerActions.resetSearch().type,
      CustomerActions.getCustomerPersonalInfo({ payload: {} as any, isSearchOrFilter: false }).type,
      CustomerActions.getPersonalInfoListSuccess({ data: [] }).type,
      CustomerActions.setFilterPersonalInfo({ params: {} as any }).type,
      CustomerActions.callApiError().type
    ];

    const uniqueTypes = new Set(actionTypes);
    expect(uniqueTypes.size).toBe(actionTypes.length);
  });

  it('No.18: should follow consistent naming convention for action types', () => {
    const action = CustomerActions.search({ data: 'test' });
    expect(action.type).toMatch(/^\[.+\] .+$/);
  });

  it('No.19: should have correct payload structure for actions with props', () => {
    const searchAction = CustomerActions.search({ data: 'test' });
    const personalInfoAction = CustomerActions.getCustomerPersonalInfo({
      payload: {} as any,
      isSearchOrFilter: true
    });

    expect(searchAction.data).toBeDefined();
    expect(personalInfoAction.payload).toBeDefined();
    expect(personalInfoAction.isSearchOrFilter).toBeDefined();
  });
});
