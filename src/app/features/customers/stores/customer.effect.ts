import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { LoadingService, MessageService } from 'src/app/core/services';
import { CustomerService } from '../services/customer.service';
import {
  getAllDataCustomerInfo,
  getAllInfoBank,
  getAllInfoBankSuccess,
  getCustomerAccountInfoDetail,
  getCustomerAccountInfoDetailSuccess,
  getCustomerPersonalInfo,
  getPersonalInfoListSuccess,
  setFilteredDataPersonalInfo,
  getCustomerAccountInfo,
  getCustomerAccountInfoSuccess,
  getCustomerDocumentInfo,
  getCustomerDocumentInfoSuccess,
  getCustomerGroupInfo,
  getCustomerGroupInfoSuccess,
  getCustomersBankInfo,
  getCustomersBankInfoSuccess,
  getCustomersAuthorityInfo,
  getCustomersAuthorityInfoSuccess,
} from './customer.action';
import { catchError, finalize, map, of, switchMap, tap } from 'rxjs';
import { getCustomerGroupGlobalInfoSuccess } from 'src/app/stores/shared/shared.actions';
import { concatLatestFrom } from '@ngrx/operators';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { AssetsService } from '../../assets/services/assets.service';
import { ICusomterGroup, IMembers } from '../model/customer';

/**
 * CustomersEffects
 */
@Injectable()
export class CustomersEffects {
  constructor(
    private readonly actions$: Actions,
    private readonly loadingService: LoadingService,
    private readonly store: Store,
    private readonly messageService: MessageService,
    private readonly customerService: CustomerService,
    private readonly assetService: AssetsService
  ) { }

  getPersonalList$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getCustomerPersonalInfo),
      tap(() => this.loadingService.show()),
      switchMap(({ payload, isSearchOrFilter }) => {
        // Get list chỉ gọi 20 accountNumber
        // const filterOrSearchAction = !!searchKey;
        return this.customerService.getPersonalInfoList(payload).pipe(
          finalize(() => this.loadingService.hide()),
          map((res) => ({ res, isSearchOrFilter }))
        );
      }),
      map(({ res, isSearchOrFilter }) => {
        const customRes = res.map((r) => ({
          ...r,
          id: r.accountNumber,
        }));
        return isSearchOrFilter
          ? setFilteredDataPersonalInfo({ data: customRes })
          : getPersonalInfoListSuccess({ data: customRes });
      }),
      catchError((error) => {
        this.messageService.error(error.error.message);
        return of(error);
      })
    );
  });

  getCustomerBankInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getCustomersBankInfo, getAllDataCustomerInfo),
      tap(() => this.loadingService.show()),
      switchMap(({ id }) => {
        return this.customerService.getCustomerBankInfo(id).pipe(
          map((res) => {
            return getCustomersBankInfoSuccess({ data: res });
          }),
          catchError((error) => {
            this.messageService.error(error.error.message);
            return of(error);
          }),
          finalize(() => this.loadingService.hide())
        );
      })
    );
  });

  getCustomerGroupInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getCustomerGroupInfo),
      concatLatestFrom(() => [this.store.select(selectCurrentBrokerView$)]),
      tap(() => this.loadingService.show()),
      switchMap(([{ brokerId }, broker]) => {
        const { brokerCode } = broker;
        return this.customerService.getCustomerGroupInfo(brokerId ?? brokerCode).pipe(
          map((res) => {
            // const convertData = res.map((d) => ({
            //   ...d,
            //   isCallAPIChildren: true,
            //   customers: this.mapCustomers(d.members),
            // }));
            // this.updateCustomerGroupGlobal(res);
            return getCustomerGroupInfoSuccess({ data: res });
          }),
          catchError((error) => {
            this.messageService.error(error.error.message);
            return of(error);
          }),
          // finalize(() => this.loadingService.hide())
        );
      })
    );
  });

  mapCustomers(members: IMembers[]) {
    return members.map((c) => ({
      ...c,
      accountNumber: c.accountNo,
      customerName: c.name,
      isCallApiShowMore: true,
    }));
  }

  updateCustomerGroupGlobal(res: ICusomterGroup[]) {
    this.store.dispatch(getCustomerGroupGlobalInfoSuccess({ data: res.filter((item) => item.isActive) }));
  }

  getCustomerDocumentInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getCustomerDocumentInfo, getAllDataCustomerInfo),
      tap(() => this.loadingService.show()),
      switchMap(({ id }) => {
        return this.customerService.getCustomerDocumentInfo(id).pipe(
          map((res) => {
            return getCustomerDocumentInfoSuccess({ data: res });
          }),
          catchError((error) => {
            this.messageService.error(error.error.message);
            return of(error);
          }),
          finalize(() => this.loadingService.hide())
        );
      })
    );
  });

  getCustomerAccountInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getCustomerAccountInfo),
      tap(() => this.loadingService.show()),
      switchMap(({ id }) => {
        return this.customerService.getCustomerAccountInfo(id).pipe(
          map((res) => {
            return getCustomerAccountInfoSuccess({ data: res });
          }),
          catchError((error) => {
            this.messageService.error(error.error.message);
            return of(error);
          }),
          finalize(() => this.loadingService.hide())
        );
      })
    );
  });

  getCustomerAccountDetailInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getCustomerAccountInfoDetail),
      tap(() => this.loadingService.show()),
      switchMap(({ id }) => {
        return this.customerService.getCustomerAccountInfo(id).pipe(
          map((res) => {
            return getCustomerAccountInfoDetailSuccess({ data: res });
          }),
          catchError((error) => {
            this.messageService.error(error.error.message);
            return of(error);
          }),
          finalize(() => this.loadingService.hide())
        );
      })
    );
  });

  getAuthContractList$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getCustomersAuthorityInfo, getAllDataCustomerInfo),
      tap(() => this.loadingService.show()),
      switchMap(({ id }) => {
        return this.customerService.getAuthContractList(id).pipe(
          map((res) => {
            return getCustomersAuthorityInfoSuccess({ data: res });
          }),
          catchError((error) => {
            this.messageService.error(error.error.message);
            return of(error);
          }),
          finalize(() => this.loadingService.hide())
        );
      })
    );
  });

  getAllBankList$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getAllInfoBank),
      tap(() => this.loadingService.show()),
      switchMap((action) => {
        return this.customerService.getAllBankList().pipe(
          map((res) => {
            return getAllInfoBankSuccess({ data: res });
          }),
          catchError((error) => {
            this.messageService.error(error.error.message);
            return of(error);
          }),
          finalize(() => this.loadingService.hide())
        );
      })
    );
  });
}
