import { createFeatureSelector, createSelector } from '@ngrx/store';
import { ICustomerState } from '../model/customer';

export const CUSTOMER_STATE_NAME = 'CUSTOMER';

export const selectCustomerState = createFeatureSelector<ICustomerState>(CUSTOMER_STATE_NAME);

export const selectSearchValue$ = createSelector(selectCustomerState, (state) => state?.searchValue);

// PERSONAL
export const selectCustomerList$ = createSelector(selectCustomerState, (state) => state?.customerPersonalData);

export const selectFilterPersonalInfo$ = createSelector(selectCustomerState, (state) => state?.filterPersonalInfo);

export const selectFilteredDataPersonalInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state.filteredPersonalData
);

export const pageIndexPersonalInfo$ = createSelector(selectCustomerState, (state) => state?.pageIndexPersonalInfo);

export const selectPersonalInfoList$ = createSelector(selectCustomerState, (state) => state?.customerPersonalData);

// BANK
export const selectFilterBankInfo$ = createSelector(selectCustomerState, (state) => state?.filterBankInfo);

export const selectFilteredDataBankInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state.filteredBankData
);

export const selectCustomerBankInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state?.customerBankData
);

// DOCUMENT
export const selectFilterDocumentInfo$ = createSelector(selectCustomerState, (state) => state?.filterDocumentInfo);

export const selectFilteredDataDocumentInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state?.filteredDocumentData
);

export const selectCustomerDocumentInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state?.customerDocumentData
);

// ACCOUNT
export const selectFilterAccountInfo$ = createSelector(selectCustomerState, (state) => state?.filterAccountInfo);

export const selectFilteredDataAccountInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state?.filteredAccountData
);

export const selectCustomerAccountInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state?.customerAccountData
);

export const selectCustomerAccountDetailInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state?.customerAccountDetailData
);

// INTEREST-RATE
export const selectFilterInterestRateInfo$ = createSelector(
  selectCustomerState,
  (state) => state?.filterInterestRateInfo
);

export const selectFilteredDataInterestRateInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state?.filteredInterestRateData
);

// AUTHORITY
export const selectCustomerAuthority$ = createSelector(selectCustomerState, (state) => state?.customerAuthorityData);

export const selectFilterAuthorityInfo$ = createSelector(selectCustomerState, (state) => state?.filterAuthorityInfo);

export const selectFilteredDataAuthorityInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state?.filteredAuthorityData
);

// CUSTOMER GROUP
export const selectFilterCustomerGroup$ = createSelector(selectCustomerState, (state) => state?.filterCustomerGroup);

export const selectFilteredDataCustomerGroup$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state?.filteredCustomerGroupData
);

export const selectCustomerGroupInfo$ = createSelector(
  selectCustomerState,
  (state: ICustomerState) => state?.customerGroupData
);

export const selecAllBankList$ = createSelector(selectCustomerState, (state: ICustomerState) => state?.listAllBankInfo);
