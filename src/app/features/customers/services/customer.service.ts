import { Injectable } from '@angular/core';
import { map, tap } from 'rxjs';
import { ApiResponse } from 'src/app/core/models/api-response';
import { ApiService } from 'src/app/core/services';
import {
  ICustomerAuthority,
  IBankInfo,
  ICusomterGroupPayload,
  ICustomerBank,
  IPayloadAddCustomerInGroup,
  IPayloadTranferEachOfCustomers,
  ICusomterGroup,
  ICustomerDocument,
  ICustomerAccount,
  ICustomerDataResponse,
  ICustomerGroupTransferPayload,
} from '../model/customer';
import { IPayloadPersonalList } from 'src/app/shared/models/global';

/**
 * CustomerService
 */
@Injectable({
  providedIn: 'root',
})
export class CustomerService {
  url = 'v1/customer';

  /**
   *constructor
   * @param apiService apiService
   */
  constructor(private readonly apiService: ApiService) {}

  createCustomerGroup(payload: ICusomterGroupPayload) {
    return this.apiService.post<ApiResponse<{ id: string }>>(`${this.url}/customer-group`, payload);
  }

  editNameCustomerGroup(name: string, id: string, brokerCode: string) {
    const payload = { name, brokerCode };
    return this.apiService.put<ApiResponse<boolean>>(`${this.url}/customer-group/${id}`, payload);
  }

  deleteCustomerGroup(ids: string[], brokerCode: string) {
    const payload = {
      ids,
      brokerCode,
    };
    return this.apiService.delete<ApiResponse<boolean>>(`${this.url}/customer-group`, {}, payload);
  }

  transferCustomerToOtherGroup(payload: ICustomerGroupTransferPayload) {
    return this.apiService.post<ApiResponse<boolean>>(`${this.url}/customer-group/transfer-all`, payload);
  }

  addCustomerInGroup(payload: IPayloadAddCustomerInGroup) {
    return this.apiService.post<ApiResponse<boolean>>(`${this.url}/customer-group/add-customers`, payload);
  }

  deleteCustomerInGroup(id: string, accountNos: string[], brokerCode: string) {
    const payload = {
      groupId: id,
      accountNos,
      brokerCode,
    };
    return this.apiService.delete<ApiResponse<boolean>>(`${this.url}/customer-group/customers`, {}, payload);
  }

  transferEachOfCustomer(payload: IPayloadTranferEachOfCustomers) {
    return this.apiService.post<ApiResponse<boolean>>(`${this.url}/customer-group/transfer`, payload);
  }

  // Bank Info
  getCustomerBankInfo(brokerCode: string) {
    return this.apiService
      .get<ApiResponse<ICustomerBank[]>>(`${this.url}/bank/broker/${brokerCode}`)
      .pipe(map((res) => res.data));
  }

  // document info
  getCustomerDocumentInfo(brokerCode: string) {
    return this.apiService
      .get<ApiResponse<ICustomerDocument[]>>(`${this.url}/contract/broker/${brokerCode}`)
      .pipe(map((res) => res.data));
  }

  // account info
  getCustomerAccountInfo(brokerCode: string) {
    const params = {
      ...(brokerCode && { brokerCode }),
    };
    return this.apiService
      .get<ApiResponse<ICustomerAccount[]>>(`${this.url}/account/broker`, { params })
      .pipe(map((res) => res.data));
  }

  // customer group info
  getCustomerGroupInfo(brokerCode: string) {
    const payload = {
      brokerCodes: [brokerCode],
      accountNumbers: [],
      searchValue: ''
    };
    return this.apiService
      .post<ApiResponse<ICusomterGroup[]>>(`${this.url}/customer-group/get-all-group-customer`, payload)
      .pipe(
        map((res) => res.data));
  }

  getPersonalInfoList(payload: IPayloadPersonalList) {
    return this.apiService
      .post<ApiResponse<ICustomerDataResponse[]>>(`${this.url}`, payload)
      .pipe(map((res) => res.data));
  }

  getAuthContractList(brokerCode: string) {
    return this.apiService
      .get<ApiResponse<ICustomerAuthority[]>>(`${this.url}/auth-contract/${brokerCode}`)
      .pipe(map((res) => res.data));
  }

  getAllBankList() {
    return this.apiService.get<ApiResponse<IBankInfo[]>>(`${this.url}/bank`).pipe(map((res) => res.data));
  }
}
