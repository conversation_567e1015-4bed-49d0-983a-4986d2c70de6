import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { CustomerService } from './customer.service';
import { ApiService } from 'src/app/core/services/api.service';
import { ApiResponse } from 'src/app/core/models/api-response';
import {
  ICusomterGroupPayload,
  ICustomerGroupTransferPayload,
  IPayloadAddCustomerInGroup,
  IPayloadTranferEachOfCustomers,
} from '../model/customer';

describe('CustomerService', () => {
  let service: CustomerService;
  let mockApiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiServiceSpy = jasmine.createSpyObj('ApiService', ['get', 'post', 'put', 'delete']);

    TestBed.configureTestingModule({
      providers: [
        CustomerService,
        { provide: ApiService, useValue: apiServiceSpy }
      ]
    });

    service = TestBed.inject(CustomerService);
    mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  // Constructor and Initialization
  it('No.1: should properly initialize with ApiService dependency', () => {
    expect(service).toBeTruthy();
    expect(service.url).toBe('v1/customer');
  });

  // Customer Group Operations
  it('No.2: should create customer group successfully', () => {
    const mockPayload: ICusomterGroupPayload = {
      name: 'Test Group',
      brokerCode: 'BR001',
      members: []
    };
    const mockResponse: ApiResponse<{ id: string }> = {
      data: { id: 'group123' },
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.createCustomerGroup(mockPayload).subscribe();

    expect(mockApiService.post).toHaveBeenCalledWith('v1/customer/customer-group', mockPayload);
  });

  it('No.3: should edit customer group name successfully', () => {
    const name = 'Updated Group';
    const id = 'group123';
    const brokerCode = 'BR001';
    const expectedPayload = { name, brokerCode };
    const mockResponse: ApiResponse<boolean> = {
      data: true,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.put.and.returnValue(of(mockResponse));

    service.editNameCustomerGroup(name, id, brokerCode).subscribe();

    expect(mockApiService.put).toHaveBeenCalledWith(`v1/customer/customer-group/${id}`, expectedPayload);
  });

  it('No.4: should delete customer group successfully', () => {
    const ids = ['group1', 'group2'];
    const brokerCode = 'BR001';
    const expectedPayload = { ids, brokerCode };
    const mockResponse: ApiResponse<boolean> = {
      data: true,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.delete.and.returnValue(of(mockResponse));

    service.deleteCustomerGroup(ids, brokerCode).subscribe();

    expect(mockApiService.delete).toHaveBeenCalledWith('v1/customer/customer-group', {}, expectedPayload);
  });

  it('No.5: should transfer customer to other group successfully', () => {
    const mockPayload: ICustomerGroupTransferPayload = {
      fromId: 'group1',
      toId: 'group2',
      accountNos: ['ACC001'],
      brokerCode: 'BR001'
    };
    const mockResponse: ApiResponse<boolean> = {
      data: true,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.transferCustomerToOtherGroup(mockPayload).subscribe();

    expect(mockApiService.post).toHaveBeenCalledWith('v1/customer/customer-group/transfer-all', mockPayload);
  });

  it('No.6: should add customer in group successfully', () => {
    const mockPayload: IPayloadAddCustomerInGroup = {
      groupId: 'group1',
      members: [{ name: 'Customer 1', accountNo: 'ACC001' }],
      brokerCode: 'BR001'
    };
    const mockResponse: ApiResponse<boolean> = {
      data: true,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.addCustomerInGroup(mockPayload).subscribe();

    expect(mockApiService.post).toHaveBeenCalledWith('v1/customer/customer-group/add-customers', mockPayload);
  });

  it('No.7: should delete customer in group successfully', () => {
    const id = 'group1';
    const accountNos = ['ACC001', 'ACC002'];
    const brokerCode = 'BR001';
    const expectedPayload = {
      groupId: id,
      accountNos,
      brokerCode
    };
    const mockResponse: ApiResponse<boolean> = {
      data: true,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.delete.and.returnValue(of(mockResponse));

    service.deleteCustomerInGroup(id, accountNos, brokerCode).subscribe();

    expect(mockApiService.delete).toHaveBeenCalledWith('v1/customer/customer-group/customers', {}, expectedPayload);
  });

  it('No.8: should transfer each customer successfully', () => {
    const mockPayload: IPayloadTranferEachOfCustomers = {
      fromId: 'group1',
      toId: 'group2',
      accountNos: ['ACC001'],
      brokerCode: 'BR001'
    };
    const mockResponse: ApiResponse<boolean> = {
      data: true,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.transferEachOfCustomer(mockPayload).subscribe();

    expect(mockApiService.post).toHaveBeenCalledWith('v1/customer/customer-group/transfer', mockPayload);
  });

  // Bank Information Operations
  it('No.9: should get customer bank info successfully', () => {
    const brokerCode = 'BR001';
    const mockData = [
      {
        customerName: 'Customer 1',
        accountNumber: 'ACC001',
        accountType: 1,
        bankAccounts: []
      }
    ];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerBankInfo(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith(`v1/customer/bank/broker/${brokerCode}`);
  });

  it('No.10: should handle empty brokerCode for bank info', () => {
    const brokerCode = '';
    const mockData: any[] = [];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerBankInfo(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/bank/broker/');
  });

  // Document Information Operations
  it('No.11: should get customer document info successfully', () => {
    const brokerCode = 'BR001';
    const mockData: any[] = [
      {
        customerName: 'Customer 1',
        accountNumber: 'ACC001',
        accountType: 1,
        identification: {},
        registrationNo: 'REG001',
        registration: {},
        signature: {},
        contracts: []
      }
    ];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerDocumentInfo(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith(`v1/customer/contract/broker/${brokerCode}`);
  });

  it('No.12: should handle empty brokerCode for document info', () => {
    const brokerCode = '';
    const mockData: any[] = [];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerDocumentInfo(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/contract/broker/');
  });

  // Account Information Operations
  it('No.13: should get customer account info with brokerCode', () => {
    const brokerCode = 'BR001';
    const mockData = [
      {
        accountNumber: 'ACC001',
        customerName: 'Customer 1',
        customerLevel: 'VIP',
        customerGroup: 'Group1',
        brokerName: 'Broker 1',
        brokerCode: 'BR001',
        saleGroupName: 'Sales 1',
        children: []
      }
    ];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerAccountInfo(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/account/broker', { params: { brokerCode } });
  });

  it('No.14: should get customer account info without brokerCode', () => {
    const brokerCode = '';
    const mockData: any[] = [];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerAccountInfo(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/account/broker', { params: {} });
  });

  it('No.15: should handle null brokerCode for account info', () => {
    const brokerCode = null as any;
    const mockData: any[] = [];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerAccountInfo(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/account/broker', { params: {} });
  });

  // Customer Group Information Operations
  it('No.16: should get customer group info successfully', () => {
    const brokerCode = 'BR001';
    const mockData: any[] = [
      {
        id: 'group1',
        name: 'Group 1',
        brokerDebtId: [],
        brokerId: [],
        customers: [],
        members: [],
        groupCode: 'GRP001'
      }
    ];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerGroupInfo(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/customer-group', { params: { brokerCode } });
  });

  it('No.17: should handle empty brokerCode for group info', () => {
    const brokerCode = '';
    const mockData: any[] = [];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerGroupInfo(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/customer-group', { params: { brokerCode: '' } });
  });

  // Personal Information Operations
  it('No.18: should get personal info list successfully', () => {
    const mockPayload = {
      accountNumbers: ['ACC001'],
      brokerCode: ['BR001'],
      userType: 'individual',
      fromBirthYear: '1990',
      toBirthYear: '2000',
      searchKey: 'test'
    };
    const mockData: any[] = [
      {
        accountNumber: 'ACC001',
        name: 'Customer 1',
        customerName: 'Customer 1',
        accountType: 1
      }
    ];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getPersonalInfoList(mockPayload).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/customer', mockPayload);
  });

  it('No.19: should handle empty payload for personal info list', () => {
    const mockPayload = {
      accountNumbers: [],
      brokerCode: [],
      userType: '',
      fromBirthYear: '',
      toBirthYear: '',
      searchKey: ''
    };
    const mockData: any[] = [];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getPersonalInfoList(mockPayload).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/customer', mockPayload);
  });

  // Authority Contract Operations
  it('No.20: should get auth contract list successfully', () => {
    const brokerCode = 'BR001';
    const mockData: any[] = [
      {
        id: 'auth1',
        accountNumber: 'ACC001',
        customerName: 'Customer 1',
        authPerson: 'Auth Person',
        authTelephone: '*********',
        authEmail: '<EMAIL>',
        authAddress: 'Auth Address',
        contractNo: 'CONTRACT001',
        accountSignature: 'signature1',
        authSignature: 'signature2',
        authIdentity: 'ID001',
        authIdentityDate: '2023-01-01',
        authIdentityIssuer: 'Issuer',
        authFromDate: '2023-01-01',
        authToDate: '2024-01-01',
        authAll: true,
        authCommand: false,
        authMoney: true,
        authAdvance: false,
        authDepository: true,
        authFinanceService: false,
        authStock: true,
        authStatus: 'active',
        contractImg: 'contract.jpg',
        accountSignatureImg: 'signature1.jpg',
        authSignatureImg: 'signature2.jpg',
        authIdentityImg: 'identity.jpg'
      }
    ];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getAuthContractList(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith(`v1/customer/auth-contract/${brokerCode}`);
  });

  it('No.21: should handle empty brokerCode for auth contract list', () => {
    const brokerCode = '';
    const mockData: any[] = [];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getAuthContractList(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/auth-contract/');
  });

  // Bank List Operations
  it('No.22: should get all bank list successfully', () => {
    const mockData: any[] = [
      {
        id: 'bank1',
        name: 'Bank 1',
        bankName: 'Bank Name 1',
        shortName: 'BN1',
        logo: 'logo1.jpg'
      }
    ];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getAllBankList().subscribe(result => {
      expect(result).toEqual(mockData);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/bank');
  });

  // Error Handling
  it('No.23: should handle API errors for customer group operations', () => {
    const mockPayload: ICusomterGroupPayload = {
      name: 'Test Group',
      brokerCode: 'BR001',
      members: []
    };
    const errorResponse = new Error('API Error');

    mockApiService.post.and.returnValue(throwError(() => errorResponse));

    service.createCustomerGroup(mockPayload).subscribe({
      next: () => fail('Should have failed'),
      error: (error) => {
        expect(error).toBe(errorResponse);
      }
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/customer/customer-group', mockPayload);
  });

  it('No.24: should handle API errors for information retrieval operations', () => {
    const brokerCode = 'BR001';
    const errorResponse = new Error('API Error');

    mockApiService.get.and.returnValue(throwError(() => errorResponse));

    service.getCustomerBankInfo(brokerCode).subscribe({
      next: () => fail('Should have failed'),
      error: (error) => {
        expect(error).toBe(errorResponse);
      }
    });

    expect(mockApiService.get).toHaveBeenCalledWith(`v1/customer/bank/broker/${brokerCode}`);
  });

  // URL Construction
  it('No.25: should construct correct URLs for all endpoints', () => {
    expect(service.url).toBe('v1/customer');

    // Test URL construction by checking the calls made to apiService
    const brokerCode = 'BR001';
    const mockResponse: ApiResponse<any[]> = {
      data: [],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerBankInfo(brokerCode).subscribe();
    service.getCustomerDocumentInfo(brokerCode).subscribe();
    service.getCustomerAccountInfo(brokerCode).subscribe();
    service.getCustomerGroupInfo(brokerCode).subscribe();
    service.getAuthContractList(brokerCode).subscribe();
    service.getAllBankList().subscribe();

    expect(mockApiService.get).toHaveBeenCalledWith(`v1/customer/bank/broker/${brokerCode}`);
    expect(mockApiService.get).toHaveBeenCalledWith(`v1/customer/contract/broker/${brokerCode}`);
    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/account/broker', { params: { brokerCode } });
    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/customer-group', { params: { brokerCode } });
    expect(mockApiService.get).toHaveBeenCalledWith(`v1/customer/auth-contract/${brokerCode}`);
    expect(mockApiService.get).toHaveBeenCalledWith('v1/customer/bank');
  });

  // Data Mapping
  it('No.26: should properly map response data for get operations', () => {
    const brokerCode = 'BR001';
    const mockData: any[] = [{ test: 'data' }];
    const mockResponse: ApiResponse<any[]> = {
      data: mockData,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerBankInfo(brokerCode).subscribe(result => {
      expect(result).toEqual(mockData);
      // The result should be the data property, not the full response
      expect(result).toBe(mockResponse.data);
    });
  });

  it('No.27: should handle null/undefined response data', () => {
    const brokerCode = 'BR001';
    const mockResponse: ApiResponse<any> = {
      data: null,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerBankInfo(brokerCode).subscribe(result => {
      expect(result).toBeNull();
    });
  });

  // Parameter Validation and Edge Cases
  it('No.28: should handle special characters in brokerCode', () => {
    const brokerCode = 'BR@#$%001';
    const mockResponse: ApiResponse<any[]> = {
      data: [],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerBankInfo(brokerCode).subscribe();

    expect(mockApiService.get).toHaveBeenCalledWith(`v1/customer/bank/broker/${brokerCode}`);
  });

  it('No.29: should handle very long strings in parameters', () => {
    const longBrokerCode = 'A'.repeat(1000);
    const mockResponse: ApiResponse<any[]> = {
      data: [],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    service.getCustomerBankInfo(longBrokerCode).subscribe();

    expect(mockApiService.get).toHaveBeenCalledWith(`v1/customer/bank/broker/${longBrokerCode}`);
  });

  it('No.30: should handle array parameters correctly', () => {
    const ids = ['id1', 'id2', 'id3'];
    const brokerCode = 'BR001';
    const mockResponse: ApiResponse<boolean> = {
      data: true,
      message: 'Success',
      statusCode: 200
    };

    mockApiService.delete.and.returnValue(of(mockResponse));

    service.deleteCustomerGroup(ids, brokerCode).subscribe();

    expect(mockApiService.delete).toHaveBeenCalledWith('v1/customer/customer-group', {}, { ids, brokerCode });
  });

  // Observable Behavior
  it('No.31: should return Observable for all methods', () => {
    const mockResponse: ApiResponse<any> = {
      data: [],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));
    mockApiService.post.and.returnValue(of(mockResponse));

    const bankInfoObservable = service.getCustomerBankInfo('BR001');
    const personalInfoObservable = service.getPersonalInfoList({
      accountNumbers: [],
      brokerCode: [],
      userType: '',
      fromBirthYear: '',
      toBirthYear: '',
      searchKey: ''
    });

    expect(typeof bankInfoObservable.subscribe).toBe('function');
    expect(typeof personalInfoObservable.subscribe).toBe('function');
  });

  it('No.32: should handle multiple subscribers to same Observable', () => {
    const mockResponse: ApiResponse<any[]> = {
      data: [{ test: 'data' }],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.get.and.returnValue(of(mockResponse));

    const observable = service.getCustomerBankInfo('BR001');
    let result1: any;
    let result2: any;

    observable.subscribe(data => result1 = data);
    observable.subscribe(data => result2 = data);

    expect(result1).toEqual(result2);
  });

  // Service Integration
  it('No.33: should be injectable as singleton', () => {
    const service2 = TestBed.inject(CustomerService);
    expect(service).toBe(service2);
  });
});
