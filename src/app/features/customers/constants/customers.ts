import { hasExpandedRow } from '@shared/models';
import { ITypeOption } from '../../assets/models/asset';

export enum ETypeAccount {
  NONE,
  INDIVIDUAL,
  ORGANIZATION,
}

export const CONVERT_TYPE_ACCOUNT_TO_CLASS: { [key: number]: string } = {
  [ETypeAccount.INDIVIDUAL]: 'individual',
  [ETypeAccount.ORGANIZATION]: 'organization',
};

export const CONVERT_TYPE_ACCOUNT_TO_LABLE: { [key: number]: string } = {
  [ETypeAccount.INDIVIDUAL]: 'Cá nhân',
  [ETypeAccount.ORGANIZATION]: 'Tổ chức',
};

export enum EGender {
  NONE,
  MALE,
  FEMALE,
}

export const CONVERT_GENDER_TO_LABLE: { [key: number]: string } = {
  [EGender.MALE]: 'Nam',
  [EGender.FEMALE]: 'Nữ',
};

export const CONVERT_LABEL_GENDER_TO_NUMBER: { [key: string]: number } = {
  Nam: EGender.MALE,
  Nữ: EGender.FEMALE,
};

export const EAuthority = {
  TRUE: 'Có uỷ quyền',
  FALSE: 'Không uỷ quyền',
};

export const CONVERT_AUTHORITY_STATUS_TO_LABEL: { [key: string]: string } = {
  0: 'Có uỷ quyền',
  1: 'Không uỷ quyền',
};

export const CONVERT_AUTH_STATUS_TO_CLASS: { [key: number]: string } = {
  0: 'status-authority has-authority',
  1: 'status-authority',
};

export const CONVERT_AUTHORITY_TO_BOOLEAN: { [key: string]: boolean } = {
  [EAuthority.TRUE]: true,
  [EAuthority.FALSE]: false,
};

export enum EIDType {
  IDENTIFYCARD = 'Chứng minh nhân dân',
  IDENTIFICATIONCARD = 'Căn cước công dân',
  PASSPORT = 'Hộ chiếu',
}

export const ListOptionGender: ITypeOption[] = [
  {
    name: 'Nam',
    value: EGender.MALE,
  },
  {
    name: 'Nữ',
    value: EGender.FEMALE,
  },
];

export const ListOptionAuthority: ITypeOption[] = [
  {
    name: 'Có uỷ quyền',
    value: true,
  },
  {
    name: 'Không uỷ quyền',
    value: false,
  },
];

export const ListOptionCustomerLevel: ITypeOption[] = [
  {
    value: 'PLATINUM',
  },
  {
    value: 'DIAMOND',
  },
  {
    value: 'GOLD',
  },
  {
    value: 'NORMAL',
  },
];

export const ListOfTypeID = [
  {
    name: EIDType.IDENTIFYCARD,
    value: 0,
  },
  {
    name: EIDType.IDENTIFICATIONCARD,
    value: 1,
  },
  {
    name: EIDType.PASSPORT,
    value: 2,
  },
];

export interface IInterestRateInfoData extends IExtendIInterestRate, hasExpandedRow {
  accountNumber: string;
  customerLevel: string;
  customerName: string;
  interests: IInterestRate[];
}

export interface IExtendIInterestRate {
  [key: string]: number | string | boolean | IInterestRate[] | undefined;
}

export interface IInterestRate {
  interestRate: number;
  limit: number;
}
export enum ECustomerLevel {
  PLATINUM = 'PLATINUM',
  DIAMOND = 'DIAMOND',
  GOLD = 'GOLD',
  NORMAL = 'NORMAL',
}

export const CUSTOMER_LEVEL_MAPPING = {
  [ECustomerLevel.NORMAL]: 0,
  [ECustomerLevel.PLATINUM]: 1,
  [ECustomerLevel.GOLD]: 2,
  [ECustomerLevel.DIAMOND]: 3,
};

export const CONVERT_CUSTOMER_LEVEL_TO_LABLE: { [key: string]: string } = {
  [ECustomerLevel.PLATINUM]: 'PLATINUM',
  [ECustomerLevel.DIAMOND]: 'DIAMOND',
  [ECustomerLevel.GOLD]: 'GOLD',
  [ECustomerLevel.NORMAL]: 'NORMAL',
};

export const CONVERT_AUTHORIRY_STATUS = {
  false: 0,
  true: 1,
};

export enum ECustomerGroup {
  SURFING = 'LƯỚT SÓNG',
  LONGTERM = 'DÀI HẠN',
  NOGROUP = 'CHƯA CÓ NHÓM',
  MEDIUMTERM = 'TRUNG HẠN',
}

export const CONVERT_CUSTOMER_GROUP_TO_LABLE: { [key: string]: string } = {
  [ECustomerGroup.SURFING]: 'LƯỚT SÓNG',
  [ECustomerGroup.LONGTERM]: 'DÀI HẠN',
  [ECustomerGroup.NOGROUP]: 'CHƯA CÓ NHÓM',
  [ECustomerGroup.MEDIUMTERM]: 'TRUNG HẠN',
};

export enum ECustomerGroupToValue {
  SURFING = 'LƯỚT SÓNG',
  LONGTERM = 'DÀI HẠN',
  NOGROUP = 'Chưa có nhóm',
  MEDIUMTERM = 'TRUNG HẠN',
}

export const CONVERT_CUSTOMER_GROUP_TO_VALUE: { [key: string]: string } = {
  [ECustomerGroupToValue.SURFING]: 'Nhóm Lướt sóng',
  [ECustomerGroupToValue.LONGTERM]: 'Nhóm Đầu tư lâu dài',
  [ECustomerGroupToValue.NOGROUP]: 'Chưa có nhóm',
  [ECustomerGroupToValue.MEDIUMTERM]: 'Nhóm Đầu tư trung hạn',
};

export const ListOptionCustomerGroup: ITypeOption[] = [
  {
    name: 'LƯỚT SÓNG',
    value: CONVERT_CUSTOMER_GROUP_TO_VALUE[ECustomerGroupToValue.SURFING],
  },
  {
    name: 'DÀI HẠN',
    value: CONVERT_CUSTOMER_GROUP_TO_VALUE[ECustomerGroupToValue.LONGTERM],
  },
  {
    name: 'TRUNG HẠN',
    value: CONVERT_CUSTOMER_GROUP_TO_VALUE[ECustomerGroupToValue.MEDIUMTERM],
  },
  {
    name: 'CHƯA CÓ NHÓM',
    value: CONVERT_CUSTOMER_GROUP_TO_VALUE[ECustomerGroupToValue.NOGROUP],
    classCustom: 'option-color-brand-600-cls',
  },
];

export const LIST_OF_CUSTOMER = [
  {
    customerGroup: '069C-125485',
    customerName: 'Phạm Thị Thu Trang',
  },
  {
    customerGroup: '069C-586547',
    customerName: 'Đặng Hoàng An Nhiên',
  },
  {
    customerGroup: '069C-918882',
    customerName: 'Phạm Tiến Nam Phương',
  },
  {
    customerGroup: '069C-891135',
    customerName: 'Trần Văn Hậu',
  },
  {
    customerGroup: '069C-251114',
    customerName: 'Công ty cổ phần địa ốc Ngọc Minh Huy',
  },
  {
    customerGroup: '069C-637085',
    customerName: 'Công ty TNHH Tigon 68',
  },
  {
    customerGroup: '069C-316087',
    customerName: 'Công ty TNHH Mica Group',
  },
  {
    customerGroup: '069C-388482',
    customerName: 'Công ty cổ phần Money Max',
  },
  {
    customerGroup: '069C-862656',
    customerName: 'Công ty TNHH du lịch Cá Voi Xanh',
  },
  {
    customerGroup: '069C-252138',
    customerName: 'Công ty TNHH xây dựng và đầu tư Phú Khang',
  },
  {
    customerGroup: '069C-400190',
    customerName: 'Ngô Thị Hằng',
  },
  {
    customerGroup: '069C-883962',
    customerName: 'Bùi Thị Hạnh',
  },
];

export const LIST_OF_CUSTOMER_GROUP = [
  {
    customerGroup: 'DÀI HẠN',
  },
  {
    customerGroup: 'TRUNG HẠN',
  },
  {
    customerGroup: 'LƯỚT SÓNG',
  },
];

export const LIST_MG_OPTIONS = [
  {
    id: '1',
    name: 'MG-015: Nguyễn Tuấn Dương',
    parentId: '1',
    config: 'level2',
  },
  {
    id: '2',
    name: 'MG-22: Cao Tuấn Nghĩa',
    parentId: '1',
    config: 'level2',
  },
  {
    id: '3',
    name: 'MG-05: Mai Tiến Đạt',
    parentId: '4',
    config: 'level2',
  },
  {
    id: '4',
    name: 'MG-020: Vũ Minh Chiến',
    parentId: '2',
    config: 'level2',
  },
  {
    id: '5',
    name: 'MG-016: Nguyễn Hoàng Cảnh',
    parentId: '4',
    config: 'level2',
  },
  {
    id: '6',
    name: 'MG-019: Đinh Sỹ Dũng',
    parentId: '3',
    config: 'level2',
  },
  {
    id: '7',
    name: 'MG-021: Lê Ngọc Hà',
    parentId: '5',
    config: 'level2',
  },
  {
    id: '8',
    name: 'MG-018: Phạm Văn Tây',
    parentId: '3',
    config: 'level2',
  },
];

export const LIST_MG_ROOM_OPTIONS = [
  {
    id: '1',
    name: 'Phòng MG 05',
    children: [...structuredClone(LIST_MG_OPTIONS)],
    config: 'level1',
  },
  {
    id: '2',
    name: 'Phòng MG 08',
    children: structuredClone(LIST_MG_OPTIONS),
    config: 'level1',
  },
  {
    id: '3',
    name: 'Phòng MG 07',
    children: structuredClone(LIST_MG_OPTIONS),
    config: 'level1',
  },
  {
    id: '4',
    name: 'Phòng MG 06',
    children: structuredClone(LIST_MG_OPTIONS),
    config: 'level1',
  },
  {
    id: '5',
    name: 'Phòng MG 09',
    children: structuredClone(LIST_MG_OPTIONS),
    config: 'level1',
  },
];

export const CONVERT_TYPE_FILE_DOCMENT_INFO_TO_NAME_CONFIG: { [key: string]: string } = {
  '00': 'contractId00',
  '01': 'contractId01',
  '02': 'contractId02',
  '03': 'contractId03',
  '04': 'contractId04',
  '80': 'contractId05',
  '86': 'contractId80',
};

export enum IIdNoType {
  IDENTIFYCARD = '1',
  TRADINGCODE = '2',
  BUSINESSREGISTRATION = '3',
  OTHERS = '4',
  FIIN = '5',
  PASSPORT = '6',
  GOVT = '7',
}

export const CONVERT_ID_NO_TYPE_TO_LABEL: { [key: string]: string } = {
  [IIdNoType.IDENTIFYCARD]: 'Chứng minh nhân dân',
  [IIdNoType.TRADINGCODE]: 'Trading code',
  [IIdNoType.BUSINESSREGISTRATION]: 'Giấy phép ĐKKD',
  [IIdNoType.OTHERS]: 'Loại hình khác',
  [IIdNoType.FIIN]: 'FIIN',
  [IIdNoType.PASSPORT]: 'Hộ chiếu',
  [IIdNoType.GOVT]: 'GOVT',
};

export const CUSTOMERS_NO_GROUP_NAME = 'CHƯA CÓ NHÓM';
export const CUSTOMERS_NO_GROUP_ID = 'chua_co_nhom_id';

export enum EAccountStatus {
  ACTIVE = '1',
  INACTIVE = '2',
}
export const CONVERT_STATUS_ACCOUNT_TO_CLASS: { [key: string]: string } = {
  [EAccountStatus.ACTIVE]: 'individual',
  [EAccountStatus.INACTIVE]: 'organization',
};

export const CONVERT_STATUS_ACCOUNT_TO_LABLE: { [key: string]: string } = {
  [EAccountStatus.ACTIVE]: 'MES-697',
  [EAccountStatus.INACTIVE]: 'MES-698',
};

export enum EAccountGrade {
  NORMAL = 'Normal',
  GOLD = 'Gold',
  DIAMOND = 'Diamond',
  PLATINUM = 'Platinum',
  NVSHS = 'Nhân viên SHS',
  OTHER = 'Khác',
}
export const CONVERT_RANK_ACCOUNT_TO_LABEL: { [key: string]: string } = {
  [EAccountGrade.NORMAL]: 'MES-699',
  [EAccountGrade.GOLD]: 'MES-700',
  [EAccountGrade.DIAMOND]: 'MES-701',
  [EAccountGrade.PLATINUM]: 'MES-702',
  [EAccountGrade.NVSHS]: 'MES-703',
  [EAccountGrade.OTHER]: 'MES-704',
};

export const CONVERT_RANK_ACCOUNT_TO_CLASS: { [key: string]: string } = {
  [EAccountGrade.NORMAL]: 'normal-cls',
  [EAccountGrade.GOLD]: 'gold-cls',
  [EAccountGrade.DIAMOND]: 'diamond-cls',
  [EAccountGrade.PLATINUM]: 'platinum-cls',
  [EAccountGrade.NVSHS]: 'nvshs-cls',
  [EAccountGrade.OTHER]: 'other-cls',
};
