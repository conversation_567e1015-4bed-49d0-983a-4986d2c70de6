import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router } from '@angular/router';
import { DestroyService, DialogService } from 'src/app/core/services';
import { CreateCustomerGroupComponent } from '../components/create-customer-group/create-customer-group.component';
import { FormControl } from '@angular/forms';
import { debounceTime, filter, take, takeUntil, tap } from 'rxjs';
import { Store } from '@ngrx/store';
import { resetSearch, search } from '../stores/customer.action';
import { selectCurrentBrokerView$, selectSearchCustomerGroup$ } from 'src/app/stores/shared/shared.selectors';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { getCurrentBrokerView } from 'src/app/stores/shared/shared.actions';
import { selectSearchValue$ } from '../stores/customer.selector';

/**
 * Customer View
 */
@Component({
  selector: 'app-customers-view',
  templateUrl: './customers.view.html',
  styleUrls: ['./customers.view.scss'],
  providers: [DestroyService],
})
export class CustomersView implements OnInit {
  menuCustomers = [
    {
      router: 'personal-info',
      name: 'MES-07',
      nameIcon: 'icon:user-icon',
    },
    // {
    //   router: 'bank-info',
    //   name: 'MES-08',
    //   nameIcon: 'icon:bank-icon',
    // },
    // {
    //   router: 'document-info',
    //   name: 'MES-09',
    //   nameIcon: 'icon:document-icon',
    // },
    // {
    //   router: 'account-info',
    //   name: 'MES-10',
    //   nameIcon: 'icon:profile-icon',
    // },
    // {
    //   router: 'interest-rate-info',
    //   name: 'MES-11',
    //   nameIcon: 'icon:percentage-icon',
    // },
    // {
    //   router: 'authority-info',
    //   name: 'MES-12',
    //   nameIcon: 'icon:user-permission-icon',
    // },
    {
      router: 'group-customer-info',
      name: 'MES-13',
      nameIcon: 'icon:group-customer-icon',
    },
  ];

  activeTab = 0;

  brokerId = '';

  searchControl = new FormControl();
  private searchSubscription: any;
  private firstRouteChange = true;

  /**
   * Constructor
   * @param _destroy
   * @param router
   * @param dialogService
   * @param store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly router: Router,
    private readonly dialogService: DialogService,
    private readonly store: Store,
    private readonly route: ActivatedRoute
  ) {
    this.updateActiveTab(router.url);
  }

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.updateBrokerId();
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.route.queryParams.pipe(takeUntil(this._destroy)).subscribe((param) => {
      this.brokerId = param['brokerId'];
    });

    this.listenSearchValueChange();
    this.resetSearchControlOnRouteChange();

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchControl.patchValue(value?.trim(), { emitEvent: false });
      });

    this.store
      .select(selectSearchCustomerGroup$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchControl.patchValue(value.trim(), { emitEvent: false });
      });
  }

  /**
   * listenSearchValueChange
   */
  listenSearchValueChange(): void {
    this.searchSubscription = this.searchControl.valueChanges
      .pipe(
        debounceTime(300),
        tap((value) => {
          this.store.dispatch(search({ data: value.trim() }));
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * resetSearchControlOnRouteChange customer
   */
  resetSearchControlOnRouteChange(): void {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationStart),
        tap(() => {
          if (this.firstRouteChange) {
            // Temporarily disable the valueChanges subscription customer
            if (this.searchSubscription) {
              this.searchSubscription.unsubscribe();
            }

            // Reset search control and update the store customer
            this.searchControl.setValue('');
            this.store.dispatch(resetSearch());

            // Re-enable the valueChanges subscription customer
            this.listenSearchValueChange();

            // Set the flag to false after the first reset customer
            this.firstRouteChange = false;
          }
        }),
        takeUntil(this._destroy)
      )
      .subscribe((event: any) => {
        this.updateActiveTab(event.url);
      });
  }

  updateBrokerId() {
    const queryParamsCu = this.route.snapshot.queryParams;
    if (queryParamsCu['brokerId']) {
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userListCus) => {
          if (!userListCus) return;
          const currentBroker = userListCus.find((user) => user.brokerCode === queryParamsCu['brokerId']);
          if (!currentBroker) return;
          this.store.dispatch(getCurrentBrokerView({ data: currentBroker }));
        });
    } else {
      this.store
        .select(selectCurrentBrokerView$)
        .pipe(take(1))
        .subscribe((brokerCu) => {
          this.router.navigate([], {
            queryParams: {
              ...queryParamsCu,
              brokerId: brokerCu.brokerCode,
            },
            queryParamsHandling: 'merge',
          });
        });
    }
  }

  /**
   * createCustomerGroup
   * @param event - MouseEvent
   */
  createCustomerGroup(event: MouseEvent) {
    this.dialogService.openRightDialog(CreateCustomerGroupComponent, {
      width: '450px',
    });
  }

  /**
   * Update active tab index based on current URL
   * @param url - Current URL
   */
  updateActiveTab(url: string) {
    this.activeTab = this.menuCustomers.findIndex((item) => url.includes(item.router));
  }

  /**
   * OnTabChange
   */
  onTabChange() {
    this.searchControl.patchValue('');
  }
}
