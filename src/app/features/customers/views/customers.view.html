<div class="customer-view-containers">
  <div class="customer-header-cls">
    <div class="header-txt typo-heading-8">{{'MES-00' | translate}}</div>

    <div class="header-right">
      <app-create-place-order-component></app-create-place-order-component>
      <div class="header-btn typo-button-5" (click)="createCustomerGroup($event)">
        <img src="./assets/icons/profile-user.svg" alt="profile-user" />
        {{'MES-06' | translate}}
      </div>
    </div>
  </div>
  <div class="sub-menu-cls">
    <!-- <div class="menu-customer-cls">
      <a
        class="box-selection"
        mat-list-item
        routerLinkActive="isSelect"
        [routerLink]="item.router"
        *ngFor="let item of menuCustomers"
      >
        <mat-icon
          class="mat-icon-cls"
          aria-hidden="false"
          aria-label="icon"
          [svgIcon]="item.nameIcon"
          [ngClass]="item.router"
        ></mat-icon>
        <div class="text-section">{{item.name | translate}}</div>
      </a>
    </div> -->
    <mat-tab-group animationDuration="200ms" [selectedIndex]="activeTab" (selectedTabChange)="onTabChange()">
      <mat-tab *ngFor="let item of menuCustomers; let index = index" isActive="item.isActive">
        <ng-template mat-tab-label>
          <div class="menu-customer-cls">
            <a
              class="box-selection"
              mat-list-item
              [class.isSelect]="activeTab === index "
              [routerLink]="item.router"
              [queryParams]="brokerId ? {brokerId} : null"
            >
              <mat-icon
                class="mat-icon-cls"
                aria-hidden="false"
                aria-label="icon"
                [svgIcon]="item.nameIcon"
                [ngClass]="item.router"
              ></mat-icon>
              <div class="typo-body-6">{{item.name | translate}}</div>
            </a>
          </div>
        </ng-template>
      </mat-tab>
    </mat-tab-group>

    <div class="search-cls">
      <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
      <input
        noSpecialCharacters
        class="input-cls input-style-common typo-body-12"
        type="text"
        [placeholder]="'MES-14' | translate"
        [formControl]="searchControl"
      />
    </div>
  </div>
  <div class="router-container-cls"><router-outlet></router-outlet></div>
</div>
