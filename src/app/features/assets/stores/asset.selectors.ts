import { createFeatureSelector, createSelector } from '@ngrx/store';
import { IAssetState } from '../models/asset';

export const ASSET_STATE_NAME = 'ASSET';

export const selectAssetState = createFeatureSelector<IAssetState>(ASSET_STATE_NAME);

export const selectSearchValue$ = createSelector(selectAssetState, (state) => state?.searchValue);

export const selectGuaranteeValue$ = createSelector(selectAssetState, (state) => state?.guarantee);

// ASSET INFO
export const selectFilterAssetInfo$ = createSelector(selectAssetState, (state) => state?.filterAssetInfo);

export const selectFilteredDataAssetInfo$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.filteredAssetInfoData
);

export const selectAssetInfoList$ = createSelector(selectAssetState, (state) => state?.assetsInfoList);

export const pageIndexAssetInfo$ = createSelector(selectAssetState, (state) => state?.pageIndexAssetInfo);

// ACCOUNT STATUS
export const selectFilterAccountStatus$ = createSelector(selectAssetState, (state) => state?.filterAccountStatus);

export const selectFilteredDataAccountStatus$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.filteredAccountStatusData
);

// TRANSACTION
export const selectFilterTransaction$ = createSelector(selectAssetState, (state) => state?.filterTransaction);

export const selectFilteredDataTransaction$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.filteredTransactionData
);

export const selectTransationList$ = createSelector(selectAssetState, (state) => state?.transactionList);

export const pageIndexTransactionInfo$ = createSelector(selectAssetState, (state) => state?.pageIndexTransactionInfo);

// MONEY INFO
export const selectMoneyInfoList$ = createSelector(selectAssetState, (state) => state?.moneyInfoList);

export const selectFilterMoneyInfo$ = createSelector(selectAssetState, (state) => state?.filterMoneyInfo);

export const selectFilteredDataMoneyInfo$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.filteredMoneyInfoData
);

export const pageIndexMoneyInfo$ = createSelector(selectAssetState, (state) => state?.pageIndexMoneyInfo);

// DEPOSIT INFO
export const selectFilterDepositInfo$ = createSelector(selectAssetState, (state) => state?.filterDepositInfo);

export const selectFilteredDataDepositInfo$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.filteredDepositInfoData
);

// DEBT INFO
export const selectFilterDebtInfo$ = createSelector(selectAssetState, (state) => state?.filterDebtInfo);

export const selectFilteredDataDebtInfo$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.filteredDebtInfoData
);

export const selectDebtInfoList$ = createSelector(selectAssetState, (state) => state?.debtInfoList);

// LOAN INFO
export const selectFilterLoanInfo$ = createSelector(selectAssetState, (state) => state?.filterLoanInfo);

export const selectFilteredDataLoanInfo$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.filteredLoanInfoData
);

// INVESTMENT
export const selectFilterInvestment$ = createSelector(selectAssetState, (state) => state?.filterInvestment);

export const selectFilteredDataInvestment$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.filteredInvestmentData
);

export const selectInvestmentPortfolioList$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.investmentPortfolioList
);

export const selectSubInvestmentPortfolioList$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.subInvestmentPortfolioList
);

export const selectPageIndexInvestmentPortfolio$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.pageIndexInvestmentPortfolio
);

// ASSET ALLOCATION
export const selectFilterAssetAllocation$ = createSelector(selectAssetState, (state) => state?.filterAssetAllocation);

export const selectFilteredDataAssetAllocation$ = createSelector(
  selectAssetState,
  (state: IAssetState) => state.filteredAssetAllocationData
);

// PURCHASING POWER

export const selectFilterPuschasingPower$ = createSelector(selectAssetState, (state) => state?.filterPurchasingPower);

export const selectFilteredPuchasingPower$ = createSelector(
  selectAssetState,
  (state) => state.filteredPurchasingPowerData
);

export const selectFakeDataList$ = createSelector(selectAssetState, (state) => state?.fakeList);

export const selectPurchasingPower$ = createSelector(selectAssetState, (state) => state?.purchasingPowerList);

export const pageIndexPurchasingPower$ = createSelector(selectAssetState, (state) => state?.pageIndexPurchasingPower);

// thong tin tai san detail

export const selectAssetInfoDetail$ = createSelector(selectAssetState, (state) => state?.assetInfoDetail);

export const selectAssetSumInfo$ = createSelector(selectAssetState, (state) => state?.assetSumInfo);

export const selectSearchType$ = createSelector(selectAssetState, (state) => state?.typeSearch);

export const selectMoneySumInfo$ = createSelector(selectAssetState, (state) => state?.moneySumInfo);

export const selectTransactionSumInfo$ = createSelector(selectAssetState, (state) => state?.transactionSumInfo);
