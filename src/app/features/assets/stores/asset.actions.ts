import { createAction, props } from '@ngrx/store';
import {
  IAssetInfoData,
  IAssetInfoResponse,
  IDebtInfoResponse,
  IFilterAccountStatusParam,
  IFilterAssetAllocationParam,
  IFilterAssetInfoParam,
  IFilterDebtInfoParam,
  IFilterDepositInfoParam,
  IFilterInvestmentParam,
  IFilterLoanInfoParam,
  IFilterMoneyInfoParam,
  IFilterPurchasingPowerInfoParam,
  IFilterTransactionParam,
  IGuaranteeInfo,
  IDataInvestmentPortfolio,
  IMoneyInfoResponse,
  IPayloadInfoSubAccount,
  IPurchasingPowerResponse,
  ITransactionInfoResponse,
  IPayloadAsset,
  ISumTransactionPayLoad,
  IPayloadAssetInfoDetail,
  IInvestmentPayload,
  ISubInvestmentPayload,
  IAssetSumInfo,
  IMoneySumInfo,
} from '../models/asset';

export const search = createAction('[Asset] search', props<{ data: string }>());

export const resetSearch = createAction('[Asset] reset search');

export const updateGuaranteeData = createAction('[Asset] guarantee', props<{ data: IGuaranteeInfo }>());

// ASSET INFO
export const setFilterAssetInfo = createAction(
  '[Asset info] set filter asset info',
  props<{ params: IFilterAssetInfoParam }>()
);

// TODO: Type of data
export const setFilteredDataAssetInfo = createAction('[Asset info] filtered data asset info', props<{ data: any[] }>());

export const resetFilterAssetInfo = createAction('[Asset info] reset filter asset info default');

// ACCOUNT STATUS
export const setFilterAccountStatus = createAction(
  '[Account status] set filter account status',
  props<{ params: IFilterAccountStatusParam }>()
);

// TODO: Type of data
export const setFilteredDataAccountStatus = createAction(
  '[Account status] filtered data account status',
  props<{ data: any[] }>()
);

export const resetFilterAccountStatus = createAction('[Account status] reset filter account status default');

// TRANSACTION INFO
export const setFilterTransaction = createAction(
  '[Transaction info] set filter transaction info',
  props<{ params: IFilterTransactionParam }>()
);

// TODO: Type of data
export const setFilteredDataTransaction = createAction(
  '[Transaction info] filtered data transaction info',
  props<{ data: any[] }>()
);

export const resetFilterTransaction = createAction('[Transaction info] reset filter transaction info default');

// MONEY INFO
export const setFilterMoneyInfo = createAction(
  '[Money info] set filter money info',
  props<{ params: IFilterMoneyInfoParam }>()
);

// TODO: Type of data
export const setFilteredDataMoneyInfo = createAction('[Money info] filtered data money info', props<{ data: any[] }>());

export const resetFilterMoneyInfo = createAction('[Money info] reset filter money info default');

// DEPOSIT INFO
export const setFilterDepositInfo = createAction(
  '[Deposit info] set filter deposit info',
  props<{ params: IFilterDepositInfoParam }>()
);

// TODO: Type of data
export const setFilteredDataDepositInfo = createAction(
  '[Deposit info] filtered data deposit info',
  props<{ data: any[] }>()
);

export const resetFilterDepositInfo = createAction('[Deposit info] reset filter deposit info default');

// DEBT INFO
export const setFilterDebtInfo = createAction(
  '[Debt info] set filter debt info',
  props<{ params: IFilterDebtInfoParam }>()
);

// TODO: Type of data
export const setFilteredDataDebtInfo = createAction('[Debt info] filtered data debt info', props<{ data: any[] }>());

export const resetFilterDebtInfo = createAction('[Debt info] reset filter debt info default');

// LOAN INFO
export const setFilterLoanInfo = createAction(
  '[Loan info] set filter loan info',
  props<{ params: IFilterLoanInfoParam }>()
);

// TODO: Type of data
export const setFilteredDataLoanInfo = createAction('[Loan info] filtered data loan info', props<{ data: any[] }>());

export const resetFilterLoanInfo = createAction('[Loan info] reset filter loan info default');

// INVESTMENT
export const setFilterInvestment = createAction(
  '[Investment] set filter investment',
  props<{ params: IFilterInvestmentParam }>()
);

// TODO: Type of data
export const setFilteredDataInvestment = createAction(
  '[Investment] filtered data investment',
  props<{ data: any[] }>()
);

export const resetFilterInvestment = createAction('[Investment] reset filter investment default');

// ASSET ALLOCATION
export const setFilterAssetAllocation = createAction(
  '[Asset allocation] set filter asset allocation',
  props<{ params: IFilterAssetAllocationParam }>()
);

// TODO: Type of data
export const setFilteredDataAssetAllocation = createAction(
  '[Asset allocation] filtered data asset allocation',
  props<{ data: any[] }>()
);

export const resetFilterAssetAllocation = createAction('[Asset allocation] reset filter asset allocation default');

export const setFilterPurchasingPower = createAction(
  '[Purchasing Power] set filter puschasing power',
  props<{ params: IFilterPurchasingPowerInfoParam }>()
);

export const setFilteredPurchasingPower = createAction(
  '[Purchasing Power] set filtered puschasing power',
  props<{ data: any[] }>()
);

export const resetFilterPurchasingPower = createAction('[Purchasing Power] reset filter purchasing power');

export const saveListFakeData = createAction(
  '[Assets] get list fake data assets page',
  props<{ listFake: IAssetInfoData[] }>()
);

// ASSETS INFO
export const getDataAssetInfo = createAction(
  '[Assets] Get data asset info page',
  props<{ assetCustomerInfo: IPayloadAsset }>()
);

export const getDataAssetInfoSuccess = createAction(
  '[Assets] Get data asset info page success',
  props<{ data: IAssetInfoResponse[] }>()
);

// Danh mục đầu tư
export const getInvestmentPortfolio = createAction(
  '[Assets] Get data investment portfolio info page',
  props<{ data: IInvestmentPayload }>()
);

export const getInvestmentPortfolioSuccess = createAction(
  '[Assets] Get data investment portfolio info page success',
  props<{ data: IDataInvestmentPortfolio[] }>()
);

export const getSubInvestmentPortfolio = createAction(
  '[Assets] get sub investment portfolio',
  props<{ payload: ISubInvestmentPayload }>()
);

export const resetSubInvestmentPortfolio = createAction('[Assets] reset data sub investment portfolio');

export const getSubInvestmentPortfolioSuccess = createAction(
  '[Assets] Get data sub investment portfolio info page success',
  props<{ data: IDataInvestmentPortfolio[] }>()
);

// Thông tin giao dịch
export const getTransactionInfo = createAction(
  '[Assets] Get data transaction info page',
  props<{ payload: ISumTransactionPayLoad }>()
);

export const getTransactionInfoSuccess = createAction(
  '[Assets] Get data transaction info page success',
  props<{ data: ITransactionInfoResponse[] }>()
);

// Thông tin Tiền
export const getDataMoneyInfo = createAction(
  '[Assets] get data money info page',
  props<{ moneyCustomerInfo: IPayloadAsset }>()
);

export const getDataMoneyInfoSuccess = createAction(
  '[Assets] get data money info page success',
  props<{ data: IMoneyInfoResponse[] }>()
);

export const getDataMoneyInfoFail = createAction('[Assets] Get data money info page fail');

export const getDataMoneyInfoWithFilter = createAction(
  '[Assets] get data money info with filter',
  props<{ moneyCustomerInfo: IPayloadAsset }>()
);

export const getMoneySumInfoSuccess = createAction(
  '[Assets] Get data money total info with filter success',
  props<{ data: IMoneySumInfo }>()
);

// Thông tin dư nợ
export const getDataDebtInfo = createAction(
  '[Assets] get data debt info page',
  props<{ payload: IPayloadInfoSubAccount[] }>()
);

export const getDataDebtInfoSuccess = createAction(
  '[Assets] get data debt info page success',
  props<{ data: IDebtInfoResponse[] }>()
);

export const getDataDebtInfoFail = createAction('[Assets] get data debt info page failed');

// Thông tin sức mua
export const getDataPurchasingPowerInfo = createAction(
  '[Assets] Get data purchasing-power info page',
  props<{ purchasingCustomerInfo: IPayloadAsset }>()
);

export const getDataPurchasingPowerInfoSuccess = createAction(
  '[Assets] Get data purchasing-power info page success',
  props<{ data: IPurchasingPowerResponse[] }>()
);

export const clearIntervalAssetInfo = createAction('[Assets] clear interval in asset info');

export const clearIntervalInvestmentPorfolioInfo = createAction('[Assets] clear interval in investment porfolio info');

export const getDataInvestmentPorfolioInfoFail = createAction('[Assets] Get data investment porfolio info page fail');

export const updatePageIndexAssetInfo = createAction(
  '[Assets] update page index asset info ',
  props<{ pageIndex: number }>()
);

export const updatePageIndexTransactionInfo = createAction(
  '[Assets] update page index transaction info ',
  props<{ pageIndex: number }>()
);

export const updatePageIndexPurchasingPower = createAction(
  '[Assets] update page index purchasing power ',
  props<{ pageIndex: number }>()
);

export const updatePageIndexMoneyInfo = createAction(
  '[Assets] update page index money info ',
  props<{ pageIndex: number }>()
);

export const updatePageIndexInvestmentPortfolio = createAction(
  '[Assets] update page index investment portfolio ',
  props<{ pageIndex: number }>()
);

// Assset detail info

export const getAssetInfoDetail = createAction(
  '[Assets] get asset info detail',
  props<{ data: IPayloadAssetInfoDetail }>()
);

export const getAssetInfoDetailSuccess = createAction('[Assets] get asset info detail success', props<{ data: any }>());

export const resetAssetInfoDetailData = createAction('[Action] reset asset info detail data');

// ASSETS INFO
export const getAssetInfoWithFilter = createAction(
  '[Assets] Get data asset info page with filter',
  props<{ assetCustomerInfo: IPayloadAsset }>()
);

export const getAssetSumInfoSuccess = createAction(
  '[Assets] Get data asset total info page with filter success',
  props<{ data: IAssetSumInfo }>()
);

export const searchByAccount = createAction('[Asset] search investments by account no', props<{ account: string }>());
