import { createReducer, on } from '@ngrx/store';
import * as AssetAction from './asset.actions';
import { IAssetState, ITransactionInfoResponse, ITransactionsSumInfo } from '../models/asset';
import { ESearchType } from '../constant/assets';

export const initialAssetState: IAssetState = {
  searchValue: '',
  guarantee: {
    accountNumber: null,
    value: null,
    type: null,
  },
  // Asset info
  assetsInfoList: [],
  filterAssetInfo: null,
  filteredAssetInfoData: [],

  // Account status
  filterAccountStatus: {
    isFilter: false,
    customers: null,
    marginRate: {
      from: null,
      to: null,
    },
    marginRateViolationPercent: {
      from: null,
      to: null,
    },
    overDueDebt: null,
    feeDebt: null,
    marginRateViolation: null,
  },
  filteredAccountStatusData: [],

  // Transaction
  transactionList: [],
  filterTransaction: {
    isFilter: false,
    customers: null,
    totalTransactionValue: {
      from: null,
      to: null,
    },
    buyTransactionValue: {
      from: null,
      to: null,
    },
    sellTransactionValue: {
      from: null,
      to: null,
    },
    revenueFee: {
      from: null,
      to: null,
    },
    netTransactionFee: {
      from: null,
      to: null,
    },
    brokerCommission: {
      from: null,
      to: null,
    },
    stockExchangeFee: {
      from: null,
      to: null,
    },
    depositoryFee: {
      from: null,
      to: null,
    },
    smsFee: {
      from: null,
      to: null,
    },
  },
  filteredTransactionData: [],

  // Money info
  moneyInfoList: [],
  filterMoneyInfo: null,
  filteredMoneyInfoData: [],

  // Deposit info
  filterDepositInfo: {
    isFilter: false,
    customersSelect: null,
    marginRate: {
      from: null,
      to: null,
    },
    nav: {
      from: null,
      to: null,
    },
    cash: {
      from: null,
      to: null,
    },
    purchasingPower: {
      from: null,
      to: null,
    },
    eightyAssets: {
      from: null,
      to: null,
    },
    eightyInvest: {
      from: null,
      to: null,
    },
    notSubmittedMargin: {
      from: null,
      to: null,
    },
    marginStatus: null,
    vsdcMargin: {
      from: null,
      to: null,
    },
    canWithdrawMargin: {
      from: null,
      to: null,
    },
    toSubmittedMargin: {
      from: null,
      to: null,
    },
  },
  filteredDepositInfoData: [],

  // Debt info
  debtInfoList: [],
  filterDebtInfo: {
    isFilter: false,
    customersSelect: null,
    marginRate: {
      from: null,
      to: null,
    },
    guaranteedAsset: {
      from: null,
      to: null,
    },
    debt: {
      from: null,
      to: null,
    },
    marginLoanDebt: {
      from: null,
      to: null,
    },
    marginInterest: {
      from: null,
      to: null,
    },
    percentDebtPerNav: {
      from: null,
      to: null,
    },
    percentDebtPerMargin: {
      from: null,
      to: null,
    },
  },
  filteredDebtInfoData: [],

  // Investment
  investmentPortfolioList: [],

  subInvestmentPortfolioList: [],
  filterInvestment: null,
  filteredInvestmentData: [],

  // Loan Info
  filterLoanInfo: {
    isFilter: false,
    customersSelect: null,
    loanStatus: null,
    dueDate: {
      from: null,
      to: null,
    },
    openingDebt: {
      from: null,
      to: null,
    },
    remainDebt: {
      from: null,
      to: null,
    },
    paidDebt: {
      from: null,
      to: null,
    },
    tempLoanFee: {
      from: null,
      to: null,
    },
    tempInterest: {
      from: null,
      to: null,
    },
    loanDate: {
      from: null,
      to: null,
    },
  },
  filteredLoanInfoData: [],

  // Asset Allocation
  filterAssetAllocation: {
    isFilter: false,
    customers: null,
    nav: {
      from: null,
      to: null,
    },
    proportionOfCash: {
      from: null,
      to: null,
    },
    proportionOfStocks: {
      from: null,
      to: null,
    },
    derivativeProportion: {
      from: null,
      to: null,
    },
    bondProportion: {
      from: null,
      to: null,
    },
    proportionOfFundCertificates: {
      from: null,
      to: null,
    },
  },
  filteredAssetAllocationData: [],

  // Purchasing power
  filterPurchasingPower: null,
  filteredPurchasingPowerData: [],
  fakeList: [],
  pageIndexAssetInfo: 1,
  pageIndexTransactionInfo: 1,
  pageIndexPurchasingPower: 1,
  pageIndexMoneyInfo: 1,
  pageIndexInvestmentPortfolio: 1,
  purchasingPowerList: [],

  assetInfoDetail: null,
  assetSumInfo: null,

  typeSearch: ESearchType.ACCOUNT,

  moneySumInfo: null,

  transactionSumInfo: null,
};

export const assetReducers = createReducer<IAssetState>(
  initialAssetState,

  on(
    AssetAction.search,
    (state, action): IAssetState => ({
      ...state,
      searchValue: action.data,
      pageIndexAssetInfo: initialAssetState.pageIndexAssetInfo,
      pageIndexMoneyInfo: initialAssetState.pageIndexMoneyInfo,
      pageIndexInvestmentPortfolio: initialAssetState.pageIndexInvestmentPortfolio,
      pageIndexPurchasingPower: initialAssetState.pageIndexPurchasingPower,
      pageIndexTransactionInfo: initialAssetState.pageIndexTransactionInfo,
      filterAssetInfo: initialAssetState.filterAssetInfo,
      filterMoneyInfo: initialAssetState.filterMoneyInfo,
      filterInvestment: initialAssetState.filterInvestment,
      filterPurchasingPower: initialAssetState.filterPurchasingPower,
      typeSearch: ESearchType.CODE,
      // filterTransaction: initialAssetState.filterTransaction,
    })
  ),

  on(AssetAction.resetSearch, (state) => ({ ...state, searchValue: initialAssetState.searchValue })),

  on(
    AssetAction.updateGuaranteeData,
    (state, action): IAssetState => ({
      ...state,
      guarantee: action.data,
    })
  ),
  // ASSET INFO
  on(
    AssetAction.setFilterAssetInfo,
    (state, action): IAssetState => ({
      ...state,
      filterAssetInfo: { ...action.params },
      pageIndexAssetInfo: initialAssetState.pageIndexAssetInfo,
      searchValue: initialAssetState.searchValue,
    })
  ),

  on(
    AssetAction.setFilteredDataAssetInfo,
    (state, action): IAssetState => ({
      ...state,
      filteredAssetInfoData: action.data,
    })
  ),

  on(
    AssetAction.resetFilterAssetInfo,
    (state): IAssetState => ({
      ...state,
      filterAssetInfo: initialAssetState.filterAssetInfo,
      pageIndexAssetInfo: initialAssetState.pageIndexAssetInfo,
    })
  ),

  on(
    AssetAction.getDataAssetInfoSuccess,
    (state, action): IAssetState => ({
      ...state,
      assetsInfoList: action.data,
    })
  ),

  // ACCOUNT STATUS
  on(
    AssetAction.setFilterAccountStatus,
    (state, action): IAssetState => ({
      ...state,
      filterAccountStatus: { ...action.params },
    })
  ),

  on(
    AssetAction.setFilteredDataAccountStatus,
    (state, action): IAssetState => ({
      ...state,
      filteredAccountStatusData: [...action.data],
    })
  ),

  on(
    AssetAction.resetFilterAccountStatus,
    (state): IAssetState => ({
      ...state,
      filterAccountStatus: {
        ...initialAssetState.filterAccountStatus,
      },
    })
  ),

  // TRANSACTION
  on(
    AssetAction.setFilterTransaction,
    (state, action): IAssetState => ({
      ...state,
      filterTransaction: { ...action.params },
      pageIndexTransactionInfo: initialAssetState.pageIndexTransactionInfo,
    })
  ),

  on(
    AssetAction.setFilteredDataTransaction,
    (state, action): IAssetState => ({
      ...state,
      filteredTransactionData: [...action.data],
      // searchValue: initialAssetState.searchValue,
    })
  ),

  on(
    AssetAction.resetFilterTransaction,
    (state): IAssetState => ({
      ...state,
      filterTransaction: {
        ...initialAssetState.filterTransaction,
      },
      // searchValue: initialAssetState.searchValue,
      pageIndexTransactionInfo: initialAssetState.pageIndexTransactionInfo,
    })
  ),

  on(AssetAction.getTransactionInfoSuccess, (state, action): IAssetState => {
    let sumAccountNumber = action.data.length,
      sumBuyTransactionValue = 0,
      sumSellTransactionValue = 0,
      sumTotalTransactionValue = 0,
      sumRevenueFee = 0,
      sumStockExchangeFee = 0,
      sumOtherFees = 0,
      sumDepositoryFee = 0,
      sumNetTransactionFee = 0;
    action.data.forEach((item: ITransactionInfoResponse) => {
      const {
        totalTransactionValue,
        buyTransactionValue,
        sellTransactionValue,
        revenueFee,
        stockExchangeFee,
        depositoryFee,
        otherFees,
        netTransactionFee,
      } = item;
      sumBuyTransactionValue += buyTransactionValue;
      sumSellTransactionValue += sellTransactionValue;
      sumTotalTransactionValue += totalTransactionValue;
      sumRevenueFee += revenueFee;
      sumStockExchangeFee += stockExchangeFee ?? 0;
      sumOtherFees += otherFees;
      sumDepositoryFee += depositoryFee;
      sumNetTransactionFee += netTransactionFee ?? 0;
    });

    const transactionSumInfo: ITransactionsSumInfo = {
      sumAccountNumber,
      sumBuyTransactionValue,
      sumSellTransactionValue,
      sumTotalTransactionValue,
      sumRevenueFee,
      sumStockExchangeFee,
      sumOtherFees,
      sumDepositoryFee,
      sumNetTransactionFee,
    };
    return {
      ...state,
      transactionList: action.data,
      transactionSumInfo,
    };
  }),

  // MONEY INFO
  on(
    AssetAction.getDataMoneyInfoSuccess,
    (state, action): IAssetState => ({
      ...state,
      moneyInfoList: [...action.data],
    })
  ),

  on(
    AssetAction.setFilterMoneyInfo,
    (state, action): IAssetState => ({
      ...state,
      filterMoneyInfo: { ...action.params },
      pageIndexMoneyInfo: initialAssetState.pageIndexMoneyInfo,
      searchValue: initialAssetState.searchValue,
    })
  ),

  on(
    AssetAction.setFilteredDataMoneyInfo,
    (state, action): IAssetState => ({
      ...state,
      filteredMoneyInfoData: action.data,
    })
  ),

  on(
    AssetAction.resetFilterMoneyInfo,
    (state): IAssetState => ({
      ...state,
      filterMoneyInfo: initialAssetState.filterMoneyInfo,
      pageIndexMoneyInfo: initialAssetState.pageIndexMoneyInfo,
    })
  ),

  // DEPOSIT INFO
  on(
    AssetAction.setFilterDepositInfo,
    (state, action): IAssetState => ({
      ...state,
      filterDepositInfo: { ...action.params },
    })
  ),

  on(
    AssetAction.setFilteredDataDepositInfo,
    (state, action): IAssetState => ({
      ...state,
      filteredDepositInfoData: [...action.data],
    })
  ),

  on(
    AssetAction.resetFilterDepositInfo,
    (state): IAssetState => ({
      ...state,
      filterDepositInfo: {
        ...initialAssetState.filterDepositInfo,
      },
    })
  ),

  // DEBT INFO
  on(
    AssetAction.setFilterDebtInfo,
    (state, action): IAssetState => ({
      ...state,
      filterDebtInfo: { ...action.params },
    })
  ),

  on(
    AssetAction.setFilteredDataDebtInfo,
    (state, action): IAssetState => ({
      ...state,
      filteredDebtInfoData: [...action.data],
    })
  ),

  on(
    AssetAction.resetFilterDebtInfo,
    (state): IAssetState => ({
      ...state,
      filterDebtInfo: {
        ...initialAssetState.filterDebtInfo,
      },
    })
  ),

  on(
    AssetAction.getDataDebtInfoSuccess,
    (state, action): IAssetState => ({
      ...state,
      debtInfoList: action.data,
    })
  ),

  // LOAN INFO
  on(
    AssetAction.setFilterLoanInfo,
    (state, action): IAssetState => ({
      ...state,
      filterLoanInfo: { ...action.params },
    })
  ),

  on(
    AssetAction.setFilteredDataLoanInfo,
    (state, action): IAssetState => ({
      ...state,
      filteredLoanInfoData: [...action.data],
    })
  ),

  on(
    AssetAction.resetFilterLoanInfo,
    (state): IAssetState => ({
      ...state,
      filterLoanInfo: {
        ...initialAssetState.filterLoanInfo,
      },
    })
  ),

  // INVESTMENT
  on(
    AssetAction.setFilterInvestment,
    (state, action): IAssetState => ({
      ...state,
      filterInvestment: { ...action.params },
      searchValue: initialAssetState.searchValue,
    })
  ),

  on(
    AssetAction.setFilteredDataInvestment,
    (state, action): IAssetState => ({
      ...state,
      filteredInvestmentData: action.data,
    })
  ),

  on(
    AssetAction.resetFilterInvestment,
    (state): IAssetState => ({
      ...state,
      filterInvestment: initialAssetState.filterInvestment,
      pageIndexInvestmentPortfolio: initialAssetState.pageIndexInvestmentPortfolio,
    })
  ),

  on(
    AssetAction.getInvestmentPortfolioSuccess,
    (state, action): IAssetState => ({
      ...state,
      investmentPortfolioList: action.data,
    })
  ),

  on(
    AssetAction.getSubInvestmentPortfolioSuccess,
    (state, action): IAssetState => ({
      ...state,
      subInvestmentPortfolioList: action.data,
    })
  ),

  on(
    AssetAction.resetSubInvestmentPortfolio,
    (state): IAssetState => ({
      ...state,
      subInvestmentPortfolioList: initialAssetState.subInvestmentPortfolioList,
    })
  ),

  // ASSET ALLOCATION
  on(
    AssetAction.setFilterAssetAllocation,
    (state, action): IAssetState => ({
      ...state,
      filterAssetAllocation: { ...action.params },
    })
  ),

  on(
    AssetAction.setFilteredDataAssetAllocation,
    (state, action): IAssetState => ({
      ...state,
      filteredAssetAllocationData: [...action.data],
    })
  ),

  on(
    AssetAction.resetFilterAssetAllocation,
    (state): IAssetState => ({
      ...state,
      filterAssetAllocation: {
        ...initialAssetState.filterAssetAllocation,
      },
    })
  ),
  on(
    AssetAction.setFilterPurchasingPower,
    (state, action): IAssetState => ({
      ...state,
      filterPurchasingPower: {
        ...action.params,
      },
      pageIndexPurchasingPower: initialAssetState.pageIndexPurchasingPower,
    })
  ),

  on(
    AssetAction.setFilteredPurchasingPower,
    (state, action): IAssetState => ({
      ...state,
      filteredPurchasingPowerData: action.data,
    })
  ),
  on(
    AssetAction.resetFilterPurchasingPower,
    (state): IAssetState => ({
      ...state,
      filterPurchasingPower: initialAssetState.filterPurchasingPower,
      pageIndexPurchasingPower: initialAssetState.pageIndexPurchasingPower,
    })
  ),
  on(
    AssetAction.saveListFakeData,
    (state, action): IAssetState => ({
      ...state,
      fakeList: action.listFake,
    })
  ),
  on(
    AssetAction.getDataPurchasingPowerInfoSuccess,
    (state, action): IAssetState => ({
      ...state,
      purchasingPowerList: action.data,
    })
  ),
  on(
    AssetAction.updatePageIndexAssetInfo,
    (state, action): IAssetState => ({
      ...state,
      pageIndexAssetInfo: action.pageIndex,
    })
  ),

  on(
    AssetAction.updatePageIndexTransactionInfo,
    (state, action): IAssetState => ({
      ...state,
      pageIndexTransactionInfo: action.pageIndex,
    })
  ),

  on(
    AssetAction.updatePageIndexPurchasingPower,
    (state, action): IAssetState => ({
      ...state,
      pageIndexPurchasingPower: action.pageIndex,
    })
  ),

  on(
    AssetAction.updatePageIndexMoneyInfo,
    (state, action): IAssetState => ({
      ...state,
      pageIndexMoneyInfo: action.pageIndex,
    })
  ),
  on(
    AssetAction.updatePageIndexInvestmentPortfolio,
    (state, action): IAssetState => ({
      ...state,
      pageIndexInvestmentPortfolio: action.pageIndex,
    })
  ),

  // Thoong tin tai san deital

  on(
    AssetAction.getAssetInfoDetailSuccess,
    (state, action): IAssetState => ({
      ...state,
      assetInfoDetail: action.data,
    })
  ),
  on(
    AssetAction.resetAssetInfoDetailData,
    (state, action): IAssetState => ({
      ...state,
      assetInfoDetail: initialAssetState.assetInfoDetail,
    })
  ),

  on(
    AssetAction.getAssetSumInfoSuccess,
    (state, action): IAssetState => ({
      ...state,
      assetSumInfo: action.data,
    })
  ),

  on(
    AssetAction.searchByAccount,
    (state, action): IAssetState => ({
      ...state,
      searchValue: action.account,
      typeSearch: ESearchType.ACCOUNT,
      filterInvestment: initialAssetState.filterInvestment,
    })
  ),

  on(
    AssetAction.getMoneySumInfoSuccess,
    (state, action): IAssetState => ({
      ...state,
      moneySumInfo: action.data,
    })
  )
);
