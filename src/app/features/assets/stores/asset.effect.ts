import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { LoadingService, MessageService } from 'src/app/core/services';
import {
  getAssetInfoDetail,
  getAssetInfoDetailSuccess,
  getAssetInfoWithFilter,
  getAssetSumInfoSuccess,
  getDataAssetInfo,
  getDataAssetInfoSuccess,
  getDataDebtInfo,
  getDataDebtInfoFail,
  getDataDebtInfoSuccess,
  getDataMoneyInfo,
  getDataMoneyInfoFail,
  getDataMoneyInfoSuccess,
  getDataMoneyInfoWithFilter,
  getDataPurchasingPowerInfo,
  getDataPurchasingPowerInfoSuccess,
  getInvestmentPortfolio,
  getInvestmentPortfolioSuccess,
  getMoneySumInfoSuccess,
  getSubInvestmentPortfolio,
  getSubInvestmentPortfolioSuccess,
  getTransactionInfo,
  getTransactionInfoSuccess,
  setFilteredDataAssetInfo,
  setFilteredDataInvestment,
  setFilteredDataMoneyInfo,
  setFilteredPurchasingPower,
} from './asset.actions';
import { catchError, finalize, map, of, switchMap, tap, withLatestFrom } from 'rxjs';
import { AssetsService } from '../services/assets.service';
import { SharedService } from 'src/app/shared/services/shared.service';
import { IFilterInvestmentParam, IPayloadInvestment, IPayloadTransactionInfo } from '../models/asset';
import {
  selectFilterAssetInfo$,
  selectFilterInvestment$,
  selectFilterMoneyInfo$,
  selectFilterPuschasingPower$,
  selectFilterTransaction$,
  selectSearchType$,
  selectSearchValue$,
} from './asset.selectors';
import { selectAllAccountNumberListByBrokerView$, selectAllStockList$ } from 'src/app/stores/shared/shared.selectors';
import { HttpErrorResponse } from '@angular/common/http';
import { ESearchType } from '../constant/assets';

/**
 * AssetsEffects
 */
@Injectable()
export class AssetsEffects {
  constructor(
    private readonly actions$: Actions,
    private readonly loadingService: LoadingService,
    private readonly store: Store,
    private readonly messageService: MessageService,
    private readonly assetService: AssetsService,
    private readonly sharedService: SharedService
  ) {}
  // Thông tin tài sản
  getAssetsInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getDataAssetInfo, getAssetInfoWithFilter),
      withLatestFrom(this.store.select(selectFilterAssetInfo$)),
      tap(() => this.loadingService.show()),
      switchMap(([action, filter]) => {
        const { accountNumbers, brokerCode } = action.assetCustomerInfo;

        const newPayload = {
          accountNumbers: !filter ? accountNumbers : [], // when filter send [] accountNumbers
          brokerCodes: brokerCode,
          filterAssetsInfo: filter,
        };

        return this.assetService.getAssetsInfo(newPayload).pipe(
          finalize(() => this.loadingService.hide()),
          map((res) => ({ res, filter }))
        );
      }),
      map(({ res, filter }) => {
        const resCustom = res.map((r) => {
          return {
            ...r,
            id: r.accountNumber,
            marginNav: {
              numberMargin: +r.marginNav?.numberMargin || 0,
              percentMargin: +(+r.marginNav?.percentMargin * 100).toFixed(2) || 0,
            },
            marginStock: {
              numberMargin: +r.marginStock?.numberMargin || 0,
              percentMargin: +(+r.marginStock?.percentMargin * 100).toFixed(2) || 0,
            },
            marginRate: +r.marginRate * 100,
            children: r.children.map((child) => {
              return {
                ...child,
                id: `${r.accountNumber} - ${child.subAccountNumber}`,
                marginNav: {
                  numberMargin: +child.marginNav?.numberMargin || 0,
                  percentMargin: +(+child.marginNav?.percentMargin * 100).toFixed(2) || 0,
                },
                marginStock: {
                  numberMargin: +child.marginStock?.numberMargin || 0,
                  percentMargin: +(+child.marginStock?.percentMargin * 100).toFixed(2) || 0,
                },
                marginRate: +child.marginRate * 100,
              };
            }),
          };
        });

        return filter ? setFilteredDataAssetInfo({ data: resCustom }) : getDataAssetInfoSuccess({ data: resCustom });
      }),
      catchError((error) => {
        this.messageService.error(error.error.message);
        this.loadingService.hide();
        return of();
      })
    );
  });

  getTotalAssetsInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getAssetInfoWithFilter),
      withLatestFrom(this.store.select(selectFilterAssetInfo$)),
      tap(() => this.loadingService.show()),
      switchMap(([action, filter]) => {
        const { accountNumbers, brokerCode } = action.assetCustomerInfo;

        const newPayload = {
          accountNumbers: !filter ? accountNumbers : [], // when filter send [] accountNumbers
          brokerCodes: brokerCode,
          filterAssetsInfo: filter,
        };

        return this.assetService.getSumValueAssetsInfo(newPayload).pipe(
          finalize(() => this.loadingService.hide()),
          map((res) => res)
        );
      }),
      map((res) => {
        return getAssetSumInfoSuccess({ data: res });
      }),
      catchError((error) => {
        this.messageService.error(error.error.message);
        this.loadingService.hide();
        return of();
      })
    );
  });

  // getAssetsInfos$ = createEffect(() => {
  //   return this.actions$.pipe(
  //     ofType(getDataAssetInfo),
  //     tap(() => this.loadingService.show()),
  //     switchMap(({ payload }) => {
  //       return this.assetService.getAssetsInfo(payload).pipe(
  //         tap(() => this.loadingService.hide()),
  //         switchMap((res) => {
  //           const successAction$ = of(getDataAssetInfoSuccess({ data: res }));
  //           const polling$ = interval(5000).pipe(
  //             switchMap(() => this.assetService.getAssetsInfo(payload)),
  //             map((res) => {
  //               return getDataAssetInfoSuccess({ data: res });
  //             })
  //           );

  //           return successAction$.pipe(
  //             switchMap(() => polling$),
  //             takeUntil(this.actions$.pipe(ofType(clearIntervalAssetInfo)))
  //           );
  //         }),
  //         finalize(() => this.loadingService.hide())
  //       );
  //     })
  //   );
  // });

  // Thông tin Tiền
  getMoneyInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getDataMoneyInfo, getDataMoneyInfoWithFilter),
      withLatestFrom(this.store.select(selectFilterMoneyInfo$)),
      tap(() => this.loadingService.show()),
      switchMap(([action, filter]) => {
        const { accountNumbers, brokerCode } = action.moneyCustomerInfo;

        const newPayload = {
          accountNumbers: !filter ? accountNumbers : [], // when filter send [] accountNumbers
          brokerCodes: brokerCode,
          filterMoneyInfo: filter,
        };
        return this.assetService.getMoneyInfo(newPayload).pipe(
          finalize(() => this.loadingService.hide()),
          map((res) => ({ res, filter }))
        );
      }),
      map(({ res, filter }) => {
        const resCustom = res.map((r) => {
          return {
            ...r,
            id: r.accountNumber,
            marginCash: {
              ...r.marginCash,
              percentMargin: +(+r.marginCash?.percentMargin * 100).toFixed(2) || 0,
            },
            children: r.children.map((child) => {
              return {
                ...child,
                id: `${r.accountNumber} - ${child.subAccNumber}`,
              };
            }),
          };
        });
        return filter ? setFilteredDataMoneyInfo({ data: resCustom }) : getDataMoneyInfoSuccess({ data: resCustom });
      }),
      catchError((error) => {
        this.messageService.error(error.error.message);
        this.loadingService.hide();
        return of(getDataMoneyInfoFail());
      })
    );
  });

  getTotalMoneyInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getDataMoneyInfoWithFilter),
      withLatestFrom(this.store.select(selectFilterMoneyInfo$)),
      tap(() => this.loadingService.show()),
      switchMap(([action, filter]) => {
        const { accountNumbers, brokerCode } = action.moneyCustomerInfo;

        const newPayload = {
          accountNumbers: !filter ? accountNumbers : [], // when filter send [] accountNumbers
          brokerCodes: brokerCode,
          filterMoneyInfo: filter,
        };

        return this.assetService.getSumValueMoneyInfo(newPayload).pipe(
          finalize(() => this.loadingService.hide()),
          map((res) => res)
        );
      }),
      map((res) => {
        return getMoneySumInfoSuccess({ data: res });
      }),
      catchError((error) => {
        this.messageService.error(error.error.message);
        this.loadingService.hide();
        return of();
      })
    );
  });

  // Thông tin dư nợ
  getDebtInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getDataDebtInfo),
      tap(() => this.loadingService.show()),
      switchMap(({ payload }) => {
        return this.assetService.getDebtInfo(payload).pipe(finalize(() => this.loadingService.hide()));
      }),
      map((res) => {
        const resCustom = res.map((r) => {
          return {
            ...r,
            id: r.accountNumber,
            children: r.children.map((child) => {
              return {
                ...child,
                id: `${r.accountNumber} - ${child.subAccNumber}`,
              };
            }),
          };
        });
        return getDataDebtInfoSuccess({ data: resCustom });
      }),
      catchError((error) => {
        this.messageService.error(error.error.message);
        this.loadingService.hide();
        return of(getDataDebtInfoFail());
      })
    );
  });

  // Thông tin sức mua
  getPurchasingPowerInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getDataPurchasingPowerInfo),
      withLatestFrom(this.store.select(selectFilterPuschasingPower$)),
      tap(() => this.loadingService.show()),
      switchMap(([action, filter]) => {
        const { accountNumbers, brokerCode } = action.purchasingCustomerInfo;

        const newPayload = {
          accountNumbers: !filter ? accountNumbers : [], // when filter send [] accountNumbers
          brokerCodes: brokerCode,
          filterPurchasingPower: filter,
        };
        return this.assetService.getPurchasingPowerInfo(newPayload).pipe(
          finalize(() => this.loadingService.hide()),
          map((res) => ({ res, filter }))
        );
      }),
      map(({ res, filter }) => {
        const resCustom = res.map((r) => {
          return {
            ...r,
            id: r.accountNumber,
            children: r.children.map((child) => {
              return {
                ...child,
                id: `${r.accountNumber} - ${child.subAccount}`,
              };
            }),
          };
        });
        return filter
          ? setFilteredPurchasingPower({ data: resCustom })
          : getDataPurchasingPowerInfoSuccess({ data: resCustom });
      }),
      catchError((error) => {
        this.messageService.error(error.error.message);
        this.loadingService.hide();
        return of();
      })
    );
  });

  // Danh mục đầu tư
  getInvestmentPorfolioInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getInvestmentPortfolio),
      withLatestFrom(
        this.store.select(selectFilterInvestment$),
        this.store.select(selectSearchValue$),
        this.store.select(selectAllStockList$),
        this.store.select(selectAllAccountNumberListByBrokerView$),
        this.store.select(selectSearchType$)
      ),
      tap(() => this.loadingService.show()),
      switchMap(([action, filter, search, stockCode, accountNos, type]) => {
        const { brokerCode, stockCodes } = action.data;
        const filterPayload: IFilterInvestmentParam | null = filter
          ? { ...filter, ...(filter && { accountNo: filter?.accountNo ?? [], searchValue: search }) }
          : null;
        if (filterPayload) delete filterPayload.isFilter;
        const filterSearch = {
          searchValue: type === ESearchType.CODE ? search : '',
          stockCode: !filter ? [] : stockCode.map((t) => t.id),
          accountNo: type === ESearchType.ACCOUNT ? [search] : [],
        };

        const filterObjectKeysPayload = filterPayload && Object.keys(filterPayload).length ? filterPayload : null;
        const stockCodesFilter = filterObjectKeysPayload?.stockCode?.length
          ? filterObjectKeysPayload.stockCode
          : stockCodes;
        if (filterObjectKeysPayload) filterObjectKeysPayload.stockCode = stockCodesFilter;
        filterSearch.stockCode = stockCodesFilter;
        const newPayload: IPayloadInvestment = {
          stockCodes: stockCodesFilter,
          brokerCodes: brokerCode,
          filterPortfolioInvestmentDTO: search && !filter ? { ...filterSearch } : filterObjectKeysPayload,
        };
        return this.assetService.getInvestmentPortfolioInfo(newPayload).pipe(
          finalize(() => this.loadingService.hide()),
          catchError((error: HttpErrorResponse) => {
            this.loadingService.hide();
            this.messageService.error(error.error);
            return of({ data: [] });
          }),
          map((res) => ({ res, filter, search }))
        );
      }),

      map(({ res, filter, search }) => {
        const children = { id: '1' } as any;
        const convertData = res.data.map((d) => ({
          ...d,
          subAccount: d.accQty,
          ...(d.accQty && { children: [{ ...children }], isCallAPIChildren: true }),
          // ...((filter || search) && {
          //   children: (d.children ?? []).map((c) => ({
          //     ...c,
          //   })),
          // }),
        }));

        return filter
          ? setFilteredDataInvestment({ data: convertData })
          : getInvestmentPortfolioSuccess({ data: convertData });
      })
    );
  });

  getSubInvestmentPorfolioInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getSubInvestmentPortfolio),
      withLatestFrom(
        this.store.select(selectSearchValue$),
        this.store.select(selectSearchType$),
        this.store.select(selectFilterInvestment$)
      ),
      tap(() => this.loadingService.show()),
      switchMap(([action, search, typeSearch, filter]) => {
        const payload = { ...action.payload };
        if (typeSearch === ESearchType.ACCOUNT && search) {
          payload.filterPortfolioInvestmentDTO = { accountNo: [search] };
        }
        if (filter) {
          payload.filterPortfolioInvestmentDTO = filter;
        }
        return this.assetService
          .getSubInvestmentPortfolioInfo(payload)
          .pipe(finalize(() => this.loadingService.hide()));
      }),
      catchError((error) => {
        this.messageService.error(error.error);
        return of({ data: [] });
      }),
      map((res) => {
        return getSubInvestmentPortfolioSuccess({ data: res.data });
      })
    );
  });

  // Thông tin giao dịch
  getTransactionInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getTransactionInfo),
      withLatestFrom(this.store.select(selectFilterTransaction$)),
      tap(() => this.loadingService.show()),
      switchMap(([{ payload }, filter]) => {
        const filterData = {
          totalTransaction: { from: filter?.totalTransactionValue.start, to: filter?.totalTransactionValue.end },
          buyTransaction: { from: filter?.buyTransactionValue.start, to: filter?.buyTransactionValue.end },
          sellTransaction: { from: filter?.sellTransactionValue.start, to: filter?.sellTransactionValue.end },
          revenueFee: { from: filter?.revenueFee.start, to: filter?.revenueFee.end },
          stockExchangeFee: { from: filter?.stockExchangeFee.start, to: filter?.stockExchangeFee.end },
          depositoryFee: { from: filter?.depositoryFee.start, to: filter?.depositoryFee.end },
          netTransactionFee: { from: filter?.netTransactionFee.start, to: filter?.netTransactionFee.end },
        };

        const payloadData: IPayloadTransactionInfo = {
          ...payload,
          valueFilterInfo: filter?.isFilter ? { ...filterData } : null,
        };
        return this.assetService.getTransactionInfo(payloadData).pipe(finalize(() => this.loadingService.hide()));
      }),
      map((res) => {
        const resCustom = res.map((r) => {
          const children = { id: 'sample' } as any;
          return {
            ...r,
            id: r.accountNumber,
            ...(!r.children.length && { children: [children] }),
          };
        });
        return getTransactionInfoSuccess({ data: resCustom });
      })
    );
  });

  // thông tin chi tiết
  getAssetInfoDetail$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getAssetInfoDetail),
      tap(() => this.loadingService.show()),
      switchMap(({ data }) => {
        return this.assetService.getAssetInfoDetail(data).pipe(finalize(() => this.loadingService.hide()));
      }),
      map((res) => getAssetInfoDetailSuccess({ data: res.data }))
    );
  });
}
