import { IOptionCustom } from 'src/app/shared/models/dropdown-item.model';

export interface IAssetDialogData {
  type: EAssetType;
  amount: number;
  percentage: number;
  asset: {
    accountNumber: string;
    customerName: string;
    nav: number;
    proportionOfCash: number;
    percentProportionOfCash: number;
    proportionOfStocks: number;
    percentProportionOfStocks: number;
    derivativeProportion: number;
    percentDerivativeProportion: number;
    bondProportion: number;
    percentBondProportion: number;
    proportionOfFundCertificates: number;
    percentProportionOfFundCertificates: number;
  };
}

export interface IListOptions {
  label?: string;
  value?: number | string;
  name?: string;
  group?: string;
  id?: string;
  isSelect?: boolean;
  [key: string]: string | boolean | number | undefined;
}

export interface IAllocationRef {
  type: EAssetType;
  oldValue?: {
    amount: number;
    percentage: number;
  };
  newValue?: {
    amount: number;
    percentage: number;
  };
}

export interface IAllocationResult {
  allocatedSource: IAllocationRef;
  allocatedTarget: IAllocationRef;
  sentBy: string;
  sentDate: Date;
  accountNumber: string;
  customerName: string;
  note: string;
  title: string;
  type?: string;
}

export enum EAssetType {
  CASH = 'CASH',
  STOCK = 'STOCK',
  BOND = 'BOND',
  DERIVATIVE = 'DERIVATIVE',
  CERTIFICATE = 'CERTIFICATE',
}

export const CONVERT_ASSET_TYPE_TO_LABEL: { [key: string]: string } = {
  [EAssetType.CASH]: 'MES-78',
  [EAssetType.STOCK]: 'MES-91',
  [EAssetType.BOND]: 'MES-95',
  [EAssetType.DERIVATIVE]: 'MES-93',
  [EAssetType.CERTIFICATE]: 'MES-92',
};

export const CONVERT_ASSET_TYPE_TO_CLASS: { [key: string]: string } = {
  [EAssetType.CASH]: 'cash',
  [EAssetType.STOCK]: 'stock',
  [EAssetType.BOND]: 'bond',
  [EAssetType.DERIVATIVE]: 'derivative',
  [EAssetType.CERTIFICATE]: 'certificate',
};

export const MARGIN_STATUS_MAP: { [key: number]: string } = {
  0: 'NORMAL',
  1: 'WARNING 01',
  2: 'WARNING 02',
  3: 'FORCE SELL',
};

export const MARGIN_STATUS_CLASS_MAP: { [key: number]: string } = {
  0: 'normal',
  1: 'warning-1',
  2: 'warning-2',
  3: 'force-sell',
};

export const LOAN_STATUS_MAP: { [key: number]: string } = {
  0: 'Trong hạn',
  1: 'Quá hạn',
};

export const LOAN_STATUS_CLASS_MAP: { [key: number]: string } = {
  0: 'in-due-date',
  1: 'out-of-date',
};

export const MARGIN_RATIO_VIOLATION_MAP_CLASS: { [key: number]: string } = {
  0: 'none',
  1: 'warning-1',
  2: 'warning-2',
  3: 'warning-3',
  4: 'additional-rate',
  5: 'mortgage-settlement-rate',
};

export const OVERDUE_DEBT_MAP: { [key: number]: string } = {
  0: 'Không',
  1: 'Có',
  2: 'Nợ Margin',
  3: 'Nợ Khác',
};

export const OVERDUE_DEBT_MAP_CLASS: { [key: number]: string } = {
  0: 'none',
  1: 'debt',
  2: 'debt',
  3: 'debt',
};

export const FEE_DEBT_MAP: { [key: number]: string } = {
  0: 'Không',
  1: 'Có',
  2: 'Phí SMS',
  3: 'Phí lưu ký',
  4: 'Phí quản lý TSKQ',
  5: 'Phí chuyển khoản',
};

export const FEE_DEBT_MAP_CLASS: { [key: number]: string } = {
  0: 'none',
  1: 'debt',
  2: 'debt',
  3: 'debt',
  4: 'debt',
  5: 'debt',
};

export const LIST_OF_CUSTOMER = [
  {
    group: '069C-125485',
    name: 'Phạm Thị Thu Trang',
  },
  {
    group: '069C-586547',
    name: 'Đặng Hoàng An Nhiên',
  },
  {
    group: '069C-918882',
    name: 'Phạm Tiến Nam Phương',
  },
  {
    group: '069C-891135',
    name: 'Trần Văn Hậu',
  },
  {
    group: '069C-251114',
    name: 'Công ty cổ phần địa ốc Ngọc Minh Huy',
  },
  {
    group: '069C-637085',
    name: 'Công ty TNHH Tigon 68',
  },
  {
    group: '069C-316087',
    name: 'Công ty TNHH Mica Group',
  },
  {
    group: '069C-388482',
    name: 'Công ty cổ phần Money Max',
  },
  {
    group: '069C-862656',
    name: 'Công ty TNHH du lịch Cá Voi Xanh',
  },
  {
    group: '069C-252138',
    name: 'Công ty TNHH xây dựng và đầu tư Phú Khang',
  },
  {
    group: '069C-400190',
    name: 'Ngô Thị Hằng',
  },
  {
    group: '069C-883962',
    name: 'Bùi Thị Hạnh',
  },
];

export const RECOMMEND_ALLOCATION_LABEL: { [key: number]: string } = {
  0: 'TIỀN MẶT',
  1: 'CỔ PHIẾU',
  2: 'TRÁI PHIẾU',
  3: 'PHÁI SINH',
  4: 'CHỨNG CHỈ QUỸ',
};

export const RECOMMEND_ALLOCATION_CLASS: { [key: number]: string } = {
  0: 'cash',
  1: 'stock',
  2: 'bonds',
  3: 'derivative',
  4: 'fund-certificates',
};

export const RECOMMEND_ALLOCATION_PROPROTION: { [key: number]: string } = {
  0: 'tiền mặt',
  1: 'cổ phiếu',
  2: 'trái phiếu',
  3: 'phái sinh',
  4: 'CC quỹ',
};

export enum EStatusLoanAsset {
  VALID,
  EXPIRE,
}

export const CONVERT_STATUS_LOAN_ASSET_TO_CLASS: { [key: number]: string } = {
  [EStatusLoanAsset.VALID]: 'valid-cls',
  [EStatusLoanAsset.EXPIRE]: 'expire-cls',
};

export const CONVERT_STATUS_LOAN_ASSET_TO_LABEL: { [key: number]: string } = {
  [EStatusLoanAsset.VALID]: 'MES-458',
  [EStatusLoanAsset.EXPIRE]: 'MES-459',
};

export interface ICustomers {
  group: string;
  name: string;
}

export enum ESearchType {
  ACCOUNT,
  CODE,
}

export const searchTypeOptions: IOptionCustom[] = [
  { label: 'MES-15', value: ESearchType.ACCOUNT },
  { label: 'MES-157', value: ESearchType.CODE },
];
