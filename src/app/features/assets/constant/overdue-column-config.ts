import { customNumberFormat } from "src/app/shared/utils/currency";
import { LOAN_STATUS_CLASS_MAP, LOAN_STATUS_MAP } from "./assets";
import { IColumnConfig } from "@shared/models";


export const OVERDUE_COLUMN_CONFIG = [
    {
        name: '#món vay',
        minWidth: 93,
        width: 93,
        tag: 'loanId',
        isDisplay: true,
        align: 'start',
    },
    {
        name: '<PERSON><PERSON><PERSON> đáo hạn',
        minWidth: 116,
        width: 116,
        tag: 'dueDate',
        isDisplay: true,
        align: 'start',
    },
    {
        name: 'Tình trạng',
        minWidth: 116,
        width: 116,
        tag: 'status',
        isDisplay: true,
        displayValueFn: (v: any) => {
            if (v == null) return '...';
            else return LOAN_STATUS_MAP[v];
        },
        dynamicClass: (v: any) => {
            if (v == null) return 'null';
            else return LOAN_STATUS_CLASS_MAP[v];
        },
        align: 'start',
    },
    {
        name: '<PERSON>ư nợ còn lại',
        minWidth: 156,
        width: 156,
        tag: 'restDebt',
        isDisplay: true,
        displayValueFn: (v: number) => {
            if (!v) return '-';
            else return customNumberFormat(v);
        },
    },
] as IColumnConfig[];