import { Component, Inject, OnInit } from '@angular/core';
import { DestroyService, DialogService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { GuaranteeConfirmComponent } from 'src/app/shared/components/guarantee-confirm/guarantee-confirm.component';
import { take } from 'rxjs';
import { Store } from '@ngrx/store';
import { updateGuaranteeData } from '../../stores/asset.actions';

/**
 * SubmitGuaranteeOptionsComponent
 */
@Component({
  selector: 'app-submit-guarantee-options',
  templateUrl: './submit-guarantee-options.component.html',
  styleUrl: './submit-guarantee-options.component.scss',
})
export class SubmitGuaranteeOptionsComponent implements OnInit {
  accountNumber!: string;
  submitGuarantee!: number;
  /**
   * Constructor
   * @param popoverRef - PopoverRef
   * @param dialogService DialogService
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(
    @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly dialogService: DialogService,
    private readonly _destroy: DestroyService,
    private readonly store: Store
  ) {}

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.accountNumber = this.popoverRef.componentConfig.accountNumber;
    this.submitGuarantee = this.popoverRef.componentConfig.submitGuarantee;
  }

  /**
   * SendNotify
   * @param {string} accountNumber - accountNumber
   */
  sendNotify(accountNumber: string) {
    this.popoverRef.close();
    this.dialogService.openPopUp(GuaranteeConfirmComponent, {
      width: '360px',
      height: '273px',
      panelClass: [''],
      data: {
        action: 'notify',
        info: [accountNumber],
        title: ['MES-15'],
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-144',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
    });
  }

  /**
   * CollectGuarantee
   * @param {string} accountNumber - accountNumber
   * @param {number} guarantee - submitGuarantee
   */
  collectGuarantee(accountNumber: string, guarantee: number) {
    this.popoverRef.close();
    const ref = this.dialogService.openPopUp(GuaranteeConfirmComponent, {
      width: '360px',
      height: '297px',
      panelClass: [''],
      data: {
        action: 'submitGuarantee',
        info: [accountNumber, guarantee],
        title: ['MES-15', 'MES-142'],
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-143',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        this.popoverRef.close();
        const info = {
          type: v.type,
          accountNumber: v.data[0],
          value: v.data[1],
        };
        this.store.dispatch(updateGuaranteeData({ data: info }));
      });
  }
}
