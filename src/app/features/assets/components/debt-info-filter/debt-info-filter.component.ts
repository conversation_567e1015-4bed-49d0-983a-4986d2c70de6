import { Component, Inject, OnInit, Optional } from '@angular/core';
import { IListOptions } from '../../constant/assets';
import { FormControl } from '@angular/forms';
import { DestroyService } from 'src/app/core/services';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IRangeFilter } from '../../models/asset';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { Store } from '@ngrx/store';
import { selectAllAccountNumberListByBrokerView$ } from 'src/app/stores/shared/shared.selectors';

/**
 * DebtInfoFilterComponent
 */
@Component({
  selector: 'app-debt-info-filter',
  templateUrl: './debt-info-filter.component.html',
  styleUrl: './debt-info-filter.component.scss',
})
export class DebtInfoFilterComponent implements OnInit {
  isSelectAllCustomer = true;

  listFilterCustomerOptions: IListOptions[] = [];
  listFilterCustomerStore: IListOptions[] = [];

  searchCustomerControl = new FormControl();

  startMarginRate = new FormControl();
  endMarginRate = new FormControl();

  startGuaranteedAsset = new FormControl();
  endGuaranteedAsset = new FormControl();

  startDebt = new FormControl();
  endDebt = new FormControl();

  startMarginLoanDebt = new FormControl();
  endMarginLoanDebt = new FormControl();

  startMarginInterest = new FormControl();
  endMarginInterest = new FormControl();

  startPercentDebtPerNav = new FormControl();
  endPercentDebtPerNav = new FormControl();

  startPercentDebtPerMargin = new FormControl();
  endPercentDebtPerMargin = new FormControl();

  /**
   *
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param data
   * @param dialogRefDIFilter
   * @param store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRefDIFilter: MatDialogRef<DebtInfoFilterComponent>,
    private readonly store: Store
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    const { filterOptions } = data;
    const {
      customersSelect,
      marginRate,
      guaranteedAsset,
      debt,
      marginLoanDebt,
      marginInterest,
      percentDebtPerNav,
      percentDebtPerMargin,
    } = filterOptions;

    this.updateListDIFilter(customersSelect);
    this.updateFormControlValueDIFilter(marginRate, this.startMarginRate, this.startMarginRate);
    this.updateFormControlValueDIFilter(guaranteedAsset, this.startGuaranteedAsset, this.endGuaranteedAsset);
    this.updateFormControlValueDIFilter(debt, this.startDebt, this.endDebt);
    this.updateFormControlValueDIFilter(marginLoanDebt, this.startMarginLoanDebt, this.endMarginLoanDebt);
    this.updateFormControlValueDIFilter(marginInterest, this.startMarginInterest, this.endMarginInterest);
    this.updateFormControlValueDIFilter(percentDebtPerNav, this.startPercentDebtPerNav, this.endPercentDebtPerNav);
    this.updateFormControlValueDIFilter(percentDebtPerMargin, this.startPercentDebtPerMargin, this.endPercentDebtPerMargin);
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.listFilterCustomerStore = this.listFilterCustomerOptions;
    this.listenSearchChangeDIFilter()
  }

  private listenSearchChangeDIFilter() {
    this.searchCustomerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterCustomerOptions = this._filterDIFiler(value ?? '', this.listFilterCustomerStore);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * UpdateFormControlValueDIFilter
   * @param date date
   * @param startControl startControl
   * @param endControl endControl
   */
  updateFormControlValueDIFilter(date: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    const { start, end } = date;
    startControl.patchValue(start);
    endControl.patchValue(end);
  }

  /**
   * updateListDIFilter
   * @param data
   * @param type
   */
  updateListDIFilter(data: (string | number)[] | null) {
    const isSelect = (v: string | number) => data === null || data?.includes(v);
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((customerList) => {
        this.listFilterCustomerOptions = customerList.map((customer) => ({
          name: customer.customerName,
          group: customer.accountNumber,
          isSelect: isSelect(customer.accountNumber),
        }));
        this.isSelectAllCustomer = this.listFilterCustomerOptions.every((t) => t.isSelect === true);
      });
  }

  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param type Kiểu giá trị được thay đổi
   * @param section
   * @param item Item thay đổi
   */
  changeSectionsDIFilter(checked: boolean, type: string, section: string, item?: IListOptions) {
    if (type === 'all') {
      this.updateSelectAllDIFilter(checked, section);
    } else if (type === 'item' && item) {
      item.isSelect = checked;
      this.updateSelectAllStatusDIFilter(section);
    }
  }

  /**
   * Cập nhật trạng thái chọn tất cả
   * @param {boolean} checked
   * @param {string} section
   */
  updateSelectAllDIFilter(checked: boolean, section: string) {
    if (section === 'customer') {
      this.isSelectAllCustomer = checked;
      this.listFilterCustomerOptions.forEach((i) => {
        i.isSelect = checked;
      });
    }
  }

  /**
   * Cập nhật trạng thái của SelectAll checkbox
   * @param {string} section
   */
  updateSelectAllStatusDIFilter(section: string) {
    if (section === 'customer') {
      this.isSelectAllCustomer = this.listFilterCustomerOptions.every((t) => t.isSelect);
    }
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    const customersSelect = this.listFilterCustomerOptions.filter((t) => t.isSelect).map((t) => t.name);

    const marginRate = {
      start: this.startMarginRate.value,
      end: this.endMarginRate.value,
    };

    const guaranteedAsset = {
      start: this.startGuaranteedAsset.value,
      end: this.endGuaranteedAsset.value,
    };

    const debt = {
      start: this.startDebt.value,
      end: this.endDebt.value,
    };

    const marginLoanDebt = {
      start: this.startMarginLoanDebt.value,
      end: this.endMarginLoanDebt.value,
    };

    const marginInterest = {
      start: this.startMarginInterest.value,
      end: this.endMarginInterest.value,
    };

    const percentDebtPerNav = {
      start: this.startPercentDebtPerNav.value,
      end: this.endPercentDebtPerNav.value,
    };

    const percentDebtPerMargin = {
      start: this.startPercentDebtPerMargin.value,
      end: this.endPercentDebtPerMargin.value,
    };

    const isFilter = true;

    const optionFilter = {
      isFilter,
      customersSelect,
      marginRate,
      guaranteedAsset,
      debt,
      marginLoanDebt,
      marginInterest,
      percentDebtPerNav,
      percentDebtPerMargin,
    };

    this.dialogRefDIFilter.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * DefaultFilter
   */
  defaultFilterDIF() {
    this.dialogRefDIFilter.close({
      type: 'default',
    });
  }

  /**
   *
   * @param {string} value
   * @param {IListOptions[]} options
   */
  private _filterDIFiler(value: string, options: IListOptions[]): IListOptions[] {
    const filterValue = value.toString().toLowerCase();

    return options.filter(
      (option) => option.name?.toLowerCase().includes(filterValue) || option.group?.toLowerCase().includes(filterValue)
    );
  }
}
