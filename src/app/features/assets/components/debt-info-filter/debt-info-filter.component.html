<div class="debt-info-filter-wrap">
  <div class="debt-info-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="debt-info-content">
    <!-- LEFT -->
    <div class="content-left">
      <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        <div class="search-box">
          <input
            type="text"
            class="input-cls-custom input-style-common typo-body-11 fs-12"
            [placeholder]="'MES-295' | translate"
            [formControl]="searchCustomerControl"
          />
          <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
        </div>
        <div class="option-list-cls">
          <mat-checkbox
            (change)="changeSectionsDIFilter($event.checked, 'all', 'customer')"
            [checked]="isSelectAllCustomer"
            class="checkbox-cls"
          >
            {{ 'MES-58' | translate }}</mat-checkbox
          >
          @for (item of listFilterCustomerOptions; let i = $index; track item) {
          <div class="checkbox-cls-item typo-body-15">
            <mat-checkbox
              (change)="changeSectionsDIFilter($event.checked, 'item', 'customer', item)"
              [checked]="item.isSelect"
              class="checkbox-cls"
            >
              {{ item.group }} - {{ item.name }}</mat-checkbox
            >
          </div>
          }
        </div>
      </div>

      <!-- Tỷ lệ ký quỹ -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-282' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0%'"
              [formControl]="startMarginRate"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endMarginRate"
            />
          </div>
        </div>
      </div>

      <!-- Tài sản đảm bảo -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-296' | translate }} đảm bảo</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startGuaranteedAsset"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endGuaranteedAsset"
            />
          </div>
        </div>
      </div>

      <!-- Tổng dư nợ -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-242' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startDebt"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endDebt"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- RIGHT -->
    <div class="content-right">
      <!-- Lãi vay Margin -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-323' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startMarginInterest"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endMarginInterest"
            />
          </div>
        </div>
      </div>

      <!-- Tỷ lệ Nợ / TTS -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-300' | translate }} / TTS (mua)</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0%'"
              [formControl]="startPercentDebtPerNav"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endPercentDebtPerNav"
            />
          </div>
        </div>
      </div>

      <!-- Tỷ lệ Nợ / DM ky quy -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-300' | translate }} / DM Ký quỹ</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0%'"
              [formControl]="startPercentDebtPerMargin"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endPercentDebtPerMargin"
            />
          </div>
        </div>
      </div>

      <!-- Nợ vay Margin-->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-320' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startMarginLoanDebt"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endMarginLoanDebt"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="footer-filter">
    <div mat-dialog-close (click)="defaultFilterDIF()" class="btn default typo-button-3">
      {{ 'MES-20' | translate }}
    </div>
    <div mat-dialog-close (click)="applyFilter()" class="btn apply typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
