import { Component, Inject, Input } from '@angular/core';
import { DestroyService, DialogService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { AssetInfoDetailComponent } from '../assets-info-detail/assets-info-detail.component';
import { Store } from '@ngrx/store';
import { selectSHSStock$ } from 'src/app/stores/shared/shared.selectors';
import { takeUntil } from 'rxjs';
import { IAllStockList } from 'src/app/shared/models/global';
import { IOptionStock } from 'src/app/features/trade-order/models/trade-order';
import { PlaceOrderPopupComponent } from 'src/app/features/trade-order/components/place-order-popup/place-order-popup.component';

/**
 * OptionsRecommendationComponent
 */
@Component({
  selector: 'app-options-recommendation',
  templateUrl: './options-asset.component.html',
  styleUrls: ['./options-asset.component.scss'],
  providers: [DestroyService],
})
export class OptionsAssetComponent {
  @Input() brokerCode = '';

  @Input() element: any;

  initialStock!: IOptionStock;

  /**
   * Constructor
   * @param popoverRef - PopoverRef
   * @param dialogService - DialogService
   * @param messageService - MessageService
   * @param store Store
   */
  constructor(
    @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly dialogService: DialogService,
    private store: Store,
    private _destroy: DestroyService
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.store
      .select(selectSHSStock$)
      .pipe(takeUntil(this._destroy))
      .subscribe((SHSstock: IAllStockList | null) => {
        if (!SHSstock) return;
        const { shortName, name, id, stock } = SHSstock;
        this.initialStock = {
          value: shortName,
          label: name,
          id: id,
          stoke: stock,
        };
      });
  }

  openAssetInfoDetail() {
    this.popoverRef.close();
    this.dialogService.openPopUp(AssetInfoDetailComponent, {
      width: 'calc(100vw - 200px)',
      height: 'calc(100vh - 50px)',
      panelClass: 'open-asset-info-cls',
      data: {
        status: 'asset-info-detail',
        element: this.element,
        brokerCode: this.brokerCode,
      },
    });
  }

  openPlaceOrderDialog() {
    const { parent, subAccountNumber, subAccount } = this.element;
    const infoCustomer = {
      accountNumber: parent.accountNumber,
      customerName: parent.customerName,
      subAccount: subAccountNumber ?? subAccount,
    };
    this.popoverRef.close();
    this.dialogService.openRightDialog(PlaceOrderPopupComponent, {
      width: '1200px',
      panelClass: 'overlay-place-order',
      data: {
        title: 'MES-651',
        initialStock: this.initialStock,
        isDontResetPage: true,
        infoCustomer,
      },
    });
  }
}
