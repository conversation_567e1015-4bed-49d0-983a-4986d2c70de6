<div class="asset-info-detail-container" #assetInfoDetailRef>
  <div class="asset-info-header">
    <div class="left-box">
      <div class="text-box">
        <div class="typo-body-3">{{ assetInfoData?.customerName }}</div>
        <div class="typo-body-9 code-text">{{ assetInfoData?.accountNumber }}</div>
      </div>
      <!-- <div class="status-box typo-body-9">Diamond</div> -->
    </div>
    <div class="right-box">
      <app-create-place-order-component [infoCustomer]="infoCustomer"></app-create-place-order-component>
      <img mat-dialog-close src="./assets/icons/x-cross.svg" alt="x-cross" />
    </div>
  </div>
  <div class="asset-info-body">
    <div class="asset-nav-bar">
      <a
        class="box-nav-bar"
        (click)="selectNavItem(nav)"
        [attr.data-target]="nav.dataset"
        *ngFor="let nav of menuNavBar; let idx = index"
      >
        <mat-icon
          [ngClass]="nav.class"
          class="mat-icon-cls"
          aria-hidden="false"
          aria-label="icon"
          [svgIcon]="nav.nameIcon"
        ></mat-icon>
        <div class="typo-body-9">{{ nav.name | translate }}</div>
      </a>
    </div>
    <div class="asset-content" (scroll)="handleScroll($event)">
      <!-- Thông tin tài sản -->
      <div id="asset-info-detail" class="box-content">
        <app-asset-layout-component [headerText]="'MES-422'">
          <app-asset-info-component [assetInfo]="assetInfoData?.assetsInfo" bodyContent></app-asset-info-component>
        </app-asset-layout-component>
      </div>

      <!-- Trạng Thái Tài Khoản -->
      <!-- <div id="status-account-detail" class="box-content">
        <app-asset-layout-component [headerText]="'MES-427'">
          <app-status-account-component
            [statusAccount]="assetInfoData?.accountStatus"
            bodyContent
          ></app-status-account-component>
        </app-asset-layout-component>
      </div> -->

      <!-- Thông Tin Giao dịch -->
      <div id="transaction-info-detail" class="box-content">
        <app-asset-layout-component [headerText]="'MES-440'">
          <app-transaction-info-component
            [transactionInfo]="assetInfoData?.transactionInfo"
            bodyContent
          ></app-transaction-info-component>
        </app-asset-layout-component>
      </div>

      <!-- Thông tin tiền -->
      <div id="money-info-detail" class="box-content">
        <app-asset-layout-component [headerText]="'MES-434'">
          <app-money-info-detail-component
            [moneyInfo]="assetInfoData?.moneyInfo"
            bodyContent
          ></app-money-info-detail-component>
        </app-asset-layout-component>
      </div>

      <!-- Thông tin sức mua -->
      <div id="purchasing-power-detail" class="box-content">
        <app-asset-layout-component [headerText]="'MES-307'">
          <!-- <div buttonAction class="btn-box-cls"> -->
          <!-- Cấp bảo lãnh -->
          <!-- <a class="action-box typo-body-9" (click)="openPopoverGuaranteeMoney($event, element)">
              <img src="./assets/icons/add-circle-orange.svg" alt="add-circle-orange" />
              {{ 'MES-75' | translate }}
            </a> -->
          <!-- Thu bảo lãnh -->
          <!-- <a
              class="action-box typo-body-9"
              (click)="collectGuarantee($event, element.accountNumber, element.toSubmitGuarantee)"
            >
              <img src="./assets/icons/money-recive-orange.svg" alt="money-recive-orange" />
              {{ 'MES-140' | translate }}
            </a> -->
          <!-- Gửi thông báo -->
          <!-- <a class="action-box typo-body-9" (click)="sendNotify($event, element.accountNumber)">
              <img src="./assets/icons/notification-bing-orange.svg" alt="notification-bing-orange" />
              {{ 'MES-139' | translate }}
            </a> -->
          <!-- </div> -->
          <app-purchasing-power-detail-component
            [purchasingPowerInfor]="assetInfoData?.purchasingPowerInfo"
            [accountNumber]="assetInfoData?.accountNumber ?? ''"
            bodyContent
          ></app-purchasing-power-detail-component>
        </app-asset-layout-component>
      </div>

      <!-- Thông Tin Ký Quỹ VSD -->
      <!-- <div id="deposit-vsd-detail" class="box-content">
        <app-asset-layout-component [headerText]="'MES-439'">
          <app-deposit-detail-vsd-component
            [depositInfo]="assetInfoData?.depositInfo"
            [accountNumber]="assetInfoData!.accountNumber"
            bodyContent
          ></app-deposit-detail-vsd-component>
        </app-asset-layout-component>
      </div> -->

      <!-- Thông Tin Dư Nợ -->
      <!-- <div id="debt-detail" class="box-content">
        <app-asset-layout-component [headerText]="'MES-449'">
          <app-debt-detail-info-component
            [debtInfo]="assetInfoData?.debtInfo"
            bodyContent
          ></app-debt-detail-info-component>
        </app-asset-layout-component>
      </div> -->

      <!-- Món vay -->
      <!-- <div id="loan-detail" class="box-content">
        <app-asset-layout-component [headerText]="'MES-454'">
          <app-loan-detail-info-component
            [loanInfo]="assetInfoData?.loanInfo"
            bodyContent
          ></app-loan-detail-info-component>
        </app-asset-layout-component>
      </div> -->

      <!-- Danh mục đầu tư -->
      <div id="portfolio-detail" class="box-content">
        <app-asset-layout-component [headerText]="'MES-460'">
          <app-portfolio-info-component
            [portfolioInfo]="assetInfoData?.investmentInfo"
            bodyContent
          ></app-portfolio-info-component>
        </app-asset-layout-component>
      </div>

      <!-- Phân Bổ Tài Sản -->
      <!-- <div id="proportion-asset-detail" class="box-content">
        <app-asset-layout-component [headerText]="'MES-426'">
          <app-proportion-asset
            bodyContent
            [proportionInfo]="assetInfoData?.propotionAsset"
            [labels]="datasetLabels"
            [datasets]="assetInfoDatasets"
          ></app-proportion-asset>
        </app-asset-layout-component>
      </div> -->
    </div>
  </div>
</div>
