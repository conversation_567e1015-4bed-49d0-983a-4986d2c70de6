import {
  AfterViewInit,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Chart } from 'chart.js/auto';
import { IDataSetChart, IProportionAsset } from '../../../models/asset';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { IColumnConfig } from '@shared/models';
import {
  RECOMMEND_ALLOCATION_CLASS,
  RECOMMEND_ALLOCATION_LABEL,
  RECOMMEND_ALLOCATION_PROPROTION,
} from '../../../constant/assets';

/**
 * ProportionAssetComponent
 */
@Component({
  selector: 'app-proportion-asset',
  templateUrl: './proportion-asset.component.html',
  styleUrl: './proportion-asset.component.scss',
})
export class ProportionAssetComponent implements AfterViewInit, OnInit, OnChanges {
  @ViewChild('proportionInfoRef', { static: true }) proportionInfoRef: TemplateRef<any> | null = null;

  @Input() id = 'chart-id-asset';

  @Input() labels = [''];

  @Input() datasets: IDataSetChart[] = [];

  @Input() proportionInfo!: IProportionAsset | undefined;

  chart!: any;

  @Input() dropdownListTime: any[] = [];

  dataNote: any[] = [];

  data: any = [];

  totalNav = '';

  columnConfigs: IColumnConfig[] = [];

  RECOMMEND_ALLOCATION_PROPROTION = RECOMMEND_ALLOCATION_PROPROTION;

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    const cellTemplate = this.proportionInfoRef;

    this.columnConfigs = [
      {
        name: 'Ngày khuyến nghị',
        minWidth: 130,
        width: 130,
        tag: 'recommendDate',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
      },
      {
        name: 'Người gửi khuyến nghị',
        minWidth: 30,
        width: 220,
        tag: 'personSent',
        isDisplay: true,
        resizable: true,
        align: 'start',
        displayValueFn: (v) => `${v.nickName} - ${v.customerName}`,
      },
      {
        name: 'Phân bổ từ',
        minWidth: 30,
        width: 156,
        tag: 'allocationFrom',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => RECOMMEND_ALLOCATION_LABEL[v],
        dynamicClass: (v) => RECOMMEND_ALLOCATION_CLASS[v],
        align: 'center',
      },
      {
        name: 'Phân bổ tới',
        minWidth: 30,
        width: 156,
        tag: 'allocationTo',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => RECOMMEND_ALLOCATION_LABEL[v],
        dynamicClass: (v) => RECOMMEND_ALLOCATION_CLASS[v],
        align: 'center',
      },
      {
        name: 'Tỷ trọng phân bổ',
        minWidth: 30,
        width: 280,
        tag: 'allocationProportion',
        isDisplay: true,
        resizable: true,
        cellTemplate,
        align: 'start',
      },
      {
        name: 'Nội dung',
        minWidth: 30,
        width: 170,
        tag: 'content',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
    ];
  }
  /**
   * NgAfterViewInit
   */
  ngAfterViewInit(): void {
    this.convertTotalMoney();
    setTimeout(() => {
      this.createChart();
      this.convertMoneyShow();
    });
  }

  /**
   * NgOnChanges
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    const { proportionInfo } = changes;

    if (proportionInfo) {
      this.convertTotalMoney();
      this.data = [...(this.proportionInfo?.children ?? [])];
    }
  }

  /**
   * CreateChart
   */
  createChart(): void {
    const ctx = document.getElementById(`${this.id}`) as HTMLCanvasElement;

    const containerWidth = 300;
    const containerHeight = 300;
    ctx.width = containerWidth;
    ctx.height = containerHeight;
    this.chart = new Chart(ctx, {
      type: 'doughnut',

      data: {
        labels: this.labels,
        datasets: this.datasets,
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: false,
            position: 'top',
          },
          title: {
            display: false,
          },
          tooltip: {
            enabled: false,
          },
        },
        rotation: -0.5 * 180 - (25 / 180) * 180,
        cutout: '70%',
      },
    });
  }

  /**
   * ConvertMoneyShow
   */
  convertMoneyShow() {
    const totalValue = (totalValue: number, value: number) => {
      return totalValue + value;
    };
    this.dataNote = this.labels.map((t, i) => ({
      label: t,
      backgroundColor: this.datasets[0].backgroundColor![i],
      price: `${customNumberFormat(this.datasets[0].data[i])} VND (${(
        (this.datasets[0].data[i] / this.datasets[0].data.reduce(totalValue, 0)) *
        100
      ).toFixed(2)} %)`,
    }));
  }

  /**
   * CustomNumberFormat
   * @param money
   * @param style
   * @returns {string} value
   */
  convertCustomNumberFormat(money: number = 0, style: 'decimal' | 'percent' | 'currency' | undefined = 'decimal') {
    return customNumberFormat(money, style);
  }

  /**
   * ConvertTotalMoney
   */
  convertTotalMoney() {
    const total = this.proportionInfo?.nav ?? 0;
    if (total.toString().length > 12) {
      this.totalNav = this.convertCustomNumberFormat(+total.toString().slice(0, -9)) + ' tỷ';
    } else if (total.toString().length > 9) {
      this.totalNav = this.convertCustomNumberFormat(+total.toString().slice(0, -6)) + ' triệu';
    } else {
      this.totalNav = total.toString();
    }
  }
}
