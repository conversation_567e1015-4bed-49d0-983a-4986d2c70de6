<div class="proportion-asset-component-cls">
  <div class="box-canvas">
    <canvas [id]="id"></canvas>
    <div class="detail-canvas">
      <div class="typo-body-20 header-text">{{ 'MES-429' | translate }}</div>
      <div class="typo-body-21">{{ totalNav }}</div>
    </div>
  </div>
  <div class="note-chart-cls">
    <div class="box-container">
      <div class="box-note" *ngFor="let asset of dataNote; let i = index">
        <div class="color-box-note">
          <div class="color" [style.background]="asset.backgroundColor"></div>
          <div class="text-note typo-body-15">{{ asset.label }}</div>
        </div>

        <!-- <div class="value-cls typo-body-7">{{asset.price}}</div>

        <div class="option-cls typo-body-7">
          <img src="./assets/icons/percentage-blue.svg" alt="percentage-blue" />
          {{ 'MES-98' | translate }}
        </div> -->
      </div>
    </div>

    <div class="box-container">
      <div class="box-note" *ngFor="let asset of dataNote; let i = index">
        <div class="value-cls typo-body-12">{{ asset.price }}</div>
      </div>
    </div>

    <div class="box-container">
      <div class="box-note" *ngFor="let asset of dataNote; let i = index">
        <div class="option-cls typo-body-12">
          <img src="./assets/icons/percentage-blue.svg" alt="percentage-blue" />
          {{ 'MES-98' | translate }}
        </div>
      </div>
    </div>
  </div>
</div>

<div class="proportion-asset-table">
  <sha-grid [data]="data" [columnConfigs]="columnConfigs" class="table-custom-cls"></sha-grid>

  <!-- Tỷ trọng phân bổ -->
  <ng-template #proportionInfoRef let-proportionInfo="templateInfo" let-element="element">
    @if(proportionInfo){
    <div>
      <ng-container>
        <div class="proportion-container">
          <div class="label typo-body-12">
            <span class="percent-asset">
              <span class="percentage positive">{{ proportionInfo.percent | numberFormat : 'percent' }}</span>
              <span class="type-asset"> {{ RECOMMEND_ALLOCATION_PROPROTION[proportionInfo.type] }} </span>
            </span>
            <span class="typo-body-12">&nbsp;≈ {{ proportionInfo.cash | numberFormat }}</span>
          </div>
        </div>
      </ng-container>
    </div>
    }@else {
    <div>-</div>
    }
  </ng-template>
</div>
