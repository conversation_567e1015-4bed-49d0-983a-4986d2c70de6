.proportion-asset-component-cls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 30px;
  .box-canvas {
    position: relative;
    .detail-canvas {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .header-txt {
        color: var(--color--text-subdued);
      }
    }
  }

  .note-chart-cls {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
    .box-container {
      display: flex;
      flex-direction: column;
      gap: 14px;
    }
    .box-note {
      display: flex;
      align-items: center;
      gap: 14px;
      .value-cls {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        flex: 1;
      }
      .color-box-note {
        display: flex;
        align-items: center;
        gap: 4px;
        // min-width: 120px;
        .color {
          height: 16px;
          width: 16px;
          border-radius: 50%;
          background: red;
        }
      }
      .option-cls {
        display: flex;
        gap: 6px;
        align-items: center;
        color: var(--color--text-subdued);
        img {
          cursor: pointer;
        }
      }
    }
  }
}

.proportion-asset-table {
  height: 100%;
  border: 1px solid var(--color--other--divider);
  .table-custom-cls {
    ::ng-deep {
      .content-center {
        .box-show,
        .percent-asset {
          height: 24px !important;

          input {
            height: 100%;
          }
        }
      }

      .cash {
        max-width: 156px;
        margin: 0 auto;

        span {
          border-radius: 16px;
          background-color: var(--color--cyan--200);

          input {
            padding: 2px 8px;
            text-align: center !important;
          }
        }
      }

      .stock {
        max-width: 156px;
        margin: 0 auto;

        span {
          border-radius: 16px;
          background-color: var(--color--success--300);

          input {
            padding: 2px 8px;
            text-align: center !important;
          }
        }
      }
      .bonds {
        max-width: 156px;
        margin: 0 auto;

        span {
          border-radius: 16px;
          background-color: var(--color--danger--300);

          input {
            padding: 2px 8px;
            text-align: center !important;
          }
        }
      }
      .derivative {
        max-width: 156px;
        margin: 0 auto;

        span {
          border-radius: 16px;
          background-color: var(--color--warning--300);

          input {
            padding: 2px 8px;
            text-align: center !important;
          }
        }
      }
      .fund-certificates {
        max-width: 156px;
        margin: 0 auto;

        span {
          border-radius: 16px;
          background-color: var(--color--brand--200);

          input {
            padding: 2px 8px;
            text-align: center !important;
          }
        }
      }
    }
  }

  .label {
    max-width: 316px;
    .percent-asset {
      background-color: var(--color--accents--yellow-dark);
      padding: 3px 8px;
      border-radius: 16px;
      min-width: 144px;
      display: inline-block;
      text-align: center;
    }
  }
}
