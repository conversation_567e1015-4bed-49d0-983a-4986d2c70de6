<div class="loan-info-component-cls">
  <div class="box-element">
    <div class="box-container">
      <!-- Số món vay -->
      <div class="typo-body-9 text-header">{{ 'MES-455' | translate }}</div>
      <div class="typo-body-9">{{ loanInfo?.children?.length ?? 0 }} món</div>
    </div>

    <!--Tổng dư nợ còn lại -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-317' | translate }}</div>
      <div class="typo-body-9">
        {{ loanInfo?.remainDebt ? customNumberFormat(loanInfo?.remainDebt) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tổng dư nợ đã trả -->
      <div class="typo-body-9 text-header">{{ 'MES-319' | translate }}</div>
      <div class="typo-body-9">
        {{ loanInfo?.paidDebt ? customNumberFormat(loanInfo?.paidDebt) + ' VND' : '-' }}
      </div>
    </div>

    <!--Tổng dư nợ ban đầu -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-318' | translate }}</div>
      <div class="typo-body-9">
        {{ loanInfo?.openingDebt ? customNumberFormat(loanInfo?.openingDebt) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tổng lãi vay tạm tính -->
      <div class="typo-body-9 text-header">{{ 'MES-456' | translate }}</div>
      <div class="typo-body-9">
        {{ loanInfo?.tempInterest ? customNumberFormat(loanInfo?.tempInterest) + ' VND' : '-' }}
      </div>
    </div>

    <!--Tổng phí vay tạm tính -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-457' | translate }}</div>
      <div class="typo-body-9">
        {{ loanInfo?.tempLoanFee ? customNumberFormat(loanInfo?.tempLoanFee) + ' VND' : '-' }}
      </div>
    </div>
  </div>
</div>

<div class="loan-table-container">
  <sha-grid [data]="data" [columnConfigs]="columnConfigs" class="table-custom-cls" [showIndexColumn]="false"></sha-grid>
</div>
