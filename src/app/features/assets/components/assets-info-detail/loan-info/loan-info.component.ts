import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { IColumnConfig } from '@shared/models';
import {
  CONVERT_STATUS_LOAN_ASSET_TO_CLASS,
  CONVERT_STATUS_LOAN_ASSET_TO_LABEL,
  EStatusLoanAsset,
} from '../../../constant/assets';
import { ILoanInfo } from '../../../models/asset';
import { customNumberFormat } from 'src/app/shared/utils/currency';

/**
 * LoanDetailInfoComponent
 */
@Component({
  selector: 'app-loan-detail-info-component',
  templateUrl: './loan-info.component.html',
  styleUrl: './loan-info.component.scss',
})
export class LoanDetailInfoComponent implements OnInit, OnChanges {
  data: any = [];

  @Input() loanInfo!: ILoanInfo | undefined;

  columnConfigs: IColumnConfig[] = [];

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.columnConfigs = [
      {
        name: '# món vay',
        minWidth: 80,
        width: 80,
        tag: 'debtId',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
      },
      {
        name: 'Ngày đáo hạn',
        minWidth: 30,
        width: 110,
        tag: 'dueDate',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Tình trạng khoản vay',
        minWidth: 30,
        width: 156,
        tag: 'status',
        isDisplay: true,
        align: 'center',
        resizable: true,
        dynamicClass: (v) => {
          if (v === EStatusLoanAsset.VALID) {
            return `status-box ${CONVERT_STATUS_LOAN_ASSET_TO_CLASS[v]}`;
          } else return `status-box ${CONVERT_STATUS_LOAN_ASSET_TO_CLASS[v]}`;
        },
        displayValueFn: (v) => CONVERT_STATUS_LOAN_ASSET_TO_LABEL[v],
      },
      {
        name: 'Dự nợ còn lại',
        minWidth: 30,
        width: 130,
        tag: 'remainDebt',
        isDisplay: true,
        resizable: true,
        align: 'end',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(Math.round(v)) : '-';
        },
      },
      {
        name: 'Dư nợ đã trả',
        minWidth: 30,
        width: 140,
        tag: 'paidDebt',
        isDisplay: true,
        resizable: true,
        align: 'end',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(Math.round(v)) : '-';
        },
      },
      {
        name: 'Dư nợ ban đầu',
        minWidth: 30,
        width: 140,
        tag: 'openingDebt',
        isDisplay: true,
        resizable: true,
        align: 'end',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(Math.round(v)) : '-';
        },
      },
      {
        name: 'Lãi vay tạm tính',
        minWidth: 30,
        width: 140,
        tag: 'tempInterest',
        isDisplay: true,
        resizable: true,
        align: 'end',
        panelClass: 'color-greeen-cls',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(Math.round(v)) : '-';
        },
      },
      {
        name: 'Phí vay tạm tính',
        minWidth: 30,
        width: 140,
        tag: 'tempLoanFee',
        isDisplay: true,
        resizable: true,
        align: 'end',
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(Math.round(v)) : '-';
        },
      },
    ];
  }

  /**
   * NgOnChanges
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    const { loanInfo } = changes;
    if (loanInfo) {
      this.data = [...(this.loanInfo?.children ?? [])];
    }
  }

  /**
   * CustomNumberFormat
   * @param money
   * @param style
   * @returns {string} value
   */
  customNumberFormat(money: number = 0, style: 'decimal' | 'percent' | 'currency' | undefined = 'decimal') {
    return customNumberFormat(money, style);
  }
}
