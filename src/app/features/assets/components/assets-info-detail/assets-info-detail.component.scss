.asset-info-detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  .asset-info-header {
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--color--other--divider);
    .left-box {
      display: flex;
      align-items: flex-start;
      gap: 24px;
      .text-box {
        display: flex;
        flex-direction: column;
        gap: 8px;
        .code-text {
          color: var(--color--text--subdued);
        }
      }
      .status-box {
        padding: 2px 24px;
        text-transform: uppercase;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 16px;
        background-color: var(--color--accents--mint);
        height: 24px;
      }
    }
    .right-box {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;
      .dropdown-box {
        padding: 10px 12px;
        border: 1px solid var(--color--other--divider);
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
      }
      img[alt='x-cross'] {
        cursor: pointer;
      }
    }
  }
  .asset-info-body {
    flex: 1;
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
    .asset-nav-bar {
      display: flex;
      flex-direction: column;
      padding: 16px 8px;
      border-right: 1px solid var(--color--other--divider);
      min-width: 260px;
      gap: 12px;
      .box-nav-bar {
        padding: 8px 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        width: 100%;
        border-radius: 6px;
        color: var(--color--text--subdued);
        &.isSeleted {
          background-color: var(--color--background--selected);
          color: var(--color--brand--500);

          mat-icon {
            ::ng-deep {
              path {
                stroke: var(--color--brand--500);
              }
            }

            &.convert-card {
              ::ng-deep {
                path {
                  &:nth-child(5) {
                    fill: var(--color--brand--500);
                  }
                }

                &:nth-child(6) {
                  fill: var(--color--brand--500);
                }
              }
            }

            &.money {
              ::ng-deep {
                path {
                  &:nth-child(3) {
                    fill: var(--color--brand--500);
                  }
                }
              }
            }

            &.debt {
              ::ng-deep {
                path {
                  fill: var(--color--brand--500);
                }
              }
            }
            &.wallet-remove {
              ::ng-deep {
                path {
                  &:nth-child(3),
                  &:nth-child(4),
                  &:nth-child(5),
                  &:nth-child(6) {
                    fill: var(--color--brand--500);
                  }
                }
              }
            }
            &.investment-portfolio {
              ::ng-deep {
                path {
                  &:nth-child(5),
                  &:nth-child(4) {
                    fill: var(--color--brand--500);
                  }
                }
              }
            }
            &.percentage {
              ::ng-deep {
                path {
                  &:nth-child(4) {
                    fill: var(--color--brand--500);
                  }
                }
              }
            }
          }
        }
        gap: 12px;
        mat-icon {
          ::ng-deep {
            path {
              stroke: #808080;
            }
          }
          &.acc-status {
            ::ng-deep {
              path {
                fill: #808080;
                stroke: unset;
              }
            }
          }
          &.convert-card {
            ::ng-deep {
              path {
                &:nth-child(5) {
                  fill: #808080;
                  stroke: unset;
                }

                &:nth-child(6) {
                  fill: #808080;
                  stroke: unset;
                }
              }
            }
          }
          &.money {
            ::ng-deep {
              path {
                &:nth-child(3) {
                  fill: #808080;
                  stroke: unset;
                }
              }
            }
          }
          &.debt {
            ::ng-deep {
              path {
                stroke: unset;
                fill: #808080;
              }
            }
          }
          &.wallet-remove {
            ::ng-deep {
              path {
                &:nth-child(3),
                &:nth-child(4),
                &:nth-child(5),
                &:nth-child(6) {
                  stroke: unset;
                  fill: #808080;
                }
              }
            }
          }
          &.investment-portfolio {
            ::ng-deep {
              path {
                &:nth-child(5),
                &:nth-child(4) {
                  fill: #808080;
                  stroke: unset;
                }
              }
            }
          }
          &.percentage {
            ::ng-deep {
              path {
                &:nth-child(4) {
                  fill: #808080;
                  stroke: unset;
                }
              }
            }
          }
        }
      }
    }
    .asset-content {
      flex: 1;
      padding: 16px;
      overflow: auto;
      .box-content {
        margin-bottom: 16px;
      }
      .btn-box-cls {
        display: flex;
        align-items: center;
        gap: 16px;
        .action-box {
          display: flex;
          align-items: center;
          gap: 4px;
          color: var(--color--brand--500);
          cursor: pointer;
        }
      }
    }
  }
}

:host {
  height: 100%;
  width: 100%;
}
