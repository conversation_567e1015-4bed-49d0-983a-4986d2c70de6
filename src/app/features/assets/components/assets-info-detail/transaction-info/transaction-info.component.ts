import { Component, Input } from '@angular/core';
import { ITransactionInfo } from '../../../models/asset';
import { customNumberFormat } from 'src/app/shared/utils/currency';

/**
 * TransactionInfoDetailComponent
 */
@Component({
  selector: 'app-transaction-info-component',
  templateUrl: './transaction-info.component.html',
  styleUrl: './transaction-info.component.scss',
})
export class TransactionInfoDetailComponent {
  @Input() transactionInfo!: ITransactionInfo | undefined;

  /**
   * CustomNumberFormat
   * @param money
   * @param style
   * @returns {string} value
   */
  customNumberFormat(money: number = 0, style: 'decimal' | 'percent' | 'currency' | undefined = 'decimal') {
    return customNumberFormat(money, style);
  }
}
