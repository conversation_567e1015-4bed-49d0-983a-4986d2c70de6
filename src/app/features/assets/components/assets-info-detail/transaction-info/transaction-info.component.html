<div class="transaction-info-component-cls">
  <div class="box-element">
    <div class="box-container">
      <!-- Tổng GTGD -->
      <div class="typo-body-9 text-header">{{ 'MES-441' | translate }}</div>
      <div class="typo-body-9">
        {{
          transactionInfo?.totalTransactionValue
            ? customNumberFormat(transactionInfo?.totalTransactionValue) + ' VND'
            : '-'
        }}
      </div>
    </div>
    <!-- GTGD (mua) -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-442' | translate }}</div>
      <div class="typo-body-9 text-color-green">
        <!-- <img src="./assets/icons/up.svg" alt="up-icon" /> -->
        {{
          transactionInfo?.buyTransactionValue
            ? customNumberFormat(transactionInfo?.buyTransactionValue) + ' VND '
            : '-'
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tổng doanh thu phí -->
      <div class="typo-body-9 text-header">{{ 'MES-581' | translate }}</div>
      <div class="typo-body-9">
        {{ transactionInfo?.revenueFee ? customNumberFormat(transactionInfo?.revenueFee) + ' VND' : '-' }}
      </div>
    </div>
    <!-- GTGD (bán) -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-443' | translate }}</div>
      <div class="typo-body-9 text-color-green danger-color">
        <!-- <img src="./assets/icons/up.svg" alt="up-icon" /> -->
        {{
          transactionInfo?.sellTransactionValue
            ? customNumberFormat(transactionInfo?.sellTransactionValue) + ' VND '
            : '-'
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Phí trả sở -->
      <div class="typo-body-9 text-header">{{ 'MES-288' | translate }}</div>
      <div class="typo-body-9">
        {{ transactionInfo?.stockExchangeFee ? customNumberFormat(transactionInfo?.stockExchangeFee) + ' VND' : '-' }}
      </div>
    </div>
    <!-- Thuế TNCN -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-444' | translate }}</div>
      <div class="typo-body-9">
        {{ transactionInfo?.depositoryFee ? customNumberFormat(transactionInfo?.depositoryFee) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Thuế khac -->
      <div class="typo-body-9 text-header">{{ 'MES-693' | translate }}</div>
      <div class="typo-body-9">
        {{ transactionInfo?.otherFees ? customNumberFormat(transactionInfo?.otherFees) + ' VND' : '-' }}
      </div>
    </div>
    <!-- Phí SMS -->
    <!-- <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-290' | translate }}</div>
      <div class="typo-body-9">
        {{
          transactionInfo?.smsFee === 0
            ? '0 VND'
            : transactionInfo?.smsFee
            ? customNumberFormat(transactionInfo?.smsFee) + ' VND'
            : '-'
        }}
      </div>
    </div> -->
    <!-- Net phí giao dịch -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-195' | translate }}</div>
      <div class="typo-body-9">
        {{ transactionInfo?.netTransactionFee ? customNumberFormat(transactionInfo?.netTransactionFee) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <!-- <div class="box-element">
    <div class="box-container"> -->
  <!-- Net phí giao dịch -->
  <!-- <div class="typo-body-9 text-header">{{ 'MES-195' | translate }}</div>
      <div class="typo-body-9">
        {{
          transactionInfo?.netTransactionFee === 0
            ? '0 VND'
            : transactionInfo?.netTransactionFee
            ? customNumberFormat(transactionInfo?.netTransactionFee) + ' VND'
            : '-'
        }}
      </div>
    </div> -->
  <!-- Hoa hồng MG -->
  <!-- <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-287' | translate }}</div>
      <div class="typo-body-9">
        {{
          transactionInfo?.brokerCommission === 0
            ? '0 VND'
            : transactionInfo?.brokerCommission
            ? customNumberFormat(transactionInfo?.brokerCommission) + ' VND'
            : '-'
        }}
      </div>
    </div> -->
  <!-- </div> -->
</div>
