import { Component, Input } from '@angular/core';
import { IDespositInfo } from '../../../models/asset';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { MARGIN_STATUS_CLASS_MAP, MARGIN_STATUS_MAP } from '../../../constant/assets';

/**
 * DepositDetailVSDComponent
 */
@Component({
  selector: 'app-deposit-detail-vsd-component',
  templateUrl: './deposit-detail-vsd.component.html',
  styleUrl: './deposit-detail-vsd.component.scss',
})
export class DepositDetailVSDComponent {
  @Input() depositInfo!: IDespositInfo | undefined;

  @Input() accountNumber = '';

  MARGIN_STATUS_MAP = MARGIN_STATUS_MAP;

  MARGIN_STATUS_CLASS_MAP = MARGIN_STATUS_CLASS_MAP;
  /**
   * CustomNumberFormat
   * @param money
   * @param style
   * @returns {string} value
   */
  customNumberFormat(money: number = 0, style: 'decimal' | 'percent' | 'currency' | undefined = 'decimal') {
    return customNumberFormat(money, style);
  }
}
