<div class="deposit-detail-component-cls">
  <div class="box-element">
    <div class="box-container">
      <!-- Số tài khoản -->
      <div class="typo-body-9 text-header">{{ 'MES-66' | translate }}</div>
      <div class="typo-body-9">{{ accountNumber }}</div>
    </div>

    <!-- Tỷ lệ ký quỹ -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-282' | translate }}</div>
      <div class="typo-body-9 dropBox-cls">
        <div class="box-color" [ngClass]="{ 'non-value': !depositInfo?.marginRate }">
          {{ depositInfo?.marginRate ? customNumberFormat(depositInfo?.marginRate, 'percent') : '-' }}
        </div>
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tài sản ròng (NAV) -->
      <div class="typo-body-9 text-header">{{ 'MES-279' | translate }}</div>
      <div class="typo-body-9">{{ depositInfo?.nav ? customNumberFormat(depositInfo?.nav) + ' VND' : '-' }}</div>
    </div>

    <!-- Tình trạng ký quỹ -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-299' | translate }}</div>
      <div class="typo-body-9 dropBox-cls">
        <div
          class="box-color color-gray-cls"
          [ngClass]="depositInfo?.marginStatus ? MARGIN_STATUS_CLASS_MAP[depositInfo!.marginStatus] : 'non-value'"
        >
          {{ depositInfo?.marginStatus ? MARGIN_STATUS_MAP[depositInfo!.marginStatus] : '-' }}
        </div>
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tổng tiền -->
      <div class="typo-body-9 text-header">{{ 'MES-281' | translate }}</div>
      <div class="typo-body-9">{{ depositInfo?.cash ? customNumberFormat(depositInfo?.cash) + ' VND' : '-' }}</div>
    </div>

    <!-- Sức mua -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-203' | translate }}</div>
      <div class="typo-body-9">
        {{ depositInfo?.purchasingPower ? customNumberFormat(depositInfo?.purchasingPower) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tài sản 80 -->
      <div class="typo-body-9 text-header">{{ 'MES-296' | translate }} 80</div>
      <div class="typo-body-9">
        {{ depositInfo?.eightyAssets ? customNumberFormat(depositInfo?.eightyAssets) + ' VND' : '-' }}
      </div>
    </div>

    <!-- +/- Tài sản 80 -->
    <div class="box-container">
      <div class="typo-body-9 text-header">+/-{{ 'MES-296' | translate }} 80</div>
      <div
        class="typo-body-9 text-color"
        [class.text-color-green]="depositInfo?.marginEightyAssets?.numberMargin ?? 0 > 0"
        [ngClass]="{
          'text-color-green': 0 < (depositInfo?.marginEightyAssets?.numberMargin ?? 0),
          'danger-color': 0 > (depositInfo?.marginEightyAssets?.numberMargin ?? 0)
        }"
      >
        <img
          [src]="
            0 < (depositInfo?.marginEightyAssets?.numberMargin ?? 0)
              ? './assets/icons/up.svg'
              : 0 > (depositInfo?.marginEightyAssets?.numberMargin ?? 0)
              ? './assets/icons/down.svg'
              : './assets/icons/minus.svg'
          "
          alt="up-icon"
        />
        {{
          depositInfo?.marginEightyAssets && depositInfo?.marginEightyAssets?.numberMargin
            ? (0 < (depositInfo?.marginEightyAssets?.numberMargin ?? 0) ? '+' : '-') +
              customNumberFormat(depositInfo?.marginEightyAssets?.numberMargin ?? 0) +
              ((0 < (depositInfo?.marginEightyAssets?.numberMargin ?? 0) ? ' +' : ' ') +
                customNumberFormat(depositInfo?.marginEightyAssets?.percentMargin ?? 0, 'percent'))
            : depositInfo?.marginEightyAssets?.numberMargin ??
              0 + ' - ' + customNumberFormat(depositInfo?.marginEightyAssets?.percentMargin ?? 0, 'percent')
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Giá trị đầu tư 80  -->
      <div class="typo-body-9 text-header">{{ 'MES-297' | translate }} 80</div>
      <div class="typo-body-9">
        {{ depositInfo?.eightyInvest ? customNumberFormat(depositInfo?.eightyInvest) + ' VND' : '-' }}
      </div>
    </div>

    <!--+/- GTĐT 80 chưa đóng -->
    <div class="box-container">
      <div class="typo-body-9 text-header">+/- GTĐT 80 chưa đóng</div>
      <div
        class="typo-body-9 text-color"
        [class.text-color-green]="depositInfo?.marginUnclosedEightyInvest?.numberMargin ?? 0 > 0"
        [ngClass]="{
          'text-color-green': 0 < (depositInfo?.marginUnclosedEightyInvest?.numberMargin ?? 0),
          'danger-color': 0 > (depositInfo?.marginUnclosedEightyInvest?.numberMargin ?? 0)
        }"
      >
        <img
          [src]="
            0 < (depositInfo?.marginUnclosedEightyInvest?.numberMargin ?? 0)
              ? './assets/icons/up.svg'
              : 0 > (depositInfo?.marginUnclosedEightyInvest?.numberMargin ?? 0)
              ? './assets/icons/down.svg'
              : './assets/icons/minus.svg'
          "
          alt="up-icon"
        />
        {{
          depositInfo?.marginUnclosedEightyInvest && depositInfo?.marginUnclosedEightyInvest?.numberMargin
            ? (0 < (depositInfo?.marginUnclosedEightyInvest?.numberMargin ?? 0) ? '+' : '') +
              customNumberFormat(depositInfo?.marginUnclosedEightyInvest?.numberMargin ?? 0) +
              ((0 < (depositInfo?.marginUnclosedEightyInvest?.numberMargin ?? 0) ? ' +' : ' ') +
                customNumberFormat(depositInfo?.marginUnclosedEightyInvest?.percentMargin ?? 0, 'percent'))
            : depositInfo?.marginUnclosedEightyInvest?.numberMargin ??
              0 + ' - ' + customNumberFormat(depositInfo?.marginUnclosedEightyInvest?.percentMargin ?? 0, 'percent')
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Số dư chưa nộp ký quỹ -->
      <div class="typo-body-9 text-header">{{ 'MES-298' | translate }} 80</div>
      <div class="typo-body-9">
        {{ depositInfo?.notSubmittedMargin ? customNumberFormat(depositInfo?.notSubmittedMargin) + ' VND' : '-' }}
      </div>
    </div>

    <!-- +/- GTĐT 80 đã đóng -->
    <div class="box-container">
      <div class="typo-body-9 text-header">+/- GTĐT 80 đã đóng</div>
      <div
        class="typo-body-9 text-color"
        [class.text-color-green]="depositInfo?.marginClosedEightyInvest?.numberMargin ?? 0 > 0"
        [ngClass]="{
          'text-color-green': 0 < (depositInfo?.marginClosedEightyInvest?.numberMargin ?? 0),
          'danger-color': 0 > (depositInfo?.marginClosedEightyInvest?.numberMargin ?? 0)
        }"
      >
        <img
          [src]="
            0 < (depositInfo?.marginClosedEightyInvest?.numberMargin ?? 0)
              ? './assets/icons/up.svg'
              : 0 > (depositInfo?.marginClosedEightyInvest?.numberMargin ?? 0)
              ? './assets/icons/down.svg'
              : './assets/icons/minus.svg'
          "
          alt="up-icon"
        />
        {{
          depositInfo?.marginClosedEightyInvest && depositInfo?.marginClosedEightyInvest?.numberMargin
            ? (0 < (depositInfo?.marginClosedEightyInvest?.numberMargin ?? 0) ? '+' : '') +
              customNumberFormat(depositInfo?.marginClosedEightyInvest?.numberMargin ?? 0) +
              ((0 < (depositInfo?.marginClosedEightyInvest?.numberMargin ?? 0) ? '+' : ' ') +
                customNumberFormat(depositInfo?.marginClosedEightyInvest?.percentMargin ?? 0, 'percent'))
            : depositInfo?.marginClosedEightyInvest?.numberMargin ??
              0 + ' - ' + customNumberFormat(depositInfo?.marginClosedEightyInvest?.percentMargin ?? 0, 'percent')
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tiền ký quỹ VSDC -->
      <div class="typo-body-9 text-header">{{ 'MES-446' | translate }}</div>
      <div class="typo-body-9">
        {{ depositInfo?.vsdcMargin ? customNumberFormat(depositInfo?.vsdcMargin) + ' VND' : '-' }}
      </div>
    </div>

    <!-- Tiền ký quỹ có thể rút -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-447' | translate }}</div>
      <div class="typo-body-9">
        {{ depositInfo?.canWithdrawMargin ? customNumberFormat(depositInfo?.canWithdrawMargin) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tiền ký quỹ phải nộp -->
      <div class="typo-body-9 text-header">{{ 'MES-448' | translate }}</div>
      <div class="typo-body-9">
        {{ depositInfo?.toSubmittedMargin ? customNumberFormat(depositInfo?.toSubmittedMargin) + ' VND' : '-' }}
      </div>
    </div>
  </div>
</div>
