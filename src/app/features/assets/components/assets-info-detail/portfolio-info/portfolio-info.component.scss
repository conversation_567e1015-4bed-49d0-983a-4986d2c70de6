.portfolio-info-component-cls {
  .box-element {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 14px;
  }
  .box-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8px;
    .text-header {
      color: var(--color--text--subdued);
    }

    .text-color {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--color--warning--500);
      &.text-color-green {
        color: var(--color--accents--green);
      }
      &.danger-color {
        color: var(--color--accents--red);
      }
    }

    .dropBox-cls {
      display: flex;
      align-items: center;
      gap: 4px;
      &.gap-10-px {
        gap: 10px;
      }
      img[alt='add-circle'] {
        cursor: pointer;
      }
      .box-color {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2px 24px;
        border-radius: 16px;
        background-color: var(--color--accents--yellow-dark);
        height: 24px;
      }
    }
  }
}

.portfolio-table-container {
  height: 100%;
  border: 1px solid var(--color--other--divider);
  .table-custom-cls {
    ::ng-deep {
      .table-container {
        max-height: 300px;
        min-height: 260px;
        background-color: var(--color--neutral--white);

        &:has(.empty-box) {
          overflow: hidden;
          & > img {
            height: 190px;
          }
        }
      }

      .empty-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .percent-cls {
        max-width: 120px;
        margin: 0 auto;

        span {
          background-color: var(--color--accents--yellow-dark);
          border-radius: 16px;
          white-space: nowrap;
          text-wrap: nowrap;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          max-width: 184px;

          input {
            text-align: center;
          }
        }
      }

      .percent-100-cls {
        max-width: 120px;
        margin: 0 auto;

        span {
          background-color: var(--color--accents--green);
          border-radius: 16px;
          white-space: nowrap;
          text-wrap: nowrap;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          max-width: 184px;

          input {
            text-align: center;
          }
        }
      }

      &.invest-increase {
        max-width: 184px;
        margin: 0 auto;

        span {
          background-color: var(--color--success--200);
          border-radius: 16px;
          white-space: nowrap;
          text-wrap: nowrap;
          display: inline-flex;
          align-items: center;
          justify-content: center;

          input {
            text-align: center;
            color: var(--color--success--500);
          }
        }
      }

      &.invest-reduce {
        max-width: 184px;
        margin: 0 auto;

        span {
          background-color: var(--color--danger--300);
          border-radius: 16px;
          white-space: nowrap;
          text-wrap: nowrap;
          display: inline-flex;
          align-items: center;
          justify-content: center;

          input {
            text-align: center;
            color: var(--color--danger--600);
          }
        }
      }
    }
  }

  .label {
    max-width: 316px;
    .percent-asset {
      background-color: var(--color--accents--yellow-dark);
      padding: 3px 8px;
      border-radius: 16px;
      min-width: 144px;
      display: inline-block;
      text-align: center;
    }
  }

  .price-increase,
  .price-reduce,
  .price-stable {
    display: flex;
    align-items: center;
    gap: 4px;

    &.price-increase {
      span {
        // color: var(--color--accents--green);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    &.price-reduce {
      span {
        // color: var(--color--accents--red);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    &.price-stable {
      span {
        // color: var(--color--warning--500);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }

  // ngTemplate
  .asset-info-increase,
  .asset-info-reduce,
  .asset-info-stable {
    display: flex;
    align-items: center;
    gap: 4px;

    &.asset-info-increase {
      span {
        color: var(--color--accents--green);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    &.asset-info-reduce {
      span {
        color: var(--color--accents--red);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    &.asset-info-stable {
      span {
        color: var(--color--warning--500);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }

  .flex-1-cls {
    flex: 1;
    span {
      width: 100%;
    }
  }
}
