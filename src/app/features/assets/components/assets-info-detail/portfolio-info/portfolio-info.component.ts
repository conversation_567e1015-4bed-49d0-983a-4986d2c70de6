import { Component, Input, OnChanges, OnInit, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { IColumnConfig } from '@shared/models';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { IInvestmentInfo } from '../../../models/asset';
import { DestroyService } from 'src/app/core/services';
import { Store } from '@ngrx/store';

/**
 * PortfolioInfoDetailComponent
 */
@Component({
  selector: 'app-portfolio-info-component',
  templateUrl: './portfolio-info.component.html',
  styleUrl: './portfolio-info.component.scss',
})
export class PortfolioInfoDetailComponent implements OnInit, OnChanges {
  @ViewChild('price', { static: true }) price: TemplateRef<any> | null = null;
  @ViewChild('templateInfo', { static: true }) templateInfo: TemplateRef<any> | null = null;
  @ViewChild('percentInvestTemplate', { static: true }) percentInvestTemplate: TemplateRef<any> | null = null;

  @Input() portfolioInfo!: IInvestmentInfo | undefined;

  columnConfigs: IColumnConfig[] = [];

  data: any = [];

  constructor(private readonly store: Store, private readonly _destroy: DestroyService) {}

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.columnConfigs = [
      {
        name: 'Mã CK',
        minWidth: 120,
        width: 120,
        tag: 'stockCode',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Giá trị hiện tại',
        minWidth: 30,
        width: 120,
        tag: 'price',
        isDisplay: true,
        resizable: true,
        align: 'end',
        cellTemplate: this.price,
      },
      {
        name: 'Vốn đầu tư',
        minWidth: 30,
        width: 120,
        tag: 'capitalInvestment',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'Lãi / lỗ',
        minWidth: 30,
        width: 140,
        tag: 'invest',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.templateInfo,
      },
      {
        name: '% Lãi / lỗ',
        minWidth: 30,
        width: 100,
        tag: 'percentInvest',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.percentInvestTemplate,
      },
      {
        name: 'KL được GD',
        minWidth: 30,
        width: 100,
        tag: 'tradableVolume',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          if (!v) return '0';
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'Tổng KL',
        minWidth: 30,
        width: 100,
        tag: 'totalVolume',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          if (!v) return '0';
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'KL mua T0',
        minWidth: 30,
        width: 100,
        tag: 't0Volume',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          if (!v) return '0';
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'KL mua T1',
        minWidth: 30,
        width: 100,
        tag: 't1Volume',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          if (!v) return '0';
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'KL mua T2',
        minWidth: 30,
        width: 100,
        tag: 't2Volume',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          if (!v) return '0';
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'Giá vốn',
        minWidth: 30,
        width: 140,
        tag: 'costPrice',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'Giá hiện tại',
        minWidth: 30,
        width: 100,
        tag: 'currentPrice',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          let num = Number(v);
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'Tỷ trọng danh mục',
        minWidth: 30,
        width: 156,
        tag: 'portfolioProportion',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return customNumberFormat(v, 'percent');
        },
        dynamicClass: (value) => {
          if (value < 100) {
            return 'percent-cls';
          } else return 'percent-100-cls';
        },
        align: 'center',
      },
      // {
      //   name: 'Cơ cấu vốn',
      //   minWidth: 30,
      //   width: 156,
      //   tag: 'capitalStructure',
      //   isDisplay: true,
      //   resizable: true,
      //   displayValueFn: (v: number) => {
      //     return customNumberFormat(v, 'percent');
      //   },
      //   dynamicClass: (value) => {
      //     if (value < 100) {
      //       return 'percent-cls';
      //     } else return 'percent-100-cls';
      //   },
      //   align: 'center',
      // },
    ];
  }

  /**
   * NgOnChanges
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    const { portfolioInfo } = changes;
    if (portfolioInfo) {
      this.data = [...(this.portfolioInfo?.children ?? [])];
    }
  }
  /**
   * CustomNumberFormat
   * @param money
   * @param style
   * @returns {string} value
   */
  customNumberFormat(money: number = 0, style: 'decimal' | 'percent' | 'currency' | undefined = 'decimal') {
    return customNumberFormat(money, style);
  }
}
