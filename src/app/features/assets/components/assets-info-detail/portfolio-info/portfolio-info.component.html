<div class="portfolio-info-component-cls">
  <div class="box-element">
    <div class="box-container">
      <!-- Tổng mã CK đang nắm giữ -->
      <div class="typo-body-9 text-header">{{ 'MES-461' | translate }}</div>
      <div class="typo-body-9">{{ portfolioInfo?.children?.length ?? 0 }} món</div>
    </div>

    <!--Tổng vốn đầu tư -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-462' | translate }}</div>
      <div class="typo-body-9">
        {{ portfolioInfo?.capitalInvestment ? customNumberFormat(portfolioInfo?.capitalInvestment) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tổng giá hiện tại -->
      <div class="typo-body-9 text-header">{{ 'MES-463' | translate }}</div>
      <div class="typo-body-9">
        {{ portfolioInfo?.price ? customNumberFormat(portfolioInfo?.price) + ' VND' : '-' }}
      </div>
    </div>
  </div>
</div>

<div class="portfolio-table-container">
  <sha-grid [data]="data" [columnConfigs]="columnConfigs" class="table-custom-cls"></sha-grid>

  <ng-template #price let-price="templateInfo" let-element="element">
    @if(price) {
    <div class="flex-1-cls">
      <!-- <ng-container *ngIf="price > element.openingPrice"> -->
      <!-- price-increase -->
      <div class="typo-body-12">
        <!-- <img src="./assets/icons/up.svg" alt="document-logo" /> -->
        <span class="typo-body-12">{{ customNumberFormat(price) }}</span>
      </div>
      <!-- </ng-container> -->
      <!-- <ng-container *ngIf="price < element.openingPrice">
        <div class="price-reduce typo-body-12"> -->
      <!-- <img src="./assets/icons/down.svg" alt="document-logo" /> -->
      <!-- <span class="typo-body-12">{{ customNumberFormat(price) }}</span>
        </div>
      </ng-container>
      <ng-container *ngIf="price === element.openingPrice">
        <div class="price-stable typo-body-12"> -->
      <!-- <img src="./assets/icons/minus.svg" alt="document-logo" /> -->
      <!-- <span class="typo-body-12"> {{ customNumberFormat(price) }}</span>
        </div>
      </ng-container> -->
    </div>
    } @else {
    <div>-</div>
    }
  </ng-template>

  <ng-template #templateInfo let-element="templateInfo">
    @if(element) {
    <div class="flex-1-cls">
      <ng-container *ngIf="element > 0">
        <div class="asset-info-increase typo-body-12">
          <img src="./assets/icons/up.svg" alt="document-logo" />
          <span class="typo-body-12">{{ element | numberFormat }}</span>
        </div>
      </ng-container>
      <ng-container *ngIf="element < 0">
        <div class="asset-info-reduce typo-body-12">
          <img src="./assets/icons/down.svg" alt="document-logo" />
          <span class="typo-body-12">{{ element | numberFormat }} </span>
        </div>
      </ng-container>
      <ng-container *ngIf="element === 0">
        <div class="asset-info-stable typo-body-12">
          <img src="./assets/icons/minus.svg" alt="document-logo" />
          <span class="typo-body-12">{{ element | numberFormat }} </span>
        </div>
      </ng-container>
    </div>
    } @else {
    <div>-</div>
    }
  </ng-template>

  <ng-template #percentInvestTemplate let-element="templateInfo">
    @if(element) {
    <div class="flex-1-cls">
      <ng-container *ngIf="element > 0">
        <div class="asset-info-increase typo-body-12">
          <!-- <img src="./assets/icons/up.svg" alt="document-logo" /> -->
          <span class="typo-body-12">+{{ element | numberFormat : 'percent' }}</span>
        </div>
      </ng-container>
      <ng-container *ngIf="element < 0">
        <div class="asset-info-reduce typo-body-12">
          <!-- <img src="./assets/icons/down.svg" alt="document-logo" /> -->
          <span class="typo-body-12">{{ element | numberFormat }} %</span>
        </div>
      </ng-container>
      <ng-container *ngIf="element === 0">
        <div class="asset-info-stable typo-body-12">
          <!-- <img src="./assets/icons/minus.svg" alt="document-logo" /> -->
          <span class="typo-body-12">{{ element | numberFormat }} %</span>
        </div>
      </ng-container>
    </div>
    } @else {
    <div>-</div>
    }
  </ng-template>
</div>
