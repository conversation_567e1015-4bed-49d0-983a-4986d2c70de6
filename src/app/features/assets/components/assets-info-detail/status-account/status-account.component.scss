.status-account-component-cls {
  .box-element {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 14px;
  }
  .box-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8px;
    .text-header {
      color: var(--color--text--subdued);
    }

    .box-status-cls {
      display: flex;
      .box-cls {
        padding: 2px 24px;
        border-radius: 16px;
        background-color: var(--color--neutral--100);
        height: 24px;
        display: flex;
        align-items: center;

        &.half {
          background-color: var(--color--accents--yellow-dark);
        }

        &.none {
          background-color: var(--color--neutral--100);
        }
        &.warning-1 {
          background-color: var(--color--warning--500);
        }
        &.warning-2 {
          background-color: var(--color--warning--600);
        }
        &.warning-3 {
          background-color: var(--color--warning--700);
        }
        &.additional-rate,
        &.debt {
          background-color: var(--color--danger--500);
        }
        &.mortgage-settlement-rate {
          background-color: var(--color--danger--600);
        }
      }
    }
  }
}
