<div class="status-account-component-cls">
  <div class="box-element">
    <div class="box-container">
      <!-- Tỷ lệ ký quỹ (CMR) -->
      <div class="typo-body-9 text-header">{{ 'MES-424' | translate }}</div>
      <div class="typo-body-9 box-status-cls">
        <div class="box-cls half">{{ customNumberFormat(statusAccount?.marginRate, 'percent') }}</div>
      </div>
    </div>
    <!-- Vi phạm tỷ lệ ký quỹ (CMR) -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-284' | translate }}</div>
      <div class="typo-body-9 box-status-cls">
        <div class="box-cls" [ngClass]="MARGIN_RATIO_VIOLATION_MAP_CLASS[statusAccount!.marginRateViolation.type]">
          {{ statusMarginRateViolation(statusAccount!.marginRateViolation) }}
        </div>
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Nợ quá hạn -->
      <div class="typo-body-9 text-header">{{ 'MES-285' | translate }}</div>
      <div class="typo-body-9 box-status-cls">
        <div class="box-cls" [ngClass]="OVERDUE_DEBT_MAP_CLASS[statusAccount!.overDueDebt]">
          {{ OVERDUE_DEBT_MAP[statusAccount!.overDueDebt] }}
        </div>
      </div>
    </div>
    <!-- Nợ phí -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-286' | translate }}</div>
      <div class="typo-body-9 box-status-cls">
        <div class="box-cls" [ngClass]="FEE_DEBT_MAP_CLASS[statusAccount!.feeDebt]">
          {{ FEE_DEBT_MAP[statusAccount!.feeDebt] }}
        </div>
      </div>
    </div>
  </div>
</div>
