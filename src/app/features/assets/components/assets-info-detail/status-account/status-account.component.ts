import { Component, Input } from '@angular/core';
import { IAccountStatus, IMarginRateViolation } from '../../../models/asset';
import {
  FEE_DEBT_MAP,
  FEE_DEBT_MAP_CLASS,
  MARGIN_RATIO_VIOLATION_MAP_CLASS,
  OVERDUE_DEBT_MAP,
  OVERDUE_DEBT_MAP_CLASS,
} from '../../../constant/assets';
import { customNumberFormat } from 'src/app/shared/utils/currency';

/**
 * StatusAccountComponent
 */
@Component({
  selector: 'app-status-account-component',
  templateUrl: './status-account.component.html',
  styleUrl: './status-account.component.scss',
})
export class StatusAccountComponent {
  @Input() statusAccount!: IAccountStatus | undefined;

  MARGIN_RATIO_VIOLATION_MAP_CLASS = MARGIN_RATIO_VIOLATION_MAP_CLASS;

  OVERDUE_DEBT_MAP = OVERDUE_DEBT_MAP;

  FEE_DEBT_MAP_CLASS = FEE_DEBT_MAP_CLASS;

  OVERDUE_DEBT_MAP_CLASS = OVERDUE_DEBT_MAP_CLASS;

  FEE_DEBT_MAP = FEE_DEBT_MAP;

  /**
   * CustomNumberFormat
   * @param money
   * @param style
   * @returns {string} value
   */
  customNumberFormat(money: number = 0, style: 'decimal' | 'percent' | 'currency' | undefined = 'decimal') {
    return customNumberFormat(money, style);
  }

  /**
   * StatusMarginRateViolation
   * @param data
   * @returns {string} labels
   */
  statusMarginRateViolation(data: IMarginRateViolation) {
    switch (data.type) {
      case 1:
        return 'Warning 1';
      case 2:
        return 'Warning 2';
      case 3:
        return 'Warning 3';
      case 4:
        return `Tỷ lệ bổ sung: ${data.percentMarginRateViolation}%`;
      case 5:
        return `Tỷ lệ giải chấp: ${data.percentMarginRateViolation}%`;
      default:
        return 'Không';
    }
  }
}
