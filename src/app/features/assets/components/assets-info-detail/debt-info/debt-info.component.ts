import { Component, Input } from '@angular/core';
import { IDebtInfo } from '../../../models/asset';
import { customNumberFormat } from 'src/app/shared/utils/currency';

/**
 * DebtDetailInfoComponent
 */
@Component({
  selector: 'app-debt-detail-info-component',
  templateUrl: './debt-info.component.html',
  styleUrl: './debt-info.component.scss',
})
export class DebtDetailInfoComponent {

  @Input() debtInfo !: IDebtInfo | undefined;


  /**
   * CustomNumberFormat
   * @param money
   * @param style
   * @returns {string} value
   */
  customNumberFormat(money: number = 0, style: 'decimal' | 'percent' | 'currency' | undefined = 'decimal') {
    return customNumberFormat(money, style);
  }
}
