<div class="debt-info-component-cls">
  <div class="box-element">
    <div class="box-container">
      <!-- Tỷ lệ ký quỹ -->
      <div class="typo-body-9 text-header">{{ 'MES-282' | translate }}</div>
      <div class="typo-body-9 dropBox-cls">
        <div class="box-color">{{ customNumberFormat(debtInfo?.marginRate, 'percent') }}</div>
      </div>
    </div>
    <!-- Tỷ lệ Nợ / DM Ký quỹ -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-451' | translate }}</div>
      <div class="typo-body-9 dropBox-cls">
        <div class="box-color">{{ customNumberFormat(debtInfo?.percentDebtPerMargin, 'percent') }}</div>
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tài sản đảm bảo -->
      <div class="typo-body-9 text-header">{{ 'MES-452' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.guaranteedAsset ? customNumberFormat(debtInfo?.guaranteedAsset) + ' VND' : '-' }}
      </div>
    </div>
    <!-- Tổng dư nợ -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-242' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.debt ? customNumberFormat(debtInfo?.debt) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Nợ vay Margin -->
      <div class="typo-body-9 text-header">{{ 'MES-320' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.marginLoanDebt ? customNumberFormat(debtInfo?.marginLoanDebt) + ' VND' : '-' }}
      </div>
    </div>
    <!-- +/- Tổng dư nợ -->
    <div class="box-container">
      <div class="typo-body-9 text-header">+/- {{ 'MES-242' | translate }}</div>
      <div
        class="typo-body-9 text-color"
        [class.text-color-green]="debtInfo?.marginDebt?.numberMargin ?? 0 > 0"
        [ngClass]="{
          'text-color-green': 0 < (debtInfo?.marginDebt?.numberMargin ?? 0),
          'danger-color': 0 > (debtInfo?.marginDebt?.numberMargin ?? 0)
        }"
      >
        <img
          [src]="
            0 < (debtInfo?.marginDebt?.numberMargin ?? 0)
              ? './assets/icons/up.svg'
              : 0 > (debtInfo?.marginDebt?.numberMargin ?? 0)
              ? './assets/icons/down.svg'
              : './assets/icons/minus.svg'
          "
          alt="up-icon"
        />
        {{
          debtInfo?.marginDebt && debtInfo?.marginDebt?.numberMargin
            ? (0 < (debtInfo?.marginDebt?.numberMargin ?? 0) ? '+' : '') +
              customNumberFormat(debtInfo?.marginDebt?.numberMargin ?? 0) +
              ((0 < (debtInfo?.marginDebt?.numberMargin ?? 0) ? ' +' : ' ') +
                customNumberFormat(debtInfo?.marginDebt?.percentMargin ?? 0, 'percent'))
            : debtInfo?.marginDebt?.numberMargin +
              ' - ' +
              customNumberFormat(debtInfo?.marginDebt?.percentMargin ?? 0, 'percent')
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Dư nợ Margin quá hạn -->
      <div class="typo-body-9 text-header">{{ 'MES-322' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.overdueMarginLoanDebt ? customNumberFormat(debtInfo?.overdueMarginLoanDebt) + ' VND' : '-' }}
      </div>
    </div>
    <!-- Lãi vay Margin -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-323' | translate }}</div>
      <div
        class="typo-body-9 text-color"
        [class.text-color-green]="debtInfo?.marginInterest ?? 0 > 0"
        [ngClass]="{
          'text-color-green': 0 < (debtInfo?.marginInterest ?? 0),
          'danger-color': 0 > (debtInfo?.marginInterest ?? 0),
          'default-color': !debtInfo?.marginInterest
        }"
      >
        {{
          debtInfo?.marginInterest
            ? (0 < (debtInfo?.marginInterest ?? 0) ? '+' : '') + customNumberFormat(debtInfo?.marginInterest ?? 0)
            : ' - '
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Nợ vay khác -->
      <div class="typo-body-9 text-header">{{ 'MES-324' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.otherLoanDebt ? customNumberFormat(debtInfo?.otherLoanDebt) + ' VND' : '-' }}
      </div>
    </div>
    <!--  Lãi vay khác -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-325' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.otherInterest ? customNumberFormat(debtInfo?.otherInterest) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Dư nợ khác quá hạn -->
      <div class="typo-body-9 text-header">{{ 'MES-326' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.overdueOtherLoanDebt ? customNumberFormat(debtInfo?.overdueOtherLoanDebt) + ' VND' : '-' }}
      </div>
    </div>
    <!--  Dư nợ ứng trước -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-327' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.advanceDebt ? customNumberFormat(debtInfo?.advanceDebt) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Phí lưu ký -->
      <div class="typo-body-9 text-header">{{ 'MES-289' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.depositoryFee ? customNumberFormat(debtInfo?.depositoryFee) + ' VND' : '-' }}
      </div>
    </div>

    <div class="box-container">
      <!-- Lãi vay ứng trước -->
      <div class="typo-body-9 text-header">{{ 'MES-333' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.advanceInterest ? customNumberFormat(debtInfo?.advanceInterest) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Phí CMS -->
      <div class="typo-body-9 text-header">{{ 'MES-290' | translate }}</div>
      <div class="typo-body-9">
        {{ debtInfo?.smsFee ? customNumberFormat(debtInfo?.smsFee) + ' VND' : '-' }}
      </div>
    </div>

    <div class="box-container">
      <!-- Tỷ lệ Nợ / TTS -->
      <div class="typo-body-9 text-header">{{ 'MES-450' | translate }}</div>
      <div class="typo-body-9 dropBox-cls">
        <div class="box-color">{{ customNumberFormat(debtInfo?.percentDebtPerNav, 'percent') }}</div>
      </div>
    </div>
  </div>
</div>
