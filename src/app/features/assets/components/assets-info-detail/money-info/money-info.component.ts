import { Component, Input } from '@angular/core';
import { IMoneyInfo } from '../../../models/asset';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { Store } from '@ngrx/store';
import { DestroyService } from 'src/app/core/services';

/**
 * MoneyInfoDetailComponent
 */
@Component({
  selector: 'app-money-info-detail-component',
  templateUrl: './money-info.component.html',
  styleUrl: './money-info.component.scss',
})
export class MoneyInfoDetailComponent {
  @Input() moneyInfo!: IMoneyInfo | undefined;

  constructor(private readonly store: Store, private readonly _destroy: DestroyService) {}

  /**
   * CustomNumberFormat
   * @param money
   * @param style
   * @returns {string} value
   */
  customNumberFormat(money: number = 0, style: 'decimal' | 'percent' | 'currency' | undefined = 'decimal') {
    return customNumberFormat(money, style);
  }
}
