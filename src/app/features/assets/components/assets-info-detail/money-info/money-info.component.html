<div class="money-info-component-cls">
  <div class="box-element">
    <div class="box-container">
      <!-- Tổng tiền -->
      <div class="typo-body-9 text-header">{{ 'MES-281' | translate }}</div>
      <div class="typo-body-9">{{ moneyInfo?.cash ? customNumberFormat(moneyInfo?.cash) + ' VND' : '-' }}</div>
    </div>
    <!-- +/- Tổng tiền -->
    <div class="box-container">
      <div class="typo-body-9 text-header">+/- {{ 'MES-281' | translate }}</div>
      <div
        class="typo-body-9 text-color"
        [class.text-color-green]="moneyInfo?.marginCash?.numberMargin ?? 0 > 0"
        [ngClass]="{
          'text-color-green': 0 < (moneyInfo?.marginCash?.numberMargin ?? 0),
          'danger-color': 0 > (moneyInfo?.marginCash?.numberMargin ?? 0)
        }"
      >
        <img
          [src]="
            0 < (moneyInfo?.marginCash?.numberMargin ?? 0)
              ? './assets/icons/up.svg'
              : 0 > (moneyInfo?.marginCash?.numberMargin ?? 0)
              ? './assets/icons/down.svg'
              : './assets/icons/minus.svg'
          "
          alt="up-icon"
        />
        {{
          moneyInfo?.marginCash && moneyInfo?.marginCash?.numberMargin
            ? (0 < (moneyInfo?.marginCash?.numberMargin ?? 0) ? '+' : '') +
              customNumberFormat(moneyInfo?.marginCash?.numberMargin ?? 0) +
              ((0 < (moneyInfo?.marginCash?.numberMargin ?? 0) ? ' +' : ' ') +
                customNumberFormat(moneyInfo?.marginCash?.percentMargin ?? 0, 'percent'))
            : moneyInfo?.marginCash?.numberMargin +
              ' - ' +
              customNumberFormat(moneyInfo?.marginCash?.percentMargin ?? 0, 'percent')
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Sức mua -->
      <!-- <div class="typo-body-9 text-header">{{ 'MES-203' | translate }}</div>
      <div class="typo-body-9">440.000.000 VND</div>
    </div> -->

      <!-- Số dư tiền mặt -->
      <div class="box-container">
        <div class="typo-body-9 text-header">{{ 'MES-291' | translate }}</div>
        <div class="typo-body-9">
          {{ moneyInfo?.cashBalance ? customNumberFormat(moneyInfo?.cashBalance) + ' VND' : '-' }}
        </div>
      </div>
    </div>
    <div class="box-container">
      <!-- Tiền bán chờ về khả ứng -->
      <div class="typo-body-9 text-header">{{ 'MES-613' | translate }}</div>
      <div class="typo-body-9">
        {{ moneyInfo?.awaitingAdvance ? customNumberFormat(moneyInfo?.unpaid) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <!-- <div class="box-element">
    <div class="box-container">
      Tiền bán chờ ứng
      <div class="typo-body-9 text-header">{{ 'MES-292' | translate }}</div>
      <div class="typo-body-9">
        {{ moneyInfo?.awaitingAdvance ? customNumberFormat(moneyInfo?.awaitingAdvance) + ' VND' : '-' }}
      </div>
    </div>

    Tiền bán T0
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-435' | translate }} T0</div>
      <div class="typo-body-9">{{ moneyInfo?.t0 ? customNumberFormat(moneyInfo?.t0) + ' VND' : '-' }}</div>
    </div>
  </div> -->

  <!-- <div class="box-element">
    <div class="box-container">
      Tiền bán T1
      <div class="typo-body-9 text-header">{{ 'MES-435' | translate }} T1</div>
      <div class="typo-body-9">{{ moneyInfo?.t1 ? customNumberFormat(moneyInfo?.t1) + ' VND' : '-' }}</div>
    </div>

    Tiền bán T2
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-435' | translate }} T2</div>
      <div class="typo-body-9">{{ moneyInfo?.t2 ? customNumberFormat(moneyInfo?.t2) + ' VND' : '-' }}</div>
    </div>
  </div> -->

  <div class="box-element">
    <div class="box-container">
      <!-- Tiền cổ tức chờ về -->
      <div class="typo-body-9 text-header">{{ 'MES-436' | translate }}</div>
      <div class="typo-body-9">
        {{ moneyInfo?.awaitingDividends ? customNumberFormat(moneyInfo?.awaitingDividends) + ' VND' : '-' }}
      </div>
    </div>

    <!-- Tiền mua chưa khớp -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-395' | translate }}</div>
      <div class="typo-body-9">
        {{ moneyInfo?.notMatched ? customNumberFormat(moneyInfo?.notMatched) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tiền mua chưa TT -->
      <div class="typo-body-9 text-header">{{ 'MES-396' | translate }}</div>
      <div class="typo-body-9">{{ moneyInfo?.unpaid ? customNumberFormat(moneyInfo?.unpaid) + ' VND' : '-' }}</div>
    </div>
    <div class="box-container">
      <!-- Tiền bị phong toả -->
      <div class="typo-body-9 text-header">{{ 'MES-614' | translate }}</div>
      <div class="typo-body-9">{{ moneyInfo?.blockedCash ? customNumberFormat(moneyInfo?.unpaid) + ' VND' : '-' }}</div>
    </div>
  </div>
</div>
