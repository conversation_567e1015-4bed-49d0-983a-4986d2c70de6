.purchasing-power-component-cls {
  .box-element {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 14px;
  }
  .box-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8px;
    .text-header {
      color: var(--color--text--subdued);
    }

    .text-color {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--color--warning--500);
      &.text-color-green {
        color: var(--color--accents--green);
      }
      &.danger-color {
        color: var(--color--accents--red);
      }
    }

    .dropBox-cls {
      display: flex;
      align-items: center;
      gap: 4px;
      &.gap-10-px {
        gap: 10px;
      }
      img[alt='add-circle'] {
        cursor: pointer;
      }
      .box-color {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2px 24px;
        border-radius: 16px;
        background-color: var(--color--accents--yellow-dark);
        height: 24px;
      }
    }
  }
}
