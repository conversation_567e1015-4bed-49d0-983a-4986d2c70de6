<div class="purchasing-power-component-cls">
  <div class="box-element">
    <div class="box-container">
      <!-- T<PERSON><PERSON> k<PERSON>n ròng (NAV) -->
      <div class="typo-body-9 text-header">{{ 'MES-279' | translate }}</div>
      <div class="typo-body-9">
        {{ purchasingPowerInfor?.nav ? customNumberFormat(purchasingPowerInfor?.nav) + ' VND' : '-' }}
      </div>
    </div>

    <!-- Tổng tiền -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-281' | translate }}</div>
      <div class="typo-body-9">
        {{ purchasingPowerInfor?.cash ? customNumberFormat(purchasingPowerInfor?.cash) + ' VND' : '-' }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Sức mua -->
      <div class="typo-body-9 text-header">{{ 'MES-203' | translate }}</div>
      <div class="typo-body-9">
        {{
          purchasingPowerInfor?.purchasingPower
            ? customNumberFormat(purchasingPowerInfor?.purchasingPower) + ' VND'
            : '-'
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <!-- Tiền bảo lãnh -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-294' | translate }}</div>
      <div class="typo-body-9 dropBox-cls">
        <!-- <img
          src="./assets/icons/add-circle.svg"
          alt="add-circle"
          [matTooltip]="'MES-75' | translate"
          matTooltipPosition="above"
          matTooltipClass="custom-tooltip"
          (click)="openPopoverGuaranteeMoney($event, purchasingPowerInfor)"
        /> -->
        <div class="box-color">
          {{ purchasingPowerInfor?.guarantee ? customNumberFormat(purchasingPowerInfor?.guarantee) : '0' }} VND
        </div>
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tiền bảo lãnh đã SD -->
      <div class="typo-body-9 text-header">{{ 'MES-437' | translate }}</div>
      <div class="typo-body-9">
        {{
          purchasingPowerInfor?.usedGuarantee ? customNumberFormat(purchasingPowerInfor?.usedGuarantee) + ' VND' : '-'
        }}
      </div>
    </div>

    <!-- Tiền bảo lãnh cần nộp -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-438' | translate }}</div>
      <div class="typo-body-9 dropBox-cls gap-10-px">
        <!-- <img
          [style.cursor]="'pointer'"
          src="./assets/icons/more-circle.svg"
          alt="more-circle"
          (click)="openPopoverSubmitGuarantee($event, purchasingPowerInfor?.toSubmitGuarantee ?? 0, accountNumber)"
        /> -->
        <div>
          {{
            purchasingPowerInfor?.toSubmitGuarantee
              ? customNumberFormat(purchasingPowerInfor?.toSubmitGuarantee) + ' VND'
              : '-'
          }}
        </div>
      </div>
    </div>
  </div>
</div>
