import { Component, Input } from '@angular/core';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { PopoverGuaranteeMoneyComponent } from '../../popover-guarantee-money/popover-guarantee-money.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { Store } from '@ngrx/store';
import { DestroyService } from 'src/app/core/services';
import { IGuaranteeInfo, IPurchasingPower } from '../../../models/asset';
import { SubmitGuaranteeOptionsComponent } from '../../submit-guarantee-options/submit-guarantee-options.component';

/**
 * PurchasingPowerDetailComponent
 */
@Component({
  selector: 'app-purchasing-power-detail-component',
  templateUrl: './purchasing-power.component.html',
  styleUrl: './purchasing-power.component.scss',
})
export class PurchasingPowerDetailComponent {
  @Input() purchasingPowerInfor!: IPurchasingPower | undefined;

  @Input() accountNumber = '';
  guaranteeData!: IGuaranteeInfo;

  /**
   * constructor
   * @param popoverService
   * @param store
   * @param _destroy
   */
  constructor(
    private readonly popoverService: PopoverService,
    private readonly store: Store,
    private readonly _destroy: DestroyService
  ) {}

  /**
   * openPopoverGuaranteeMoney
   * @param event Event
   * @param element
   */
  openPopoverGuaranteeMoney(event: MouseEvent, element: any) {
    const originElement = event.target as HTMLElement;

    this.popoverService.open({
      origin: originElement,
      content: PopoverGuaranteeMoneyComponent,
      width: 400,
      height: 383,
      position: 0,
      hasBackdrop: true,
      componentConfig: { element },
    });
  }

  /**
   * OpenPopup based on the event and submitGuarantee
   * provided.
   * @param {Event} event - Event
   * @param {submitGuarantee} submitGuarantee - number
   * @param {accountNumber} accountNumber - string
   */
  openPopoverSubmitGuarantee(event: Event, submitGuarantee: number, accountNumber: string) {
    const originElement = event.target as HTMLElement;
    this.popoverService.open({
      origin: originElement,
      content: SubmitGuaranteeOptionsComponent,
      width: 191,
      height: 96,
      position: 2,
      hasBackdrop: true,
      componentConfig: { submitGuarantee, accountNumber },
    });
  }

  /**
   * CustomNumberFormat
   * @param money
   * @param style
   * @returns {string} value
   */
  customNumberFormat(money: number = 0, style: 'decimal' | 'percent' | 'currency' | undefined = 'decimal') {
    return customNumberFormat(money, style);
  }
}
