import { Component, Input } from '@angular/core';
import { IAssetInfo } from '../../../models/asset';
import { customNumberFormat } from 'src/app/shared/utils/currency';

/**
 * AssetInfoComponent
 */
@Component({
  selector: 'app-asset-info-component',
  templateUrl: './asset-info.component.html',
  styleUrl: './asset-info.component.scss',
})
export class AssetInfoComponent {
  @Input() assetInfo!: IAssetInfo | undefined;

  /**
   * CustomNumberFormat
   * @param money
   * @param style
   * @returns {string} value
   */
  customNumberFormat(money: number = 0, style: 'decimal' | 'percent' | 'currency' | undefined = 'decimal') {
    return customNumberFormat(money, style);
  }
}
