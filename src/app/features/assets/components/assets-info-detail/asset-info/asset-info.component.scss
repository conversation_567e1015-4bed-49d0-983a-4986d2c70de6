.asset-info-component-cls {
  .box-element {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 14px;
  }
  .box-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8px;
    .text-header {
      color: var(--color--text--subdued);
    }

    .text-color {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--color--warning--500);
      &.text-color-green {
        color: var(--color--accents--green);
      }
      &.danger-color {
        color: var(--color--accents--red);
      }
    }
  }
}

.tag-value {
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 24px;
  width: fit-content;

  &.down {
    background-color: var(--color--accents--yellow-dark);
  }
  &.equal {
    background-color: var(--color--accents--green);
  }
}
