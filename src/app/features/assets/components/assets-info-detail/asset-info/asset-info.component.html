<div class="asset-info-component-cls">
  <div class="box-element">
    <div class="box-container">
      <!-- Tổng tài sản ròng (NAV) -->
      <div class="typo-body-9 text-header">{{ 'MES-423' | translate }}</div>
      <div class="typo-body-9">{{ assetInfo?.nav ? customNumberFormat(assetInfo?.nav) + ' VND' : '-' }}</div>
    </div>
    <div class="box-container">
      <div class="typo-body-9 text-header">+/- NAV</div>
      <div
        class="typo-body-9 text-color"
        [class.text-color-green]="assetInfo?.marginNav?.numberMargin ?? 0 > 0"
        [ngClass]="{
          'text-color-green': 0 < (assetInfo?.marginNav?.numberMargin ?? 0),
          'danger-color': 0 > (assetInfo?.marginNav?.numberMargin ?? 0)
        }"
      >
        <img
          [src]="
            0 < (assetInfo?.marginNav?.numberMargin ?? 0)
              ? './assets/icons/up.svg'
              : 0 > (assetInfo?.marginNav?.numberMargin ?? 0)
              ? './assets/icons/down.svg'
              : './assets/icons/minus.svg'
          "
          alt="up-icon"
        />
        {{
          assetInfo?.marginNav && assetInfo?.marginNav?.numberMargin
            ? (0 < (assetInfo?.marginNav?.numberMargin ?? 0) ? '+' : '') +
              customNumberFormat(assetInfo?.marginNav?.numberMargin ?? 0) +
              ((0 < (assetInfo?.marginNav?.numberMargin ?? 0) ? ' +' : ' ') +
                customNumberFormat(assetInfo?.marginNav?.percentMargin ?? 0, 'percent'))
            : assetInfo?.marginNav?.numberMargin +
              ' - ' +
              customNumberFormat(assetInfo?.marginNav?.percentMargin ?? 0, 'percent')
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tổng giá trị CK -->
      <div class="typo-body-9 text-header">{{ 'MES-280' | translate }}</div>

      <div class="typo-body-9">
        {{ assetInfo?.stock ? customNumberFormat(assetInfo?.stock) + ' VND' : '-' }}
      </div>
    </div>

    <!-- +/- Tổng giá trị CK -->
    <div class="box-container">
      <div class="typo-body-9 text-header">+/- {{ 'MES-280' | translate }}</div>
      <div
        class="typo-body-9 text-color"
        [class.text-color-green]="assetInfo?.marginStock?.numberMargin ?? 0 > 0"
        [ngClass]="{
          'text-color-green': 0 < (assetInfo?.marginStock?.numberMargin ?? 0),
          'danger-color': 0 > (assetInfo?.marginStock?.numberMargin ?? 0)
        }"
      >
        <img
          [src]="
            0 < (assetInfo?.marginStock?.numberMargin ?? 0)
              ? './assets/icons/up.svg'
              : 0 > (assetInfo?.marginStock?.numberMargin ?? 0)
              ? './assets/icons/down.svg'
              : './assets/icons/minus.svg'
          "
          alt="up-icon"
        />
        {{
          assetInfo?.marginStock && assetInfo?.marginStock?.numberMargin
            ? (0 < (assetInfo?.marginStock?.numberMargin ?? 0) ? '+' : '') +
              customNumberFormat(assetInfo?.marginStock?.numberMargin ?? 0) +
              ((0 < (assetInfo?.marginStock?.numberMargin ?? 0) ? ' +' : ' ') +
                customNumberFormat(assetInfo?.marginStock?.percentMargin ?? 0, 'percent'))
            : assetInfo?.marginStock?.numberMargin +
              ' - ' +
              customNumberFormat(assetInfo?.marginStock?.percentMargin ?? 0, 'percent')
        }}
      </div>
    </div>
  </div>

  <div class="box-element">
    <!-- <div class="box-container"> -->
    <!-- Sức mua -->
    <!-- <div class="typo-body-9 text-header">{{ 'MES-203' | translate }}</div>
      <div class="typo-body-9">
        {{
          assetInfo?.purchasingPower === 0
            ? '0 VND'
            : assetInfo?.purchasingPower
            ? customNumberFormat(assetInfo?.purchasingPower) + ' VND'
            : '-'
        }}
      </div> -->
    <!-- </div> -->

    <!-- Tổng giá tài sản -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-425' | translate }}</div>
      <div class="typo-body-9">{{ assetInfo?.assets ? customNumberFormat(assetInfo?.assets) + ' VND' : '-' }}</div>
    </div>

    <!-- Tỉ lệ ký quỹ -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-650' | translate }}</div>
      <ng-container *ngIf="assetInfo?.marginRate?.toString()">
        <div class="typo-body-9 tag-value" [ngClass]="(assetInfo?.marginRate ?? 0) < 100 ? 'down' : 'equal'">
          {{ customNumberFormat(assetInfo?.marginRate ?? 0, 'percent') }}
        </div>
      </ng-container>
    </div>
  </div>

  <div class="box-element">
    <div class="box-container">
      <!-- Tổng tiền-->
      <div class="typo-body-9 text-header">{{ 'MES-281' | translate }}</div>
      <div class="typo-body-9">
        {{ assetInfo?.cash ? customNumberFormat(assetInfo?.cash) + ' VND' : '-' }}
      </div>
    </div>

    <!--Tổng dư nợ -->
    <div class="box-container">
      <div class="typo-body-9 text-header">{{ 'MES-242' | translate }}</div>
      <div class="typo-body-9">
        {{ assetInfo?.debt ? customNumberFormat(assetInfo?.debt) + ' VND' : '-' }}
      </div>
    </div>
  </div>
</div>
