import { AfterViewInit, Component, ElementRef, Inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { IAssetInfoData, IDataSetChart, IGuaranteeInfo, IPayloadAssetInfoDetail } from '../../models/asset';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { PopoverGuaranteeMoneyComponent } from '../popover-guarantee-money/popover-guarantee-money.component';
import { Subject, take, takeUntil } from 'rxjs';
import { selectAssetInfoDetail$ } from '../../stores/asset.selectors';
import { DestroyService, DialogService } from 'src/app/core/services';
import { Store } from '@ngrx/store';
import { GuaranteeConfirmComponent } from 'src/app/shared/components/guarantee-confirm/guarantee-confirm.component';
import { getAssetInfoDetail, resetAssetInfoDetailData, updateGuaranteeData } from '../../stores/asset.actions';

/**
 * AssetInfoDetailComponent
 */
@Component({
  selector: 'app-assets-info-detail',
  templateUrl: './assets-info-detail.component.html',
  styleUrl: './assets-info-detail.component.scss',
})
export class AssetInfoDetailComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('assetInfoDetailRef') assetInfoDetailRef!: ElementRef;

  menuNavBar = [
    // Thông tin tài sản
    {
      name: 'MES-43',
      nameIconActive: 'icon:asset-info-icon-active',
      nameIcon: 'icon:asset-info-icon',
      class: 'asset-info',
      dataset: 'asset-info-detail',
    },
    // Tình trạng tài khoản
    // {
    //   name: 'MES-64',
    //   nameIconActive: 'icon:acc-status-icon-active',
    //   nameIcon: 'icon:acc-status-icon',
    //   class: 'acc-status',
    //   dataset: 'status-account-detail',
    // },
    // Giao dịch
    {
      name: 'MES-45',
      nameIconActive: 'icon:convert-card-active',
      nameIcon: 'icon:convert-card',
      class: 'convert-card',
      dataset: 'transaction-info-detail',
    },
    // Thông tin Tiền
    {
      name: 'MES-53',
      nameIconActive: 'icon:money-icon-active',
      nameIcon: 'icon:money-icon',
      class: 'money',
      dataset: 'money-info-detail',
    },
    // Thông tin Sức mua
    {
      name: 'MES-307',
      nameIconActive: 'icon:purchasing-power-icon-active',
      nameIcon: 'icon:purchasing-power-icon',
      class: 'purchasing-power',
      dataset: 'purchasing-power-detail',
    },
    // Ký quỹ VSD
    // {
    //   name: 'MES-54',
    //   nameIconActive: 'icon:wallet-money-active',
    //   nameIcon: 'icon:wallet-money',
    //   class: 'wallet-money',
    //   dataset: 'deposit-vsd-detail',
    // },
    // Dư nợ
    // {
    //   name: 'MES-55',
    //   nameIconActive: 'icon:debt-icon-active',
    //   nameIcon: 'icon:debt-icon',
    //   class: 'debt',
    //   dataset: 'debt-detail',
    // },
    // Món vay
    // {
    //   name: 'MES-56',
    //   nameIconActive: 'icon:wallet-remove-active',
    //   nameIcon: 'icon:wallet-remove',
    //   class: 'wallet-remove',
    //   dataset: 'loan-detail',
    // },
    // DM đầu tư
    {
      name: 'MES-57',
      nameIconActive: 'icon:investment-portfolio-active',
      nameIcon: 'icon:investment-portfolio',
      class: 'investment-portfolio',
      dataset: 'portfolio-detail',
    },
    // phân bổ tài sản
    // {
    //   name: 'MES-426',
    //   nameIconActive: 'icon:percentage-icon-active',
    //   nameIcon: 'icon:percentage-icon',
    //   class: 'proportion-asset-detail',
    //   dataset: 'proportion-asset-detail',
    // },
    // {
    //   name: 'MES-65',
    //   nameIconActive: 'icon:percentage-icon-active',
    //   nameIcon: 'icon:percentage-icon',
    //   class: 'percentage',
    // },
  ];

  datasetLabels: string[] = [];

  assetInfoDatasets: IDataSetChart[] = [];

  labels = [
    {
      name: 'Tiền mặt',
    },
    {
      name: 'Cổ phiếu',
    },
    {
      name: 'Trái phiếu',
    },
    {
      name: 'Phái sinh',
    },
    {
      name: 'Chứng chỉ quỹ',
    },
  ];

  navItems: any[] = [];

  assetContent: any[] = [];

  guaranteeData!: IGuaranteeInfo;

  element = {
    accountNumber: '069C-0000000',
    guaranteeMax: *********, // fakeMax
    nav: **********, // Tài khoản ròng (NAV)
    cash: **********, // Tổng tiền
    purchasingPower: **********, // Sức mua
    guarantee: 0, // Tiền bảo lãnh
    usedGuarantee: *********, // Tiền bảo lãnh đã SD
    toSubmitGuarantee: *********, // Tiền bảo lãnh cần nộp
  };

  assetInfoData: IAssetInfoData | undefined = undefined;

  listAssetsData: IAssetInfoData[] = [];

  private readonly destroy$ = new Subject<void>();

  infoCustomer!: any;
  /**
   * Constructor
   * @param data data
   * @param popoverService popoverService
   * @param _destroy
   * @param store
   * @param dialogService
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private readonly popoverService: PopoverService,
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly dialogService: DialogService
  ) {}

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    const { element, brokerCode } = this.data;
    const payload: IPayloadAssetInfoDetail = {
      accountNumber: element.parent.accountNumber,
      customerName: element.parent.customerName,
      brokerCode,
      subAccount: element.subAccountNumber ?? element.subAccount,
    };

    this.infoCustomer = {
      accountNumber: payload.accountNumber,
      customerName: payload.customerName,
      subAccount: payload.subAccount,
    };

    this.store.dispatch(getAssetInfoDetail({ data: payload }));

    this.store
      .select(selectAssetInfoDetail$)
      .pipe(takeUntil(this.destroy$))
      .subscribe((assetInfo) => {
        if (!assetInfo) return;
        this.assetInfoData = {
          accountNumber: element.accountNumber ?? element.parent.accountNumber + ' - ' + payload.subAccount,
          customerName: element.parent.customerName,
          ...assetInfo,
          assetsInfo: {
            ...assetInfo.assetsInfo,
            marginNav: {
              ...assetInfo.assetsInfo.marginNav,
              percentMargin: +assetInfo.assetsInfo.marginNav.percentMargin * 100,
            },
            marginStock: {
              ...assetInfo.assetsInfo.marginStock,
              percentMargin: +assetInfo.assetsInfo.marginStock.percentMargin * 100,
            },
          },
          investmentInfo: {
            subAccount: element.subAccountNumber,
            capitalInvestment: assetInfo.portfolioInvestmentsInfo.reduce((init: any, item: any) => {
              init = init + item.capitalInvestment;
              return init;
            }, 0),
            price: assetInfo.portfolioInvestmentsInfo.reduce((init: any, item: any) => {
              init = init + item.price;
              return init;
            }, 0),
            children: assetInfo.portfolioInvestmentsInfo.map((item: any) => ({
              ...item,
              subAccount: item.accountNumber,
              stockCode: item.stockCode,
              price: +item.price,
              capitalInvestment: item.capitalInvestment,
              invest: item.invest,
              percentInvest: item.percentInvest,
              tradableVolume: item.tradableVolume,
              totalVolume: item.totalVolume,
              t0Volume: item.t0Volume,
              t1Volume: item.t1Volume,
              t2Volume: item.t2Volume,
              currentPrice: item.currentPrice,
              costPrice: item.costPrice,
            })),
          },
        };
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.store.dispatch(resetAssetInfoDetailData());
  }

  /**
   * NgAfterViewInit
   */
  ngAfterViewInit(): void {
    const assetInfoContainer = document.querySelector('.asset-info-detail-container') as HTMLElement;
    this.navItems = assetInfoContainer.querySelectorAll('.box-nav-bar') as any;
    this.assetContent = assetInfoContainer.querySelectorAll('.box-content') as any;

    const indexItem = this.menuNavBar.findIndex((t) => t.dataset === this.data.status);
    if (indexItem !== 0) {
      this.selectNavItem(this.menuNavBar[indexItem], true, indexItem === this.menuNavBar.length - 1);
    } else {
      assetInfoContainer.querySelector('.box-nav-bar')?.classList.add('isSeleted');
    }
  }

  /**
   * UpdateChartAssets
   */
  updateChartAssets() {
    this.datasetLabels = this.labels.map((t) => t.name);

    this.assetInfoDatasets = [
      {
        label: '',
        dataConfig: this.labels.map((t) => t.name),
        data: Object.values(this.assetInfoData!.propotionAsset)
          .filter((item) => typeof item === 'object' && item.numberProportion !== undefined)
          .map((t) => t.numberProportion),
        backgroundColor: ['#B1E1F8', '#BAE7A3', '#FECACA', '#FDE68A', '#FAC698'],
      },
    ];
  }

  /**
   * HandleScroll
   * @param event
   */
  handleScroll(event: Event) {
    const assetInfoContainer = document.querySelector('.asset-info-detail-container') as HTMLElement;
    const containerWrapper = assetInfoContainer.querySelector('.asset-content') as HTMLElement;
    let index = 0;

    while (
      index < this.assetContent.length &&
      this.assetContent[index].offsetTop <= containerWrapper.scrollTop + containerWrapper.offsetTop
    ) {
      ++index;
    }

    if (index >= this.assetContent.length) {
      index = this.assetContent.length - 1;
    }

    this.navItems.forEach((nav: HTMLElement) => {
      nav.classList.remove('isSeleted');
    });

    if (index < this.navItems.length) {
      this.navItems[index].classList.add('isSeleted');
    }
    if (Math.ceil(containerWrapper.scrollTop + containerWrapper.offsetHeight) >= containerWrapper.scrollHeight) {
      index = this.assetContent.length - 1;
      this.navItems.forEach((nav: HTMLElement) => {
        nav.classList.remove('isSeleted');
      });
      if (index < this.navItems.length) {
        this.navItems[index].classList.add('isSeleted');
      }
    }
  }

  /**
   * SelectNavItem
   * @param nav
   * @param isSmooth
   * @param isLast
   */
  selectNavItem(nav: any, isSmooth?: boolean, isLast?: boolean) {
    const assetInfoContainer = document.querySelector('.asset-info-detail-container') as HTMLElement;
    const containerWrapper = assetInfoContainer.querySelector('.asset-content') as HTMLElement;

    const targetSection = document.getElementById(nav.dataset) as HTMLElement;
    setTimeout(() => {
      containerWrapper.scrollTo({
        top: isLast ? containerWrapper.scrollHeight : targetSection.offsetTop - 300,

        behavior: isSmooth ? 'auto' : 'smooth',
      });
    });
  }

  /**
   * openPopoverGuaranteeMoney
   * @param event Event
   * @param element
   */
  openPopoverGuaranteeMoney(event: MouseEvent, element: any) {
    const originElement = event.target as HTMLElement;

    this.popoverService.open({
      origin: originElement,
      content: PopoverGuaranteeMoneyComponent,
      width: 400,
      height: 383,
      position: 0,
      hasBackdrop: true,
      componentConfig: { element },
    });
  }

  /**
   * Cấp bảo lãnh
   * grantGuaranteeData
   * @param value
   */
  grantGuaranteeData(value: number) {
    const { guarantee, purchasingPower } = this.element;
    this.element = {
      ...this.element,
      guarantee: guarantee + value,
      purchasingPower: purchasingPower + value,
    };
  }

  /**
   * Nộp bảo lãnh
   * submitGuaranteeData
   * @param value
   */
  submitGuaranteeData(value: number) {
    const { toSubmitGuarantee, cash, purchasingPower } = this.element;
    this.element = {
      ...this.element,
      toSubmitGuarantee: toSubmitGuarantee - (value ?? 0),
      cash: cash - (value ?? 0),
      purchasingPower: purchasingPower - (value ?? 0),
    };
  }

  /**
   * Gửi thông báo
   * SendNotify
   * @param event MouseEvent
   * @param {string} accountNumber - accountNumber
   */
  sendNotify(event: MouseEvent, accountNumber: string) {
    this.dialogService.openPopUp(GuaranteeConfirmComponent, {
      width: '360px',
      height: '273px',
      panelClass: [''],
      data: {
        action: 'notify',
        info: [accountNumber],
        title: ['MES-15'],
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-144',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
    });
  }

  /**
   * CollectGuarantee
   * @param event MouseEvent
   * @param {string} accountNumber - accountNumber
   * @param {number} toSubmitGuarantee - toSubmitGuarantee
   */
  collectGuarantee(event: MouseEvent, accountNumber: string, toSubmitGuarantee: number) {
    const ref = this.dialogService.openPopUp(GuaranteeConfirmComponent, {
      width: '360px',
      height: '297px',
      panelClass: [''],
      data: {
        action: 'submitGuarantee',
        info: [accountNumber, toSubmitGuarantee],
        title: ['MES-15', 'MES-142'],
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-143',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        const info = {
          type: v.type,
          accountNumber: v.data[0],
          value: v.data[1],
        };
        this.store.dispatch(updateGuaranteeData({ data: info }));
      });
  }
}
