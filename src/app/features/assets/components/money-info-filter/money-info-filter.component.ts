import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { take, tap } from 'rxjs';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { DestroyService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IFilterMoneyInfoParam, IRangeFilter } from '../../models/asset';
import { Store } from '@ngrx/store';
import { selectAllAccountNumberListByBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { validFromToValidator } from 'src/app/shared/validators/form';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';
import { transformToNumber } from 'src/app/shared/utils/format-numbet';

/**
 * MoneyInfoFilterComponent
 */
@Component({
  selector: 'app-money-info-filter',
  templateUrl: './money-info-filter.component.html',
  styleUrl: './money-info-filter.component.scss',
  providers: [DestroyService],
})
export class MoneyInfoFilterComponent implements OnInit {
  @ViewChild(VirtualScrollListComponent) virtualScroll!: VirtualScrollListComponent;

  moneyInfoFilterForm!: FormGroup;

  // Key identifier for customer objects
  readonly customerKey = 'accountNumber';

  customers: IAllAccountNumber[] = [];

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  isDisableApply = false;
  /**
   *
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef PopoverRef
   * @param data IFilterMoneyInfoParam
   * @param dialogRef MatDialogRef
   * @param store Store
   */
  constructor(
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: IFilterMoneyInfoParam,
    public dialogRef: MatDialogRef<MoneyInfoFilterComponent>,
    private readonly store: Store,
    private readonly fb: FormBuilder
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.initForm();
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.loadCustomerList();
    this.patchValueToForm();
  }

  private initForm() {
    this.moneyInfoFilterForm = this.fb.group(
      {
        startPurchasingPower: new FormControl(),
        endPurchasingPower: new FormControl(),

        startCash: new FormControl(),
        endCash: new FormControl(),

        startCashBalance: new FormControl(),
        endCashBalance: new FormControl(),

        startAwaitingAdvance: new FormControl(),
        endAwaitingAdvance: new FormControl(),

        startT0: new FormControl(),
        endT0: new FormControl(),

        startT1: new FormControl(),
        endT1: new FormControl(),

        startT2: new FormControl(),
        endT2: new FormControl(),

        startBlockedCash: new FormControl(),
        endBlockedCash: new FormControl(),

        startGuarantee: new FormControl(),
        endGuarantee: new FormControl(),

        startUsedGuarantee: new FormControl(),
        endUsedGuarantee: new FormControl(),

        startToSubmitGuarantee: new FormControl(),
        endToSubmitGuarantee: new FormControl(),
      },
      {
        validators: [
          validFromToValidator('startPurchasingPower', 'endPurchasingPower'),
          validFromToValidator('startCash', 'endCash'),
          validFromToValidator('startCashBalance', 'endCashBalance'),
          validFromToValidator('startAwaitingAdvance', 'endAwaitingAdvance'),
          validFromToValidator('startT0', 'endT0'),
          validFromToValidator('startT1', 'endT1'),
          validFromToValidator('startT2', 'endT2'),
          validFromToValidator('startBlockedCash', 'endBlockedCash'),
          validFromToValidator('startGuarantee', 'endGuarantee'),
          validFromToValidator('startUsedGuarantee', 'endUsedGuarantee'),
          validFromToValidator('startToSubmitGuarantee', 'endToSubmitGuarantee'),
        ],
      }
    );
  }

  private patchValueToForm() {
    const {
      // accountNumber,
      // purchasingPower,
      cashValue,
      cashBalanceValue,
      awaitingAdvanceValue,
      t0,
      t1,
      t2,
      blockedCashValue,
      notMatchedValue,
      unpaidValue,
      awaitingDividendsValue,
    } = this.data;

    this.moneyInfoFilterForm.patchValue({
      // startPurchasingPower: purchasingPower.from,
      // endPurchasingPower: purchasingPower.to,

      startCash: cashValue?.from,
      endCash: cashValue?.to,

      startCashBalance: cashBalanceValue?.from,
      endCashBalance: cashBalanceValue?.to,

      startAwaitingAdvance: awaitingAdvanceValue?.from,
      endAwaitingAdvance: awaitingAdvanceValue?.to,

      startT0: t0?.from,
      endT0: t0?.to,

      startT1: t1?.from,
      endT1: t1?.to,

      startT2: t2?.from,
      endT2: t2?.to,

      startBlockedCash: blockedCashValue?.from,
      endBlockedCash: blockedCashValue?.to,

      startGuarantee: notMatchedValue?.from,
      endGuarantee: notMatchedValue?.to,

      startUsedGuarantee: unpaidValue?.from,
      endUsedGuarantee: unpaidValue?.to,

      startToSubmitGuarantee: awaitingDividendsValue?.from,
      endToSubmitGuarantee: awaitingDividendsValue?.to,
    });
  }

  loadCustomerList() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(
        take(1),
        tap((customers) => {
          this.customers = customers;
        })
      )
      .subscribe();
  }

  /**
   * UpdateFormControlValue
   * @param date date
   * @param startControl startControl
   * @param endControl endControl
   */
  updateFormControlValue(date: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    const { from, to } = date;
    startControl.patchValue(from);
    endControl.patchValue(to);
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    if (this.isDisableApply) return;

    const accountNumber = (this.virtualScroll.getChecked() as IAllAccountNumber[]).map((c) => c.accountNumber);

    const {
      startPurchasingPower,
      endPurchasingPower,

      startCash,
      endCash,

      startCashBalance,
      endCashBalance,

      startAwaitingAdvance,
      endAwaitingAdvance,

      startT0,
      endT0,

      startT1,
      endT1,

      startT2,
      endT2,

      startBlockedCash,
      endBlockedCash,

      startGuarantee,
      endGuarantee,

      startUsedGuarantee,
      endUsedGuarantee,

      startToSubmitGuarantee,
      endToSubmitGuarantee,
    } = this.moneyInfoFilterForm.value;

    const purchasingPower = {
      from: transformToNumber(startPurchasingPower),
      to: transformToNumber(endPurchasingPower),
    };

    const cashValue = {
      from: transformToNumber(startCash),
      to: transformToNumber(endCash),
    };

    const cashBalanceValue = {
      from: transformToNumber(startCashBalance),
      to: transformToNumber(endCashBalance),
    };

    const awaitingAdvanceValue = {
      from: transformToNumber(startAwaitingAdvance),
      to: transformToNumber(endAwaitingAdvance),
    };

    const t0 = {
      from: transformToNumber(startT0),
      to: transformToNumber(endT0),
    };

    const t1 = {
      from: transformToNumber(startT1),
      to: transformToNumber(endT1),
    };

    const t2 = {
      from: transformToNumber(startT2),
      to: transformToNumber(endT2),
    };

    const blockedCashValue = {
      from: transformToNumber(startBlockedCash),
      to: transformToNumber(endBlockedCash),
    };

    // đổi thành tiền mua chưa khớp
    const notMatchedValue = {
      from: transformToNumber(startGuarantee),
      to: transformToNumber(endGuarantee),
    };

    // đổi thành tiền mua chưa thanh toán
    const unpaidValue = {
      from: transformToNumber(startUsedGuarantee),
      to: transformToNumber(endUsedGuarantee),
    };

    // đổi thành tiền cổ tức chờ về
    const awaitingDividendsValue = {
      from: transformToNumber(startToSubmitGuarantee),
      to: transformToNumber(endToSubmitGuarantee),
    };

    const filterField = {
      accountNumber,
      purchasingPower,
      cashBalanceValue,
      awaitingAdvanceValue,
      cashValue,
      t0,
      t1,
      t2,
      blockedCashValue,
      notMatchedValue,
      awaitingDividendsValue,
      unpaidValue,
    };

    const isFilter = this.checkStatusFilter(filterField);

    const optionFilter = {
      isFilter,
      accountNumber,
      purchasingPower,
      cashValue,
      cashBalanceValue,
      awaitingAdvanceValue,
      t0,
      t1,
      t2,
      blockedCashValue,
      notMatchedValue,
      unpaidValue,
      awaitingDividendsValue,
    };

    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * CheckStatusFilter
   * @param status
   * @param rangeDateHold
   * @param rangePotential
   * @param rangeNotRecord
   * @param rangeRecord
   * @param date
   * @returns {boolean} true / false
   */
  checkStatusFilter(filterField: {
    accountNumber: null | (string | undefined)[];
    purchasingPower: IRangeFilter;
    cashBalanceValue: IRangeFilter;
    awaitingAdvanceValue: IRangeFilter;
    cashValue: IRangeFilter;
    t0: IRangeFilter;
    t1: IRangeFilter;
    t2: IRangeFilter;
    blockedCashValue: IRangeFilter;
    notMatchedValue: IRangeFilter;
    awaitingDividendsValue: IRangeFilter;
    unpaidValue: IRangeFilter;
  }) {
    const {
      accountNumber,
      purchasingPower,
      cashBalanceValue,
      awaitingAdvanceValue,
      cashValue,
      t0,
      t1,
      t2,
      blockedCashValue,
      notMatchedValue,
      awaitingDividendsValue,
      unpaidValue,
    } = filterField;
    const isInitialCustomerSelect = accountNumber?.length === 0;
    return (
      !isInitialCustomerSelect ||
      this.checkHasValueInObject(purchasingPower) ||
      this.checkHasValueInObject(cashBalanceValue) ||
      this.checkHasValueInObject(awaitingAdvanceValue) ||
      this.checkHasValueInObject(cashValue) ||
      this.checkHasValueInObject(t0) ||
      this.checkHasValueInObject(t1) ||
      this.checkHasValueInObject(t2) ||
      this.checkHasValueInObject(blockedCashValue) ||
      this.checkHasValueInObject(notMatchedValue) ||
      this.checkHasValueInObject(awaitingDividendsValue) ||
      this.checkHasValueInObject(unpaidValue)
    );
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObject(data: any) {
    return !!data && ((data.from != null && data.from !== '') || (data.to != null && data.to !== ''));
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * getFormControl
   * @param {string} field
   */
  getFormControl(field: string) {
    return this.moneyInfoFilterForm.get(field) as FormControl;
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customer: IAllAccountNumber): string {
    if (!customer) return '';
    return `${customer.accountNumber} - ${customer.customerName}`;
  }
}
