<div class="money-info-filter-wrap">
  <div class="money-info-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <form [formGroup]="moneyInfoFilterForm" class="money-info-content">
    <!-- LEFT -->
    <div class="content-left">
      <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResults"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-669'"
          [warningMessage]="'MES-668'"
          [selectedKeys]="data.accountNumber"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-295' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          [displayFn]="displayCustomerFilter"
          (invalidSelection)="validateFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResults>
          <div class="typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>


      <!-- Tổng tiền -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-281' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startCash')"
          [toControl]="getFormControl('endCash')"
        ></app-from-to>
      </div>

      <!-- Số dư tiền mặt -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-291' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startCashBalance')"
          [toControl]="getFormControl('endCashBalance')"
        ></app-from-to>
      </div>

      <!-- Tiền bán chờ về khả ứng -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-613' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startAwaitingAdvance')"
          [toControl]="getFormControl('endAwaitingAdvance')"
        ></app-from-to>
      </div>
    </div>

    <!-- RIGHT -->
    <div class="content-right">

      <!-- Tiền cổ tức chờ về -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-436' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startToSubmitGuarantee')"
          [toControl]="getFormControl('endToSubmitGuarantee')"
        ></app-from-to>
      </div>

      <!-- Tiền bị phong tỏa -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-293' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startBlockedCash')"
          [toControl]="getFormControl('endBlockedCash')"
        ></app-from-to>
      </div>

      <!-- Tiền mua chưa khớp -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-395' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startGuarantee')"
          [toControl]="getFormControl('endGuarantee')"
        ></app-from-to>
      </div>

      <!-- Tiền mua chưa TT -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-396' | translate }}</div>

        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startUsedGuarantee')"
          [toControl]="getFormControl('endUsedGuarantee')"
        ></app-from-to>
      </div>
    </div>
  </form>

  <div class="footer-filter">
    <div mat-dialog-close (click)="defaultFilter()" class="btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <button
      [disabled]="moneyInfoFilterForm.invalid || isDisableApply"
      (click)="applyFilter()"
      class="btn apply typo-button-3"
    >
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
