.deposit-info-filter-wrap {
  width: 800px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.deposit-info-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--color--other--divider);

  .close-btn {
    cursor: pointer;
  }
}

.deposit-info-content {
  display: flex;
  flex: 1;
  overflow: hidden;

  .content-left,
  .content-right {
    width: 50%;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
  }

  .content-left {
    border-right: 1px solid var(--color--other--divider);

    .title-left {
      padding: 12px 24px;
    }

    // Mã CK
    .searchbox-wrap {
      padding: 8px 24px 16px;
      display: flex;
      flex-direction: column;
      gap: 17px;
      overflow: hidden;
      border-bottom: 1px solid var(--color--other--divider);
      min-height: 245px;

      .search-box {
        display: flex;
        position: relative;

        .input-cls-custom {
          padding: 10px 16px;
          padding-left: 32px;
          width: 100%;
        }

        .icon-search-cls {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 10px;
        }
      }

      .option-list-cls {
        height: 164px;
        overflow: auto;
        display: flex;
        flex-direction: column;
        gap: 16px;

        .checkbox-cls {
          ::ng-deep {
            .mdc-label {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;
            }
          }

          .img-cls {
            width: 18px;
            height: 18px;
            object-fit: contain;
            vertical-align: middle;
            border-radius: 50%;
          }
        }
      }
    }

    .from-to {
      display: flex;
      flex-direction: column;
      width: 100%;

      .purchasing-power-content {
        display: flex;
        gap: 8px;
        padding: 8px 24px 16px;

        .content-from,
        .content-to {
          width: 50%;
          display: flex;
          flex-direction: column;
          gap: 4px;

          input {
            border: 1px solid var(--Neutral-100, #dbdee0);
            padding: 10px 16px;
            border-radius: 8px;
            height: 40px;
          }
        }
      }
    }
  }

  .content-right {
    .title-right {
      padding: 12px 24px;
    }

    .from-to {
      display: flex;
      flex-direction: column;
      width: 100%;

      .purchasing-power-content {
        display: flex;
        gap: 8px;
        padding: 8px 24px 16px;

        .content-from,
        .content-to {
          width: 50%;
          display: flex;
          flex-direction: column;
          gap: 4px;

          input {
            border: 1px solid var(--Neutral-100, #dbdee0);
            padding: 10px 16px;
            border-radius: 8px;
            height: 40px;
          }
        }
      }
    }

    .option-list-cls {
      padding: 8px 24px 16px;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .checkbox-cls {
        ::ng-deep {
          .mdc-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
          }
        }

        .img-cls {
          width: 18px;
          height: 18px;
          object-fit: contain;
          vertical-align: middle;
          border-radius: 50%;
        }
      }
    }
  }
}

.footer-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--color--other--divider);
  padding: 16px 24px;

  .btn {
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid var(--color--other--divider);
  }

  .default {
    background-color: var(--color--neutral--white);
  }

  .apply {
    background-color: var(--color--brand--500);
    color: var(--color--neutral--white);
  }
}

::ng-deep {
  .dropdown-overlay-common {
    ng-component {
      width: 100% !important;
    }
  }
}
