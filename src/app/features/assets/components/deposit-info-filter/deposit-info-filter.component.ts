import { Component, Inject, OnInit, Optional } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ICustomers, IListOptions } from '../../constant/assets';
import { DestroyService } from 'src/app/core/services';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IRangeFilter } from '../../models/asset';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';

export const MARGIN_STATUS = [
  {
    label: 'NORMAL',
    value: 0,
  },
  {
    label: 'WARNING 01',
    value: 1,
  },
  {
    label: 'WARNING 02',
    value: 2,
  },
  {
    label: 'FORCE SELL',
    value: 3,
  },
];

/**
 * DepositInfoFilterComponent
 */
@Component({
  selector: 'app-deposit-info-filter',
  templateUrl: './deposit-info-filter.component.html',
  styleUrl: './deposit-info-filter.component.scss',
})
export class DepositInfoFilterComponent implements OnInit {
  isSelectAllCustomer = true;
  isSelectAllMarginStatus = true;

  listFilterCustomerOptions: IListOptions[] = [];
  listFilterCustomerStore: IListOptions[] = [];

  listFilterMarginStatusOptions: IListOptions[] = [];

  searchCustomerControl = new FormControl();

  startMarginRate = new FormControl();
  endMarginRate = new FormControl();

  startNav = new FormControl();
  endNav = new FormControl();

  startCash = new FormControl();
  endCash = new FormControl();

  startPurchasingPower = new FormControl();
  endPurchasingPower = new FormControl();

  startEightyAssets = new FormControl();
  endEightyAssets = new FormControl();

  startEightyInvest = new FormControl();
  endEightyInvest = new FormControl();

  startNotSubmittedMargin = new FormControl();
  endNotSubmittedMargin = new FormControl();

  startToSubmitGuarantee = new FormControl();
  endToSubmitGuarantee = new FormControl();

  startVsdcMargin = new FormControl();
  endVsdcMargin = new FormControl();

  startCanWithdrawMargin = new FormControl();
  endCanWithdrawMargin = new FormControl();

  startToSubmittedMargin = new FormControl();
  endToSubmittedMargin = new FormControl();

  customers: ICustomers[] = [];

  /**
   *
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param data
   * @param dialogRefFilterDeposit
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRefFilterDeposit: MatDialogRef<DepositInfoFilterComponent>
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    const { filterOptions, customers } = data;
    this.customers = customers;
    const {
      customersSelect,
      marginRate,
      nav,
      cash,
      purchasingPower,
      eightyAssets,
      eightyInvest,
      notSubmittedMargin,
      marginStatus,
      vsdcMargin,
      canWithdrawMargin,
      toSubmittedMargin,
    } = filterOptions;
    console.log(customersSelect);
    this.updateListDI(customersSelect, 'customer');
    this.updateListDI(marginStatus, 'marginStatus');
    this.updateFormControlValueDI(purchasingPower, this.startPurchasingPower, this.endPurchasingPower);
    this.updateFormControlValueDI(cash, this.startCash, this.endCash);
    this.updateFormControlValueDI(marginRate, this.startMarginRate, this.endMarginRate);
    this.updateFormControlValueDI(nav, this.startNav, this.endNav);
    this.updateFormControlValueDI(eightyAssets, this.startEightyAssets, this.endEightyAssets);
    this.updateFormControlValueDI(eightyInvest, this.startEightyInvest, this.endEightyInvest);
    this.updateFormControlValueDI(notSubmittedMargin, this.startNotSubmittedMargin, this.endNotSubmittedMargin);
    this.updateFormControlValueDI(vsdcMargin, this.startVsdcMargin, this.endVsdcMargin);
    this.updateFormControlValueDI(canWithdrawMargin, this.startCanWithdrawMargin, this.endCanWithdrawMargin);
    this.updateFormControlValueDI(toSubmittedMargin, this.startToSubmittedMargin, this.endToSubmittedMargin);
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.listFilterCustomerStore = this.listFilterCustomerOptions;
    this.listenSearchChangeDepositInfo()
  }

  private listenSearchChangeDepositInfo() {
    this.searchCustomerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterCustomerOptions = this._filterDI(value ?? '', this.listFilterCustomerStore);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * UpdateFormControlValueDI
   * @param date date
   * @param startControl startControl
   * @param endControl endControl
   */
  updateFormControlValueDI(date: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    const { start, end } = date;
    startControl.patchValue(start);
    endControl.patchValue(end);
  }

  /**
   * updateListDI
   * @param data
   * @param type
   */
  updateListDI(data: (string | number)[] | null, type: string) {
    const isSelectDI = (v: string | number) => data === null || data?.includes(v);
    switch (type) {
      case 'customer':
        this.listFilterCustomerOptions = this.customers.map((customer) => ({
          name: customer.name,
          group: customer.group,
          isSelect: isSelectDI(customer.name),
        }));
        this.isSelectAllCustomer = this.listFilterCustomerOptions.every((t) => t.isSelect === true);
        break;

      case 'marginStatus':
        this.listFilterMarginStatusOptions = MARGIN_STATUS.map((margin) => ({
          label: margin.label,
          value: margin.value,
          isSelect: isSelectDI(margin.value),
        }));
        this.isSelectAllMarginStatus = this.listFilterMarginStatusOptions.every((t) => t.isSelect === true);
        break;
    }
  }

  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param type Kiểu giá trị được thay đổi
   * @param section
   * @param item Item thay đổi
   */
  changeSectionsDepositInfo(checked: boolean, type: string, section: string, item?: IListOptions) {
    if (type === 'all') {
      this.updateSelectAllDepositInfo(checked, section);
    } else if (type === 'item' && item) {
      item.isSelect = checked;
      this.updateSelectAllStatusDepositInfo(section);
    }
  }

  /**
   * Cập nhật trạng thái chọn tất cả
   * @param {boolean} checked
   * @param {string} section
   */
  updateSelectAllDepositInfo(checked: boolean, section: string) {
    switch (section) {
      case 'customer':
        this.isSelectAllCustomer = checked;
        this.listFilterCustomerOptions.forEach((i) => {
          i.isSelect = checked;
        });
        break;

      case 'marginStatus':
        this.isSelectAllMarginStatus = checked;
        this.listFilterMarginStatusOptions.forEach((i) => {
          i.isSelect = checked;
        });
        break;
    }
  }

  /**
   * Cập nhật trạng thái của SelectAll checkbox
   * @param {string} section
   */
  updateSelectAllStatusDepositInfo(section: string) {
    switch (section) {
      case 'customer':
        this.isSelectAllCustomer = this.listFilterCustomerOptions.every((t) => t.isSelect);
        break;

      case 'marginStatus':
        this.isSelectAllMarginStatus = this.listFilterMarginStatusOptions.every((t) => t.isSelect);
        break;
    }
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    const customersSelect = this.listFilterCustomerOptions.filter((t) => t.isSelect).map((t) => t.name);

    const marginRate = {
      start: this.startMarginRate.value,
      end: this.endMarginRate.value,
    };

    const nav = {
      start: this.startNav.value,
      end: this.endNav.value,
    };

    const cash = {
      start: this.startCash.value,
      end: this.endCash.value,
    };

    const purchasingPower = {
      start: this.startPurchasingPower.value,
      end: this.endPurchasingPower.value,
    };

    const eightyAssets = {
      start: this.startEightyAssets.value,
      end: this.endEightyAssets.value,
    };

    const eightyInvest = {
      start: this.startEightyInvest.value,
      end: this.endEightyInvest.value,
    };

    const notSubmittedMargin = {
      start: this.startNotSubmittedMargin.value,
      end: this.endNotSubmittedMargin.value,
    };

    const vsdcMargin = {
      start: this.startVsdcMargin.value,
      end: this.endVsdcMargin.value,
    };

    const canWithdrawMargin = {
      start: this.startCanWithdrawMargin.value,
      end: this.endCanWithdrawMargin.value,
    };
    const toSubmittedMargin = {
      start: this.startToSubmittedMargin.value,
      end: this.endToSubmittedMargin.value,
    };

    const marginStatus = this.listFilterMarginStatusOptions.filter((t) => t.isSelect).map((t) => t.value);
    const isFilter =
      customersSelect.length !== this.listFilterCustomerOptions.length ||
      marginStatus.length !== this.listFilterMarginStatusOptions.length ||
      marginRate ||
      nav ||
      cash ||
      purchasingPower ||
      eightyAssets ||
      eightyInvest ||
      notSubmittedMargin ||
      toSubmittedMargin;

    const optionFilter = {
      isFilter,
      customersSelect,
      marginRate,
      nav,
      cash,
      purchasingPower,
      eightyAssets,
      eightyInvest,
      notSubmittedMargin,
      marginStatus,
      vsdcMargin,
      canWithdrawMargin,
      toSubmittedMargin,
    };

    this.dialogRefFilterDeposit.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * DefaultFilterDI
   */
  defaultFilterDI() {
    this.dialogRefFilterDeposit.close({
      type: 'default',
    });
  }

  /**
   *
   * @param {string} value
   * @param {IListOptions} options
   */
  private _filterDI(value: string, options: IListOptions[]): IListOptions[] {
    const filterValue = value.toString().toLowerCase();

    return options.filter(
      (option) => option.name?.toLowerCase().includes(filterValue) || option.group?.toLowerCase().includes(filterValue)
    );
  }
}
