<div class="deposit-info-filter-wrap">
  <div class="deposit-info-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="deposit-info-content">
    <!-- LEFT -->
    <div class="content-left">
      <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        <div class="search-box">
          <input
            type="text"
            class="input-cls-custom input-style-common typo-body-11 fs-12"
            [placeholder]="'MES-295' | translate"
            [formControl]="searchCustomerControl"
          />
          <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
        </div>
        <div class="option-list-cls">
          <mat-checkbox
            (change)="changeSectionsDepositInfo($event.checked, 'all', 'customer')"
            [checked]="isSelectAllCustomer"
            class="checkbox-cls"
          >
            {{ 'MES-58' | translate }}</mat-checkbox
          >
          @for (item of listFilterCustomerOptions; let i = $index; track item) {
          <div class="checkbox-cls-item typo-body-15">
            <mat-checkbox
              (change)="changeSectionsDepositInfo($event.checked, 'item', 'customer', item)"
              [checked]="item.isSelect"
              class="checkbox-cls"
            >
              {{ item.group }} - {{ item.name }}</mat-checkbox
            >
          </div>
          }
        </div>
      </div>

      <!-- Tỷ lệ ký quỹ -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-282' | translate }} (%)</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0%'"
              [formControl]="startMarginRate"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endMarginRate"
            />
          </div>
        </div>
      </div>

      <!-- Tài sản dòng (NAV) -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-279' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startNav"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endNav"
            />
          </div>
        </div>
      </div>

      <!-- Tổng tiền -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-281' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startCash"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endCash"
            />
          </div>
        </div>
      </div>

      <!-- Sức mua -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-203' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startPurchasingPower"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endPurchasingPower"
            />
          </div>
        </div>
      </div>
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-420' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startToSubmittedMargin"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endToSubmittedMargin"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- RIGHT -->
    <div class="content-right">
      <!-- Tài sản 80 -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-296' | translate }} 80</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startEightyAssets"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endEightyAssets"
            />
          </div>
        </div>
      </div>

      <!-- Giá trị đầu tư 80 -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-297' | translate }} 80</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startEightyInvest"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endEightyInvest"
            />
          </div>
        </div>
      </div>

      <!-- Số dư chưa nộp ký quỹ -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-298' | translate }} đã SD</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startNotSubmittedMargin"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endNotSubmittedMargin"
            />
          </div>
        </div>
      </div>

      <!-- Tình trạng ký quỹ -->
      <div class="title-right typo-body-15">{{ 'MES-299' | translate }}</div>
      <div class="option-list-cls">
        <mat-checkbox
          (change)="changeSectionsDepositInfo($event.checked, 'all', 'marginStatus')"
          [checked]="isSelectAllMarginStatus"
          class="checkbox-cls"
        >
          {{ 'MES-58' | translate }}</mat-checkbox
        >
        @for (item of listFilterMarginStatusOptions; let i = $index; track item) {
        <div class="checkbox-cls-item typo-body-15">
          <mat-checkbox
            (change)="changeSectionsDepositInfo($event.checked, 'item', 'marginStatus', item)"
            [checked]="item.isSelect"
            class="checkbox-cls"
          >
            {{ item.label }}</mat-checkbox
          >
        </div>
        }
      </div>

      <!-- Tiền ký quỹ VSDC -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-418' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startVsdcMargin"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endVsdcMargin"
            />
          </div>
        </div>
      </div>

      <!-- Tiền ký quỹ có thể rút -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-419' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startCanWithdrawMargin"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endCanWithdrawMargin"
            />
          </div>
        </div>
      </div>

      <!-- Tiền ký quỹ phải nộp -->
    </div>
  </div>

  <div class="footer-filter">
    <!--  (click)="defaultFilter()" -->
    <div mat-dialog-close (click)="defaultFilterDI()" class="btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <!-- (click)="applyFilter()"   -->
    <div mat-dialog-close (click)="applyFilter()" class="btn apply typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
