import { Component, Inject, OnInit, Optional } from '@angular/core';
import { IListOptions, LIST_OF_CUSTOMER } from '../../constant/assets';
import { FormControl } from '@angular/forms';
import { DestroyService } from 'src/app/core/services';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { IFilterAssetAllocationParam, IRangeFilter } from '../../models/asset';

/**
 * AssetAllocationFilterComponent
 */
@Component({
  selector: 'app-asset-allocation-filter',
  templateUrl: './asset-allocation-filter.component.html',
  styleUrl: './asset-allocation-filter.component.scss',
})
export class AssetAllocationFilterComponent implements OnInit {
  isSelectAllCustomer = true;

  listFilterCustomerOptions: IListOptions[] = [];
  listFilterCustomerStore: IListOptions[] = [];

  searchCustomerControl = new FormControl();

  startNav = new FormControl();
  endNav = new FormControl();

  startProportionOfCash = new FormControl();
  endProportionOfCash = new FormControl();

  startProportionOfStocks = new FormControl();
  endProportionOfStocks = new FormControl();

  startDerivativeProportion = new FormControl();
  endDerivativeProportion = new FormControl();

  startBondProportion = new FormControl();
  endBondProportion = new FormControl();

  startProportionOfFundCertificates = new FormControl();
  endProportionOfFundCertificates = new FormControl();

  /**
   *
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param data
   * @param dialogRefAssetAllocation
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: IFilterAssetAllocationParam,
    public dialogRefAssetAllocation: MatDialogRef<AssetAllocationFilterComponent>
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    const {
      customers,
      nav,
      proportionOfCash,
      proportionOfStocks,
      derivativeProportion,
      bondProportion,
      proportionOfFundCertificates,
    } = data;

    this.updateListAssetAllocation(customers, 'customer');
    this.updateFormControlValueAssetAllocation(nav, this.startNav, this.endNav);
    this.updateFormControlValueAssetAllocation(proportionOfCash, this.startProportionOfCash, this.endProportionOfCash);
    this.updateFormControlValueAssetAllocation(proportionOfStocks, this.startProportionOfStocks, this.endProportionOfStocks);
    this.updateFormControlValueAssetAllocation(derivativeProportion, this.startDerivativeProportion, this.endDerivativeProportion);
    this.updateFormControlValueAssetAllocation(bondProportion, this.startBondProportion, this.endBondProportion);
    this.updateFormControlValueAssetAllocation(
      proportionOfFundCertificates,
      this.startProportionOfFundCertificates,
      this.endProportionOfFundCertificates
    );
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.listFilterCustomerStore = this.listFilterCustomerOptions;
    this.listenSearchControlChangeAssetAllocation()
  }

  private listenSearchControlChangeAssetAllocation() {
    this.searchCustomerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterCustomerOptions = this._filterAssetAllocation(value ?? '', this.listFilterCustomerStore);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * UpdateFormControlValueAssetAllocation
   * @param date date
   * @param startControl startControl
   * @param endControl endControl
   */
  updateFormControlValueAssetAllocation(date: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    const { start, end } = date;
    startControl.patchValue(start);
    endControl.patchValue(end);
  }

  /**
   * updateListAssetAllocation
   * @param data
   * @param type
   */
  updateListAssetAllocation(data: (string | number)[] | null, type: string) {
    const isSelect = (v: string | number) => data === null || data?.includes(v);
    if (type === 'customer') {
      this.listFilterCustomerOptions = LIST_OF_CUSTOMER.map((customer) => ({
        name: customer.name,
        group: customer.group,
        isSelect: isSelect(customer.name),
      }));
      this.isSelectAllCustomer = this.listFilterCustomerOptions.every((t) => t.isSelect === true);
    }
  }

  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param type Kiểu giá trị được thay đổi
   * @param section
   * @param item Item thay đổi
   */
  changeSectionsAssetAllocation(checked: boolean, type: string, section: string, item?: IListOptions) {
    if (type === 'all') {
      this.updateSelectAllAssetAllocation(checked, section);
    } else if (type === 'item' && item) {
      item.isSelect = checked;
      this.updateSelectAllStatusAssetAllocation(section);
    }
  }

  /**
   * Cập nhật trạng thái chọn tất cả
   * @param {boolean} checked
   * @param {string} section
   */
  updateSelectAllAssetAllocation(checked: boolean, section: string) {
    if (section === 'customer') {
      this.isSelectAllCustomer = checked;
      this.listFilterCustomerOptions.forEach((i) => {
        i.isSelect = checked;
      });
    }
  }

  /**
   * Cập nhật trạng thái của SelectAll checkbox
   * @param {string} section
   */
  updateSelectAllStatusAssetAllocation(section: string) {
    if (section === 'customer') {
      this.isSelectAllCustomer = this.listFilterCustomerOptions.every((t) => t.isSelect);
    }
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    const customers = this.listFilterCustomerOptions.filter((t) => t.isSelect).map((t) => t.name);

    const nav = {
      start: this.startNav.value,
      end: this.endNav.value,
    };

    const proportionOfCash = {
      start: this.startProportionOfCash.value,
      end: this.endProportionOfCash.value,
    };

    const proportionOfStocks = {
      start: this.startProportionOfStocks.value,
      end: this.endProportionOfStocks.value,
    };

    const derivativeProportion = {
      start: this.startDerivativeProportion.value,
      end: this.endDerivativeProportion.value,
    };

    const bondProportion = {
      start: this.startBondProportion.value,
      end: this.endBondProportion.value,
    };

    const proportionOfFundCertificates = {
      start: this.startProportionOfFundCertificates.value,
      end: this.endProportionOfFundCertificates.value,
    };

    const isFilter = true;

    const optionFilter = {
      isFilter,
      customers,
      nav,
      proportionOfCash,
      proportionOfStocks,
      derivativeProportion,
      bondProportion,
      proportionOfFundCertificates,
    };

    this.dialogRefAssetAllocation.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRefAssetAllocation.close({
      type: 'default',
    });
  }

  /**
   *
   * @param {string} value
   * @param {IListOptions[]} options
   */
  private _filterAssetAllocation(value: string, options: IListOptions[]): IListOptions[] {
    const filterValueAssetAllocation = value.toString().toLowerCase();

    return options.filter(
      (option) => option.name?.toLowerCase().includes(filterValueAssetAllocation) || option.group?.toLowerCase().includes(filterValueAssetAllocation)
    );
  }
}
