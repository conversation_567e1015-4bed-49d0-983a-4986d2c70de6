<div class="asset-allocation-filter-wrap">
  <div class="asset-allocation-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="asset-allocation-content">
    <!-- LEFT -->
    <div class="content-left">
      <!-- Khách hàng -->
      <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        <div class="search-box">
          <input
            type="text"
            class="input-cls-custom input-style-common typo-body-11 fs-12"
            [placeholder]="'MES-295' | translate"
            [formControl]="searchCustomerControl"
          />
          <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
        </div>
        <div class="option-list-cls">
          <mat-checkbox
            (change)="changeSectionsAssetAllocation($event.checked, 'all', 'customer')"
            [checked]="isSelectAllCustomer"
            class="checkbox-cls"
          >
            {{ 'MES-58' | translate }}</mat-checkbox
          >
          @for (item of listFilterCustomerOptions; let i = $index; track item) {
          <div class="checkbox-cls-item typo-body-15">
            <mat-checkbox
              (change)="changeSectionsAssetAllocation($event.checked, 'item', 'customer', item)"
              [checked]="item.isSelect"
              class="checkbox-cls"
            >
              {{ item.group }} - {{ item.name }}</mat-checkbox
            >
          </div>
          }
        </div>
      </div>

      <!-- Tài sản ròng(NAV) -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-362' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0'"
              [formControl]="startNav"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endNav"
            />
          </div>
        </div>
      </div>

      <!-- Tỷ trọng tiền mặt -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-363' | translate }} Tiền mặt</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0%'"
              [formControl]="startProportionOfCash"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endProportionOfCash"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- RIGHT -->
    <div class="content-right">
      <!-- Tỷ trọng Trái Phiếu -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-363' | translate }} Trái phiếu</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0%'"
              [formControl]="startBondProportion"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endBondProportion"
            />
          </div>
        </div>
      </div>

      <!-- Tỷ trọng Chứng chỉ quỹ -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-363' | translate }} Chứng chỉ quỹ</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0%'"
              [formControl]="startProportionOfFundCertificates"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endProportionOfFundCertificates"
            />
          </div>
        </div>
      </div>
      <!-- Tỷ trọng cổ phiếu -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-363' | translate }} Cổ phiếu</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0%'"
              [formControl]="startProportionOfStocks"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endProportionOfStocks"
            />
          </div>
        </div>
      </div>

      <!-- Tỷ trọng Phái sinh -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-363' | translate }} Phái sinh</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0%'"
              [formControl]="startDerivativeProportion"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endDerivativeProportion"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="footer-filter">
    <div mat-dialog-close (click)="defaultFilter()" class="btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <div mat-dialog-close (click)="applyFilter()" class="btn apply typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
