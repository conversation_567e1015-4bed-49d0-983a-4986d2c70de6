<div class="purchasing-power-filter-wrap">
  <div class="purchasing-power-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>
  <form [formGroup]="purchasingPowerForm" class="purchasing-power-content-wrap">
    <!-- LEFT -->
    <div class="content-left">
      <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResults"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-669'"
          [warningMessage]="'MES-668'"
          [selectedKeys]="data.accountNo"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-295' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          [displayFn]="displayCustomerFilter"
          (invalidSelection)="validateFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResults>
          <div class="typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- Tài khoản ròng -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-279' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startNav')"
          [toControl]="getFormControl('endNav')"
        ></app-from-to>
      </div>

      <!-- Tổng tiền -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-281' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startCash')"
          [toControl]="getFormControl('endCash')"
        ></app-from-to>
      </div>
    </div>
  </form>

  <div class="footer-filter">
    <div mat-dialog-close (click)="defaultFilter()" class="btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <button
      [disabled]="purchasingPowerForm.invalid || isDisableApply"
      (click)="applyFilter()"
      class="btn apply typo-button-3"
    >
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
