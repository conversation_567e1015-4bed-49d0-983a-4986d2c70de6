import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { IListOptions } from '../../constant/assets';
import { IFilterPurchasingPowerInfoParam, IRangeFilter } from '../../models/asset';
import { DestroyService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { takeUntil, tap } from 'rxjs';
import { selectAllAccountNumberListByBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { Store } from '@ngrx/store';
import { validFromToValidator } from 'src/app/shared/validators/form';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';
import { transformToNumber } from 'src/app/shared/utils/format-numbet';

/**
 * PurchasingPowerFilterComponent
 */
@Component({
  selector: 'app-purchasing-power-filter',
  templateUrl: './purchasing-power-filter.component.html',
  styleUrl: './purchasing-power-filter.component.scss',
})
export class PurchasingPowerFilterComponent implements OnInit {
  @ViewChild(VirtualScrollListComponent) virtualScroll!: VirtualScrollListComponent;

  purchasingPowerForm!: FormGroup;

  // Key identifier for customer objects
  readonly customerKey = 'accountNumber';

  customers: IAllAccountNumber[] = [];

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  isDisableApply = false;
  /**
   *  Constructor
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param data
   * @param dialogRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: IFilterPurchasingPowerInfoParam,
    public dialogRef: MatDialogRef<PurchasingPowerFilterComponent>,
    private readonly store: Store,
    private readonly fb: FormBuilder
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.initForm();
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.loadCustomerList();
    this.patchValueToForm();
  }

  /**
   * initForm
   */
  private initForm() {
    this.purchasingPowerForm = this.fb.group(
      {
        // Tài khoản ròng
        startNav: new FormControl(),
        endNav: new FormControl(),
        // Tổng tiền
        startCash: new FormControl(),
        endCash: new FormControl(),
        // Sức mua
        startPurchasingPower: new FormControl(),
        endPurchasingPower: new FormControl(),
        // Tiền bảo lãnh
        startGuarantee: new FormControl(),
        endGuarantee: new FormControl(),
        // Tiền bảo lãnh đã SD
        startUsedGuarantee: new FormControl(),
        endUsedGuarantee: new FormControl(),
        // Tiền bảo lãnh cần nộp
        startToSubmitGuarantee: new FormControl(),
        endToSubmitGuarantee: new FormControl(),
      },
      {
        validator: [
          validFromToValidator('startNav', 'endNav'),
          validFromToValidator('startCash', 'endCash'),
          validFromToValidator('startPurchasingPower', 'endPurchasingPower'),
          validFromToValidator('startGuarantee', 'endGuarantee'),
          validFromToValidator('startUsedGuarantee', 'endUsedGuarantee'),
          validFromToValidator('startToSubmitGuarantee', 'endToSubmitGuarantee'),
        ],
      }
    );
  }

  private loadCustomerList() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(
        takeUntil(this._destroy),
        tap((customerList) => {
          this.customers = customerList;
        })
      )
      .subscribe();
  }

  private patchValueToForm() {
    const { navValue, cashValue, purchasingPower, guarantee, usedGuarantee, toSubmitGuarantee } = this.data;

    this.purchasingPowerForm.patchValue({
      startNav: navValue?.from,
      endNav: navValue?.to,
      startCash: cashValue?.from,
      endCash: cashValue?.to,
      startPurchasingPower: purchasingPower?.from,
      endPurchasingPower: purchasingPower?.to,
      startGuarantee: guarantee?.from,
      endGuarantee: guarantee?.to,
      startUsedGuarantee: usedGuarantee?.from,
      endUsedGuarantee: usedGuarantee?.to,
      startToSubmitGuarantee: toSubmitGuarantee?.from,
      endToSubmitGuarantee: toSubmitGuarantee?.to,
    });
  }

  getFormControl(field: string) {
    return this.purchasingPowerForm.get(field) as FormControl;
  }

  /**
   *
   * @param {string} value
   * @param {IListOptions} options
   */
  private _filter(value: string, options: IListOptions[]): IListOptions[] {
    const filterValue = value.toString().toLowerCase();

    return options.filter(
      (option) => option.name?.toLowerCase().includes(filterValue) || option.group?.toLowerCase().includes(filterValue)
    );
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    if (this.isDisableApply) return;

    const accountNo = (this.virtualScroll.getChecked() as IAllAccountNumber[]).map((c) => c.accountNumber);

    const {
      startNav,
      endNav,
      startCash,
      endCash,
      // startPurchasingPower,
      // endPurchasingPower,
      // startGuarantee,
      // endGuarantee,
      // startUsedGuarantee,
      // endUsedGuarantee,
      // startToSubmitGuarantee,
      // endToSubmitGuarantee,
    } = this.purchasingPowerForm.value;

    const navValue = {
      from: transformToNumber(startNav),
      to: transformToNumber(endNav),
    };

    const cashValue = {
      from: transformToNumber(startCash),
      to: transformToNumber(endCash),
    };

    const isFilter = this.checkStatusFilter(
      accountNo,
      navValue,
      cashValue
      // guarantee,
      // usedGuarantee,
      // toSubmitGuarantee
    );

    const optionFilter = {
      accountNo,
      navValue,
      cashValue,
      isFilter,
      // purchasingPower,
      // guarantee,
      // usedGuarantee,
      // toSubmitGuarantee,
    };

    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * CheckStatusFilter
   * @param status
   * @param rangeDateHold
   * @param rangePotential
   * @param rangeNotRecord
   * @param rangeRecord
   * @param date
   * @returns {boolean} true / false
   */
  checkStatusFilter(
    customers: null | (string | undefined)[],
    nav: IRangeFilter,
    cash: IRangeFilter
    // numberValue: IRangeFilter,
    // guarantee: IRangeFilter,
    // usedGuarantee: IRangeFilter,
    // toSubmitGuarantee: IRangeFilter
  ) {
    const isInitialCustomerSelect = customers?.length === 0;
    return (
      !isInitialCustomerSelect || this.checkHasValueInObject(nav) || this.checkHasValueInObject(cash)
      // this.checkHasValueInObject(numberValue) ||
      // this.checkHasValueInObject(guarantee) ||
      // this.checkHasValueInObject(usedGuarantee) ||
      // this.checkHasValueInObject(toSubmitGuarantee)
    );
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObject(data: any) {
    return !!data && ((data.from != null && data.from !== '') || (data.to != null && data.to !== ''));
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customer: IAllAccountNumber): string {
    if (!customer) return '';
    return `${customer.accountNumber} - ${customer.customerName}`;
  }
}
