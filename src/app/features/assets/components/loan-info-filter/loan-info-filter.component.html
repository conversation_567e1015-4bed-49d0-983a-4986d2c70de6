<div class="loan-info-filter-wrap">
  <div class="loan-info-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="loan-info-content">
    <!-- LEFT -->
    <div class="content-left">
      <!-- Ngày đáo hạn -->
      <div class="title-left typo-body-15">{{ 'MES-365' | translate }}</div>
      <div class="box-calendar">
        <!-- Từ ngày -->
        <div class="content-from">
          <div class="from-label typo-body-12">{{ 'MES-209' | translate }} ngày</div>
          <div class="input-wrapper">
            <input
              type="text"
              class="fs-12 calendar-input typo-body-12"
              matInput
              [matDatepicker]="dateDueFrom"
              [placeholder]="'DD/MM/YYYY'"
              [formControl]="dateDueStartControl"
              [max]="dateDueEndControl.value ? dateDueEndControl.value : null"
            />
            <img src="./assets/icons/calendar.svg" alt="" (click)="dateDueFrom.open()" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              #dateDueFrom
              panelClass="calendar-cls"
            ></mat-datepicker>
          </div>
        </div>

        <!-- Tới ngày -->
        <div class="content-to">
          <div class="to-label typo-body-12">{{ 'MES-210' | translate }} ngày</div>
          <div class="input-wrapper">
            <input
              type="text"
              class="fs-12 calendar-input typo-body-12"
              matInput
              [matDatepicker]="dateDueTo"
              [placeholder]="'DD/MM/YYYY'"
              [formControl]="dateDueEndControl"
              [min]="dateDueStartControl.value ? dateDueStartControl.value : null"
            />
            <img src="./assets/icons/calendar.svg" alt="" (click)="dateDueTo.open()" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              #dateDueTo
              panelClass="calendar-cls"
            ></mat-datepicker>
          </div>
        </div>
      </div>

      <!-- Tình trạng khoản vay -->
      <div class="title-left typo-body-15">{{ 'MES-368' | translate }}</div>
      <div class="option-list-cls">
        <mat-checkbox
          (change)="changeSectionsLIFiler($event.checked, 'all', 'loanStatus')"
          [checked]="isSelectAllLoanStatus"
          class="checkbox-cls"
        >
          {{ 'MES-58' | translate }}</mat-checkbox
        >
        @for (item of listFilterLoanStatusOptions; let i = $index; track item) {
        <div class="checkbox-cls-item typo-body-15">
          <mat-checkbox
            (change)="changeSectionsLIFiler($event.checked, 'item', 'loanStatus', item)"
            [checked]="item.isSelect"
            class="checkbox-cls"
          >
            {{ item.label }}</mat-checkbox
          >
        </div>
        }
      </div>

      <!-- Dư nợ ban đầu -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-55' | translate }} ban đầu</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startOpeningDebt"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endOpeningDebt"
            />
          </div>
        </div>
      </div>

      <!-- Dư nợ còn lại -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-55' | translate }} còn lại</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startRemainDebt"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endRemainDebt"
            />
          </div>
        </div>
      </div>

      <!-- Dư nợ đã trả -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-55' | translate }} đã trả</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startPaidDebt"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endPaidDebt"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- RIGHT -->
    <div class="content-right">
      <!-- Khách hàng -->
      <div class="title-right typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        <div class="search-box">
          <input
            type="text"
            class="input-cls-custom input-style-common typo-body-11 fs-12"
            [placeholder]="'MES-295' | translate"
            [formControl]="searchCustomerControl"
          />
          <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
        </div>
        <div class="option-list-cls">
          <mat-checkbox
            (change)="changeSectionsLIFiler($event.checked, 'all', 'customer')"
            [checked]="isSelectAllCustomer"
            class="checkbox-cls"
          >
            {{ 'MES-58' | translate }}</mat-checkbox
          >
          @for (item of listFilterCustomerOptions; let i = $index; track item) {
          <div class="checkbox-cls-item typo-body-15">
            <mat-checkbox
              (change)="changeSectionsLIFiler($event.checked, 'item', 'customer', item)"
              [checked]="item.isSelect"
              class="checkbox-cls"
            >
              {{ item.group }} - {{ item.name }}</mat-checkbox
            >
          </div>
          }
        </div>
      </div>

      <!-- Ngày vay -->
      <div class="title-right typo-body-15">{{ 'MES-366' | translate }}</div>
      <div class="box-calendar">
        <!-- Từ ngày -->
        <div class="content-from">
          <div class="from-label typo-body-12">{{ 'MES-209' | translate }} ngày</div>
          <div class="input-wrapper">
            <input
              type="text"
              class="fs-12 calendar-input typo-body-12"
              matInput
              [matDatepicker]="loanDateFrom"
              [placeholder]="'DD/MM/YYYY'"
              [formControl]="loanDateStartControl"
              [max]="loanDateEndControl.value ? loanDateEndControl.value : null"
            />
            <img src="./assets/icons/calendar.svg" alt="" (click)="loanDateFrom.open()" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              #loanDateFrom
              panelClass="calendar-cls"
            ></mat-datepicker>
          </div>
        </div>

        <!-- Tới ngày -->
        <div class="content-to">
          <div class="to-label typo-body-12">{{ 'MES-210' | translate }} ngày</div>
          <div class="input-wrapper">
            <input
              type="text"
              class="fs-12 calendar-input typo-body-12"
              matInput
              [matDatepicker]="loanDateTo"
              [placeholder]="'DD/MM/YYYY'"
              [formControl]="loanDateEndControl"
              [min]="loanDateStartControl.value ? loanDateStartControl.value : null"
            />
            <img src="./assets/icons/calendar.svg" alt="" (click)="loanDateTo.open()" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              #loanDateTo
              panelClass="calendar-cls"
            ></mat-datepicker>
          </div>
        </div>
      </div>
      <!-- Phí vay tạm tính -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-453' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startTempLoanFee"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endTempLoanFee"
            />
          </div>
        </div>
      </div>

      <!--Lãi vay tạm tính -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-364' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'0'"
              [formControl]="startTempInterest"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endTempInterest"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="footer-filter">
    <div mat-dialog-close (click)="defaultFilterLIFilter()" class="btn default typo-button-3">
      {{ 'MES-20' | translate }}
    </div>
    <div mat-dialog-close (click)="applyFilter()" class="btn apply typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
