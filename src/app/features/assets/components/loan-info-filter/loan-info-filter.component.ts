import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MY_DATE_FORMAT } from 'src/app/shared/constants/date-picker';
import { ICustomers, IListOptions } from '../../constant/assets';
import { FormControl } from '@angular/forms';
import { DestroyService } from 'src/app/core/services';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { IRangeFilter } from '../../models/asset';
import { DatePickerNavigationFullDateComponent } from 'src/app/shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';

export const LOAN_STATUS = [
  {
    label: 'Trong hạn',
    value: 0,
  },
  {
    label: 'Quá hạn',
    value: 1,
  },
];

/**
 * LoanInfoFilterComponent
 */
@Component({
  selector: 'app-loan-info-filter',
  templateUrl: './loan-info-filter.component.html',
  styleUrl: './loan-info-filter.component.scss',
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE],
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
  ],
})
export class LoanInfoFilterComponent implements OnInit {
  isSelectAllCustomer = true;
  isSelectAllLoanStatus = true;

  headerCalendar = DatePickerNavigationFullDateComponent;
  listFilterLoanStatusOptions: IListOptions[] = [];

  dateDueStartControl = new FormControl();

  dateDueEndControl = new FormControl();

  listFilterCustomerOptions: IListOptions[] = [];
  listFilterCustomerStore: IListOptions[] = [];

  searchCustomerControl = new FormControl();

  startOpeningDebt = new FormControl();
  endOpeningDebt = new FormControl();

  startRemainDebt = new FormControl();
  endRemainDebt = new FormControl();

  startPaidDebt = new FormControl();
  endPaidDebt = new FormControl();

  startTempInterest = new FormControl();
  endTempInterest = new FormControl();

  startTempLoanFee = new FormControl();
  endTempLoanFee = new FormControl();

  loanDateStartControl = new FormControl();

  loanDateEndControl = new FormControl();

  customers: ICustomers[] = [];

  isInLoanDate = false;
  isOutLoanDate = false;
  /**
   *
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param data
   * @param dialogRefLIFilter
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRefLIFilter: MatDialogRef<LoanInfoFilterComponent>
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
    const { customers, filterOptions } = data;
    const { customersSelect, loanStatus, openingDebt, paidDebt, remainDebt, tempLoanFee, tempInterest } = filterOptions;
    this.customers = customers;
    this.updateListLoanInfoFilter(customersSelect, 'customer');
    this.updateListLoanInfoFilter(loanStatus, 'loanStatus');
    this.updateFormControlValueLoanInfFilter(openingDebt, this.startOpeningDebt, this.endOpeningDebt);
    this.updateFormControlValueLoanInfFilter(remainDebt, this.startRemainDebt, this.endRemainDebt);
    this.updateFormControlValueLoanInfFilter(paidDebt, this.startPaidDebt, this.endPaidDebt);
    this.updateFormControlValueLoanInfFilter(tempLoanFee, this.startTempLoanFee, this.endTempLoanFee);
    this.updateFormControlValueLoanInfFilter(tempInterest, this.startTempInterest, this.endTempInterest);
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.listFilterCustomerStore = this.listFilterCustomerOptions;
    this.listenSearchChangeLoanInfoFilter()
  }

  private listenSearchChangeLoanInfoFilter() {
    this.searchCustomerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterCustomerOptions = this._filterLIFilter(value ?? '', this.listFilterCustomerStore);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * UpdateFormControlValueLoanInfFilter
   * @param date date
   * @param startControl startControl
   * @param endControl endControl
   */
  updateFormControlValueLoanInfFilter(date: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    const { start, end } = date;
    startControl.patchValue(start);
    endControl.patchValue(end);
  }

  /**
   * updateListLoanInfoFilter
   * @param data
   * @param type
   */
  updateListLoanInfoFilter(data: (string | number)[] | null, type: string) {
    const isSelect = (v: string | number) => data === null || data?.includes(v);
    switch (type) {
      case 'customer':
        this.listFilterCustomerOptions = this.customers.map((customer) => ({
          name: customer.name,
          group: customer.group,
          isSelect: isSelect(customer.name),
        }));
        this.isSelectAllCustomer = this.listFilterCustomerOptions.every((t) => t.isSelect === true);
        break;

      case 'loanStatus':
        this.listFilterLoanStatusOptions = LOAN_STATUS.map((loan) => ({
          label: loan.label,
          value: loan.value,
          isSelect: isSelect(loan.value),
        }));
        this.isSelectAllLoanStatus = this.listFilterLoanStatusOptions.every((t) => t.isSelect === true);
        break;
    }
  }

  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param type Kiểu giá trị được thay đổi
   * @param section
   * @param item Item thay đổi
   */
  changeSectionsLIFiler(checked: boolean, type: string, section: string, item?: IListOptions) {
    if (type === 'all') {
      this.updateSelectAllLIFilter(checked, section);
    } else if (type === 'item' && item) {
      item.isSelect = checked;
      this.updateSelectAllStatusLIFilter(section);
    }
  }

  /**
   * Update margin status
   * @param {number[] | null } status
   */
  updateMarginStatus(status: number[] | null) {
    if (status === null) {
      this.isInLoanDate = true;
      this.isOutLoanDate = true;
    } else {
      status.forEach((t) => {
        if (t === 0) {
          this.isInLoanDate = true;
        } else if (t === 1) {
          this.isOutLoanDate = true;
        }
      });
    }
  }

  /**
   * Cập nhật trạng thái chọn tất cả
   * @param {boolean} checked
   * @param {string} section
   */
  updateSelectAllLIFilter(checked: boolean, section: string) {
    switch (section) {
      case 'customer':
        this.isSelectAllCustomer = checked;
        this.listFilterCustomerOptions.forEach((i) => {
          i.isSelect = checked;
        });
        break;

      case 'loanStatus':
        this.isSelectAllLoanStatus = checked;
        this.listFilterLoanStatusOptions.forEach((i) => {
          i.isSelect = checked;
        });
        break;
    }
  }

  /**
   * Cập nhật trạng thái của SelectAll checkbox
   * @param {string} section
   */
  updateSelectAllStatusLIFilter(section: string) {
    switch (section) {
      case 'customer':
        this.isSelectAllCustomer = this.listFilterCustomerOptions.every((t) => t.isSelect);
        break;
      case 'loanStatus':
        this.isSelectAllLoanStatus = this.listFilterLoanStatusOptions.every((t) => t.isSelect);
        break;
    }
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    const customersSelect = this.listFilterCustomerOptions.filter((t) => t.isSelect).map((t) => t.name);

    const loanStatus = this.listFilterLoanStatusOptions.filter((t) => t.isSelect).map((t) => t.value);

    const dueDate = {
      start: this.dateDueStartControl.value._d ?? this.dateDueStartControl.value,
      end: this.dateDueEndControl.value._d ?? this.dateDueEndControl.value,
    };
    const loanDate = {
      start: this.loanDateStartControl.value._d ?? this.loanDateStartControl.value,
      end: this.loanDateEndControl.value._d ?? this.loanDateEndControl.value,
    };

    const openingDebt = {
      start: this.startOpeningDebt.value,
      end: this.endOpeningDebt.value,
    };

    const remainDebt = {
      start: this.startRemainDebt.value,
      end: this.endRemainDebt.value,
    };

    const paidDebt = {
      start: this.startPaidDebt.value,
      end: this.endPaidDebt.value,
    };

    const tempInterest = {
      start: this.startTempInterest.value,
      end: this.endTempInterest.value,
    };

    const tempLoanFee = {
      start: this.startTempLoanFee.value,
      end: this.endTempLoanFee.value,
    };

    const isFilter = true;

    const optionFilter = {
      isFilter,
      dueDate,
      loanDate,
      customersSelect,
      loanStatus,
      openingDebt,
      remainDebt,
      paidDebt,
      tempLoanFee,
      tempInterest,
    };

    this.dialogRefLIFilter.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * DefaultFilter
   */
  defaultFilterLIFilter() {
    this.dialogRefLIFilter.close({
      type: 'default',
    });
  }

  /**
   *
   * @param {string} value
   * @param {IListOptions[]} options
   */
  private _filterLIFilter(value: string, options: IListOptions[]): IListOptions[] {
    const filterValue = value.toString().toLowerCase();

    return options.filter(
      (option) => option.name?.toLowerCase().includes(filterValue) || option.group?.toLowerCase().includes(filterValue)
    );
  }
}
