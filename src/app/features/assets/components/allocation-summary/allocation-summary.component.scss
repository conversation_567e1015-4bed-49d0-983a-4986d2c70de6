.section {
  border-radius: 8px;
  padding: 16px;
  background: var(--color--background--1);

  .content {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: space-between;

    &.flex-col {
      flex-direction: column;
    }

    .sub-title {
      font-size: 12px;
      color: var(--color--text--subdued);
      margin-bottom: 10px;
    }

    .asset-amount {
      display: flex;
      gap: 8px;
      align-items: center;
      flex-wrap: wrap;
    }

    .badge {
      display: inline-flex;
      padding: 4px 12px;
      justify-content: center;
      align-items: center;
      border-radius: 16px;
      background: var(--MacOS-Accents-Yellow---Dark, #ffd60a);

      &.allocated-from {
        font-weight: 400;
        width: 140px;
      }

      &.allocated-to {
        font-weight: 400;
        width: 140px;
      }

      &.cash {
        background: #b1e1f8;
      }

      &.stock {
        background: #bae7a3;
      }

      &.bond {
        background: #fecaca;
      }

      &.derivative {
        background: #fde68a;
      }

      &.certificate {
        background: #fac698;
      }
    }

    .widget {
      position: relative;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .from,
      .to,
      mat-icon {
        flex: 1;
      }

      .from,
      .to {
        min-width: 160.02px;
      }

      .badge {
        min-width: 135px;
      }

      .amount {
        margin-top: 6px;
        white-space: nowrap;
      }
    }
  }
}

mat-form-field {
  height: 20px;
}
