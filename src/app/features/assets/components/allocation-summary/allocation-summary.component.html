<div class="section summary" *ngIf="allocatedSource">
  <div class="content flex-col">
    <!-- Thay đổi của loại tài sản -->
    <div class="widget">
      <div class="from">
        <div class="sub-title typo-body-12">{{ 'MES-83' | translate }}</div>
        <div class="badge allocated-from typo-body-12" [ngClass]="CONVERT_ASSET_TYOE_TO_CLASS[allocatedSource.type]">
          {{ CONVERT_ASSET_TYPE_TO_LABEL[allocatedSource.type] | translate | uppercase }}
        </div>
      </div>

      <ng-container *ngIf="allocatedTarget">
        <mat-icon svgIcon="icon:from-to"></mat-icon>

        <!-- chỉ hiển thị khi ô "Phân bổ tới" có giá trị -->
        <div class="to">
          <div class="sub-title typo-body-12">{{ 'MES-80' | translate }}</div>
          <div class="badge allocated-to typo-body-12" [ngClass]="CONVERT_ASSET_TYOE_TO_CLASS[allocatedTarget.type]">
            {{ CONVERT_ASSET_TYPE_TO_LABEL[allocatedTarget.type] | translate | uppercase }}
          </div>
        </div>
      </ng-container>
    </div>

    <!-- Thay đổi tỉ trọng của tài sản gốc -->
    <div class="widget">
      <div class="from">
        <div class="sub-title typo-body-12">
          {{ 'MES-84' | translate : { type: CONVERT_ASSET_TYPE_TO_LABEL[allocatedSource.type] | translate } }}
        </div>

        <ng-container *ngIf="!!allocatedSource.oldValue">
          <div class="badge typo-body-12" *ngIf="!!allocatedSource.oldValue">
            {{ allocatedSource.oldValue.percentage | numberFormat : 'percent' }} {{ 'MES-86' | translate }}
          </div>

          <div class="amount typo-body-12">≈ {{ allocatedSource.oldValue.amount | numberFormat }} VND</div>
        </ng-container>
      </div>

      <ng-container *ngIf="allocatedSource.newValue">
        <mat-icon svgIcon="icon:from-to"></mat-icon>

        <!-- chỉ hiển thị khi ô "Số tiền phân bổ" có giá trị -->
        <div class="to">
          <div class="sub-title typo-body-12 hidden">
            {{ 'MES-84' | translate : { type: CONVERT_ASSET_TYPE_TO_LABEL[allocatedSource.type] | translate } }}
          </div>

          <ng-container *ngIf="!!allocatedSource.newValue">
            <div class="badge typo-body-12" *ngIf="!!allocatedSource.newValue">
              {{ allocatedSource.newValue.percentage | numberFormat : 'percent' }} {{ 'MES-86' | translate }}
            </div>

            <div class="amount typo-body-12">≈ {{ allocatedSource.newValue.amount | numberFormat }} VND</div>
          </ng-container>
        </div>
      </ng-container>
    </div>

    <!-- Thay đổi tỉ trọng của tài sản đích -->
    <div class="widget">
      <!-- chỉ hiển thị khi ô "Phân bổ tới" có giá trị  -->
      <div class="from" *ngIf="allocatedTarget">
        <div class="sub-title typo-body-12">
          {{ 'MES-84' | translate : { type: CONVERT_ASSET_TYPE_TO_LABEL[allocatedTarget.type] | translate } }}
        </div>

        <ng-container *ngIf="!!allocatedTarget.oldValue">
          <div class="badge typo-body-12" *ngIf="!!allocatedTarget.oldValue">
            {{ allocatedTarget.oldValue.percentage | numberFormat : 'percent' }} {{ 'MES-86' | translate }}
          </div>

          <div class="amount typo-body-12">≈ {{ allocatedTarget.oldValue.amount | numberFormat }} VND</div>
        </ng-container>
      </div>

      <ng-container *ngIf="allocatedTarget && allocatedTarget.newValue">
        <mat-icon svgIcon="icon:from-to"></mat-icon>

        <!-- chỉ hiển thị khi ô "Số tiền phân bổ" & "Phân bổ tới" có giá trị -->
        <div class="to">
          <div class="sub-title typo-body-12 hidden">
            {{ 'MES-84' | translate : { type: CONVERT_ASSET_TYPE_TO_LABEL[allocatedTarget.type] | translate } }}
          </div>

          <ng-container *ngIf="!!allocatedTarget.newValue">
            <div class="badge typo-body-12" *ngIf="!!allocatedTarget.newValue">
              {{ allocatedTarget.newValue.percentage | numberFormat : 'percent' }} {{ 'MES-86' | translate }}
            </div>

            <div class="amount typo-body-12">≈ {{ allocatedTarget.newValue.amount | numberFormat }} VND</div>
          </ng-container>
        </div>
      </ng-container>
    </div>
  </div>
</div>
