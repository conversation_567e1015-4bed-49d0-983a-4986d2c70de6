import { Component, Input } from '@angular/core';
import { CONVERT_ASSET_TYPE_TO_CLASS, CONVERT_ASSET_TYPE_TO_LABEL, IAllocationRef } from '../../constant/assets';

/**
 * Declare AllocationSummaryComponent
 */
@Component({
  selector: 'app-allocation-summary',
  templateUrl: './allocation-summary.component.html',
  styleUrl: './allocation-summary.component.scss',
})
export class AllocationSummaryComponent {
  @Input() allocatedSource: IAllocationRef | null = null;
  @Input() allocatedTarget: IAllocationRef | null = null;

  CONVERT_ASSET_TYPE_TO_LABEL = CONVERT_ASSET_TYPE_TO_LABEL;
  CONVERT_ASSET_TYOE_TO_CLASS = CONVERT_ASSET_TYPE_TO_CLASS;
}
