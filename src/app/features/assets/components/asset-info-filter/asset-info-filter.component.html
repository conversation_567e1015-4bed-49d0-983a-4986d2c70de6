<div class="asset-info-filter-wrap">
  <div class="asset-info-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <form [formGroup]="assetInfoFilterForm" class="asset-info-content">
    <!-- LEFT -->
    <div class="content-left">
      <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResults"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [selectedKeys]="data.accountNo"
          [messageSelectionComplete]="'MES-669'"
          [warningMessage]="'MES-668'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-295' | translate"
          [displayFn]="displayCustomerFilter"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          (invalidSelection)="validateFilterValue($event)"
          class="asset-info-cls"
        ></app-virtual-scroll-list>

        <ng-template #noResults>
          <div class="typo-body-15 empty-message-global asset-info-cls">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- Tài sản ròng (NAV) -->
      <div class="from-to">
        <div class="title-left typo-body-15 asset-info-cls">{{ 'MES-279' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [toControl]="getFormControl('endNav')"
          [fromControl]="getFormControl('startNav')"
        ></app-from-to>
      </div>

      <!-- Tổng giá trị CK -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-280' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startStock')"
          [toControl]="getFormControl('endStock')"
        ></app-from-to>
      </div>
    </div>

    <!-- RIGHT -->
    <div class="content-right">
      <!-- Tổng tiền -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-281' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startCash')"
          [toControl]="getFormControl('endCash')"
        ></app-from-to>
      </div>

      <!-- Tổng dư nợ -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-242' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startDebt')"
          [toControl]="getFormControl('endDebt')"
        ></app-from-to>
      </div>

      <!-- Tý lệ ký quỹ(%) -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-282' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'percent.2'"
          decimalMarker="."
          [suffix]="' %'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0%'"
          [placeholderTo]="'100%'"
          [fromControl]="getFormControl('startMarginRate')"
          [toControl]="getFormControl('endMarginRate')"
        ></app-from-to>
      </div>

      <!-- Tổng tài sản -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-283' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="false"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startAsset')"
          [toControl]="getFormControl('endAsset')"
        ></app-from-to>
      </div>
    </div>
  </form>

  <div class="footer-filter">
    <div mat-dialog-close (click)="defaultFilter()" class="btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <button
      [disabled]="assetInfoFilterForm.invalid || isDisableApply"
      (click)="applyFilter()"
      class="btn apply typo-button-3"
    >
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
