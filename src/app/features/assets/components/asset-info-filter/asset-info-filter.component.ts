import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { take, tap } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { IFilterAssetInfoParam, IRangeFilter } from '../../models/asset';
import { Store } from '@ngrx/store';
import { selectAllAccountNumberListByBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { validFromToSuffixOrPrefixValidator, validFromToValidator } from 'src/app/shared/validators/form';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';
import { transformToNumber } from 'src/app/shared/utils/format-numbet';

/**
 * Thông tin tài sản
 */
@Component({
  selector: 'app-asset-info-filter',
  templateUrl: './asset-info-filter.component.html',
  styleUrl: './asset-info-filter.component.scss',
  providers: [DestroyService],
})
export class AssetInfoFilterComponent implements OnInit {
  @ViewChild(VirtualScrollListComponent) virtualScroll!: VirtualScrollListComponent;

  assetInfoFilterForm!: FormGroup;

  // Key identifier for customer objects
  readonly customerKey = 'accountNumber';

  customers: IAllAccountNumber[] = [];

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  isDisableApply = false;

  /**
   *
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param data
   * @param dialogRef
   * @param store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: IFilterAssetInfoParam,
    public dialogRef: MatDialogRef<AssetInfoFilterComponent>,
    private readonly store: Store,
    private readonly fb: FormBuilder
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.initForm();
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.loadCustomerList();
    this.patchValueToForm();
  }

  /**
   * initForm
   */
  private initForm() {
    this.assetInfoFilterForm = this.fb.group(
      {
        // Sức mua
        startPurchasingPower: new FormControl(),
        endPurchasingPower: new FormControl(),
        // NAV
        startNav: new FormControl(),
        endNav: new FormControl(),
        // Tổng giá trị CK
        startStock: new FormControl(),
        endStock: new FormControl(),
        // Tổng tiền
        startCash: new FormControl(),
        endCash: new FormControl(),
        // Tổng dư nợ
        startDebt: new FormControl(),
        endDebt: new FormControl(),
        // Tý lệ ký quỹ
        startMarginRate: new FormControl(),
        endMarginRate: new FormControl(),
        // Tổng tài sản
        startAsset: new FormControl(),
        endAsset: new FormControl(),
        // +/- Tổng giá trị CK
        startMarginStock: new FormControl(),
        endMarginStock: new FormControl(),
      },
      {
        validator: [
          validFromToValidator('startPurchasingPower', 'endPurchasingPower'),
          validFromToValidator('startNav', 'endNav'),
          validFromToValidator('startStock', 'endStock'),
          validFromToValidator('startCash', 'endCash'),
          validFromToValidator('startDebt', 'endDebt'),
          validFromToSuffixOrPrefixValidator('startMarginRate', 'endMarginRate'),
          validFromToValidator('startAsset', 'endAsset'),
          validFromToValidator('startMarginStock', 'endMarginStock'),
        ],
      }
    );
  }

  private patchValueToForm() {
    const {
      // purchasingPower,
      navValue,
      stockValue,
      marginStockValue,
      cashValue,
      debtValue,
      marginRateValue,
      assetsValue,
    } = this.data;
    this.assetInfoFilterForm.patchValue({
      // startPurchasingPower: purchasingPower.start,
      // endPurchasingPower: purchasingPower.end,
      startNav: navValue?.from,
      endNav: navValue?.to,
      startStock: stockValue?.from,
      endStock: stockValue?.to,
      startMarginStock: marginStockValue?.from,
      endMarginStock: marginStockValue?.to,
      startCash: cashValue?.from,
      endCash: cashValue?.to,
      startDebt: debtValue?.from,
      endDebt: debtValue?.to,
      startMarginRate: marginRateValue?.from ? +marginRateValue.from * 100 : marginRateValue?.from,
      endMarginRate: marginRateValue?.to ? +marginRateValue.to * 100 : marginRateValue?.to,
      startAsset: assetsValue?.from,
      endAsset: assetsValue?.to,
    });
  }

  private loadCustomerList() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(
        take(1),
        tap((customers) => {
          this.customers = customers;
        })
      )
      .subscribe();
  }

  getFormControl(field: string) {
    return this.assetInfoFilterForm.get(field) as FormControl;
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    if (this.isDisableApply) return;
    const accountNo = (this.virtualScroll.getChecked() as IAllAccountNumber[]).map((c) => c.accountNumber);
    const {
      // startPurchasingPower,
      // endPurchasingPower,
      startNav,
      endNav,
      startStock,
      endStock,
      startCash,
      endCash,
      startDebt,
      endDebt,
      startMarginRate,
      endMarginRate,
      startAsset,
      endAsset,
      startMarginStock,
      endMarginStock,
    } = this.assetInfoFilterForm.value;

    const trimPercentage = (value: string | null | undefined): number | null => {
      if (value == null) return null;
      const number = parseFloat(value.replace(/[^0-9,.]/g, '').replace(',', '.'));
      return isNaN(number) ? null : number / 100;
    };

    const navValue = {
      from: transformToNumber(startNav) ?? null,
      to: transformToNumber(endNav) ?? null,
    };

    const stockValue = {
      from: transformToNumber(startStock) ?? null,
      to: transformToNumber(endStock) ?? null,
    };

    const marginStockValue = {
      from: transformToNumber(startMarginStock) ?? null,
      to: transformToNumber(endMarginStock) ?? null,
    };

    const cashValue = {
      from: transformToNumber(startCash) ?? null,
      to: transformToNumber(endCash) ?? null,
    };

    const debtValue = {
      from: transformToNumber(startDebt) ?? null,
      to: transformToNumber(endDebt) ?? null,
    };

    const marginRateValue = {
      from: trimPercentage(startMarginRate) ?? null,
      to: trimPercentage(endMarginRate) ?? null,
    };

    const assetsValue = {
      from: transformToNumber(startAsset) ?? null,
      to: transformToNumber(endAsset) ?? null,
    };

    const fieldFilter = {
      customers: accountNo,
      nav: navValue,
      stock: stockValue,
      marginStock: marginStockValue,
      cash: cashValue,
      debt: debtValue,
      marginRate: marginRateValue,
      assets: assetsValue,
    };

    const isFilter = this.checkStatusFilter(fieldFilter);

    this.dialogRef.close({
      type: 'save',
      optionFilter: {
        isFilter,
        accountNo,
        // purchasingPower,
        navValue,
        stockValue,
        marginStockValue,
        cashValue,
        debtValue,
        marginRateValue,
        assetsValue,
      },
    });
  }

  /**
   * CheckStatusFilter
   * @param status
   * @param rangeDateHold
   * @param rangePotential
   * @param rangeNotRecord
   * @param rangeRecord
   * @param date
   * @returns {boolean} true / false
   */
  checkStatusFilter(fieldFilter: {
    customers: null | (string | undefined)[];
    nav: IRangeFilter;
    stock: IRangeFilter;
    marginStock: IRangeFilter;
    cash: IRangeFilter;
    debt: IRangeFilter;
    marginRate: IRangeFilter;
    assets: IRangeFilter;
  }) {
    const { customers, nav, stock, marginStock, cash, debt, marginRate, assets } = fieldFilter;
    const isInitialCustomerSelect = (customers ?? [])?.length === 0;
    return (
      !isInitialCustomerSelect ||
      this.checkHasValueInObject(nav) ||
      this.checkHasValueInObject(stock) ||
      this.checkHasValueInObject(marginStock) ||
      this.checkHasValueInObject(cash) ||
      this.checkHasValueInObject(marginRate) ||
      this.checkHasValueInObject(assets) ||
      this.checkHasValueInObject(debt)
    );
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObject(data: any) {
    return (!!data && data.from != null && data.from !== '') || (data.to != null && data.to !== '');
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customer: IAllAccountNumber): string {
    if (!customer) return '';
    return `${customer.accountNumber} - ${customer.customerName}`;
  }
}
