<div class="investment-portfolio-filter-wrap">
  <div class="investment-portfolio-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <form [formGroup]="investmentInfoFilterForm" class="investment-portfolio-content">
    <!-- LEFT -->
    <div class="content-left">
      <!-- Mã CK -->
      <div class="title-left typo-body-15">{{ 'MES-157' | translate }}</div>
      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="listOfStocks?.length; else noResultsStoke"
          [id]="stokeKey"
          [key]="stokeKey"
          [items]="listOfStocks"
          [selectedKeys]="data?.filterOptions?.stockCode"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-670'"
          [warningMessage]="'MES-668'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-14' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['value', 'stoke']"
          [displayFn]="displayStokeFilter"
          (invalidSelection)="validateStockFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultsStoke>
          <div class="typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- Giá trị hiện tại -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-428' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="false"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="getFormControl('startPrice')"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="false"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="getFormControl('endPrice')"
            />
          </div>
        </div>
      </div>

      <!-- Vốn đầu tư -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-369' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="false"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="getFormControl('startCapitalInvestment')"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="false"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="getFormControl('endCapitalInvestment')"
            />
          </div>
        </div>
      </div>

      <!-- Lãi / Lỗ -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-370' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              thousandSeparator=","
              decimalMarker="."
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'-∞'"
              [formControl]="getFormControl('startInvest')"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              thousandSeparator=","
              decimalMarker="."
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="getFormControl('endInvest')"
            />
          </div>
        </div>
      </div>

      <div class="from-to">
        <div class="title-left typo-body-15">% {{ 'MES-370' | translate }}</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              thousandSeparator=","
              decimalMarker="."
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'-∞'"
              [formControl]="getFormControl('startPercentInvest')"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              thousandSeparator=","
              decimalMarker="."
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="getFormControl('endPercentInvest')"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- RIGHT -->
    <div class="content-right">
      <!-- Khách hàng -->
      <div class="title-right typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResults"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers"
          [selectedKeys]="data?.filterOptions?.accountNo"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [warningMessage]="'MES-668'"
          [messageSelectionComplete]="'MES-669'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-295' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          [displayFn]="displayCustomerFilter"
          (invalidSelection)="validateFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResults>
          <div class="typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- Khối lượng GD -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-229' | translate }} GD</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="false"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="getFormControl('startTradableVolume')"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'separator.2'"
              [allowNegativeNumbers]="false"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="getFormControl('endTradableVolume')"
            />
          </div>
        </div>
      </div>
    </div>
  </form>

  <div class="footer-filter">
    <button mat-dialog-close (click)="defaultFilter()" class="btn default typo-button-3">
      {{ 'MES-20' | translate }}
    </button>
    <button
      [class.disable-btn]="investmentInfoFilterForm.invalid || isDisableApply || isDisableStockApply"
      (click)="applyFilter()"
      class="btn apply typo-button-3"
    >
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
