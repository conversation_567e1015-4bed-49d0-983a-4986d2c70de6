import { Component, Inject, OnInit, Optional, QueryList, ViewChildren } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { DestroyService } from 'src/app/core/services';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { take, tap } from 'rxjs';
import { IFilterInvestmentParam, IRangeFilter } from '../../models/asset';
import { Store } from '@ngrx/store';
import { selectAllAccountNumberListByBrokerView$, selectAllStockList$ } from 'src/app/stores/shared/shared.selectors';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';
import { validFromToValidator } from 'src/app/shared/validators/form';

interface IItemStoke {
  value: string;
  stoke: string;
}

/**
 * InvestmentPortfolioFilterComponent
 */
@Component({
  selector: 'app-investment-portfolio-filter',
  templateUrl: './investment-portfolio-filter.component.html',
  styleUrl: './investment-portfolio-filter.component.scss',
})
export class InvestmentPortfolioFilterComponent implements OnInit {
  @ViewChildren(VirtualScrollListComponent) virtualScroll!: QueryList<VirtualScrollListComponent>;

  // startCapitalInvestment = new FormControl();
  // endCapitalInvestment = new FormControl();

  // startInvest = new FormControl();
  // endInvest = new FormControl();

  // startTradableVolume = new FormControl();
  // endTradableVolume = new FormControl();

  // startPortfolioProportion = new FormControl();
  // endPortfolioProportion = new FormControl();

  // startPrice = new FormControl();
  // endPrice = new FormControl();

  // startCapitalStructure = new FormControl();
  // endCapitalStructure = new FormControl();

  // startPercentInvest = new FormControl();
  // endPercentInvest = new FormControl();

  listOfStocks: IItemStoke[] = [];

  // Key identifier for customer objects
  readonly customerKey = 'accountNumber';

  readonly stokeKey = 'value';

  customers: IAllAccountNumber[] = [];

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  isDisableApply = false;

  isDisableStockApply = false;

  investmentInfoFilterForm!: FormGroup;

  /**
   *
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param data
   * @param dialogRef
   * @param store
   */
  constructor(
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<InvestmentPortfolioFilterComponent>,
    private readonly store: Store,
    private readonly fb: FormBuilder
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.initForm();
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.loadCustomerList();
    this.patchValueToForm();
    this.getListStock();
  }

  getFormControl(field: string) {
    return this.investmentInfoFilterForm.get(field) as FormControl;
  }

  private initForm() {
    this.investmentInfoFilterForm = this.fb.group(
      {
        // Sức mua
        startCapitalInvestment: new FormControl(),
        endCapitalInvestment: new FormControl(),
        // NAV
        startInvest: new FormControl(),
        endInvest: new FormControl(),
        // Tổng giá trị CK
        startTradableVolume: new FormControl(),
        endTradableVolume: new FormControl(),
        // Tổng tiền
        startPortfolioProportion: new FormControl(),
        endPortfolioProportion: new FormControl(),
        // Tổng dư nợ
        startPrice: new FormControl(),
        endPrice: new FormControl(),
        // Tổng tài sản
        startCapitalStructure: new FormControl(),
        endCapitalStructure: new FormControl(),
        // +/- Tổng giá trị CK
        startPercentInvest: new FormControl(),
        endPercentInvest: new FormControl(),
      },
      {
        validator: [
          validFromToValidator('startCapitalInvestment', 'endCapitalInvestment'),
          validFromToValidator('startInvest', 'endInvest'),
          validFromToValidator('startTradableVolume', 'endTradableVolume'),
          validFromToValidator('startPortfolioProportion', 'endPortfolioProportion'),
          validFromToValidator('startPrice', 'endPrice'),
          validFromToValidator('startCapitalStructure', 'endCapitalStructure'),
          validFromToValidator('startPercentInvest', 'endPercentInvest'),
        ],
      }
    );
  }

  private getListStock() {
    this.store
      .select(selectAllStockList$)
      .pipe(take(1))
      .subscribe((allStocks) => {
        this.listOfStocks = allStocks.map((t) => ({
          value: t.id,
          stoke: t.stock,
        }));
      });
  }

  private loadCustomerList() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(
        take(1),
        tap((customers) => {
          this.customers = customers;
        })
      )
      .subscribe();
  }

  private patchValueToForm() {
    const { filterOptions } = this.data;

    const {
      capitalInvestmentValue,
      investValue,
      curPriValue,
      portfolioProportion,
      capitalStructure,
      sellAbleQtySumValue,
      percentInvestValue,
    } = filterOptions ?? {};

    this.investmentInfoFilterForm.patchValue({
      startCapitalInvestment: capitalInvestmentValue?.from,
      endCapitalInvestment: capitalInvestmentValue?.to,

      startInvest: investValue?.from,
      endInvest: investValue?.to,

      startTradableVolume: sellAbleQtySumValue?.from,
      endTradableVolume: sellAbleQtySumValue?.to,

      startPortfolioProportion: portfolioProportion?.from,
      endPortfolioProportion: portfolioProportion?.to,

      startPrice: curPriValue?.from,
      endPrice: curPriValue?.to,

      startCapitalStructure: capitalStructure?.from,
      endCapitalStructure: capitalStructure?.to,

      startPercentInvest: percentInvestValue?.from,
      endPercentInvest: percentInvestValue?.to,
    });
  }

  /**
   * UpdateFormControlValue
   * @param data data
   * @param startControl startControl
   * @param endControl endControl
   */
  updateFormControlValue(data: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    startControl.patchValue(data?.from);
    endControl.patchValue(data?.to);
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  validateStockFilterValue(invalid: boolean) {
    this.isDisableStockApply = invalid;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    if (this.isDisableApply || this.isDisableStockApply) return;

    const getVirtualScrollById = (id: string) => {
      return this.virtualScroll.find((component) => component.id === id);
    };
    const accountNo = ((getVirtualScrollById(this.customerKey)?.getChecked() as IAllAccountNumber[]) ?? []).map(
      (c) => c.accountNumber
    );

    const stockCode = ((getVirtualScrollById(this.stokeKey)?.getChecked() as IItemStoke[]) ?? []).map((c) => c.value);

    const {
      startPrice,
      endPrice,
      startCapitalInvestment,
      endCapitalInvestment,

      startInvest,
      endInvest,

      startPercentInvest,
      endPercentInvest,
      startTradableVolume,
      endTradableVolume,
    } = this.investmentInfoFilterForm?.value;
    // Giá trị hiện tại
    const curPriValue = {
      from: startPrice ?? null,
      to: endPrice ?? null,
    };

    // Vốn đầu tư
    const capitalInvestmentValue = {
      from: startCapitalInvestment ?? null,
      to: endCapitalInvestment ?? null,
    };

    // Lãi lỗ
    const investValue = {
      from: startInvest ?? null,
      to: endInvest ?? null,
    };

    // % lãi lỗ
    const percentInvestValue = {
      from: startPercentInvest ?? null,
      to: endPercentInvest ?? null,
    };

    // Khối lượng giao dịch
    const sellAbleQtySumValue = {
      from: startTradableVolume ?? null,
      to: endTradableVolume ?? null,
    };

    const isFilter = this.checkStatusFilter(
      accountNo,
      stockCode,
      capitalInvestmentValue,
      investValue,
      sellAbleQtySumValue,
      curPriValue,
      percentInvestValue
      // portfolioProportion,
      // capitalStructure
    );

    const optionFilter = {
      isFilter,
      accountNo,
      stockCode,
      curPriValue,
      capitalInvestmentValue,
      investValue,
      percentInvestValue,
      sellAbleQtySumValue,
    } as IFilterInvestmentParam;

    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * CheckStatusFilter
   * @param status
   * @param rangeDateHold
   * @param rangePotential
   * @param rangeNotRecord
   * @param rangeRecord
   * @param date
   * @returns {boolean} true / false
   */
  checkStatusFilter(
    customers: null | (string | undefined)[],
    stockCode: string[],
    capitalInvestment: IRangeFilter,
    invest: IRangeFilter,
    tradableVolume: IRangeFilter,
    price: IRangeFilter,
    percent: IRangeFilter
    // portfolioProportion: IRangeFilter,
    // capitalStructure: IRangeFilter
  ) {
    const isCustomnerFilter = (customers ?? []).length === 0;
    const isStockFilter = (stockCode ?? []).length === 0;
    return (
      !isCustomnerFilter ||
      !isStockFilter ||
      this.checkHasValueInObject(capitalInvestment) ||
      this.checkHasValueInObject(invest) ||
      this.checkHasValueInObject(tradableVolume) ||
      this.checkHasValueInObject(price) ||
      this.checkHasValueInObject(percent)

      // this.checkHasValueInObject(portfolioProportion) ||
      // this.checkHasValueInObject(capitalStructure)
    );
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObject(data: any) {
    return !!data && ((data.from != null && data.from !== '') || (data.to != null && data.to !== ''));
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customer: IAllAccountNumber): string {
    if (!customer) return '';
    return `${customer.accountNumber} - ${customer.customerName}`;
  }

  displayStokeFilter(stoke: IItemStoke) {
    if (!stoke) return '';
    return `${stoke.value} - ${stoke.stoke}`;
  }
}
