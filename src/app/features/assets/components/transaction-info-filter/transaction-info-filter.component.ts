import { Component, Inject, Optional, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { DestroyService } from 'src/app/core/services';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IFilterTransactionParam, IRangeFilter } from '../../models/asset';
import { take, tap } from 'rxjs';
import { selectAllAccountNumberListByBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { Store } from '@ngrx/store';
import { validFromToValidator } from 'src/app/shared/validators/form';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { MAX_ITEM_ACCOUNT_SELECTED, MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';
import { transformToNumber } from 'src/app/shared/utils/format-numbet';

/**
 * TransactionInfoFilterComponent
 */
@Component({
  selector: 'app-transaction-info-filter',
  templateUrl: './transaction-info-filter.component.html',
  styleUrl: './transaction-info-filter.component.scss',
})
export class TransactionInfoFilterComponent {
  @ViewChild(VirtualScrollListComponent) virtualScroll!: VirtualScrollListComponent;

  transactionInfoFilterForm!: FormGroup;

  // Key identifier for customer objects
  readonly customerKey = 'accountNumber';

  customers: IAllAccountNumber[] = [];

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  MAX_ITEM_ACCOUNT_SELECTED = MAX_ITEM_ACCOUNT_SELECTED;

  isDisableApply = false;

  /**
   *
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param data
   * @param dialogRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: IFilterTransactionParam,
    public dialogRef: MatDialogRef<TransactionInfoFilterComponent>,
    private readonly store: Store,
    private readonly fb: FormBuilder
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.initForm();
    this.loadCustomerList();
    this.patchFormValue();
  }

  private initForm() {
    this.transactionInfoFilterForm = this.fb.group(
      {
        startTotalTransactionValue: new FormControl(),
        endTotalTransactionValue: new FormControl(),

        startBuyTransactionValue: new FormControl(),
        endBuyTransactionValue: new FormControl(),

        startSellTransactionValue: new FormControl(),
        endSellTransactionValue: new FormControl(),

        startRevenueFee: new FormControl(),
        endRevenueFee: new FormControl(),

        startBrokerCommission: new FormControl(),
        endBrokerCommission: new FormControl(),

        startStockExchangeFee: new FormControl(),
        endStockExchangeFee: new FormControl(),

        startDepositoryFee: new FormControl(),
        endDepositoryFee: new FormControl(),

        startSmsFee: new FormControl(),
        endSmsFee: new FormControl(),

        startNetTransactionFee: new FormControl(),
        endNetTransactionFee: new FormControl(),
      },
      {
        validator: [
          validFromToValidator('startTotalTransactionValue', 'endTotalTransactionValue'),
          validFromToValidator('startBuyTransactionValue', 'endBuyTransactionValue'),
          validFromToValidator('startSellTransactionValue', 'endSellTransactionValue'),
          validFromToValidator('startRevenueFee', 'endRevenueFee'),
          validFromToValidator('startBrokerCommission', 'endBrokerCommission'),
          validFromToValidator('startStockExchangeFee', 'endStockExchangeFee'),
          validFromToValidator('startDepositoryFee', 'endDepositoryFee'),
          validFromToValidator('startSmsFee', 'endSmsFee'),
          validFromToValidator('startNetTransactionFee', 'endNetTransactionFee'),
        ],
      }
    );
  }

  private patchFormValue() {
    const {
      totalTransactionValue,
      buyTransactionValue,
      sellTransactionValue,
      revenueFee,
      netTransactionFee,
      brokerCommission,
      stockExchangeFee,
      depositoryFee,
      smsFee,
    } = this.data;

    this.transactionInfoFilterForm.patchValue({
      startTotalTransactionValue: totalTransactionValue.start,
      endTotalTransactionValue: totalTransactionValue.end,

      startBuyTransactionValue: buyTransactionValue.start,
      endBuyTransactionValue: buyTransactionValue.end,

      startSellTransactionValue: sellTransactionValue.start,
      endSellTransactionValue: sellTransactionValue.end,

      startRevenueFee: revenueFee.start,
      endRevenueFee: revenueFee.end,

      startBrokerCommission: brokerCommission.start,
      endBrokerCommission: brokerCommission.end,

      startStockExchangeFee: stockExchangeFee.start,
      endStockExchangeFee: stockExchangeFee.end,

      startDepositoryFee: depositoryFee.start,
      endDepositoryFee: depositoryFee.end,

      startSmsFee: smsFee.start,
      endSmsFee: smsFee.end,

      startNetTransactionFee: netTransactionFee.start,
      endNetTransactionFee: netTransactionFee.end,
    });
  }

  private loadCustomerList() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(
        take(1),
        tap((customers) => {
          this.customers = customers;
        })
      )
      .subscribe();
  }

  getFormControl(field: string) {
    return this.transactionInfoFilterForm.get(field) as FormControl;
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    if (this.isDisableApply) return;

    const customers = (this.virtualScroll.getChecked() as IAllAccountNumber[]).map((c) => c.accountNumber);
    const {
      startTotalTransactionValue,
      endTotalTransactionValue,

      startBuyTransactionValue,
      endBuyTransactionValue,

      startSellTransactionValue,
      endSellTransactionValue,

      startRevenueFee,
      endRevenueFee,

      startBrokerCommission,
      endBrokerCommission,

      startStockExchangeFee,
      endStockExchangeFee,

      startDepositoryFee,
      endDepositoryFee,

      startSmsFee,
      endSmsFee,

      startNetTransactionFee,
      endNetTransactionFee,
    } = this.transactionInfoFilterForm.value;

    const totalTransactionValue = {
      start: transformToNumber(startTotalTransactionValue),
      end: transformToNumber(endTotalTransactionValue),
    };

    const buyTransactionValue = {
      start: transformToNumber(startBuyTransactionValue),
      end: transformToNumber(endBuyTransactionValue),
    };

    const sellTransactionValue = {
      start: transformToNumber(startSellTransactionValue),
      end: transformToNumber(endSellTransactionValue),
    };

    const revenueFee = {
      start: transformToNumber(startRevenueFee),
      end: transformToNumber(endRevenueFee),
    };

    const netTransactionFee = {
      start: transformToNumber(startNetTransactionFee),
      end: transformToNumber(endNetTransactionFee),
    };

    const brokerCommission = {
      start: transformToNumber(startBrokerCommission),
      end: transformToNumber(endBrokerCommission),
    };

    const stockExchangeFee = {
      start: transformToNumber(startStockExchangeFee),
      end: transformToNumber(endStockExchangeFee),
    };

    const depositoryFee = {
      start: transformToNumber(startDepositoryFee),
      end: transformToNumber(endDepositoryFee),
    };

    const smsFee = {
      start: transformToNumber(startSmsFee),
      end: transformToNumber(endSmsFee),
    };

    const filterField = {
      customers,
      totalTransactionValue,
      buyTransactionValue,
      sellTransactionValue,
      revenueFee,
      netTransactionFee,
      brokerCommission,
      stockExchangeFee,
      depositoryFee,
      smsFee,
    };

    const isFilter = this.checkStatusFilter(filterField);
    const optionFilter = {
      isFilter,
      customers,
      totalTransactionValue,
      buyTransactionValue,
      sellTransactionValue,
      revenueFee,
      netTransactionFee,
      brokerCommission,
      stockExchangeFee,
      depositoryFee,
      smsFee,
    };

    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * CheckStatusFilter
   * @param status
   * @param rangeDateHold
   * @param rangePotential
   * @param rangeNotRecord
   * @param rangeRecord
   * @param date
   * @returns {boolean} true / false
   */
  checkStatusFilter(filterField: {
    customers: null | (string | undefined)[];
    totalTransactionValue: IRangeFilter;
    buyTransactionValue: IRangeFilter;
    sellTransactionValue: IRangeFilter;
    revenueFee: IRangeFilter;
    netTransactionFee: IRangeFilter;
    brokerCommission: IRangeFilter;
    stockExchangeFee: IRangeFilter;
    depositoryFee: IRangeFilter;
    smsFee: IRangeFilter;
  }) {
    const {
      customers,
      totalTransactionValue,
      buyTransactionValue,
      sellTransactionValue,
      revenueFee,
      netTransactionFee,
      brokerCommission,
      stockExchangeFee,
      depositoryFee,
      smsFee,
    } = filterField;

    const isInitialCustomerSelect = (customers ?? [])?.length === 0;
    return (
      !isInitialCustomerSelect ||
      this.checkHasValueInObject(totalTransactionValue) ||
      this.checkHasValueInObject(buyTransactionValue) ||
      this.checkHasValueInObject(sellTransactionValue) ||
      this.checkHasValueInObject(revenueFee) ||
      this.checkHasValueInObject(netTransactionFee) ||
      this.checkHasValueInObject(brokerCommission) ||
      this.checkHasValueInObject(stockExchangeFee) ||
      this.checkHasValueInObject(depositoryFee) ||
      this.checkHasValueInObject(smsFee)
    );
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObject(data: any) {
    return !!data && ((data.start != null && data.start !== '') || (data.end != null && data.end !== ''));
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customer: IAllAccountNumber): string {
    if (!customer) return '';
    return `${customer.accountNumber} - ${customer.customerName}`;
  }
}
