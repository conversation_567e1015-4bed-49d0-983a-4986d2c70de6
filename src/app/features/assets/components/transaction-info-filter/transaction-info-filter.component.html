<div class="transaction-info-filter-wrap">
  <div class="transaction-info-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <form [formGroup]="transactionInfoFilterForm" class="transaction-info-content">
    <!-- LEFT -->
    <div class="content-left">
      <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResults"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers"
          [maxSelectedItems]="MAX_ITEM_ACCOUNT_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_ACCOUNT_SELECTED }"
          [messageSelectionComplete]="'MES-669'"
          [warningMessage]="'MES-668'"
          [selectedKeys]="data.customers"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-295' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          [displayFn]="displayCustomerFilter"
          (invalidSelection)="validateFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResults>
          <div class="typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- Tổng GTGD -->
      <div class="from-to">
        <div class="title-left typo-body-15">Tổng {{ 'MES-180' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="true"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startTotalTransactionValue')"
          [toControl]="getFormControl('endTotalTransactionValue')"
        ></app-from-to>
      </div>

      <!--GTGD (mua) -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-180' | translate }} (mua)</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="true"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startBuyTransactionValue')"
          [toControl]="getFormControl('endBuyTransactionValue')"
        ></app-from-to>
      </div>

      <!--GTGD (bán) -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-180' | translate }} (bán)</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="true"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startSellTransactionValue')"
          [toControl]="getFormControl('endSellTransactionValue')"
        ></app-from-to>
      </div>
    </div>

    <!-- RIGHT -->
    <div class="content-right">
      <!--Tổng phí -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-581' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="true"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startRevenueFee')"
          [toControl]="getFormControl('endRevenueFee')"
        ></app-from-to>
      </div>

      <!-- Phí trả sở -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-288' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="true"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startStockExchangeFee')"
          [toControl]="getFormControl('endStockExchangeFee')"
        ></app-from-to>
      </div>

      <!-- Thuế TNCN -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-415' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="true"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startDepositoryFee')"
          [toControl]="getFormControl('endDepositoryFee')"
        ></app-from-to>
      </div>

      <!-- Net phí giao dịch -->
      <div class="from-to">
        <div class="title-right typo-body-15">{{ 'MES-195' | translate }}</div>
        <app-from-to
          [labelFrom]="'MES-209'"
          [labelTo]="'MES-210'"
          [mask]="'separator.2'"
          [allowNegativeNumbers]="true"
          [placeholderFrom]="'0'"
          [placeholderTo]="'∞'"
          [fromControl]="getFormControl('startNetTransactionFee')"
          [toControl]="getFormControl('endNetTransactionFee')"
        ></app-from-to>
      </div>
    </div>
  </form>

  <div class="footer-filter">
    <div mat-dialog-close (click)="defaultFilter()" class="btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <button
      [disabled]="transactionInfoFilterForm.invalid || isDisableApply"
      (click)="applyFilter()"
      class="btn apply typo-button-3"
    >
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
