<div class="popup-feedebt-container">
  <div class="feedebt-header">
    <div class="title-cls typo-body-14">{{ 'MES-286' | translate }}</div>
  </div>

  <div class="feedebt-body">
    <div class="feedebt-title-cls">
      <div class="title-feedebt typo-body-14">{{ 'MES-328' | translate }}</div>
      <img src="./assets/icons/export-icon.svg" class="icon-cls" alt="export" />
    </div>
    <div class="fee-debt-content">
      <ng-container
        *ngFor="let label of feeDebtLabel; let i = index"
        [ngTemplateOutlet]="debtInfo"
        [ngTemplateOutletContext]="{ label: label, money: money[i] }"
      >
      </ng-container>
    </div>
  </div>
</div>

<ng-template #debtInfo let-label="label" let-money="money">
  @if(debtInfo){
  <div>
    <div class="total-debt-decrease typo-body-14">
      <div class="label-feedebt typo-body-12">{{ label | translate }}</div>
      <span class="content-money">{{ money !== 0 ? customNumberFormat(money) : '-' }}</span>
    </div>
  </div>
  }
</ng-template>
