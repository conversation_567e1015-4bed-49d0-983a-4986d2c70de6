import { Component, Inject, Optional } from '@angular/core';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { DialogService } from '../../../../core/services';

/**
 * PopupFeeDebtComponent
 */
@Component({
  selector: 'app-popup-fee-debt-component',
  templateUrl: './popup-fee-debt.component.html',
  styleUrl: './popup-fee-debt.component.scss',
})
export class PopupFeeDebtComponent {
  customNumberFormat = customNumberFormat;
  /**
   * Constructor
   * @param dialogService
   * @param popoverRef
   */
  constructor(
    dialogService: DialogService,

    @Optional() @Inject(PopoverRef) public popoverRef: PopoverRef
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }

  feeDebtLabel = ['MES-333', 'MES-290', 'MES-289'];
  money = [0, 1000000, 0];
}
