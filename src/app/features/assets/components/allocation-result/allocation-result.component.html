<div class="dialog-wrap">
  <div class="dialog-header">
    <div class="title-cls typo-body-14">{{ title | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="dialog-body">
    <div class="section-wrap">
      <div class="section grid">
        <div class="grid-item content flex-col">
          <div *ngIf="type" class="sub-title typo-body-12">{{ 'MES-335' | translate }}</div>
          <div *ngIf="!type" class="sub-title typo-body-12">{{ 'MES-101' | translate }}</div>
          <!-- Check type của sentDate -->
          @if (!isString(sentDate)) {
          <div class="typo-body-12">{{ sentDate | date : 'dd / MM / YYYY' }}</div>
          } @else {
          <div class="typo-body-12">{{ sentDate }}</div>
          }
        </div>

        <div class="grid-item content flex-col">
          <div class="sub-title typo-body-12">{{ 'MES-85' | translate }}</div>
          <div class="typo-body-12">{{ sentBy }}</div>
        </div>

        <div class="grid-item content flex-col">
          <div class="sub-title typo-body-12">{{ 'MES-15' | translate }}</div>
          <div class="typo-body-12">{{ accountNumber }}</div>
        </div>

        <div class="grid-item content flex-col">
          <div class="sub-title typo-body-12">{{ 'MES-96' | translate }}</div>
          <div class="typo-body-12">{{ customerName }}</div>
        </div>
      </div>

      <app-allocation-summary
        [allocatedSource]="allocatedSource"
        [allocatedTarget]="allocatedTarget"
      ></app-allocation-summary>

      <div class="section note">
        <div class="title typo-body-12">{{ 'MES-82' | translate }}</div>
        <div class="content typo-body-12">{{ note }}</div>
      </div>
    </div>
  </div>

  <div class="dialog-footer justify-center">
    <button mat-dialog-close class="btn outline typo-button-3">{{ 'MES-74' | translate }}</button>
  </div>
</div>
