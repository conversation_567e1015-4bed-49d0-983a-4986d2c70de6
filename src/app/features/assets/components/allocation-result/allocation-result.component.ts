import { Component, Inject, Input } from '@angular/core';
import { CONVERT_ASSET_TYPE_TO_LABEL, IAllocationRef, IAllocationResult } from '../../constant/assets';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

/**
 * Declare AllocationSummaryComponent
 */
@Component({
  selector: 'app-allocation-result',
  templateUrl: './allocation-result.component.html',
  styleUrl: './allocation-result.component.scss',
})
export class AllocationResultComponent {
  @Input() allocatedSource: IAllocationRef | null = null;
  @Input() allocatedTarget: IAllocationRef | null = null;

  title = '';
  sentDate = new Date();
  sentBy = '';
  accountNumber = '';
  customerName = '';
  note = '';

  // TODO: Tạm thời để check label của popup
  type = '';

  CONVERT_ASSET_TYPE_TO_LABEL = CONVERT_ASSET_TYPE_TO_LABEL;

  /**
   *
   * @param data
   * @param data.allocatedSource
   * @param data.allocatedTarget
   */
  constructor(@Inject(MAT_DIALOG_DATA) public data: IAllocationResult) {
    this.allocatedSource = { ...data.allocatedSource };
    this.allocatedTarget = { ...data.allocatedTarget };
    this.note = data.note;
    this.sentDate = data.sentDate;
    this.sentBy = data.sentBy;
    this.accountNumber = data.accountNumber;
    this.customerName = data.customerName;
    this.title = data.title;
    this.type = data.type ?? '';
  }

  /**
   * check is string or not
   * @param sentDate
   * @returns {boolean} boolean
   */
  isString(sentDate: any): boolean {
    return typeof sentDate === 'string';
  }
}
