.section-wrap {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .section {
    border-radius: 8px;
    padding: 16px;
    background: var(--color--background--1);

    &.grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      /* Two columns of equal width */
      grid-template-rows: 1fr 1fr;
      /* Two rows of equal height */
      gap: 24px;
    }

    .title {
      color: var(--color--text--subdued);
      margin-bottom: 16px;
    }

    .content {
      display: flex;
      justify-content: space-between;

      &.flex-col {
        flex-direction: column;
      }

      .sub-title {
        font-size: 12px;
        color: var(--color--text--subdued);
        margin-bottom: 10px;
      }
    }
  }
}

.dialog-footer {
  .btn {
    width: 160px;

    &:hover {
      color: white;
      background-color: var(--color--brand--500);
    }
  }
}
