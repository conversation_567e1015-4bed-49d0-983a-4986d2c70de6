import { Component, Inject, OnInit, Optional } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DestroyService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { IFilterAccountStatusParam, IRangeFilter } from '../../models/asset';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { IListOptions, LIST_OF_CUSTOMER } from '../../constant/assets';

export const MARGIN_RATE_VIOLATION = [
  {
    label: 'Không',
    value: 0,
  },
  {
    label: 'Warning 1',
    value: 1,
  },
  {
    label: 'Warning 2',
    value: 2,
  },
  {
    label: 'Warning 3',
    value: 3,
  },
  {
    label: 'Tỷ lệ bổ sung',
    value: 4,
  },
  {
    label: 'Tỷ lệ giải chấp',
    value: 5,
  },
];

export const OVERDUE_DEBT = [
  {
    label: 'Không',
    value: 0,
  },
  {
    label: 'Có',
    value: 1,
  },
  {
    label: 'Nợ Margin',
    value: 2,
  },
];

export const FEE_DEBT = [
  {
    label: 'Không',
    value: 0,
  },
  {
    label: 'Có',
    value: 1,
  },
  {
    label: 'Phí SMS',
    value: 2,
  },
  {
    label: 'Phí lưu ký',
    value: 3,
  },
  {
    label: 'Phí quản lý TSKQ',
    value: 4,
  },
  {
    label: 'Phí chuyển khoản',
    value: 5,
  },
];

/**
 * AccountStatusFilterComponent
 */
@Component({
  selector: 'app-account-status-filter',
  templateUrl: './account-status-filter.component.html',
  styleUrl: './account-status-filter.component.scss',
})
export class AccountStatusFilterComponent implements OnInit {
  isSelectAllCustomer = true;
  isSelectAllMarginRateViolation = true;
  isSelectAllOverDueDebt = true;
  isSelectAllFeeDebt = true;

  listFilterCustomerOptions: IListOptions[] = [];
  listFilterCustomerStore: IListOptions[] = [];

  listFilterMarginRateViolationOptions: IListOptions[] = [];
  listFilterMarginRateViolationStore: IListOptions[] = [];

  listFilterOverDueDebtOptions: IListOptions[] = [];

  listFilterFeeDebtOptions: IListOptions[] = [];

  searchCustomerControl = new FormControl();
  searchMarginRateViolationControl = new FormControl();

  // Tỷ lệ ký quỹ (CMR)
  startMarginRate = new FormControl();
  endMarginRate = new FormControl();

  // Vi phạm tỷ lệ ký quỹ (CMR) tỷ lệ bổ sung
  startMarginRateViolation = new FormControl();
  endMarginRateViolation = new FormControl();

  /**
   *
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param data
   * @param dialogRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: IFilterAccountStatusParam,
    public dialogRef: MatDialogRef<AccountStatusFilterComponent>
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    const { customers, marginRate, marginRateViolationPercent, marginRateViolation, overDueDebt, feeDebt } = data;

    this.updateList(customers, 'customer');
    this.updateList(marginRateViolation, 'marginRateViolation');
    this.updateList(overDueDebt, 'overDueDebt');
    this.updateList(feeDebt, 'feeDebt');

    this.updateFormControlValue(marginRate, this.startMarginRate, this.endMarginRate);
    this.updateFormControlValue(marginRateViolationPercent, this.startMarginRateViolation, this.endMarginRateViolation);
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.listFilterCustomerStore = this.listFilterCustomerOptions;
    this.searchCustomerControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterCustomerOptions = this._filter(value ?? '', this.listFilterCustomerStore);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    this.listFilterMarginRateViolationStore = this.listFilterMarginRateViolationOptions;
    this.searchMarginRateViolationControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterMarginRateViolationOptions = this._filter(
            value ?? '',
            this.listFilterMarginRateViolationStore
          );
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * UpdateFormControlValue
   * @param date date
   * @param startControl startControl
   * @param endControl endControl
   */
  updateFormControlValue(date: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    const { start, end } = date;
    startControl.patchValue(start);
    endControl.patchValue(end);
  }

  /**
   * updateList
   * @param data
   * @param type
   */
  updateList(data: (string | number)[] | null, type: string) {
    const isSelect = (v: string | number) => data === null || data?.includes(v);
    switch (type) {
      case 'customer':
        this.listFilterCustomerOptions = LIST_OF_CUSTOMER.map((customer) => ({
          name: customer.name,
          group: customer.group,
          isSelect: isSelect(customer.name),
        }));
        this.isSelectAllCustomer = this.listFilterCustomerOptions.every((t) => t.isSelect === true);
        break;

      case 'marginRateViolation':
        this.listFilterMarginRateViolationOptions = MARGIN_RATE_VIOLATION.map((margin) => ({
          label: margin.label,
          value: margin.value,
          isSelect: isSelect(margin.value),
        }));
        this.isSelectAllMarginRateViolation = this.listFilterMarginRateViolationOptions.every(
          (t) => t.isSelect === true
        );
        break;

      case 'overDueDebt':
        this.listFilterOverDueDebtOptions = OVERDUE_DEBT.map((debt) => ({
          label: debt.label,
          value: debt.value,
          isSelect: isSelect(debt.value),
        }));

        this.isSelectAllOverDueDebt = this.listFilterOverDueDebtOptions.every((t) => t.isSelect === true);
        break;

      case 'feeDebt':
        this.listFilterFeeDebtOptions = FEE_DEBT.map((debt) => ({
          label: debt.label,
          value: debt.value,
          isSelect: isSelect(debt.value),
        }));
        this.isSelectAllFeeDebt = this.listFilterFeeDebtOptions.every((t) => t.isSelect === true);
        break;
    }
  }

  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param type Kiểu giá trị được thay đổi
   * @param section
   * @param item Item thay đổi
   */
  changeSections(checked: boolean, type: string, section: string, item?: IListOptions) {
    if (type === 'all') {
      this.updateSelectAll(checked, section);
    } else if (type === 'item' && item) {
      item.isSelect = checked;
      this.updateSelectAllStatus(section);
    }
  }

  /**
   * Cập nhật trạng thái chọn tất cả
   * @param {boolean} checked
   * @param {string} section
   */
  updateSelectAll(checked: boolean, section: string) {
    switch (section) {
      case 'customer':
        this.isSelectAllCustomer = checked;
        this.listFilterCustomerOptions.forEach((i) => {
          i.isSelect = checked;
        });
        break;
      case 'marginRateViolation':
        this.isSelectAllMarginRateViolation = checked;
        this.listFilterMarginRateViolationOptions.forEach((i) => {
          i.isSelect = checked;
        });
        break;
      case 'overDueDebt':
        this.isSelectAllOverDueDebt = checked;
        this.listFilterOverDueDebtOptions.forEach((i) => {
          i.isSelect = checked;
        });
        break;
      case 'feeDebt':
        this.isSelectAllFeeDebt = checked;
        this.listFilterFeeDebtOptions.forEach((i) => {
          i.isSelect = checked;
        });
        break;
    }
  }

  /**
   * Cập nhật trạng thái của SelectAll checkbox
   * @param {string} section
   */
  updateSelectAllStatus(section: string) {
    switch (section) {
      case 'customer':
        this.isSelectAllCustomer = this.listFilterCustomerOptions.every((t) => t.isSelect);
        break;
      case 'marginRateViolation':
        this.isSelectAllMarginRateViolation = this.listFilterMarginRateViolationOptions.every((t) => t.isSelect);
        break;
      case 'overDueDebt':
        this.isSelectAllOverDueDebt = this.listFilterOverDueDebtOptions.every((t) => t.isSelect);
        break;
      case 'feeDebt':
        this.isSelectAllFeeDebt = this.listFilterFeeDebtOptions.every((t) => t.isSelect);
        break;
    }
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    const customers = this.listFilterCustomerOptions.filter((t) => t.isSelect).map((t) => t.name);

    const marginRate = {
      start: this.startMarginRate.value,
      end: this.endMarginRate.value,
    };

    const marginRateViolationPercent = {
      start: this.startMarginRateViolation.value,
      end: this.endMarginRateViolation.value,
    };

    const marginRateViolation = this.listFilterMarginRateViolationOptions.filter((t) => t.isSelect).map((t) => t.value);

    const overDueDebt = this.listFilterOverDueDebtOptions.filter((t) => t.isSelect).map((t) => t.value);

    const feeDebt = this.listFilterFeeDebtOptions.filter((t) => t.isSelect).map((t) => t.value);

    const isFilter = true;

    this.dialogRef.close({
      type: 'save',
      optionFilter: {
        isFilter,
        customers,
        marginRate,
        marginRateViolationPercent,
        marginRateViolation,
        overDueDebt,
        feeDebt,
      },
    });
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   *
   * @param {string} value
   * @param {IListOptions[]} options
   */
  private _filter(value: string, options: IListOptions[]): IListOptions[] {
    const filterValue = value.toString().toLowerCase();

    return options.filter(
      (option) =>
        option.name?.toLowerCase().includes(filterValue) ||
        option.group?.toLowerCase().includes(filterValue) ||
        option.label?.toLowerCase().includes(filterValue)
    );
  }
}
