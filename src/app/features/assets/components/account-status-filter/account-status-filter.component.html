<div class="account-status-filter-wrap">
  <div class="account-status-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="account-status-content">
    <!-- LEFT -->
    <div class="content-left">
      <div class="title-left typo-body-15">{{ 'MES-96' | translate }}</div>
      <div class="searchbox-wrap">
        <div class="search-box">
          <input
            type="text"
            class="input-cls-custom input-style-common typo-body-11 fs-12"
            [placeholder]="'MES-295' | translate"
            [formControl]="searchCustomerControl"
          />
          <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
        </div>
        <div class="option-list-cls">
          <mat-checkbox
            (change)="changeSections($event.checked, 'all', 'customer')"
            [checked]="isSelectAllCustomer"
            class="checkbox-cls"
          >
            {{ 'MES-58' | translate }}</mat-checkbox
          >
          @for (item of listFilterCustomerOptions; let i = $index; track item) {
          <div class="checkbox-cls-item typo-body-15">
            <mat-checkbox
              (change)="changeSections($event.checked, 'item', 'customer', item)"
              [checked]="item.isSelect"
              class="checkbox-cls"
            >
              {{ item.group }} - {{ item.name }}</mat-checkbox
            >
          </div>
          }
        </div>
      </div>

      <!-- Tỷ lệ ký quỹ (CMR) -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-282' | translate }} (CMR)</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startMarginRate"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endMarginRate"
            />
          </div>
        </div>
      </div>

      <!-- Vi phạm tỷ lệ ký quỹ (CMR) tỷ lệ bổ sung -->
      <div class="from-to">
        <div class="title-left typo-body-15">{{ 'MES-284' | translate }} tỷ lệ bổ sung</div>
        <div class="purchasing-power-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="0"
              [formControl]="startMarginRateViolation"
            />
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <input
              [mask]="'percent.2'"
              [allowNegativeNumbers]="true"
              class="from-to-input typo-field-5"
              [placeholder]="'∞'"
              [formControl]="endMarginRateViolation"
            />
          </div>
        </div>
      </div>

      <!-- Vi phạm tỷ lệ ký quỹ (CMR) -->
      <div class="title-left typo-body-15">{{ 'MES-284' | translate }}</div>
      <div class="searchbox-wrap">
        <div class="search-box">
          <input
            type="text"
            class="input-cls-custom input-style-common typo-body-11 fs-12"
            [placeholder]="'MES-14' | translate"
            [formControl]="searchMarginRateViolationControl"
          />
          <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
        </div>
        <div class="option-list-cls">
          <mat-checkbox
            (change)="changeSections($event.checked, 'all', 'marginRateViolation')"
            [checked]="isSelectAllMarginRateViolation"
            class="checkbox-cls"
          >
            {{ 'MES-58' | translate }}</mat-checkbox
          >
          @for (item of listFilterMarginRateViolationOptions; let i = $index; track item) {
          <div class="checkbox-cls-item typo-body-15">
            <mat-checkbox
              (change)="changeSections($event.checked, 'item', 'marginRateViolation', item)"
              [checked]="item.isSelect"
              class="checkbox-cls"
            >
              {{ item.label }}</mat-checkbox
            >
          </div>
          }
        </div>
      </div>
    </div>

    <!-- RIGHT -->
    <div class="content-right">
      <div class="title-right typo-body-15">{{ 'MES-285' | translate }}</div>
      <div class="option-list-cls">
        <mat-checkbox
          (change)="changeSections($event.checked, 'all', 'overDueDebt')"
          [checked]="isSelectAllOverDueDebt"
          class="checkbox-cls"
        >
          {{ 'MES-58' | translate }}</mat-checkbox
        >
        @for (item of listFilterOverDueDebtOptions; let i = $index; track item) {
        <div class="checkbox-cls-item typo-body-15">
          <mat-checkbox
            (change)="changeSections($event.checked, 'item', 'overDueDebt', item)"
            [checked]="item.isSelect"
            class="checkbox-cls"
          >
            {{ item.label }}</mat-checkbox
          >
        </div>
        }
      </div>

      <div class="title-right typo-body-15">{{ 'MES-286' | translate }}</div>
      <div class="option-list-cls">
        <mat-checkbox
          (change)="changeSections($event.checked, 'all', 'feeDebt')"
          [checked]="isSelectAllFeeDebt"
          class="checkbox-cls"
        >
          {{ 'MES-58' | translate }}</mat-checkbox
        >
        @for (item of listFilterFeeDebtOptions; let i = $index; track item) {
        <div class="checkbox-cls-item typo-body-15">
          <mat-checkbox
            (change)="changeSections($event.checked, 'item', 'feeDebt', item)"
            [checked]="item.isSelect"
            class="checkbox-cls"
          >
            {{ item.label }}</mat-checkbox
          >
        </div>
        }
      </div>
    </div>
  </div>

  <div class="footer-filter">
    <!-- (click)="defaultFilter()" -->
    <div mat-dialog-close (click)="defaultFilter()" class="btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <!-- (click)="applyFilter()" -->
    <div mat-dialog-close (click)="applyFilter()" class="btn apply typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
