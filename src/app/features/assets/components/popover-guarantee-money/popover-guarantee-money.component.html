<div class="popup-confirm-wrapper">
  <!-- HEADER -->
  <div class="header-title">
    <div class="typo-body-16">{{ 'MES-146' | translate }}</div>
    <img class="close-button" src="./assets/icons/close.svg" (click)="close()" alt="Close button" />
  </div>

  <!-- CONTENT -->
  <form [formGroup]="guaranteeMoneyForm" class="content-popup">
    <div class="button-increase-reduce-wrap">
      <div class="typo-body-15">Số tiền bảo lãnh</div>
      <div class="button-increase-reduce">
        <img src="./assets/icons/amount-selector-minus.svg" alt="Decrease" (click)="decreaseValue()" />
        <input
          matInput
          class="money typo-body-12 fs-12"
          [value]="customNumberFormat(value)"
          (input)="onInputChange($event)"
          formControlName="guaranteeMoney"
        />

        <img src="./assets/icons/amount-selector-add.svg" alt="Increase" (click)="increaseValue()" />
      </div>
    </div>
    <div class="slider-wrap">
      <div class="orange typo-body-15">{{ customNumberFormat(value) }} VND</div>
      <mat-slider class="example-margin" [disabled]="false" [max]="max" [min]="min" [step]="step">
        <input formControlName="guaranteeMoneySlider" matSliderThumb [(ngModel)]="value" #slider />
      </mat-slider>
      <div class="purchasing-power typo-body-12">
        Sức mua: <span class="typo-body-15">{{ customNumberFormat(purchasingPower) }} VND</span>
      </div>
    </div>

    <div class="error" *ngIf="guaranteeMoneyForm.hasError('max', 'guaranteeMoney')">
      Số tiền bảo lãnh không thể quá {{ customNumberFormat(max) }} VND.
    </div>
    <div class="error" *ngIf="guaranteeMoneyForm.hasError('max', 'guaranteeMoneySlider')">
      Số tiền bảo lãnh không thể quá {{ customNumberFormat(max) }} VND.
    </div>
  </form>

  <!-- BTN -->
  <div class="btn-noti">
    <button
      class="btn confirm typo-button-3"
      [disabled]="value === 0 || !guaranteeMoneyForm.valid"
      [ngClass]="{ 'disabled-button': value === 0 || !guaranteeMoneyForm.valid }"
      (click)="openConfirmGuaranteeMoney(accountNumber, value)"
    >
      {{ 'MES-89' | translate }}
    </button>
    <button class="btn cancel typo-button-3" (click)="close()">{{ 'MES-74' | translate }}</button>
  </div>
</div>
