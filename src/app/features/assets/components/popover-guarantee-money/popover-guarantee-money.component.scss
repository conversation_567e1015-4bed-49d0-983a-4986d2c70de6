.popup-confirm-wrapper {
  border-radius: 6px;
  width: 400px;

  .header-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 16px 16px 24px;

    img {
      padding: 6px;
      cursor: pointer;
    }
  }

  .content-popup {
    padding: 24px 24px;
    border-bottom: 1px solid var(--color--other--divider);
    border-top: 1px solid var(--color--other--divider);
    display: flex;
    flex-direction: column;
    gap: 24px;

    .button-increase-reduce-wrap {
      display: flex;
      flex-direction: column;
      gap: 8px;
      width: 100%;

      .button-increase-reduce {
        display: flex;
        align-items: center;
        border: 1px solid var(--SHAv1-Other-Divider, #f1f2f6);
        border-radius: 8px;

        img {
          width: 44px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 8px 10px;
        }

        .money {
          flex: 1;
          padding: 10px 0;
          text-align: center;
          border: none;
        }
      }
    }

    .slider-wrap {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .orange {
        color: var(--color--brand--500);
      }

      .example-margin {
        width: 100%;
      }

      .purchasing-power {
        display: flex;
        gap: 4px;
        justify-content: end;
      }
    }

    .error {
      color: var(--color--accents--red);
    }
  }

  .btn-noti {
    padding: 16px 24px 6px 24px;
    display: flex;
    align-items: center;
    justify-content: end;
    text-align: center;
    gap: 24px;

    .btn {
      border-radius: 8px;
      padding: 6px 10px;
      cursor: pointer;
    }

    .confirm {
      background-color: var(--color--brand--500);
      color: var(--SHAv1-Neutral-White, var(--Colors-Grey-White, #fff));
    }

    .cancel {
      border: 1px solid var(--SHAv1-Other-Divider, #f1f2f6);
      background: var(--SHAv1-Neutral-White, #fff);
      color: var(--SHAv1-Text-Color-Default, #33343e);
    }
  }
}

.example-section {
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  align-items: center;
}

mat-slider {
  height: 32px;
}

::ng-deep {
  .mat-mdc-slider {
    border-radius: 100px;
    max-width: 330px;
    width: 100%;

    .mdc-slider__input {
      height: 32px;
    }

    .mdc-slider__track {
      width: 100%;
      left: 5px;
    }

    .mdc-slider__thumb {
      width: 32px;
      height: 32px;
      left: -5px;

      .mat-ripple {
        border-radius: 50%;
      }
    }

    .mdc-slider__track--inactive,
    .mdc-slider__track--active,
    .mdc-slider__track--active_fill {
      background-color: #b6bcc1;
    }
  }
}

.disabled-button {
  opacity: 0.5;
  pointer-events: none;
}
