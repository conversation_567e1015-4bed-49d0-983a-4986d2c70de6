import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { take } from 'rxjs';
import { DestroyService, DialogService } from 'src/app/core/services';
import { GuaranteeConfirmComponent } from 'src/app/shared/components/guarantee-confirm/guarantee-confirm.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { updateGuaranteeData } from '../../stores/asset.actions';

/**
 * PopoverGuaranteeMoneyComponent
 */
@Component({
  selector: 'app-popover-guarantee-money',
  templateUrl: './popover-guarantee-money.component.html',
  styleUrl: './popover-guarantee-money.component.scss',
})
export class PopoverGuaranteeMoneyComponent implements OnInit {
  value!: number;
  max!: number;
  min: number = 0;
  step: number = 50000;
  customNumberFormat = customNumberFormat;

  accountNumber!: string;
  guarantee!: number;
  purchasingPower!: number;

  guaranteeMoneyForm!: FormGroup;

  /**
   * Constructor
   * @param popoverRef - PopoverRef
   * @param dialogService DialogService
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(
    @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly dialogService: DialogService,
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly fb: FormBuilder
  ) {}

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    const { accountNumber, guaranteeMax, purchasingPower } = this.popoverRef.componentConfig.element;

    this.accountNumber = accountNumber;
    this.value = 0;
    this.max = guaranteeMax;
    this.purchasingPower = purchasingPower;

    this.guaranteeMoneyForm = this.fb.group({
      guaranteeMoney: [0, [Validators.required, Validators.max(this.max)]],
      guaranteeMoneySlider: [0, [Validators.max(this.max)]],
    });
  }

  /**
   * Updates the value property based on the event value.
   * @param {any} event - event
   */
  onSliderChange(event: any): void {
    this.value = event.value;
  }

  /**
   * Parses the input value to an integer and assigns it to `this.value`, or
   * assigns `this.min` if the input is not a valid number.
   * @param {any} event - event
   */
  onInputChange(event: any): void {
    const inputValue = event.target.value.replace(/\./g, '');
    const parsedValue = parseFloat(inputValue);

    if (!isNaN(parsedValue)) {
      this.value = parsedValue;
    } else {
      this.value = this.min;
    }

    event.target.value = customNumberFormat(this.value);
  }

  /**
   * The function decreases the value by the step amount, ensuring it does not go below the minimum
   * value.
   */
  decreaseValue(): void {
    if (this.value > this.min) {
      this.value = Math.max(this.min, this.value - this.step);
    }
  }

  /**
   * The function "increaseValue" increments the value by the step amount, up to the maximum value.
   */
  increaseValue(): void {
    if (this.value < this.max) {
      this.value = Math.min(this.max, this.value + this.step);
    }
  }

  /**
   * The close function closes a popover reference.
   */
  close(): void {
    this.popoverRef.close();
  }

  /**
   * OpenConfirmGuaranteeMoney
   * @param {string} accountNumber - accountNumber
   * @param {number} value - value
   */
  openConfirmGuaranteeMoney(accountNumber: string, value: number) {
    const ref = this.dialogService.openPopUp(GuaranteeConfirmComponent, {
      width: '360px',
      height: '297px',
      panelClass: [''],
      data: {
        action: 'grant',
        info: [accountNumber, value],
        title: ['MES-15', 'MES-264'],
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-147',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        this.popoverRef.close();
        const info = {
          type: v.type,
          accountNumber: v.data[0],
          value: v.data[1],
        };

        this.store.dispatch(updateGuaranteeData({ data: info }));
      });
  }
}
