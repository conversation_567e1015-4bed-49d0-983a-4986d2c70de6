import { Component, OnInit } from '@angular/core';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { OVERDUE_COLUMN_CONFIG } from '../../constant/overdue-column-config';

/**
 * OverDueDebtDetailComponent
 */
@Component({
  selector: 'app-overdue-debt-detail-component',
  templateUrl: './overdue-debt-detail.component.html',
  styleUrl: './overdue-debt-detail.component.scss',
})
export class OverDueDebtDetailComponent extends BaseTableComponent<any> implements OnInit {
  fakeData = [
    {
      loanId: '#1008',
      dueDate: '01/10/2025',
      status: 0,
      restDebt: 300000000,
    },
    {
      loanId: '#1009',
      dueDate: '01/03/2024',
      status: 1,
      restDebt: 300000000,
    },
  ];

  loanLabels = ['MES-317', 'MES-318', 'MES-319'];
  loanMoney = ['100000000', '300000000', '200000000'];
  customNumberFormat = customNumberFormat;

  /**
   * Constructor
   */
  constructor() {
    super();
  }
  /**
   *
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.columnConfigs = [
      ...OVERDUE_COLUMN_CONFIG
    ];
  }

  /**
   * @param tag
   */
  override clickButton(tag: string): void {
    console.log("tag overdue debt detail", tag);
  }
}
