import { Component } from '@angular/core';
import { customNumberFormat } from '../../../../shared/utils/currency';

/**
 * FeeDebtDetailComponent
 */
@Component({
  selector: 'app-fee-debt-detail-component',
  templateUrl: './fee-debt-detail.component.html',
  styleUrl: './fee-debt-detail.component.scss',
})
export class FeeDebtDetailComponent {
  customNumberFormat = customNumberFormat;
  /**
   *
   */
  constructor() {}

  feeDebtLabel = ['MES-333', 'MES-290', 'MES-289'];
  money = [0, 1000000, 0];
}
