:host {
  width: 100%;
  height: 100%;
}

.section-wrap {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .section {
    border-radius: 8px;
    padding: 16px;
    background: var(--color--background--1);

    &.from {
      mat-form-field {
        width: 220px;
      }
    }

    .title {
      color: var(--color--text--subdued);
      margin-bottom: 16px;
    }

    .content {
      display: flex;
      gap: 20px;
      align-items: center;
      justify-content: space-between;

      &.flex-col {
        flex-direction: column;
      }

      .sub-title {
        font-size: 12px;
        color: var(--color--text--subdued);
        margin-bottom: 10px;
      }

      .asset-amount {
        display: flex;
        gap: 8px;
        align-items: center;
        flex-wrap: wrap;
      }

      .badge {
        display: inline-flex;
        padding: 4px 12px;
        justify-content: center;
        align-items: center;
        border-radius: 16px;
        background: var(--MacOS-Accents-Yellow---Dark, #ffd60a);

        &.allocated-from {
          font-weight: 400;
          width: 140px;
          background: var(--SHAv1-Cyan-cyan-200, #b1e1f8);
        }

        &.allocated-to {
          font-weight: 400;
          width: 140px;
          background: var(--SHAv1-Success-Color-Green-300, #bae7a3);
        }
      }
    }
  }
}

.dialog-footer {
  .btn {
    width: 160px;
  }
}

.form-field-to {
  ::ng-deep {
    .mat-mdc-text-field-wrapper {
      .mat-mdc-select {
        span {
          font-size: 12px;
        }
      }
    }
  }
}

.form-field-note,
.form-field-from {
  ::ng-deep {
    .mat-mdc-text-field-wrapper {
      .mdc-floating-label {
        font-size: 12px;
      }
    }
  }
}

mat-option {
  ::ng-deep {
    .mdc-list-item__primary-text {
      font-size: 12px;
    }
  }
}
