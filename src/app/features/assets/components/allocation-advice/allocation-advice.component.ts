import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { combineLatest, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { IOption } from 'src/app/shared/models/dropdown-item.model';
import { DialogService } from '../../../../core/services/dialog.service';
import {
  CONVERT_ASSET_TYPE_TO_LABEL,
  EAssetType,
  IAllocationRef,
  IAllocationResult,
  IAssetDialogData,
} from '../../constant/assets';
import { AllocationResultComponent } from '../allocation-result/allocation-result.component';

/**
 * Declare AllocationAdviceComponent
 */
@Component({
  selector: 'app-allocation-advice',
  templateUrl: './allocation-advice.component.html',
  styleUrl: './allocation-advice.component.scss',
})
export class AllocationAdviceComponent implements OnInit {
  CONVERT_ASSET_TYPE_TO_LABEL = CONVERT_ASSET_TYPE_TO_LABEL;

  assetData: IAssetDialogData;
  adviceForm: FormGroup;

  options: IOption[] = [
    {
      label: 'MES-78',
      value: EAssetType.CASH,
    },
    {
      label: 'MES-91',
      value: EAssetType.STOCK,
    },
    {
      label: 'MES-95',
      value: EAssetType.BOND,
    },
    {
      label: 'MES-93',
      value: EAssetType.DERIVATIVE,
    },
    {
      label: 'MES-92',
      value: EAssetType.CERTIFICATE,
    },
  ];

  allocatedSource: IAllocationRef | null = null;
  allocatedTarget: IAllocationRef | null = null;

  /**
   * Initialize the form
   * @param fb
   * @param _destroy
   * @param dialogService
   * @param dialogRef
   * @param {IAssetDialogData} data
   */
  constructor(
    private readonly fb: FormBuilder,
    private readonly _destroy: DestroyService,
    private readonly dialogService: DialogService,
    private readonly dialogRef: MatDialogRef<AllocationAdviceComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IAssetDialogData
  ) {
    this.assetData = data;
    this.options = this.options.filter((option) => option.value !== data.type);

    this.adviceForm = this.initForm();
    this.allocatedSource = this.initAllocatedSource();
  }

  /**
   * Khởi tạo Form
   */
  private initForm() {
    return this.fb.group({
      allocatedFrom: [this.assetData.type, Validators.required],
      allocatedAmount: [null, [Validators.required, Validators.max(this.assetData.amount)]],
      testAmount: [71500.12],
      allocatedTo: [null, Validators.required],
      note: [null, Validators.required],
    });
  }

  /**
   * Khởi tạo giá trị tiền & phần trăm của tài sản nguồn
   */
  private initAllocatedSource() {
    return {
      type: this.assetData.type,
      oldValue: {
        amount: this.assetData.amount,
        percentage: this.assetData.percentage,
      },
    };
  }

  /**
   * Get form control "allocatedAmount"
   */
  get allocatedAmount() {
    return this.adviceForm.get('allocatedAmount');
  }

  /**
   * Get form control "allocatedTo"
   */
  get allocatedTo() {
    return this.adviceForm.get('allocatedTo');
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.setupSubscribersOnAdviceForm();
  }

  /**
   * Subscribe thay đổi của ô "Số tiền phân bổ" và "Loại tài sản" để tính toán phần tổng quan (summary)
   */
  private setupSubscribersOnAdviceForm() {
    combineLatest([this.allocatedAmount!.valueChanges, this.allocatedTo!.valueChanges])
      .pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this._destroy))
      .subscribe(([allocatedAmount, allocatedTo]) => {
        this.calculateAllocateResult({ allocatedAmount, allocatedTo });
      });
  }

  /**
   * Cách làm dưới đây mang tính chất tạm thời: 
      - lặp qua từng nested-key trong property "asset" được truyền vào khi mở dialog
      - tìm các key contains "allocatedTo"
   * @param allocatedTo
   */
  private findMatchedAssetData(allocatedTo: EAssetType): { amount: number; percentage: number } {
    const nestedKeyList = Object.keys(this.assetData.asset);
    const matchedKey = nestedKeyList.filter((key) => key.toLowerCase().includes(allocatedTo.toLowerCase()));

    const matchedAssetData = { amount: 0, percentage: 0 };

    matchedKey.forEach((key) => {
      if (key.toLowerCase().includes('percent')) {
        matchedAssetData.percentage = this.assetData.asset[key as keyof typeof this.assetData.asset] as number;
      } else {
        matchedAssetData.amount = this.assetData.asset[key as keyof typeof this.assetData.asset] as number;
      }
    });

    return matchedAssetData;
  }

  /**
   * Tính toán kết quả phân bổ sau khuyến nghị
   * @param root0
   * @param root0.allocatedAmount
   * @param root0.allocatedTo
   */
  private calculateAllocateResult({
    allocatedAmount,
    allocatedTo,
  }: {
    allocatedAmount: number;
    allocatedTo: EAssetType;
  }) {
    const { nav } = this.assetData.asset;

    if (allocatedAmount > this.assetData.amount || !allocatedAmount) {
      this.allocatedSource!.newValue = undefined;
      this.allocatedTarget!.newValue = undefined;
      return;
    }

    if (!allocatedAmount && this.allocatedSource) {
      this.allocatedSource.newValue = undefined;
    }

    if (!allocatedAmount && this.allocatedTarget) {
      this.allocatedTarget.newValue = undefined;
    }

    if (allocatedTo) {
      // Xác định giá trị ban đầu của tài sản ĐÍCH để hiển thị lên UI
      const matchedAssetData = this.findMatchedAssetData(allocatedTo);

      // Bước 2: gán giá trị cho biến this.allocatedTarget để update UI
      this.allocatedTarget = {
        type: allocatedTo,
        oldValue: {
          amount: matchedAssetData.amount,
          percentage: matchedAssetData.percentage,
        },
      };
    }

    if (allocatedAmount) {
      const currentSourceAmount = this.allocatedSource?.oldValue?.amount ?? 0;
      const newSourceAmount = currentSourceAmount - allocatedAmount;
      const newSourcePercentage = (newSourceAmount / nav) * 100;

      this.allocatedSource = {
        ...this.allocatedSource!,
        newValue: {
          amount: newSourceAmount,
          percentage: newSourcePercentage,
        },
      };

      const currentTargetAmount = this.allocatedTarget?.oldValue?.amount ?? 0;
      const newTargetAmount = currentTargetAmount + allocatedAmount;
      const newTargetPercentage = (newTargetAmount / nav) * 100;

      this.allocatedTarget = {
        ...this.allocatedTarget!,
        newValue: {
          amount: newTargetAmount,
          percentage: newTargetPercentage,
        },
      };
    }
  }

  /**
   * Xử lý gửi khuyến nghị
   */
  onSendAdvice() {
    const allocateResult: IAllocationResult = {
      title: 'MES-100',
      allocatedSource: this.allocatedSource!,
      allocatedTarget: this.allocatedTarget!,
      sentBy: 'duongnc',
      sentDate: new Date(),
      accountNumber: this.assetData.asset.accountNumber,
      customerName: this.assetData.asset.customerName,
      note: this.adviceForm.value.note,
    };

    this.dialogRef.close();

    this.dialogService.open(AllocationResultComponent, {
      width: '560px',
      data: allocateResult,
    });
  }
}
