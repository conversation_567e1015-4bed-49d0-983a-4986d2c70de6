<div class="dialog-wrap allocate-advice">
  <div class="dialog-header">
    <div class="title-cls typo-body-14">{{ 'MES-76' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="dialog-body">
    <form class="section-wrap" [formGroup]="adviceForm">
      <div class="section from">
        <div class="title typo-body-12">{{ 'MES-77' | translate }}</div>

        <div class="content">
          <div class="left">
            <div class="sub-title typo-body-12">{{ CONVERT_ASSET_TYPE_TO_LABEL[assetData.type] | translate }}</div>
            <div class="asset-amount">
              <span class="typo-body-15">{{ assetData.amount | numberFormat }} VND</span>
              <span class="badge typo-body-15">{{ assetData.percentage | numberFormat : 'percent' }}</span>
            </div>
          </div>

          <div class="right">
            <app-form-control>
              <mat-form-field class="form-field-from">
                <mat-label>{{ 'MES-79' | translate }}</mat-label>
                <input matInput formControlName="allocatedAmount" mask="separator.2" class="fs-12 typo-field-5" />
              </mat-form-field>
            </app-form-control>
          </div>
        </div>
      </div>

      <div class="section to">
        <div class="title typo-body-12">{{ 'MES-80' | translate }}</div>

        <div class="content">
          <app-form-control>
            <mat-form-field class="form-field-to">
              <mat-select formControlName="allocatedTo" [placeholder]="'MES-80' | translate">
                @for (option of options; track option) {
                <mat-option [value]="option.value">{{ option.label | translate }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          </app-form-control>
        </div>
      </div>

      <div class="section note">
        <div class="title typo-body-12">{{ 'MES-82' | translate }}</div>

        <div class="content">
          <app-form-control>
            <mat-form-field class="form-field-note fs-12">
              <mat-label>{{ 'MES-82' | translate }}</mat-label>
              <textarea rows="4" matInput formControlName="note"></textarea>
            </mat-form-field>
          </app-form-control>
        </div>
      </div>

      <app-allocation-summary
        [allocatedSource]="allocatedSource"
        [allocatedTarget]="allocatedTarget"
      ></app-allocation-summary>
    </form>
  </div>

  <div class="dialog-footer justify-center">
    <button [disabled]="adviceForm.invalid" class="btn primary typo-button-3" (click)="onSendAdvice()">
      {{ 'MES-73' | translate }}
    </button>
    <button mat-dialog-close class="btn outline typo-button-3">{{ 'MES-74' | translate }}</button>
  </div>
</div>
