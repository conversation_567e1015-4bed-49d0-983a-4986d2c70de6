.popup-overdue-container {
  width: 500px;
  height: 600px;
  display: flex;
  z-index: 100;
  flex-direction: column;
  .overdue-header {
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--color--other--divider);
    padding: 16px 16px 16px 24px;
  }
  .popup-overdue-body {
    padding: 24px;
    display: flex;
    gap: 24px;
    flex-direction: column;
    overflow-y: auto;
    .loan-info-container {
      .loan-title-cls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title-loan {
          color: var(--color--text-vibrant--secondary);
        }
        .export-icon {
          filter: invert(59%) sepia(80%) saturate(2137%) hue-rotate(347deg) brightness(99%) contrast(94%);
          cursor: pointer;
        }
      }

      .loan-content {
        border-radius: 8px;
        background-color: #f8fafd;
        border: 1px solid #f1f2f6;
        padding: 16px 24px 24px 24px;
        .loan-content-info {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 16px 24px;
          margin-bottom: 16px;
          .loan-item-detail {
            display: flex;
            flex-direction: column;
            gap: 4px;
            .content-money {
              font-size: 14px;
              font-weight: 500;
              line-height: 24px;
            }
          }
        }
        .loan-table-view {
          border: 0.5px solid #d1d1d1;
          overflow: hidden !important;
          .table-custom-cls {
            overflow: hidden;
            ::ng-deep {
              .in-due-date {
                span {
                  background-color: var(--color--accents--green);
                  border-radius: 16px;
                  white-space: nowrap;
                  text-wrap: nowrap;
                  display: inline-flex;
                  align-items: center;
                  justify-content: center;
                  max-width: 140px;

                  input {
                    text-align: center !important;
                  }
                }
              }
              .out-of-date {
                span {
                  background-color: var(--color--accents--red);
                  border-radius: 16px;
                  white-space: nowrap;
                  text-wrap: nowrap;
                  display: inline-flex;
                  align-items: center;
                  justify-content: center;
                  max-width: 140px;
                  input {
                    text-align: center !important;
                  }
                }
              }
            }
          }
        }
      }
    }
    .debt-info-container {
      .debt-title-cls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title-debt {
          color: #808080;
        }
        .export-icon {
          filter: invert(59%) sepia(80%) saturate(2137%) hue-rotate(347deg) brightness(99%) contrast(94%);
          cursor: pointer;
        }
      }
      .debt-content {
        padding: 16px 24px 24px 24px;
        border-radius: 8px;
        background-color: #f8fafd;
        border: 1px solid #f1f2f6;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px 24px;
        .margin-debt-cls,
        .all-debt-cls,
        .overdue-margin-cls,
        .interest-margin-cls,
        .loan-debt-cls,
        .loan-interest-cls,
        .debt-another-overdue-cls,
        .advane-debt-cls {
          display: flex;
          flex-direction: column;
          gap: 4px;
          overflow: hidden;
        }
      }
    }
  }
}

.content-money {
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
}

.total-debt-decrease {
  display: flex;
  gap: 10px;
  align-items: center;
  overflow: hidden;
  span {
    color: var(--color--accents--red);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
