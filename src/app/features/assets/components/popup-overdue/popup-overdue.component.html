<div class="popup-overdue-container">
  <div class="overdue-header">
    <div class="title-cls typo-body-14">{{ 'MES-285' | translate }}</div>
  </div>
  <div class="popup-overdue-body">
    <div class="loan-info-container">
      <div class="loan-title-cls">
        <div class="title-loan typo-body-14">{{ 'MES-329' | translate }}</div>

        <img class src="./assets/icons/export-icon.svg" class="export-icon" alt="" />
      </div>

      <div class="loan-content">
        <div class="loan-content-info">
          <ng-container
            *ngFor="let loanLabel of loanLabels; let i = index"
            [ngTemplateOutlet]="loanDetail"
            [ngTemplateOutletContext]="{ label: loanLabel, money: loanMoney[i] }"
          ></ng-container>
        </div>
        <div class="loan-table-view">
          <sha-grid
            [showIndexColumn]="false"
            [columnConfigs]="columnConfigs"
            [data]="data"
            class="table-custom-cls"
            style="overflow: hidden !important"
          >
          </sha-grid>
        </div>
      </div>
    </div>

    <div class="debt-info-container">
      <div class="debt-title-cls">
        <div class="title-debt typo-body-14">{{ 'MES-328' | translate }}</div>
        <div class="icon-wrapper">
          <img src="./assets/icons/export-icon.svg" class="export-icon" alt="" />
        </div>
      </div>
      <div class="debt-content">
        <!-- Nợ vay Margin -->
        <div class="margin-debt-cls">
          <div class="label-debt typo-body-12">{{ 'MES-320' | translate }}</div>
          <div class="content-money">{{ customNumberFormat(200000000) + ' VND' }}</div>
        </div>
        <!-- +-Tổng dư nợ -->
        <div class="all-debt-cls">
          <div class="label-debt typo-body-12">{{ 'MES-321' | translate }}</div>
          <div class="all-debt-money content-money">
            <ng-container [ngTemplateOutlet]="allDebt"> </ng-container>
          </div>
        </div>
        <!-- Dư nợ Margin quá hạn -->
        <div class="overdue-margin-cls">
          <div class="label-debt typo-body-12">{{ 'MES-322' | translate }}</div>
          <div class="total-debt-decrease content-money">{{ customNumberFormat(200000000) + ' VND' }}</div>
        </div>

        <!-- Lãi vay Margin -->
        <div class="interest-margin-cls">
          <div class="label-debt typo-body-12">{{ 'MES-323' | translate }}</div>
          <div class="total-debt-decrease content-money">{{ customNumberFormat(200000000) + ' VND' }}</div>
        </div>
        <!-- Nợ vay khác -->
        <div class="loan-debt-cls">
          <div class="label-debt typo-body-12">{{ 'MES-324' | translate }}</div>
          <div class="content-money">-</div>
        </div>
        <!-- Lãi vay khác -->
        <div class="loan-interest-cls">
          <div class="label-debt typo-body-12">{{ 'MES-325' | translate }}</div>
          <div class="content-money">-</div>
        </div>
        <!-- Dư nợ trước quá hạn -->
        <div class="debt-another-overdue-cls">
          <div class="label-debt typo-body-12">{{ 'MES-326' | translate }}</div>
          <div class="content-money">-</div>
        </div>
        <!-- Dư nợ ứng trước -->
        <div class="advane-debt-cls">
          <div class="label-debt typo-body-12">{{ 'MES-326' | translate }}</div>
          <div class="content-money">-</div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #loanDetail let-label="label" let-money="money">
  <div class="loan-item-detail">
    <div class="label-loan typo-body-12">{{ label | translate }}</div>
    <div class="content-money">{{ customNumberFormat(money) + ' VND' }}</div>
  </div>
</ng-template>

<!-- +- Tổng dư nợ -->
<ng-template #allDebt let-label="label">
  @if(allDebt) {
  <div>
    <ng-container>
      <div class="total-debt-decrease typo-body-14">
        <img src="./assets/icons/down.svg" alt="document-logo" />
        <span class="typo-body-14">
          {{ '+' + customNumberFormat(9000000) + ' VND' + ' + ' + customNumberFormat(900000000) }}
        </span>
      </div>
    </ng-container>
  </div>
  }
</ng-template>
