import { Component, Inject, Input, OnInit, Optional } from '@angular/core';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { OVERDUE_COLUMN_CONFIG } from '../../constant/overdue-column-config';

/**
 *
 */
@Component({
  selector: 'app-popup-overdue-component',
  templateUrl: './popup-overdue.component.html',
  styleUrl: './popup-overdue.component.scss',
})
export class PopupOverdueComponent extends BaseTableComponent<any> implements OnInit {
  loanLabels = ['MES-317', 'MES-318', 'MES-319'];
  loanMoney = ['100000000', '300000000', '200000000'];
  @Input() text: string = '';
  /**
   * Constructor
   * @param popoverRef
   */
  constructor(@Optional() @Inject(PopoverRef) public popoverRef: PopoverRef) {
    super();
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }
  fakeData = [
    {
      loanId: '#1008',
      dueDate: '01/10/2025',
      status: 0,
      restDebt: 300000000,
    },
    {
      loanId: '#1009',
      dueDate: '01/03/2024',
      status: 1,
      restDebt: 300000000,
    },
  ];
  customNumberFormat = customNumberFormat;

  /**
   * The Onit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.columnConfigs = [
      ...OVERDUE_COLUMN_CONFIG

    ];
  }

  /**
   * @param tag
   */
  override clickButton(tag: string): void {
    console.log('tag popup overdue', tag);
  }

  // onMouseEnter() {
  //   console.log(this.popoverRef);
  //   this.popoverRef.overlay.hostElement.addEventListener('mouseenter', () => {});
  // }

  // onMouseLeave() {
  //   this.popoverRef.overlay.hostElement.addEventListener('mouseleave', () => {
  //     this.popoverRef.close();
  //   });
  // }
}
