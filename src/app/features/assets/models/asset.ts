import { ESearchType } from '../constant/assets';

export interface IAssetState {
  guarantee: IGuaranteeInfo;
  searchValue: string;
  filterAssetInfo: IFilterAssetInfoParam | null;
  filteredAssetInfoData: any[];
  filterAccountStatus: IFilterAccountStatusParam;
  filteredAccountStatusData: any[];
  filterTransaction: IFilterTransactionParam;
  filteredTransactionData: any[];
  filterMoneyInfo: IFilterMoneyInfoParam | null;
  filteredMoneyInfoData: any[];
  filterDepositInfo: IFilterDepositInfoParam;
  filteredDepositInfoData: any[];
  filterDebtInfo: IFilterDebtInfoParam;
  filteredDebtInfoData: any[];
  filterLoanInfo: IFilterLoanInfoParam;
  filteredLoanInfoData: any[];
  filterInvestment: IFilterInvestmentParam | null;
  filteredInvestmentData: any[];
  filterAssetAllocation: IFilterAssetAllocationParam;
  filteredAssetAllocationData: any[];
  filterPurchasingPower: IFilterPurchasingPowerInfoParam | null;
  filteredPurchasingPowerData: any[];

  fakeList: IAssetInfoData[];
  pageIndexAssetInfo: number;
  pageIndexTransactionInfo: number;
  pageIndexPurchasingPower: number;
  pageIndexMoneyInfo: number;
  pageIndexInvestmentPortfolio: number;

  // AssetInfo
  assetsInfoList: IAssetInfoResponse[];
  // Thông tin giao dịch
  transactionList: ITransactionInfoResponse[];
  // Danh mục đầu tư
  investmentPortfolioList: IDataInvestmentPortfolio[];
  subInvestmentPortfolioList: IDataInvestmentPortfolio[];
  // Thông tin Tiền
  moneyInfoList: IMoneyInfoResponse[];
  // Thông tin Dư nợ
  debtInfoList: IDebtInfoResponse[];
  // Thông tin Sức mua
  purchasingPowerList: IPurchasingPowerResponse[];

  assetInfoDetail: any;

  assetSumInfo: IAssetSumInfo | null;

  typeSearch: ESearchType;

  moneySumInfo: IMoneySumInfo | null;

  transactionSumInfo: ITransactionsSumInfo | null;
}

export interface IFilterAssetInfoParam {
  accountNo: string[] | null;
  purchasingPower: IRangeFilter;
  navValue: IRangeFilter;
  stockValue: IRangeFilter;
  marginStockValue: IRangeFilter;
  cashValue: IRangeFilter;
  debtValue: IRangeFilter;
  marginRateValue: IRangeFilter;
  assetsValue: IRangeFilter;
  isFilter?: boolean;
  index?: string;
}

export interface IGuaranteeInfo {
  type: string | null;
  accountNumber: string | null;
  value: number | null;
}

type StringNumberNull = string | number | null;

export interface IRangeFilter {
  from?: StringNumberNull;
  to?: StringNumberNull;
  start?: StringNumberNull;
  end?: StringNumberNull;
}

export interface IFilterAccountStatusParam {
  isFilter: boolean;
  customers: string[] | null;
  marginRate: IRangeFilter;
  marginRateViolationPercent: IRangeFilter;
  overDueDebt: string[] | null;
  feeDebt: string[] | null;
  marginRateViolation: string[] | null;
}

export interface IFilterTransactionParam {
  isFilter: boolean;
  customers: string[] | null;
  totalTransactionValue: IRangeFilter;
  buyTransactionValue: IRangeFilter;
  sellTransactionValue: IRangeFilter;
  revenueFee: IRangeFilter;
  netTransactionFee: IRangeFilter;
  brokerCommission: IRangeFilter;
  stockExchangeFee: IRangeFilter;
  depositoryFee: IRangeFilter;
  smsFee: IRangeFilter;
  index?: string;
}

export interface IFilterMoneyInfoParam {
  accountNumber: string[] | null;
  purchasingPower: IRangeFilter;
  cashValue: IRangeFilter;
  cashBalanceValue: IRangeFilter;
  awaitingAdvanceValue: IRangeFilter;
  t0: IRangeFilter;
  t1: IRangeFilter;
  t2: IRangeFilter;
  blockedCashValue: IRangeFilter;
  notMatchedValue: IRangeFilter;
  awaitingDividendsValue: IRangeFilter;
  unpaidValue: IRangeFilter;
  isFilter?: boolean;
}

export interface IFilterPurchasingPowerInfoParam {
  accountNo: string[] | null;
  navValue: IRangeFilter;
  cashValue: IRangeFilter;
  isFilter?: boolean;
  purchasingPower: IRangeFilter;
  guarantee: IRangeFilter;
  usedGuarantee: IRangeFilter;
  toSubmitGuarantee: IRangeFilter;
  index?: string;
}

export interface IFilterDepositInfoParam {
  isFilter: boolean;
  customersSelect: string[] | null;
  marginRate: IRangeFilter;
  nav: IRangeFilter;
  cash: IRangeFilter;
  purchasingPower: IRangeFilter;
  eightyAssets: IRangeFilter;
  eightyInvest: IRangeFilter;
  notSubmittedMargin: IRangeFilter;
  marginStatus: string[] | null;
  vsdcMargin: IRangeFilter;
  canWithdrawMargin: IRangeFilter;
  toSubmittedMargin: IRangeFilter;
}

export interface IFilterDebtInfoParam {
  isFilter: boolean;
  customersSelect: string[] | null;
  marginRate: IRangeFilter;
  guaranteedAsset: IRangeFilter;
  debt: IRangeFilter;
  marginLoanDebt: IRangeFilter;
  marginInterest: IRangeFilter;
  percentDebtPerNav: IRangeFilter;
  percentDebtPerMargin: IRangeFilter;
}

export interface IFilterLoanInfoParam {
  isFilter: boolean;
  customersSelect: string[] | null;
  loanStatus: string[] | null;
  dueDate: IRangeFilter;
  openingDebt: IRangeFilter;
  remainDebt: IRangeFilter;
  paidDebt: IRangeFilter;
  tempLoanFee: IRangeFilter;
  tempInterest: IRangeFilter;
  loanDate: IRangeFilter;
}

export interface IFilterAssetAllocationParam {
  isFilter: boolean;
  customers: string[] | null;
  nav: IRangeFilter;
  proportionOfCash: IRangeFilter;
  proportionOfStocks: IRangeFilter;
  derivativeProportion: IRangeFilter;
  bondProportion: IRangeFilter;
  proportionOfFundCertificates: IRangeFilter;
}

export interface IFilterInvestmentParam {
  accountNo: string[] | null;
  stockCode: string[] | null;
  curPriValue: IRangeFilter;
  capitalInvestmentValue: IRangeFilter;
  investValue: IRangeFilter; // Lãi / lỗ
  percentInvestValue: IRangeFilter; // % Lãi lỗ
  sellAbleQtySumValue: IRangeFilter; // Khối lượng giao dịch
  isFilter?: boolean;
}

export interface IDataSetChart {
  label: string;
  backgroundColor?: string | string[];
  hoverBackgroundColor?: string | string[];
  data: number[];
  borderColor?: string | string[];
  borderWidth?: number;
  maxBarThickness?: number;
  borderSkipped?: 'start' | 'end' | 'left' | 'right' | 'bottom' | 'top' | 'middle' | boolean;
  barPercentage?: number;
  categoryPercentage?: number;
  fill?: boolean;
  tension?: number;
  dataConfig?: string[];
  cutout?: string;
  radius?: string;
  borderRadius?: number;
}
export interface ITypeOption {
  name?: string;
  value?: string | number | boolean;
  classCustom?: string;
}

export interface IAssetInfoData {
  customerName: string;
  accountNumber: string;
  assetsInfo: IAssetInfo;
  accountStatus: IAccountStatus;
  transactionInfo: ITransactionInfo;
  moneyInfo: IMoneyInfo;
  purchasingPowerInfo: IPurchasingPower;
  depositInfo: IDespositInfo;
  debtInfo: IDebtInfo;
  loanInfo: ILoanInfo;
  investmentInfo: IInvestmentInfo;
  propotionAsset: IProportionAsset;
}

export interface IAssetInfo {
  nav: number;
  stock: number;
  purchasingPower: number;
  cash: number;
  marginNav: IMarginNavStock;
  marginStock: IMarginNavStock;
  assets: number;
  debt: number;
  marginRate: number;
}

interface IMarginNavStock {
  numberMargin: number | null;
  percentMargin: number | null;
}

export interface IAccountStatus {
  marginRate: number;
  marginRateViolation: IMarginRateViolation;
  overDueDebt: number;
  feeDebt: number;
}

export interface IMarginRateViolation {
  type: number;
  percentMarginRateViolation: number | null;
}

export interface ITransactionInfo {
  totalTransactionValue: number;
  buyTransactionValue: number;
  sellTransactionValue: number;
  revenueFee: number;
  stockExchangeFee: number;
  depositoryFee: number;
  dividendTax: number;
  smsFee: number;
  netTransactionFee: number;
  brokerCommission: number;
  otherFees: number;
}

export interface IMoneyInfo {
  purchasingPower: number;
  cash: number;
  recentCash: number;
  marginCash: IMarginNavStock;
  cashBalance: number;
  awaitingAdvance: number;
  t0: number;
  t1: number;
  t2: number;
  awaitingDividends: number;
  blockedCash: number;
  notMatched: number;
  unpaid: number;
  guarantee: number;
  usedGuarantee: number;
  toSubmitGuarantee: number;
}

export interface IPurchasingPower {
  purchasingPower: number;
  cash: number;
  recentCash: number;
  marginCash: IMarginNavStock;
  nav: number;
  guarantee: number;
  guaranteeMax: number;
  usedGuarantee: number;
  toSubmitGuarantee: number;
}
export interface IDespositInfo {
  marginRate: number;
  nav: number;
  cash: number;
  purchasingPower: number;
  eightyAssets: number;
  marginEightyAssets: IMarginNavStock;
  eightyInvest: number;
  marginClosedEightyInvest: IMarginNavStock;
  marginUnclosedEightyInvest: IMarginNavStock;
  marginStatus: number;
  notSubmittedMargin: number;
  vsdcMargin: number;
  canWithdrawMargin: number;
  toSubmittedMargin: number;
}

export interface IDebtInfo {
  marginRate: number;
  guaranteedAsset: number;
  recentDebt: number;
  debt: number;
  marginDebt: IMarginNavStock;
  marginLoanDebt: number;
  marginInterest: number;
  overdueMarginLoanDebt: number;
  otherLoanDebt: number;
  otherInterest: number;
  overdueOtherLoanDebt: number;
  advanceDebt: number;
  advanceInterest: number;
  smsFee: number;
  depositoryFee: number;
  numberStock: number;
  percentDebtPerNav: number;
  percentDebtPerMargin: number;
}

export interface ILoanInfo {
  dueDate: string;
  paidDebt: number;
  tempInterest: number;
  remainDebt: number;
  openingDebt: number;
  debtId: string | null;
  tempLoanFee: number;
  status: number;
  children?: ILoanInfo[];
}

export interface IInvestmentInfo {
  subAccount: number;
  price: number;
  capitalInvestment: number;
  children: IChildrenInvestementInfo[];
}

interface IChildrenInvestementInfo {
  subAccount: string;
  stockCode: string;
  price: number;
  capitalInvestment: number;
  invest: number;
  percentInvest: number;
  tradableVolume: number;
  totalVolume: number;
  t0Volume: number;
  t1Volume: number;
  t2Volume: number;
  costPrice: number;
  portfolioProportion: number;
  capitalStructure: number;
  openingPrice: number;
  currentPrice: number;
}

export interface IProportionAsset {
  nav: number;
  proportionOfCash: ITypeAsset;
  bondProportion: ITypeAsset;
  derivativeProportion: ITypeAsset;
  proportionOfFundCertificates: ITypeAsset;
  proportionOfStocks: ITypeAsset;
  children?: any;
}

interface ITypeAsset {
  numberProportion: number;
  percentProportion: number;
  type: string;
}

export interface IAssetInfoResponse {
  id: string;
  accountNumber: string;
  customerName: string;
  subAccountNumber?: string;
  subAccount?: string;
  purchasingPower: number;
  firstNav: number;
  nav: number;
  marginNav: {
    numberMargin: number;
    percentMargin: number;
  };
  firstStock: number;
  stock: number;
  marginStock: {
    numberMargin: number;
    percentMargin: number;
  };
  cash: number;
  debt: number;
  marginRate: number;
  assets: number;
  children: IAssetInfoResponse[];
}

export interface IMoneyInfoResponse {
  id?: string;
  accountNumber: string;
  subAccNumber?: string;
  subAccount?: string;
  customerName?: string;
  firstCash: number;
  cash: number;
  marginCash: {
    numberMargin: number;
    percentMargin: number;
  };
  cashBalance: number;
  awaitingDividends: number;
  blockedCash: number;
  notMatched: number;
  unpaid: number;
  awaitingAdvance: number;
  children: IMoneyInfoResponse[];
}

export interface IDebtInfoResponse {
  id?: string;
  accountNumber: string;
  subAccNumber?: string;
  customerName?: string;
  marginRate: number;
  guaranteedAsset: number;
  firstDebt: number;
  debt: number;
  marginDebt: {
    numberMargin: number;
    percentMargin: number;
  };
  marginLoanDebt: number;
  marginInterest: number;
  overdueMarginLoanDebt: number;
  otherLoanDebt: number;
  otherInterest: number;
  overdueOtherLoanDebt: number;
  advanceDebt: number;
  advanceInterest: number;
  smsFee: number;
  depositoryFee: number;
  percentDebtPerNav: number;
  percentDebtPerMargin: number;
  children: IDebtInfoResponse[];
}

export interface IPayloadInfoSubAccount {
  accountNumber: string;
  subAccount: string;
  brokerCode: string;
  customerName?: string;
}

export interface IInvestmentPayload {
  brokerCode: string[];
  stockCodes: string[];
}

export interface ISubInvestmentPayload {
  brokerCode: string;
  stockCode: string;
  filterPortfolioInvestmentDTO?: IFilterInvestmentParam | { accountNo: string[] };
}

export interface ICustomerInfos {
  accountNumber: string;
  subAccount: string[];
  customerName: string;
}
export interface ISumTransactionPayLoad {
  customerInfos: ICustomerInfos[];
  brokerCode: string[];
}

export interface IPayloadAsset {
  brokerCode: string[];
  accountNumbers: string[];
}

export interface IPayloadAssetInfo {
  brokerCodes: string[];
  accountNumbers: string[];
  filterAssetsInfo: IFilterAssetInfoParam | null;
}

export interface IPayloadMoneyInfo {
  brokerCodes: string[];
  accountNumbers: string[];
  filterMoneyInfo: IFilterMoneyInfoParam | null;
}

export interface IPayloadPurchasingPower {
  brokerCodes: string[];
  accountNumbers: string[];
  filterPurchasingPower: IFilterPurchasingPowerInfoParam | null;
}

export interface IPayloadInvestment {
  stockCodes: string[];
  brokerCodes: string[];
  filterPortfolioInvestmentDTO:
    | IFilterInvestmentParam
    | null
    | {
        searchValue: string;
        stockCode: string[];
        accountNo: string[];
      };
}

export interface ITransactionInfoResponse {
  id?: string;
  accountNumber: string;
  customerName: string;
  totalTransactionValue: number;
  buyTransactionValue: number;
  sellTransactionValue: number;
  revenueFee: number;
  stockExchangeFee?: number;
  subAccount?: string;
  depositoryFee: number;
  smsFee: number;
  netTransactionFee?: number;
  otherFees: number;
  children: ITransactionInfoResponse[];
}

export interface IPurchasingPowerResponse {
  id?: string;
  accountNumber: string;
  subAccount?: string;
  nav: number;
  cash: number;
  purchasingPower: number;
  usedGuarantee: number;
  toSubmitGuarantee: number;
  customerName: 'string';
  children: IPurchasingPowerResponse[];
}

export interface IInvestmentPortfolioResponse {
  page: number;
  pageSize: number;
  totalPages: number;
  data: IDataInvestmentPortfolio[];
}

export interface IDataInvestmentPortfolio {
  stockCode: string;
  subAccount: number;
  accountNumber: string;
  capitalInvestment: number;
  tradableVolume: number;
  totalVolume: number;
  t0Volume: number;
  t1Volume: number;
  t2Volume: number;
  costPrice: number;
  currentPrice: number;
  portfolioProportion: number;
  capitalStructure: number;
  children: IDataInvestmentPortfolio[];
  stockCodeParent?: string;
  id: string;
  accQty: number;
  isCallChildren?: boolean;
}

// Thông tin sức mua lấy ở thông tin tài sản
export interface IPurchasingPowerAssetInfo {
  accountNumber: string;
  purchasingPower: number;
  debt: number;
  marginRate: number;
  children: IPurchasingPowerAssetInfo[];
}

export interface IPayloadAssetInfoDetail {
  accountNumber: string;
  customerName: string;
  brokerCode: string;
  subAccount: string;
}

export interface IPayloadTransactionInfo extends ISumTransactionPayLoad {
  valueFilterInfo: null | IFilterPayloadTransactionInfo;
}

export interface IFilterPayloadTransactionInfo {
  totalTransaction: IRangeFilter;
  buyTransaction: IRangeFilter;
  sellTransaction: IRangeFilter;
  revenueFee: IRangeFilter;
  stockExchangeFee: IRangeFilter;
  depositoryFee: IRangeFilter;
  netTransactionFee: IRangeFilter;
}

export interface ISubAccInvestmentData extends Omit<IDataInvestmentPortfolio, 'subAccount'> {
  subAccount: string;
  parent: {
    accountNumber: string;
  };
  hasNextValue?: boolean;
  idx?: number;
  isCallApiShowMore?: boolean;
}

export interface IChildrenTransactionData {
  customerName: null | string;
  subAccount: string;
  accountNumber: string;
  totalTransactionValue: number;
  buyTransactionValue: number;
  sellTransactionValue: number;
  revenueFee: number;
  stockExchangeFee: number;
  depositoryFee: number;
  smsFee: number;
  netTransactionFee: number;
  otherFees: number;
  id?: string;
}
export interface IAssetSumInfo {
  sumAccountNumber: number;
  sumNav: number;
  sumStock: number;
  sumCash: number;
  sumDebt: number;
  sumAssets: number;
}

export interface ISearchInvestmentPayload {
  searchvalue: string;
  type: ESearchType;
}

export interface IMoneySumInfo {
  sumAccountNumber: number;
  sumCash: number;
  sumCashBalance: number;
  sumAwaitingAdvance: number;
  sumAwaitingDividends: number;
  sumBlockedCash: number;
  sumNotMatched: number;
  sumUnpaid: number;
}

export interface ITransactionsSumInfo {
  sumAccountNumber: number;
  sumBuyTransactionValue: number;
  sumSellTransactionValue: number;
  sumTotalTransactionValue: number;
  sumRevenueFee: number;
  sumStockExchangeFee: number;
  sumOtherFees: number;
  sumDepositoryFee: number;
  sumNetTransactionFee: number;
}

export interface ISumInfoItem {
  label: string;
  value: number;
}
