import { Injectable } from '@angular/core';
import { ApiService } from 'src/app/core/services';
import {
  IAssetInfoResponse,
  IDebtInfoResponse,
  IDataInvestmentPortfolio,
  IMoneyInfoResponse,
  IPayloadInfoSubAccount,
  IPurchasingPowerResponse,
  ITransactionInfoResponse,
  ISumTransactionPayLoad,
  IPayloadAssetInfoDetail,
  ISubInvestmentPayload,
  IPayloadAssetInfo,
  IPayloadMoneyInfo,
  IPayloadPurchasingPower,
  IPayloadInvestment,
  IAssetSumInfo,
  IMoneySumInfo,
} from '../models/asset';
import { map } from 'rxjs';
import { ApiResponse } from 'src/app/core/models/api-response';

@Injectable({
  providedIn: 'root',
})
export class AssetsService {
  urlCustomer = 'v1/customer';
  urlAsset = 'v1/assets';
  urlOrder = 'v1/order';

  /**
   *constructor
   * @param apiService apiService
   */
  constructor(private readonly apiService: ApiService) {}

  // Get Thông tin tài sản
  getAssetsInfo(payload: IPayloadAssetInfo) {
    return this.apiService
      .post<ApiResponse<IAssetInfoResponse[]>>(`${this.urlAsset}/assets-info`, payload)
      .pipe(map((res) => res.data));
  }

  // Get Thông tin Tiền
  getMoneyInfo(payload: IPayloadMoneyInfo) {
    return this.apiService
      .post<ApiResponse<IMoneyInfoResponse[]>>(`${this.urlAsset}/money-info`, payload)
      .pipe(map((res) => res.data));
  }

  // Get Thông tin dư nợ
  getDebtInfo(payload: IPayloadInfoSubAccount[]) {
    return this.apiService
      .post<ApiResponse<IDebtInfoResponse[]>>(`${this.urlAsset}/debt-info`, payload)
      .pipe(map((res) => res.data));
  }

  // Get Thông tin giao dịch
  getTransactionInfo(payload: ISumTransactionPayLoad) {
    return this.apiService
      .post<ApiResponse<ITransactionInfoResponse[]>>(`${this.urlOrder}/sum-transaction`, payload)
      .pipe(map((res) => res.data));
  }

  // Get Thông tin Sức mua
  getPurchasingPowerInfo(payload: IPayloadPurchasingPower) {
    return this.apiService
      .post<ApiResponse<IPurchasingPowerResponse[]>>(`${this.urlAsset}/purchasing-power`, payload)
      .pipe(
        map((res) => {
          return res.data;
        })
      );
  }

  // Get Danh mục đầu tư
  getInvestmentPortfolioInfo(payload: IPayloadInvestment) {
    return this.apiService.post<ApiResponse<IDataInvestmentPortfolio[]>>(
      `${this.urlAsset}/investment_portfolio`,
      payload
    );
    // .pipe(map((res) => res.data));
  }

  // Get sub Danh mục đầu tư
  getSubInvestmentPortfolioInfo(payload: ISubInvestmentPayload) {
    return this.apiService.post<ApiResponse<IDataInvestmentPortfolio[]>>(
      `${this.urlAsset}/investment_portfolio/sub-acc`,
      payload
    );
  }

  getAssetInfoDetail(payload: IPayloadAssetInfoDetail) {
    return this.apiService.post<ApiResponse<any>>(`${this.urlAsset}/assets-info/detail`, payload);
  }

  getSumValueAssetsInfo(payload: IPayloadAssetInfo) {
    return this.apiService
      .post<ApiResponse<IAssetSumInfo>>(`${this.urlAsset}/assets-info/sum-assets`, payload)
      .pipe(map((res) => res.data));
  }

  getSumValueMoneyInfo(payload: IPayloadMoneyInfo) {
    return this.apiService
      .post<ApiResponse<IMoneySumInfo>>(`${this.urlAsset}/money-info/sum-money`, payload)
      .pipe(map((res) => res.data));
  }
}
