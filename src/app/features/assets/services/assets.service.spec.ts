import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { AssetsService } from './assets.service';
import { ApiService } from 'src/app/core/services';
import { ApiResponse } from 'src/app/core/models/api-response';
import {
  IPayloadAssetInfo,
  IPayloadMoneyInfo,
  IPayloadInfoSubAccount,
  ISumTransactionPayLoad,
  IPayloadPurchasingPower,
  IPayloadInvestment,
  ISubInvestmentPayload,
  IPayloadAssetInfoDetail,
  IAssetInfoResponse,
  IMoneyInfoResponse,
  IDebtInfoResponse,
  ITransactionInfoResponse,
  IPurchasingPowerResponse,
  IDataInvestmentPortfolio
} from '../models/asset';

describe('AssetsService', () => {
  let service: AssetsService;
  let mockApiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiServiceSpy = jasmine.createSpyObj('ApiService', ['post']);

    TestBed.configureTestingModule({
      providers: [
        AssetsService,
        { provide: ApiService, useValue: apiServiceSpy }
      ]
    });

    service = TestBed.inject(AssetsService);
    mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  // Constructor and Initialization
  it('No.1: should properly initialize with ApiService dependency', () => {
    expect(service).toBeTruthy();
    expect(service.urlCustomer).toBe('v1/customer');
    expect(service.urlAsset).toBe('v1/assets');
    expect(service.urlOrder).toBe('v1/order');
  });

  // Asset Information Operations
  it('No.2: should get assets info successfully', () => {
    const mockPayload: IPayloadAssetInfo = {
      brokerCodes: ['BR001'],
      accountNumbers: ['ACC001'],
      filterAssetsInfo: null
    };
    const mockResponse: ApiResponse<IAssetInfoResponse[]> = {
      data: [
        {
          id: '1',
          accountNumber: 'ACC001',
          customerName: 'Customer 1',
          purchasingPower: 100000,
          firstNav: 50000,
          nav: 55000,
          marginNav: { numberMargin: 5000, percentMargin: 10 },
          firstStock: 30000,
          stock: 35000,
          marginStock: { numberMargin: 5000, percentMargin: 15 },
          cash: 20000,
          debt: 5000,
          marginRate: 80,
          assets: 75000,
          children: []
        }
      ],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getAssetsInfo(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse.data);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/assets-info', mockPayload);
    });
  });

  it('No.3: should handle getAssetsInfo with empty payload', () => {
    const mockPayload: IPayloadAssetInfo = {
      brokerCodes: [],
      accountNumbers: [],
      filterAssetsInfo: null
    };
    const mockResponse: ApiResponse<IAssetInfoResponse[]> = {
      data: [],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getAssetsInfo(mockPayload).subscribe(result => {
      expect(result).toEqual([]);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/assets-info', mockPayload);
    });
  });

  // Money Information Operations
  it('No.4: should get money info successfully', () => {
    const mockPayload: IPayloadMoneyInfo = {
      brokerCodes: ['BR001'],
      accountNumbers: ['ACC001'],
      filterMoneyInfo: null
    };
    const mockResponse: ApiResponse<IMoneyInfoResponse[]> = {
      data: [
        {
          accountNumber: 'ACC001',
          customerName: 'Customer 1',
          firstCash: 10000,
          cash: 15000,
          marginCash: { numberMargin: 2000, percentMargin: 15 },
          cashBalance: 13000,
          awaitingDividends: 500,
          blockedCash: 1000,
          notMatched: 200,
          unpaid: 300,
          awaitingAdvance: 0,
          children: []
        }
      ],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getMoneyInfo(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse.data);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/money-info', mockPayload);
    });
  });

  it('No.5: should handle getMoneyInfo with filter parameters', () => {
    const mockPayload: IPayloadMoneyInfo = {
      brokerCodes: ['BR001'],
      accountNumbers: ['ACC001'],
      filterMoneyInfo: {
        accountNumber: ['ACC001'],
        purchasingPower: { from: 10000, to: 50000 },
        cashValue: { from: 5000, to: 20000 },
        cashBalanceValue: { from: 0, to: 15000 },
        awaitingAdvanceValue: { from: 0, to: 1000 },
        t0: { from: 0, to: 5000 },
        t1: { from: 0, to: 5000 },
        t2: { from: 0, to: 5000 },
        blockedCashValue: { from: 0, to: 2000 },
        notMatchedValue: { from: 0, to: 500 },
        awaitingDividendsValue: { from: 0, to: 1000 },
        unpaidValue: { from: 0, to: 500 },
        isFilter: true
      }
    };
    const mockResponse: ApiResponse<IMoneyInfoResponse[]> = {
      data: [],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getMoneyInfo(mockPayload).subscribe(result => {
      expect(result).toEqual([]);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/money-info', mockPayload);
    });
  });

  // Debt Information Operations
  it('No.6: should get debt info successfully', () => {
    const mockPayload: IPayloadInfoSubAccount[] = [
      {
        accountNumber: 'ACC001',
        subAccount: 'SUB001',
        brokerCode: 'BR001',
        customerName: 'Customer 1'
      }
    ];
    const mockResponse: ApiResponse<IDebtInfoResponse[]> = {
      data: [
        {
          accountNumber: 'ACC001',
          customerName: 'Customer 1',
          marginRate: 80,
          guaranteedAsset: 50000,
          firstDebt: 5000,
          debt: 4500,
          marginDebt: { numberMargin: 500, percentMargin: 10 },
          marginLoanDebt: 4000,
          marginInterest: 100,
          overdueMarginLoanDebt: 0,
          otherLoanDebt: 0,
          otherInterest: 0,
          overdueOtherLoanDebt: 0,
          advanceDebt: 0,
          advanceInterest: 0,
          smsFee: 50,
          depositoryFee: 25,
          percentDebtPerNav: 8.2,
          percentDebtPerMargin: 9.0,
          children: []
        }
      ],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getDebtInfo(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse.data);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/debt-info', mockPayload);
    });
  });

  it('No.7: should handle getDebtInfo with empty array', () => {
    const mockPayload: IPayloadInfoSubAccount[] = [];
    const mockResponse: ApiResponse<IDebtInfoResponse[]> = {
      data: [],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getDebtInfo(mockPayload).subscribe(result => {
      expect(result).toEqual([]);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/debt-info', mockPayload);
    });
  });

  // Transaction Information Operations
  it('No.8: should get transaction info successfully', () => {
    const mockPayload: ISumTransactionPayLoad = {
      customerInfos: [
        {
          accountNumber: 'ACC001',
          subAccount: ['SUB001'],
          customerName: 'Customer 1'
        }
      ],
      brokerCode: ['BR001']
    };
    const mockResponse: ApiResponse<ITransactionInfoResponse[]> = {
      data: [
        {
          accountNumber: 'ACC001',
          customerName: 'Customer 1',
          totalTransactionValue: 100000,
          buyTransactionValue: 60000,
          sellTransactionValue: 40000,
          revenueFee: 500,
          depositoryFee: 100,
          smsFee: 50,
          otherFees: 25,
          children: []
        }
      ],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getTransactionInfo(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse.data);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/order/sum-transaction', mockPayload);
    });
  });

  // Purchasing Power Operations
  it('No.9: should get purchasing power info successfully', () => {
    const mockPayload: IPayloadPurchasingPower = {
      brokerCodes: ['BR001'],
      accountNumbers: ['ACC001'],
      filterPurchasingPower: null
    };
    const mockResponse: ApiResponse<IPurchasingPowerResponse[]> = {
      data: [
        {
          accountNumber: 'ACC001',
          nav: 55000,
          cash: 15000,
          purchasingPower: 40000,
          usedGuarantee: 5000,
          toSubmitGuarantee: 2000,
          customerName: 'string',
          children: []
        }
      ],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getPurchasingPowerInfo(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse.data);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/purchasing-power', mockPayload);
    });
  });

  it('No.10: should handle getPurchasingPowerInfo with complex mapping', () => {
    const mockPayload: IPayloadPurchasingPower = {
      brokerCodes: ['BR001'],
      accountNumbers: ['ACC001'],
      filterPurchasingPower: {
        accountNo: ['ACC001'],
        navValue: { from: 50000, to: 100000 },
        cashValue: { from: 10000, to: 20000 },
        purchasingPower: { from: 30000, to: 50000 },
        guarantee: { from: 0, to: 10000 },
        usedGuarantee: { from: 0, to: 5000 },
        toSubmitGuarantee: { from: 0, to: 3000 },
        isFilter: true
      }
    };
    const mockResponse: ApiResponse<IPurchasingPowerResponse[]> = {
      data: [],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getPurchasingPowerInfo(mockPayload).subscribe(result => {
      expect(result).toEqual([]);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/purchasing-power', mockPayload);
    });
  });

  // Investment Portfolio Operations
  it('No.11: should get investment portfolio info successfully', () => {
    const mockPayload: IPayloadInvestment = {
      stockCodes: ['VIC', 'VCB'],
      brokerCodes: ['BR001'],
      filterPortfolioInvestmentDTO: null
    };
    const mockResponse: ApiResponse<IDataInvestmentPortfolio[]> = {
      data: [
        {
          stockCode: 'VIC',
          subAccount: 1,
          accountNumber: 'ACC001',
          capitalInvestment: 50000,
          tradableVolume: 100,
          totalVolume: 150,
          t0Volume: 50,
          t1Volume: 50,
          t2Volume: 50,
          costPrice: 100000,
          currentPrice: 105000,
          portfolioProportion: 25.5,
          capitalStructure: 30.2,
          children: [],
          id: '1',
          accQty: 150
        }
      ],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getInvestmentPortfolioInfo(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/investment_portfolio', mockPayload);
    });
  });

  it('No.12: should get sub investment portfolio info successfully', () => {
    const mockPayload: ISubInvestmentPayload = {
      brokerCode: 'BR001',
      stockCode: 'VIC'
    };
    const mockResponse: ApiResponse<IDataInvestmentPortfolio[]> = {
      data: [],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getSubInvestmentPortfolioInfo(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/investment_portfolio/sub-acc', mockPayload);
    });
  });

  // Asset Detail Operations
  it('No.13: should get asset info detail successfully', () => {
    const mockPayload: IPayloadAssetInfoDetail = {
      accountNumber: 'ACC001',
      customerName: 'Customer 1',
      brokerCode: 'BR001',
      subAccount: 'SUB001'
    };
    const mockResponse: ApiResponse<any> = {
      data: {
        accountNumber: 'ACC001',
        detailInfo: 'Asset detail information'
      },
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.getAssetInfoDetail(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
      expect(mockApiService.post).toHaveBeenCalledWith('v1/assets/assets-info/detail', mockPayload);
    });
  });

  // URL Construction and Service Properties
  it('No.14: should have correct URL properties', () => {
    expect(service.urlCustomer).toBe('v1/customer');
    expect(service.urlAsset).toBe('v1/assets');
    expect(service.urlOrder).toBe('v1/order');
  });
});
