import { Component, ElementRef, Inject, On<PERSON><PERSON>roy, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { DestroyService, I18nService, LoadingService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { SubmitGuaranteeOptionsComponent } from '../../components/submit-guarantee-options/submit-guarantee-options.component';
import { PopoverGuaranteeMoneyComponent } from '../../components/popover-guarantee-money/popover-guarantee-money.component';
import { MoneyInfoFilterComponent } from '../../components/money-info-filter/money-info-filter.component';
import { IFilterMoneyInfoParam, IMoneyInfoResponse, IMoneySumInfo } from '../../models/asset';
import { combineLatest, distinctUntilChanged, take, takeUntil } from 'rxjs';
import {
  getDataMoneyInfo,
  getDataMoneyInfoSuccess,
  getDataMoneyInfoWithFilter,
  resetFilterMoneyInfo,
  resetSearch,
  setFilterMoneyInfo,
  updatePageIndexMoneyInfo,
} from '../../stores/asset.actions';
import { Store } from '@ngrx/store';
import {
  pageIndexMoneyInfo$,
  selectFilterMoneyInfo$,
  selectFilteredDataMoneyInfo$,
  selectMoneyInfoList$,
  selectMoneySumInfo$,
  selectSearchValue$,
} from '../../stores/asset.selectors';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { deepClone, rangeMatchData, searchByValue, updateBorkerName } from 'src/app/shared/utils/utils';
import { IClickOnColumnEvent } from '@shared/models';
import { AssetInfoDetailComponent } from '../../components/assets-info-detail/assets-info-detail.component';
import { Router } from '@angular/router';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import { IAllAccountNumber, IAllLevelOfBroker } from 'src/app/shared/models/global';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { CompareValueService } from 'src/app/shared/directives/compare-value/compare-value.service';
import { IListOptions } from '../../constant/assets';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { getCurrentBrokerView, getListAccountNumberAndLevelByBrokerView } from 'src/app/stores/shared/shared.actions';
import { OptionsAssetComponent } from '../../components/options-asset/options-asset.component';

interface IActionMoneyInfo {
  brokerCode: string[];
  accountNumbers: string[];
}

/**
 * Thông tin tiền
 */
@Component({
  selector: 'app-money-info',
  templateUrl: './money-info.container.html',
  styleUrl: './money-info.container.scss',
  providers: [DestroyService],
})
export class MoneyInfoContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('marginCash', { static: true }) marginCash: TemplateRef<any> | null = null;

  @ViewChild('guarantee', { static: true }) guarantee: TemplateRef<any> | null = null;

  @ViewChild('submitGuarantee', { static: true }) submitGuarantee: TemplateRef<any> | null = null;

  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  pageSize = 10;

  totalPage!: number;

  currentPage = 1;

  initialCustomer!: IAllAccountNumber[];

  filterOptions!: IFilterMoneyInfoParam | null;

  showCollapse: boolean = true;

  tags!: string[];

  LIST_MG: IListOptions[] = [];

  isLoadingPage = false;

  brokerCode = '';

  actionMoneyInfo: IActionMoneyInfo | null = null;

  currentBrokerCode = '';

  storeTotalPage = 1;
  customNumberFormat = customNumberFormat;

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param store Store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly store: Store,
    private readonly localStorageService: LocalStorageService,
    private readonly compareValueService: CompareValueService,
    private readonly router: Router,
    private readonly loadingService: LoadingService,
    private readonly i18n: I18nService
  ) {
    super();
    this.brokerInfo();
    this.setButtonInMoneyInfo();

    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }

  private setButtonInMoneyInfo() {
    this.toggleButtonByTags([ActionButton.broker, ActionButton.loading, ActionButton.display, ActionButton.filter]);
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        resizable: true,
        minWidth: 155,
        width: 155,
      },
      {
        name: 'Tên khách hàng',
        isDisplay: true,
        resizable: true,
        minWidth: 30,
        width: 200,
        tag: 'customerName',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
      },

      {
        name: 'Tổng tiền',
        minWidth: 30,
        width: 156,
        tag: 'cash',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
        isCompare: true,
      },
      {
        name: '+/- Tổng tiền',
        minWidth: 30,
        width: 225,
        tag: 'marginCash',
        isDisplay: true,
        cellTemplate: this.marginCash,
        align: 'start',
        resizable: true,
      },
      {
        name: 'Số dư tiền mặt',
        minWidth: 30,
        width: 156,
        tag: 'cashBalance',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
        isCompare: true,
      },
      {
        name: 'Tiền bán chờ về khả ứng',
        minWidth: 30,
        width: 170,
        tag: 'awaitingAdvance',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(Math.round(v));
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
        isCompare: true,
      },
      // {
      //   name: 'Tiền bán T0',
      //   minWidth: 30,
      //   width: 156,
      //   tag: 't0',
      //   displayValueFn: (v: number) => {
      //     return customNumberFormat(Math.round(v));
      //   },
      //   isDisplay: true,
      //   align: 'end',
      //   resizable: true,
      // },
      // {
      //   name: 'Tiền bán T1',
      //   minWidth: 30,
      //   width: 156,
      //   tag: 't1',
      //   displayValueFn: (v: number) => {
      //     return customNumberFormat(Math.round(v));
      //   },
      //   isDisplay: true,
      //   align: 'end',
      //   resizable: true,
      // },
      // {
      //   name: 'Tiền bán T2',
      //   minWidth: 30,
      //   width: 156,
      //   tag: 't2',
      //   displayValueFn: (v: number) => {
      //     return customNumberFormat(Math.round(v));
      //   },
      //   isDisplay: true,
      //   align: 'end',
      //   resizable: true,
      // },
      {
        name: 'Tiền cổ tức chờ về',
        minWidth: 30,
        width: 156,
        tag: 'awaitingDividends',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
        isCompare: true,
      },
      {
        name: 'Tiền bị phong toả',
        minWidth: 30,
        width: 156,
        tag: 'blockedCash',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
        isCompare: true,
      },
      {
        name: 'Tiền mua chưa khớp',
        minWidth: 30,
        width: 160,
        tag: 'notMatched',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
        isCompare: true,
      },
      {
        name: 'Tiền mua chưa TT',
        minWidth: 30,
        width: 156,
        tag: 'unpaid',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
        isCompare: true,
      },
    ];
    this.store
      .select(selectFilterMoneyInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        if (filter) {
          this.isFilter = filter.isFilter ?? false;
        } else {
          this.isFilter = false;
        }
        this.filterOptions = filter as IFilterMoneyInfoParam;
      });

    this.store
      .select(selectMoneySumInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((res) => {
        if (!res) return;
        const {
          sumAccountNumber,
          sumCash,
          sumCashBalance,
          sumAwaitingAdvance,
          sumAwaitingDividends,
          sumBlockedCash,
          sumNotMatched,
          sumUnpaid,
        } = res;
        this.tags = [
          `${sumAccountNumber} ${this.i18n.translate('MES-15')}`,
          `${this.i18n.translate('MES-281')}: ${customNumberFormat(sumCash)}`,
          `${this.i18n.translate('MES-291')}: ${customNumberFormat(sumCashBalance)}`,
          `${this.i18n.translate('MES-613')}: ${customNumberFormat(sumAwaitingAdvance)}`,
          `${this.i18n.translate('MES-436')}: ${customNumberFormat(sumAwaitingDividends)}`,
          `${this.i18n.translate('MES-614')}: ${customNumberFormat(sumBlockedCash)}`,
          `${this.i18n.translate('MES-395')}: ${customNumberFormat(sumNotMatched)}`,
          `${this.i18n.translate('MES-396')}: ${customNumberFormat(sumUnpaid)}`,
        ];
      });

    combineLatest([
      this.store.select(selectAllAccountNumberListByBrokerView$),
      this.store.select(selectAllBrokerLevelListByBrokerView$),
      this.store.select(pageIndexMoneyInfo$).pipe(distinctUntilChanged(() => (this.isLoadingPage = false))),
      this.store.select(selectSearchValue$),
    ])
      .pipe(distinctUntilChanged(), takeUntil(this._destroy))
      .subscribe(([customerAccount, brokers, pageIndex, search]) => {
        this.initialCustomer = customerAccount ?? [];
        let customerBase: string[] = [];
        let customerSearch: string[] = [];

        customerBase = (this.initialCustomer ?? []).map((t) => t.accountNumber);

        if (search) {
          this.isFilter = false;
          customerSearch = searchByValue(this.initialCustomer, search).map((t: IAllAccountNumber) => t.accountNumber);
          this.filterOptions = null;

          if (!customerSearch.length) {
            this.totalPage = 1;
            this.currentPage = 1;
            this.data = [];
            return;
          }
        }

        this.storeTotalPage = Math.ceil((search ? customerSearch : customerBase).length / this.pageSize);
        this.transformData(search ? customerSearch : customerBase, brokers, pageIndex, search);
        this.currentPage = +pageIndex;
      });

    combineLatest([this.store.select(selectFilteredDataMoneyInfo$), this.store.select(pageIndexMoneyInfo$)])
      .pipe(takeUntil(this._destroy))
      .subscribe(([data, pageIndex]) => {
        if (this.isFilter) {
          this.filteredData = data.map((d) => {
            const customerMatch = this.initialCustomer.find((cus) => cus.accountNumber === d.accountNumber);
            return {
              ...d,
              customerName: customerMatch ? customerMatch.customerName : d.customerName,
              children: d.children.map((child: any) => ({
                ...child,
                accountNumber: child.accountNumber + ' - ' + child.subAccount,
                parent: {
                  ...d,
                  customerName: customerMatch ? customerMatch.customerName : d.customerName,
                },
              })),
            };
          });
          this.totalPage = Math.ceil(data.length ? data.length / this.pageSize : 1);
          this.data = this.filteredData.slice(this.pageSize * (+pageIndex - 1), this.pageSize * +pageIndex);
        }
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe(() => {
        if (this.grid) this.map.resetExpandedRowAmountMap();
      });
  }

  getBrokerByParentBrokerId = (brokerObject: IAllLevelOfBroker[]) => {
    let brokerCodesMoneyInfo: string[] = [];
    brokerObject?.forEach((broker) => {
      brokerCodesMoneyInfo.push(broker.brokerCode);

      if (Array.isArray(broker.children) && broker.children.length > 0) {
        brokerCodesMoneyInfo = brokerCodesMoneyInfo.concat(this.getBrokerByParentBrokerId(broker.children));
      }
    });
    return brokerCodesMoneyInfo;
  };

  /**
   * Lấy acc và sub acc để lấy call api get list
   */
  transformData(customerId: string[], brokers: IAllLevelOfBroker[], pageIndex: number | string, searchValue?: string) {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(take(1))
      .subscribe((currentBroker) => {
        if (!currentBroker) return;
        const brokerId = this.route.snapshot.queryParams['brokerId'];

        const indexBroker = brokers.findIndex((broker) => broker.brokerCode === brokerId);

        const brokerCodeIds = [brokerId, ...this.getBrokerByParentBrokerId(brokers[indexBroker]?.children)];
        const listCustomerId = customerId.slice(this.pageSize * (+pageIndex - 1), this.pageSize * +pageIndex);
        let moneyCustomerInfo = {
          brokerCode: brokerCodeIds,
          accountNumbers: listCustomerId,
        };
        if (!listCustomerId.length) {
          this.store.dispatch(getDataMoneyInfoSuccess({ data: [] }));
          return;
        }
        this.brokerCode = brokerId ?? currentBroker.brokerCode;
        this.actionMoneyInfo = {
          brokerCode: brokerCodeIds,
          accountNumbers: customerId.slice(0, this.pageSize),
        };
        this.store.dispatch(getDataMoneyInfo({ moneyCustomerInfo }));
      });

    this.store
      .select(selectMoneyInfoList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((moneyInfoList) => {
        if (!moneyInfoList.length) {
          this.data = [];
          this.initialData = [];
          this.totalPage = 1;
          this.currentPage = 1;

          return;
        }

        this.totalPage = this.storeTotalPage;
        this.data = moneyInfoList.reduce((initial: any, d) => {
          const customerMatch = this.initialCustomer.find((cus) => cus.accountNumber === d.accountNumber);
          const object = {
            ...d,
            isExpanded:
              this.map.getHasExpandedAmountByRowId(d.id ?? '') && (this.map.getExpandedAmountByRowId(d.id ?? '') ?? 0),
            customerName: customerMatch ? customerMatch?.customerName : d.customerName,
            children: d.children.map((child) => ({
              ...child,
              parent: {
                ...d,
                customerName: customerMatch ? customerMatch.customerName : d.customerName,
              },
              accountNumber: `${child.accountNumber} - ${child.subAccNumber ?? child.subAccount}`,
            })),
          };
          if (object.isExpanded) {
            const items = d.children.map((t) => ({
              ...t,
              id: `${t.accountNumber} - ${t.subAccNumber ?? t.subAccount}`, // update to compare value
              accountNumber: `${t.accountNumber} - ${t.subAccNumber ?? t.subAccount}`,
              parent: object,
              backgroundColor: '#f6f6f6',
            }));
            initial.push(object, ...items);
          } else {
            initial.push(object);
          }
          return initial;
        }, []);

        this.initialData = deepClone(this.data);

        if (!this.data[0].isExpanded) {
          if (this.isLoadingPage) return;
          this.data[0].isExpanded = true;
          this.addItemsAt({ index: 0, items: this.data[0].children });
        }
      });
  }

  /**
   * changeCurrentPage
   * @param data
   */
  changeCurrentPage(currentPage: number) {
    this.currentPage = currentPage;

    if (this.isFilter) {
      this.loadingService.show();
      setTimeout(() => {
        this.loadingService.hide();
      }, 1000);
      this.updateDataItem();
    } else this.store.dispatch(updatePageIndexMoneyInfo({ pageIndex: currentPage }));
  }

  updateDataItem() {
    const ITEM_SIZE = 10;
    const start = (this.currentPage - 1) * ITEM_SIZE;
    const end = Math.min(start + ITEM_SIZE, this.filteredData.length);
    const data = this.filteredData.slice(start, end);
    this.data = [...data];
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    if (this.isFilter) {
      this.store.dispatch(resetFilterMoneyInfo());
    }

    if (this.isSearch) {
      this.store.dispatch(resetSearch());
    }
    this.localStorageService.removeData('currentPage');
    this.compareValueService.clearAll();
  }

  /**
   * calculateSum
   * @param {IMoneyInfoResponse[]} data
   * @param {string} field
   * @returns {number}
   */
  calculateSum(data: IMoneyInfoResponse[], field: keyof IMoneyInfoResponse): number {
    return data.reduce((sum, item) => {
      if (!item.children) return sum;
      const value = item[field];
      return sum + (typeof value === 'number' ? value : 0);
    }, 0);
  }

  /**
   * searchData
   * @param {string} value searchValue
   */
  searchData(value: string) {
    let searchData = [];
    const dataClone = deepClone(this.data);
    const searchValue = value.toString().toLowerCase();
    if (value) {
      searchData = dataClone.filter((item) => {
        return this.containsSearchValue(item, searchValue);
      });

      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchData = deepClone(this.initialData);
      this.isSearch = false; // Set isSearch to false when not searching
    }

    this.data = deepClone(searchData);
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    const accountNumber = item.accountNumber?.toString().toLowerCase();
    const customerName = item.customerName?.toString().toLowerCase();

    return accountNumber?.includes(searchValue) ?? customerName?.includes(searchValue);
  }

  /**
   * Thông tin của broker
   */
  brokerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBroker) => {
        if (!currentBroker) return;
        this.currentBrokerCode = currentBroker.brokerCode;

        combineLatest([
          this.store.select(selectInfoUserLogin$),
          this.store.select(selectAllBrokerLevelListByBrokerView$),
        ])
          .pipe(takeUntil(this._destroy))
          .subscribe(([userList, brokers]) => {
            const queryParams = this.route.snapshot.queryParams;

            if (!userList) return;

            if (!userList) return;

            const brokerConvert = updateBorkerName([...brokers], [...userList]);

            const subBroker = brokerConvert.map((broker) => {
              return {
                brokerCode: `${broker.brokerCode}`,
                name: `${broker.brokerName}`,
                isSelect: queryParams['brokerId']
                  ? broker.brokerCode === queryParams['brokerId']
                  : broker.brokerCode === currentBroker.brokerCode,
              };
            });

            this.LIST_MG = [...subBroker];
            const broker = this.LIST_MG.find((t) => t.isSelect);

            if (broker) {
              this.findButtonByTags(ActionButton.broker).label = broker['name'] ?? '';
            }
          });
      });
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'broker':
        this.changeViewBroker();
        break;

      case 'loading':
        if (this.isFilter) return;
        this.loadPage();

        break;

      case 'filter':
        {
          const ref = this.openFilter(MoneyInfoFilterComponent, {
            width: '800px',
            data: this.filterOptions,
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe({
              next: (v) => {
                if (!v) return;
                this.applyFilter(v);
              },
            });
        }
        break;

      case 'display':
        this.compareValueService.clearAll();
        break;
    }
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * OpenPopup based on the event and submitGuarantee
   * provided.
   * @param {Event} event - Event
   * @param {submitGuarantee} submitGuarantee - number
   * @param {accountNumber} accountNumber - string
   */
  openPopoverSubmitGuarantee(event: Event, submitGuarantee: number, accountNumber: string) {
    const originElement = event.target as HTMLElement;

    this.popoverService.open({
      origin: originElement,
      content: SubmitGuaranteeOptionsComponent,
      width: 191,
      height: 96,
      position: 2,
      hasBackdrop: true,
      componentConfig: { submitGuarantee, accountNumber },
    });
  }

  /**
   * OpenPopoverGuaranteeMoney
   * @param {MouseEvent} event - event
   * @param {number} guarantee - guarantee
   * @param {number} purchasingPower - purchasingPower
   * @param {string} accountNumber - accountNumber
   */
  openPopoverGuaranteeMoney(event: MouseEvent, guarantee: number, purchasingPower: number, accountNumber: string) {
    const originElement = event.target as HTMLElement;
    this.popoverService.open({
      origin: originElement,
      content: PopoverGuaranteeMoneyComponent,
      width: 400,
      height: 383,
      position: 0,
      hasBackdrop: true,
      componentConfig: { guarantee, purchasingPower, accountNumber },
    });
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;
    const currentFilter = this.isFilter;
    if (type === 'save') {
      if (!this.isFilter && !optionFilter?.isFilter) return;
      this.currentPage = 1;
      if (optionFilter.isFilter) {
        this.store.dispatch(setFilterMoneyInfo({ params: optionFilter }));
      } else this.store.dispatch(resetFilterMoneyInfo());
    } else if (type === 'default') {
      this.filteredData = [];
      if (!this.isFilter) {
        return;
      }
      this.currentPage = 1;
      this.store.dispatch(resetFilterMoneyInfo());
      this.map.resetExpandedRowAmountMap();
    }
    if (!currentFilter && !optionFilter?.isFilter && type === 'save') return;

    if (this.actionMoneyInfo) {
      if (optionFilter?.isFilter) {
        this.store.dispatch(getDataMoneyInfoWithFilter({ moneyCustomerInfo: this.actionMoneyInfo }));
      } else this.store.dispatch(getDataMoneyInfo({ moneyCustomerInfo: this.actionMoneyInfo }));
      this.totalPage = Math.ceil(this.initialCustomer.length / this.pageSize);
    }
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilter: any) {
    const dataCloneMI = deepClone(this.initialData);
    const newListFilterMI = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(dataCloneMI, optionFilter);

    return newListFilterMI;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    const newListFilter = data.filter((item, index) => {
      const {
        customers,
        // purchasingPower,
        cash,
        cashBalance,
        awaitingAdvance,
        // t0,
        // t1,
        // t2,
        blockedCash,
        guarantee,
        usedGuarantee,
        toSubmitGuarantee,
      } = optionFilter;
      const isCustomerMatch = (customers ?? []).length ? (customers ?? []).includes(item.accountNumber) : true;

      const isCashMatch = rangeMatchData(item.cash, cash);

      const isCashBalanceMatch = rangeMatchData(item.cashBalance, cashBalance);

      const isAwaitingAdvanceMatch = rangeMatchData(item.awaitingAdvance, awaitingAdvance);

      const isBlockedCashMatch = rangeMatchData(item.blockedCash, blockedCash);

      const isGuaranteeMatch = rangeMatchData(item.notMatched, guarantee);

      const isUsedGuaranteeMatch = rangeMatchData(item.unpaid, usedGuarantee);

      const isToSubmitGuaranteeMatch = rangeMatchData(item.awaitingDividends, toSubmitGuarantee);
      return (
        isCustomerMatch &&
        // isPurchasingPowerMatch &&
        isCashMatch &&
        isCashBalanceMatch &&
        isAwaitingAdvanceMatch &&
        // isT0Match &&
        // isT1Match &&
        // isT2Match &&
        isGuaranteeMatch &&
        isUsedGuaranteeMatch &&
        isBlockedCashMatch &&
        isToSubmitGuaranteeMatch
      );
    });

    return newListFilter;
  }

  /**
   * OpenAssetInfoDetail
   * @param event IClickOnColumnEvent
   */
  openAssetInfoDetail(event: IClickOnColumnEvent) {
    const { element } = event;
    if (element.children) return;
    this.dialogService.openPopUp(AssetInfoDetailComponent, {
      width: 'calc(100vw - 200px)',
      height: 'calc(100vh - 50px)',
      panelClass: 'open-asset-info-cls',
      data: {
        status: 'money-info-detail',
        element,
        brokerCode: this.brokerCode,
      },
    });
  }

  /**
   * Tải lại
   */
  loadPage() {
    this.isLoadingPage = true;
    this.store
      .select(selectAllBrokerLevelListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((brokers) => {
        this.store
          .select(pageIndexMoneyInfo$)
          .pipe(take(1))
          .subscribe((pageIndex) => {
            if (this.isFilter || this.isSearch) {
              this.isFilter = false;
              this.isSearch = false;
              this.store.dispatch(resetFilterMoneyInfo());
            }

            this.totalPage = Math.ceil((this.initialCustomer ?? []).map((t) => t.accountNumber).length / this.pageSize);
            this.transformData(
              (this.initialCustomer ?? []).map((t) => t.accountNumber),
              brokers,
              pageIndex
            );

            this.data.forEach((d) => {
              this.processCompareData(d);
            });
          });
      });
  }

  /**
   * Lưu giá trị để check thay đổi trong bảng
   */
  processCompareData(data: any) {
    Object.entries(data).forEach(([key, value]) => {
      if (typeof value === 'number' || key === 'id') {
        this.compareValueService.setData(data.id, key, value as number);
      }
    });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    const queryParams = this.route.snapshot.queryParams;
    const elementRef = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidth = elementRef.nativeElement as HTMLElement;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRef as any,
      width: elementWidth.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(takeUntil(this._destroy)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(takeUntil(this._destroy))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBroker = userList.find((user) => user.brokerCode === itemSelected['brokerCode']);

          const subBroker = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelected['brokerCode']);
          if (queryParams['brokerId'] === itemSelected['brokerCode']) return;
          if (subBroker && !currentBroker) {
            const brokerCode = subBroker['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (currentBroker) {
            if (this.currentBrokerCode === currentBroker.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBroker.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBroker }));
          }

          if (subBroker) {
            this.LIST_MG = this.LIST_MG.map((broker) => ({
              ...broker,
              isSelect: broker['brokerCode'] === subBroker['brokerCode'],
            }));

            this.findButtonByTags(ActionButton.broker).label = subBroker['name'] ?? '';
          }
          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));
          this.store.dispatch(resetFilterMoneyInfo());

          const subBrokerCode = subBroker ? subBroker['brokerCode'] : null;
          this.router.navigate([], {
            queryParams: {
              ...queryParams,
              brokerId: currentBroker ? currentBroker.brokerCode : subBrokerCode,
            },
          });
        });
    });
  }

  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsAssetComponent,
      width: 'fit-content',
      hasBackdrop: true,
      position: 1,
      componentConfig: {
        brokerCode: this.brokerCode,
        element: element,
      },
    });
  }
}
