<div class="money-info-container">
  <div class="header-money-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-70' | translate}}</div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [listColumnInitial]="columnConfigsInitial"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter,
        }"
      ></app-action-btn>
    </div>
  </div>

  <ng-container *ngIf="isFilter">
    <app-slide-tag [tags]="tags" [classParent]="'money-info-container'"></app-slide-tag>
  </ng-container>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); openAssetInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls"
    >
    </sha-grid>

    <app-paginator (currentPageChanged)="changeCurrentPage($event)" [currentPage]="currentPage" [totalPage]="totalPage">
    </app-paginator>
  </div>
</div>

<!-- Tổng tiền -->
<ng-template #marginCash let-marginCash="templateInfo">
  @if(marginCash) {
  <div>
    <ng-container *ngIf="marginCash.numberMargin > 0">
      <div class="total-amount-increase typo-body-12">
        <img src="./assets/icons/up.svg" alt="document-logo" />
        <span class="typo-body-12"
          >+{{ marginCash.numberMargin | numberFormat}} +{{ marginCash.percentMargin.toFixed(2) }}%</span
        >
      </div>
    </ng-container>
    <ng-container *ngIf="marginCash.numberMargin < 0 ">
      <div class="total-amount-reduce typo-body-12">
        <img src="./assets/icons/down.svg" alt="document-logo" />
        <span *ngIf="marginCash.percentMargin !== 0 " class="typo-body-12"
          >{{ marginCash.numberMargin | numberFormat }} {{ marginCash.percentMargin.toFixed(2) }}%</span
        >
        <span *ngIf="marginCash.percentMargin === 0 " class="typo-body-12"
          >{{ marginCash.numberMargin | numberFormat }} - {{ marginCash.percentMargin.toFixed(2) }}%</span
        >
      </div>
    </ng-container>
    <ng-container *ngIf="marginCash.numberMargin === 0">
      <div class="total-amount-stable typo-body-12">
        <img src="./assets/icons/minus.svg" alt="document-logo" />
        <span class="typo-body-12"
          >{{ marginCash.numberMargin | numberFormat }} - {{ marginCash.percentMargin.toFixed(2).replace('.',
          ',')}}%</span
        >
      </div>
    </ng-container>
  </div>
  } @else {
  <div>-</div>
  }
</ng-template>

<!-- Tiền bảo lãnh -->
<ng-template #guarantee let-guarantee="templateInfo" let-element="element">
  @if(!!guarantee) {
  <div>
    <ng-container>
      <div class="guarantee-money-wrapper typo-body-12">
        <img
          src="./assets/icons/add-circle.svg"
          alt="add-guarantee-money"
          [matTooltip]="'MES-75' | translate"
          matTooltipPosition="above"
          matTooltipClass="custom-tooltip"
          (click)="openPopoverGuaranteeMoney($event ,guarantee, element.purchasingPower, element.accountNumber)"
        />
        <span class="guarantee-money typo-body-12">{{customNumberFormat(guarantee)}}</span>
      </div>
    </ng-container>
  </div>
  } @else {
  <div>-</div>
  }
</ng-template>

<!-- Tiền bảo lãnh cần nộp -->
<ng-template #submitGuarantee let-submitGuarantee="templateInfo" let-element="element">
  @if(!!submitGuarantee) {
  <div>
    <ng-container>
      <div class="guarantee-money-wrapper typo-body-12">
        <img
          src="./assets/icons/more-circle.svg"
          alt="more-option-guarantee-money"
          (click)="openPopoverSubmitGuarantee($event, submitGuarantee ,element.accountNumber)"
        />
        <span class="typo-body-12">{{customNumberFormat(submitGuarantee)}}</span>
      </div>
    </ng-container>
  </div>
  } @else {
  <div>-</div>
  }
</ng-template>
