.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.money-info-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-money-info {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 16px 12px;

    .middle-box {
      display: flex;
      align-items: center;
      gap: 12px;
      order: 2;
      flex: 1 1 100%; // take remaining space, wrap when needed
      text-align: center;
      .number-info-cls {
        display: flex;
        gap: 12px;
        align-items: center;
        flex-wrap: wrap;
        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
      }
    }

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;
      flex: 0 0 auto;

      .text-header {
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        text-wrap: nowrap;
        white-space: nowrap;
        line-height: 28px;
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;
      margin-left: auto;
      flex: 0 0 auto;

      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--broker {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          mat-icon[data-mat-icon-name='people-icon'] {
            #Group,
            #Group_2,
            #Group_3 {
              ::ng-deep {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
            #box-id--loading {
              opacity: 0.5;
              cursor: not-allowed !important;
              .icons-cls {
                cursor: not-allowed !important;
              }
            }
          }
        }

        &.disable-btn-cls {
          ::ng-deep {
            .box-btn {
              opacity: 0.5;
            }
          }
        }
      }
    }
  }

  .table-view-container {
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;

    .table-custom-cls {
      height: calc(100% - 50px);
      ::ng-deep {
        .table-container {
          position: relative;
        }
        td {
          cursor: pointer;

          &.increasing {
            background-color: var(--color--success--200);
          }

          &.decreasing {
            background-color: var(--color--brand--100);
          }
        }

        // element background color has expanded
        tr[row-expanded='true'] {
          background-color: var(--color--background--hover);
        }
      }
    }
  }

  .box-info-wrapper {
    padding: 12px 12px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 12px;

    .box-info {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 8px;
      border-radius: 16px;
      background-color: #f8fafd;
      box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
      color: #808080;
      // text
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      white-space: nowrap;
      text-wrap: nowrap;
      width: fit-content;
    }
  }

  // Hide the tabContainer
  .hidden {
    display: none;
  }

  .dropdown-btn,
  .collapse-btn {
    display: flex;
    align-items: center;
    background-color: var(--color--brand--50);
    border-radius: 16px;

    img {
      padding: 4px, 8px, 4px, 8px;
      width: 20px;
      height: 20px;
    }
  }

  // ngTemplate
  .total-amount-increase,
  .total-amount-reduce,
  .total-amount-stable {
    display: flex;
    align-items: center;
    gap: 4px;

    &.total-amount-increase {
      span {
        color: var(--color--accents--green);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    &.total-amount-reduce {
      span {
        color: var(--color--accents--red);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    &.total-amount-stable {
      span {
        color: var(--color--warning--500);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }

  .guarantee-money-wrapper {
    display: flex;
    align-items: center;
    gap: 4px;

    ::ng-deep {
      .guarantee-money {
        padding: 2px 24px;
        background-color: var(--color--accents--yellow-dark);
        border-radius: 16px;
        white-space: nowrap;
        text-wrap: nowrap;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        flex: 1 0 0;
        min-width: 120px;
        max-width: 130px;
      }
    }

    img {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 4px;
      border-radius: 16px;
      background-color: var(--color--cyan--50);
      cursor: pointer;
      width: 26px;
      height: 26px;
    }
  }
}
