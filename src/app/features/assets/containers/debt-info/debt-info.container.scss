.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.debt-info-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-debt-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;

      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--broker {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          mat-icon[data-mat-icon-name='people-icon'] {
            #Group,
            #Group_2,
            #Group_3 {
              ::ng-deep {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.disable-btn-cls {
          ::ng-deep {
            .box-btn {
              opacity: 0.5;
            }
          }
        }
      }
    }
  }

  // Hide the tabContainer
  .hidden {
    display: none;
  }

  .dropdown-btn,
  .collapse-btn {
    display: flex;
    align-items: center;
    background-color: var(--color--brand--50);
    border-radius: 16px;

    img {
      padding: 4px, 8px, 4px, 8px;
      width: 20px;
      height: 20px;
    }
  }

  .table-view-container {
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;

    .table-custom-cls {
      height: calc(100% - 50px);
      ::ng-deep {
        .table-container {
          position: relative;
        }

        // element background color has expanded
        tr[row-expanded='true'] {
          background-color: var(--color--background--hover);
        }

        td {
          cursor: pointer;

          &.increasing {
            background-color: var(--color--success--200);
          }

          &.decreasing {
            background-color: var(--color--brand--100);
          }
        }
        .margin-rate-cls {
          max-width: 120px;
          margin: 0 auto;

          span {
            background-color: var(--color--accents--yellow-dark);
            border-radius: 16px;
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;

            input {
              text-align: center;
            }
          }
        }

        .margin-rate-100-cls {
          max-width: 120px;
          margin: 0 auto;

          span {
            background-color: var(--color--accents--green);
            border-radius: 16px;
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;

            input {
              text-align: center;
            }
          }
        }

        .margin-interest {
          max-width: 120px;
          margin: 0 auto;

          span {
            input {
              color: var(--color--accents--green);
            }
          }
        }
      }
    }
  }
}

.box-info-wrapper {
  padding: 12px 12px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 12px;

  .box-info {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 16px;
    background-color: #f8fafd;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    color: #808080;
    // text
    white-space: nowrap;
    text-wrap: nowrap;
    width: fit-content;
  }
}

// Hide the tabContainer
.hidden {
  display: none;
}

.dropdown-btn,
.collapse-btn {
  display: flex;
  align-items: center;
  background-color: var(--color--brand--50);
  border-radius: 16px;

  img {
    padding: 4px, 8px, 4px, 8px;
    width: 20px;
    height: 20px;
  }
}

// ngTemplate
.debt-info-increase,
.debt-info-reduce,
.debt-info-stable {
  display: flex;
  align-items: center;
  gap: 4px;

  &.debt-info-increase {
    span {
      color: var(--color--accents--green);
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  &.debt-info-reduce {
    span {
      color: var(--color--accents--red);
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  &.debt-info-stable {
    span {
      color: var(--color--warning--500);
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
