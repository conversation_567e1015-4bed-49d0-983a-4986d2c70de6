import { Component, ElementRef, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { DestroyService } from 'src/app/core/services';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { Store } from '@ngrx/store';
import { selectDebtInfoList$, selectFilterDebtInfo$, selectSearchValue$ } from '../../stores/asset.selectors';
import { take, takeUntil } from 'rxjs';
import { GridComponent } from '../../../../shared/components/table-custom/grid.component';
import { deepClone, rangeMatchData } from '../../../../shared/utils/utils';
import { DebtInfoFilterComponent } from '../../components/debt-info-filter/debt-info-filter.component';
import { IDebtInfoResponse, IFilterDebtInfoParam, IPayloadInfoSubAccount } from '../../models/asset';
import { getDataDebtInfo, resetFilterDebtInfo, resetSearch, setFilterDebtInfo } from '../../stores/asset.actions';
import { IClickOnColumnEvent } from '@shared/models';
import { AssetInfoDetailComponent } from '../../components/assets-info-detail/assets-info-detail.component';
import { IAllAccountNumber, IInfoUserLogined } from 'src/app/shared/models/global';
import {
  selectAllAccountNumberListByBrokerView$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { CompareValueService } from 'src/app/shared/directives/compare-value/compare-value.service';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { getCurrentBrokerView } from 'src/app/stores/shared/shared.actions';
import { IListOptions } from '../../constant/assets';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';

/**
 * Dư nợ
 */
@Component({
  selector: 'app-debt-info',
  templateUrl: './debt-info.container.html',
  styleUrl: './debt-info.container.scss',
})
export class DebtInfoContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('marginDebt', { static: true }) marginDebt: TemplateRef<any> | null = null;

  filterOptions!: IFilterDebtInfoParam;

  showCollapse: boolean = true;

  pageSize = 10;

  totalPage!: number;

  currentPage = 1;

  initialCustomer!: IAllAccountNumber[];

  dataStore: any[] = [];

  customNumberFormat = customNumberFormat;

  tags: string[] = [];

  LIST_MG: IListOptions[] = [];

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly localStorageService: LocalStorageService,
    private readonly compareValueService: CompareValueService,
    private readonly popoverService: PopoverService
  ) {
    super();
    this.brokerInfoDI();
    this.toggleButtonByTags([
      ActionButton.broker,
      ActionButton.loading,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    const cellTemplate = this.marginDebt;
    const columnBaseColumnConfig = {
      minWidth: 30,
      width: 156,
      isDisplay: true,
      resizable: true,
      displayValueFn: (v: number) => {
        if (v === 0) return '-';
        return customNumberFormat(v);
      },
    }


    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 155,
        width: 155,
        tag: 'accountNumber',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 290,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: string) => {
          if (!v) return '';
          return v;
        },
      },
      {
        name: 'Tỷ lệ ký quỹ',
        minWidth: 30,
        width: 152,
        tag: 'marginRate',
        displayValueFn: (v) => {
          return customNumberFormat(v, 'percent');
        },
        dynamicClass: (value) => {
          if (!value) return '';
          return value < 100 ? 'margin-rate-cls' : 'margin-rate-100-cls';
        },
        isDisplay: true,
        resizable: true,
        align: 'center',
      },
      {
        name: 'Tài sản đảm bảo',
        minWidth: 30,
        width: 156,
        tag: 'guaranteedAsset',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
        isCompare: true,
      },
      {
        name: 'Tổng dư nợ',
        minWidth: 30,
        width: 156,
        tag: 'debt',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        align: 'end',
        isCompare: true,
      },
      {
        name: '+/- Tổng dư nợ',
        minWidth: 30,
        width: 225,
        tag: 'marginDebt',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
      {
        name: 'Nợ vay Margin',
        tag: 'marginLoanDebt',
        ...columnBaseColumnConfig,
        align: 'end',
        isCompare: true,
      },
      {
        name: 'Lãi vay Margin',
        tag: 'marginInterest',
        align: 'end',
        dynamicClass: (value) => {
          if (value === 0 || !value) return '';
          return 'margin-interest';
        },
        ...columnBaseColumnConfig,
      },
      {
        name: 'Dư nợ Margin quá hạn',
        tag: 'overdueMarginLoanDebt',
        ...columnBaseColumnConfig,
        width: 170,
        align: 'end',
        isCompare: true,
      },
      {
        name: 'Nợ vay khác',
        tag: 'otherLoanDebt',
        ...columnBaseColumnConfig,
        align: 'end',
        isCompare: true,
      },
      {
        name: 'Lãi vay khác',
        tag: 'otherInterest',
        ...columnBaseColumnConfig,
        align: 'end',
        isCompare: true,
      },
      {
        name: 'Dư nợ khác quá hạn',
        tag: 'overdueOtherLoanDebt',
        ...columnBaseColumnConfig,
        width: 170,
        align: 'end',
        isCompare: true,
      },
      {
        name: 'Dư nợ ứng trước',
        tag: 'advanceDebt',
        ...columnBaseColumnConfig,
        align: 'end',
        isCompare: true,
      },
      {
        name: 'Lãi vay ứng trước',
        tag: 'advanceInterest',
        ...columnBaseColumnConfig,
        align: 'end',
        isCompare: true,
      },
      {
        name: 'Phí SMS',
        tag: 'smsFee',
        ...columnBaseColumnConfig,
        align: 'end',
        isCompare: true,
      },
      {
        name: 'Phí lưu ký',
        tag: 'depositoryFee',
        ...columnBaseColumnConfig,
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(Math.round(v));
        },
        align: 'end',
        isCompare: true,
      },
      {
        name: 'Tỷ lệ Nợ / TTS',
        tag: 'percentDebtPerNav',
        minWidth: 30,
        width: 170,
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          return customNumberFormat(v, 'percent');
        },
        dynamicClass: (value) => {
          return value < 100 ? 'margin-rate-cls' : 'margin-rate-100-cls';
        },
        align: 'center',
      },
      {
        name: 'Tỷ lệ Nợ / DM Ký quỹ',
        tag: 'percentDebtPerMargin',
        minWidth: 30,
        width: 170,
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          return customNumberFormat(v, 'percent');
        },
        dynamicClass: (value) => {
          return value < 100 ? 'margin-rate-cls' : 'margin-rate-100-cls';
        },
        align: 'center',
      },
    ];

    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((customerAcc) => {
        if (!customerAcc.length) return;
        this.totalPage = Math.ceil(customerAcc.length / this.pageSize);
        this.initialCustomer = customerAcc;
      });

    this.store
      .select(selectFilterDebtInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter?.isFilter;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchMultipleLevel(value ?? '', ['accountNumber', 'customerName']);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    if (this.isFilter) {
      this.store.dispatch(resetFilterDebtInfo());
    }

    if (this.isSearch) {
      this.store.dispatch(resetSearch());
    }
    this.localStorageService.removeData('currentPage');
    this.compareValueService.clearAll();
  }

  /**
   * Lấy acc và sub acc để lấy call api get list
   */
  transformData() {
    const localStoragePageIndex = this.localStorageService.getData('currentPage');
    if (localStoragePageIndex) {
      this.currentPage = +localStoragePageIndex;
    }

    let newValue: IPayloadInfoSubAccount[] = [];

    this.store.dispatch(getDataDebtInfo({ payload: newValue }));
    this.store
      .select(selectDebtInfoList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((debtInfoList) => {
        if (!debtInfoList.length) return;
        this.data = debtInfoList.reduce((initial: any, d) => {
          const customerMatch = this.initialCustomer.find((cus) => cus.accountNumber === d.accountNumber);
          const object = {
            ...d,
            isExpanded:
              this.map.getHasExpandedAmountByRowId(d.id ?? '') &&
              (this.map.getExpandedAmountByRowId(d.id ?? '') ?? 0) > 0,
            customerName: customerMatch ? customerMatch?.customerName : d.customerName,
            children: d.children.map((child) => ({
              ...child,
              accountNumber: `${child.accountNumber} - ${child.subAccNumber}`,
            })),
          };
          if (
            this.map.getHasExpandedAmountByRowId(d.id ?? '') &&
            (this.map.getExpandedAmountByRowId(d.id ?? '') ?? 0) > 0
          ) {
            const items = d.children.map((t) => ({
              ...t,
              accountNumber: `${t.accountNumber} - ${t.subAccNumber}`,
              backgroundColor: '#f6f6f6',
            }));
            initial.push(object, ...items);
          } else {
            initial.push(object);
          }
          return initial;
        }, []);

        this.totalTags(this.data);
        this.dataStore = this.data;
        this.initialData = deepClone(this.data);
      });
  }

  /**
   * calculate totalTags
   * @param {IDebtInfoResponse[]} data
   */
  totalTags(data: IDebtInfoResponse[]) {
    this.tags = [
      `${this.pageSize} Tài khoản`,
      `TS đảm bảo: ${customNumberFormat(this.calculateSum(data, 'guaranteedAsset'))}`,
      `Dư nợ: ${customNumberFormat(this.calculateSum(data, 'debt'))}`,
      `Nợ Margin: ${customNumberFormat(this.calculateSum(data, 'marginLoanDebt'))}`,
      `Nợ khác: ${customNumberFormat(this.calculateSum(data, 'otherLoanDebt'))}`,
      `Dư nợ ứng trước: ${customNumberFormat(this.calculateSum(data, 'advanceDebt'))}`,
      `Lãi vay ứng trước: ${customNumberFormat(this.calculateSum(data, 'advanceInterest'))}`,
      `Phí SMS: ${customNumberFormat(this.calculateSum(data, 'smsFee'))}`,
      `KQ phải nộp: ${customNumberFormat(this.calculateSum(data, 'depositoryFee'))}`,
    ];
  }

  /**
   * changeCurrentPage
   * @param data
   */
  changeCurrentPage(currentPage: number) {
    this.currentPage = currentPage;
    this.localStorageService.saveData('currentPage', currentPage.toString());
    this.transformData();
    this.store.dispatch(resetSearch());
  }

  /**
   * calculateSum
   * @param {IDebtInfoResponse[]} data
   * @param {string} field
   * @returns {number}
   */
  calculateSum(data: IDebtInfoResponse[], field: keyof IDebtInfoResponse): number {
    return data.reduce((sum, item) => {
      const value = item[field];
      return sum + (typeof value === 'number' ? value : 0);
    }, 0);
  }

  /**
   * Thông tin của broker - thông tin dư nợ
   */
  brokerInfoDI() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBroker) => {
        if (!currentBroker) return;
        this.findButtonByTags(ActionButton.broker).label = `${currentBroker.brokerCode}: ${currentBroker.brokerName}`;

        this.getCurrentBroker(currentBroker);
      });
  }

  private getCurrentBroker(currentBroker: IInfoUserLogined) {
    this.store
      .select(selectInfoUserLogin$)
      .pipe(takeUntil(this._destroy))
      .subscribe((userList) => {
        if (!userList) return;
        this.LIST_MG = userList.map((user) => {
          return {
            brokerCode: `${user.brokerCode}`,
            name: `${user.brokerCode}: ${user.brokerName}`,
            isSelect: user.brokerCode === currentBroker.brokerCode,
          };
        });
      });
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    const customers = this.initialData.map((item) => {
      return {
        group: item.accountNumber,
        name: item.customerName,
      };
    });

    switch (tag) {
      case 'broker':
        this.changeViewBrokerDI();
        break;

      case 'loading':
        this.dataStore.forEach((item) => {
          Object.keys(item).forEach((key) => {
            if (key !== 'id') {
              // Exclude 'id' from being treated as a tag
              this.compareValueService.setData(item.id, key, item[key]);
            }
          });
        });

        break;

      case 'filter':
        {
          const ref = this.openFilter(DebtInfoFilterComponent, {
            width: '800px',
            data: {
              filterOptions: this.filterOptions,
              customers,
            },
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe({
              next: (v) => {
                if (!v) return;
                this.applyFilter(v);
              },
            });
        }
        break;

      case 'display':
        this.compareValueService.clearAll();
        break;
    }
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;

    if (type === 'save') {
      const newListFilterDI = this.saveFunc(optionFilter);
      this.filteredData = newListFilterDI;
      this.data = newListFilterDI;
    } else if (type === 'default') {
      this.isFilter = false;
      this.filteredData = [];
      this.updateDataWithSearchValueDI()
      this.store.dispatch(resetFilterDebtInfo());
    }
  }

  private updateDataWithSearchValueDI() {
    this.isSearch
      ? this.store
        .select(selectSearchValue$)
        .pipe(takeUntil(this._destroy))
        .subscribe((value) => {
          this.searchMultipleLevel(value ?? '', ['accountNumber', 'customerName']);
        })
      : (() => {
        this.data = this.initialData;
      })();
  }

  /**
   * UpdateParamInStore
   * @param optionFilter
   */
  updateParamInStore(optionFilter: any) {
    this.store.dispatch(setFilterDebtInfo({ params: optionFilter }));
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilter: any) {
    this.updateParamInStore(optionFilter);

    const dataCloneDI = deepClone(this.initialData);
    const newListFilterDI = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(dataCloneDI, optionFilter);
    return newListFilterDI;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {

    const newListFilter = data.filter((item, index) => {
      const {
        customersSelect,
        marginRate,
        guaranteedAsset,
        debt,
        marginLoanDebt,
        marginInterest,
        percentDebtPerNav,
        percentDebtPerMargin,
      } = optionFilter;
      const isCustomerMatch = (customersSelect ?? []).length
        ? (customersSelect ?? []).includes(item.customerName)
        : true;

      const isMarginRateMatch = rangeMatchData(item.marginRate, marginRate);

      const isGuaranteedAssetMatch = rangeMatchData(item.guaranteedAsset, guaranteedAsset);

      const isDebtMatch = rangeMatchData(item.debt, debt);

      const isMarginLoanDebtMatch = rangeMatchData(item.marginLoanDebt, marginLoanDebt);

      const isMarginInterestMatch = rangeMatchData(item.marginInterest, marginInterest);

      const isPercentDebtPerNavMatch = rangeMatchData(item.percentDebtPerNav, percentDebtPerNav);

      const isPercentDebtPerMarginMatch = rangeMatchData(item.percentDebtPerMargin, percentDebtPerMargin);

      return (
        isCustomerMatch &&
        isMarginRateMatch &&
        isGuaranteedAssetMatch &&
        isDebtMatch &&
        isMarginLoanDebtMatch &&
        isMarginInterestMatch &&
        isPercentDebtPerNavMatch &&
        isPercentDebtPerMarginMatch
      );
    });

    return newListFilter;
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * OpenAssetInfoDetail
   * @param event IClickOnColumnEvent
   */
  openAssetInfoDetail(event: IClickOnColumnEvent) {
    const { element } = event;
    this.dialogService.openPopUp(AssetInfoDetailComponent, {
      width: 'calc(100vw - 200px)',
      height: 'calc(100vh - 50px)',
      panelClass: 'open-asset-info-cls',
      data: {
        status: 'debt-detail',
        element,
      },
    });
  }

  /**
   * changeViewBrokerDI
   */
  changeViewBrokerDI() {
    const elementRefDI = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidthDI = elementRefDI.nativeElement as HTMLElement;

    const refDI = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRefDI as any,
      width: elementWidthDI.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    refDI.afterClosed$.pipe(takeUntil(this._destroy)).subscribe((res) => {
      if (!res.data) return;
      const itemSelectedDI = res.data.find((i) => i.isSelect);
      if (!itemSelectedDI) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(takeUntil(this._destroy))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBrokerDI = userList.find((user) => user.brokerCode === itemSelectedDI['brokerCode']);
          if (!currentBrokerDI) return;
          this.store.dispatch(getCurrentBrokerView({ data: currentBrokerDI }));
        });
    });
  }
}
