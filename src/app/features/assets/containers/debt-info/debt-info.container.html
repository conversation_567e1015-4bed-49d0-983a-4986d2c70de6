<div class="debt-info-container">
  <div class="header-debt-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-72' | translate}}</div>
      <div class="number-info-cls" *ngIf="!showCollapse">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[0]}}
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[1]}}
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[2]}}
        </div>
        <div class="dropdown-btn" *ngIf="!showCollapse" (click)="toggleButtons()">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div>
      </div>
      <div class="collapse-btn" *ngIf="showCollapse" (click)="toggleButtons()">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter,
        }"
      ></app-action-btn>
    </div>
  </div>

  <div [ngClass]="{'hidden': !showCollapse}">
    <app-slide-stock [tags]="tags"></app-slide-stock>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); openAssetInfoDetail($event);"
      (resizeColumn)="resizeColumn($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
      class="table-custom-cls"
    >
    </sha-grid>
  </div>
</div>

<ng-template #marginDebt let-marginDebt="templateInfo">
  @if(marginDebt) {
  <div>
    <ng-container *ngIf="marginDebt.numberMargin > 0">
      <div class="debt-info-increase typo-body-12">
        <img src="./assets/icons/up.svg" alt="document-logo" />
        <span class="typo-body-12"
          >+{{ marginDebt.numberMargin | numberFormat}} +{{ marginDebt.percentMargin | numberFormat : 'percent' }}</span
        >
      </div>
    </ng-container>
    <ng-container *ngIf="marginDebt.numberMargin < 0 ">
      <div class="debt-info-reduce typo-body-12">
        <img src="./assets/icons/down.svg" alt="document-logo" />
        <span class="typo-body-12"
          >{{ marginDebt.numberMargin | numberFormat }} {{ marginDebt.percentMargin | numberFormat : 'percent' }}</span
        >
      </div>
    </ng-container>
    <ng-container *ngIf="marginDebt.numberMargin === 0">
      <div class="debt-info-stable typo-body-12">
        <img src="./assets/icons/minus.svg" alt="document-logo" />
        <span *ngIf="marginDebt.percentMargin !== 0 " class="typo-body-12"
          >{{ marginDebt.numberMargin | numberFormat }} {{ marginDebt.percentMargin.toFixed(2) }}%</span
        >
        <span *ngIf="marginDebt.percentMargin === 0 " class="typo-body-12"
          >{{ marginDebt.numberMargin | numberFormat }} - {{ marginDebt.percentMargin.toFixed(2) }}%</span
        >
      </div>
    </ng-container>
  </div>
  } @else {
  <div>-</div>
  }
</ng-template>
