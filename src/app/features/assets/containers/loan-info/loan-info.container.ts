import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { DestroyService } from 'src/app/core/services';
import { ActionButton, IActionBtn } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { ICustomers, LOAN_STATUS_CLASS_MAP, LOAN_STATUS_MAP } from '../../constant/assets';
import { selectFilterLoanInfo$, selectSearchValue$ } from '../../stores/asset.selectors';
import { take, takeUntil } from 'rxjs';
import { Store } from '@ngrx/store';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { deepClone, rangeMatchData } from 'src/app/shared/utils/utils';
import { LoanInfoFilterComponent } from '../../components/loan-info-filter/loan-info-filter.component';
import { IFilterLoanInfoParam } from '../../models/asset';
import { resetFilterLoanInfo, setFilterLoanInfo } from '../../stores/asset.actions';
import { IClickOnColumnEvent } from '@shared/models';
import { AssetInfoDetailComponent } from '../../components/assets-info-detail/assets-info-detail.component';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';

interface ILoanData {
  dueDate: string;
  loanDate: string | null;
  remainDebt: number;
  paidDebt: number;
  openingDebt: number;
  tempInterest: number;
  tempLoanFee: number;
  debtId: string | number;
  status: number | null;
  accountNumber: string | number;
  customerName?: string;
  children?: ILoanData[];
}

/**
 * Món vay
 */
@Component({
  selector: 'app-loan-info',
  templateUrl: './loan-info.container.html',
  styleUrl: './loan-info.container.scss',
})
export class LoanInfoContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;
  @ViewChild('debtId', { static: true }) debtId: TemplateRef<any> | null = null;

  filterOptions!: IFilterLoanInfoParam;

  totalLoan = {
    account: 0,
    remainDebt: 0,
    paidDebt: 0,
    openingDebt: 0,
    tempInterest: 0,
    tempLoanFee: 0,
  };
  showCollapse: boolean = true;
  tags: string[] = [];
  customNumberFormat = customNumberFormat;
  brokerButton: IActionBtn = {
    label: '',
    icons: 'icon:people-icon',
    name: 'broker',
    isDisplayed: false,
    tag: ActionButton.broker,
  };

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(private readonly _destroy: DestroyService, private readonly store: Store) {
    super();
    this.brokerInfo();
    this.actionButtons.unshift(this.brokerButton);
    this.toggleButtonByTags([
      ActionButton.broker,
      ActionButton.loading,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);
    this.data = this.updateLoanData(this.data);
    const tags = this.totalLoan;
    const { account, remainDebt, paidDebt, openingDebt, tempInterest, tempLoanFee } = tags;
    this.tags = [
      `${account} Tài khoản`,
      `Dư nợ còn lại: ${customNumberFormat(remainDebt)}`,
      `Dư nợ đã trả : ${customNumberFormat(paidDebt)}`,
      `Dư nợ ban đầu: ${customNumberFormat(openingDebt)}`,
      `Lãi vay: ${customNumberFormat(tempInterest)}`,
      `Phí vay: ${customNumberFormat(tempLoanFee)}`,
    ];
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.data = deepClone([]);
    this.initialData = deepClone(this.data);
    const baseColumnConfigLoanInfo = {
      minWidth: 30,
      width: 156,
      displayValueFn: (v: number) => {
        return v ? customNumberFormat(Math.round(v)) : '-';
      },
      isDisplay: true,
      resizable: true,
      align: 'end',
    } as any


    this.columnConfigs = [
      {
        name: 'Ngày đáo hạn',
        minWidth: 156,
        width: 156,
        tag: 'dueDate',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        displayValueFn: (v) => {
          return v;
        },
        pinned: 'left',
      },
      {
        name: 'Ngày vay',
        minWidth: 30,
        width: 156,
        tag: 'loanDate',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          return v ?? '...';
        },
      },
      {
        name: 'Dư nợ còn lại',
        tag: 'remainDebt',
        ...baseColumnConfigLoanInfo
      },
      {
        name: 'Dư nợ đã trả',
        tag: 'paidDebt',
        ...baseColumnConfigLoanInfo

      },
      {
        name: 'Dư nợ ban đầu',
        tag: 'openingDebt',
        ...baseColumnConfigLoanInfo
      },
      {
        name: 'Lãi vay tạm tính',
        tag: 'tempInterest',
        panelClass: 'temporarily-loan-interest',
        ...baseColumnConfigLoanInfo
      },
      {
        name: 'Phí vay tạm tính',
        tag: 'tempLoanFee',
        minWidth: 30,
        width: 156,
        displayValueFn: (v: number) => {
          return v ? customNumberFormat(Math.round(v)) : '-';
        },
        isDisplay: true,
        resizable: true,
        align: 'end',
      },
      {
        name: '# món vay',
        tag: 'debtId',
        minWidth: 30,
        width: 156,
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Tình trạng khoản vay',
        minWidth: 30,
        width: 160,
        tag: 'status',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (v == null) return '...';
          else return LOAN_STATUS_MAP[v];
        },
        dynamicClass: (v) => {
          if (v == null) return 'null';
          else return LOAN_STATUS_CLASS_MAP[v];
        },
        align: 'start',
      },
      {
        name: 'Số tài khoản',
        minWidth: 30,
        width: 165,
        tag: 'accountNumber',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 355,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
      },
    ];

    this.store
      .select(selectFilterLoanInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter?.isFilter;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchMultipleLevel(value ?? '', ['dueDate', 'loanDate', 'accountNumber', 'customerName']);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterLoanInfo());
  }

  /**
   * Thông tin của broker
   */
  brokerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        this.brokerButton.label = `${user?.brokerCode}: ${user?.brokerName}`;
      });
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    const customersExist: string[] = [];
    const customers: ICustomers[] = [];
    this.data.forEach((item: any) => {
      item?.children.forEach((i: any) => {
        const isCustomerExist = customersExist.includes(i.customerName);
        if (!isCustomerExist) {
          customersExist.push(i.customerName);
          customers.push({
            name: i.customerName,
            group: i.accountNumber,
          });
        }
      });
    });

    switch (tag) {
      case 'broker':
        this.changeViewBroker();
        break;

      case 'filter':
        {
          const ref = this.openFilter(LoanInfoFilterComponent, {
            width: '800px',
            data: {
              filterOptions: this.filterOptions,
              customers,
            },
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe({
              next: (v) => {
                if (!v) return;
                this.applyFilterLoanInfo(v);
              },
            });
        }
        break;
    }
  }

  /**
   * ApplyFilterLoanInfo
   * @param data
   */
  applyFilterLoanInfo(data: any) {
    const { optionFilter, type } = data;

    if (type === 'save') {
      const newListFilterLI = this.saveFunc(optionFilter);
      this.filteredData = newListFilterLI;
      this.data = newListFilterLI;
    } else if (type === 'default') {
      this.isFilter = false;
      this.filteredData = [];

      this.updateDataWhenSetDefaultFilterLI()
      this.store.dispatch(resetFilterLoanInfo());
    }
  }

  private updateDataWhenSetDefaultFilterLI() {
    this.isSearch
      ? this.store
        .select(selectSearchValue$)
        .pipe(takeUntil(this._destroy))
        .subscribe((value) => {
          this.searchMultipleLevel(value ?? '', ['accountNumber', 'customerName']);
        })
      : (() => {
        this.data = this.initialData;
      })();
  }

  /**
   * UpdateParamInStore
   * @param optionFilter
   */
  updateParamInStore(optionFilter: any) {
    this.store.dispatch(setFilterLoanInfo({ params: optionFilter }));
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilter: any) {
    this.updateParamInStore(optionFilter);
    const dataClone = deepClone(this.initialData);
    const newListFilter = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(dataClone, optionFilter);
    return newListFilter;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    let newListFilter: any[] = [];

    const {
      customersSelect,
      dueDate,
      loanStatus,
      openingDebt,
      remainDebt,
      paidDebt,
      tempLoanFee,
      tempInterest,
      loanDate,
    } = optionFilter;

    data
      .filter((i) => {
        const startDueDate = new Date(dueDate.start ?? '').getTime();
        const endDueDate = new Date(dueDate.end ?? '').getTime();
        const dobDueDate = new Date(i.dueDate.split('/').reverse().join('-')).getTime();
        const isDueDateMatch =
          (dueDate.start ? dobDueDate >= startDueDate : true) && (dueDate.end ? dobDueDate <= endDueDate : true);
        const isOpeningDebtMatch = rangeMatchData(i.openingDebt, openingDebt);

        const isRemainDebtMatch = rangeMatchData(i.remainDebt, remainDebt);

        const isPaidDebtDebtMatch = rangeMatchData(i.paidDebt, paidDebt);

        const isTempLoanFeeMatch = rangeMatchData(i.tempLoanFee, tempLoanFee);

        const isTempInterestMatch = rangeMatchData(i.tempInterest, tempInterest);

        return (
          isDueDateMatch &&
          isOpeningDebtMatch &&
          isRemainDebtMatch &&
          isPaidDebtDebtMatch &&
          isTempLoanFeeMatch &&
          isTempInterestMatch
        );
      })
      .forEach((d) => {
        const childrenMatch = d.children?.filter((item: any) => {
          const startLoanDate = new Date(loanDate.start ?? '').getTime();
          const endLoanDate = new Date(loanDate.end ?? '').getTime();
          const dobLoanDate = new Date(item.loanDate.split('/').reverse().join('-')).getTime();
          const isLoanDateMatch =
            (loanDate.start ? dobLoanDate >= startLoanDate : true) &&
            (loanDate.end ? dobLoanDate <= endLoanDate : true);
          return (
            customersSelect.some((cus: string) => (item.customerName as string) === cus) &&
            loanStatus.some((sta: number) => (item.status as number) === sta) &&
            isLoanDateMatch
          );
        });

        if (childrenMatch.length > 0) {
          newListFilter = [...newListFilter, { ...d, children: childrenMatch, isExpanded: true }];
        }
      });

    newListFilter.forEach((item) => this.traverseNodeAddExpanded(item));
    for (let i = 0; i < newListFilter.length; i++) {
      const value = newListFilter[i];
      if (value.isExpanded) {
        const childrenCount = value.children ? value.children.length : 0;
        this.flattenMultipleLevelChildrenItems(newListFilter, i + 1, 0, value.children);
        i += childrenCount;
      }
    }

    return newListFilter;
  }

  /**
   * searchData
   * @param {string} value searchData
   */
  searchData(value: string) {
    let searchData: any[] = [];
    const dataClone = deepClone(this.data);
    const searchValue = value.toString().toLowerCase();

    if (value) {
      dataClone.forEach((item) => {
        let searchChildren: any[] = [];

        if (item.children) {
          searchChildren = item.children.filter((child: any) => this.containsSearchValue(child, searchValue));
        }

        if (this.containsSearchValue(item, searchValue) || searchChildren.length > 0) {
          const uniqueCustomer = [...new Set(searchChildren.map((child) => child.customerName))];
          const updateItem = {
            ...item,
            children: searchChildren.length > 0 ? searchChildren : item.children,
            accountNumber: `${searchChildren.length} tài khoản`,
            debtId: `${searchChildren.length} món vay`,
            customerName: `${uniqueCustomer.length} khách hàng`,
          };

          searchData.push(updateItem);
        }
      });

      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchData = deepClone(this.initialData);
      this.isSearch = false; // Set isSearch to false when not searching
    }

    this.data = deepClone(searchData);
  }

  /**
   * containsSearchValue
   * @param child
   * @param searchValue
   */
  containsSearchValue(child: any, searchValue: string): boolean {
    if (
      child.accountNumber?.toString().toLowerCase().includes(searchValue) ??
      child.customerName?.toString().toLowerCase().includes(searchValue)
    ) {
      return true;
    }

    return false;
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * Clone LoanData then use Set with customerName and length of debtId,accountNumber
   * with each object have children
   * @param {ILoanData[]} data - ILoanData[]
   */
  updateLoanData(data: ILoanData[]): ILoanData[] {
    return data.map((item) => {
      if (item.children) {
        // Update children debtId to include '#'
        const updatedChildren = item.children.map((child) => ({
          ...child,
          debtId: `#${child.debtId}`,
        }));

        return {
          ...item,
          debtId: `${item.debtId} món vay`,
          accountNumber: `${item.accountNumber} tài khoản`,
          customerName: `${item.customerName} khách hàng`,
          children: updatedChildren,
        };
      }
      return item;
    });
  }

  /**
   * OpenAssetInfoDetail
   * @param event IClickOnColumnEvent
   */
  openAssetInfoDetail(event: IClickOnColumnEvent) {
    const { element } = event;
    if (element.children?.length) return;
    this.dialogService.openPopUp(AssetInfoDetailComponent, {
      width: 'calc(100vw - 200px)',
      height: 'calc(100vh - 50px)',
      panelClass: 'open-asset-info-cls',
      data: {
        status: 'loan-detail',
        element,
      },
    });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    // FIX WHEN HAVE ROLE
  }
}
