import {
  Component,
  ElementRef,
  HostListener,
  Inject,
  OnDestroy,
  OnInit,
  Optional,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { DestroyService } from 'src/app/core/services';
import { ActionButton, IActionBtn } from 'src/app/shared/components/action-btn/action-btn.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { PopoverGuaranteeMoneyComponent } from '../../components/popover-guarantee-money/popover-guarantee-money.component';
import { SubmitGuaranteeOptionsComponent } from '../../components/submit-guarantee-options/submit-guarantee-options.component';
import {
  pageIndexPurchasingPower$,
  selectFilteredPuchasingPower$,
  selectFilterPuschasingPower$,
  selectGuaranteeValue$,
  selectPurchasingPower$,
  selectSearchValue$,
} from '../../stores/asset.selectors';
import { combineLatest, distinctUntilChanged, take, takeUntil } from 'rxjs';
import { IFilterPurchasingPowerInfoParam, IGuaranteeInfo, IPurchasingPowerResponse } from '../../models/asset';
import { deepClone, rangeMatchData, searchByValue, updateBorkerName } from 'src/app/shared/utils/utils';
import { v4 as uuidv4 } from 'uuid';
import { GridComponent } from '../../../../shared/components/table-custom/grid.component';
import { PurchasingPowerFilterComponent } from '../../components/purchasing-power-filter/purchasing-power-filter.component';
import {
  getDataPurchasingPowerInfo,
  getDataPurchasingPowerInfoSuccess,
  resetFilterPurchasingPower,
  resetSearch,
  setFilterPurchasingPower,
  updatePageIndexPurchasingPower,
} from '../../stores/asset.actions';
import { IClickOnColumnEvent } from '@shared/models';
import { AssetInfoDetailComponent } from '../../components/assets-info-detail/assets-info-detail.component';
import { Router } from '@angular/router';
import { IAllAccountNumber, IAllLevelOfBroker } from 'src/app/shared/models/global';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { CompareValueService } from 'src/app/shared/directives/compare-value/compare-value.service';
import { IListOptions } from '../../constant/assets';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { getCurrentBrokerView, getListAccountNumberAndLevelByBrokerView } from 'src/app/stores/shared/shared.actions';
import { OptionsAssetComponent } from '../../components/options-asset/options-asset.component';

interface IActionPurchasingPower {
  brokerCode: string[];
  accountNumbers: string[];
}
/**
 * Thông tin sức mua
 */
@Component({
  selector: 'app-purchasing-power',
  templateUrl: './purchasing-power.container.html',
  styleUrl: './purchasing-power.container.scss',
  providers: [DestroyService],
})
export class PurchasingPowerContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('guarantee', { static: true }) guarantee: TemplateRef<any> | null = null;

  @ViewChild('submitGuarantee', { static: true }) submitGuarantee: TemplateRef<any> | null = null;

  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('numberTemplate', { static: true }) numberTemplate: TemplateRef<any> | null = null;

  showCollapse: boolean = false;

  filterOptions!: IFilterPurchasingPowerInfoParam | null;

  tags: string[] = [];

  duplicateTags: string[] = [];

  isOpenOtherPopUp = false;

  pageSize = 10;

  totalPage!: number;

  currentPage = 1;

  initialCustomer!: IAllAccountNumber[];

  dataStore: any[] = [];

  LIST_MG: IListOptions[] = [];

  brokerCode = '';

  isLoadingPage = false;

  actionPurchasingPower: IActionPurchasingPower | null = null;

  currentBrokerCode = '';

  storeTotalPage = 1;

  private popoverSubmitRef!: PopoverRef<any>;
  private isPopoverOpen = false;
  private isMouseOverPopover = false;

  updatedValue!: IGuaranteeInfo;
  customNumberFormat = customNumberFormat;
  TotalMoneyData = {
    account: 0,
    purchasingPower: 0,
    cash: 0,
    nav: 0,
  };
  brokerButton: IActionBtn = {
    label: '',
    icons: 'icon:people-icon',
    name: 'broker',
    isDisplayed: false,
    tag: ActionButton.broker,
  };

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param store Store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly store: Store,
    private readonly localStorageService: LocalStorageService,
    private readonly compareValueService: CompareValueService,
    private readonly router: Router
  ) {
    super();
    this.brokerInfoPP();
    this.setButtonInPurchasingPower();
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }

  private setButtonInPurchasingPower() {
    this.toggleButtonByTags([ActionButton.broker, ActionButton.loading, ActionButton.display, ActionButton.filter]);
  }
  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 155,
        width: 155,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 200,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Tài sản ròng (NAV)',
        minWidth: 30,
        width: 156,
        tag: 'nav',
        isDisplay: true,
        align: 'end',
        resizable: true,
        cellTemplate: this.numberTemplate,
        isCompare: true,
      },
      {
        name: 'Tổng tiền',
        minWidth: 30,
        width: 156,
        tag: 'cash',
        isDisplay: true,
        align: 'end',
        resizable: true,
        cellTemplate: this.numberTemplate,
        isCompare: true,
      },
      {
        name: 'Sức mua',
        minWidth: 30,
        width: 156,
        tag: 'purchasingPower',
        isDisplay: true,
        align: 'center',
        resizable: true,
        cellTemplate: this.numberTemplate,
        componentConfig: {
          isShowDetail: true,
        },
        isCompare: true,
      },
      {
        name: 'Tiền bảo lãnh',
        minWidth: 30,
        width: 180,
        tag: 'guarantee',
        isDisplay: true,
        cellTemplate: this.numberTemplate,
        align: 'center',
        resizable: true,
        componentConfig: {
          isShowDetail: true,
        },
      },
      {
        name: 'Tiền bảo lãnh đã SD',
        minWidth: 30,
        width: 160,
        tag: 'usedGuarantee',
        cellTemplate: this.numberTemplate,
        componentConfig: {
          isShowDetail: true,
        },
        isDisplay: true,
        align: 'center',
        resizable: true,
      },
      {
        name: 'Tiền bảo lãnh cần nộp',
        minWidth: 30,
        width: 180,
        tag: 'toSubmitGuarantee',
        isDisplay: true,
        cellTemplate: this.numberTemplate,
        componentConfig: {
          isShowDetail: true,
        },
        align: 'center',
        resizable: true,
      },
    ];

    this.store
      .select(selectFilterPuschasingPower$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        if (filter) {
          this.isFilter = filter.isFilter ?? false;
        } else {
          this.isFilter = false;
        }
        this.filterOptions = filter as IFilterPurchasingPowerInfoParam;
      });

    this.getPurchasingPowerByStore();

    combineLatest([this.store.select(selectFilteredPuchasingPower$), this.store.select(pageIndexPurchasingPower$)])
      .pipe(takeUntil(this._destroy))
      .subscribe(([filteredData, pageIndex]) => {
        if (this.isFilter) {
          this.filteredData = filteredData.map((d) => {
            const customerMatch = this.initialCustomer.find((cus) => cus.accountNumber === d.accountNumber);
            return {
              ...d,
              customerName: customerMatch ? customerMatch.customerName : d.customerName,
              children: d.children.map((child: any) => ({
                ...child,
                accountNumber: child.accountNumber + ' - ' + child.subAccount,
              })),
            };
          });
          this.totalPage = Math.ceil(filteredData.length ? filteredData.length / this.pageSize : 1);
          this.data = this.filteredData.slice(this.pageSize * (+pageIndex - 1), this.pageSize * +pageIndex);
          this.totalTags(this.data);
        }
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe(() => {
        if (this.grid) this.map.resetExpandedRowAmountMap();
      });

    this.store
      .select(selectGuaranteeValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((editedData) => {
        if (!editedData) return;
        this.updatedValue = editedData;
        // Check nếu là grant sẽ cấp bảo lãnh
        if (this.updatedValue.type === 'grant') {
          this.grantGuaranteeData();
        } else if (this.updatedValue.type === 'submitGuarantee') {
          this.submitGuaranteeData();
        }
      });
  }

  private getPurchasingPowerByStore() {
    combineLatest([
      this.store.select(selectAllAccountNumberListByBrokerView$),
      this.store.select(selectAllBrokerLevelListByBrokerView$),
      this.store.select(pageIndexPurchasingPower$).pipe(distinctUntilChanged(() => (this.isLoadingPage = false))),
      this.store.select(selectSearchValue$),
    ])
      .pipe(distinctUntilChanged(), takeUntil(this._destroy))
      .subscribe(([customerAccount, brokers, pageIndex, search]) => {
        this.initialCustomer = customerAccount ?? [];
        let customerBasePurchasingPower: string[] = [];
        let customerSearchPurchasingPower: string[] = [];

        customerBasePurchasingPower = (this.initialCustomer ?? []).map((t) => t.accountNumber);

        if (search) {
          this.isFilter = false;
          customerSearchPurchasingPower = searchByValue(this.initialCustomer, search).map(
            (t: IAllAccountNumber) => t.accountNumber
          );
          this.filterOptions = null;

          if (!customerSearchPurchasingPower.length) {
            this.totalPage = 1;
            this.currentPage = 1;
            this.data = [];
            return;
          }
        }

        this.storeTotalPage = Math.ceil(
          (search ? customerSearchPurchasingPower : customerBasePurchasingPower).length / this.pageSize
        );
        this.transformData(
          search ? customerSearchPurchasingPower : customerBasePurchasingPower,
          brokers,
          pageIndex,
          search
        );
        this.currentPage = +pageIndex;
      });
  }

  /**
   * NgAfterViewInit of PurchasingPowerContainer
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;

    if (this.data.length > 0) {
      this.addItemsAt({ index: 0, items: this.data[0].children });
    }
    this.initialData = structuredClone(this.data);
  }

  /**
   * check width when resize
   * @returns
   */
  @HostListener('window:resize', ['$event'])
  onResize(): void {
    if (this.showCollapse) return;
    this.showCollapse = this.checkWidthOfScreenToCollapse(); // Recheck width on window resize
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    if (this.isFilter) {
      this.store.dispatch(resetFilterPurchasingPower());
    }

    if (this.isSearch) {
      this.store.dispatch(resetSearch());
    }
    this.localStorageService.removeData('currentPage');
    this.compareValueService.clearAll();
  }

  getBrokerByParentBrokerId = (brokerObject: IAllLevelOfBroker[]) => {
    let brokerCodesPurchasingPower: string[] = [];
    brokerObject.forEach((broker) => {
      brokerCodesPurchasingPower.push(broker.brokerCode);

      if (Array.isArray(broker.children) && broker.children.length > 0) {
        brokerCodesPurchasingPower = brokerCodesPurchasingPower.concat(this.getBrokerByParentBrokerId(broker.children));
      }
    });
    return brokerCodesPurchasingPower;
  };

  /**
   * Lấy acc và sub acc để lấy call api get list
   */
  transformData(customerId: string[], brokers: IAllLevelOfBroker[], pageIndex: number | string, searchValue?: string) {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(take(1))
      .subscribe((currentBroker) => {
        if (!currentBroker) return;
        const brokerIdPP = this.route.snapshot.queryParams['brokerId'];

        const indexBrokerPP = brokers.findIndex((broker) => broker.brokerCode === brokerIdPP);

        let brokerCodeIdsPP: string[] = [];
        if (indexBrokerPP !== -1 && brokers[indexBrokerPP]) {
          brokerCodeIdsPP = [brokerIdPP, ...this.getBrokerByParentBrokerId(brokers[indexBrokerPP].children ?? [])];
        }

        const listCustomerIdPP = customerId.slice(this.pageSize * (+pageIndex - 1), this.pageSize * +pageIndex);
        let purchasingCustomerInfo = {
          brokerCode: brokerCodeIdsPP,
          accountNumbers: listCustomerIdPP,
        };

        if (!listCustomerIdPP.length) {
          this.store.dispatch(getDataPurchasingPowerInfoSuccess({ data: [] }));
          return;
        }
        this.brokerCode = brokerIdPP ?? currentBroker.brokerCode;
        this.actionPurchasingPower = {
          brokerCode: brokerCodeIdsPP,
          accountNumbers: customerId.slice(0, this.pageSize),
        };
        this.store.dispatch(getDataPurchasingPowerInfo({ purchasingCustomerInfo }));
      });

    this.store
      .select(selectPurchasingPower$)
      .pipe(takeUntil(this._destroy))
      .subscribe((purchasingPowerList) => {
        if (!purchasingPowerList.length) {
          this.data = [];
          this.initialData = [];
          this.isFilter = false;
          this.totalPage = 1;
          this.currentPage = 1;
          this.totalTags(this.data);
          return;
        }

        this.totalPage = this.storeTotalPage;
        this.data = purchasingPowerList.reduce((initial: any, d, index) => {
          const customerMatch = this.initialCustomer.find((cus) => cus.accountNumber === d.accountNumber);
          const object = {
            ...d,
            customerName: customerMatch ? customerMatch?.customerName : d.customerName,
            isExpanded:
              this.map.getHasExpandedAmountByRowId(d.id ?? '') &&
              (this.map.getExpandedAmountByRowId(d.id ?? '') ?? 0) > 0,
            children: d.children.map((child) => ({
              ...child,
              parent: {
                ...d,
                customerName: customerMatch ? customerMatch.customerName : d.customerName,
              },
              accountNumber: `${child.accountNumber} - ${child.subAccount}`,
            })),
          };
          if (
            this.map.getHasExpandedAmountByRowId(d.id ?? '') &&
            (this.map.getExpandedAmountByRowId(d.id ?? '') ?? 0) > 0
          ) {
            const items = d.children.map((t) => ({
              ...t,
              parent: object,
              accountNumber: `${t.accountNumber} - ${t.subAccount}`,
              backgroundColor: '#f6f6f6',
            }));
            initial.push(object, ...items);
          } else {
            initial.push(object);
          }
          return initial;
        }, []);

        this.totalTags(this.data);
        this.dataStore = this.data;
        this.initialData = deepClone(this.data);

        // dropdown first element
        if (!this.data[0].isExpanded) {
          if (this.isLoadingPage) return;
          this.data[0].isExpanded = true;
          this.addItemsAt({ index: 0, items: this.data[0].children });
        }

        setTimeout(() => {
          if (this.showCollapse) return;
          this.showCollapse = this.checkWidthOfScreenToCollapse();
        });
      });
  }

  /**
   * changeCurrentPage
   * @param data
   */
  changeCurrentPage(currentPage: number) {
    this.currentPage = currentPage;
    if (this.isFilter) {
      this.updateDataItem();
      this.totalTags(this.data);
    } else {
      this.store.dispatch(updatePageIndexPurchasingPower({ pageIndex: currentPage }));
    }
  }

  updateDataItem() {
    const ITEM_SIZE = 10;
    const start = (this.currentPage - 1) * ITEM_SIZE;
    const end = Math.min(start + ITEM_SIZE, this.filteredData.length);
    const data = this.filteredData.slice(start, end);
    this.data = [...data];
  }

  /**
   * calculate totalTags
   * @param {IPurchasingPowerResponse[]} data
   */
  totalTags(data: IPurchasingPowerResponse[]) {
    const amountAccount = data.filter((item) => item.children && item.children.length > 0).length;
    this.tags = [
      `${amountAccount} Tài khoản`,
      // `Sức mua: ${customNumberFormat(this.calculateSum(data, 'purchasingPower'))}`,
      `Tổng tiền: ${customNumberFormat(this.calculateSum(data, 'cash'))}`,
      `NAV: ${customNumberFormat(this.calculateSum(data, 'nav'))}`,
    ];

    this.duplicateTags = [...this.tags, ...this.tags];
  }

  /**
   * calculateSum
   * @param {IPurchasingPowerResponse[]} data
   * @param {string} field
   * @returns {number}
   */
  calculateSum(data: IPurchasingPowerResponse[], field: keyof IPurchasingPowerResponse): number {
    return data.reduce((sum, item) => {
      if (!item.children) return sum;
      const value = item[field];
      return sum + (typeof value === 'number' ? value : 0);
    }, 0);
  }

  /**
   * ConvertData
   * hàm format data phù hợp với custom table
   * @param {any[]} data data
   * @returns {any[]} new data
   */
  convertData(data: any[]) {
    return data.map((parent: any, index: number) => {
      return {
        ...parent,
        customerGroup: {
          name: parent.customerGroup,
          childrenCount: parent.children.length,
        },
        id: uuidv4(),

        children: parent.children.map((child: any) => {
          return {
            ...child,

            idParent: index,
            id: uuidv4(),
          };
        }),
      };
    });
  }

  /**
   * Cấp bảo lãnh
   * grantGuaranteeData
   */
  grantGuaranteeData() {
    let updatedData = [];
    const initialDataClone = deepClone(this.data);
    if (this.updatedValue) {
      updatedData = initialDataClone.map((item) => {
        let isAccountNumberMatched = false;

        // Check if any child has the matching account number
        const updatedChildren = item.children?.map((child: any) => {
          if (child.accountNumber === this.updatedValue.accountNumber) {
            isAccountNumberMatched = true;
            return {
              ...child,
              guarantee: child.guarantee + this.updatedValue.value,
              purchasingPower: child.purchasingPower + this.updatedValue.value,
            };
          }
          return child;
        });

        // Check trùng sau khi rải children ngang hàng với parent rồi update
        if (item.accountNumber === this.updatedValue.accountNumber) {
          return {
            ...item,
            guarantee: item.guarantee + this.updatedValue.value,
            purchasingPower: item.purchasingPower + this.updatedValue.value,
          };
        }

        return {
          ...item,
          guarantee: isAccountNumberMatched ? item.guarantee + this.updatedValue.value : item.guarantee,
          purchasingPower: isAccountNumberMatched
            ? item.purchasingPower + this.updatedValue.value
            : item.purchasingPower,
          children: updatedChildren,
        };
      });

      this.isSearch = true; // Set isSearch to true when updateData in table
    }
    this.data = deepClone(updatedData);
  }

  /**
   * Nộp bảo lãnh
   * submitGuaranteeData
   */
  submitGuaranteeData() {
    let updatedData = [];
    const initialDataClone = deepClone(this.data);

    if (this.updatedValue) {
      updatedData = initialDataClone.map((item) => {
        let isAccountNumberMatched = false;

        // Check if any child has the matching account number
        const updatedChildren = item.children?.map((child: any) => {
          if (child.accountNumber === this.updatedValue.accountNumber) {
            isAccountNumberMatched = true;
            return {
              ...child,
              toSubmitGuarantee: child.toSubmitGuarantee - (this.updatedValue.value ?? 0),
              cash: child.cash - (this.updatedValue.value ?? 0),
              purchasingPower: child.purchasingPower - (this.updatedValue.value ?? 0),
            };
          }
          return child;
        });

        // Check trùng sau khi rải children ngang hàng với parent rồi update
        if (item.accountNumber === this.updatedValue.accountNumber) {
          return {
            ...item,
            toSubmitGuarantee: item.toSubmitGuarantee - (this.updatedValue.value ?? 0),
            cash: item.cash - (this.updatedValue.value ?? 0),
            purchasingPower: item.purchasingPower - (this.updatedValue.value ?? 0),
          };
        }

        return {
          ...item,
          toSubmitGuarantee: isAccountNumberMatched
            ? item.toSubmitGuarantee - (this.updatedValue.value ?? 0)
            : item.toSubmitGuarantee,
          cash: isAccountNumberMatched ? item.cash - (this.updatedValue.value ?? 0) : item.cash,
          purchasingPower: isAccountNumberMatched
            ? item.purchasingPower - (this.updatedValue.value ?? 0)
            : item.purchasingPower,
          children: updatedChildren,
        };
      });

      this.isSearch = true; // Set isSearch to true when updateData in table
    }
    this.data = deepClone(updatedData);
  }

  /**
   * searchData
   * @param {string} value searchValue
   */
  searchData(value: string) {
    let searchDataPP = [];
    const dataClonePP = deepClone(this.data);
    const searchValuePP = value.toString().toLowerCase();
    if (value) {
      searchDataPP = dataClonePP.filter((item) => {
        return this.containsSearchValue(item, searchValuePP);
      });

      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchDataPP = deepClone(this.initialData);
      this.isSearch = false; // Set isSearch to false when not searching
    }

    this.data = deepClone(searchDataPP);
  }

  /**
   * containsSearchValue of PP
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    const accountNumberPP = item.accountNumber?.toString().toLowerCase();
    const customerNamePP = item.customerName?.toString().toLowerCase();

    return accountNumberPP?.includes(searchValue) ?? customerNamePP?.includes(searchValue);
  }

  /**
   * Thông tin của broker thông tin sức mua
   */
  brokerInfoPP() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBroker) => {
        if (!currentBroker) return;
        this.currentBrokerCode = currentBroker.brokerCode;

        combineLatest([
          this.store.select(selectInfoUserLogin$),
          this.store.select(selectAllBrokerLevelListByBrokerView$),
        ])
          .pipe(takeUntil(this._destroy))
          .subscribe(([userList, brokers]) => {
            const queryParamsPP = this.route.snapshot.queryParams;

            if (!userList) return;

            const brokerConvertPP = updateBorkerName([...brokers], [...userList]);

            const subBrokerPP = brokerConvertPP.map((broker) => {
              return {
                brokerCode: `${broker.brokerCode}`,
                name: `${broker.brokerName}`,
                isSelect: queryParamsPP['brokerId']
                  ? broker.brokerCode === queryParamsPP['brokerId']
                  : broker.brokerCode === currentBroker.brokerCode,
              };
            });

            this.LIST_MG = [...subBrokerPP];
            const brokerPP = this.LIST_MG.find((t) => t.isSelect);

            if (brokerPP) {
              this.findButtonByTags(ActionButton.broker).label = brokerPP['name'] ?? '';
            }
          });
      });
  }

  /**
   *  Open filter component of PP
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'loading':
        if (this.isFilter) return;
        this.loadPagePP();
        break;

      case 'broker':
        this.changeViewBrokerPP();
        break;

      case 'filter':
        {
          const ref = this.openFilter(PurchasingPowerFilterComponent, {
            width: '400px',
            data: this.filterOptions,
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe({
              next: (v) => {
                if (!v) return;
                this.applyFilter(v);
              },
            });
        }
        break;

      case 'display':
        this.compareValueService.clearAll();
        break;
    }
  }

  /**
   * Tải lại
   */
  loadPagePP() {
    this.isLoadingPage = true;
    this.store
      .select(selectAllBrokerLevelListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((brokers) => {
        this.store
          .select(pageIndexPurchasingPower$)
          .pipe(take(1))
          .subscribe((pageIndex) => {
            if (this.isFilter || this.isSearch) {
              this.isFilter = false;
              this.isSearch = false;
              this.store.dispatch(resetFilterPurchasingPower());
            }
            this.updateDataOfPP(brokers, pageIndex);
          });
      });
  }

  private updateDataOfPP(brokers: IAllLevelOfBroker[], pageIndex: number) {
    this.totalPage = Math.ceil((this.initialCustomer ?? []).map((t) => t.accountNumber).length / this.pageSize);
    this.transformData(
      (this.initialCustomer ?? []).map((t) => t.accountNumber),
      brokers,
      pageIndex
    );

    this.data.forEach((d) => {
      this.processCompareData(d);
    });
  }

  /**
   * Lưu data để so sánh trong bảng
   */
  processCompareData(data: any) {
    Object.entries(data).forEach(([key, value]) => {
      if (typeof value === 'number' || key === 'id') {
        this.compareValueService.setData(data.id, key, value as number);
      }
    });
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;
    const currentFilter = this.isFilter;
    if (type === 'save') {
      if (!this.isFilter && !optionFilter?.isFilter) return;
      this.currentPage = 1;
      if (optionFilter.isFilter) {
        this.store.dispatch(setFilterPurchasingPower({ params: optionFilter }));
      } else this.store.dispatch(resetFilterPurchasingPower());
    } else if (type === 'default') {
      this.filteredData = [];
      if (!this.isFilter) {
        return;
      }
      this.currentPage = 1;
      this.store.dispatch(resetFilterPurchasingPower());
      this.map.resetExpandedRowAmountMap();
    }

    if (!currentFilter && !optionFilter?.isFilter && type === 'save') return;

    if (this.actionPurchasingPower) {
      this.store.dispatch(getDataPurchasingPowerInfo({ purchasingCustomerInfo: this.actionPurchasingPower }));
      this.totalPage = Math.ceil(this.initialCustomer.length / this.pageSize);
    }
  }

  /**
   * UpdateParamInStore
   * @param optionFilter
   */
  updateParamInStore(optionFilter: any) {
    this.store.dispatch(setFilterPurchasingPower({ params: optionFilter }));
  }
  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilter: any) {
    const dataClonePP = deepClone(this.initialData);
    const newListFilterPP = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(dataClonePP, optionFilter);

    return newListFilterPP;
  }
  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    const newListFilter = data.filter((item, index) => {
      const { customers, nav, cash, purchasingPower, guarantee, usedGuarantee, toSubmitGuarantee } = optionFilter;
      const isCustomerMatch = (customers ?? []).length ? (customers ?? []).includes(item.accountNumber) : true;

      const isPurchasingPowerMatch = rangeMatchData(item.purchasingPower, purchasingPower);

      const isNavMatch = rangeMatchData(item.nav, nav);

      const isCashMatch = rangeMatchData(item.cash, cash);

      const isGuaranteeMatch = rangeMatchData(item.guarantee, guarantee);

      const isUsedGuaranteeMatch = rangeMatchData(item.usedGuarantee, usedGuarantee);

      const isToSubmitGuaranteeMatch = rangeMatchData(item.toSubmitGuarantee, toSubmitGuarantee);

      return (
        isCustomerMatch &&
        isPurchasingPowerMatch &&
        isNavMatch &&
        isCashMatch &&
        isGuaranteeMatch &&
        isUsedGuaranteeMatch &&
        isToSubmitGuaranteeMatch
      );
    });
    return newListFilter;
  }
  /**
   * OpenPopup based on the event and submitGuarantee
   * provided.
   * @param {Event} event - Event
   * @param {submitGuarantee} submitGuarantee - number
   * @param {accountNumber} accountNumber - string
   */
  openPopoverSubmitGuarantee(event: Event, submitGuarantee: number, accountNumber: string) {
    const originElement = event.target as HTMLElement;
    this.isPopoverOpen = true;
    this.popoverSubmitRef = this.popoverService.open({
      origin: originElement,
      content: SubmitGuaranteeOptionsComponent,
      width: 191,
      height: 96,
      position: 2,
      hasBackdrop: true,
      componentConfig: { submitGuarantee, accountNumber },
    });

    // Add mouse enter/leave event listeners to the popover content
    this.popoverSubmitRef.overlay.hostElement.addEventListener('mouseenter', this.onMouseEnterPopover.bind(this));
    this.popoverSubmitRef.overlay.hostElement.addEventListener('mouseleave', this.onMouseLeavePopover.bind(this));
  }

  /**
   * Close the Popover
   */
  closePopover(): void {
    if (this.popoverSubmitRef && !this.isMouseOverPopover) {
      setTimeout(() => {
        if (!this.isMouseOverPopover) {
          this.popoverService.close(this.popoverSubmitRef);
          this.isPopoverOpen = false;
        }
      }, 200);
    }
  }

  /**
   * Handle mouse enter event on the popover
   */
  onMouseEnterPopover(): void {
    this.isMouseOverPopover = true;
  }

  /**
   * Handle mouse leave event on the popover
   */
  onMouseLeavePopover(): void {
    this.isMouseOverPopover = false;
    this.closePopover();
  }

  /**
   * OpenPopoverGuaranteeMoney
   * @param {MouseEvent} event - event
   * @param element - any
   */
  openPopoverGuaranteeMoney(event: MouseEvent, element: any) {
    this.isOpenOtherPopUp = true;

    const originElement = event.target as HTMLElement;

    const ref = this.popoverService.open({
      origin: originElement,
      content: PopoverGuaranteeMoneyComponent,
      width: 400,
      height: 383,
      position: 0,
      hasBackdrop: true,
      componentConfig: { element },
    });

    ref.afterClosed$.pipe(take(1)).subscribe(() => (this.isOpenOtherPopUp = false));
  }

  /**
   * OpenAssetInfoDetail
   * @param event IClickOnColumnEvent
   */
  openAssetInfoDetail(event: IClickOnColumnEvent) {
    if (this.isOpenOtherPopUp || event.element.children) return;
    const { element } = event;
    this.dialogService.openPopUp(AssetInfoDetailComponent, {
      width: 'calc(100vw - 200px)',
      height: 'calc(100vh - 50px)',
      panelClass: 'open-asset-info-cls',
      data: {
        status: 'purchasing-power-detail',
        element,
        brokerCode: this.brokerCode,
      },
    });
  }

  /**
   * changeViewBrokerPP
   */
  changeViewBrokerPP() {
    const queryParamsPP = this.route.snapshot.queryParams;
    const elementRefPP = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidthPP = elementRefPP.nativeElement as HTMLElement;

    const refPP = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRefPP as any,
      width: elementWidthPP.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    refPP.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelectedPP = res.data.find((i) => i.isSelect);
      if (!itemSelectedPP) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBrokerPP = userList.find((user) => user.brokerCode === itemSelectedPP['brokerCode']);
          const subBrokerPP = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelectedPP['brokerCode']);
          if (queryParamsPP['brokerId'] === itemSelectedPP['brokerCode']) return;
          if (subBrokerPP && !currentBrokerPP) {
            const brokerCodePP = subBrokerPP['brokerCode'] as string;
            this.store.dispatch(
              getListAccountNumberAndLevelByBrokerView({ brokerCode: brokerCodePP, isPrimaryBrokerCode: true })
            );
          }

          if (currentBrokerPP) {
            if (this.currentBrokerCode === currentBrokerPP.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBrokerPP.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBrokerPP }));
          }

          if (subBrokerPP) {
            this.LIST_MG = this.LIST_MG.map((broker) => ({
              ...broker,
              isSelect: broker['brokerCode'] === subBrokerPP['brokerCode'],
            }));

            this.findButtonByTags(ActionButton.broker).label = subBrokerPP['name'] ?? '';
          }
          this.store.dispatch(resetFilterPurchasingPower());
          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));

          this.map.resetExpandedRowAmountMap();

          const subBrokerCodePP = subBrokerPP ? subBrokerPP['brokerCode'] : null;
          this.router.navigate([], {
            queryParams: {
              ...queryParamsPP,
              brokerId: currentBrokerPP ? currentBrokerPP.brokerCode : subBrokerCodePP,
            },
          });
        });
    });
  }

  /**
   * check width của header với tag và button action sẽ show slide
   * @returns {boolean}
   */
  checkWidthOfScreenToCollapse() {
    const headerElementWidth = document.getElementById('header-wrap')?.offsetWidth;
    const leftWidth = document.getElementById('header-left')?.offsetWidth;
    const rightWidth = document.getElementById('header-right')?.offsetWidth;

    if (!leftWidth || !rightWidth || !headerElementWidth) return false;
    return leftWidth + rightWidth > headerElementWidth - 44; // 22 padding 20 gap
  }

  /**
   * valueShow
   */
  numberShowTemplate(value: number) {
    return value === 0 ? '-' : customNumberFormat(value);
  }

  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsAssetComponent,
      width: 'fit-content',
      hasBackdrop: true,
      position: 1,
      componentConfig: {
        brokerCode: this.brokerCode,
        element: element,
      },
    });
  }
}
