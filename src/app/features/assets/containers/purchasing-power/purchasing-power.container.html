<div class="purchasing-power-container">
  <div class="header-purchasing-power" id="header-wrap">
    <div class="left-box" id="header-left">
      <div class="typo-heading-9 text-header">{{'MES-257' | translate}}</div>
      <div class="number-info-cls" *ngIf="!showCollapse">
        <div class="box-info typo-body-11" *ngFor="let tag of tags;">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tag}}
        </div>
      </div>
    </div>

    <div class="right-box" id="header-right">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [listColumnInitial]="columnConfigsInitial"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter,
        }"
      ></app-action-btn>
    </div>
  </div>

  <div class="number-info-collapse-cls" *ngIf="showCollapse">
    <div class="box-info typo-body-11" *ngFor="let tag of tags;">
      <img src="./assets/icons/table_sum.svg" alt="table_sum" />
      {{tag}}
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); openAssetInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls"
    >
    </sha-grid>

    <app-paginator (currentPageChanged)="changeCurrentPage($event)" [currentPage]="currentPage" [totalPage]="totalPage">
    </app-paginator>
  </div>
</div>

<!-- Tiền bảo lãnh -->
<ng-template #guarantee let-guarantee="templateInfo" let-element="element">
  <div>
    <ng-container>
      <div class="guarantee-money-wrapper typo-body-12">
        <!-- <img
          src="./assets/icons/add-circle.svg"
          alt="add-guarantee-money"
          [matTooltip]="'MES-75' | translate"
          matTooltipPosition="above"
          matTooltipClass="custom-tooltip"
          (click)="openPopoverGuaranteeMoney($event ,element)"
          *ngIf="!element.children"
        /> -->
        <!-- <div class="fake-img" *ngIf="element.children"></div> -->
        <span class="guarantee-money typo-body-12">{{customNumberFormat(guarantee)}}</span>
      </div>
    </ng-container>
  </div>
</ng-template>

<!-- Tiền bảo lãnh cần nộp -->
<ng-template #submitGuarantee let-submitGuarantee="templateInfo" let-element="element">
  @if(!!submitGuarantee) {
  <div>
    <ng-container>
      <div class="guarantee-money-wrapper typo-body-12">
        <div class="img-wrapper" *ngIf="!element.children">
          <div
            class="absolute"
            (mouseenter)="openPopoverSubmitGuarantee($event, submitGuarantee ,element.accountNumber)"
            (mouseleave)="closePopover()"
          ></div>
          <img src="./assets/icons/more-circle.svg" alt="more-option-guarantee-money" />
        </div>
        <div class="fake-img" *ngIf="element.children"></div>
        <span class="typo-body-12">{{customNumberFormat(submitGuarantee)}}</span>
      </div>
    </ng-container>
  </div>
  } @else {
  <div class="flex-end typo-body-12">-</div>
  }
</ng-template>

<ng-template #numberTemplate let-numberTemplate="templateInfo" let-element="element" let-column="column">
  @if(column.componentConfig?.isShowDetail && element.children) {
  <div></div>
  } @else if (column.componentConfig?.isShowDetail && !element.children) {
  <div class="typo-body-9 show-detail-label">{{ 'MES-608' | translate }}</div>
  } @else {
  <div class="number-info typo-body-12">{{ numberShowTemplate(numberTemplate)}}</div>
  }
</ng-template>
