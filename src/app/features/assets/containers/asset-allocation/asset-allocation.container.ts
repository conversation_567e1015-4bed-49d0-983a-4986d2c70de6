import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { DestroyService } from 'src/app/core/services';
import { ActionButton, IActionBtn } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { AllocationAdviceComponent } from '../../components/allocation-advice/allocation-advice.component';
import { IAssetDialogData } from '../../constant/assets';
import { selectFilterAssetAllocation$, selectSearchValue$ } from '../../stores/asset.selectors';
import { take, takeUntil } from 'rxjs';
import { Store } from '@ngrx/store';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { deepClone, rangeMatchData } from 'src/app/shared/utils/utils';
import { IFilterAssetAllocationParam } from '../../models/asset';
import { AssetAllocationFilterComponent } from '../../components/asset-allocation-filter/asset-allocation-filter.component';
import { resetFilterAssetAllocation, setFilterAssetAllocation } from '../../stores/asset.actions';
import { IClickOnColumnEvent } from '@shared/models';
import { AssetInfoDetailComponent } from '../../components/assets-info-detail/assets-info-detail.component';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';

/**
 * % tài sản
 */
@Component({
  selector: 'app-asset-allocation',
  templateUrl: './asset-allocation.container.html',
  styleUrl: './asset-allocation.container.scss',
})
export class AssetAllocationContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('proportionInfo', { static: true }) proportionInfo: TemplateRef<any> | null = null;
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  filterOptions!: IFilterAssetAllocationParam;

  totalAssetAllocation = {
    account: 0,
    nav: 0,
    cash: 0,
    stock: 0,
    derivative: 0,
    bond: 0,
    fundCertificates: 0,
  };
  showCollapse: boolean = true;
  assetData: any = null;
  customNumberFormat = customNumberFormat;
  tags: string[] = [];
  brokerButton: IActionBtn = {
    label: '',
    icons: 'icon:people-icon',
    name: 'broker',
    isDisplayed: false,
    tag: ActionButton.broker,
  };

  fakeData: any = [];

  isOpenOtherPopup = false;

  /**
   * Constructor
   * @param _destroy
   * @param store
   */
  constructor(private readonly _destroy: DestroyService, private readonly store: Store) {
    super();
    this.brokerInfo();
    this.actionButtons.unshift(this.brokerButton);
    this.toggleButtonByTags([
      ActionButton.broker,
      ActionButton.loading,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);
    const tags = this.totalAssetAllocation;
    const { account, nav, cash, stock, derivative, bond, fundCertificates } = tags;

    this.tags = [
      `${account} Tài khoản`,
      `NAV: ${customNumberFormat(nav)}`,
      `Tiền mặt : ${customNumberFormat(cash)}`,
      `Cổ phiếu: ${customNumberFormat(Number(stock.toFixed(0)))}`,
      `Phái sinh: ${customNumberFormat(Number(derivative.toFixed(0)))}`,
      `Trái phiếu: ${customNumberFormat(Number(bond.toFixed(0)))}`,
      `Chứng chỉ quỹ: ${customNumberFormat(Number(fundCertificates.toFixed(0)))}`,
    ];
  }
  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.data = deepClone(this.fakeData);
    this.initialData = deepClone(this.data);

    const cellTemplate = this.proportionInfo;

    const baseColumn = {
      minWidth: 30,
      width: 300,
      isDisplay: true,
      resizable: true,
      cellTemplate,
    }
    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 155,
        width: 155,
        tag: 'accountNumber',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 290,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Tài sản ròng (NAV)',
        minWidth: 30,
        width: 156,
        tag: 'nav',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => {
          return customNumberFormat(Math.round(v));
        },
        align: 'end',
      },
      {
        name: 'Tỷ trọng tiền mặt',
        tag: 'proportionOfCash',
        ...baseColumn
      },
      {
        name: 'Tỷ trọng Cổ phiếu',
        tag: 'proportionOfStocks',
        ...baseColumn

      },
      {
        name: 'Tỷ trọng Phái sinh',
        tag: 'derivativeProportion',
        ...baseColumn

      },
      {
        name: 'Tỷ trọng Trái phiếu',
        tag: 'bondProportion',
        ...baseColumn

      },
      {
        name: 'Tỷ trọng Chứng chỉ quỹ',
        tag: 'proportionOfFundCertificates',
        ...baseColumn

      },
    ];

    this.getFilterAssetAllocation()

    this.getSearchValueAssetAllocationStore()
  }

  /**
   * NgAfterViewInit of asset allocation
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  private getFilterAssetAllocation() {
    this.store
      .select(selectFilterAssetAllocation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter?.isFilter;
      });
  }

  private getSearchValueAssetAllocationStore() {
    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchMultipleLevel(value ?? '', ['accountNumber', 'customerName']);
      });
  }

  /**
   * NgOnDestroy of asset allocation
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterAssetAllocation());
  }

  /**
   * searchData
   * @param {string} value searchData
   */
  searchDataAssetAllocation(value: string) {
    let searchDataAssetAllocation = [];
    const dataClone = deepClone(this.data);
    const searchValue = value.toString().toLowerCase();
    if (value) {
      searchDataAssetAllocation = dataClone.filter((item) => {
        return this.containsSearchValueAssetAllocation(item, searchValue);
      });

      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchDataAssetAllocation = deepClone(this.initialData);
      this.isSearch = false; // Set isSearch to false when not searching
    }

    this.data = deepClone(searchDataAssetAllocation);
  }

  /**
   * containsSearchValueAssetAllocation
   * @param item
   * @param searchValue
   */
  containsSearchValueAssetAllocation(item: any, searchValue: string): boolean {
    const accountNumberAssetAllocation = item.accountNumber?.toString().toLowerCase();
    const customerNameAssetAllocation = item.customerName?.toString().toLowerCase();

    return accountNumberAssetAllocation?.includes(searchValue) ?? customerNameAssetAllocation?.includes(searchValue);
  }

  /**
   * Thông tin của broker
   */
  brokerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        this.brokerButton.label = user ? `${user?.brokerCode}: ${user?.brokerName}` : '';
      });
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'broker':
        this.changeViewBroker();
        break;

      case 'filter':
        {
          const ref = this.openFilter(AssetAllocationFilterComponent, {
            width: '800px',
            data: this.filterOptions,
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe({
              next: (v) => {
                if (!v) return;
                this.applyFilter(v);
              },
            });
        }
        break;
    }
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;

    if (type === 'save') {
      const newListFilterAssetAllocation = this.saveFunc(optionFilter);
      this.filteredData = newListFilterAssetAllocation;

      this.data = newListFilterAssetAllocation;
    } else if (type === 'default') {
      this.isFilter = false;
      this.filteredData = [];
      this.resetDataWithSearchValue()
      this.store.dispatch(resetFilterAssetAllocation());
    }
  }

  /**
   * ResetDataWithSearchValue
   */
  private resetDataWithSearchValue() {
    this.isSearch
      ? this.store
        .select(selectSearchValue$)
        .pipe(takeUntil(this._destroy))
        .subscribe((value) => {
          this.searchMultipleLevel(value ?? '', ['accountNumber', 'customerName']);
        })
      : (() => {
        this.data = this.initialData;
      })();
  }

  /**
   * UpdateParamInStore
   * @param optionFilter
   */
  updateParamInStore(optionFilter: any) {
    this.store.dispatch(setFilterAssetAllocation({ params: optionFilter }));
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilter: any) {
    this.updateParamInStore(optionFilter);
    const dataAssetAllocationClone = deepClone(this.initialData);
    const newListFilterAssetAllocation = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(dataAssetAllocationClone, optionFilter);
    return newListFilterAssetAllocation;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {

    const newListFilter = data.filter((item, index) => {
      const {
        customers,
        nav,
        proportionOfCash,
        proportionOfStocks,
        derivativeProportion,
        bondProportion,
        proportionOfFundCertificates,
      } = optionFilter;
      const isCustomerMatch = (customers ?? []).length ? (customers ?? []).includes(item.customerName) : true;

      const isNavMatch = rangeMatchData(item.nav, nav);

      const isProportionOfCashMatch = rangeMatchData(item.proportionOfCash, proportionOfCash);

      const isProportionOfStocksMatch = rangeMatchData(item.proportionOfStocks, proportionOfStocks);

      const isDerivativeProportionMatch = rangeMatchData(item.derivativeProportion, derivativeProportion);

      const isBondProportionMatch = rangeMatchData(item.bondProportion, bondProportion);

      const isProportionOfFundCertificatesMatch = rangeMatchData(
        item.proportionOfFundCertificates,
        proportionOfFundCertificates
      );

      return (
        isCustomerMatch &&
        isNavMatch &&
        isProportionOfCashMatch &&
        isProportionOfStocksMatch &&
        isDerivativeProportionMatch &&
        isBondProportionMatch &&
        isProportionOfFundCertificatesMatch
      );
    });

    return newListFilter;
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * Based-on percent to return class
   * @param percent - proportionInfo.percent
   * @returns {string}- class
   */
  getClassBasedOnPercent(percent: number) {
    if (percent === 0) {
      return 'percent-zero';
    } else if (percent === 100) {
      return 'percent-hundred';
    } else if (percent > 0 && percent < 100) {
      return 'percent-positive';
    }
    return '';
  }

  /**
   * Function Tạo khuyến nghị phân bố
   * @param {any} element - CellData
   * @param {any} proportionInfo - proportionInfo
   */
  createAllocation(element: any, proportionInfo: any) {
    this.isOpenOtherPopup = true;
    const { type, numberProportion, percentProportion } = proportionInfo;
    const {
      parent,
      customerName,
      accountNumber,
      nav,
      proportionOfCash,
      proportionOfStocks,
      derivativeProportion,
      bondProportion,
      proportionOfFundCertificates,
    } = element;

    const isChildItem = parent;

    const assetData: IAssetDialogData = {
      type: type,
      amount: numberProportion,
      percentage: percentProportion,
      asset: {
        customerName: isChildItem ? parent.customerName : customerName,
        accountNumber: accountNumber,
        nav: nav,
        proportionOfCash: proportionOfCash.numberProportion,
        percentProportionOfCash: proportionOfCash.percentProportion,
        proportionOfStocks: proportionOfStocks.numberProportion,
        percentProportionOfStocks: proportionOfStocks.percentProportion,
        derivativeProportion: derivativeProportion.numberProportion,
        percentDerivativeProportion: derivativeProportion.percentProportion,
        bondProportion: bondProportion.numberProportion,
        percentBondProportion: bondProportion.percentProportion,
        proportionOfFundCertificates: proportionOfFundCertificates.numberProportion,
        percentProportionOfFundCertificates: proportionOfFundCertificates.percentProportion,
      },
    };

    const ref = this.dialogService.openRightDialog(AllocationAdviceComponent, {
      width: '560px',
      data: assetData,
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe(() => {
        this.isOpenOtherPopup = false;
      });
  }

  /**
   * OpenAssetInfoDetail
   * @param {IClickOnColumnEvent} event - IClickOnColumnEvent
   */
  openAssetInfoDetail(event: IClickOnColumnEvent) {
    if (this.isOpenOtherPopup) return;
    let { element } = event;
    if (!element.children && !element.children?.length) {
      element = element.parent;
    }
    this.dialogService.openPopUp(AssetInfoDetailComponent, {
      width: 'calc(100vw - 200px)',
      height: 'calc(100vh - 50px)',
      panelClass: 'open-asset-info-cls',
      data: {
        status: 'proportion-asset-detail',
        element,
      },
    });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    // FIX ME WHEN HAVE ROLE
  }
}
