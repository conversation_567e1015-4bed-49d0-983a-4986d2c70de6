<div class="asset-distribution-container">
  <div class="header-asset-distribution">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-94' | translate}}</div>
      <div class="number-info-cls" *ngIf="!showCollapse">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[0]}}
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tags[1]}}
        </div>
        <div class="box-info typo-body-11"><img src="./assets/icons/table_sum.svg" alt="table_sum" /> {{tags[2]}}</div>
        <div class="dropdown-btn" *ngIf="!showCollapse" (click)="toggleButtons()">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div>
      </div>
      <div class="collapse-btn" *ngIf="showCollapse" (click)="toggleButtons()">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter,
        }"
      ></app-action-btn>
    </div>
  </div>

  <!-- <mat-tab-group class="box-info-wrapper" [ngClass]="{'hidden': !showCollapse}">
    @for(tag of tags; track tags) {
    <mat-tab>
      <ng-template mat-tab-label>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tag}}
        </div>
      </ng-template>
    </mat-tab>
    }
  </mat-tab-group> -->

  <div [ngClass]="{'hidden': !showCollapse}">
    <app-slide-tag [tags]="tags" [classParent]="'asset-distribution-container'"></app-slide-tag>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event);openAssetInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
      class="table-custom-cls"
    >
    </sha-grid>
  </div>
</div>

<ng-template #proportionInfo let-proportionInfo="templateInfo" let-element="element">
  @if(proportionInfo) {
  <div>
    <ng-container>
      <div class="proportion-container typo-body-12">
        <img
          class="icon"
          src="./assets/icons/percentage-blue.svg"
          alt="document-logo"
          [ngStyle]="{'opacity': proportionInfo.percentProportion === 0 ? '0.5' : '1'}"
          [matTooltip]="'MES-98' | translate"
          matTooltipPosition="above"
          matTooltipClass="custom-tooltip"
          (click)="createAllocation(element, proportionInfo)"
        />
        <div class="label">
          <span class="percentage typo-body-12" [ngClass]="getClassBasedOnPercent( proportionInfo.percentProportion)"
            >{{ proportionInfo.percentProportion | numberFormat:'percent'}}</span
          >
          <span class="typo-body-12">{{ proportionInfo.numberProportion.toFixed(0)| numberFormat }}</span>
        </div>
      </div>
    </ng-container>
  </div>
  } @else {
  <div>-</div>
  }
</ng-template>
