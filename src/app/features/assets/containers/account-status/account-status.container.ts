import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { DestroyService } from 'src/app/core/services';
import { ActionButton, IActionBtn } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import {
  FEE_DEBT_MAP,
  FEE_DEBT_MAP_CLASS,
  MARGIN_RATIO_VIOLATION_MAP_CLASS,
  OVERDUE_DEBT_MAP,
  OVERDUE_DEBT_MAP_CLASS,
} from '../../constant/assets';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { Store } from '@ngrx/store';
import { take, takeUntil } from 'rxjs';
import {
  selectFilterAccountStatus$,
  selectFilteredDataAccountStatus$,
  selectSearchValue$,
} from '../../stores/asset.selectors';
import { AccountStatusFilterComponent } from '../../components/account-status-filter/account-status-filter.component';
import { resetFilterAccountInfo } from 'src/app/features/customers/stores/customer.action';
import { IFilterAccountStatusParam } from '../../models/asset';
import { resetFilterAccountStatus, setFilterAccountStatus } from '../../stores/asset.actions';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { PopupOverdueComponent } from '../../components/popup-overdue/popup-overdue.component';
import { IClickOnColumnEvent } from '../../../../shared/models/grid.model';
import { OverDueDebtDetailComponent } from '../../components/overdue-debt-detail/overdue-debt-detail.component';
import { PopupFeeDebtComponent } from '../../components/popup-fee-debt/popup-fee-debt.component';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { FeeDebtDetailComponent } from '../../components/fee-debt-detail/fee-debt-detail.component';
import { deepClone } from 'src/app/shared/utils/utils';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { AssetInfoDetailComponent } from '../../components/assets-info-detail/assets-info-detail.component';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';

interface IAccountStatus {
  accountNumber: string;
  customerName: string | null;
  marginRate: number;
  marginRateViolation: {
    type: number;
    percentMarginRateViolation: number | null;
  };
  overDueDebt: number;
  feeDebt: number;
  children?: IAccountStatus[];
}

/**
 * Tình trạng tài khoản
 */
@Component({
  selector: 'app-account-status',
  templateUrl: './account-status.container.html',
  styleUrl: './account-status.container.scss',
})
export class AccountStatusContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  filterOptions!: IFilterAccountStatusParam;
  overdueRef!: PopoverRef<any>;
  feeDebtRef!: PopoverRef<any>;
  isMouseInsidePopup: boolean = false;
  private isMouseOverPopover = false;
  private isPopoverOpen = false;
  overdueDebtMap = OVERDUE_DEBT_MAP;
  fakeData: IAccountStatus[] = [
    // {
    //   accountNumber: '069C-125485',
    //   customerName: 'Phạm Thị Thu Trang',
    //   marginRate: 97.5,
    //   marginRateViolation: {
    //     type: 3,
    //     percentMarginRateViolation: null,
    //   },
    //   overDueDebt: 1,
    //   feeDebt: 1,
    //   children: [
    //     {
    //       accountNumber: '069C-125485-00',
    //       customerName: null,
    //       marginRate: 96.**************,
    //       marginRateViolation: {
    //         type: 0,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 4,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-125485-01',
    //       marginRate: 98.**************,
    //       marginRateViolation: {
    //         type: 1,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 3,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-125485-02',
    //       marginRate: 98.**************,
    //       marginRateViolation: {
    //         type: 2,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 0,
    //       feeDebt: 3,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-125485-03',
    //       marginRate: 96.**************,
    //       marginRateViolation: {
    //         type: 3,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 4,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-586547',
    //   customerName: 'Đặng Hoàng An Nhiên',
    //   marginRate: 98.4375,
    //   marginRateViolation: {
    //     type: 5,
    //     percentMarginRateViolation: 50,
    //   },
    //   overDueDebt: 1,
    //   feeDebt: 0,
    //   children: [
    //     {
    //       accountNumber: '069C-586547-00',
    //       customerName: null,
    //       marginRate: 97.**************,
    //       marginRateViolation: {
    //         type: 4,
    //         percentMarginRateViolation: 25,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 0,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-586547-01',
    //       marginRate: 100,
    //       marginRateViolation: {
    //         type: 5,
    //         percentMarginRateViolation: 50,
    //       },
    //       overDueDebt: 0,
    //       feeDebt: 0,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-586547-02',
    //       marginRate: 99,
    //       marginRateViolation: {
    //         type: 0,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 3,
    //       feeDebt: 0,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-400190',
    //   customerName: 'Ngô Thị Hằng',
    //   marginRate: 98.**************,
    //   marginRateViolation: {
    //     type: 5,
    //     percentMarginRateViolation: 60,
    //   },
    //   overDueDebt: 1,
    //   feeDebt: 1,
    //   children: [
    //     {
    //       accountNumber: '069C-400190-00',
    //       customerName: null,
    //       marginRate: 98.8,
    //       marginRateViolation: {
    //         type: 4,
    //         percentMarginRateViolation: 40,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 3,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-400190-01',
    //       marginRate: 97.**************,
    //       marginRateViolation: {
    //         type: 5,
    //         percentMarginRateViolation: 60,
    //       },
    //       overDueDebt: 0,
    //       feeDebt: 5,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-400190-02',
    //       marginRate: 99.**************,
    //       marginRateViolation: {
    //         type: 2,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 3,
    //       feeDebt: 2,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-918882',
    //   customerName: 'Phạm Tiến Nam Phương',
    //   marginRate: 98,
    //   marginRateViolation: {
    //     type: 4,
    //     percentMarginRateViolation: 15,
    //   },
    //   overDueDebt: 0,
    //   feeDebt: 1,
    //   children: [
    //     {
    //       accountNumber: '069C-918882-00',
    //       customerName: null,
    //       marginRate: 100,
    //       marginRateViolation: {
    //         type: 4,
    //         percentMarginRateViolation: 15,
    //       },
    //       overDueDebt: 0,
    //       feeDebt: 5,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-918882-01',
    //       marginRate: 99,
    //       marginRateViolation: {
    //         type: 1,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 0,
    //       feeDebt: 3,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-918882-80',
    //       marginRate: 95.**************,
    //       marginRateViolation: {
    //         type: 2,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 0,
    //       feeDebt: 5,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-883962',
    //   customerName: 'Bùi Thị Hạnh',
    //   marginRate: 98.**************,
    //   marginRateViolation: {
    //     type: 4,
    //     percentMarginRateViolation: 60,
    //   },
    //   overDueDebt: 1,
    //   feeDebt: 1,
    //   children: [
    //     {
    //       accountNumber: '069C-883962-00',
    //       customerName: null,
    //       marginRate: 98.**************,
    //       marginRateViolation: {
    //         type: 4,
    //         percentMarginRateViolation: 60,
    //       },
    //       overDueDebt: 3,
    //       feeDebt: 2,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-883962-80',
    //       marginRate: 97.*************,
    //       marginRateViolation: {
    //         type: 0,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 4,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-891135',
    //   customerName: 'Trần Văn Hậu',
    //   marginRate: 97.**************,
    //   marginRateViolation: {
    //     type: 5,
    //     percentMarginRateViolation: 45,
    //   },
    //   overDueDebt: 1,
    //   feeDebt: 1,
    //   children: [
    //     {
    //       accountNumber: '069C-891135-00',
    //       customerName: null,
    //       marginRate: 96.**************,
    //       marginRateViolation: {
    //         type: 3,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 4,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-891135-01',
    //       marginRate: 98.**************,
    //       marginRateViolation: {
    //         type: 5,
    //         percentMarginRateViolation: 45,
    //       },
    //       overDueDebt: 3,
    //       feeDebt: 5,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-891135-02',
    //       marginRate: 95.625,
    //       marginRateViolation: {
    //         type: 4,
    //         percentMarginRateViolation: 23,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 3,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-316087',
    //   customerName: 'Công ty TNHH Mica Group',
    //   marginRate: 97.*************,
    //   marginRateViolation: {
    //     type: 5,
    //     percentMarginRateViolation: 49,
    //   },
    //   overDueDebt: 1,
    //   feeDebt: 0,
    //   children: [
    //     {
    //       accountNumber: '069C-316087-00',
    //       customerName: null,
    //       marginRate: 97.1875,
    //       marginRateViolation: {
    //         type: 0,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 0,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-316087-01',
    //       marginRate: 98,
    //       marginRateViolation: {
    //         type: 5,
    //         percentMarginRateViolation: 49,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 0,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-316087-02',
    //       marginRate: 100,
    //       marginRateViolation: {
    //         type: 4,
    //         percentMarginRateViolation: 53,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 0,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-251114',
    //   customerName: 'Công ty cổ phần địa ốc Ngọc Minh Huy',
    //   marginRate: 96.**************,
    //   marginRateViolation: {
    //     type: 2,
    //     percentMarginRateViolation: null,
    //   },
    //   overDueDebt: 1,
    //   feeDebt: 1,
    //   children: [
    //     {
    //       accountNumber: '069C-251114-00',
    //       customerName: null,
    //       marginRate: 95.**************,
    //       marginRateViolation: {
    //         type: 1,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 5,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-251114-80',
    //       marginRate: 97.**************,
    //       marginRateViolation: {
    //         type: 2,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 3,
    //       feeDebt: 4,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-388482',
    //   customerName: 'Công ty cổ phần Money Max',
    //   marginRate: 97.**************,
    //   marginRateViolation: {
    //     type: 4,
    //     percentMarginRateViolation: 89,
    //   },
    //   overDueDebt: 1,
    //   feeDebt: 1,
    //   children: [
    //     {
    //       accountNumber: '069C-388482-00',
    //       customerName: null,
    //       marginRate: 98.125,
    //       marginRateViolation: {
    //         type: 4,
    //         percentMarginRateViolation: 89,
    //       },
    //       overDueDebt: 3,
    //       feeDebt: 4,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-388482-01',
    //       marginRate: 97.**************,
    //       marginRateViolation: {
    //         type: 2,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 5,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-637085',
    //   customerName: 'Công ty TNHH Tigon 68',
    //   marginRate: 99.*************,
    //   marginRateViolation: {
    //     type: 5,
    //     percentMarginRateViolation: 90,
    //   },
    //   overDueDebt: 0,
    //   feeDebt: 1,
    //   children: [
    //     {
    //       accountNumber: '069C-637085-00',
    //       customerName: null,
    //       marginRate: 99.*************,
    //       marginRateViolation: {
    //         type: 5,
    //         percentMarginRateViolation: 90,
    //       },
    //       overDueDebt: 0,
    //       feeDebt: 5,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-637085-01',
    //       marginRate: 99.**************,
    //       marginRateViolation: {
    //         type: 4,
    //         percentMarginRateViolation: 42,
    //       },
    //       overDueDebt: 0,
    //       feeDebt: 2,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-862656',
    //   customerName: 'Công ty TNHH du lịch Cá Voi Xanh',
    //   marginRate: 98,
    //   marginRateViolation: {
    //     type: 2,
    //     percentMarginRateViolation: null,
    //   },
    //   overDueDebt: 1,
    //   feeDebt: 1,
    //   children: [
    //     {
    //       accountNumber: '069C-862656-00',
    //       customerName: null,
    //       marginRate: 100,
    //       marginRateViolation: {
    //         type: 2,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 4,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-862656-01',
    //       marginRate: 96,
    //       marginRateViolation: {
    //         type: 0,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 0,
    //       feeDebt: 5,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-862656-80',
    //       marginRate: 98.**************,
    //       marginRateViolation: {
    //         type: 2,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 3,
    //       feeDebt: 5,
    //     },
    //   ],
    // },
    // {
    //   accountNumber: '069C-252138',
    //   customerName: 'Công ty TNHH xây dựng và đầu tư Phú Khang',
    //   marginRate: 95.**************,
    //   marginRateViolation: {
    //     type: 4,
    //     percentMarginRateViolation: 56,
    //   },
    //   overDueDebt: 1,
    //   feeDebt: 1,
    //   children: [
    //     {
    //       accountNumber: '069C-252138-00',
    //       customerName: null,
    //       marginRate: 97.**************,
    //       marginRateViolation: {
    //         type: 4,
    //         percentMarginRateViolation: 56,
    //       },
    //       overDueDebt: 2,
    //       feeDebt: 4,
    //     },
    //     {
    //       customerName: null,
    //       accountNumber: '069C-252138-80',
    //       marginRate: 93.75,
    //       marginRateViolation: {
    //         type: 0,
    //         percentMarginRateViolation: null,
    //       },
    //       overDueDebt: 3,
    //       feeDebt: 5,
    //     },
    //   ],
    // },
  ];

  brokerButton: IActionBtn = {
    label: '',
    icons: 'icon:people-icon',
    name: 'broker',
    isDisplayed: false,
    tag: ActionButton.broker,
  };

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   * @param popoverService
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly popoverService: PopoverService
  ) {
    super();
    this.brokerInfo();
    this.actionButtons.unshift(this.brokerButton);
    this.toggleButtonByTags([
      ActionButton.broker,
      ActionButton.loading,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    this.data = deepClone(this.fakeData);
    this.initialData = deepClone(this.data);
    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 155,
        width: 155,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 290,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Tỷ lệ ký quỹ(CMR)',
        minWidth: 30,
        width: 230,
        tag: 'marginRate',
        isDisplay: true,
        displayValueFn: (v: number) => {
          return customNumberFormat(v, 'percent');
        },
        dynamicClass: (value) => {
          if (value < 100) {
            return 'margin-rate-cls';
          } else return 'margin-rate-100-cls';
        },
        align: 'center',
        resizable: true,
      },
      {
        name: 'Vi phạm tỷ lệ ký quỹ(CMR)',
        minWidth: 30,
        width: 230,
        tag: 'marginRateViolation',
        isDisplay: true,
        displayValueFn: (v) => {
          switch (true) {
            case v.type === 1:
              return 'Warning 1';
            case v.type === 2:
              return 'Warning 2';
            case v.type === 3:
              return 'Warning 3';
            case v.type === 4:
              return `Tỷ lệ bổ sung: ${v.percentMarginRateViolation}%`;
            case v.type === 5:
              return `Tỷ lệ giải chấp: ${v.percentMarginRateViolation}%`;
            default:
              return 'Không';
          }
        },
        dynamicClass: (v) => {
          return MARGIN_RATIO_VIOLATION_MAP_CLASS[v.type];
        },
        align: 'center',
        resizable: true,
      },

      {
        name: 'Nợ quá hạn',
        minWidth: 30,
        width: 230,
        tag: 'overDueDebt',
        isDisplay: true,
        displayValueFn: (v) => {
          return OVERDUE_DEBT_MAP[v];
        },
        dynamicClass: (v) => {
          return OVERDUE_DEBT_MAP_CLASS[v];
        },
        align: 'center',
        resizable: true,
      },
      {
        name: 'Nợ phí',
        minWidth: 30,
        width: 230,
        tag: 'feeDebt',
        isDisplay: true,
        displayValueFn: (v) => {
          return FEE_DEBT_MAP[v];
        },
        dynamicClass: (v) => {
          return FEE_DEBT_MAP_CLASS[v];
        },
        align: 'center',
        resizable: true,
      },
    ];

    this.store
      .select(selectFilterAccountStatus$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter?.isFilter;
      });

    this.store
      .select(selectFilteredDataAccountStatus$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filteredData) => {
        this.data = filteredData.length ? deepClone(filteredData) : deepClone(this.initialData);
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchMultipleLevel(value ?? '', ['accountNumber', 'customerName']);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * ngOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterAccountStatus());
  }

  /**
   * searchData
   * @param {string} value searchValue
   */
  searchData(value: string) {
    const searchValue = value.toString().toLowerCase();

    if (value) {
      this.data = this.initialData.filter((item) => {
        return (
          item.accountNumber.toLowerCase().includes(searchValue) ??
          item.customerName.toLowerCase().includes(searchValue)
        );
      });
      this.isSearch = true; // Set isSearch to true when searching
    } else {
      this.data = this.initialData;

      this.isSearch = false; // Set isSearch to false when not searching
    }
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    const accountNumber = item.accountNumber?.toString().toLowerCase();
    const customerName = item.customerName?.toString().toLowerCase();
    return accountNumber?.includes(searchValue) ?? customerName?.includes(searchValue);
  }

  /**
   * Thông tin của broker
   */
  brokerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        this.brokerButton.label = user ? `${user?.brokerCode}: ${user?.brokerName}` : ''; // fix me later
      });
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'broker':
        this.changeViewBroker();
        break;

      case 'filter':
        {
          const ref = this.openFilter(AccountStatusFilterComponent, {
            width: '800px',
            data: this.filterOptions,
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe({
              next: (v) => {
                if (!v) return;
                console.log(v);
                this.applyFilter(v);
              },
            });
        }
        break;
    }
  }

  /**
   * Hover column
   * @param data
   */
  onHoverColumn(data: any) {
    const { event, element, tag } = data;
    if (tag === 'overDueDebt' && element.children && !this.isPopoverOpen) {
      const ref = this.popoverService.open<any>({
        origin: event.target as HTMLElement,
        content: PopupOverdueComponent,
        position: 1,
        width: 500,
        id: 'overDueDebt',
        hasBackdrop: false,
        componentConfig: {
          text: element.customerName,
        },
        // height: 600,
      });
      this.isPopoverOpen = true;
      setTimeout(() => {
        this.overdueRef = ref;
        this.overdueRef.overlay.hostElement.addEventListener('mouseenter', this.onMouseEnterPopover.bind(this));
        this.overdueRef.overlay.hostElement.addEventListener('mouseleave', this.onMouseLeavePopover.bind(this));
      }, 0);
    } else if (tag === 'feeDebt' && element.children) {
      const ref = this.popoverService.open<any>({
        origin: event.target as HTMLElement,
        content: PopupFeeDebtComponent,
        position: 1,
        hasBackdrop: false,
      });
      this.isPopoverOpen = true;
      setTimeout(() => {
        this.feeDebtRef = ref;
        this.feeDebtRef.overlay.hostElement.addEventListener('mouseenter', this.onMouseEnterPopover.bind(this));
        this.feeDebtRef.overlay.hostElement.addEventListener('mouseleave', this.onMouseLeavePopover.bind(this));
      }, 0);
    }
  }

  /**
   * Handle mouse enter event on the popover
   */
  onMouseEnterPopover(): void {
    this.isMouseOverPopover = true;
  }

  /**
   * Handle mouse leave event on the popover
   */
  onMouseLeavePopover(): void {
    this.isMouseOverPopover = false;
    this.closePopover();
  }

  /**
   * Close Popover
   * Fix me
   */
  closePopover(): void {
    if (this.overdueRef && !this.isMouseOverPopover) {
      setTimeout(() => {
        if (!this.isMouseOverPopover) {
          this.popoverService.close(this.overdueRef);
          this.isPopoverOpen = false;
        }
      }, 0);
    }

    if (this.feeDebtRef && !this.isMouseOverPopover) {
      setTimeout(() => {
        if (!this.isMouseOverPopover) {
          this.popoverService.close(this.feeDebtRef);
          this.isPopoverOpen = false;
        }
      }, 0);
    }
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;
    if (type === 'save') {
      this.store.dispatch(setFilterAccountStatus({ params: optionFilter }));
      const newListFilter = this.saveFunc(optionFilter);
      this.filteredData = newListFilter;
      this.data = newListFilter;
    } else if (type === 'default') {
      this.isFilter = false;
      this.filteredData = [];

      this.isSearch
        ? this.store
            .select(selectSearchValue$)
            .pipe(takeUntil(this._destroy))
            .subscribe((value) => {
              this.searchMultipleLevel(value ?? '', ['accountNumber', 'customerName']);
            })
        : (() => {
            this.data = this.initialData;
          })();
      this.store.dispatch(resetFilterAccountInfo());
    }
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilter: any) {
    const dataClone = deepClone(this.initialData);
    const newListFilter = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(dataClone, optionFilter);
    return newListFilter;
  }

  private rangeMatch(value: number, range: any) {
    if (range?.start && range?.end) {
      return value >= +range.start && value <= +range.end;
    } else if (range?.start) {
      return value >= +range.start;
    } else if (range?.end) {
      return value <= +range.end;
    } else return true;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    let newListFilter: any[] = [];
    const { customers, marginRate, marginRateViolationPercent, marginRateViolation, overDueDebt, feeDebt } =
      optionFilter;

    const filteredData = data.filter((item, index) => {
      const isCustomerMatch = (customers ?? []).length ? (customers ?? []).includes(item.customerName) : true;

      const isMarginRateMatch = this.rangeMatch(item.marginRate, marginRate);

      const isMarginRateViolationPercentMatch = this.rangeMatch(
        item.marginRateViolation.percentMarginRateViolation,
        marginRateViolationPercent
      );

      return isCustomerMatch && isMarginRateMatch && isMarginRateViolationPercentMatch;
    });

    filteredData.forEach((item) => {
      const isMatchParent =
        ((feeDebt ?? []).length ? (feeDebt ?? []).includes(item.feeDebt) : true) &&
        ((overDueDebt ?? []).length ? (overDueDebt ?? []).includes(item.overDueDebt) : true) &&
        ((marginRateViolation ?? []).length
          ? (marginRateViolation ?? []).includes(item.marginRateViolation.type)
          : true);

      const childrenMatch = this.filterChildNodes(item, feeDebt, overDueDebt, marginRateViolation);

      if (isMatchParent && childrenMatch.length === 0) {
        newListFilter.push({ ...item });
      } else if (!isMatchParent && childrenMatch.length === 0) {
        return;
      } else {
        newListFilter.push({ ...item, children: childrenMatch, isExpanded: true });
      }
    });

    newListFilter.forEach((item) => this.traverseNodeAddExpanded(item));
    for (let i = 0; i < newListFilter.length; i++) {
      const value = newListFilter[i];
      if (value.isExpanded) {
        const childrenCount = value.children ? value.children.length : 0;
        this.flattenMultipleLevelChildrenItems(newListFilter, i + 1, 0, value.children);
        i += childrenCount;
      }
    }

    return newListFilter;
  }

  private filterChildNodes(item: any, feeDebt: any[], overDueDebt: any[], marginRateViolation: any[]): any[] {
    const childrenMatch = item.children?.filter((i: any) => {
      const isIncludeFeeDebt = feeDebt.some((f: number) => (i.feeDebt as number) === f);
      const isIncludeOverDueDebt = overDueDebt.some((o: number) => (i.overDueDebt as number) === o);
      const isIncludeMarginRate = marginRateViolation.some((m: number) => (i.marginRateViolation.type as number) === m);
      return isIncludeFeeDebt && isIncludeOverDueDebt && isIncludeMarginRate;
    });
    return childrenMatch;
  }

  /**
   * @param event
   */
  handleClickOnColumn(event: IClickOnColumnEvent): void {
    if (this.overdueRef || this.feeDebtRef) this.closePopover();
    if (event.tag === 'overDueDebt' && event.element.children) {
      this.dialogService.open(OverDueDebtDetailComponent, {
        width: '640px',
      });
    } else if (event.tag === 'feeDebt' && event.element.children) {
      this.dialogService.open(FeeDebtDetailComponent, {
        width: '450px',
      });
    }
  }

  /**
   * OpenAssetInfoDetail
   * @param event IClickOnColumnEvent
   */
  openAssetInfoDetail(event: IClickOnColumnEvent) {
    if (event.tag === 'overDueDebt' || event.tag === 'feeDebt') return;
    let { element } = event;
    if (!element.children && !element.children?.length) {
      element = element.parent;
    }
    this.dialogService.openPopUp(AssetInfoDetailComponent, {
      width: 'calc(100vw - 200px)',
      height: 'calc(100vh - 50px)',
      panelClass: 'open-asset-info-cls',
      data: {
        status: 'status-account-detail',
        element,
      },
    });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    // FIX WHEN HAVE ROLE
  }
}
