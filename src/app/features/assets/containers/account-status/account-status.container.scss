.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.account-status-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-account-status {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;

      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--broker {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          mat-icon[data-mat-icon-name='people-icon'] {
            #Group,
            #Group_2,
            #Group_3 {
              ::ng-deep {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.disable-btn-cls {
          ::ng-deep {
            .box-btn {
              opacity: 0.5;
            }
          }
        }
      }
    }
  }

  .tab-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
    height: 56px;
    overflow: hidden;
    transition: transform 0.5s ease;
    white-space: nowrap;
    text-wrap: nowrap;
    padding: 12px 0px;
    margin: 0 12px;
    position: relative;

    .tab {
      width: 100%;
      display: inline-block;
      vertical-align: top;

      .box-info {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 16px;
        background-color: #f8fafd;
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
        color: #808080;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        white-space: nowrap;
        text-wrap: nowrap;
        width: fit-content;
      }
    }
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        td {
          cursor: pointer;
        }
        .margin-rate-cls {
          max-width: 180px;
          margin: 0 auto;

          span {
            background-color: var(--color--accents--yellow-dark);
            border-radius: 16px;
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            max-width: 184px;

            input {
              text-align: center;
            }
          }
        }

        .margin-rate-100-cls {
          max-width: 180px;
          margin: 0 auto;

          span {
            background-color: var(--color--accents--green);
            border-radius: 16px;
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            max-width: 184px;

            input {
              text-align: center;
            }
          }
        }

        &.none {
          max-width: 180px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--neutral--100);
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            max-width: 184px;

            input {
              text-align: center;
            }
          }
        }

        &.warning-1 {
          max-width: 180px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--warning--500);
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            max-width: 184px;

            input {
              text-align: center;
            }
          }
        }

        &.warning-2 {
          max-width: 180px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--warning--600);
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            max-width: 184px;

            input {
              text-align: center;
            }
          }
        }

        &.warning-3 {
          max-width: 180px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--warning--700);
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            max-width: 184px;

            input {
              text-align: center;
            }
          }
        }

        &.additional-rate,
        &.debt {
          max-width: 180px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--danger--500);
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            max-width: 184px;

            input {
              text-align: center;
            }
          }
        }

        &.mortgage-settlement-rate {
          max-width: 180px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--danger--600);
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            max-width: 184px;

            input {
              text-align: center;
            }
          }
        }
      }
    }
  }
}
