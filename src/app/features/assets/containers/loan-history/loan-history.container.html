<div class="loan-history-container">
  <div class="header-loan-history">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-99' | translate}}</div>
      <!-- <div class="number-info-cls">
        <div class="box-info typo-body-11" *ngFor="let tag of tags">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tag}}
        </div>
      </div> -->
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [listColumnInitial]="columnConfigsInitial"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
            'filter-mode-cls' : isFilter,
          }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (resizeColumn)="resizeColumn($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
      (showMoreEvent)="showMoreData($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls"
    >
    </sha-grid>

    <!-- <app-paginator
        (currentPageChanged)="changeCurrentPage($event)"
        [currentPage]="currentPage"
        [totalPage]="totalPage"
        [pageSize]="pageSize"
      >
      </app-paginator> -->
  </div>
</div>
 