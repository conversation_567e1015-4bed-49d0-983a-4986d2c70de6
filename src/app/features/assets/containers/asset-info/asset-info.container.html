<div class="asset-info-container">
  <div class="header-asset-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-43' | translate}}</div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [listColumnInitial]="columnConfigsInitial"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter,
        }"
      ></app-action-btn>
    </div>
  </div>

  <ng-container *ngIf="isFilter">
    <app-slide-tag [tags]="tags" [classParent]="'asset-info-container'"></app-slide-tag>
  </ng-container>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event);openAssetInfoDetail($event) "
      (resizeColumn)="resizeColumn($event)"
      (removeItemsAt)="removeItemsAt($event)"
      (addItemsAt)="addItemsAt($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls"
    >
    </sha-grid>

    <app-paginator (currentPageChanged)="changeCurrentPage($event)" [currentPage]="currentPage" [totalPage]="totalPage">
    </app-paginator>
  </div>
</div>

<ng-template #templateInfo let-assetInfo="templateInfo">
  @if(assetInfo) {
  <div>
    <ng-container *ngIf="assetInfo.numberMargin > 0">
      <div class="asset-info-increase typo-body-12">
        <img src="./assets/icons/up.svg" alt="document-logo" />
        <span class="typo-body-12"
          >+{{ assetInfo.numberMargin | numberFormat}} +{{ assetInfo.percentMargin.toFixed(2)}}%</span
        >
      </div>
    </ng-container>
    <ng-container *ngIf="assetInfo.numberMargin < 0 ">
      <div class="asset-info-reduce typo-body-12">
        <img src="./assets/icons/down.svg" alt="document-logo" />
        <span *ngIf="assetInfo.percentMargin !== 0 " class="typo-body-12"
          >{{ assetInfo.numberMargin | numberFormat }} {{ assetInfo.percentMargin.toFixed(2) }}%</span
        >
        <span *ngIf="assetInfo.percentMargin === 0 " class="typo-body-12"
          >{{ assetInfo.numberMargin | numberFormat }} - {{ assetInfo.percentMargin.toFixed(2) }}%</span
        >
      </div>
    </ng-container>
    <ng-container *ngIf="assetInfo.numberMargin === 0">
      <div class="asset-info-stable typo-body-12">
        <img src="./assets/icons/minus.svg" alt="document-logo" />
        <span class="typo-body-12"
          >{{ assetInfo.numberMargin | numberFormat }} - {{ assetInfo.percentMargin.toFixed(2) }}%</span
        >
      </div>
    </ng-container>
  </div>
  } @else {
  <div>-</div>
  }
</ng-template>

<ng-template #numberTemplate let-numberTemplate="templateInfo" let-element="element" let-rowIdx="rowIdx" let-tag="tag">
  @if(numberTemplate === 0) {
  <div class="number-info typo-body-12">-</div>
  } @else {
  <div class="number-info typo-body-12">{{customNumberFormat(numberTemplate)}}</div>
  }
</ng-template>
