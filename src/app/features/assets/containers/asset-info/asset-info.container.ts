import { Component, ElementRef, OnD<PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { DestroyService, I18nService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { AssetInfoFilterComponent } from '../../components/asset-info-filter/asset-info-filter.component';
import { Store } from '@ngrx/store';
import { combineLatest, distinctUntilChanged, take, takeUntil } from 'rxjs';
import {
  pageIndexAssetInfo$,
  selectAssetInfoList$,
  selectAssetSumInfo$,
  selectFilterAssetInfo$,
  selectFilteredDataAssetInfo$,
  selectSearchValue$,
} from '../../stores/asset.selectors';
import {
  clearIntervalAssetInfo,
  getAssetInfoWithFilter,
  getDataAssetInfo,
  getDataAssetInfoSuccess,
  resetFilterAssetInfo,
  resetSearch,
  setFilterAssetInfo,
  updatePageIndexAssetInfo,
} from '../../stores/asset.actions';
import { IAssetInfoResponse, IAssetSumInfo, IFilterAssetInfoParam } from '../../models/asset';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { deepClone, rangeMatchData, searchByValue, updateBorkerName } from 'src/app/shared/utils/utils';
import { AssetInfoDetailComponent } from '../../components/assets-info-detail/assets-info-detail.component';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import {
  IAllAccountNumber,
  IAllLevelOfBroker,
  ICustomerListInRecommendationDialog,
} from 'src/app/shared/models/global';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { CompareValueService } from 'src/app/shared/directives/compare-value/compare-value.service';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { IListOptions } from '../../constant/assets';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { getCurrentBrokerView, getListAccountNumberAndLevelByBrokerView } from 'src/app/stores/shared/shared.actions';
import { Router } from '@angular/router';
import { OptionsAssetComponent } from '../../components/options-asset/options-asset.component';

interface IActionAssetInfo {
  accountNumbers: string[];
  brokerCode: string[];
}

/**
 * Thông tin tài sản
 */
@Component({
  selector: 'app-asset-info',
  templateUrl: './asset-info.container.html',
  styleUrl: './asset-info.container.scss',
  providers: [DestroyService],
})
export class AssetInfoContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('templateInfo', { static: true }) assetInfo: TemplateRef<any> | null = null;

  @ViewChild('numberTemplate', { static: true }) numberTemplate: TemplateRef<any> | null = null;

  customerList: ICustomerListInRecommendationDialog[] = [];

  customNumberFormat = customNumberFormat;

  showCollapse: boolean = true;

  filterOptions!: IFilterAssetInfoParam | null;

  pageSize = 10;

  totalPage: number = 0;

  currentPage = 1;

  initialCustomer!: IAllAccountNumber[];

  tags!: string[];

  LIST_MG: IListOptions[] = [];

  brokerCode = '';

  currentBrokerCode = '';

  isLoadingPage = false;

  isChangeCurrentBroker = false;

  actionAssetInfo: IActionAssetInfo | null = null;

  storeTotalPage = 1;

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly localStorageService: LocalStorageService,
    private readonly compareValueService: CompareValueService,
    private readonly popoverService: PopoverService,
    private readonly router: Router,
    private readonly i18n: I18nService
  ) {
    super();
    this.brokerInfoAssetInfo();
    this.toggleButtonByTags([ActionButton.broker, ActionButton.loading, ActionButton.display, ActionButton.filter]);
  }

  /**
   * The ngOnInit
   */
  ngOnInit(): void {
    const cellTemplate = this.assetInfo;
    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 165,
        width: 165,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 200,
        tag: 'customerName',
        isDisplay: true,
        sticky: false,
        resizable: true,
        displayValueFn: (v: string) => {
          if (!v) return '';
          return v;
        },
      },
      // {
      //   name: 'Sức mua',
      //   minWidth: 30,
      //   width: 150,
      //   tag: 'purchasingPower',
      //   isDisplay: true,
      //   resizable: true,
      //   align: 'end',
      //   cellTemplate: this.numberTemplate,
      //   isCompare: true,
      // },
      {
        name: 'NAV đầu phiên',
        minWidth: 30,
        width: 150,
        tag: 'firstNav',
        isDisplay: true,
        resizable: true,
        align: 'end',
        cellTemplate: this.numberTemplate,
        isCompare: true,
      },
      {
        name: 'Tài sản ròng (NAV)',
        minWidth: 30,
        width: 160,
        tag: 'nav',
        align: 'end',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.numberTemplate,
        isCompare: true,
      },
      {
        name: '+/- NAV',
        minWidth: 30,
        width: 250,
        tag: 'marginNav',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
      {
        name: 'Tổng giá trị CK',
        minWidth: 30,
        width: 150,
        tag: 'stock',
        isDisplay: true,
        align: 'end',
        resizable: true,
        cellTemplate: this.numberTemplate,
        isCompare: true,
      },
      {
        name: '+/- Tổng giá trị CK',
        minWidth: 30,
        width: 240,
        tag: 'marginStock',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
      {
        name: 'Tổng tiền',
        minWidth: 30,
        width: 150,
        tag: 'cash',
        resizable: true,
        isDisplay: true,
        align: 'end',
        cellTemplate: this.numberTemplate,
        isCompare: true,
      },
      {
        name: 'Tổng dư nợ',
        minWidth: 30,
        width: 150,
        tag: 'debt',
        isDisplay: true,
        resizable: true,
        align: 'end',
        cellTemplate: this.numberTemplate,
        isCompare: true,
      },
      {
        name: 'Tỉ lệ ký quỹ',
        minWidth: 30,
        width: 120,
        tag: 'marginRate',
        displayValueFn: (v) => {
          return v === null ? '' : customNumberFormat(v, 'percent');
        },
        dynamicClass: (value) => {
          if (value === null) return '';
          if (value < 100) {
            return 'margin-rate-cls';
          } else return 'margin-rate-100-cls';
        },
        align: 'center',
        isDisplay: true,
      },
      {
        name: 'Tổng tài sản',
        minWidth: 30,
        width: 160,
        tag: 'assets',
        isDisplay: true,
        resizable: true,
        align: 'end',
        cellTemplate: this.numberTemplate,
        isCompare: true,
      },
    ];

    this.store
      .select(selectFilterAssetInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        if (filter) {
          this.isFilter = filter.isFilter ?? false;
        } else {
          this.isFilter = false;
        }
        this.filterOptions = { ...filter } as IFilterAssetInfoParam;
      });

    this.store
      .select(selectAssetSumInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((res) => {
        if (!res) return;
        const { sumAccountNumber, sumNav, sumStock, sumCash, sumDebt, sumAssets } = res;
        this.tags = [
          `${sumAccountNumber} ${this.i18n.translate('MES-15')}`,
          `${this.i18n.translate('MES-694')}: ${customNumberFormat(sumNav)}`,
          `${this.i18n.translate('MES-695')}: ${customNumberFormat(sumStock)}`,
          `${this.i18n.translate('MES-696')}: ${customNumberFormat(sumCash)}`,
          `${this.i18n.translate('MES-55')}: ${customNumberFormat(sumDebt)}`,
          `${this.i18n.translate('MES-296')}: ${customNumberFormat(sumAssets)}`,
        ];
      });

    // GET LIST DATA
    combineLatest([
      this.store.select(selectAllAccountNumberListByBrokerView$),
      this.store.select(selectAllBrokerLevelListByBrokerView$),
      // this.store.select(selectFilterAssetInfo$),
      this.store.select(pageIndexAssetInfo$).pipe(distinctUntilChanged(() => (this.isLoadingPage = false))),
      this.store.select(selectSearchValue$),
    ])
      .pipe(distinctUntilChanged(), takeUntil(this._destroy))
      .subscribe(([customerAccount, brokers, pageIndex, search]) => {
        this.initialCustomer = customerAccount ?? [];

        let customerBase: string[] = [];
        let customerSearch: string[] = [];

        customerBase = this.initialCustomer.map((t) => t.accountNumber);

        if (search) {
          this.isFilter = false;
          customerSearch = searchByValue(this.initialCustomer, search).map((t: IAllAccountNumber) => t.accountNumber);
          this.filterOptions = null;

          if (!customerSearch.length) {
            this.totalPage = 1;
            this.currentPage = 1;
            this.data = [];
            return;
          }
        }

        const customersPayload = search ? customerSearch : customerBase;
        this.storeTotalPage = Math.ceil(customersPayload.length / this.pageSize);
        this.transformData(customersPayload, brokers, pageIndex, search);

        this.currentPage = +pageIndex;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe(() => {
        if (this.grid) this.map.resetExpandedRowAmountMap();
      });

    // GET LIST FILTERED DATA
    combineLatest([this.store.select(selectFilteredDataAssetInfo$), this.store.select(pageIndexAssetInfo$)])
      .pipe(takeUntil(this._destroy))
      .subscribe(([data, pageIndex]) => {
        if (this.isFilter) {
          this.filteredData = data.map((d) => {
            const customerMatch = this.initialCustomer.find((cus) => cus.accountNumber === d.accountNumber);
            return {
              ...d,
              customerName: customerMatch ? customerMatch.customerName : d.customerName,
              children: d.children.map((child: any) => ({
                ...child,
                accountNumber: child.accountNumber + ' - ' + child.subAccountNumber,
                parent: {
                  ...d,
                  customerName: customerMatch ? customerMatch.customerName : d.customerName,
                },
              })),
              marginRate: d.children.length ? null : d.marginRate,
            };
          });
          this.totalPage = Math.ceil(data.length ? data.length / this.pageSize : 1);
          this.data = this.filteredData.slice(this.pageSize * (+pageIndex - 1), this.pageSize * +pageIndex);
        }
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    if (this.isFilter) {
      this.store.dispatch(resetFilterAssetInfo());
    }

    if (this.isSearch) {
      this.store.dispatch(resetSearch());
    }

    this.store.dispatch(clearIntervalAssetInfo());

    this.localStorageService.removeData('currentPage');
    this.compareValueService.clearAll();
  }

  /**
   * Lấy acc và sub acc để lấy call api get list
   */
  transformData(customerId: string[], brokers: IAllLevelOfBroker[], pageIndex: number | string, searchValue?: string) {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(take(1))
      .subscribe((currentBroker) => {
        const brokerId = this.route.snapshot.queryParams['brokerId'];
        if (!currentBroker) return;

        const indexBroker = brokers.findIndex((broker) => broker.brokerCode === brokerId);
        if (indexBroker === -1) return;
        const brokerCodeIds: string[] = [brokerId, ...this.getBrokerByParentBrokerId(brokers[indexBroker]?.children)];

        const listCustomerId = customerId.slice(this.pageSize * (+pageIndex - 1), this.pageSize * +pageIndex);
        const assetCustomerInfo = {
          brokerCode: brokerCodeIds,
          accountNumbers: listCustomerId,
        };
        if (!listCustomerId.length) {
          this.store.dispatch(getDataAssetInfoSuccess({ data: [] }));
          return;
        }

        this.actionAssetInfo = {
          brokerCode: brokerCodeIds,
          accountNumbers: customerId.slice(0, this.pageSize),
        };
        this.brokerCode = brokerId ?? currentBroker.brokerCode;

        this.store.dispatch(getDataAssetInfo({ assetCustomerInfo }));
      });

    this.store
      .select(selectAssetInfoList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((assetInfoList) => {
        if (!assetInfoList.length) {
          this.data = [];
          this.initialData = [];
          this.totalPage = 1;
          this.currentPage = 1;
          return;
        }
        this.totalPage = this.storeTotalPage;
        this.data = assetInfoList.reduce((initial: any[], d, index) => {
          const customerMatch = this.initialCustomer.find((cus) => cus.accountNumber === d.accountNumber);

          const objectAssetInfo = {
            ...d,
            customerName: customerMatch ? customerMatch.customerName : d.customerName,
            isExpanded:
              this.map.getHasExpandedAmountByRowId(d.id ?? '') && (this.map.getExpandedAmountByRowId(d.id ?? '') ?? 0),
            children: d.children.map((child: any) => ({
              ...child,
              parent: {
                ...d,
                customerName: customerMatch ? customerMatch.customerName : d.customerName,
              },
              accountNumber: `${child.accountNumber} - ${child.subAccountNumber ?? child.subAccount}`,
            })),
            marginRate: d.children.length ? null : d.marginRate,
          };

          if (objectAssetInfo.isExpanded) {
            const items = d.children.map((t: any) => ({
              ...t,
              accountNumber: `${t.accountNumber} - ${t.subAccountNumber ?? t.subAccount}`,
              parent: objectAssetInfo,
              backgroundColor: '#f6f6f6',
            }));
            initial.push(objectAssetInfo, ...items);
          } else {
            initial.push(objectAssetInfo);
          }

          return initial;
        }, []);

        this.updateDataAssetInfo();
      });
  }

  private updateDataAssetInfo() {
    this.initialData = deepClone(this.data);

    // dropdown first element
    if (!this.data[0].isExpanded) {
      if (this.isLoadingPage) return;
      this.data[0].isExpanded = true;
      this.addItemsAt({ index: 0, items: this.data[0].children });
    }
  }

  getBrokerByParentBrokerId = (brokerObject: IAllLevelOfBroker[]) => {
    let brokerCodes: string[] = [];
    brokerObject?.forEach((broker) => {
      brokerCodes.push(broker.brokerCode);

      if (Array.isArray(broker.children) && broker.children.length > 0) {
        brokerCodes = brokerCodes.concat(this.getBrokerByParentBrokerId(broker.children));
      }
    });
    return brokerCodes;
  };

  /**
   * changeCurrentPage
   * @param data
   */
  changeCurrentPage(currentPage: number) {
    this.currentPage = currentPage;
    if (this.isFilter) {
      this.updateDataItem();
    } else this.store.dispatch(updatePageIndexAssetInfo({ pageIndex: currentPage }));
  }

  updateDataItem() {
    const ITEM_SIZE = 10;
    const start = (this.currentPage - 1) * ITEM_SIZE;
    const end = Math.min(start + ITEM_SIZE, this.filteredData.length);
    const data = this.filteredData.slice(start, end);
    this.data = [...data];
  }

  /**
   * calculateSum
   * @param {IAssetInfoResponse[]} data
   * @param {string} field
   * @returns {number}
   */
  calculateSum(data: IAssetInfoResponse[], field: keyof IAssetInfoResponse): number {
    return data.reduce((sum, item) => {
      if (!item.children) return sum;
      const value = item[field];
      return sum + (typeof value === 'number' ? value : 0);
    }, 0);
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    const accountNumberAssetInfo = item.accountNumber?.toString().toLowerCase();
    const customerNameAssetInfo = item.customerName?.toString().toLowerCase();

    return accountNumberAssetInfo?.includes(searchValue) ?? customerNameAssetInfo?.includes(searchValue);
  }

  /**
   * Thông tin của broker - thông tin tài sản
   */
  brokerInfoAssetInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBroker) => {
        if (!currentBroker) return;

        this.currentBrokerCode = currentBroker.brokerCode;
        combineLatest([
          this.store.select(selectInfoUserLogin$),
          this.store.select(selectAllBrokerLevelListByBrokerView$),
        ])
          .pipe(takeUntil(this._destroy))
          .subscribe(([userList, brokers]) => {
            const queryParamsAssetInfo = this.route.snapshot.queryParams;

            if (!userList) return;

            const brokerConvertAssetInfo = updateBorkerName([...brokers], [...userList]);

            const subBrokerAssetInfo = brokerConvertAssetInfo.map((broker) => {
              return {
                brokerCode: `${broker.brokerCode}`,
                name: `${broker.brokerName}`,
                isSelect: queryParamsAssetInfo['brokerId']
                  ? broker.brokerCode === queryParamsAssetInfo['brokerId']
                  : broker.brokerCode === currentBroker.brokerCode,
              };
            });

            this.LIST_MG = [...subBrokerAssetInfo];
            const broker = this.LIST_MG.find((t) => t.isSelect);

            if (broker) {
              this.findButtonByTags(ActionButton.broker).label = broker['name'] ?? '';
            }
          });
      });
  }

  /**
   *  Open filter component
   * @param tag string
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'filter':
        {
          const ref = this.openFilter(AssetInfoFilterComponent, {
            width: '800px',
            data: this.filterOptions,
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe({
              next: (v) => {
                if (!v) return;
                this.applyFilter(v);
              },
            });
        }
        break;

      case 'broker':
        this.changeViewBrokerAssetInfo();
        break;

      case 'display':
        this.compareValueService.clearAll();
        break;

      case 'loading':
        if (this.isFilter) return;
        this.loadPage();
        break;
    }
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;
    if (type === 'save') {
      if (!this.isFilter && !optionFilter?.isFilter) return;
      this.currentPage = 1;
      if (optionFilter.isFilter) {
        this.store.dispatch(setFilterAssetInfo({ params: optionFilter }));
      } else this.store.dispatch(resetFilterAssetInfo());
    } else if (type === 'default') {
      this.filteredData = [];
      if (!this.isFilter) {
        return;
      }
      this.currentPage = 1;
      this.store.dispatch(resetFilterAssetInfo());
    }
    this.map.resetExpandedRowAmountMap();

    if (this.actionAssetInfo) {
      if (optionFilter?.isFilter) {
        this.store.dispatch(getAssetInfoWithFilter({ assetCustomerInfo: this.actionAssetInfo }));
      } else this.store.dispatch(getDataAssetInfo({ assetCustomerInfo: this.actionAssetInfo }));
      this.totalPage = Math.ceil(this.initialCustomer.length / this.pageSize);
    }
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilter: any) {
    const dataCloneAssetInfo = deepClone(this.initialData);
    const newListFilterAssetInfo = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(dataCloneAssetInfo, optionFilter);
    return newListFilterAssetInfo;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    const newListFilter = data.filter((item, index) => {
      const { customers, purchasingPower, nav, stock, marginStock, cash, debt, marginRate, assets } = optionFilter;

      const isCustomersMatch = (customers ?? []).length ? (customers ?? []).includes(item.accountNumber) : true;

      const isPurchasingPowerMatch = rangeMatchData(item.purchasingPower, purchasingPower);

      const isNavMatch = rangeMatchData(item.nav, nav);

      const isStock = rangeMatchData(item.stock, stock);

      const isMarginStock = rangeMatchData(item.marginStock.percentMargin, marginStock);

      const isCashMatch = rangeMatchData(item.cash, cash);

      const isDebtMatch = rangeMatchData(item.debt, debt);

      const isMarginRateMatch = rangeMatchData(item.marginRate, marginRate);

      const isAssetMatch = rangeMatchData(item.assets, assets);

      // isStockCodesMatch
      return (
        isCustomersMatch &&
        isPurchasingPowerMatch &&
        isNavMatch &&
        isStock &&
        isMarginStock &&
        isCashMatch &&
        isDebtMatch &&
        isMarginRateMatch &&
        isAssetMatch
      );
    });

    return newListFilter;
  }

  /**
   * OpenAssetInfoDetail
   * @param data
   */
  openAssetInfoDetail(data: any) {
    const { element } = data;
    if (element.children) return;
    this.dialogService.openPopUp(AssetInfoDetailComponent, {
      width: 'calc(100vw - 200px)',
      height: 'calc(100vh - 50px)',
      panelClass: 'open-asset-info-cls',
      data: {
        status: 'asset-info-detail',
        element,
        brokerCode: this.brokerCode,
      },
    });
  }

  /**
   * Tải lại
   */
  loadPage() {
    this.isLoadingPage = true;
    this.store
      .select(selectAllBrokerLevelListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((brokers) => {
        this.store
          .select(pageIndexAssetInfo$)
          .pipe(take(1))
          .subscribe((pageIndex) => {
            if (this.isFilter || this.isSearch) {
              this.isFilter = false;
              this.isSearch = false;
              this.store.dispatch(resetFilterAssetInfo());
            }

            this.updateDataWhenLoadPageSuccess(brokers, pageIndex);
          });
      });
  }

  private updateDataWhenLoadPageSuccess(brokers: IAllLevelOfBroker[], pageIndex: number) {
    this.totalPage = Math.ceil((this.initialCustomer ?? []).map((t) => t.accountNumber).length / this.pageSize);
    this.transformData(
      (this.initialCustomer ?? []).map((t) => t.accountNumber),
      brokers,
      pageIndex
    );

    this.data.forEach((d) => {
      this.processCompareDataAssetInfo(d);
    });
  }

  /**
   * Lưu data để so sánh trong bảng asset info
   */
  processCompareDataAssetInfo(data: any) {
    Object.entries(data).forEach(([key, value]) => {
      if (typeof value === 'number' || key === 'id') {
        this.compareValueService.setData(data.id, key, value as number);
      }
    });
  }

  /**
   * changeViewBrokerAssetInfo
   */
  changeViewBrokerAssetInfo() {
    const queryParamsAssetInfo = this.route.snapshot.queryParams;
    const elementRefAssetInfo = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidthAssetInfo = elementRefAssetInfo.nativeElement as HTMLElement;

    const refAssetInfo = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRefAssetInfo as any,
      width: elementWidthAssetInfo.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    refAssetInfo.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBrokerAssetInfo = userList.find((user) => user.brokerCode === itemSelected['brokerCode']);
          const subBrokerAssetInfo = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelected['brokerCode']);
          if (queryParamsAssetInfo['brokerId'] === itemSelected['brokerCode']) return;

          if (subBrokerAssetInfo && !currentBrokerAssetInfo) {
            const brokerCode = subBrokerAssetInfo['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (currentBrokerAssetInfo) {
            if (this.currentBrokerCode === currentBrokerAssetInfo.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBrokerAssetInfo.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBrokerAssetInfo }));
          }

          if (subBrokerAssetInfo) {
            this.LIST_MG = this.LIST_MG.map((broker) => ({
              ...broker,
              isSelect: broker['brokerCode'] === subBrokerAssetInfo['brokerCode'],
            }));

            this.findButtonByTags(ActionButton.broker).label = subBrokerAssetInfo['name'] ?? '';
          }

          this.isChangeCurrentBroker = true;
          this.store.dispatch(resetFilterAssetInfo());
          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));

          const subBrokerCodeAssetInfo = subBrokerAssetInfo ? subBrokerAssetInfo['brokerCode'] : null;
          this.router.navigate([], {
            queryParams: {
              ...queryParamsAssetInfo,
              brokerId: currentBrokerAssetInfo ? currentBrokerAssetInfo.brokerCode : subBrokerCodeAssetInfo,
            },
          });
        });
    });
  }

  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsAssetComponent,
      width: 'fit-content',
      hasBackdrop: true,
      position: 1,
      componentConfig: {
        brokerCode: this.brokerCode,
        element: element,
      },
    });
  }
}
