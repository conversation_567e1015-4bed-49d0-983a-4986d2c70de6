<div class="portfolio-info-container">
  <div class="header-portfolio-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-99' | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11" *ngFor="let tag of tags">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tag}}
        </div>
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [listColumnInitial]="columnConfigsInitial"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter,
        }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event);openAssetInfoDetail($event)"
      (eventScroll$)="scrollData($event)"
      (resizeColumn)="resizeColumn($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
      (expandRowEvent)="expandRowData($event)"
      (showMoreEvent)="showMoreData($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls"
    >
    </sha-grid>

    <!-- <app-paginator
      (currentPageChanged)="changeCurrentPage($event)"
      [currentPage]="currentPage"
      [totalPage]="totalPage"
      [pageSize]="pageSize"
    >
    </app-paginator> -->
  </div>
</div>

<ng-template #price let-price="templateInfo" let-element="element">
  @if(price) {
  <div>
    <ng-container *ngIf="!element.openingPrice">
      <div class="price-increase typo-body-12">
        <!-- <img src="./assets/icons/up.svg" alt="document-logo" /> -->
        <span class="typo-body-12">{{ customNumberFormat(price)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="price > element.openingPrice">
      <div class="price-increase typo-body-12">
        <!-- <img src="./assets/icons/up.svg" alt="document-logo" /> -->
        <span class="typo-body-12">{{ customNumberFormat(price)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="price < element.openingPrice ">
      <div class="price-reduce typo-body-12">
        <!-- <img src="./assets/icons/down.svg" alt="document-logo" /> -->
        <span class="typo-body-12">{{ customNumberFormat(price)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="price === element.openingPrice">
      <div class="price-stable typo-body-12">
        <!-- <img src="./assets/icons/minus.svg" alt="document-logo" /> -->
        <span class="typo-body-12"> {{ customNumberFormat(price)}}</span>
      </div>
    </ng-container>
  </div>
  } @else {
  <div class="non-price typo-body-12">-</div>
  }
</ng-template>

<ng-template #templateInfo let-element="templateInfo" let-item="element" let-tag="tag">
  @if(element) {
  <div>
    <ng-container *ngIf="element > 0">
      <div
        [ngClass]="{'center-box' : tag === 'currentPrice'}"
        class="asset-info-increase typo-body-12"
        [class.isChangeBgBox]="item?.isChange"
      >
        <img src="./assets/icons/up.svg" alt="document-logo" />
        <span *ngIf="tag === 'currentPrice'; else number" class="typo-body-12">{{ element | numberFormat}}</span>
        <ng-template #number> <span class="typo-body-12">{{ element | numberFormat}}</span></ng-template>
      </div>
    </ng-container>
    <ng-container *ngIf="element < 0 ">
      <div
        [ngClass]="{'center-box' : tag === 'currentPrice'}"
        class="asset-info-reduce typo-body-12"
        [class.isChangeBgBox]="item?.isChange"
      >
        <img src="./assets/icons/down.svg" alt="document-logo" />
        <span *ngIf="tag === 'currentPrice'; else number" class="typo-body-12">{{ element | numberFormat}}</span>
        <ng-template #number> <span class="typo-body-12">{{ element | numberFormat}}</span></ng-template>
      </div>
    </ng-container>
    <ng-container *ngIf="element === 0">
      <div
        [ngClass]="{'center-box' : tag === 'currentPrice'}"
        class="asset-info-stable typo-body-12"
        [class.isChangeBgBox]="item?.isChange"
      >
        <img src="./assets/icons/minus.svg" alt="document-logo" />
        <span *ngIf="tag === 'currentPrice'; else number" class="typo-body-12">{{ element | numberFormat}}</span>
        <ng-template #number> <span class="typo-body-12">{{ element | numberFormat}}</span></ng-template>
      </div>
    </ng-container>
  </div>
  } @else {
  <div [ngClass]="{'center-box' : tag === 'currentPrice'}" class="non-value typo-body-12">-</div>
  }
</ng-template>

<ng-template #percentInvestTemplate let-element="templateInfo" let-item="element">
  @if(element) {
  <div>
    <ng-container *ngIf="element > 0">
      <div class="asset-info-increase typo-body-12" [class.isChangeBgBox]="item?.isChange">
        <!-- <img src="./assets/icons/up.svg" alt="document-logo" /> -->
        <span class="typo-body-12">+{{ element | numberFormat: 'percent'}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="element < 0 ">
      <div class="asset-info-reduce typo-body-12" [class.isChangeBgBox]="item?.isChange">
        <!-- <img src="./assets/icons/down.svg" alt="document-logo" /> -->
        <span class="typo-body-12">{{element | numberFormat: 'percent' }}</span>
      </div>
    </ng-container>
  </div>
  } @else if (element === 0) {
  <ng-container>
    <div class="asset-info-stable typo-body-12" [class.isChangeBgBox]="item?.isChange">
      <span class="typo-body-12">{{ element.toFixed(2) }} %</span>
    </div>
  </ng-container>
  } @else {
  <div>-</div>
  }
</ng-template>

<ng-template #numberTemplate let-numberTemplate="templateInfo" let-element="element" let-rowIdx="rowIdx" let-tag="tag">
  @if(numberTemplate === 0) {
  <div class="number-info typo-body-12">-</div>
  } @else {
  <div class="number-info typo-body-12">{{ customNumberFormat(numberTemplate)}}</div>
  }
</ng-template>
