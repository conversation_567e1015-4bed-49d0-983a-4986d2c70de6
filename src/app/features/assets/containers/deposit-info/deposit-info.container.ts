import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { DestroyService } from 'src/app/core/services';
import { ActionButton, IActionBtn } from 'src/app/shared/components/action-btn/action-btn.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { MARGIN_STATUS_CLASS_MAP, MARGIN_STATUS_MAP } from '../../constant/assets';
import { DepositInfoFilterComponent } from '../../components/deposit-info-filter/deposit-info-filter.component';
import { IFilterDepositInfoParam } from '../../models/asset';
import { take, takeUntil } from 'rxjs';
import { resetFilterDepositInfo, setFilterDepositInfo } from '../../stores/asset.actions';
import { Store } from '@ngrx/store';
import {
  selectFilterDepositInfo$,
  selectFilteredDataDepositInfo$,
  selectSearchValue$,
} from '../../stores/asset.selectors';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { deepClone, rangeMatchData } from 'src/app/shared/utils/utils';
import { IClickOnColumnEvent } from '@shared/models';
import { AssetInfoDetailComponent } from '../../components/assets-info-detail/assets-info-detail.component';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';

/**
 * Ký quỹ VSD
 */
@Component({
  selector: 'app-deposit-info',
  templateUrl: './deposit-info.container.html',
  styleUrl: './deposit-info.container.scss',
})
export class DepositInfoContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('marginAsset', { static: true }) marginAsset: TemplateRef<any> | null = null;
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  filterOptions!: IFilterDepositInfoParam;

  totalDepository = {
    account: 0,
    nav: 0,
    cash: 0,
    marginRate: 0,
    eightyAssets: 0,
    eightyInvest: 0,
    notSubmittedMargin: 0,
    vsdcMargin: 0,
    canWithdrawMargin: 0,
    toSubmittedMargin: 0,
  };
  showCollapse: boolean = true;

  searchValue: string = '';

  customNumberFormat = customNumberFormat;

  tags: string[] = [];
  brokerButton: IActionBtn = {
    label: '',
    icons: 'icon:people-icon',
    name: 'broker',
    isDisplayed: false,
    tag: ActionButton.broker,
  };

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(private readonly _destroy: DestroyService, private readonly store: Store) {
    super();
    this.brokerInfo();
    this.actionButtons.unshift(this.brokerButton);
    this.toggleButtonByTags([
      ActionButton.broker,
      ActionButton.loading,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    const tags = this.totalDepository;
    const {
      account,
      nav,
      cash,
      marginRate,
      eightyAssets,
      eightyInvest,
      notSubmittedMargin,
      vsdcMargin,
      canWithdrawMargin,
      toSubmittedMargin,
    } = tags;

    this.tags = [
      `${account} Tài khoản`,
      `NAV: ${customNumberFormat(nav)}`,
      `Tiền : ${customNumberFormat(cash)}`,
      `Tỷ lệ ký quỹ: ${customNumberFormat(Number(marginRate.toFixed(2)), 'percent')}`,
      `Tài sản 80: ${customNumberFormat(eightyAssets)}`,
      `Đầu tư 80: ${customNumberFormat(eightyInvest)}`,
      `Chưa nộp KQ: ${customNumberFormat(notSubmittedMargin)}`,
      `KQ VSDC: ${customNumberFormat(vsdcMargin)}`,
      `KQ có thể rút: ${customNumberFormat(canWithdrawMargin)}`,
      `KQ phải nộp: ${customNumberFormat(toSubmittedMargin)}`,
    ];

    this.data = structuredClone([]);
    this.initialData = this.data;

    const cellTemplate = this.marginAsset;

    const baseColumnConfigDepositInfo = {
      minWidth: 30,
      width: 156,
      displayValueFn: (v: number) => {
        return customNumberFormat(v);
      },
      isDisplay: true,
      resizable: true,
    }

    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 155,
        width: 155,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 290,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Tỷ lệ ký quỹ',
        minWidth: 30,
        width: 116,
        tag: 'marginRate',
        displayValueFn: (v) => {
          return customNumberFormat(v, 'percent');
        },
        dynamicClass: (value) => {
          if (value < 100) {
            return 'margin-rate-cls';
          } else return 'margin-rate-100-cls';
        },
        isDisplay: true,
        align: 'center',
        resizable: true,
      },
      {
        name: 'Tài sản ròng (NAV)',
        tag: 'nav',
        ...baseColumnConfigDepositInfo,
        align: 'end',
      },
      {
        name: 'Tổng tiền',
        tag: 'cash',
        ...baseColumnConfigDepositInfo,
        align: 'end',
      },
      // {
      //   name: 'Sức mua',
      //   minWidth: 30,
      //   width: 156,
      //   tag: 'purchasingPower',
      //   displayValueFn: (v: number) => {
      //     return customNumberFormat(v);
      //   },
      //   isDisplay: true,
      //   align: 'end',
      //   resizable: true,
      // },
      {
        name: 'Tài sản 80',
        tag: 'eightyAssets',
        ...baseColumnConfigDepositInfo,
        align: 'end',
      },
      {
        name: '+/- Tài sản 80',
        minWidth: 30,
        width: 225,
        tag: 'marginEightyAssets',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
      {
        name: 'Giá trị đầu tư 80',
        minWidth: 30,
        width: 156,
        tag: 'eightyInvest',
        isDisplay: true,
        displayValueFn: (v: number) => {
          return customNumberFormat(v);
        },
        align: 'end',
        resizable: true,
      },
      {
        name: '+/- GTĐT 80 chưa đóng',
        minWidth: 30,
        width: 225,
        tag: 'marginUnclosedEightyInvest',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
      {
        name: '+/- GTĐT 80 đã đóng',
        minWidth: 30,
        width: 225,
        tag: 'marginClosedEightyInvest',
        isDisplay: true,
        resizable: true,
        cellTemplate,
      },
      {
        name: 'Tình trạng ký quỹ',
        minWidth: 30,
        width: 156,
        tag: 'marginStatus',
        isDisplay: true,
        displayValueFn: (v) => {
          return MARGIN_STATUS_MAP[v];
        },
        dynamicClass: (v) => {
          return MARGIN_STATUS_CLASS_MAP[v];
        },
        align: 'center',
        resizable: true,
      },
      {
        name: 'Số dư chưa nộp ký quỹ',
        minWidth: 30,
        width: 180,
        tag: 'notSubmittedMargin',
        isDisplay: true,
        displayValueFn: (v: number) => {
          return customNumberFormat(v);
        },
        align: 'end',
        resizable: true,
      },
      {
        name: 'Tiền ký quỹ VSDC',
        minWidth: 30,
        width: 156,
        tag: 'vsdcMargin',
        isDisplay: true,
        displayValueFn: (v: number) => {
          return customNumberFormat(v);
        },
        align: 'end',
        resizable: true,
      },
      {
        name: 'Tiền ký quỹ có thể rút',
        minWidth: 30,
        width: 170,
        tag: 'canWithdrawMargin',
        isDisplay: true,
        displayValueFn: (v: number) => {
          return customNumberFormat(v);
        },
        align: 'end',
        resizable: true,
      },
      {
        name: 'Tiền ký quỹ phải nộp',
        minWidth: 30,
        width: 160,
        tag: 'toSubmittedMargin',
        isDisplay: true,
        displayValueFn: (v: number) => {
          return customNumberFormat(v);
        },
        align: 'end',
        resizable: true,
      },
    ];

    this.store
      .select(selectFilterDepositInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter?.isFilter;
      });

    this.store
      .select(selectFilteredDataDepositInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filteredData) => {
        this.data = filteredData.length ? filteredData : this.initialData;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchMultipleLevel(value ?? '', ['accountNumber', 'customerName']);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterDepositInfo());
  }

  /**
   * searchData
   */
  searchData() {
    let searchData = [];
    if (this.searchValue) {
      const searchValue = this.searchValue.toString().toLowerCase();
      searchData = this.initialData.filter((item) => {
        return this.containsSearchValue(item, searchValue);
      });

      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchData = [...this.initialData];
      this.isSearch = false; // Set isSearch to false when not searching
    }

    this.data = structuredClone(searchData);
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    return (
      item.accountNumber?.toString().toLowerCase().includes(searchValue) ??
      item.customerName?.toString().toLowerCase().includes(searchValue)
    );
  }

  /**
   * Thông tin của broker
   */
  brokerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        this.brokerButton.label = user ? `${user?.brokerCode}: ${user?.brokerName}` : ''; // fix me later
      });
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    const customers = this.initialData.map((item) => {
      return {
        group: item.accountNumber,
        name: item.customerName,
      };
    });

    switch (tag) {
      case 'broker':
        this.changeViewBroker();
        break;

      case 'filter':
        {
          const ref = this.openFilter(DepositInfoFilterComponent, {
            width: '800px',
            data: {
              filterOptions: this.filterOptions,
              customers,
            },
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe({
              next: (v) => {
                if (!v) return;
                this.applyFilterDepositInfo(v);
              },
            });
        }
        break;

      default:
        break;
    }
  }

  /**
   * ApplyFilterDepositInfo
   * @param data
   */
  applyFilterDepositInfo(data: any) {
    const { optionFilter, type } = data;
    if (type === 'save') {
      const newListFilterDepositInfo = this.saveFuncDepositInfo(optionFilter);
      this.filteredData = newListFilterDepositInfo;
      this.data = newListFilterDepositInfo;
    } else if (type === 'default') {
      this.isFilter = false;
      this.filteredData = [];
      this.updateDataWhenDefaultFilterDI()
      this.store.dispatch(resetFilterDepositInfo());
    }
  }

  private updateDataWhenDefaultFilterDI() {
    this.isSearch
      ? this.store
        .select(selectSearchValue$)
        .pipe(takeUntil(this._destroy))
        .subscribe((value) => {
          this.searchMultipleLevel(value ?? '', ['accountNumber', 'customerName']);
        })
      : (() => {
        this.data = this.initialData;
      })();
  }

  /**
   * UpdateParamInStoreDepositInfo
   * @param optionFilter
   */
  updateParamInStoreDepositInfo(optionFilter: any) {
    this.store.dispatch(setFilterDepositInfo({ params: optionFilter }));
  }

  /**
   * SaveFuncDepositInfo
   * @param optionFilter
   * @returns {any} new list
   */
  saveFuncDepositInfo(optionFilter: any) {
    const dataCloneDI = deepClone(this.initialData);
    const newListFilterDI = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(dataCloneDI, optionFilter);
    return newListFilterDI;
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {

    this.updateParamInStoreDepositInfo(optionFilter);

    const newListFilter = data.filter((item, index) => {
      const {
        customersSelect,
        marginRate,
        nav,
        cash,
        purchasingPower,
        eightyAssets,
        eightyInvest,
        notSubmittedMargin,
        marginStatus,
        vsdcMargin,
        canWithdrawMargin,
        toSubmittedMargin,
      } = optionFilter;

      const isCustomerMatch = (customersSelect ?? []).length
        ? (customersSelect ?? []).includes(item.customerName)
        : true;
      const isNavMatch = rangeMatchData(item.nav, nav);

      const isCashMatch = rangeMatchData(item.cash, cash);

      const isPurchasingPowerMatch = rangeMatchData(item.purchasingPower, purchasingPower);

      const isEightAssets = rangeMatchData(item.eightyAssets, eightyAssets);

      const isEightyInvest = rangeMatchData(item.eightyInvest, eightyInvest);

      const isMarginRateMatch = rangeMatchData(item.marginRate, marginRate);

      const isNotSubmittedMarginMatch = rangeMatchData(item.notSubmittedMargin, notSubmittedMargin);

      const isVsdcMargin = rangeMatchData(item.vsdcMargin, vsdcMargin);

      const isCanWithdrawMargin = rangeMatchData(item.canWithdrawMargin, canWithdrawMargin);

      const isToSubmittedMargin = rangeMatchData(item.toSubmittedMargin, toSubmittedMargin);

      const isMarginStatusMatch = (marginStatus ?? []).length ? (marginStatus ?? []).includes(item.marginStatus) : true;

      return (
        isCustomerMatch &&
        isNavMatch &&
        isCashMatch &&
        isPurchasingPowerMatch &&
        isEightAssets &&
        isEightyInvest &&
        isMarginRateMatch &&
        isNotSubmittedMarginMatch &&
        isMarginStatusMatch &&
        isVsdcMargin &&
        isCanWithdrawMargin &&
        isToSubmittedMargin
      );
    });

    return newListFilter;
  }

  /**
   * OpenAssetInfoDetail
   * @param event IClickOnColumnEvent
   */
  openAssetInfoDetail(event: IClickOnColumnEvent) {
    const { element } = event;
    this.dialogService.openPopUp(AssetInfoDetailComponent, {
      width: 'calc(100vw - 200px)',
      height: 'calc(100vh - 50px)',
      panelClass: 'open-asset-info-cls',
      data: {
        status: 'deposit-vsd-detail',
        element,
      },
    });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    console.log('changeViewBroker');
  }
}
