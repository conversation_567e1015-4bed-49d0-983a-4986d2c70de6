<div class="deposit-info-container">
  <div class="header-deposit-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-71' | translate}}</div>
      <div class="number-info-cls" *ngIf="!showCollapse">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          10 {{'MES-15' | translate}}
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          GTGD: 90.440.000.000
        </div>
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />Doanh thu phí: 90.440.000.000
        </div>
        <div class="dropdown-btn" *ngIf="!showCollapse" (click)="toggleButtons()">
          <img src="./assets/icons/arrow-down-btn.svg" alt="" />
        </div>
      </div>
      <div class="collapse-btn" *ngIf="showCollapse" (click)="toggleButtons()">
        <img src="./assets/icons/arrow-up-orange.svg" alt="" />
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter,
        }"
      ></app-action-btn>
    </div>
  </div>

  <div [ngClass]="{'hidden': !showCollapse}">
    <app-slide-tag [tags]="tags" [classParent]="'deposit-info-container'"></app-slide-tag>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event);openAssetInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
    >
    </sha-grid>
  </div>
</div>

<ng-template #marginAsset let-marginAsset="templateInfo">
  @if(marginAsset) {
  <div>
    <ng-container *ngIf="marginAsset.percentMargin > 0">
      <div class="deposit-info-increase typo-body-12">
        <img src="./assets/icons/up.svg" alt="document-logo" />
        <span class="typo-body-12"
          >+{{ marginAsset.numberMargin | numberFormat}} +{{ marginAsset.percentMargin | numberFormat : 'percent'
          }}</span
        >
      </div>
    </ng-container>
    <ng-container *ngIf="marginAsset.percentMargin < 0 ">
      <div class="deposit-info-reduce typo-body-12">
        <img src="./assets/icons/down.svg" alt="document-logo" />
        <span class="typo-body-12"
          >{{ marginAsset.numberMargin | numberFormat }} {{ marginAsset.percentMargin | numberFormat : 'percent'
          }}</span
        >
      </div>
    </ng-container>
    <ng-container *ngIf="marginAsset.percentMargin === 0">
      <div class="deposit-info-stable typo-body-12">
        <img src="./assets/icons/minus.svg" alt="document-logo" />
        <span class="typo-body-12"
          >{{ marginAsset.numberMargin | numberFormat }} - {{ marginAsset.percentMargin | numberFormat : 'percent'
          }}</span
        >
      </div>
    </ng-container>
  </div>
  } @else {
  <div>-</div>
  }
</ng-template>
