.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  color: #33343e;
  line-height: 20px;
}

.transaction-info-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-transaction-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 8px;
    padding: 16px 12px;

    .middle-box {
      order: 2;
      overflow: hidden;
    }

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;
      flex: 0 0 auto;

      .text-header {
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        text-wrap: nowrap;
        white-space: nowrap;
        line-height: 28px;
      }

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;
      margin-left: auto;
      flex: 0 0 auto;
      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--broker {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          mat-icon[data-mat-icon-name='people-icon'] {
            #Group,
            #Group_2,
            #Group_3 {
              ::ng-deep {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
            #box-id--loading {
              opacity: 0.5;
              cursor: not-allowed !important;
              .icons-cls {
                cursor: not-allowed !important;
              }
            }
          }
        }

        &.disable-btn-cls {
          ::ng-deep {
            .box-btn {
              opacity: 0.5;
            }
          }
        }
      }
    }
  }

  .tag-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
    height: 56px;
    overflow: hidden;
    transition: transform 0.5s ease;
    text-wrap: nowrap;
    white-space: nowrap;
    padding: 12px 0px;
    margin: 0 12px;
    position: relative;

    .box-info {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 8px;
      border-radius: 16px;
      background-color: #f8fafd;
      box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
      color: #808080;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      white-space: nowrap;
      text-wrap: nowrap;
      width: fit-content;
    }
  }
}

.table-view-container {
  height: calc(100% - 50px);
  display: flex;
  flex-direction: column;
  .table-custom-cls {
    height: calc(100% - 50px);
    ::ng-deep {
      .table-container {
        position: relative;
      }

      tr[row-expanded='true'] {
        background-color: var(--color--background--hover);
      }

      td {
        cursor: pointer;
      }
      .gtgd-buy {
        span {
          input {
            color: var(--color--accents--green);
          }
        }
      }

      &.gtgd-sell {
        span {
          input {
            color: var(--color--accents--red);
          }
        }
      }
    }
  }
}

// Hide the tabContainer
.hidden {
  display: none;
}

.dropdown-btn,
.collapse-btn {
  display: flex;
  align-items: center;
  background-color: var(--color--brand--50);
  border-radius: 16px;

  img {
    padding: 4px, 8px, 4px, 8px;
    width: 20px;
    height: 20px;
  }
}
