import { Component, ElementRef, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { DestroyService, I18nService, LoadingService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import {
  ISumInfoItem,
  IChildrenTransactionData,
  IFilterTransactionParam,
  ISumTransactionPayLoad,
  ITransactionInfoResponse,
  ITransactionsSumInfo,
} from '../../models/asset';
import {
  pageIndexTransactionInfo$,
  selectFilterTransaction$,
  selectSearchValue$,
  selectTransactionSumInfo$,
  selectTransationList$,
} from '../../stores/asset.selectors';
import { combineLatest, finalize, map, take, takeUntil, withL<PERSON>tFrom } from 'rxjs';
import { Store } from '@ngrx/store';
import { TransactionInfoFilterComponent } from '../../components/transaction-info-filter/transaction-info-filter.component';
import {
  getTransactionInfo,
  resetFilterTransaction,
  resetSearch,
  setFilterTransaction,
  updatePageIndexTransactionInfo,
} from '../../stores/asset.actions';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { deepClone, rangeMatchData, updateBorkerName } from 'src/app/shared/utils/utils';
import { IClickOnColumnEvent } from '@shared/models';
import { AssetInfoDetailComponent } from '../../components/assets-info-detail/assets-info-detail.component';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { CompareValueService } from 'src/app/shared/directives/compare-value/compare-value.service';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { IListOptions } from '../../constant/assets';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { getCurrentBrokerView, getListAccountNumberAndLevelByBrokerView } from 'src/app/stores/shared/shared.actions';
import { SharedService } from 'src/app/shared/services/shared.service';
import { DEFAULT_ROW_BACKGROUND_COLOR } from 'src/app/shared/constants/config';
import { Router } from '@angular/router';
import { OptionsAssetComponent } from '../../components/options-asset/options-asset.component';
import { IOption } from 'src/app/shared/models/dropdown-item.model';

/**
 * Giao dịch
 */
@Component({
  selector: 'app-transaction-info',
  templateUrl: './transaction-info.container.html',
  styleUrl: './transaction-info.container.scss',
  providers: [DestroyService],
})
export class TransactionInfoContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  showCollapse: boolean = true;

  tags!: string[];

  filterOptions!: IFilterTransactionParam;

  extendsTags!: string[];

  pageSize = 10;

  totalPage: number = 1;

  currentPage = 1;

  initialCustomer!: IAllAccountNumber[];

  dataStore: any[] = [];

  LIST_MG: IListOptions[] = [];

  brokerCode = '';

  currentBrokerCode = '';

  storeTotalPage = 1;

  customerMap: { [key: string]: string } = {};

  expandedByAccountNumberMap: Set<string> = new Set();

  preventDuplicate = false;

  isFilterJustAccountNumber = false;

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param store Store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly localStorageService: LocalStorageService,
    private readonly compareValueService: CompareValueService,
    private readonly popoverService: PopoverService,
    private readonly router: Router,
    private readonly shareService: SharedService,
    private readonly loadingService: LoadingService,
    private readonly i18n: I18nService
  ) {
    super();
    this.brokerInfoTI();
    this.toggleButtonByTags([
      ActionButton.broker,
      ActionButton.loading,
      // ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    const baseColumnConfigTI = {
      minWidth: 30,
      width: 156,
      displayValueFn: (v: number) => {
        if (!v) {
          return '-';
        } else {
          return customNumberFormat(v);
        }
      },
      isDisplay: true,
      align: 'end',
      resizable: true,
    } as any;

    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 165,
        width: 165,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 200,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Tổng GTGD',
        tag: 'totalTransactionValue',
        ...baseColumnConfigTI,
      },
      {
        name: 'GTGD (mua)',
        tag: 'buyTransactionValue',
        panelClass: 'gtgd-buy',
        ...baseColumnConfigTI,
      },
      {
        name: 'GTGD (bán)',
        panelClass: 'gtgd-sell',
        tag: 'sellTransactionValue',
        ...baseColumnConfigTI,
      },
      {
        name: 'Tổng phí',
        tag: 'revenueFee',
        ...baseColumnConfigTI,
      },
      {
        name: 'Phí trả sở',
        minWidth: 30,
        width: 156,
        tag: 'stockExchangeFee',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
      },
      {
        name: 'Phí khác (Phí chuyển khoản bán)',
        minWidth: 30,
        width: 250,
        tag: 'otherFees',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
      },
      {
        name: 'Thuế TNCN',
        minWidth: 30,
        width: 156,
        tag: 'depositoryFee',
        displayValueFn: (v: number) => {
          if (v === 0) return '-';
          return customNumberFormat(v);
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
      },
      // {
      //   name: 'Thuế cổ tức',
      //   minWidth: 30,
      //   width: 156,
      //   tag: 'smsFee',
      //   displayValueFn: (v: number) => {
      //     if (!v) {
      //       return '-';
      //     } else {
      //       return customNumberFormat(v);
      //     }
      //   },
      //   isDisplay: true,
      //   align: 'end',
      //   resizable: true,
      // },
      {
        name: 'Net phí giao dịch',
        minWidth: 30,
        width: 156,
        tag: 'netTransactionFee',
        displayValueFn: (v: number) => {
          if (!v) {
            return '-';
          } else {
            return customNumberFormat(v);
          }
        },
        isDisplay: true,
        align: 'end',
        resizable: true,
      },
      // {
      //   name: 'Hoa hồng MG',
      //   minWidth: 30,
      //   width: 156,
      //   tag: 'brokerCommission',
      //   displayValueFn: (v: number) => {
      //     if (!v) {
      //       return '-';
      //     } else {
      //       return customNumberFormat(v);
      //     }
      //   },
      //   isDisplay: true,
      //   align: 'end',
      //   resizable: true,
      // },
    ];

    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((customerAcc) => {
        if (!customerAcc.length) {
          this.data = [];
          this.initialData = [];
          this.isFilter = false;
          this.totalPage = 1;
          this.currentPage = 1;
          return;
        }

        this.initialCustomer = customerAcc;

        this.customerMap = {};
        customerAcc.forEach((customer) => {
          this.customerMap[customer.accountNumber] = customer.customerName;
        });

        if (this.initialCustomer.length) this.transformData([]);

        // }
      });

    this.store
      .select(selectFilterTransaction$)
      .pipe(takeUntil(this._destroy), withLatestFrom(this.store.select(selectSearchValue$)))
      .subscribe(([filter, search]) => {
        this.isFilter = filter?.isFilter;
        this.filterOptions = { ...filter };

        const customerBySearch = this.initialCustomer
          ?.filter((customer) => this.containsSearchValueTI(customer, search))
          .map((t) => t.accountNumber);

        let customerInfos: string[];
        if (search) {
          customerInfos = customerBySearch;
        } else if (filter.isFilter && filter.customers) {
          if (filter.customers.length === this.initialCustomer.length) {
            customerInfos = [];
          } else {
            customerInfos = filter.customers;
          }
        } else {
          customerInfos = [];
        }

        if (filter.isFilter) {
          this.checkParamsJustFilterAccountNumber();
        }
        if (this.preventDuplicate) {
          this.preventDuplicate = false;
          return;
        }
        if (this.initialCustomer?.length) this.transformData(customerInfos);
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        if (this.grid?.map) this.map.resetExpandedRowAmountMap();
        this.store.dispatch(resetFilterTransaction());
        this.isSearch = !!value;
      });

    this.updateTransactionInfo();
  }

  private checkParamsJustFilterAccountNumber() {
    const filteredFields = Object.entries(this.filterOptions).filter(([key, value]) => {
      return value !== null && typeof value === 'object' && 'start' in value && 'end' in value;
    });

    const valueAllField = Object.values(Object.fromEntries(filteredFields));

    this.isFilterJustAccountNumber = valueAllField.every((t) => !this.checkHasValueInObject(t));
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  private checkHasValueInObject(data: any) {
    return (!!data && data.start != null && data.start !== '') || (data.end != null && data.end !== '');
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
    this.cdf.detectChanges();
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    if (this.isFilter) {
      this.store.dispatch(resetFilterTransaction());
    }

    if (this.isSearch) {
      this.store.dispatch(resetSearch());
    }
    this.localStorageService.removeData('currentPage');
  }

  transformData(customerId: string[]) {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(take(1))
      .subscribe((currentBroker) => {
        if (!currentBroker) {
          return;
        }

        const brokerId = this.route.snapshot.queryParams['brokerId'];
        this.brokerCode = brokerId ?? currentBroker.brokerCode;

        if (!customerId.length) {
          const sumTransactionPayLoad = {
            brokerCode: [brokerId ?? currentBroker.brokerCode],
            customerInfos: [],
          };
          this.store.dispatch(getTransactionInfo({ payload: sumTransactionPayLoad }));
          return;
        }

        const customerInfos = customerId.map((item) => {
          return {
            accountNumber: item,
            subAccount: [],
            customerName: this.customerMap[item],
          };
        });

        const SumTransactionPayLoad: ISumTransactionPayLoad = {
          brokerCode: [brokerId ?? currentBroker.brokerCode],
          customerInfos,
        };

        this.store.dispatch(getTransactionInfo({ payload: SumTransactionPayLoad }));
      });
  }

  updateTransactionInfo() {
    this.store
      .select(selectTransationList$)
      .pipe(withLatestFrom(this.store.select(selectFilterTransaction$)), takeUntil(this._destroy))
      .subscribe(([transactionList, filter]) => {
        if (!transactionList.length) {
          this.data = [];
          this.totalPage = 1;
          this.currentPage = 1;
          this.initialData = [];
          return;
        }

        this.dataStore = transactionList.reduce((initial: any, d) => {
          const isExpandedAmount =
            this.map.getHasExpandedAmountByRowId(d.id ?? '') && (this.map.getExpandedAmountByRowId(d.id ?? '') ?? 0);
          const isNotFilter = this.grid ? isExpandedAmount : false;

          const object = {
            ...d,
            customerName: this.customerMap[d.accountNumber],
            isExpanded: filter?.isFilter ? !filter.isFilter : isNotFilter,
            isCallAPIChildren: true,
            children: d.children.map((child) => ({
              ...child,
              parent: {
                ...d,
                customerName: this.customerMap[child.accountNumber],
              },
              accountNumber: `${child.accountNumber} - ${child.subAccount}`,
            })),
          };

          if (object.isExpanded) {
            const items = d.children.map((t) => ({
              ...t,
              parent: object,
              accountNumber: `${t.accountNumber} - ${t.subAccount}`,
              backgroundColor: '#f6f6f6',
            }));
            initial.push(object, ...items);
          } else {
            initial.push(object);
          }
          return initial;
        }, []);

        this.sliceData([...this.dataStore]);
      });

    this.store
      .select(selectTransactionSumInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((res) => {
        if (!res) return;
        const {
          sumAccountNumber,
          sumTotalTransactionValue,
          sumBuyTransactionValue,
          sumSellTransactionValue,
          sumRevenueFee,
          sumStockExchangeFee,
          sumOtherFees,
          sumDepositoryFee,
          sumNetTransactionFee,
        } = res;
        this.tags = [
          `${sumAccountNumber} ${this.i18n.translate('MES-15')}`,
          `${this.i18n.translate('MES-441')}: ${customNumberFormat(sumTotalTransactionValue)}`,
          `${this.i18n.translate('MES-442')}: ${customNumberFormat(sumBuyTransactionValue)}`,
          `${this.i18n.translate('MES-443')}: ${customNumberFormat(sumSellTransactionValue)}`,
          `${this.i18n.translate('MES-581')}: ${customNumberFormat(sumRevenueFee)}`,
          `${this.i18n.translate('MES-288')}: ${customNumberFormat(sumStockExchangeFee)}`,
          `${this.i18n.translate('MES-693')}: ${customNumberFormat(sumOtherFees)}`,
          `${this.i18n.translate('MES-415')}: ${customNumberFormat(sumDepositoryFee)}`,
          `${this.i18n.translate('MES-195')}: ${customNumberFormat(sumNetTransactionFee)}`,
        ];
      });
  }

  private sliceData(data: any) {
    this.store
      .select(pageIndexTransactionInfo$)
      .pipe(take(1))
      .subscribe((pageIndex) => {
        this.data = [...data].slice((pageIndex - 1) * this.pageSize, pageIndex * this.pageSize);
        this.totalPage = Math.ceil(data.length / this.pageSize);
        // this.totalTags(this.data);
        this.initialData = deepClone(this.data);
      });
  }

  /**
   * calculateSum
   * @param {ITransactionInfoResponse[]} data
   * @param {string} field
   * @returns {number}
   */
  calculateSum(data: ITransactionInfoResponse[], field: keyof ITransactionInfoResponse): number {
    return data.reduce((sum, item) => {
      // Process only items with children
      if (!item.children || item.children.length === 0) {
        return sum;
      }
      // Get the value for the given field and add it to the sum if it's a number
      const value = item[field];
      return sum + (typeof value === 'number' ? value : 0);
    }, 0);
  }

  /**
   * changeCurrentPage
   * @param data
   */
  changeCurrentPage(currentPage: number) {
    this.loadingService.show();

    setTimeout(() => {
      this.loadingService.hide();
    }, 500);
    this.currentPage = currentPage;

    this.store.dispatch(updatePageIndexTransactionInfo({ pageIndex: currentPage }));
    this.map.resetExpandedRowAmountMap();
    this.clearExpand();
    this.sliceData([...this.dataStore]);
  }

  private clearExpand() {
    this.data.forEach((d) => {
      d.isExpanded = false;
      d.backgroundColor = '';
    });
  }

  updateDataItem() {
    const ITEM_SIZE = 10;
    const start = (this.currentPage - 1) * ITEM_SIZE;
    const end = Math.min(start + ITEM_SIZE, this.filteredData.length);
    const data = this.filteredData.slice(start, end);
    this.data = [...data];
  }

  /**
   * searchData
   * @param {string} value searchValue
   */
  searchData(value: string) {
    let searchData = [];
    const dataClone = deepClone(this.dataStore);
    const searchValue = value.toString().toLowerCase();
    if (value) {
      searchData = dataClone.filter((item) => {
        return this.containsSearchValueTI(item, searchValue);
      });

      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchData = deepClone(this.dataStore);
      this.isSearch = false; // Set isSearch to false when not searching
    }

    return searchData;
  }

  /**
   * containsSearchValueTI
   * @param item
   * @param searchValue
   */
  containsSearchValueTI(item: any, searchValue: string): boolean {
    const accountNumberTI = item.accountNumber?.toString().toLowerCase();
    const customerNameTI = item.customerName?.toString().toLowerCase();

    return accountNumberTI?.includes(searchValue) ?? customerNameTI?.includes(searchValue);
  }

  /**
   * Thông tin của broker
   */
  brokerInfoTI() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBroker) => {
        if (!currentBroker) return;
        this.currentBrokerCode = currentBroker.brokerCode;

        combineLatest([
          this.store.select(selectInfoUserLogin$),
          this.store.select(selectAllBrokerLevelListByBrokerView$),
        ])
          .pipe(takeUntil(this._destroy))
          .subscribe(([userList, brokers]) => {
            const queryParamsTI = this.route.snapshot.queryParams;

            if (!userList) return;

            const brokerConvertTI = updateBorkerName([...brokers], [...userList]);

            const subBrokerTI = brokerConvertTI.map((broker) => {
              return {
                brokerCode: `${broker.brokerCode}`,
                name: `${broker.brokerName}`,
                isSelect: queryParamsTI['brokerId']
                  ? broker.brokerCode === queryParamsTI['brokerId']
                  : broker.brokerCode === currentBroker.brokerCode,
              };
            });

            this.LIST_MG = [...subBrokerTI];
            const brokerTI = this.LIST_MG.find((t) => t.isSelect);

            if (brokerTI) {
              this.findButtonByTags(ActionButton.broker).label = brokerTI['name'] ?? '';
            }
          });
      });
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'broker':
        this.changeViewBrokerTI();
        break;

      case 'loading':
        if (this.isFilter) return;
        this.loadPageTI();
        break;

      case 'filter':
        {
          const ref = this.openFilter(TransactionInfoFilterComponent, {
            width: '800px',
            data: this.filterOptions,
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe({
              next: (v) => {
                if (!v) return;
                this.applyFilterTI(v);
              },
            });
        }
        break;
    }
  }

  expandRowData({ data, index, isExpand }: { data: any; index: number; isExpand: boolean }) {
    if (!isExpand) {
      this.removeItemsAt(index);
      return;
    }
    const accountNumber = data.accountNumber;
    const subAccountMap = new Set(
      data.children.filter((d: any) => d?.id !== 'sample').map((t: { subAccount: string }) => t.subAccount)
    );

    const DATA_DEFAULT = {
      customerName: null,
      totalTransactionValue: 0,
      buyTransactionValue: 0,
      sellTransactionValue: 0,
      revenueFee: 0,
      stockExchangeFee: 0,
      depositoryFee: 0,
      smsFee: 0,
      netTransactionFee: 0,
      otherFees: 0,
    };

    if ((!this.expandedByAccountNumberMap.has(accountNumber) && !this.isFilter) || this.isFilterJustAccountNumber) {
      this.loadingService.show();
      this.shareService
        .getAccountNumberAndSubAccount([accountNumber])
        .pipe(
          take(1),
          map((dataSub) => {
            this.expandedByAccountNumberMap.add(accountNumber);
            const subNotExist = dataSub[0].subAccounts.filter((d) => !subAccountMap.has(d.subNo));
            const dataDefault = subNotExist.map(({ subNo }) => ({
              ...DATA_DEFAULT,
              subAccount: subNo,
              accountNumber: `${accountNumber} - ${subNo}`,
              customerName: this.customerMap[accountNumber],
            }));

            const children = data.children
              .filter((d: IChildrenTransactionData) => d?.id !== 'sample')
              .map((d: IChildrenTransactionData) => ({
                ...d,
                customerName: this.customerMap[accountNumber],
              }));

            const finalChildren = [...children, ...dataDefault];

            this.updateDataChildren(index, finalChildren);
          }),
          finalize(() => this.loadingService.hide())
        )
        .subscribe();
    } else {
      this.addItemsAt({ index, items: data.children });
    }
  }

  collapseRow({ data, index }: { data: any; index: number }) {
    this.removeItemsAt(index);
  }

  private updateDataChildren(index: number, children: IChildrenTransactionData[]) {
    const data = [...this.data];

    data[index].children = [...children];
    data[index].isExpanded = true;
    data[index].backgroundColor = DEFAULT_ROW_BACKGROUND_COLOR;
    this.addItemsAt({ index, items: children });

    this.initialData = [...this.data];
  }

  /**
   * ApplyFilterTI
   * @param data
   */
  applyFilterTI(data: any) {
    const { optionFilter, type } = data;
    this.expandedByAccountNumberMap.clear();
    if (type === 'save') {
      this.currentPage = 1;
      if (optionFilter.isFilter) {
        if (this.isSearch) {
          this.preventDuplicate = true;
          this.store.dispatch(resetSearch());
        }

        this.store.dispatch(setFilterTransaction({ params: optionFilter }));
      } else this.store.dispatch(resetFilterTransaction());
    } else if (type === 'default') {
      this.currentPage = 1;
      this.filteredData = [];
      this.store.dispatch(resetFilterTransaction());
      this.map.resetExpandedRowAmountMap();
    }
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilter: any) {
    const dataCloneTI = deepClone(this.initialData);
    const newListFilterTI = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(dataCloneTI, optionFilter);

    return newListFilterTI;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    const newListFilter = data.filter((item, index) => {
      const {
        customers,
        totalTransactionValue,
        buyTransactionValue,
        sellTransactionValue,
        revenueFee,
        netTransactionFee,
        brokerCommission,
        stockExchangeFee,
        depositoryFee,
        smsFee,
      } = optionFilter;
      const isCustomerMatch = (customers ?? []).length ? (customers ?? []).includes(item.accountNumber) : true;
      const isTotalTransactionValueMatch = rangeMatchData(item.totalTransactionValue, totalTransactionValue);
      const isBuyTransactionValueMatch = rangeMatchData(item.buyTransactionValue, buyTransactionValue);

      const isSellTransactionValueMatch = rangeMatchData(item.sellTransactionValue, sellTransactionValue);

      const isRevenueFeeMatch = rangeMatchData(item.revenueFee, revenueFee);

      const isNetTransactionFeeMatch = rangeMatchData(item.netTransactionFee, netTransactionFee);

      const isBrokerCommissionMatch = rangeMatchData(item.brokerCommission, brokerCommission);

      const isStockExchangeFeeMatch = rangeMatchData(item.stockExchangeFee, stockExchangeFee);

      const isDepositoryFeeMatch = rangeMatchData(item.depositoryFee, depositoryFee);

      const isSmsFeeMatch = rangeMatchData(item.smsFee, smsFee);

      return (
        isCustomerMatch &&
        isTotalTransactionValueMatch &&
        isBuyTransactionValueMatch &&
        isSellTransactionValueMatch &&
        isRevenueFeeMatch &&
        isNetTransactionFeeMatch &&
        isBrokerCommissionMatch &&
        isStockExchangeFeeMatch &&
        isDepositoryFeeMatch &&
        isSmsFeeMatch
      );
    });

    return newListFilter;
  }

  /**
   * Tải lại
   */
  loadPageTI() {
    if (this.isSearch) {
      this.store.dispatch(resetSearch());
    } else {
      this.transformData([]);
    }
    this.expandedByAccountNumberMap.clear();
    this.map.resetExpandedRowAmountMap();
    this.clearExpand();
  }

  /**
   * Show or hide the tag Slide
   */
  toggleButtons() {
    this.showCollapse = !this.showCollapse;
  }

  /**
   * OpenAssetInfoDetail
   * @param event IClickOnColumnEvent
   */
  openAssetInfoDetail(event: IClickOnColumnEvent) {
    const { element } = event;
    if (element.children) return;
    this.dialogService.openPopUp(AssetInfoDetailComponent, {
      width: 'calc(100vw - 200px)',
      height: 'calc(100vh - 50px)',
      panelClass: 'open-asset-info-cls',
      data: {
        status: 'transaction-info-detail',
        element,
        brokerCode: this.brokerCode,
      },
    });
  }

  /**
   * changeViewBrokerTI
   */
  changeViewBrokerTI() {
    const queryParamsTI = this.route.snapshot.queryParams;
    const elementRefTI = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidthTI = elementRefTI.nativeElement as HTMLElement;

    const refTI = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRefTI as any,
      width: elementWidthTI.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    refTI.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBrokerTI = userList.find((user) => user.brokerCode === itemSelected['brokerCode']);
          const subBrokerTI = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelected['brokerCode']);
          if (queryParamsTI['brokerId'] === itemSelected['brokerCode']) return;
          if (subBrokerTI && !currentBrokerTI) {
            const brokerCode = subBrokerTI['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (currentBrokerTI) {
            if (this.currentBrokerCode === currentBrokerTI.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBrokerTI.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBrokerTI }));
          }

          if (subBrokerTI) {
            this.LIST_MG = this.LIST_MG.map((broker) => ({
              ...broker,
              isSelect: broker['brokerCode'] === subBrokerTI['brokerCode'],
            }));

            this.findButtonByTags(ActionButton.broker).label = subBrokerTI['name'] ?? '';
          }

          this.store.dispatch(updatePageIndexTransactionInfo({ pageIndex: 1 }));
          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));

          const subBrokerCode = subBrokerTI ? subBrokerTI['brokerCode'] : null;
          this.router.navigate([], {
            queryParams: {
              ...queryParamsTI,
              brokerId: currentBrokerTI ? currentBrokerTI.brokerCode : subBrokerCode,
            },
          });
        });
    });
  }

  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsAssetComponent,
      width: 'fit-content',
      hasBackdrop: true,
      position: 1,
      componentConfig: {
        brokerCode: this.brokerCode,
        element: element,
      },
    });
  }
}
