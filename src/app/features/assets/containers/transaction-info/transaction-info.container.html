<div class="transaction-info-container">
  <div class="header-transaction-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-68' | translate}}</div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [listColumnInitial]="columnConfigsInitial"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter,
        }"
      ></app-action-btn>
    </div>
  </div>

  <app-slide-tag [tags]="tags" [classParent]="'transaction-info-container'"> </app-slide-tag>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); openAssetInfoDetail($event)"
      (resizeColumn)="resizeColumn($event)"
      (addItemsAt)="addItemsAt($event)"
      (removeItemsAt)="removeItemsAt($event)"
      (expandRowEvent)="expandRowData($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls"
    >
    </sha-grid>

    <app-paginator (currentPageChanged)="changeCurrentPage($event)" [currentPage]="currentPage" [totalPage]="totalPage">
    </app-paginator>
  </div>
</div>
