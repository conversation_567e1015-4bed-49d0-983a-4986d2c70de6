.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.conditions-trades-info-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-conditions-trades-info {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;
    white-space: nowrap;
    text-wrap: nowrap;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
        .text-edit-cls {
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          white-space: nowrap;
          text-wrap: nowrap;
          color: var(--color--brand--500);
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;

      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--cancel {
            color: var(--color--danger--600);
            border: 1px solid var(--color--danger--600);
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }

        &.not-accept-save-cls {
          ::ng-deep {
            #box-id--save {
              opacity: 0.5;
            }
          }
        }
      }
    }
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        .table-container {
          position: relative;
        }

        .type-status {
          .box-show {
            padding: 4px 0px;
            border-radius: 16px;
            justify-content: center;
            background-color: var(--color--accents--green-dark);
            display: inline-flex;
            padding: 0px 12px;
            text-wrap: nowrap;
            white-space: nowrap;
            text-transform: uppercase;
            margin: 0 auto;
            max-width: 100px;

            .input-table-view {
              text-align: center;
              text-transform: uppercase;
            }

            .isEditMode {
              border-color: transparent !important;
              text-align: unset;
            }
          }

          &.sell-cls {
            .box-show {
              background-color: var(--color--accents--red-dark);
            }
          }
        }

        .type-status-trades-match {
          .box-show {
            padding: 4px 0px;
            border-radius: 16px;
            justify-content: center;
            background-color: var(--color--neutral--100);
            display: inline-flex;
            padding: 0px 12px;
            white-space: nowrap;
            text-wrap: nowrap;
            text-transform: uppercase;
            margin: 0 auto;
            max-width: 180px;

            .input-table-view {
              text-align: center;
              text-transform: uppercase;
            }

            .isEditMode {
              border-color: transparent !important;
              text-align: unset;
            }
          }

          &.not-match-cls {
            .box-show {
              background-color: var(--color--accents--red);
            }
          }

          &.half-match-cls {
            .box-show {
              background-color: var(--color--accents--yellow);
            }
          }
          &.isActive-cls {
            .box-show {
              background-color: var(--color--accents--green);
            }
          }
        }
      }
    }
  }

  .type-account-cls {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 16px;
    background-color: #ffd60a;

    &.organize-cls {
      background-color: #32d74b;
    }
  }

  .file-extention-cls {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #5ac8f5;
  }
}

.calendar-template-info-cls {
  border-radius: 6px;
}

.context-menu-container-cls {
  display: flex;
  flex-direction: column;
  .box-btn {
    padding: 10px 2px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
  }
}
