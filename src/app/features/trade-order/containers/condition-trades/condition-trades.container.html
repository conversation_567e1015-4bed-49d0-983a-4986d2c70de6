<div class="conditions-trades-info-container">
  <div class="header-conditions-trades-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-197' | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          0 {{'MES-170' | translate}}
        </div>
        <div *ngIf="onEditMode" class="text-edit-cls">{{'MES-156' | translate}}: {{getCountDataEdited()}}</div>
      </div>
    </div>
    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [class.not-accept-save-cls]="checkEnableSaveBtn()"
        [ngClass]="{
          'filter-mode-cls' : isFilter,

        }"
      ></app-action-btn>
    </div>
  </div>
  <div class="table-view-container">
    <sha-grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); openDetailOpenTrades($event.element)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
      (clickMoreAction)="openMoreAction($event)"
      (contextMenu)="contextMenuClick($event)"
    >
    </sha-grid>
  </div>
</div>

<ng-template #contextMenuRef let-option="option">
  <div class="context-menu-container-cls">
    <div class="box-btn">
      <img [src]="option.url" [alt]="option.tag" />
      <div class="typo-body-12">{{ option.label | translate}}</div>
    </div>
  </div>
</ng-template>
