/* eslint-disable complexity */
import { <PERSON>mpo<PERSON>, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { DestroyService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import {
  CONVERT_TYPE_TRADES_ORDER_TO_LABLE,
  CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE,
  EStatusTrades,
  EStatusTradesOrder,
} from '../../constants/trade-order';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { TradesOrderFilterComponent } from '../../components/open-trades-filter/trades-order-filter.component';
import {
  IFilterOpenTradesParam,
  IListOptionConfig,
  IOptionConfigFilter,
  IOptionSelection,
  IRangeFilt<PERSON>,
} from '../../models/trade-order';
import {
  accountNumberOptions,
  codeCKOptions,
  orderChannelOptions,
  statusTradesOpenTradesOption,
  tradersOptions,
  typeTradesDropdownOption,
} from '../../constants/fakeDataList';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { GridContextMenu } from '@shared/models';
import { Store } from '@ngrx/store';
import { selectFilterConditionTrade$, selectSearchValue$ } from '../../stores/trade-order.selections';
import { take, takeUntil } from 'rxjs';
import { resetFilterConditionTrades, setFilterConditionTrades } from '../../stores/trade-order.actions';
import { dateToYMD } from '../../../../shared/utils/date';
import { OpenTradesDetailComponent } from '../../components/open-trades-detail/open-trades-detail.component';

/**
 * ConditionTradesContainer
 */
@Component({
  selector: 'app-condition-trades-container',
  templateUrl: './condition-trades.container.html',
  styleUrls: ['./condition-trades.container.scss'],
})
export class ConditionTradesContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('contextMenuRef', { static: true }) contextMenuRef: TemplateRef<any> | null = null;
  dataFilter!: IFilterOpenTradesParam;
  fakeData: any = [
    // {
    //   dateTrades: '15/04/2024',
    //   timeTrades: '13:15:00',
    //   accountNumber: '069C-125485',
    //   customerName: 'Phạm Thị Thu Trang',
    //   status: 1,
    //   typeTrades: 'STOP LO',
    //   code: 'SHS : HNX',
    //   activationPrice: 19,
    //   orderPrice: 15,
    //   orderVolume: 100000,
    //   tradeValue: **********,
    //   revenueFee: 3400000,
    //   netTransactionFee: 1200000,
    //   brokerCommission: 1300000,
    //   tradeStatus: 2,
    //   activationDate: null,
    //   trader: 'duongnt',
    //   orderChannel: 'SHTRADING',
    // },
    // {
    //   dateTrades: '14/04/2024',
    //   timeTrades: '13:28:00',
    //   accountNumber: '069C-586547',
    //   customerName: 'Đặng Hoàng An Nhiên',
    //   status: 1,
    //   typeTrades: 'STOP MP',
    //   code: 'TCB : HSX',
    //   activationPrice: 18,
    //   orderPrice: 'STOP MP',
    //   orderVolume: 100000,
    //   tradeValue: **********,
    //   revenueFee: 3400000,
    //   netTransactionFee: 4500000,
    //   brokerCommission: 4500000,
    //   tradeStatus: 2,
    //   activationDate: null,
    //   trader: 'trangptt',
    //   orderChannel: 'SHTRADING',
    // },
    // {
    //   dateTrades: '13/04/2024',
    //   timeTrades: '08:15:00',
    //   accountNumber: '069C-883962',
    //   customerName: 'Bùi Thị Hạnh',
    //   status: 0,
    //   typeTrades: 'STOP LO',
    //   code: 'VCB : HSX',
    //   activationPrice: 19,
    //   orderPrice: 18,
    //   orderVolume: 320000,
    //   tradeValue: **********,
    //   revenueFee: 4500000,
    //   netTransactionFee: 1400000,
    //   brokerCommission: 2300000,
    //   tradeStatus: 2,
    //   activationDate: null,
    //   trader: 'datmt',
    //   orderChannel: 'SHADVISOR',
    // },
    // {
    //   dateTrades: '12/04/2024',
    //   timeTrades: '17:36:00',
    //   accountNumber: '069C-316087',
    //   customerName: 'Công ty TNHH Mica Group',
    //   status: 0,
    //   typeTrades: 'STOP MP',
    //   code: 'TCB : HSX',
    //   activationPrice: 20,
    //   orderPrice: 'STOP MP',
    //   orderVolume: 45000,
    //   tradeValue: *********,
    //   revenueFee: 4200000,
    //   netTransactionFee: 6400000,
    //   brokerCommission: 8400000,
    //   tradeStatus: 2,
    //   activationDate: null,
    //   trader: 'chienvm',
    //   orderChannel: 'SHPRO',
    // },
    // {
    //   dateTrades: '11/04/2024',
    //   timeTrades: '16:35:00',
    //   accountNumber: '069C-637085',
    //   customerName: 'Công ty TNHH Tigon 68',
    //   status: 0,
    //   typeTrades: 'STOP LO',
    //   code: 'SHA : HSX',
    //   activationPrice: 15,
    //   orderPrice: 17,
    //   orderVolume: 66000,
    //   tradeValue: **********,
    //   revenueFee: 3600000,
    //   netTransactionFee: 4700000,
    //   brokerCommission: 8500000,
    //   tradeStatus: 2,
    //   activationDate: null,
    //   trader: 'quangtd',
    //   orderChannel: 'SHADVISOR',
    // },
    // {
    //   dateTrades: '10/04/2024',
    //   timeTrades: '14:15:00',
    //   accountNumber: '069C-862656',
    //   customerName: 'Công ty TNHH du lịch Cá Voi Xanh',
    //   status: 1,
    //   typeTrades: 'STOP LO',
    //   code: 'TCB : HSX',
    //   activationPrice: 19,
    //   orderPrice: 18,
    //   orderVolume: 31000,
    //   tradeValue: *********,
    //   revenueFee: 4700000,
    //   netTransactionFee: 7800000,
    //   brokerCommission: 1600000,
    //   tradeStatus: 3,
    //   activationDate: '19/04/2024',
    //   trader: 'haln',
    //   orderChannel: 'SHWEB',
    // },
  ];

  searchValue: string = '';

  /**
   * Constructor
   * @param popoverService popoverService
   *  @param store store
   * @param _destroy _destroy
   */
  constructor(
    private readonly popoverService: PopoverService,
    private readonly store: Store,
    private readonly _destroy: DestroyService
  ) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.display, ActionButton.filter]);
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    const dataClone = [...this.fakeData];
    this.data = dataClone.map((d) => ({
      ...d,
      date: d.timeTrades + ' - ' + d.dateTrades,
    }));
    this.initialData = structuredClone(this.data);
    this.columnConfigs = [
      // Ngày đặt lệnh
      {
        resizable: true,
        name: 'MES-171',
        minWidth: 200,
        width: 200,
        tag: 'date',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
      },
      // Số tài khoản
      {
        resizable: true,
        name: 'MES-66',
        minWidth: 146,
        width: 146,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        sticky: true,
        disable: true,
      },
      // Tên khách hàng
      {
        resizable: true,
        name: 'MES-184',
        minWidth: 30,
        width: 290,
        tag: 'customerName',
        isDisplay: true,
      },
      // Số hiệu lệnh gốc
      {
        resizable: true,
        name: 'MES-604',
        minWidth: 30,
        width: 150,
        tag: 'null',
        isDisplay: true,
        align: 'center',
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
      },
      // Số hiệu lệnh
      {
        resizable: true,
        name: 'MES-597',
        minWidth: 30,
        width: 100,
        tag: 'ordNo',
        isDisplay: true,
        align: 'center',
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
      },
      // Lệnh
      {
        resizable: true,
        name: 'MES-172',
        minWidth: 30,
        width: 100,
        tag: 'status',
        isDisplay: true,
        align: 'center',
        displayValueFn: (value) => CONVERT_TYPE_TRADES_ORDER_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === EStatusTradesOrder.SELL) {
            return 'type-status sell-cls';
          } else return 'type-status';
        },
      },
      // Loại lệnh
      {
        resizable: true,
        name: 'MES-173',
        minWidth: 30,
        width: 130,
        tag: 'typeTrades',
        isDisplay: true,
      },
      // Mã CK
      {
        resizable: true,
        name: 'MES-174',
        minWidth: 30,
        width: 130,
        tag: 'code',
        isDisplay: true,
      },
      // Giá kích hoạt
      {
        resizable: true,
        name: 'MES-175',
        minWidth: 30,
        width: 130,
        tag: 'activationPrice',
        align: 'end',
        isDisplay: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return (+v).toFixed(2).toString();
        },
      },
      // Giá đặt
      {
        resizable: true,
        name: 'MES-176',
        minWidth: 30,
        width: 100,
        tag: 'orderPrice',
        align: 'end',
        isDisplay: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          if (typeof v === 'string') return v;
          return customNumberFormat(v, 'decimal', 'en-US', 2);
        },
      },
      // KL đặt
      {
        resizable: true,
        name: 'MES-179',
        minWidth: 30,
        width: 130,
        tag: 'orderVolume',
        align: 'end',
        isDisplay: true,
        panelClass: 'location-cls',
        displayValueFn: (v) => {
          if (!v) return '-';

          return customNumberFormat(v, 'decimal', 'en-US');
        },
      },
      // GTGD
      {
        resizable: true,
        name: 'MES-180',
        minWidth: 30,
        width: 140,
        tag: 'tradeValue',
        isDisplay: true,
        align: 'end',
        displayValueFn: (v) => {
          if (!v) {
            return '-';
          } else if (+v) {
            return customNumberFormat(v, 'decimal', 'en-US');
          } else return v;
        },
      },
      // Tổng phí
      {
        resizable: true,
        name: 'MES-581',
        minWidth: 30,
        width: 155,
        align: 'end',
        tag: 'revenueFee',
        isDisplay: true,
        displayValueFn: (v) => {
          if (!v) return '-';

          return customNumberFormat(v, 'decimal', 'en-US');
        },
      },
      // Net phí giao dịch
      {
        resizable: true,
        name: 'MES-195',
        minWidth: 30,
        width: 140,
        tag: 'netTransactionFee',
        align: 'end',
        isDisplay: true,
        displayValueFn: (v) => {
          if (!v) {
            return '-';
          } else if (+v) {
            return customNumberFormat(v, 'decimal', 'en-US');
          } else return v;
        },
      },
      // Hoa Hồng MG
      // {
      //   name: 'MES-196',
      //   minWidth: 30,
      //   width: 140,
      //   tag: 'brokerCommission',
      //   align: 'end',
      //   isDisplay: true,
      //   resizable: true,
      //   displayValueFn: (v) => {
      //     if (!v) return '-';

      //     return customNumberFormat(v, 'decimal', 'en-US');
      //   },
      // },
      // Trạng thái lệnh
      {
        resizable: true,
        name: 'MES-181',
        minWidth: 30,
        width: 180,
        tag: 'tradeStatus',
        isDisplay: true,
        align: 'center',
        displayValueFn: (value) => CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === EStatusTrades.NOT_MATCH) {
            return 'type-status-trades-match not-match-cls';
          } else if (value === EStatusTrades.PARTIAL_MATCH) {
            return 'type-status-trades-match half-match-cls';
          } else if (value === EStatusTrades.ACTIVATED) {
            return 'type-status-trades-match isActive-cls';
          } else return 'type-status-trades-match';
        },
      },
      // Ngày kích hoạt
      {
        resizable: true,
        name: 'MES-198',
        minWidth: 30,
        width: 140,
        tag: 'activationDate',
        isDisplay: true,
        displayValueFn: (v) => {
          if (!v) return '-';

          return v;
        },
      },
      // Người đặt
      {
        resizable: true,
        name: 'MES-182',
        minWidth: 30,
        width: 90,
        tag: 'trader',
        isDisplay: true,
      },
      // Kênh đặt
      {
        resizable: true,
        name: 'MES-183',
        minWidth: 30,
        width: 140,
        tag: 'orderChannel',
        isDisplay: true,
      },
    ];

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchValue = value ?? '';
        this.searchData();
      });

    this.store
      .select(selectFilterConditionTrade$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        if (!value) return;
        this.dataFilter = { ...value };
        this.isFilter = value.isFilter;
      });
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterConditionTrades());
  }

  /**
   * searchData
   */
  searchData() {
    let searchData = [];
    if (this.searchValue) {
      const searchValue = this.searchValue.toString().toLowerCase();
      searchData = this.isFilter
        ? this.filteredData.filter((item) => this.containsSearchValue(item, searchValue))
        : this.initialData.filter((item) => this.containsSearchValue(item, searchValue));

      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchData = this.isFilter ? [...this.filteredData] : [...this.initialData];

      this.isSearch = false; // Set isSearch to false when not searching
    }
    this.searchedData = searchData;
    this.data = [...searchData];
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    return (
      item.accountNumber?.toString().toLowerCase().includes(searchValue) ??
      item.customerName?.toString().toLowerCase().includes(searchValue) ??
      item.dateTrades?.toString().toLowerCase().includes(searchValue) ??
      item.activationDate?.toString().toLowerCase().includes(searchValue)
    );
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    if (tag === 'filter') {
      const { codeCKOptionsClone, numberAccountOptionsClone, traderOptionsClone, orderChannelClone } =
        this.updateListForFilter();
      const ref = this.openFilter(TradesOrderFilterComponent, {
        width: '800px',
        data: {
          config: {
            code: true,
            status: true,
            typeTrades: 'checkbox',
            statusTrades: 'checkbox',
            numberAccount: true,
            dateOrderTrades: {
              position: 'left',
              show: true,
            },
            dateActivation: true,
            trader: 'right',
            orderChannel: 'right',
            tradesValue: true,
            revenue: true,
            netTradesFee: true,
            brokerageCommission: true,
          } as IOptionConfigFilter,
          listOption: {
            codeOptions: codeCKOptionsClone,
            numberAccountOptions: numberAccountOptionsClone,
            traderOptions: traderOptionsClone,
            orderChannel: orderChannelClone,
          } as IListOptionConfig,
          optionFilter: this.dataFilter,
        },
      });

      ref
        .afterClosed()
        .pipe(take(1))
        .subscribe((v) => {
          if (!v) return;
          this.applyFilter(v);
        });
    }
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;
    if (type === 'save') {
      this.store.dispatch(setFilterConditionTrades({ params: optionFilter }));

      const newListFilter = this.isSearch
        ? this.saveFunc(this.searchedData, optionFilter)
        : this.saveFunc(this.initialData, optionFilter);
      this.data = newListFilter;
      this.filteredData = newListFilter;
    } else if (type === 'default') {
      this.store.dispatch(resetFilterConditionTrades());
      this.isSearch
        ? this.store
            .select(selectSearchValue$)
            .pipe(takeUntil(this._destroy))
            .subscribe((value) => {
              this.searchValue = value ?? '';
              this.searchData();
            })
        : (() => {
            this.data = this.initialData;
          })();
    }
  }

  /**
   * @param data
   * @param optionFilter
   */
  saveFunc(data: any[], optionFilter: any) {
    const { buySellStatus, optionSelection, activeStatus, isStopTypeTrades } = optionFilter;
    const newListFilter = data.filter((init) => {
      const isBuySellStatus = buySellStatus.length ? buySellStatus.includes(init.status) : true;
      const isInActiveStatus = activeStatus.length ? activeStatus.includes(init.tradeStatus) : true;
      const isTypeTradeMatch = isStopTypeTrades.length ? isStopTypeTrades.includes(init.typeTrades) : true;
      return (
        isBuySellStatus &&
        isInActiveStatus &&
        isTypeTradeMatch &&
        this.updateOptionSelection(optionSelection, init) &&
        this.updateRangeFilter(optionFilter, init)
      );
    });
    return newListFilter;
  }

  /**
   *
   */
  updateListForFilter() {
    const { optionSelection } = this.dataFilter;
    const {
      codeOptionsSelect = [],
      typeTradesOptionsSelect = [],
      numberAccountOptionsSelect = [],
      traderOptionsSelect = [],
      orderChannelOptionSelect = [],
      statusTradesOptionsSelect = [],
    } = optionSelection;

    const isSelect = (value: string, data: string[] | null) => data === null || data.includes(value);

    const codeCKOptionsClone = codeCKOptions.map((code) => ({
      label: code.label,
      isSelect: isSelect(code.label, codeOptionsSelect),
    }));

    const typeTradesOptionsClone = typeTradesDropdownOption.map((code) => ({
      label: code.label,
      isSelect: isSelect(code.label, typeTradesOptionsSelect),
    }));

    const statusTradesOptionsClone = statusTradesOpenTradesOption.map((code) => ({
      label: code.label,
      isSelect: isSelect(code.label, statusTradesOptionsSelect),
    }));

    const numberAccountOptionsClone = accountNumberOptions.map((code) => ({
      label: code.label,
      isSelect: isSelect(code.label, numberAccountOptionsSelect),
    }));

    const traderOptionsClone = tradersOptions.map((code) => ({
      label: code.label,
      isSelect: isSelect(code.label, traderOptionsSelect),
    }));

    const orderChannelClone = orderChannelOptions.map((code) => ({
      label: code.label,
      isSelect: isSelect(code.label, orderChannelOptionSelect),
    }));

    return {
      codeCKOptionsClone,
      typeTradesOptionsClone,
      statusTradesOptionsClone,
      numberAccountOptionsClone,
      traderOptionsClone,
      orderChannelClone,
    };
  }

  /**
   * ContextMenuClick
   * @param {GridContextMenu<any>} param0 { event, element, source }
   */
  contextMenuClick({ event, element, source }: GridContextMenu<any>) {
    event.preventDefault();

    const origin = event.target as HTMLElement;
    this.openSelectOption(origin, element);
  }

  /**
   * UpdateRangeFilter
   * @param optionFilter
   * @param init
   * @returns {any} new list
   */
  updateRangeFilter(optionFilter: IFilterOpenTradesParam, init: any) {
    const { netTradesFee, revenue, valueTrades, brokerageCommission, dateActiveRange } = optionFilter;

    const getValueMatch = (value: IRangeFilter, initField: number) => {
      if (value?.start && value?.end) {
        return initField >= +value.start && initField <= +value.end;
      } else if (value?.start) {
        return initField >= +value.start;
      } else if (value?.end) {
        return initField <= +value.end;
      } else {
        return true;
      }
    };

    const isValueTradesMatch = getValueMatch(valueTrades, +init.tradeValue);

    const isBrokerCommissionMatch = getValueMatch(brokerageCommission, +init.brokerageCommission);

    const isRevenueMatch = getValueMatch(revenue, +init.total);

    const isNetTradesFee = getValueMatch(netTradesFee, +init.netTransactionFee);

    const dateActive = init.activationDate
      ? new Date(init.activationDate.split('/').reverse().join('-'))
      : new Date('');
    const startDate = new Date(dateActiveRange.start ? dateToYMD(dateActiveRange.start) : '');
    const endDate = new Date(dateActiveRange.end ? dateToYMD(dateActiveRange.end) : '');

    const compareDateActiveRage =
      (dateActiveRange.start ? dateActive > startDate : true) && (dateActiveRange.end ? dateActive < endDate : true);
    const isDataActiveInRange = dateActive ? compareDateActiveRage : true;

    return isValueTradesMatch && isBrokerCommissionMatch && isRevenueMatch && isNetTradesFee && isDataActiveInRange;
  }

  /**
   * OpenSelectOption
   * @param {HTMLElement} origin  origin
   * @param {any} element element
   */
  openSelectOption(origin: HTMLElement, element: any) {
    const options = [
      {
        url: './assets/icons/arrow-2-direction.svg',
        label: 'MES-211',
        tag: 'detail',
      },
      {
        url: './assets/icons/edit-2.svg',
        label: 'MES-212',
        tag: 'edit',
        classCustom: element.tradeStatus === 2 ? '' : 'disable',
      },
      {
        url: './assets/icons/x-cross-red.svg',
        label: 'MES-213',
        tag: 'cancel',
        classCustom: element.tradeStatus === 2 ? 'color-red-text' : 'color-red-text disable',
      },
    ];

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 3,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        templateRefExp: this.contextMenuRef,
        searchKey: 'tag',
        options,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe({
      next: (v) => {
        const { item } = v.data;
        if (!item.length) return;
        const { tag } = item[0];
        switch (tag) {
          case 'detail':
            this.openDetailOpenTrades(element);
            break;

          default:
            break;
        }
      },
    });
  }

  /**
   * OpenMoreAction
   * @param {any} event
   */
  openMoreAction(event: any) {
    const { html, element } = event;
    this.openSelectOption(html, element);
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.export]);
  }

  /**
   * UpdateOptionSelection
   * @param optionSelection
   * @param init
   * @returns  {any} new list
   */
  updateOptionSelection(optionSelection: IOptionSelection, init: any) {
    const {
      codeOptionsSelect,
      typeTradesOptionsSelect,
      numberAccountOptionsSelect,
      traderOptionsSelect,
      orderChannelOptionSelect,
    } = optionSelection;

    const isCodeMatchConditionTrades = (codeOptionsSelect ?? []).length
      ? (codeOptionsSelect ?? []).includes(init.code)
      : true;
    const isTypeTradeMatchConditionTrades = (typeTradesOptionsSelect ?? []).length
      ? (typeTradesOptionsSelect ?? []).includes(init.typeTrades)
      : true;

    const isNumberAccountMatchConditionTrades = (numberAccountOptionsSelect ?? []).length
      ? (numberAccountOptionsSelect ?? []).some((item) => item.includes(init.accountNumber))
      : true;

    const isTraderMatchConditionTrades = (traderOptionsSelect ?? []).length
      ? (traderOptionsSelect ?? []).includes(init.trader)
      : true;

    const isOrderChanelMatchConditionTrades = (orderChannelOptionSelect ?? []).length
      ? (orderChannelOptionSelect ?? []).includes(init.orderChannel)
      : true;

    return (
      isCodeMatchConditionTrades &&
      isTypeTradeMatchConditionTrades &&
      isNumberAccountMatchConditionTrades &&
      isTraderMatchConditionTrades &&
      isOrderChanelMatchConditionTrades
    );
  }

  /**
   * OpenDetailOpenTrades
   * @param {any} element element
   */
  openDetailOpenTrades(element: any) {
    const isActive = element.tradeStatus === EStatusTrades.ACTIVATED;
    this.dialogService.openRightDialog(OpenTradesDetailComponent, {
      width: '530px',
      data: {
        trade: element,
        isHideMatchVolumne: true,
        isHideMatchPrice: true,
        isHideCancelTradeOrderBtn: !isActive ? false : true,
        isHideEditTradeOrderBtn: !isActive ? false : true,
      },
    });
  }
}
