import { Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { DestroyService, I18nService, LoadingService, MessageService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import {
  CONVERT_ORDER_CHANNEL,
  CONVERT_STATUS_TRADED_ORDER_TO_VALUE,
  CONVERT_TYPE_TRADES_ORDER_TO_LABLE,
  CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE,
  EStatusTrades,
  EStatusTradeFilter,
  EStatusTradesOrder,
  ETagPageOrder,
  TOTAL_ITEM_IN_LIST,
} from '../../constants/trade-order';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { combineLatest, filter, map, Subject, take, takeUntil, withLatestFrom } from 'rxjs';
import { OpenTradesDetailComponent } from '../../components/open-trades-detail/open-trades-detail.component';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { TradesOrderFilterComponent } from '../../components/open-trades-filter/trades-order-filter.component';
import {
  CONVERT_TYPE_TRADES,
  EOrderType,
  ICustomerInfos,
  IEventSocketTradeOrder,
  IFilterOpenTradesParam,
  IListOptionConfig,
  IOptionConfigFilter,
  IOptionSelection,
  IOptionStock,
  IOrderListResponse,
  IRangeFilter,
  IStockItemInList,
} from '../../models/trade-order';
import {
  orderChannelOptions,
  statusTradesOpenTradesOption,
  typeTradesDropdownOption,
} from '../../constants/fakeDataList';
import { GridContextMenu } from '@shared/models';
import { Store } from '@ngrx/store';
import {
  selectDateOpenTradesOrder$,
  selectFilterOpenTrades$,
  selectListTodayOrder$,
  selectSearchValue$,
} from '../../stores/trade-order.selections';
import {
  getListTodayOrder,
  resetFilterOpenTrades,
  resetSearchValueListOrder,
  setFilterOpenTrades,
  updateDateInOpenTradesOrder,
} from '../../stores/trade-order.actions';
import { OrderStatusEventService } from 'src/app/shared/services/order-status.service';
import { SocketService } from 'src/app/shared/services/realtime/socket.service';
import { IDateChangeItem } from 'src/app/shared/components/date-picker/date-picker/date-picker.component';
import { ConvertToDate, dateToDMY, getPreviousFriday } from 'src/app/shared/utils/date';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectAllStockList$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { getCurrentBrokerView, getListAccountNumberAndLevelByBrokerView } from 'src/app/stores/shared/shared.actions';
import { IAllAccountNumber, IAllLevelOfBroker, IAllStockList, IOptionSelect } from 'src/app/shared/models/global';
import { initialTradesOrderState } from '../../stores/trade-order.reducers';
import { HttpClient } from '@angular/common/http';
import { deepClone, updateBorkerName } from 'src/app/shared/utils/utils';
import { Router } from '@angular/router';
import { HashService } from 'src/app/shared/services/hash.service';
import { SharedService } from 'src/app/shared/services/shared.service';
import { OpenTradeStore } from './open-trades.store';
import { BODY_MESSAGE, DESTINATION_MESSAGE, ECodeEventOrder, TOPIC } from 'src/app/shared/constants/trading';
import { SockJSClientService } from 'src/app/shared/services/realtime/sockjs-client.service';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { removeVietnameseTones } from 'src/app/shared/utils/text';
import { PlaceOrderPopupComponent } from '../../components/place-order-popup/place-order-popup.component';

/**
 * OpenTradesContainer
 */
@Component({
  selector: 'app-open-trades-container',
  templateUrl: './open-trades.container.html',
  styleUrls: ['./open-trades.container.scss'],
  providers: [DestroyService],
})
export class OpenTradesContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('contextMenuRef', { static: true }) contextMenuRef: TemplateRef<any> | null = null;
  @ViewChild('grid', { static: true }) gridRef!: GridComponent<any>;

  searchValue: string = '';

  disableLoadingBtn = false;

  private readonly destroyTradeOder$ = new Subject<void>();

  dataFilter!: IFilterOpenTradesParam;

  isOutPage = false;

  currentIndex = 1;

  currentDate = new Date();

  isOpenCalendar = false;

  limitDateAccept = new Date();

  brokerInfo!: {
    label: string;
    value: string;
  } | null;

  LIST_MG: IListOptions[] = [];

  allCustomerInfo!: ICustomerInfos[];

  stockCodeList!: IAllStockList[];

  customNumberFormat = customNumberFormat;

  messageOrder = '';

  allTradesOrderItems: any = [];

  currentIndexItem = 0;

  isScrolling = false;

  currentBrokerCode = '';

  /**
   * Constructor
   * @param popoverService popoverService
   * @param store store
   * @param _destroy _destroy
   */
  constructor(
    private readonly popoverService: PopoverService,
    private readonly store: Store,
    private readonly _destroy: DestroyService,
    private readonly orderStatusEventService: OrderStatusEventService,
    private readonly socketService: SocketService,
    private readonly loadingService: LoadingService,
    private readonly http: HttpClient,
    private readonly router: Router,
    private readonly hashService: HashService,
    private readonly shareService: SharedService,
    private readonly openTradeStore: OpenTradeStore,
    private readonly socketJs: SockJSClientService,
    private readonly i18: I18nService,
    private readonly messageService: MessageService
  ) {
    super();
    this.getBrokerInfo();
    this.toggleButtonByTags([ActionButton.loading, ActionButton.display, ActionButton.filter]);
    this.socketService.createConnectionTradeOrder('open-trade-Order');
    this.checkIsTodayAndIsDayOff();
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.columnConfigs = [
      // Ngày đặt lệnh
      {
        tag: 'date',
        name: 'MES-171',
        minWidth: 200,
        width: 200,
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
      },
      // Số tài khoản
      {
        tag: 'accountNumber',
        name: 'MES-66',
        minWidth: 146,
        width: 146,
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
      },
      // Tên khách hàng
      {
        tag: 'customerName',
        name: 'MES-184',
        minWidth: 30,
        width: 200,
        isDisplay: true,
        resizable: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
      },
      // Số hiệu lệnh gốc
      {
        tag: 'fstNo',
        name: 'MES-604',
        minWidth: 30,
        width: 150,
        isDisplay: true,
        align: 'center',
        resizable: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
      },
      // Số hiệu lệnh
      {
        tag: 'ordNo',
        name: 'MES-597',
        minWidth: 30,
        width: 100,
        isDisplay: true,
        align: 'center',
        resizable: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
      },
      // Lệnh
      {
        tag: 'status',
        name: 'MES-172',
        minWidth: 30,
        width: 100,
        isDisplay: true,
        resizable: true,
        align: 'center',
        displayValueFn: (value) => CONVERT_TYPE_TRADES_ORDER_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === EStatusTradesOrder.SELL) {
            return 'type-status sell-cls';
          } else return 'type-status';
        },
      },
      // Loại lệnh
      {
        tag: 'typeTrades',
        name: 'MES-173',
        minWidth: 30,
        width: 130,
        isDisplay: true,
        resizable: true,
      },
      // Mã CK
      {
        tag: 'code',
        name: 'MES-174',
        minWidth: 30,
        width: 130,
        isText: true,
        isDisplay: true,
        resizable: true,
      },
      // Giá kích hoạt
      // {
      //   name: 'MES-175',
      //   minWidth: 30,
      //   width: 130,
      //   tag: 'activationPrice',
      //   align: 'end',
      //   isDisplay: true,
      //   resizable: true,
      //   displayValueFn: (v) => {
      //     if (!v) return '-';
      //     return (+v).toFixed(2).toString();
      //     // return customNumberFormat(v, 'decimal', 'en-US', 2);
      //   },
      // },
      // Giá đặt
      {
        tag: 'orderPrice',
        name: 'MES-176',
        minWidth: 30,
        width: 100,
        align: 'end',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          if (typeof v === 'string') return v;
          return (+v).toFixed(2).toString();
        },
      },
      // Giá khớp
      {
        tag: 'matchedPrice',
        name: 'MES-177',
        minWidth: 30,
        width: 130,
        align: 'end',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v || v === 0) return '-';
          return (+v).toFixed(2).toString();
        },
      },
      // KL khớp
      {
        tag: 'matchedVolume',
        name: 'MES-178',
        minWidth: 30,
        width: 130,
        align: 'end',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v || v === 0) return '-';
          return customNumberFormat(v);
        },
      },
      // KL đặt
      {
        tag: 'orderVolume',
        name: 'MES-179',
        minWidth: 30,
        width: 130,
        align: 'end',
        isDisplay: true,
        resizable: true,
        panelClass: 'location-cls',
        displayValueFn: (v) => {
          if (v === 0) return '0';
          if (!v) return '-';

          return customNumberFormat(v);
        },
      },
      // GTGD
      {
        tag: 'tradeValue',
        name: 'MES-180',
        minWidth: 30,
        width: 140,
        isDisplay: true,
        resizable: true,
        align: 'end',
        displayValueFn: (v) => {
          if (v === 0) return '0';
          if (!v) {
            return '-';
          } else if (+v) {
            return customNumberFormat(v);
          } else return v;
        },
      },
      // Trạng thái lệnh
      {
        tag: 'tradeStatus',
        name: 'MES-181',
        minWidth: 30,
        width: 180,
        isDisplay: true,
        resizable: true,
        align: 'center',
        displayValueFn: (value) => CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === EStatusTrades.NOT_MATCH) {
            return 'type-status-trades-match not-match-cls';
          } else if (value === EStatusTrades.PARTIAL_MATCH) {
            return 'type-status-trades-match partial-match-cls';
          } else if (value === EStatusTrades.NOT_ACTIVATED) {
            return 'type-status-trades-match not-activated-cls';
          } else if (value === EStatusTrades.CANCELED) {
            return 'type-status-trades-match canceled-cls';
          } else return 'type-status-trades-match rejected-cls';
        },
      },
      // Người đặt
      {
        tag: 'trader',
        name: 'MES-182',
        minWidth: 30,
        width: 90,
        isDisplay: true,
        resizable: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
      },
      // Kênh đặt
      {
        tag: 'orderChannel',
        name: 'MES-183',
        minWidth: 30,
        width: 140,
        isDisplay: true,
        resizable: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
      },
    ];

    this.getListOpenTradesOrder();
    this.store
      .select(selectListTodayOrder$)
      .pipe(takeUntil(this._destroy), withLatestFrom(this.store.select(selectSearchValue$)))
      .subscribe(([openTrades, searchValue]) => {
        this.isSearch = !!searchValue;
        if (this.isOutPage || this.isSearch) return;
        this.data = [];
        this.initialData = [];
        this.allTradesOrderItems = [];
        this.currentIndexItem = 0;
        this.gridRef?.scrollTopEvent();
        this.allTradesOrderItems = openTrades.map((d) => {
          return {
            ...d,
            orderPrice: this.getOrderPrice(d.orderPrice, d.typeTrades),
            matchedPrice: d.matchedPrice / 1000,
            date: d.timeTrades + ' - ' + d.dateTrades,
            accountNumber: d.accountNumber + ' - ' + d.subAccount,
            tradeValue: this.getTradesValue(d.mthAmt, CONVERT_TYPE_TRADES[d.typeTrades], d.tradeStatus),
            customerName: this.allCustomerInfo.find((cus) => cus.accountNo === d.accountNumber)?.customerName,
            typeTrades: CONVERT_TYPE_TRADES[d.typeTrades],
            orderChannel: CONVERT_ORDER_CHANNEL[d.orderChannel],
            tradeStatus: d.tradeStatus === 0 && d.nmthQty === 0 ? EStatusTrades.CANCELED : d.tradeStatus,
          };
        });

        this.sliceDataItems();

        this.checkHeightTableOpenTrades();
      });

    this.store
      .select(selectFilterOpenTrades$)
      .pipe(takeUntil(this._destroy))
      .subscribe((v) => {
        if (!v) return;
        this.dataFilter = { ...v };
        this.isFilter = v.isFilter;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchValue = value;
        this.searchOpenTradeData();
      });

    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((customer) => {
        if (!customer.length) return;
      });
  }

  sliceDataItems() {
    if (this.data.length) this.currentIndexItem = this.data.length;
    const end = this.currentIndexItem + TOTAL_ITEM_IN_LIST;

    const listSlice = this.isSearch
      ? this.searchedData.slice(this.currentIndexItem, end)
      : this.allTradesOrderItems.slice(this.currentIndexItem, end);
    this.currentIndexItem = end;

    if (this.currentIndexItem > this.allTradesOrderItems.length) {
      this.currentIndexItem = this.allTradesOrderItems.length;
    }
    const item = { ...listSlice[listSlice.length - 1] };
    if (!item.isShowLoading && this.currentIndexItem !== this.allTradesOrderItems.length) {
      item.isShowLoading = true;
      listSlice[listSlice.length - 1] = item;
    }

    const dataClone = [...this.data];
    dataClone.forEach((t) => (t.isShowLoading = false));

    this.data = [...dataClone, ...listSlice];
    this.initialData = structuredClone(this.data);
  }

  scrollData(event: Event) {
    const contentList = event.target as Element;
    if (Math.ceil(contentList.scrollTop + contentList.clientHeight) < contentList.scrollHeight - 42) {
      return;
    }

    if (this.currentIndexItem === this.allTradesOrderItems.length) return;

    if (!this.isScrolling) {
      this.isScrolling = true;
      this.sliceDataItems();
      this.isScrolling = false;
    }
  }

  checkHeightTableOpenTrades() {
    setTimeout(() => {
      const tableContainerOpenTrade = document.querySelector('.table-container') as HTMLElement;
      const gridElementsOpenTrades = document.querySelector('.mat-mdc-table') as HTMLElement;

      const tableContainerHeight = tableContainerOpenTrade.offsetHeight;
      const gridElementsHeightOpenTrade = gridElementsOpenTrades.offsetHeight;

      if (
        gridElementsHeightOpenTrade < tableContainerHeight &&
        this.data.length &&
        this.allTradesOrderItems.length > this.data.length
      ) {
        const missingOpenTradeItems = Math.ceil((tableContainerHeight - gridElementsHeightOpenTrade) / 36);
        this.currentIndexItem += missingOpenTradeItems;
        if (this.currentIndexItem > this.allTradesOrderItems.length) {
          this.currentIndexItem = this.allTradesOrderItems.length;
        }

        const listOpenTradeSlice = this.allTradesOrderItems.slice(this.data.length, this.currentIndexItem);

        const item = { ...listOpenTradeSlice[listOpenTradeSlice.length - 1] };
        if (!item.isShowLoading && this.currentIndexItem !== this.allTradesOrderItems.length) {
          item.isShowLoading = true;
          listOpenTradeSlice[listOpenTradeSlice.length - 1] = item;
        }
        const dataClone = [...this.data];
        dataClone.forEach((t) => (t.isShowLoading = false));

        this.data = [...dataClone, ...listOpenTradeSlice];
        this.initialData = structuredClone(this.data);
      }
    }, 500);
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.isOutPage = true;
    this.destroyTradeOder$.next();
    this.destroyTradeOder$.complete();
    this.socketService.closeConnection('open-trade-Order');

    const message = {
      destination: DESTINATION_MESSAGE.order,
      body: this.messageOrder,
    };
    if (this.messageOrder) {
      this.socketJs.sendMessage(message.destination, message.body);
    }

    this.store.dispatch(resetFilterOpenTrades());
    this.store.dispatch(resetSearchValueListOrder());
  }

  checkIsTodayAndIsDayOff() {
    this.store
      .select(selectDateOpenTradesOrder$)
      .pipe(takeUntil(this._destroy))
      .subscribe((date) => {
        const today = new Date();
        const formattedDate = dateToDMY(today);
        this.disableLoadingBtn = date !== formattedDate; // disable btn nếu kp ngày hôm nay

        // Nếu hôm nay là t7, Cn sẽ update ngày t6 gần nhất
        if (date === formattedDate && (today.getDay() === 0 || today.getDay() === 6)) {
          const dateObj = new Date(today);
          const previousFriday = getPreviousFriday(dateObj);
          this.currentDate = ConvertToDate(previousFriday);
          this.store.dispatch(updateDateInOpenTradesOrder({ date: previousFriday }));
        }
      });
  }

  getOrderPrice(price: number, typeTrades: string) {
    const typeNotShowPrice = ['LO', 'STOP LO'];
    if (typeNotShowPrice.includes(CONVERT_TYPE_TRADES[typeTrades])) {
      return price / 1000;
    }
    return CONVERT_TYPE_TRADES[typeTrades];
  }

  getTradesValue(tradeValue: number | null, typeTrades: string, tradeStatus: number) {
    if (!tradeValue) return null;

    if (tradeStatus === EStatusTrades.PARTIAL_MATCH) {
      return tradeValue;
    } else return '-';
  }

  listenChangeTradesOrder(customer: IAllAccountNumber[]) {
    const codeEvent = Object.values(ECodeEventOrder).filter((value) => typeof value === 'string');

    const codeEventString = codeEvent.join('|');

    const accountNo = customer.map((t) => t.accountNumber).join('|');
    if (!accountNo) return;

    const message = {
      destination: DESTINATION_MESSAGE.order,
      body: `sub${BODY_MESSAGE.ORDER_ALL}${codeEventString}.${accountNo}`,
    };

    if (this.messageOrder) {
      const messageUnSub = {
        ...message,
        body: this.messageOrder,
      };

      this.socketJs.sendMessage(messageUnSub.destination, messageUnSub.body);
    }

    this.messageOrder = `unsub${BODY_MESSAGE.ORDER_ALL}${codeEventString}.${accountNo}`;

    const topic = TOPIC.order;

    this.socketJs.initSocket(
      message,
      topic,
      (data, topicSocket) => {
        if (topic === topicSocket) {
          const { event_code, ord_no, stk_price, stk_qty, stk_cd, acnt_no, sub_no } = JSON.parse(data);
          let message = '';
          const status = [ECodeEventOrder.F03111, ECodeEventOrder.F03113, ECodeEventOrder.F03116].includes(
            event_code as ECodeEventOrder
          )
            ? this.i18.translate('MES-186')
            : this.i18.translate('MES-185');

          if ([ECodeEventOrder.F03111, ECodeEventOrder.F03112].includes(event_code as ECodeEventOrder)) {
            message = this.i18.translate('MES-627');

            message = message.replace('{{matchedPrice}}', stk_price).replace('{{matchedVolume}}', stk_qty);
          } else if ([ECodeEventOrder.F03113, ECodeEventOrder.F03114].includes(event_code as ECodeEventOrder)) {
            message = this.i18.translate('MES-628');
          } else if ([ECodeEventOrder.F03116, ECodeEventOrder.F03117].includes(event_code as ECodeEventOrder)) {
            message = this.i18.translate('MES-629');
          }

          message = message
            .replace('{{accNo}}', acnt_no)
            .replace('{{subNo}}', sub_no)
            .replace('{{code}}', stk_cd)
            .replace('{{status}}', status)
            .replace('{{orderNo}}', ord_no); // Nếu lọc tradeStatus chọn HUỶ và ko chọn CHƯA KHỚP

          if (message) {
            this.messageService.success(message);
          }
        }
      },
      true
    );
  }

  getStatusTradeOrder() {
    const openTradeCodes = this.data.map((d) => d.accountNumber);
    if (!openTradeCodes.length) return;
    if (this.destroyTradeOder$) {
      this.destroyTradeOder$.next();
    }
    const uniqueOpenTradeCode = [...new Set(openTradeCodes)];
    const OpenTradeMessage = uniqueOpenTradeCode.join('|');
    this.orderStatusEventService
      .changeOrderStatusEvent(`SHA.${OpenTradeMessage}`)
      .pipe(takeUntil(this.destroyTradeOder$))
      .subscribe((event: IEventSocketTradeOrder) => {
        if (!event) return;
        const { acnt_no, ordNo, event_code, stk_qty, stk_price, date } = event;
        const convertOpenTradeToDate = (date: string) => {
          const openTradeYear = date.slice(0, 4);
          const openTradeMonth = date.slice(4, 6);
          const openTradeDay = date.slice(6, 8);
          return openTradeDay + '/' + openTradeMonth + '/' + openTradeYear;
        };
        this.data = this.data.map((item) => {
          if (item.accountNumber === acnt_no && item?.ordNo === ordNo) {
            item.status = CONVERT_STATUS_TRADED_ORDER_TO_VALUE[event_code];
            item.matchedVolume = stk_qty;
            item.matchedPrice = stk_price;
            item.dateTrades = convertOpenTradeToDate(date);
            item.date = item.timeTrades + ' - ' + item.dateTrades;
            item.tradeStatus = +item.orderVolume === +stk_qty ? EStatusTrades.MATCHED : EStatusTrades.PARTIAL_MATCH;
          }
          return item;
        });
        this.initialData = structuredClone(this.data);
      });
  }

  /**
   * Xử lý gửi payload getListOpenTradesOrder
   */
  getListOpenTradesOrder() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((customers) => {
        if (!customers.length) {
          this.data = [];
          this.initialData = [];
          this.currentIndexItem = 0;
          return;
        }
        this.allCustomerInfo = customers.map((customer) => ({
          customerName: customer.customerName,
          accountNo: customer.accountNumber,
        }));
        const tagPageOrder = ETagPageOrder.OPENTRADE;

        this.store
          .select(selectAllBrokerLevelListByBrokerView$)
          .pipe(takeUntil(this._destroy))
          .subscribe((allOpenTradeBrokerLevelList) => {
            const currentOpenTradeBrokerCode = this.route.snapshot.queryParams['brokerId'];
            this.store.dispatch(
              getListTodayOrder({
                accountNumbers: [],
                brokerCode: this.getAllChildBrokerCodes(allOpenTradeBrokerLevelList, currentOpenTradeBrokerCode),
                tagPageOrder,
              })
            );
          });
      });
  }

  /**
   * searchData
   */
  searchOpenTradeData() {
    let searchOpenTradesData = [];
    this.loadingService.show();

    if (this.searchValue) {
      this.isSearch = true; // Set isSearch to true when searching

      const searchValue = this.searchValue.toString().trim().toLowerCase();
      searchOpenTradesData = this.allTradesOrderItems.filter((item: any) =>
        this.containsSearchValue(item, removeVietnameseTones(searchValue))
      );
    } else {
      this.isSearch = false; // Set isSearch to false when not searching
      searchOpenTradesData = [...this.allTradesOrderItems];
    }
    this.searchedData = [...searchOpenTradesData];

    setTimeout(() => {
      this.loadingService.hide();
    }, 1000);

    // this.searchedData <= pageSize
    if (!this.searchedData.length || this.searchedData.length <= TOTAL_ITEM_IN_LIST) {
      this.currentIndexItem = this.searchedData.length;
      const openTradeItem = { ...this.searchedData[this.searchedData.length - 1] };

      openTradeItem.isShowLoading = false;
      this.searchedData[this.searchedData.length - 1] = openTradeItem;
      this.isScrolling = false;
      this.data = [...this.searchedData];
    }

    // this.searchedData > pageSize
    if (this.searchedData.length > TOTAL_ITEM_IN_LIST) {
      const dataClone = this.searchedData.slice(0, TOTAL_ITEM_IN_LIST);
      const openTradeItem = { ...dataClone[dataClone.length - 1] };
      if (!openTradeItem.isShowLoading && this.currentIndexItem !== this.searchedData.length) {
        openTradeItem.isShowLoading = true;
        dataClone[dataClone.length - 1] = openTradeItem;
      }
      this.currentIndexItem = 0;
      this.gridRef?.scrollTopEvent();
      this.data = [...dataClone];
    }
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValue(item: any, searchValue: string): boolean {
    return (
      removeVietnameseTones(item.accountNumber?.toString().trim().toLowerCase())?.includes(searchValue) ||
      removeVietnameseTones(item.customerName?.toString().trim().toLowerCase())?.includes(searchValue) ||
      item.dateTrades?.toString().toLowerCase()?.includes(searchValue) ||
      removeVietnameseTones(item.code.toString().trim().toLowerCase())?.includes(searchValue)
    );
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'filter':
        {
          const {
            stockCodeOptionClone,
            typeTradesOptionsClone,
            statusTradesOptionsClone,
            accountNumberOptionsClone,
            // traderOptionsClone,
            orderChannelClone,
          } = this.updateOpenTradeListForFilter();

          const ref = this.openFilter(TradesOrderFilterComponent, {
            width: '800px',
            data: {
              config: {
                code: true,
                status: true,
                typeTrades: 'dropdown',
                statusTrades: 'checkbox',
                numberAccount: true,
                dateOrderTrades: {
                  position: 'right',
                  show: true,
                },
                trader: 'right',
                orderChannel: 'right',
                tradesValue: true,
                revenue: false,
                netTradesFee: false,
                brokerageCommission: false,
              } as IOptionConfigFilter,
              listOption: {
                codeOptions: stockCodeOptionClone,
                typeTradesOptions: typeTradesOptionsClone,
                statusTradesOptions: statusTradesOptionsClone,
                numberAccountOptions: accountNumberOptionsClone,
                // traderOptions: traderOptionsClone,
                orderChannel: orderChannelClone,
              } as IListOptionConfig,
              optionFilter: this.dataFilter,
            },
          });

          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe((v) => {
              if (!v) return;
              this.applyFilter(v);
            });
        }
        break;

      case 'loading':
        this.getListOpenTradesOrder();
        break;

      default:
        break;
    }
  }

  /**
   * UpdateListForFilter
   * @returns {any} newlist
   */
  updateOpenTradeListForFilter() {
    const { optionSelection } = this.dataFilter;
    const {
      codeOptionsSelect,
      typeTradesOptionsSelect,
      statusTradesOptionsSelect,
      numberAccountOptionsSelect,
      orderChannelOptionSelect,
    } = optionSelection;

    const isSelectOpenTrades = (value: string, data: string[] | null) => data === null || data.includes(value);

    let stockCodeOptionClone!: IOptionSelect[];
    this.store
      .select(selectAllStockList$)
      .pipe(
        take(1),
        filter((stockCode) => stockCode.length > 0),
        map((stockCode) => {
          stockCodeOptionClone = stockCode.map((code) => ({
            label: `${code.id} : ${code.stock}`,
            value: code.id,
            isSelect: codeOptionsSelect?.length ? isSelectOpenTrades(code.id, codeOptionsSelect) : true, // [] -> all
          }));
        })
      )
      .subscribe();

    const typeTradesOptionsClone = typeTradesDropdownOption.map((code) => ({
      label: code.label,
      value: code.value,
      isSelect: typeTradesOptionsSelect?.length ? isSelectOpenTrades(code.value, typeTradesOptionsSelect) : true, // [] -> all
    }));

    const statusTradesOptionsClone = statusTradesOpenTradesOption
      .filter((code) => code.value !== EStatusTradeFilter.NOT_ACTIVATED && code.value !== EStatusTradeFilter.ACTIVATED)
      .map((code) => ({
        label: code.label,
        value: code.value,
        isSelect: statusTradesOptionsSelect?.length ? isSelectOpenTrades(code.label, statusTradesOptionsSelect) : true, // [] -> all
      }));

    let accountNumberOptionsClone!: IOptionSelect[];
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(take(1))
      .subscribe((customers) => {
        accountNumberOptionsClone = customers.map((customer) => ({
          label: `${customer.accountNumber} - ${customer.customerName}`,
          value: `${customer.accountNumber}`,
          isSelect: numberAccountOptionsSelect?.length
            ? isSelectOpenTrades(customer.accountNumber, numberAccountOptionsSelect)
            : true, // [] -> all
        }));
      });

    const orderChannelClone = orderChannelOptions.map((code) => ({
      label: code.label,
      value: code.value,
      isSelect: orderChannelOptionSelect?.length ? isSelectOpenTrades(code.value, orderChannelOptionSelect) : true, // [] -> all
    }));

    return {
      stockCodeOptionClone,
      typeTradesOptionsClone,
      statusTradesOptionsClone,
      accountNumberOptionsClone,
      // traderOptionsClone,
      orderChannelClone,
    };
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;
    if (type === 'save') {
      if (!this.isFilter && !optionFilter?.isFilter) return;
      this.store.dispatch(setFilterOpenTrades({ params: optionFilter }));
      this.store.dispatch(resetSearchValueListOrder());
    } else if (type === 'default') {
      this.store.dispatch(setFilterOpenTrades({ params: initialTradesOrderState.openTradeFilter }));
    }
  }

  /**
   * UpdateRangeFilter
   * @param optionFilter
   * @param init
   * @returns {any} new list
   */
  updateRangeFilter(optionFilter: IFilterOpenTradesParam, init: any) {
    const { valueTrades, dateRange } = optionFilter;

    // GTGD
    const getValueMatch = (value: IRangeFilter, initField: number) => {
      if (value?.start && value?.end) {
        return initField >= +value.start && initField <= +value.end;
      } else if (value?.start) {
        return initField >= +value.start;
      } else if (value?.end) {
        return initField <= +value.end;
      } else {
        return true;
      }
    };

    const isValueTradesMatch = getValueMatch(valueTrades, +init.tradeValue);

    const dateData = new Date(init.dateTrades.split('/').reverse().join('-')).getTime();
    const startDate = new Date(dateRange.start ?? '').getTime();
    const endDate = new Date(dateRange.end ?? '').getTime();
    const isDateInRange =
      (dateRange.start ? dateData >= startDate : true) && (dateRange.end ? endDate >= dateData : true);
    return isValueTradesMatch && isDateInRange;
  }

  /**
   * UpdateOptionSelection
   * @param optionSelection
   * @param init
   * @returns  {any} new list
   */
  updateOptionSelection(optionSelection: IOptionSelection, init: any) {
    const {
      codeOptionsSelect,
      typeTradesOptionsSelect,
      statusTradesOptionsSelect,
      numberAccountOptionsSelect,
      traderOptionsSelect,
      orderChannelOptionSelect,
    } = optionSelection;

    const isCodeMatch = (codeOptionsSelect ?? []).length ? (codeOptionsSelect ?? []).includes(init.code) : true;

    const isTypeTradeMatch = (typeTradesOptionsSelect ?? []).length
      ? (typeTradesOptionsSelect ?? []).includes(init.typeTrades)
      : true;

    const isStatusTradeMatch = (statusTradesOptionsSelect ?? []).length
      ? (statusTradesOptionsSelect ?? []).includes(statusTradesOpenTradesOption[init.tradeStatus].label)
      : true;

    const isNumberAccountMatch = (numberAccountOptionsSelect ?? []).length
      ? (numberAccountOptionsSelect ?? []).some((item) => item.includes(init.accountNumber))
      : true;

    const isTraderMatch = (traderOptionsSelect ?? []).length ? (traderOptionsSelect ?? []).includes(init.trader) : true;

    const isOrderChanelMatch = (orderChannelOptionSelect ?? []).length
      ? (orderChannelOptionSelect ?? []).includes(init.orderChannel)
      : true;

    return (
      isCodeMatch &&
      isTypeTradeMatch &&
      isStatusTradeMatch &&
      isNumberAccountMatch &&
      isTraderMatch &&
      isOrderChanelMatch
    );
  }

  /**
   * ContextMenuClick
   * @param {GridContextMenu<any>} param0 { event, element, source }
   */
  contextMenuClick({ event, element, source }: GridContextMenu<any>) {
    event.preventDefault();

    const origin = event.target as HTMLElement;
    this.openSelectOption(origin, element);
  }

  /**
   * OpenSelectOption
   * @param {HTMLElement} origin  origin
   * @param {any} element element
   */
  openSelectOption(origin: HTMLElement, element: any) {
    let options = [
      {
        url: './assets/icons/arrow-2-direction.svg',
        label: 'MES-211',
        tag: 'detail',
      },
      {
        url: './assets/icons/edit-2.svg',
        label: 'MES-212',
        tag: 'edit',
      },
      {
        url: './assets/icons/x-cross-red.svg',
        label: 'MES-213',
        tag: 'cancel',
        classCustom: 'color-red-text',
      },
    ];

    const today = new Date();
    const todayFormatted = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1)
      .toString()
      .padStart(2, '0')}/${today.getFullYear()}`;
    const isToday = element.dateTrades === todayFormatted;
    if (element.tradeStatus === EStatusTrades.CANCELED || !isToday) {
      options = options.filter((option) => option.tag !== 'edit' && option.tag !== 'cancel');
    }

    if (element.tradeStatus === EStatusTrades.REJECTED) {
      options = options.filter((option) => option.tag !== 'edit' && option.tag !== 'cancel');
    }

    if (EOrderType.LO !== element.typeTrades) {
      options = options.filter((option) => option.tag !== 'edit');
    }

    const openTradeRef = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 3,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        templateRefExp: this.contextMenuRef,
        searchKey: 'tag',
        options,
      },
    });

    openTradeRef.afterClosed$.pipe(take(1)).subscribe({
      next: (v) => {
        if (!v.data) return;
        const { item } = v.data;
        if (!item.length) return;
        const { tag } = item[0];
        switch (tag) {
          case 'detail':
            this.openDetailOpenTrades(element);
            break;
          case 'edit':
            this.editOpenTrades(element);
            break;
          case 'cancel':
            this.cancelOpenTrades(element);
            break;
          default:
            break;
        }
      },
    });
  }

  /**
   * editOpenTrades
   * @param {IOrderListResponse} element
   */
  editOpenTrades(element: IOrderListResponse) {
    this.store
      .select(selectAllStockList$)
      .pipe(take(1))
      .subscribe((d: any) => {
        const stockOpenTradesOptions: IOptionStock[] = d.map((t: IStockItemInList) => ({
          value: t.shortName,
          label: t.name,
          id: t.id,
          stoke: t.stock,
        }));
        const initialStockOptions = deepClone(stockOpenTradesOptions);
        const initialStock = stockOpenTradesOptions.find((option) => option.id === element.code.split(':')[0].trim());

        this.dialogService.openRightDialog(PlaceOrderPopupComponent, {
          width: '1100px',
          data: {
            title: 'MES-624',
            isDerivatives: false, // fix khi có lệnh phái sinh
            stockOpenTradesOptions,
            initialStockOptions,
            initialStock,
            element,
            type: ETagPageOrder.OPENTRADE,
          },
        });
      });
  }

  /**
   * CancelOpenTrades
   * @param {IOrderListResponse} element element
   */
  cancelOpenTrades(element: IOrderListResponse) {
    let openTradeUserName = '';
    let openTradebrokerCode = '';
    let openTradeMacAddress = '';
    let idNo = '';

    this.store
      .select(selectCurrentBrokerView$)
      .pipe(take(1))
      .subscribe((userOp) => {
        openTradeUserName = userOp.userName;
        openTradebrokerCode = userOp.brokerCode;
        this.hashService.hashWithSHA256(userOp.brokerCode, 'SHA').then((hash) => (openTradeMacAddress = hash));

        const payloadGetIdNo = {
          accountNumbers: [element.accountNumber.split(' - ')[0]],
          brokerCode: [openTradebrokerCode],
          userType: '',
          fromBirthYear: '',
          toBirthYear: '',
          searchKey: '',
        };

        this.shareService
          .getPersonalInfoList(payloadGetIdNo)
          .pipe(take(1))
          .subscribe((dataOptenTrade) => {
            idNo = dataOptenTrade[0].identity;
          });
      });

    const refOpen = this.dialogService.openPopUp(DeleteNotificationComponent, {
      data: {
        isActive: false,
        labels: {
          headerConfirmLabel: 'MES-87',
          buttonPrimaryLabel: 'MES-74',
          subTitleConfirm: 'MES-225',
          buttonOtherLabel: 'MES-226',
        },
        isReverse: true,
      },
      height: '260px',
      panelClass: ['popup-confirm', 'not-padding-popup'],
      width: '360px',
    });

    refOpen
      .afterClosed()
      .pipe(take(1))
      .subscribe((v) => {
        if (v === 'save') {
          const payload = {
            hts_user_id: openTradeUserName,
            cli_mac_addr: openTradeMacAddress,
            hts_user_nm: openTradebrokerCode,
            account_list: [
              {
                acnt_no: element.accountNumber.split(' - ')[0],
                sub_no: element.subAccount,
                idno: idNo,
              },
            ],
            ord_no: element.ordNo.toString(),
            bank_cd: '9999',
            brch_cd: element.bnhCd,
            ord_mdm_tp: '01',
          };

          this.openTradeStore.cancelTradeOrder({ payload });
        }
      });
  }

  /**
   * OpenDetailOpenTrades
   * @param {any} element element
   */
  openDetailOpenTrades(element: any) {
    const today = new Date();
    const todayFormatted = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1)
      .toString()
      .padStart(2, '0')}/${today.getFullYear()}`;
    const isToday = element.dateTrades === todayFormatted;

    this.dialogService.openRightDialog(OpenTradesDetailComponent, {
      width: '530px',
      data: {
        trade: { ...element, typeTagOrder: ETagPageOrder.OPENTRADE },
        isHideEditTradeOrderBtn: !isToday || element.typeTrades !== EOrderType.LO,
        isHideCancelTradeOrderBtn: !isToday,
        isHideBrokerCommission: true,
        isHideNETTransactionFee: true,
        isHideRevenueFee: true,
      },
    });
  }

  dateChange(data: IDateChangeItem) {
    const { date } = data;
    const cleanString = (str: string) => str.trim().normalize();

    this.store
      .select(selectDateOpenTradesOrder$)
      .pipe(take(1))
      .subscribe((dateStore) => {
        if (cleanString(dateStore) === cleanString(date as string)) return;
        this.store.dispatch(updateDateInOpenTradesOrder({ date: date as string }));
      });
  }

  /**
   * OpenMoreAction
   * @param {any} event
   */
  openMoreAction(event: any) {
    const { html, element } = event;
    this.openSelectOption(html, element);
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.export]);
  }

  /**
   * getBrokerInfo
   */
  getBrokerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBroker) => {
        if (!currentBroker) return;

        this.currentBrokerCode = currentBroker.brokerCode;

        combineLatest([
          this.store.select(selectInfoUserLogin$),
          this.store.select(selectAllBrokerLevelListByBrokerView$),
        ])
          .pipe(takeUntil(this._destroy))
          .subscribe(([userList, brokers]) => {
            const queryParams = this.route.snapshot.queryParams;

            if (!userList) return;

            const brokerConvert = updateBorkerName([...brokers], [...userList]);

            const subBroker = brokerConvert.map((broker) => {
              return {
                brokerCode: `${broker.brokerCode}`,
                name: `${broker.brokerName}`,
                isSelect: queryParams['brokerId']
                  ? broker.brokerCode === queryParams['brokerId']
                  : broker.brokerCode === currentBroker.brokerCode,
              };
            });

            this.LIST_MG = [...subBroker];
            const broker = this.LIST_MG.find((t) => t.isSelect);

            if (broker) {
              this.brokerInfo = {
                label: broker.name ?? '',
                value: (broker['brokerCode'] as string) ?? '',
              };
            }
          });
      });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    const queryParamsOt = this.route.snapshot.queryParams;

    const elementRef = new ElementRef(document.querySelector(`.broker-icon`));
    const elementWidth = elementRef.nativeElement as HTMLElement;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRef as any,
      width: elementWidth.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelectedOt = res.data.find((i) => i.isSelect);
      if (!itemSelectedOt) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBrokerOt = userList.find((user) => user.brokerCode === itemSelectedOt['brokerCode']);

          const subBroker = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelectedOt['brokerCode']);
          if (queryParamsOt['brokerId'] === itemSelectedOt['brokerCode']) return;
          if (subBroker && !currentBrokerOt) {
            const brokerCode = subBroker['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (currentBrokerOt) {
            if (this.currentBrokerCode === currentBrokerOt.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBrokerOt.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBrokerOt }));
          }

          if (subBroker) {
            this.LIST_MG = this.LIST_MG.map((brokerOt) => ({
              ...brokerOt,
              isSelect: brokerOt['brokerCode'] === subBroker['brokerCode'],
            }));

            this.brokerInfo = {
              label: subBroker['name'] ?? '',
              value: (subBroker['brokerCode'] as string) ?? '',
            };
          }

          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));

          const subBrokerCode = subBroker ? subBroker['brokerCode'] : null;
          this.router.navigate([], {
            queryParams: {
              ...queryParamsOt,
              brokerId: currentBrokerOt ? currentBrokerOt.brokerCode : subBrokerCode,
            },
          });
        });
    });
  }

  /**
   * Lấy list brokerCode con của brokerCode và brokerCode đó
   * @param {IAllLevelOfBroker[]} allLevelOfBrokerList
   * @param {string} currentBrokerCode
   * @returns {string[]}
   */
  getAllChildBrokerCodes(allLevelOfBrokerList: IAllLevelOfBroker[], currentBrokerCode: string): string[] {
    const currentBroker = allLevelOfBrokerList.find((b) => b.brokerCode === currentBrokerCode);
    const result: string[] = [];

    const collectChildBrokerCodes = (brokerItem: IAllLevelOfBroker) => {
      for (const child of brokerItem.children) {
        result.push(child.brokerCode);
        collectChildBrokerCodes(child);
      }
    };

    if (currentBroker) {
      result.push(currentBroker.brokerCode);
      collectChildBrokerCodes(currentBroker);
    }

    return result;
  }
}
