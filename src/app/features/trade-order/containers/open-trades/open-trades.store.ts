import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { ImmerComponentStore } from 'ngrx-immer/component-store';
import { DestroyService,  LoadingService, MessageService } from 'src/app/core/services';
import { ICancelOrderPayload } from '../../models/trade-order';
import { catchError, combineLatest, finalize, of, switchMap, takeUntil, tap } from 'rxjs';
import { TradesOrderService } from '../../services/trades-order.service';
import { ApiErrorResponse } from '@shared/models';
import { tapResponse } from '@ngrx/operators';
import {
  selectCurrentBrokerView$,
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import { ETagPageOrder } from '../../constants/trade-order';
import { getListTodayOrder } from '../../stores/trade-order.actions';
import { IAllLevelOfBroker } from 'src/app/shared/models/global';

interface IOpenTradeState {}

@Injectable({ providedIn: 'root' })
export class OpenTradeStore extends ImmerComponentStore<IOpenTradeState> {
  readonly cancelTradeOrder = this.effect<{
    payload: ICancelOrderPayload;
  }>(($event) =>
    $event.pipe(
      tap(() => {
        this.openTradeloadingService.show();
      }),
      switchMap(({ payload }) => {
        return this.openTradeOrderService.cancelPlaceOrder(payload).pipe(
          finalize(() => this.openTradeloadingService.hide()),
          catchError((error) => {
            return of(new ApiErrorResponse<any>(error.error));
          })
        );
      }),
      tapResponse(
        (openTraderes) => {
          if (openTraderes instanceof ApiErrorResponse) {
            this.messageOpenTradeService.error(openTraderes.message);
            return;
          }
          const messageShow = openTraderes[0].error_desc.split(']').pop();
          if (!openTraderes[0].success) {
            this.messageOpenTradeService.error(messageShow ?? openTraderes[0].error_desc);
          }
          this.getListOpenTradesOrder();
        },
        (err: any) => {}
      )
    )
  );

  getListOpenTradesOrder() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((customers) => {
        if (!customers.length) return;
        const tagPageOrderOpenTrade = ETagPageOrder.OPENTRADE;

        combineLatest([
          this.store.select(selectCurrentBrokerView$),
          this.store.select(selectAllBrokerLevelListByBrokerView$),
        ])
          .pipe(takeUntil(this._destroy))
          .subscribe(([usersOpenTrade, allBrokerLevelListOpenTrade]) => {
            const currentBrokerCode = usersOpenTrade.brokerCode;

            this.store.dispatch(
              getListTodayOrder({
                accountNumbers: [],
                brokerCode: this.getAllChildBrokerCodes(allBrokerLevelListOpenTrade, currentBrokerCode),
               tagPageOrder: tagPageOrderOpenTrade,
              })
            );
          });
      });
  }

  /**
   * Lấy list brokerCode con của brokerCode và brokerCode đó
   * @param {IAllLevelOfBroker[]} allLevelOfBrokerList
   * @param {string} currentBrokerCodeOpen
   * @returns {string[]}
   */
  getAllChildBrokerCodes(allLevelOfBrokerList: IAllLevelOfBroker[], currentBrokerCodeOpen: string): string[] {
    const currentBrokerOpen = allLevelOfBrokerList.find((b) => b.brokerCode === currentBrokerCodeOpen);
    const result: string[] = [];

    const collectChildBrokerCodes = (brokerItem: IAllLevelOfBroker) => {
      for (const child of brokerItem.children) {
        result.push(child.brokerCode);
        collectChildBrokerCodes(child);
      }
    };

    if (currentBrokerOpen) {
      result.push(currentBrokerOpen.brokerCode);
      collectChildBrokerCodes(currentBrokerOpen);
    }

    return result;
  }

  constructor(
    private readonly openTradeloadingService: LoadingService,
    private readonly messageOpenTradeService: MessageService,
    private readonly store: Store,
    private readonly _destroy: DestroyService,
    private readonly openTradeOrderService: TradesOrderService
  ) {
    super({});
  }
}
