/* eslint-disable complexity */
import { Component, Element<PERSON>ef, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { DestroyService, I18nService, LoadingService, MessageService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import {
  CONVERT_ORDER_CHANNEL,
  CONVERT_SESSION_TO_LABEL,
  CONVERT_STATUS_TRADED_ORDER_TO_VALUE,
  CONVERT_TYPE_TRADES_ORDER_TO_LABLE,
  CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE,
  EStatusTradeFilter,
  EStatusTrades,
  EStatusTradesOrder,
  ETagPageOrder,
  TOTAL_ITEM_IN_LIST,
} from '../../constants/trade-order';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { TradesOrderFilterComponent } from '../../components/open-trades-filter/trades-order-filter.component';
import {
  CONVERT_TYPE_TRADES,
  ICustomerInfos,
  IEventSocketTradeOrder,
  IFilterOpenTradesParam,
  IListOptionConfig,
  IOptionConfigFilter,
  IOptionSelection,
  IOrderListResponse,
  IRangeFilter,
} from '../../models/trade-order';
import {
  conditionTradesOptions,
  orderChannelOptions,
  statusTradesOpenTradesOption,
  typeTradesDropdownOption,
} from '../../constants/fakeDataList';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { GridContextMenu } from '@shared/models';
import { Store } from '@ngrx/store';
import {
  selectDateReserveTradesOrder$,
  selectFilterReserveTrade$,
  selectListTodayOrder$,
  selectSearchValue$,
} from '../../stores/trade-order.selections';
import { combineLatest, filter, map, Subject, take, takeUntil, withLatestFrom } from 'rxjs';
import {
  getListTodayOrder,
  resetFilterReserveTrades,
  resetSearchValueListOrder,
  setFilterReserveTrades,
  updateDateInReserveTradeOrder,
} from '../../stores/trade-order.actions';
import { ConvertToDate, dateToDMY, dateToYMD, getPreviousFriday } from '../../../../shared/utils/date';
import { OpenTradesDetailComponent } from '../../components/open-trades-detail/open-trades-detail.component';
import { Router } from '@angular/router';
import { IAllLevelOfBroker, IAllStockList, IOptionSelect } from 'src/app/shared/models/global';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import {
  getCurrentBrokerView,
  getCustomerGroupsList,
  getListAccountNumberAndLevelByBrokerView,
} from 'src/app/stores/shared/shared.actions';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectAllStockList$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import { IDateChangeItem } from 'src/app/shared/components/date-picker/date-picker/date-picker.component';
import { SocketService } from 'src/app/shared/services/realtime/socket.service';
import { OrderStatusEventService } from 'src/app/shared/services/order-status.service';
import { initialTradesOrderState } from '../../stores/trade-order.reducers';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { SharedService } from 'src/app/shared/services/shared.service';
import { ReserveTradeStore } from './reserve-trades.store';
import { isTradingSession } from 'src/app/shared/utils/trading';
import { BODY_MESSAGE, DESTINATION_MESSAGE, ECodeEventOrder, TOPIC } from 'src/app/shared/constants/trading';
import { SockJSClientService } from 'src/app/shared/services/realtime/sockjs-client.service';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { updateBorkerName } from 'src/app/shared/utils/utils';
import { removeVietnameseTones } from 'src/app/shared/utils/text';

/**
 * ReserveTradesContainer
 */
@Component({
  selector: 'app-reserve-trades-container',
  templateUrl: './reserve-trades.container.html',
  styleUrls: ['./reserve-trades.container.scss'],
  providers: [DestroyService],
})
export class ReserveTradesContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('contextMenuRef', { static: true }) contextMenuRef: TemplateRef<any> | null = null;
  @ViewChild('grid', { static: true }) gridRef!: GridComponent<any>;

  searchValue: string = '';

  customNumberFormat = customNumberFormat;

  disableLoadingBtn = false;

  private readonly destroyTradeOder$ = new Subject<void>();

  dataFilter!: IFilterOpenTradesParam;

  isOutPage = false;

  currentIndex = 1;

  currentDate = new Date();

  isOpenCalendar = false;

  limitDateAccept = new Date();

  brokerInfo!: {
    label: string;
    value: string;
  } | null;

  LIST_MG: IListOptions[] = [];
  allCustomerInfo!: ICustomerInfos[];
  stockCodeList!: IAllStockList[];

  messageOrder = '';

  allTradesOrderItems: any = [];

  currentIndexItem = 0;

  isScrolling = false;

  currentBrokerCode = '';

  /**
   * Constructor
   * @param popoverService PopoverService
   * @param store store
   * @param _destroy _destroy
   * @param cdf ChangeDetectorRef
   */
  constructor(
    private readonly popoverService: PopoverService,
    private readonly store: Store,
    private readonly _destroy: DestroyService,
    private readonly socketService: SocketService,
    private readonly orderStatusEventService: OrderStatusEventService,
    private readonly router: Router,
    private readonly shareService: SharedService,
    private readonly reserveTradeStore: ReserveTradeStore,
    private readonly i18: I18nService,
    private readonly messageService: MessageService,
    private readonly socketJs: SockJSClientService,
    private readonly loadingService: LoadingService
  ) {
    super();
    this.getBrokerInfo();
    this.toggleButtonByTags([ActionButton.loading, ActionButton.display, ActionButton.filter]);
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.columnConfigs = [
      // Ngày đặt lệnh
      {
        minWidth: 200,
        name: 'MES-171',
        width: 200,
        tag: 'date',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
      },
      // Số tài khoản
      {
        minWidth: 146,
        name: 'MES-66',
        width: 146,
        tag: 'accountNumber',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
      },
      // Tên khách hàng
      {
        minWidth: 30,
        name: 'MES-184',
        width: 200,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
      },
      // Số hiệu lệnh gốc
      {
        minWidth: 30,
        name: 'MES-604',
        width: 150,
        tag: 'fstNo',
        isDisplay: true,
        align: 'center',
        resizable: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
      },
      // Số hiệu lệnh
      {
        minWidth: 30,
        name: 'MES-597',
        width: 100,
        tag: 'ordNo',
        isDisplay: true,
        align: 'center',
        resizable: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          return value;
        },
      },
      // Lệnh
      {
        minWidth: 30,
        name: 'MES-172',
        width: 100,
        tag: 'status',
        isDisplay: true,
        resizable: true,
        align: 'center',
        displayValueFn: (value) => CONVERT_TYPE_TRADES_ORDER_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === EStatusTradesOrder.SELL) {
            return 'type-status sell-cls';
          } else return 'type-status';
        },
      },
      // Loại lệnh
      {
        minWidth: 30,
        name: 'MES-173',
        width: 130,
        tag: 'typeTrades',
        isDisplay: true,
        resizable: true,
      },
      // Điều kiện lệnh
      {
        minWidth: 30,
        name: 'MES-201',
        width: 180,
        tag: 'conditionTrades',
        isDisplay: true,
        resizable: true,
      },
      // Mã CK
      {
        minWidth: 30,
        name: 'MES-174',
        width: 130,
        tag: 'code',
        isDisplay: true,
        resizable: true,
      },
      // Giá đặt
      {
        minWidth: 30,
        name: 'MES-176',
        width: 100,
        tag: 'orderPrice',
        align: 'end',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          if (typeof v === 'string') return v;
          return customNumberFormat(v, 'decimal', 'en-US', 2);
        },
      },
      // Giá khớp
      {
        minWidth: 30,
        name: 'MES-177',
        width: 130,
        tag: 'matchedPrice',
        align: 'end',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';

          return customNumberFormat(v, 'decimal', 'en-US', 2);
        },
      },
      // KL khớp
      {
        minWidth: 30,
        name: 'MES-178',
        width: 130,
        tag: 'matchedVolume',
        align: 'end',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';

          return customNumberFormat(v, 'decimal', 'en-US');
        },
      },
      // KL đặt
      {
        minWidth: 30,
        name: 'MES-179',
        width: 130,
        tag: 'orderVolume',
        align: 'end',
        isDisplay: true,
        resizable: true,
        panelClass: 'location-cls',
        displayValueFn: (v) => {
          if (!v) return '-';

          return customNumberFormat(v, 'decimal', 'en-US');
        },
      },
      // GTGD
      {
        minWidth: 30,
        name: 'MES-180',
        width: 140,
        tag: 'tradeValue',
        isDisplay: true,
        resizable: true,
        align: 'end',
        displayValueFn: (v) => {
          if (!v) {
            return '-';
          } else if (+v) {
            return customNumberFormat(v, 'decimal', 'en-US');
          } else return v;
        },
      },
      // Tổng phí
      {
        minWidth: 30,
        name: 'MES-581',
        width: 155,
        align: 'end',
        tag: 'revenueFee',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';

          return customNumberFormat(v, 'decimal', 'en-US');
        },
      },
      // Net phí giao dịch
      {
        minWidth: 30,
        name: 'MES-195',
        width: 140,
        tag: 'netTransactionFee',
        align: 'end',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) {
            return '-';
          } else if (+v) {
            return customNumberFormat(v, 'decimal', 'en-US');
          } else return v;
        },
      },
      // Hoa Hồng MG
      // {
      //   name: 'MES-196',
      //   minWidth: 30,
      //   width: 140,
      //   tag: 'brokerCommission',
      //   align: 'end',
      //   isDisplay: true,
      //   resizable: true,
      //   displayValueFn: (v) => {
      //     if (!v) return '-';

      //     return customNumberFormat(v, 'decimal', 'en-US');
      //   },
      // },
      // Trạng thái lệnh
      {
        minWidth: 30,
        name: 'MES-181',
        width: 180,
        tag: 'tradeStatus',
        isDisplay: true,
        resizable: true,
        align: 'center',
        displayValueFn: (value) => CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE[value],
        dynamicClass: (value) => {
          if (value === EStatusTrades.NOT_MATCH) {
            return 'type-status-trades-match not-match-cls';
          } else if (value === EStatusTrades.PARTIAL_MATCH) {
            return 'type-status-trades-match partial-match-cls';
          } else if (value === EStatusTrades.NOT_ACTIVATED) {
            return 'type-status-trades-match not-activated-cls';
          } else if (value === EStatusTrades.CANCELED) {
            return 'type-status-trades-match canceled-cls';
          } else if (value === EStatusTrades.ACTIVATED) {
            return 'type-status-trades-match active-cls';
          } else return 'type-status-trades-match rejected-cls';
        },
      },
      // Ngày kích hoạt
      {
        minWidth: 30,
        name: 'MES-198',
        width: 140,
        tag: 'activationDate',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (!v) return '-';

          return v;
        },
      },
      // Người đặt
      {
        minWidth: 30,
        name: 'MES-182',
        width: 90,
        tag: 'trader',
        isDisplay: true,
        resizable: true,
      },
      // Kênh đặt
      {
        minWidth: 30,
        name: 'MES-183',
        width: 140,
        tag: 'orderChannel',
        isDisplay: true,
        resizable: true,
      },
    ];

    this.getListReserveTradesOrder();
    this.store
      .select(selectListTodayOrder$)
      .pipe(takeUntil(this._destroy), withLatestFrom(this.store.select(selectSearchValue$)))
      .subscribe(([openTrades, searchValue]) => {
        this.isSearch = !!searchValue;
        if (this.isOutPage || this.isSearch) return;
        this.data = [];
        this.initialData = [];
        this.currentIndexItem = 0;
        this.gridRef?.scrollTopEvent();
        this.allTradesOrderItems = openTrades.map((d) => ({
          ...d,
          orderPrice: this.getOrderPrice(d.orderPrice, d.typeTrades),
          matchedPrice: d.matchedPrice / 1000,
          date: d.timeTrades + ' - ' + d.dateTrades,
          accountNumber: d.accountNumber + ' - ' + d.subAccount,
          tradeValue: this.getTradesValue(d.mthAmt, CONVERT_TYPE_TRADES[d.typeTrades], d.tradeStatus),
          customerName: this.allCustomerInfo.find((cus) => cus.accountNo === d.accountNumber)?.customerName,
          typeTrades: CONVERT_TYPE_TRADES[d.typeTrades],
          orderChannel: CONVERT_ORDER_CHANNEL[d.orderChannel],
          conditionTrades: CONVERT_SESSION_TO_LABEL[d.preOrderSession],
          activationDate: d.tradeStatus === EStatusTrades.ACTIVATED ? this.formatActivationDate(d.activateDate) : null,
          tradeStatus: d.tradeStatus === 0 && d.nmthQty === 0 ? EStatusTrades.CANCELED : d.tradeStatus,
        }));

        this.sliceReserveDataItems();

        this.checkHeightTable();

        const isTrading = isTradingSession();
        if (!isTrading) return;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchValue = value;
        this.searchData();
      });

    this.store
      .select(selectFilterReserveTrade$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        if (!value) return;
        this.dataFilter = { ...value };
        this.isFilter = value.isFilter;
      });
  }

  sliceReserveDataItems() {
    if (this.data.length) this.currentIndexItem = this.data.length;
    const endReserveTrades = this.currentIndexItem + TOTAL_ITEM_IN_LIST;
    const listSliceReserveTrades = this.isSearch
      ? this.searchedData.slice(this.currentIndexItem, endReserveTrades)
      : this.allTradesOrderItems.slice(this.currentIndexItem, endReserveTrades);
    this.currentIndexItem = endReserveTrades;
    if (this.currentIndexItem > this.allTradesOrderItems.length) {
      this.currentIndexItem = this.allTradesOrderItems.length;
    }
    const item = { ...listSliceReserveTrades[listSliceReserveTrades.length - 1] };
    if (!item.isShowLoading && this.currentIndexItem !== this.allTradesOrderItems.length) {
      item.isShowLoading = true;
      listSliceReserveTrades[listSliceReserveTrades.length - 1] = item;
    }

    const dataCloneReserveTrades = [...this.data];
    dataCloneReserveTrades.forEach((t) => (t.isShowLoading = false));

    this.data = [...dataCloneReserveTrades, ...listSliceReserveTrades];
    this.initialData = structuredClone(this.data);
  }

  scrollData(event: Event) {
    const contentList = event.target as Element;
    if (Math.ceil(contentList.scrollTop + contentList.clientHeight) < contentList.scrollHeight - 42) {
      return;
    }

    if (this.currentIndexItem === this.allTradesOrderItems.length) return;

    if (!this.isScrolling) {
      this.isScrolling = true;
      this.sliceReserveDataItems();
      this.isScrolling = false;
    }
  }

  checkHeightTable() {
    setTimeout(() => {
      const tableContainer = document.querySelector('.table-container') as HTMLElement;
      const gridElements = document.querySelector('.mat-mdc-table') as HTMLElement;

      const tableContainerHeight = tableContainer.offsetHeight;
      const gridElementsHeight = gridElements.offsetHeight;

      if (
        gridElementsHeight < tableContainerHeight &&
        this.data.length &&
        this.allTradesOrderItems.length > this.data.length
      ) {
        const missingItems = Math.ceil((tableContainerHeight - gridElementsHeight) / 36);
        this.currentIndexItem += missingItems;
        if (this.currentIndexItem > this.allTradesOrderItems.length) {
          this.currentIndexItem = this.allTradesOrderItems.length;
        }

        const listSlice = this.allTradesOrderItems.slice(this.data.length, this.currentIndexItem);

        const item = { ...listSlice[listSlice.length - 1] };
        if (!item.isShowLoading && this.currentIndexItem !== this.allTradesOrderItems.length) {
          item.isShowLoading = true;
          listSlice[listSlice.length - 1] = item;
        }
        const dataClone = [...this.data];
        dataClone.forEach((t) => (t.isShowLoading = false));

        this.data = [...dataClone, ...listSlice];
        this.initialData = structuredClone(this.data);
      }
    }, 500);
  }

  updateDataShowLoadingItem(data: any, isLoading: boolean) {
    const item = { ...data[data.length - 1] };
    item.isShowLoading = isLoading;

    data[data.length - 1] = item;
    return data;
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.isOutPage = true;
    this.destroyTradeOder$.next();
    this.destroyTradeOder$.complete();

    const message = {
      destination: DESTINATION_MESSAGE.order,
      body: this.messageOrder,
    };
    if (this.messageOrder) {
      this.socketJs.sendMessage(message.destination, message.body);
    }

    this.store.dispatch(resetFilterReserveTrades());
    this.store.dispatch(resetSearchValueListOrder());
  }

  /**
   * Xử lý gửi payload getListReserveTradesOrder
   */
  getListReserveTradesOrder() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((customers) => {
        if (!customers.length) {
          this.data = [];
          this.initialData = [];
          this.currentIndexItem = 0;
          return;
        }
        this.allCustomerInfo = customers.map((customer) => ({
          customerName: customer.customerName,
          accountNo: customer.accountNumber,
        }));
        const tagPageOrder = ETagPageOrder.PREORDER;

        this.store
          .select(selectAllBrokerLevelListByBrokerView$)
          .pipe(takeUntil(this._destroy))
          .subscribe((allBrokerLevelList) => {
            const currentBrokerCode = this.route.snapshot.queryParams['brokerId'];
            this.store.dispatch(
              getListTodayOrder({
                accountNumbers: [],
                brokerCode: this.getAllChildBrokerCodes(allBrokerLevelList, currentBrokerCode),
                tagPageOrder,
              })
            );
          });
      });
  }

  listenChangeTradesOrder(trades: IOrderListResponse[]) {
    const codeEvent = [ECodeEventOrder.F03113, ECodeEventOrder.F03114].join('|');

    const accountNo = trades.map((t) => t.accountNumber).join('|');
    if (!accountNo) return;

    const message = {
      destination: DESTINATION_MESSAGE.order,
      body: `sub${BODY_MESSAGE.ORDER_ALL}${codeEvent}.${accountNo}`,
    };

    if (this.messageOrder) {
      const messageUnSub = {
        ...message,
        body: this.messageOrder,
      };

      this.socketJs.sendMessage(messageUnSub.destination, messageUnSub.body);
    }

    this.messageOrder = `unsub${BODY_MESSAGE.ORDER_ALL}${codeEvent}.${accountNo}`;

    const topic = TOPIC.order;

    this.socketJs.initSocket(
      message,
      topic,
      (data) => {
        const { event_code, ord_no, stk_cd, acnt_no, sub_no } = JSON.parse(data);
        let message = '';
        const status = [ECodeEventOrder.F03111, ECodeEventOrder.F03113, ECodeEventOrder.F03116].includes(
          event_code as ECodeEventOrder
        )
          ? this.i18.translate('MES-186')
          : this.i18.translate('MES-185');

        if ([ECodeEventOrder.F03113, ECodeEventOrder.F03114].includes(event_code as ECodeEventOrder)) {
          message = this.i18.translate('MES-628');
        }

        message = message
          .replace('{{accNo}}', acnt_no)
          .replace('{{subNo}}', sub_no)
          .replace('{{code}}', stk_cd)
          .replace('{{status}}', status)
          .replace('{{orderNo}}', ord_no);

        if (message) {
          this.messageService.success(message);
        }
      },
      true
    );
  }

  getOrderPrice(price: number, typeTrades: string) {
    const typeNotShowPrice = ['LO', 'STOP LO'];
    if (typeNotShowPrice.includes(CONVERT_TYPE_TRADES[typeTrades])) {
      return price / 1000;
    }
    return CONVERT_TYPE_TRADES[typeTrades];
  }

  /**
   * check có phải t7 cn ko sẽ chuyển về t6 gần nhất
   */
  checkIsTodayAndIsDayOff() {
    this.store
      .select(selectDateReserveTradesOrder$)
      .pipe(takeUntil(this._destroy))
      .subscribe((date) => {
        const today = new Date();
        const formattedDate = dateToDMY(today);
        this.disableLoadingBtn = date !== formattedDate; // disable btn nếu kp ngày hôm nay

        // Nếu hôm nay là t7, Cn sẽ update ngày t6 gần nhất
        if (date === formattedDate && (today.getDay() === 0 || today.getDay() === 6)) {
          const dateObj = new Date(today);
          const previousFriday = getPreviousFriday(dateObj);
          this.currentDate = ConvertToDate(previousFriday);
          this.store.dispatch(updateDateInReserveTradeOrder({ date: previousFriday }));
        }
      });
  }

  getStatusTradeOrder() {
    const reserveTradeCodes = this.data.map((d) => d.accountNumber);
    if (!reserveTradeCodes.length) return;
    if (this.destroyTradeOder$) {
      this.destroyTradeOder$.next();
    }
    const uniqueReserveTradeCode = [...new Set(reserveTradeCodes)];
    const reserveTradeMessage = uniqueReserveTradeCode.join('|');
    this.orderStatusEventService
      .changeOrderStatusEvent(`SHA.${reserveTradeMessage}`)
      .pipe(takeUntil(this.destroyTradeOder$))
      .subscribe((event: IEventSocketTradeOrder) => {
        if (!event) return;
        const { acnt_no, ordNo, event_code, stk_qty, stk_price, date } = event;

        const convertReserveTradeToDate = (date: string) => {
          const reserveTradeYear = date.slice(0, 4);
          const reserveTradeMonth = date.slice(4, 6);
          const reserveTradeDay = date.slice(6, 8);
          return reserveTradeDay + '/' + reserveTradeMonth + '/' + reserveTradeYear;
        };
        this.data = this.data.map((item) => {
          if (item.accountNumber === acnt_no && item?.ordNo === ordNo) {
            item.status = CONVERT_STATUS_TRADED_ORDER_TO_VALUE[event_code];
            item.matchedVolume = stk_qty;
            item.matchedPrice = stk_price;
            item.dateTrades = convertReserveTradeToDate(date);
            item.date = item.timeTrades + ' - ' + item.dateTrades;
            item.tradeStatus = +item.orderVolume === +stk_qty ? EStatusTrades.MATCHED : EStatusTrades.PARTIAL_MATCH;
          }
          return item;
        });
        this.initialData = structuredClone(this.data);
      });
  }

  getTradesValue(tradeValue: number | null, typeTrades: string, tradeStatus: number) {
    if (!tradeValue) return null;

    if (tradeStatus === EStatusTrades.PARTIAL_MATCH || tradeStatus === EStatusTrades.ACTIVATED) {
      return tradeValue;
    } else return '-';
  }

  /**
   * dateChange
   * @param {IDateChangeItem} data
   */
  dateChange(data: IDateChangeItem) {
    const { date } = data;
    const cleanString = (str: string) => str.trim().normalize();
    this.store
      .select(selectDateReserveTradesOrder$)
      .pipe(take(1))
      .subscribe((dateStore) => {
        if (cleanString(dateStore) === cleanString(date as string)) return;
        this.store.dispatch(updateDateInReserveTradeOrder({ date: date as string }));
      });
  }

  /**
   * searchData
   */
  searchData() {
    let searchReserveData = [];
    this.loadingService.show();
    if (this.searchValue) {
      const searchValue = this.searchValue.toString().trim().toLowerCase();
      searchReserveData = this.allTradesOrderItems.filter((item: any) =>
        this.containsSearchValueReserveTrades(item, removeVietnameseTones(searchValue))
      );

      this.isSearch = true; // Set isSearch to true when searching
    } else {
      searchReserveData = [...this.allTradesOrderItems];

      this.isSearch = false; // Set isSearch to false when not searching
    }
    this.searchedData = searchReserveData;

    setTimeout(() => {
      this.loadingService.hide();
    }, 1000);

    // this.searchedData <= pageSize
    if (!this.searchedData.length || this.searchedData.length <= TOTAL_ITEM_IN_LIST) {
      this.currentIndexItem = this.searchedData.length;
      const itemReserve = { ...this.searchedData[this.searchedData.length - 1] };

      itemReserve.isShowLoading = false;
      this.searchedData[this.searchedData.length - 1] = itemReserve;
      this.isScrolling = false;
      this.data = [...this.searchedData];
    }

    // this.searchedData > pageSize
    if (this.searchedData.length > TOTAL_ITEM_IN_LIST) {
      const dataClone = this.searchedData.slice(0, TOTAL_ITEM_IN_LIST);
      const itemReserve = { ...dataClone[dataClone.length - 1] };
      if (!itemReserve.isShowLoading && this.currentIndexItem !== this.searchedData.length) {
        itemReserve.isShowLoading = true;
        dataClone[dataClone.length - 1] = itemReserve;
      }
      this.currentIndexItem = 0;
      this.gridRef?.scrollTopEvent();
      this.data = [...dataClone];
    }
  }

  /**
   * containsSearchValue
   * @param item
   * @param searchValue
   */
  containsSearchValueReserveTrades(item: any, searchValue: string): boolean {
    return (
      removeVietnameseTones(item.accountNumber?.toString().trim().toLowerCase())?.includes(searchValue) ||
      removeVietnameseTones(item.customerName?.toString().trim().toLowerCase())?.includes(searchValue) ||
      item.dateTrades?.toString().toLowerCase()?.includes(searchValue) ||
      removeVietnameseTones(item.activationDate?.toString().toLowerCase())?.includes(searchValue) ||
      removeVietnameseTones(item.code?.toString().trim().toLowerCase())?.includes(searchValue)
    );
  }

  updateListForFilter() {
    const { optionSelection } = this.dataFilter;

    const {
      numberAccountOptionsSelect = [],
      codeOptionsSelect = [],
      typeTradesOptionsSelect = [],
      conditionTradesOptionsSelect = [],
      statusTradesOptionsSelect = [],
      // traderOptionsSelect = [],
      orderChannelOptionSelect = [],
    } = optionSelection;

    const isSelect = (value: string | number, data: (string | number)[] | null): boolean => {
      return data === null || data.includes(value);
    };

    let accountNumberOptionsClone!: IOptionSelect[];
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(take(1))
      .subscribe((customers) => {
        accountNumberOptionsClone = customers.map((customer) => ({
          label: `${customer.accountNumber} - ${customer.customerName}`,
          value: `${customer.accountNumber}`,
          isSelect: numberAccountOptionsSelect?.length
            ? isSelect(customer.accountNumber, numberAccountOptionsSelect)
            : true, // [] -> all
        }));
      });

    let stockCodeOptionClone!: IOptionSelect[];
    this.store
      .select(selectAllStockList$)
      .pipe(
        take(1),
        filter((stockCode) => stockCode.length > 0),
        map((stockCode) => {
          stockCodeOptionClone = stockCode.map((code) => ({
            label: `${code.id} : ${code.stock}`,
            value: code.id,
            isSelect: codeOptionsSelect?.length ? isSelect(code.id, codeOptionsSelect) : true, // [] -> all
          }));
        })
      )
      .subscribe();

    const typeTradesOptionsClone = typeTradesDropdownOption.map((code) => ({
      label: code.label,
      value: code.value,
      isSelect: typeTradesOptionsSelect?.length ? isSelect(code.value, typeTradesOptionsSelect) : true, // [] -> all
    }));

    const statusTradesOptionsClone = statusTradesOpenTradesOption
      .filter(
        (code) =>
          code.value === EStatusTradeFilter.NOT_ACTIVATED ||
          code.value === EStatusTradeFilter.ACTIVATED ||
          code.value === EStatusTradeFilter.CANCELED ||
          code.value === EStatusTradeFilter.REJECTED
      )
      .map((code) => ({
        label: code.label,
        value: code.value,
        isSelect: statusTradesOptionsSelect?.length ? isSelect(code.label, statusTradesOptionsSelect) : true, // [] -> all
      }));

    const conditionTradesOptionsClone = conditionTradesOptions.map((code) => ({
      label: code.label,
      value: code.value,
      isSelect: conditionTradesOptionsSelect?.length ? isSelect(code.value, conditionTradesOptionsSelect) : true, // [] -> all
    }));

    const orderChannelClone = orderChannelOptions.map((code) => ({
      label: code.label,
      value: code.value,
      isSelect: orderChannelOptionSelect?.length ? isSelect(code.value, orderChannelOptionSelect) : true, // [] -> all
    }));

    return {
      accountNumberOptionsClone,
      stockCodeOptionClone,
      typeTradesOptionsClone,
      statusTradesOptionsClone,
      conditionTradesOptionsClone,
      orderChannelClone,
    };
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'filter':
        {
          const {
            accountNumberOptionsClone,
            stockCodeOptionClone,
            typeTradesOptionsClone,
            statusTradesOptionsClone,
            conditionTradesOptionsClone,
            orderChannelClone,
          } = this.updateListForFilter();

          const ref = this.openFilter(TradesOrderFilterComponent, {
            width: '800px',
            data: {
              config: {
                code: true,
                status: true,
                typeTrades: 'dropdown',
                statusTrades: 'checkbox',
                conditionTrades: true,
                numberAccount: true,
                dateOrderTrades: {
                  position: 'left',
                  show: true,
                },
                dateActivation: true,
                trader: 'right',
                orderChannel: 'right',
                tradesValue: true,
                revenue: true,
                netTradesFee: true,
                brokerageCommission: false,
              } as IOptionConfigFilter,
              listOption: {
                numberAccountOptions: accountNumberOptionsClone,
                codeOptions: stockCodeOptionClone,
                typeTradesOptions: typeTradesOptionsClone,
                statusTradesOptions: statusTradesOptionsClone,
                conditionTradesOptions: conditionTradesOptionsClone,
                // traderOptions: tradersOptions,
                orderChannel: orderChannelClone,
              } as IListOptionConfig,
              optionFilter: this.dataFilter,
            },
          });
          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe((v) => {
              if (!v) return;
              this.applyFilter(v);
            });
        }
        break;

      case 'loading':
        this.getListReserveTradesOrder();
        break;

      default:
        break;
    }
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;
    if (type === 'save') {
      if (!this.isFilter && !optionFilter?.isFilter) return;
      this.store.dispatch(setFilterReserveTrades({ params: optionFilter }));
      this.store.dispatch(resetSearchValueListOrder());
    } else if (type === 'default') {
      this.store.dispatch(setFilterReserveTrades({ params: initialTradesOrderState.reserveTraderFilter }));
    }
  }

  /**
   * @param data
   * @param optionFilter
   */
  saveFunc(data: any[], optionFilter: any) {
    const { buySellStatus, activeStatus, optionSelection } = optionFilter;

    const newListFilter = data.filter((init) => {
      const isBuySellStatus = buySellStatus.length ? buySellStatus.includes(init.status) : true;
      const isActiveStatus = activeStatus.length ? activeStatus.includes(init.tradeStatus) : true;
      return (
        isActiveStatus &&
        isBuySellStatus &&
        this.updateOptionSelection(optionSelection, init) &&
        this.updateRangeFilterReserveTrades(optionFilter, init)
      );
    });
    return newListFilter;
  }

  /**
   * ContextMenuClick
   * @param {GridContextMenu<any>} param0 { event, element, source }
   */
  contextMenuClick({ event, element, source }: GridContextMenu<any>) {
    event.preventDefault();

    const origin = event.target as HTMLElement;
    this.openSelectOption(origin, element);
  }

  /**
   * OpenSelectOption
   * @param {HTMLElement} origin  origin
   * @param {any} element element
   */
  openSelectOption(origin: HTMLElement, element: any) {
    let options = [
      {
        url: './assets/icons/arrow-2-direction.svg',
        label: 'MES-211',
        tag: 'detail',
        isDisable: false,
      },
      {
        url: './assets/icons/edit-2.svg',
        label: 'MES-212',
        tag: 'edit',
        isDisable: false,
      },
      {
        url: './assets/icons/x-cross-red.svg',
        label: 'MES-213',
        tag: 'cancel',
        classCustom: 'color-red-text',
        isDisable: false,
      },
    ];

    if (element.tradeStatus === EStatusTrades.REJECTED)
      options = options.filter((option) => option.tag !== 'edit' && option.tag !== 'cancel');

    const today = new Date();
    const todayFormatted = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1)
      .toString()
      .padStart(2, '0')}/${today.getFullYear()}`;
    const isToday = element.dateTrades === todayFormatted;

    const isDisable =
      (!isToday && element.tradeStatus === EStatusTrades.NOT_ACTIVATED) ||
      element.tradeStatus === EStatusTrades.ACTIVATED ||
      element.tradeStatus === EStatusTrades.CANCELED ||
      element.tradeStaus === EStatusTrades.REJECTED;

    options.forEach((t) => {
      if (t.tag === 'cancel') t.isDisable = isDisable;
      if (t.tag === 'edit') t.isDisable = true;
    });
    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 3,
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        templateRefExp: this.contextMenuRef,
        searchKey: 'tag',
        options,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe({
      next: (v) => {
        if (!v.data) return;
        const { item } = v.data;
        if (!item.length) return;
        const { tag } = item[0];
        switch (tag) {
          case 'detail':
            this.openDetailOpenTrades(element);
            break;

          case 'cancel':
            this.cancelAdvanceTrades(element);
            break;
          default:
            break;
        }
      },
    });
  }

  /**
   * OpenMoreAction
   * @param {any} event
   */
  openMoreAction(event: any) {
    const { html, element } = event;
    this.openSelectOption(html, element);
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.export]);
  }

  /**
   * updateRangeFilterReserveTrades
   * @param optionFilter
   * @param init
   * @returns {any} new list
   */
  updateRangeFilterReserveTrades(optionFilter: IFilterOpenTradesParam, init: any): any {
    const { netTradesFee, revenue, valueTrades, brokerageCommission, dateActiveRange } = optionFilter;

    const getValueMatch = (value: IRangeFilter, initField: number) => {
      if (value?.start && value?.end) {
        return initField >= +value.start && initField <= +value.end;
      } else if (value?.start) {
        return initField >= +value.start;
      } else if (value?.end) {
        return initField <= +value.end;
      } else {
        return true;
      }
    };

    const isValueReserveTradesMatch = getValueMatch(valueTrades, +init.tradeValue);

    const isBrokerCommissionMatchReserve = getValueMatch(brokerageCommission, +init.brokerageCommission);

    const isRevenueMatchReserve = getValueMatch(revenue, +init.total);

    const isNetReserveTradesFee = getValueMatch(netTradesFee, +init.netTransactionFee);

    const dateActive = init.activationDate
      ? new Date(init.activationDate.split('/').reverse().join('-'))
      : new Date('');
    const startDate = new Date(dateActiveRange.start ? dateToYMD(dateActiveRange.start) : '');
    const endDate = new Date(dateActiveRange.end ? dateToYMD(dateActiveRange.end) : '');

    const compareActiveRange =
      (dateActiveRange.start ? dateActive > startDate : true) && (dateActiveRange.end ? dateActive < endDate : true);
    const isDataActiveInRange = dateActive ? compareActiveRange : true;

    return (
      isValueReserveTradesMatch &&
      isBrokerCommissionMatchReserve &&
      isRevenueMatchReserve &&
      isNetReserveTradesFee &&
      isDataActiveInRange
    );
  }

  /**
   * UpdateOptionSelection
   * @param optionSelection
   * @param init
   * @returns  {any} new list
   */
  updateOptionSelection(optionSelection: IOptionSelection, init: any) {
    const {
      codeOptionsSelect,
      typeTradesOptionsSelect,
      numberAccountOptionsSelect,
      traderOptionsSelect,
      orderChannelOptionSelect,
      conditionTradesOptionsSelect,
    } = optionSelection;

    const isCodeMatchReserve = (codeOptionsSelect ?? []).length ? (codeOptionsSelect ?? []).includes(init.code) : true;

    const isTypeReserveTradeMatch = (typeTradesOptionsSelect ?? []).length
      ? (typeTradesOptionsSelect ?? []).includes(init.typeTrades)
      : true;

    const isNumberAccountMatchReserve = (numberAccountOptionsSelect ?? []).length
      ? (numberAccountOptionsSelect ?? []).some((item) => item.includes(init.accountNumber))
      : true;

    const isTraderMatchReserve = (traderOptionsSelect ?? []).length
      ? (traderOptionsSelect ?? []).includes(init.trader)
      : true;

    const isOrderChanelMatchReserve = (orderChannelOptionSelect ?? []).length
      ? (orderChannelOptionSelect ?? []).includes(init.orderChannel)
      : true;

    const isConditionReserveTradesMatch = (conditionTradesOptionsSelect ?? []).length
      ? (conditionTradesOptionsSelect ?? []).includes(init.conditionTrades)
      : true;

    return (
      isCodeMatchReserve &&
      isTypeReserveTradeMatch &&
      isNumberAccountMatchReserve &&
      isTraderMatchReserve &&
      isOrderChanelMatchReserve &&
      isConditionReserveTradesMatch
    );
  }

  /**
   * OpenDetailOpenTrades
   * @param {any} element element
   */
  openDetailOpenTrades(element: any) {
    const today = new Date();
    const todayFormatted = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1)
      .toString()
      .padStart(2, '0')}/${today.getFullYear()}`;
    const isToday = element.dateTrades === todayFormatted;

    this.dialogService.openRightDialog(OpenTradesDetailComponent, {
      width: '530px',
      data: {
        trade: { ...element, typeTagOrder: ETagPageOrder.PREORDER },
        isHideEditTradeOrderBtn: true,
        isHideCancelTradeOrderBtn: !isToday,
        isHideBrokerCommission: true,
      },
    });
  }

  /**
   * CancelAdvanceTrades
   * @param element
   */
  cancelAdvanceTrades(element: any) {
    const ref = this.dialogService.openPopUp(DeleteNotificationComponent, {
      data: {
        isActive: false,
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-225',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-226',
        },
        isReverse: true,
      },
      height: '260px',
      width: '360px',
      panelClass: ['popup-confirm', 'not-padding-popup'],
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v) => {
        if (v === 'save') {
          this.store
            .select(selectCurrentBrokerView$)
            .pipe(take(1))
            .subscribe((user) => {
              // Extract and format date to yyyyMMdd
              const formattedDate = element.activateDate.replace(/-/g, '');
              const payload = {
                hts_user_id: user.userName,
                hts_user_nm: user.brokerCode,
                account_list: [
                  {
                    acnt_no: element.accountNumber.split(' - ')[0],
                    sub_no: element.subAccount,
                  },
                ],
                ord_no: element.ordNo.toString(),
                ord_frct_dt: formattedDate,
                lang_code: 'V',
                ord_mdm_tp: '01',
              };

              this.reserveTradeStore.cancelAdvanceTradeOrder({ payload });
            });
        }
      });
  }

  /**
   * getBrokerInfo
   */
  getBrokerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((currentBroker) => {
        if (!currentBroker) return;
        this.currentBrokerCode = currentBroker.brokerCode;

        combineLatest([
          this.store.select(selectInfoUserLogin$),
          this.store.select(selectAllBrokerLevelListByBrokerView$),
        ])
          .pipe(takeUntil(this._destroy))
          .subscribe(([userList, brokers]) => {
            const queryParams = this.route.snapshot.queryParams;

            if (!userList) return;

            const brokerConvert = updateBorkerName([...brokers], [...userList]);

            const subBroker = brokerConvert.map((broker) => {
              return {
                brokerCode: `${broker.brokerCode}`,
                name: `${broker.brokerName}`,
                isSelect: queryParams['brokerId']
                  ? broker.brokerCode === queryParams['brokerId']
                  : broker.brokerCode === currentBroker.brokerCode,
              };
            });

            this.LIST_MG = [...subBroker];
            const broker = this.LIST_MG.find((t) => t.isSelect);

            if (broker) {
              this.brokerInfo = {
                label: broker.name ?? '',
                value: (broker['brokerCode'] as string) ?? '',
              };
            }
          });
      });
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    const queryParams = this.route.snapshot.queryParams;

    const elementRef = new ElementRef(document.querySelector(`.broker-icon`));
    const elementWidth = elementRef.nativeElement as HTMLElement;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRef as any,
      width: elementWidth.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i['isSelect']);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBroker = userList.find((user) => user.brokerCode === itemSelected['brokerCode']);
          const subBroker = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelected['brokerCode']);
          if (queryParams['brokerId'] === itemSelected['brokerCode']) return;
          if (subBroker && !currentBroker) {
            const brokerCode = subBroker['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
            this.store.dispatch(getCustomerGroupsList({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (currentBroker) {
            if (this.currentBrokerCode === currentBroker.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBroker.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
              this.store.dispatch(
                getCustomerGroupsList({ brokerCode: currentBroker.brokerCode, isPrimaryBrokerCode: true })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBroker }));
          }

          if (subBroker) {
            this.LIST_MG = this.LIST_MG.map((broker) => ({
              ...broker,
              isSelect: broker['brokerCode'] === subBroker['brokerCode'],
            }));

            this.brokerInfo = {
              label: subBroker['name'] ?? '',
              value: (subBroker['brokerCode'] as string) ?? '',
            };
          }
          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));

          const subBrokerCode = subBroker ? subBroker['brokerCode'] : null;
          this.router.navigate([], {
            queryParams: {
              ...queryParams,
              brokerId: currentBroker ? currentBroker.brokerCode : subBrokerCode,
            },
          });
        });
    });
  }

  /**
   * Lấy list brokerCode con của brokerCode và brokerCode đó
   * @param {IAllLevelOfBroker[]} allLevelOfBrokerList
   * @param {string} currentBrokerCode
   * @returns {string[]}
   */
  getAllChildBrokerCodes(allLevelOfBrokerList: IAllLevelOfBroker[], currentBrokerCode: string): string[] {
    const currentBroker = allLevelOfBrokerList.find((b) => b.brokerCode === currentBrokerCode);
    const result: string[] = [];

    const collectChildBrokerCodes = (brokerItem: IAllLevelOfBroker) => {
      for (const child of brokerItem.children) {
        result.push(child.brokerCode);
        collectChildBrokerCodes(child);
      }
    };

    if (currentBroker) {
      result.push(currentBroker.brokerCode);
      collectChildBrokerCodes(currentBroker);
    }

    return result;
  }

  formatActivationDate = (input: string): string => {
    if (input.length !== 8) return input;

    const year = input.slice(0, 4);
    const month = input.slice(4, 6);
    const day = input.slice(6, 8);

    return `${day}/${month}/${year}`;
  };
}
