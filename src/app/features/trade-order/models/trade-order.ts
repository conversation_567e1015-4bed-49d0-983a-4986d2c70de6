type TradeTypeConfigFilter = 'dropdown' | 'checkbox' | null;

export interface IOptionConfigFilter {
  code?: boolean; //Mã CK
  status?: boolean; // Lệnh
  typeTrades?: TradeTypeConfigFilter; //Loại lệnh
  statusTrades?: TradeTypeConfigFilter; // Trạng thái lệnh
  statusConfirmTrades?: TradeTypeConfigFilter; // Trạng thái xác nhận lệnh
  conditionTrades?: boolean; //Điều kiện lệnh
  numberAccount?: boolean; // Số tài khoản
  dateOrderTrades?: {
    position: string | null;
    show: boolean;
    place?: string | null;
  }; // Ngày đặt lệnh
  dateMatchedTrades?: boolean; // Ngày khớp lệnh
  dateActivation?: boolean; // Ngày kích hoạt
  trader?: 'left' | 'right' | null; // Người đặt lệnh
  orderChannel?: 'left' | 'right' | null; // Kênh đặt lệnh
  tradesValue?: boolean; // GTGD
  revenue?: boolean; // Doanh thu phí
  netTradesFee?: boolean; // Net giao dịch
  brokerageCommission?: boolean; // Hoa Hồng MG
}

export interface IListOptionConfig {
  codeOptions?: IListOptions[];
  typeTradesOptions?: IListOptions[];
  statusTradesOptions?: IListOptions[];
  statusConfirmTradesOptions?: IListOptions[];
  numberAccountOptions?: IListOptions[];
  traderOptions?: IListOptions[];
  orderChannel?: IListOptions[];
  conditionTradesOptions?: IListOptions[];
  [key: string]: IListOptions[] | undefined;
}

export interface IOptionList {
  customerName?: string;
  customerGroup?: string;
  isSelect?: boolean;
  [key: string]: string | boolean | undefined;
}

export interface IListOptions {
  label?: string;
  [key: string]: any;
}

export enum EOrderType {
  LO = 'LO',
  MP = 'MP',
  ATC = 'ATC',
  ATO = 'ATO',
  PLO = 'PLO',
  MTL = 'MTL',
  FAK = 'FAK',
  FOK = 'FOK',
  STOP_LO = 'STOP LO',
  STOP_MP = 'STOP MP',
}

export const CONVERT_STK_ORD_TYPE: { [key: string]: string } = {
  [EOrderType.LO]: '01',
  [EOrderType.MP]: '02',
  [EOrderType.ATO]: '03',
  [EOrderType.ATC]: '04',
  [EOrderType.FOK]: '07',
  [EOrderType.FAK]: '08',
  [EOrderType.MTL]: '09',
  [EOrderType.PLO]: '15',
};

export const CONVERT_TYPE_TRADES: { [key: string]: string } = {
  ['01']: EOrderType.LO,
  ['02']: EOrderType.MP,
  ['03']: EOrderType.ATO,
  ['04']: EOrderType.ATC,
  ['07']: EOrderType.FOK,
  ['08']: EOrderType.FAK,
  ['09']: EOrderType.MTL,
  ['15']: EOrderType.PLO,
};

export enum EOrderCategory {
  BUY = 'BUY',
  SELL = 'SELL',
}

export enum EStockExchange {
  HOSE = 'HOSE',
  HNX = 'HNX',
  UPCOM = 'UPCOM',
  HSX = 'HSX',
}

export enum ETradingConditions {
  BEFORE_MORNING = 'BEFORE_MORNING',
  BEFORE_AFTERNOON = 'BEFORE_AFTERNOON',
  BEFORE_ATO = 'BEFORE_ATO',
  BEFORE_ATC = 'BEFORE_ATC',
}

export interface IOrderConfirm {
  isDerivatives: boolean;
  type: EOrderType;
  category: EOrderCategory;
  price: number;
  advanced: boolean;
  volume: number;
  stockCompany: string;
  targets: IOptionList[]; // typeof LIST_OF_CUSTOMER
  activate?: number;
  derivativeCondition?: ETradingConditions;
  derivativeDate?: Date;
}

export const CONVERT_TRADING_CONDITIONS_TO_LABEL: { [key: string]: string } = {
  [ETradingConditions.BEFORE_MORNING]: 'MES-260',
  [ETradingConditions.BEFORE_AFTERNOON]: 'MES-261',
  [ETradingConditions.BEFORE_ATO]: 'MES-262',
  [ETradingConditions.BEFORE_ATC]: 'MES-263',
};

export interface IFilterOpenTradesParam {
  isFilter: boolean;
  buySellStatus: number[] | null;
  activeStatus: number[] | null;
  isStopTypeTrades: string[] | null;
  valueTrades: IRangeFilter;
  revenue: IRangeFilter;
  netTradesFee: IRangeFilter;
  brokerageCommission: IRangeFilter;
  optionSelection: IOptionSelection;
  dateActiveRange: IRangeFilter;
  dateRange: IRangeFilter;
}

export interface IOptionSelection {
  codeOptionsSelect: string[] | null;
  typeTradesOptionsSelect: string[] | null;
  statusTradesOptionsSelect: string[] | null;
  numberAccountOptionsSelect: string[] | null;
  traderOptionsSelect: string[] | null;
  conditionTradesOptionsSelect: string[] | null;
  orderChannelOptionSelect: string[] | null;
  statusTradesOptionsSelectValue: number[] | null;
  orderChannelOptionSelectValue: string[] | null;
  typeTradesOptionsSelectValue: string[] | null;
  numberAccount?: string[];
}

export interface IRangeFilter {
  start: string | null;
  end: string | null;
}

export interface ITradeOrderState {
  searchValue: string; // Search lệnh mở / lệnh khớp / lệnh đặt trước
  searchValueConfirmOrder: string; // Search màn xác nhận lệnh
  openTradeFilter: IFilterOpenTradesParam;
  matchTraderFilter: IFilterOpenTradesParam;
  conditionTraderFilter: IFilterOpenTradesParam;
  reserveTraderFilter: IFilterOpenTradesParam;
  groupTraderFilter: IFilterOpenTradesParam;

  // Open trades order
  brokerCode: string;
  indexOpenTrades: number;
  openTradesOrderDate: string;

  // Matched trades order
  indexMatchedTrades: number;
  matchedTradesOrderDate: string;

  // Reserve trades order
  reserveTradesOrderDate: string;

  // Order List
  orderList: IOrderListResponse[];
  filterOrderParam: IFilterOrderParam;

  accountNumbers: string[];
  brokerCodeIds: string[];
  tagPageOrder: number;

  // trade order management

  tradesOrderManagementDate: string;
  indexTradeOrderManagement: number;
  tradeOrderManagementFilter: IFilterOpenTradesParam;

  confirmOrderTradesList: IConfirmOrderTrades[];
  totalPages: number;
}

export interface IConfirmOrderTrades {
  dateTrades: string;
  timeTrades: string;
  accountNumber: string;
  subAccount: string;
  status: string;
  ordNo: string;
  typeTrades: string;
  code: string;
  orderPrice: string;
  matchedPrice: string;
  matchedVolume: string;
  orderVolume: string;
  tradeStatus: string;
  trader: string;
  orderChannel: string;
  totalPages?: number;
}

export interface IEventSocketTradeOrder {
  date: string; // Ngày sự kiện (yyyymmdd)
  event_seqno: string; // Số sequence
  event_code: string; // Mã sự kiện
  acnt_no: string; // Số TK
  sub_no: string; // Số tiểu khoản
  stk_cd: string; // Mã CK
  stk_qty: string; // Khối lượng Khớp
  stk_price: string; // Giá
  ordNo: string; // Số hiệu lệnh
}

export interface IOpenTradesOrderResponse {
  dateTrades: string;
  timeTrades: string;
  accountNumber: string;
  subAccount: string;
  typeTrades: string;
  code: string;
  activationPrice: number | null;
  orderPrice: number;
  matchedPrice: number;
  matchedVolume: number;
  orderVolume: number;
  tradeValue: number | null;
  tradeStatus: number;
  trader: string | null;
  orderChannel: string | null;
  status: number;
}

export interface IMatchedTradesOrderResponse {
  dateTrades: string;
  timeTrades: string;
  accountNumber: string;
  subAccount: string;
  customerName: string;
  typeTrades: string;
  code: string;
  status: string;
  orderPrice: number;
  matchedPrice: number;
  matchedVolume: number;
  tradeValue: number;
  revenueFee: number;
  netTransactionFee: number;
  trader: string;
  orderChannel: string;
}

export interface IOptionStock {
  label: string;
  value: string;
  id: string;
  stoke: string;
}

export interface IStockItemInList {
  stock: string;
  id: string;
  name: string;
  shortName: string;
}

export interface IOrderCategoryInfo {
  number: number; // Bán || Mua tôi đa
  volume: number; // KL Bán || Mua tôi đa
  marginRate?: number; // Tỉ Lệ CMR
  debt?: number; // Tổng dư nợ
}

export interface IOrderPayload {
  // Cố định
  hts_user_id: string;
  hts_user_nm: string;
  cli_ip_addr?: string;
  cli_mac_addr: string;
  mkt_type: string;
  account_list: IAccountListOrder[];
  stk_cd: string;
  stk_ord_tp: string;
  ord_qty: string;
  ord_pri: string;
  bank_cd: string; // Fix cứng: 9999
  bank_acnt_no?: string; // Fix cứng: 9999
  lang_code: string; // Fix cứng: V
  ord_mdm_tp: string; // Fix cứng: 01

  // Lệnh mua đặt trước
  tel_no?: string;
}

export interface IEditOrderPayload {
  hts_user_id: string;
  hts_user_nm: string;
  cli_ip_addr?: string;
  cli_mac_addr: string;
  acnt_no: string;
  sub_no: string;
  idno: string;
  brch_cd: string | null;
  ord_no: string;
  ord_qty: string;
  ord_pri: string;
  ord_mdm_tp: string;
  stk_cd: string;
  mkt_tp: string;
  langCode: string; // Fix cứng V
  stkOrdTp: string;
  bankCd: string; // Fix cứng: 9999
}

export interface ICancelOrderPayload {
  hts_user_id: string;
  hts_user_nm: string;
  cli_ip_addr?: string;
  cli_mac_addr?: string; // (Thường)
  account_list: IAccountListOrder[];
  ord_no: string;
  bank_cd?: string; // (Thường) Fix cứng: 9999
  brch_cd?: string | null; // (Thường)
  ord_frt_dt?: string; // (Đặt trước) Ngày đặt lệnh
  lang_code?: string; // (Đặt trước) Fix cứng V
  ord_mdm_tp: string; // Fix cứng: 01
}

export interface IBuyableInfoPayload {
  acnt_no: string;
  sub_no: string;
  bank_cd: string;
  stk_cd: string;
  mkt_trd_tp: string;
  ord_pri: string;
}

export interface IAccountListOrder {
  acnt_no: string;
  sub_no: string;
  idno?: string; // Cần khi đặt, hủy lệnh thường
}

export interface IPlaceOrderResponse {
  error_code: string;
  error_desc: string;
  success: boolean;
  count: number;
  total_record: string;
  data_list: IDataListPlaceOrderResponse[];
}

export interface IDataListPlaceOrderResponse {
  acountNo: string;
  subNo: string;
  idno: string;
  new_ord_no: string;
  stock_code: string;
  oms_ord_no: string;
  dummy_field: string;
}

export interface IPayloadOrderList {
  accountNumbers: string[];
  brokerCode: string[];
  tagPageOrder: number;
}

export interface ICustomerInfos {
  customerName: string;
  accountNo: string;
}

export interface IOrderListResponse {
  dateTrades: string;
  timeTrades: string;
  activateDate: string;
  accountNumber: string;
  subAccount: string;
  typeTrades: string;
  code: string;
  activationPrice: number;
  orderPrice: number;
  preOrderSession: string; // Điều kiện lệnh
  matchedPrice: number;
  matchedVolume: number;
  orderVolume: number;
  mthAmt: number; // GTGD
  tradeStatus: number;
  trader: string;
  orderChannel: string;
  ordNo: number;
  status: number;
  fstNo: number; // Số hiệu lệnh gốc
  revenueFee: number;
  createDt: string;
  netTransactionFee: number;
  modCanTp: string;
  bnhCd: string;
  customerName?: string;
  nmthQty?: number;
  orgOrdQty?: number;
}

export interface IFilterOrderParam {
  accountNumbers: string[];
  stockCodes: string[];
  sellBuyTps: string[];
  typeTrades: string[];
  sessions: number[];
  tradeStatus: number[];
  fromDate: string | null;
  toDate: string | null;
  orderChannel: string[];
  tradeValue: {
    from: number | null;
    to: number | null;
  };
  revenueFee: {
    from: number | null;
    to: number | null;
  };
  netTransactionFee: {
    from: number | null;
    to: number | null;
  };
}

export interface IStockQuotePayload {
  symbol: string;
  fetchCount: string;
  lastIndex: string;
  lastSize?: string;
}

export interface IStockQuoteListResponse {
  lastIndex: 0;
  lastSize: 0;
  data: IDataOfStockQuoteList[];
}

export interface IDataOfStockQuoteList {
  o: string; // Giá mở cửa
  ti: string; // Thời gian khớp
  c: string; // Giá hiện tại/ giá khớp
  ch: string; // Thay đổi giá
  h: string; // Giá cao nhất
  l: string; // Giá thấp nhất
  mb: string; // Loại lệnh
  mv: string; // Khối lượng khớp
  r: string; // % thay đổi
  va: string; // Tổng giá trị khớp lệnh
  vo: string; // Tổng khối lượng khớp lệnh
  cl?: string;
}

export interface IDataQuoteSummaryResponse {
  price: number; // Bước giá
  volume: number; // Khối lượng khớp ở bước giá này
  rate: number; // tỉ lệ khối lượng khớp ở bước giá này so với tổng khối lượng khớp của mã
  buyVolume: number; // khối lượng khớp mua ở bước giá này
  sellVolume: number; // khối lượng khớp bán ở bước giá này
  bsVolume: number; // khối lượng khớp mua/bán ở bước giá này
  buyRate: number; // tỉ lệ khớp mua ở bước giá này
  sellRate: number; // tỉ lệ khớp bán ở bước giá này
  bsRate: number; // tỉ lệ khớp bán ở bước giá này
}

export interface IPayloadGetDebtInAsset {
  accountNumber: string;
  subAccount: string;
  brokerCode: string;
  stockCode: string;
}

export interface IGetDebtInAssetResponse {
  sell_able_qty: number; // KL bán
  loan_total: number; // Dư nợ
}

export interface ISubAccountListOption {
  customerName: string;
  accountNumber: string;
  subAccount: string;
  brokerCode: string;
  isSelect?: boolean;
}

export interface IInvestmentBySubAccountResponse {
  subNo: string;
  stkCd: string;
  sellAbleQty: number;
  costPri: number;
  ownQtyPl: number;
}

export interface IInvestmentBySubAccountList {
  symbol: string; // Mã chứng khoán
  exchange: string; // Tên sàn
  tradableVolume: number; // Khối lượng có thể giao dịch
  totalVolume: number; // Tổng khối lượng
  price: number; // Giá vốn
}
