<div class="open-detail-trades-box">
  <div class="header-box">
    <div class="typo-body-2">{{ 'MES-217' | translate }}</div>
    <img src="./assets/icons/x-cross.svg" alt="x-cross" mat-dialog-close />
  </div>
  <div class="body-box">
    <div class="body-detail-box">
      <div class="header-detail typo-body-4">{{ 'MES-170' | translate }}</div>
      <div class="body-detail">
        <div class="box-body">
          <!-- Giờ giao dịch -->
          <div class="box">
            <div class="label typo-body-12">{{ 'MES-219' | translate }}</div>
            <div class="value typo-body-6">{{ tradeData.timeTrades }}</div>
          </div>
          <!-- Ngày giao dịch -->
          <div class="box">
            <div class="label typo-body-12">{{ 'MES-220' | translate }}</div>
            <div class="value typo-body-6">{{ tradeData.dateTrades }}</div>
          </div>
        </div>
        <div class="box-body">
          <!-- Sổ tài khoản -->
          <div class="box">
            <div class="label typo-body-12">{{ 'MES-66' | translate }}</div>
            <div class="value typo-body-6">{{ tradeData.accountNumber }}</div>
          </div>
          <!-- Tên khách hàng -->
          <div class="box">
            <div class="label typo-body-12">{{ 'MES-184' | translate }}</div>
            <div class="value typo-body-6">{{ tradeData.customerName | falsyToHyphen }}</div>
          </div>
        </div>
        <div class="box-body">
          <!-- Sổ hiệu lệnh -->
          <div class="box">
            <div class="label typo-body-12">{{ 'MES-597' | translate }}</div>
            <div class="value typo-body-6">{{ tradeData.ordNo | falsyToHyphen }}</div>
          </div>

          <!-- Số hiệu lệnh gốc -->
          <div class="box" *ngIf="!isHideOriginOrdNo">
            <div class="label typo-body-12">{{ 'MES-604' | translate }}</div>
            <div class="value typo-body-6">{{ tradeData.fstNo | falsyToHyphen }}</div>
          </div>

          <div class="box" *ngIf="isShowStockCodeBesideOrdNo">
            <div class="label typo-body-12">{{ 'MES-174' | translate }}</div>
            <div class="value typo-body-6">{{ tradeData.code }}</div>
          </div>
        </div>
        <div class="box-body">
          <!-- Loại lệnh -->
          <div class="box">
            <div class="label typo-body-12">{{ 'MES-173' | translate }}</div>
            <div class="value typo-body-6">
              <span
                class="typo-body-12 status status-type"
                [class.sell-cls]="tradeData.status === EStatusTradesOrder.SELL"
                >{{ CONVERT_TYPE_TRADES_ORDER_TO_LABLE[tradeData.status] | translate }}</span
              >
              - <span class="typo-body-6 type">{{ tradeData.typeTrades }}</span>
            </div>
          </div>
          <!-- Mã CK -->
          <div class="box" *ngIf="!isHideStockCode">
            <div class="label typo-body-12">{{ 'MES-174' | translate }}</div>
            <div class="value typo-body-6">{{ tradeData.code }}</div>
          </div>

          <!-- Trạng thái xác nhận -->

          <div class="box" *ngIf="isShowStatusBesideType">
            <div class="label typo-body-12">{{ 'MES-610' | translate }}</div>
            <div class="value typo-body-6">
              <span
                class="status status-trades typo-body-12"
                [ngClass]="{
                  'confirm-cls': tradeData.tradeStatus === EStatusTradeOrderManagement.CONFIRMED,
                  'not-confirm-cls': tradeData.tradeStatus === EStatusTradeOrderManagement.NOT_CONFIRM,
                }"
              >
                {{ CONVERT_STATUS_TRADES_ORDER_MANAGEMENT_TO_LABLE[tradeData.tradeStatus] | translate }}
              </span>
            </div>
          </div>
        </div>
        <div class="box-body" *ngIf="!isHideActivePrice || !isHideTradeStatus">
          <!-- Giá kích hoạt -->
          <div class="box" *ngIf="!isHideActivePrice">
            <div class="label typo-body-12">{{ 'MES-175' | translate }}</div>
            <div class="value typo-body-6">{{ convertActivationPrice(tradeData.activationPrice) }}</div>
          </div>
          <!-- Trạng thái -->
          <div class="box" *ngIf="!isHideTradeStatus">
            <div class="label typo-body-12">{{ 'MES-221' | translate }}</div>
            <div class="value typo-body-6">
              <span
                class="status status-trades typo-body-12"
                [ngClass]="{
                  'not-match-cls': tradeData.tradeStatus === EStatusTrades.NOT_MATCH,
                  'partial-match-cls': tradeData.tradeStatus === EStatusTrades.PARTIAL_MATCH,
                  'not-activated-cls': tradeData.tradeStatus === EStatusTrades.NOT_ACTIVATED,
                  'canceled-cls': tradeData.tradeStatus === EStatusTrades.CANCELED,
                  'rejected-cls': tradeData.tradeStatus === EStatusTrades.REJECTED,
                  'activated-cls': tradeData.tradeStatus === EStatusTrades.ACTIVATED
                }"
              >
                {{ CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE[tradeData.tradeStatus] | translate }}
              </span>
            </div>
          </div>
        </div>
        <div class="box-body">
          <!-- Giá đặt -->
          <div class="box">
            <div class="label typo-body-12">{{ 'MES-176' | translate }}</div>
            <div class="value typo-body-6">
              {{
                tradeData.orderPrice
                  ? +tradeData.orderPrice
                    ? customNumberFormat(tradeData.orderPrice, 'decimal', 'en-US', 2)
                    : tradeData.orderPrice
                  : '-'
              }}
            </div>
          </div>
          <!-- Khối lượng đặt -->
          <div class="box">
            <div class="label typo-body-12">{{ 'MES-464' | translate }}</div>
            <div class="value typo-body-6">
              {{ tradeData.orderVolume | numberFormat : 'decimal' : 'en-US' | falsyToHyphen }}
              {{ tradeData.orderVolume || tradeData.orderVolume === 0 ? 'CP' : '' }}
            </div>
          </div>
        </div>
        <div class="box-body" *ngIf="!isHideMatchPrice || !isHideMatchVolumne">
          <!-- Giá Khớp -->
          <div class="box" *ngIf="!isHideMatchPrice">
            <div class="label typo-body-12">{{ 'MES-177' | translate }}</div>

            @if(tradeData.tradeStatus === EStatusTrades.NOT_MATCH) {
            <div class="value typo-body-6">-</div>

            } @else {
            <div class="value typo-body-6">
              {{ formatPrice(tradeData.matchedPrice) | falsyToHyphen }}
            </div>
            }
          </div>
          <!-- Khối lượng khớp -->
          <div class="box" *ngIf="!isHideMatchVolumne">
            <div class="label typo-body-12">{{ 'MES-465' | translate }}</div>

            @if(tradeData.tradeStatus === EStatusTrades.NOT_MATCH) {
            <div class="value typo-body-6">-</div>
            } @else {
            <div class="value typo-body-6">
              {{ tradeData.matchedVolume | numberFormat : 'decimal' : 'en-US' | falsyToHyphen }}
              {{ tradeData.matchedVolume || tradeData.matchedVolume === 0 ? 'CP' : '' }}
            </div>
            }
          </div>
        </div>
        <div class="box-body" *ngIf="!isTotalTradeValue || !isHideRevenueFee">
          <!-- GTGD -->
          <div class="box" *ngIf="!isTotalTradeValue">
            <div class="label typo-body-12">{{ 'MES-180' | translate }}</div>
            <div class="value typo-body-6">{{ convertTradeValue(tradeData.tradeValue) }}</div>
          </div>
          <!-- Tổng phí -->
          <div class="box" *ngIf="!isHideRevenueFee">
            <div class="label typo-body-12">{{ 'MES-581' | translate }}</div>
            <div class="value typo-body-6">{{ (tradeData.revenueFee | numberFormat : 'decimal') ?? '-' }}</div>
          </div>
        </div>
        <div class="box-body" *ngIf="!isHideNETTransactionFee || !isHideBrokerCommission">
          <!-- Net phí giao dịch -->
          <div class="box" *ngIf="!isHideNETTransactionFee">
            <div class="label typo-body-12">{{ 'MES-195' | translate }}</div>
            <div class="value typo-body-6">
              {{ (tradeData.netTransactionFee | numberFormat : 'decimal') ?? '-' | falsyToHyphen }}
            </div>
          </div>
          <!-- Hoa hồng môi giới -->
          <div class="box" *ngIf="!isHideBrokerCommission">
            <div class="label typo-body-12">{{ 'MES-223' | translate }}</div>
            <div class="value typo-body-6">
              {{ (tradeData.brokerCommission | numberFormat : 'decimal') ?? '-' | falsyToHyphen }}
            </div>
          </div>
        </div>
        <div class="box-body">
          <!-- Người đặt lệnh -->
          <div class="box">
            <div class="label typo-body-12">{{ 'MES-224' | translate }}</div>
            <div class="value typo-body-6">{{ tradeData.trader ?? '-' }}</div>
          </div>
          <!-- Kênh đặt -->
          <div class="box">
            <div class="label typo-body-12">{{ 'MES-183' | translate }}</div>
            <div class="value typo-body-6">{{ tradeData.orderChannel ?? '-' }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="footer-box">
    <div mat-dialog-close class="btn close typo-button-3">{{ 'MES-74' | translate }}</div>
    <button *ngIf="!isHideEditTradeOrderBtn" (click)="editTrades(tradeData)" class="btn edit typo-button-3">
      {{ 'MES-212' | translate }}
    </button>
    <button *ngIf="!isHideCancelTradeOrderBtn" (click)="cancelTrades(tradeData)" class="btn cancel typo-button-3">
      {{ 'MES-213' | translate }}
    </button>
  </div>
</div>
