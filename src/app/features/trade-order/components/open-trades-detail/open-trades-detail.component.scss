.open-detail-trades-box {
  display: flex;
  flex-direction: column;
  flex: 1;
  .header-box {
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--color--other--divider);
    img[alt='x-cross'] {
      cursor: pointer;
    }
  }
  .body-box {
    flex: 1;
    overflow: auto;
    padding: 24px;
    .body-detail-box {
      border-radius: 8px;
      border: 1px solid var(--color--other--divider);
      background-color: var(--color--background--1);
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 24px;
      .header-detail {
        text-transform: uppercase;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .body-detail {
        display: flex;
        flex-direction: column;
        gap: 24px;
        .box-body {
          display: flex;
          align-items: center;
          gap: 10px;
        }
        .box {
          display: flex;
          flex-direction: column;
          gap: 4px;
          flex: 1;
          .label {
            color: var(--color--text--subdued);
          }
          .value {
            display: flex;
            align-items: center;
            gap: 6px;
            .status {
              text-transform: uppercase;
              border-radius: 16px;
              padding: 2px 10px;
              &-trades {
                background-color: var(--color--neutral--100);

                &.not-match-cls {
                  background-color: var(--color--accents--red);
                }

                &.partial-match-cls {
                  background-color: var(--color--accents--yellow);
                }
                &.not-activated-cls {
                  background-color: var(--color--neutral--100);
                }

                &.canceled-cls {
                  background-color: var(--color--brand--600);
                }

                &.rejected-cls {
                  background-color: var(--color--neutral--300);
                }

                &.activated-cls {
                  background-color: var(--color--accents--green);
                }

                &.confirm-cls {
                  background-color: var(--color--accents--green-dark);
                }

                &.not-confirm-cls {
                  background-color: var(--color--accents--red);
                }
              }
              &-type {
                background-color: var(--color--accents--green-dark);

                &.sell-cls {
                  background-color: var(--color--accents--red-dark);
                }
              }
            }
            .type {
              text-transform: uppercase;
            }
          }
        }
      }
    }
  }

  .footer-box {
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid var(--color--other--divider);
    gap: 24px;
    .btn {
      padding: 12px 16px;
      cursor: pointer;
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      width: calc((100% / 3) - 12px);

      &.close {
        &:hover {
          background-color: var(--color--brand--500);
          color: var(--color--neutral--white);
        }
      }
      &.edit {
        background-color: var(--color--brand--500);
        border: 1px solid var(--color--brand--500);
        color: var(--color--neutral--white);
      }
      &.cancel {
        background-color: var(--color--danger--600);
        border: 1px solid var(--color--danger--600);
        color: var(--color--neutral--white);
      }
    }
  }
}

:host {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
}
