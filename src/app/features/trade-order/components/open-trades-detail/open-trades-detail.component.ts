import { Component, Inject, Input } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  CONVERT_STATUS_TRADES_ORDER_MANAGEMENT_TO_LABLE,
  CONVERT_TYPE_TRADES_ORDER_TO_LABLE,
  CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE,
  EStatusTrades,
  EStatusTradeOrderManagement,
  EStatusTradesOrder,
  ETagPageOrder,
} from '../../constants/trade-order';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { DestroyService, DialogService } from 'src/app/core/services';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { take } from 'rxjs';
import { Store } from '@ngrx/store';
import { selectAllStockList$, selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { IOptionStock, IStockItemInList } from '../../models/trade-order';
import { deepClone } from 'src/app/shared/utils/utils';
import { HashService } from 'src/app/shared/services/hash.service';
import { SharedService } from 'src/app/shared/services/shared.service';
import { OpenTradeStore } from '../../containers/open-trades/open-trades.store';
import { ReserveTradeStore } from '../../containers/reserve-trades/reserve-trades.store';
import { PlaceOrderPopupComponent } from '../place-order-popup/place-order-popup.component';

/**
 * OpenTradesDetailComponent
 */
@Component({
  selector: 'app-open-trades-detail-component',
  templateUrl: './open-trades-detail.component.html',
  styleUrls: ['./open-trades-detail.component.scss'],
  providers: [DestroyService],
})
export class OpenTradesDetailComponent {
  @Input() isHideMatchVolumne = false;

  isHideMatchPrice = false;

  EStatusTradesOrder = EStatusTradesOrder;

  EStatusTradeOrderManagement = EStatusTradeOrderManagement;

  CONVERT_TYPE_TRADES_ORDER_TO_LABLE = CONVERT_TYPE_TRADES_ORDER_TO_LABLE;

  EStatusTrades = EStatusTrades;

  CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE = CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE;

  CONVERT_STATUS_TRADES_ORDER_MANAGEMENT_TO_LABLE = CONVERT_STATUS_TRADES_ORDER_MANAGEMENT_TO_LABLE;

  tradeData: any;

  isHideEditTradeOrderBtn = false;

  isHideCancelTradeOrderBtn = false;

  isHideTradeStatus = false;

  customNumberFormat = customNumberFormat;

  isHideActivePrice = false;

  isHideRevenueFee = false;

  isHideNETTransactionFee = false;

  isHideBrokerCommission = false;

  isHideStockCode = false;

  isShowStockCodeBesideOrdNo = false;

  isHideOriginOrdNo = false;

  // Trạng thái xác nhận
  isShowStatusBesideType = false;

  isTotalTradeValue = false;
  /**
   * Constructor
   * @param data data
   * @param dialogService dialogService
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private readonly dialogService: DialogService,
    private readonly store: Store,
    private readonly dialogRef: MatDialogRef<OpenTradesDetailComponent>,
    private readonly hashService: HashService,
    private readonly shareService: SharedService,
    private readonly openTradeStore: OpenTradeStore,
    private readonly reserveTradeStore: ReserveTradeStore
  ) {
    const {
      trade,
      isHideMatchVolumne,
      isHideEditTradeOrderBtn,
      isHideCancelTradeOrderBtn,
      isHideTradeStatus,
      isHideMatchPrice,
      isHideActivePrice,
      isHideRevenueFee,
      isHideNETTransactionFee,
      isHideBrokerCommission,
      isHideStockCode,
      isShowStockCodeBesideOrdNo,
      isHideOriginOrdNo,
      isShowStatusBesideType,
      isTotalTradeValue,
    } = data;

    this.tradeData = trade;
    this.isHideMatchVolumne = isHideMatchVolumne ?? false;
    this.isHideMatchPrice = isHideMatchPrice ?? false;
    this.isHideEditTradeOrderBtn =
      this.tradeData.tradeStatus === 4 || this.tradeData.tradeStatus === EStatusTrades.REJECTED
        ? true
        : isHideEditTradeOrderBtn ?? false;
    this.isHideCancelTradeOrderBtn =
      this.tradeData.tradeStatus === 4 || this.tradeData.tradeStatus === EStatusTrades.REJECTED
        ? true
        : isHideCancelTradeOrderBtn ?? false;
    this.isHideTradeStatus = isHideTradeStatus ?? false;
    this.isHideActivePrice = isHideActivePrice ?? false;
    this.isHideRevenueFee = isHideRevenueFee ?? false;
    this.isHideNETTransactionFee = isHideNETTransactionFee ?? false;
    this.isHideBrokerCommission = isHideBrokerCommission ?? false;
    this.isHideStockCode = isHideStockCode ?? false;
    this.isShowStockCodeBesideOrdNo = isShowStockCodeBesideOrdNo ?? false;
    this.isHideOriginOrdNo = isHideOriginOrdNo ?? false;
    this.isShowStatusBesideType = isShowStatusBesideType ?? false;
    this.isTotalTradeValue = isTotalTradeValue ?? false;
  }

  /**
   * convertActivationPrice
   * @param num number
   * @returns {string} value
   */
  convertActivationPrice(num: number) {
    if (!num) return '-';
    return (+num).toFixed(2).toString() + ' CP';
  }

  /**
   * ConvertTradeValue
   * @param num string
   * @returns {any} string | number | '-'
   */
  convertTradeValue(num: string) {
    if (+num === 0) return '0' + ' VND';
    if (!num) {
      return '-';
    } else if (+num) {
      return '≈' + customNumberFormat(+num, 'decimal', 'en-US') + ' VND';
    } else return num;
  }

  formatPrice(value: number | undefined) {
    return value !== undefined ? `${value.toFixed(2)}` : '-';
  }

  editTrades(element: any) {
    this.dialogRef.close();
    this.store
      .select(selectAllStockList$)
      .pipe(take(1))
      .subscribe((allStockList) => {
        const stockOptions: IOptionStock[] = allStockList.map((t: IStockItemInList) => ({
          value: t.shortName,
          label: t.name,
          id: t.id,
          stoke: t.stock,
        }));
        const initialStockOptions = deepClone(stockOptions);
        const initialStock = stockOptions.find((option) => option.id === element.code.split(':')[0].trim());

        this.dialogService.openRightDialog(PlaceOrderPopupComponent, {
          width: '1100px',
          data: {
            title: 'MES-624',
            isDerivatives: false, // fix khi có lệnh phái sinh
            stockOptions,
            initialStockOptions,
            initialStock,
            element,
            type: ETagPageOrder.OPENTRADE,
          },
        });
      });
  }

  /**
   * CancelTrades
   */
  cancelTrades(element: any) {
    let userName = '';
    let brokerCode = '';
    let macAddress = '';
    let idNo = '';

    this.store
      .select(selectCurrentBrokerView$)
      .pipe(take(1))
      .subscribe((user) => {
        userName = user.userName;
        brokerCode = user.brokerCode;
        this.hashService.hashWithSHA256(user.brokerCode, 'SHA').then((hash) => (macAddress = hash));

        const payloadGetIdNo = {
          accountNumbers: [element.accountNumber.split(' - ')[0]],
          brokerCode: [brokerCode],
          userType: '',
          fromBirthYear: '',
          toBirthYear: '',
          searchKey: '',
        };

        this.shareService
          .getPersonalInfoList(payloadGetIdNo)
          .pipe(take(1))
          .subscribe((data) => {
            idNo = data[0].identity;
          });
      });

    const ref = this.dialogService.openPopUp(DeleteNotificationComponent, {
      data: {
        isActive: false,
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-225',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-226',
        },
        isReverse: true,
      },
      height: '260px',
      width: '360px',
      panelClass: ['popup-confirm', 'not-padding-popup'],
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v) => {
        if (v === 'save') {
          this.dialogRef.close();
          switch (element.typeTagOrder) {
            case ETagPageOrder.OPENTRADE:
              {
                const payload = {
                  hts_user_id: userName,
                  hts_user_nm: brokerCode,
                  cli_mac_addr: macAddress,
                  account_list: [
                    {
                      acnt_no: element.accountNumber.split(' - ')[0],
                      sub_no: element.subAccount,
                      idno: idNo,
                    },
                  ],
                  ord_no: element.ordNo.toString(),
                  bank_cd: '9999',
                  brch_cd: '9999',
                  ord_mdm_tp: '01',
                };

                this.openTradeStore.cancelTradeOrder({ payload });
              }
              break;

            case ETagPageOrder.PREORDER:
              this.store
                .select(selectCurrentBrokerView$)
                .pipe(take(1))
                .subscribe((user) => {
                  // Extract and format date to yyyyMMdd
                  const formattedDate = element.activateDate.replace(/-/g, '');
                  const payload = {
                    hts_user_id: user.userName,
                    hts_user_nm: user.brokerCode,
                    cli_mac_addr: macAddress,
                    account_list: [
                      {
                        acnt_no: element.accountNumber.split(' - ')[0],
                        sub_no: element.subAccount,
                      },
                    ],
                    ord_no: element.ordNo.toString(),
                    ord_frct_dt: formattedDate,
                    lang_code: 'V',
                    ord_mdm_tp: '01',
                  };

                  this.reserveTradeStore.cancelAdvanceTradeOrder({ payload });
                });
              break;

            default:
              break;
          }
        }
      });
  }
}
