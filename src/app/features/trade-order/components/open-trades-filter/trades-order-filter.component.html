<form [formGroup]="tradeOrderFilterForm" class="form-container-open-trades">
  <div class="open-trades-filter-container">
    <div class="header-cls">
      <div class="typo-body-14">{{ 'MES-19' | translate }}</div>
      <img src="./assets/icons/x-cross.svg" alt="x-cross" mat-dialog-close />
    </div>
    <div class="body-cls" [ngClass]="{ 'one-column': data.isOneColumnInPopup }">
      <div class="box-body">
        <!-- Khách hàng -->
        <div *ngIf="optionConfigFilter.numberAccount" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-96' | translate }}</div>
          <app-virtual-scroll-list
            *ngIf="customers?.length; else noResults"
            [id]="customerKey"
            [key]="customerKey"
            [items]="customers"
            [selectedKeys]="initSelectedCustomer"
            [maxSelectedItems]="MAX_ITEM_SELECTED_CUSTOMER"
            [translateParams]="{ maxItem: MAX_ITEM_SELECTED_CUSTOMER }"
            [messageSelectionComplete]="'MES-669'"
            [warningMessage]="'MES-646'"
            [selectAllLabel]="'MES-58' | translate"
            [searchPlaceholder]="'MES-295' | translate"
            [noResultsMessage]="'MES-585' | translate"
            [searchKeys]="['accountNumber', 'customerName']"
            [displayFn]="displayCustomerFilter"
            (invalidSelection)="validateFilterValue($event)"
          ></app-virtual-scroll-list>

          <ng-template #noResults>
            <div class="typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
          </ng-template>
        </div>
        <!-- Ngày đặt lệnh custom -->
        <div
          *ngIf="
            optionConfigFilter.dateOrderTrades?.position === 'left' &&
            optionConfigFilter.dateOrderTrades?.place === 'belowNumberAccountOptions'
          "
          class="drop-down-box"
        >
          <div class="typo-body-15">{{ 'MES-171' | translate }}</div>
          <app-range-date-picker-component
            [isNotAcceptEnterDate]="true"
            [rangeDate]="
              data.optionFilter.dateRange.start
                ? dateToDMY(dateStartControl.value) + '-' + dateToDMY(dateEndControl.value)
                : ''
            "
            (dataChangeEvent)="receiverDataDateRange($event)"
          ></app-range-date-picker-component>
        </div>

        <!-- Lệnh -->
        <div *ngIf="optionConfigFilter.status" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-172' | translate }}</div>
          <div class="check-box-list">
            <div class="checkbox-cls-item">
              <mat-checkbox (change)="isBuyStatus = $event.checked" [checked]="isBuyStatus" class="checkbox-cls">{{
                'MES-186' | translate
              }}</mat-checkbox>
            </div>
            <div class="checkbox-cls-item">
              <mat-checkbox (change)="isSellStatus = $event.checked" [checked]="isSellStatus" class="checkbox-cls">{{
                'MES-185' | translate
              }}</mat-checkbox>
            </div>
          </div>

          <div *ngIf="!this.isBuyStatus && !this.isSellStatus" class="err-msg typo-body-10">
            {{ 'MES-667' | translate }}
          </div>
        </div>
        <!-- Loại lệnh - dropdown -->
        <div *ngIf="optionConfigFilter.typeTrades === 'dropdown'" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-173' | translate }}</div>
          <button
            class="box-dropdown"
            (click)="openOptionSelection($event, optionListConfigFilter.typeTradesOptions, 'typeTradesOptions')"

          >
            <span class="typo-body-12">{{
              optionListFilter.typeTradesOptions &&
              optionListFilter.typeTradesOptions.length !== optionListConfigFilter.typeTradesOptions?.length
                ? optionListFilter.typeTradesOptions.length + ' ' + ('MES-172' | translate)
                : ('MES-58' | translate)
            }}</span>
            <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
          </button>
        </div>
        <!-- Trạng thái lệnh - checkbox -->
        <div *ngIf="optionConfigFilter.statusTrades === 'checkbox'" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-181' | translate }}</div>
          <div class="check-box-list">
            <div class="checkbox-cls-item" *ngFor="let option of optionListConfigFilter.statusTradesOptions">
              <mat-checkbox
                (change)="changeStatusTradeOptions(option, $event.checked)"
                [checked]="option['isSelect']"
                class="checkbox-cls"
                >{{ option.label }}</mat-checkbox
              >
            </div>
          </div>

          <div
            *ngIf="isCheckboxListAllNotSelected(optionListConfigFilter.statusTradesOptions ?? [])"
            class="err-msg typo-body-10"
          >
            {{ 'MES-667' | translate }}
          </div>
        </div>
        <!-- Trạng thái xác nhận lệnh - dropdown -->
        <div *ngIf="optionConfigFilter.statusConfirmTrades === 'dropdown'" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-610' | translate }}</div>
          <div class="check-box-list">
            <div class="checkbox-cls-item" *ngFor="let option of optionListConfigFilter.statusTradesOptions">
              <mat-checkbox
                (change)="changeStatusTradeOptions(option, $event.checked)"
                [checked]="option['isSelect']"
                class="checkbox-cls"
                >{{ option.label }}</mat-checkbox
              >
            </div>
          </div>
        </div>
        <!-- Loại lệnh - checkbox show option  -->
        <div *ngIf="optionConfigFilter.typeTrades === 'checkbox'" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-173' | translate }}</div>
          <div class="check-box-list">
            <div class="checkbox-cls-item">
              <mat-checkbox
                (change)="isStopLOTypeTrades = $event.checked"
                [checked]="isStopLOTypeTrades"
                class="checkbox-cls"
                >STOP LO</mat-checkbox
              >
            </div>
            <div class="checkbox-cls-item">
              <mat-checkbox
                (change)="isStopMPTypeTrades = $event.checked"
                [checked]="isStopMPTypeTrades"
                class="checkbox-cls"
                >STOP MP</mat-checkbox
              >
            </div>
          </div>
        </div>
        <!-- Điều kiện lệnh - dropdown -->
        <div *ngIf="optionConfigFilter.conditionTrades" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-201' | translate }}</div>
          <button
            class="box-dropdown"
            (click)="
              openOptionSelection($event, optionListConfigFilter.conditionTradesOptions, 'conditionTradesOptions')
            "

          >
            <span class="typo-body-12">{{
              optionListFilter.conditionTradesOptions &&
              optionListFilter.conditionTradesOptions.length !== optionListConfigFilter.conditionTradesOptions?.length
                ? optionListFilter.conditionTradesOptions.length + ' ' + ('MES-201' | translate)
                : ('MES-58' | translate)
            }}</span>
            <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
          </button>

        </div>
        <!-- Ngày đặt lệnh -->
        <div
          *ngIf="optionConfigFilter.dateOrderTrades?.position === 'left' && !optionConfigFilter.dateOrderTrades?.place"
          class="drop-down-box"
        >
          <div class="typo-body-15">{{ 'MES-171' | translate }}</div>
          <app-range-date-picker-component
            [isNotAcceptEnterDate]="true"
            [rangeDate]="
              data.optionFilter.dateRange.start
                ? dateToDMY(dateStartControl.value) + '-' + dateToDMY(dateEndControl.value)
                : ''
            "
            (dataChangeEvent)="receiverDataDateRange($event)"
          ></app-range-date-picker-component>
        </div>
        <!-- Ngày khớp lệnh -->
        <div *ngIf="optionConfigFilter.dateMatchedTrades" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-228' | translate }}</div>
          <div class="box-calendar">
            <!-- Từ ngày -->
            <div class="content-from">
              <div class="from-label typo-body-12">{{ 'MES-209' | translate }} ngày</div>
              <button class="input-wrapper" (click)="dateFromMatchTrades.open()"
              >
                <input
                  (focus)="handleFocus($event)"
                  type="text"
                  class="fs-12 calendar-input typo-body-12"
                  matInput
                  [matDatepicker]="dateFromMatchTrades"
                  [placeholder]="'DD/MM/YYYY'"
                />
                <img src="./assets/icons/calendar.svg" alt="" />
                <mat-datepicker
                  [calendarHeaderComponent]="headerCalendar"
                  panelClass="calendar-cls"
                  #dateFromMatchTrades
                ></mat-datepicker>
              </button>
            </div>

            <!-- Tới ngày -->
            <div class="content-to">
              <div class="to-label typo-body-12">{{ 'MES-210' | translate }} ngày</div>
              <button class="input-wrapper" (click)="dateToMatchTrades.open()"
              >
                <input
                  (focus)="handleFocus($event)"
                  type="text"
                  class="fs-12 calendar-input typo-body-12"
                  matInput
                  [matDatepicker]="dateToMatchTrades"
                  [placeholder]="'DD/MM/YYYY'"
                />
                <img src="./assets/icons/calendar.svg" alt="" />
                <mat-datepicker
                  [calendarHeaderComponent]="headerCalendar"
                  panelClass="calendar-cls"
                  #dateToMatchTrades
                ></mat-datepicker>
              </button>
            </div>
          </div>
        </div>
        <!-- Ngày kích hoạt -->
        <div *ngIf="optionConfigFilter.dateActivation" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-198' | translate }}</div>
          <div class="box-calendar">
            <!-- Từ ngày -->
            <div class="content-from">
              <div class="from-label typo-body-12">{{ 'MES-209' | translate }} ngày</div>
              <button class="input-wrapper" (click)="dateFromActivationTrades.open()"
              >
                <input
                  (focus)="handleFocus($event)"
                  type="text"
                  class="fs-12 calendar-input typo-body-12"
                  matInput
                  [matDatepicker]="dateFromActivationTrades"
                  [placeholder]="'DD/MM/YYYY'"
                  [formControl]="dateActiveStartControl"
                  [max]="dateActiveEndControl.value ?? today"
                />
                <img src="./assets/icons/calendar.svg" alt="" />
                <mat-datepicker
                  [calendarHeaderComponent]="headerCalendar"
                  panelClass="calendar-cls"
                  #dateFromActivationTrades
                ></mat-datepicker>
              </button>
            </div>

            <!-- Tới ngày -->
            <div class="content-to">
              <div class="to-label typo-body-12">{{ 'MES-210' | translate }} ngày</div>
              <button class="input-wrapper" (click)="dateToActivationTrades.open()"
              >
                <input
                  (focus)="handleFocus($event)"
                  type="text"
                  class="fs-12 calendar-input typo-body-12"
                  matInput
                  [formControl]="dateActiveEndControl"
                  [min]="dateActiveStartControl.value"
                  [max]="today"
                  [matDatepicker]="dateToActivationTrades"
                  [placeholder]="'DD/MM/YYYY'"
                />
                <img src="./assets/icons/calendar.svg" alt="" />
                <mat-datepicker
                  [calendarHeaderComponent]="headerCalendar"
                  panelClass="calendar-cls"
                  #dateToActivationTrades
                ></mat-datepicker>
              </button>
            </div>
          </div>
        </div>
        <!-- Kênh đặt lệnh -->
        <div *ngIf="optionConfigFilter.orderChannel === 'left'" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-227' | translate }}</div>
          <button
            class="box-dropdown"
            (click)="openOptionSelection($event, optionListConfigFilter.orderChannel, 'orderChannel')"

          >
            <span class="typo-body-12">{{
              optionListFilter.orderChannel &&
              optionListFilter.orderChannel.length !== optionListConfigFilter.orderChannel?.length
                ? optionListFilter.orderChannel.length + ' ' + ('MES-305' | translate)
                : ('MES-58' | translate)
            }}</span>
            <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
          </button>
        </div>
      </div>
      <div class="box-body">
        <!-- Mã CK -->
        <div *ngIf="optionConfigFilter.code" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-174' | translate }}</div>
          <app-virtual-scroll-list
            *ngIf="listOfStocks?.length; else noResultsStoke"
            [id]="stokeKey"
            [key]="stokeKey"
            [items]="listOfStocks"
            [selectedKeys]="initSelectedStoke"
            [maxSelectedItems]="MAX_ITEM_SELECTED"
            [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
            [messageSelectionComplete]="'MES-670'"
            [warningMessage]="'MES-668'"
            [selectAllLabel]="'MES-58' | translate"
            [searchPlaceholder]="'MES-14' | translate"
            [noResultsMessage]="'MES-585' | translate"
            [searchKeys]="['value', 'stoke']"
            [displayFn]="displayStokeFilter"
            (invalidSelection)="validateStockFilterValue($event)"
          ></app-virtual-scroll-list>

          <ng-template #noResultsStoke>
            <div class="typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
          </ng-template>
        </div>

        <!-- Ngày đặt lệnh -->
        <div *ngIf="optionConfigFilter.dateOrderTrades?.position === 'right'" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-171' | translate }}</div>
          <app-range-date-picker-component
            [isNotAcceptEnterDate]="true"
            [rangeDate]="
              data.optionFilter.dateRange.start
                ? dateToDMY(dateStartControl.value) + '-' + dateToDMY(dateEndControl.value)
                : ''
            "
            (dataChangeEvent)="receiverDataDateRange($event)"
          ></app-range-date-picker-component>
        </div>
        <!-- Kênh đặt lệnh -->
        <div *ngIf="optionConfigFilter.orderChannel === 'right'" class="drop-down-box">
          <div class="typo-body-15">{{ 'MES-227' | translate }}</div>
          <button
            class="box-dropdown"
            (click)="openOptionSelection($event, optionListConfigFilter.orderChannel, 'orderChannel')"
          >
            <span class="typo-body-12">{{
              optionListFilter.orderChannel &&
              optionListFilter.orderChannel.length !== optionListConfigFilter.orderChannel?.length
                ? optionListFilter.orderChannel.length + ' ' + ('MES-305' | translate)
                : ('MES-58' | translate)
            }}</span>
            <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
          </button>
        </div>
        <!-- GTDG -->
        <div *ngIf="optionConfigFilter.tradesValue" class="drop-down-box right-box">
          <div class="typo-body-15">{{ 'MES-180' | translate }}</div>
          <div class="box-range">
            <div class="content-from">
              <app-form-control>
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'separator.2'"
                  [allowNegativeNumbers]="true"
                  formControlName="fromValueTradesControl"
                  [placeholder]="'0'"
                />
              </app-form-control>
            </div>

            <div class="content-to">
              <app-form-control>
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'separator.2'"
                  [allowNegativeNumbers]="true"
                  formControlName="toValueTradesControl"
                  [placeholder]="'∞'"
                />
              </app-form-control>
            </div>
          </div>
        </div>
        <!-- Tổng phí -->
        <div *ngIf="optionConfigFilter.revenue" class="drop-down-box right-box">
          <div class="typo-body-15">{{ 'MES-581' | translate }}</div>
          <div class="box-range">
            <div class="content-from">
              <app-form-control>
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'separator.2'"
                  [allowNegativeNumbers]="true"
                  formControlName="fromRevenueControl"
                  [placeholder]="'0'"
                />
              </app-form-control>
            </div>

            <div class="content-to">
              <app-form-control>
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'separator.2'"
                  [allowNegativeNumbers]="true"
                  formControlName="toRevenueControl"
                  [placeholder]="'∞'"
                />
              </app-form-control>
            </div>
          </div>
        </div>
        <!-- Net phí giao dịch -->
        <div *ngIf="optionConfigFilter.netTradesFee" class="drop-down-box right-box">
          <div class="typo-body-15">{{ 'MES-195' | translate }}</div>
          <div class="box-range">
            <div class="content-from">
              <app-form-control>
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'separator.2'"
                  [allowNegativeNumbers]="true"
                  formControlName="fromNetTradesControl"
                  [placeholder]="'0'"
                />
              </app-form-control>
            </div>

            <div class="content-to">
              <app-form-control>
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'separator.2'"
                  [allowNegativeNumbers]="true"
                  formControlName="toNetTradesControl"
                  [placeholder]="'∞'"
                />
              </app-form-control>
            </div>
          </div>
        </div>
        <!-- Hoa Hồng MG -->
        <div *ngIf="optionConfigFilter.brokerageCommission" class="drop-down-box right-box">
          <div class="typo-body-15">{{ 'MES-196' | translate }}</div>
          <div class="box-range">
            <div class="content-from">
              <app-form-control>
                <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'separator.2'"
                  [allowNegativeNumbers]="true"
                  formControlName="fromBrokerageCommissionControl"
                  [placeholder]="'0'"
                />
              </app-form-control>
            </div>

            <div class="content-to">
              <app-form-control>
                <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
                <input
                  class="holding-period-input typo-body-12 fs-12"
                  [mask]="'separator.2'"
                  [allowNegativeNumbers]="true"
                  formControlName="toBrokerageCommissionControl"
                  [placeholder]="'∞'"
                />
              </app-form-control>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer-cls">
      <button (click)="defaultFilter()" class="btn typo-button-3"
      >{{ 'MES-20' | translate }}</button>
      <button
        [class.disable]="isDisableApply || isDisableStockApply"
        (click)="applyFilter()"
        class="btn typo-button-3 brand-color"
      >
        {{ 'MES-21' | translate }}
      </button>
    </div>
  </div>
</form>
