import { Component, Inject, QueryList, ViewChildren } from '@angular/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, provideNativeDateAdapter } from '@angular/material/core';
import { MY_DATE_FORMAT } from 'src/app/shared/constants/date-picker';
import {
  IFilterOpenTradesParam,
  IListOptionConfig,
  IListOptions,
  IOptionConfigFilter,
  IRangeFilter,
} from '../../models/trade-order';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { CustomDropdownPopupFilterComponent } from 'src/app/shared/components/custom-dropdown-popup-filter/custom-dropdown-popup-filter.component';
import { take, tap } from 'rxjs';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup } from '@angular/forms';
import { DatePickerNavigationFullDateComponent } from 'src/app/shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';
import { IDataChangeItem } from '../../../../shared/components/date-picker/range-date-picker/range-date-picker.component';
import { dateToDMY, dateToYMD } from 'src/app/shared/utils/date';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { DestroyService } from 'src/app/core/services';
import { Store } from '@ngrx/store';
import {
  selectAllAccountByEmNo$,
  selectAllAccountNumberListByBrokerView$,
  selectAllStockList$,
} from 'src/app/stores/shared/shared.selectors';
import { validFromToValidator } from 'src/app/shared/validators/form';
import { MAX_ITEM_ACCOUNT_SELECTED, MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';

export interface IDataFilter {
  config: IOptionConfigFilter;
  listOption: IListOptionConfig;
  optionFilter: IFilterOpenTradesParam;
  isOneColumnInPopup?: boolean;
  isAccountNumberByEmNo?: boolean;
}

interface IItemStoke {
  value: string;
  stoke: string;
}

/**
 * TradesOrderFilterComponent
 */
@Component({
  selector: 'app-trades-order-filter-component',
  templateUrl: './trades-order-filter.component.html',
  styleUrls: ['./trades-order-filter.component.scss'],
  providers: [
    provideNativeDateAdapter(),
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
  ],
})
export class TradesOrderFilterComponent {
  @ViewChildren(VirtualScrollListComponent) virtualScroll!: QueryList<VirtualScrollListComponent>;

  optionConfigFilter: IOptionConfigFilter = {
    code: false,
    status: false,
    typeTrades: null,
    statusTrades: null,
    statusConfirmTrades: null,
    conditionTrades: false,
    numberAccount: false,
    dateOrderTrades: {
      position: null,
      show: false,
      place: null,
    },
    dateMatchedTrades: false,
    dateActivation: false,
    trader: null,
    orderChannel: null,
    tradesValue: false,
    revenue: false,
    netTradesFee: false,
    brokerageCommission: false,
  };

  optionListConfigFilter: IListOptionConfig = {
    codeOptions: [],
    typeTradesOptions: [],
    statusTradesOptions: [],
    statusConfirmTradesOptions: [],
    numberAccountOptions: [],
    traderOptions: [],
    orderChannel: [],
    conditionTradesOptions: [],
  };

  /**
   * Dùng để lưu giá trị thay đổi trong dropdown
   */
  optionListFilter: IListOptionConfig = {
    codeOptions: [],
    typeTradesOptions: [],
    statusTradesOptions: [],
    statusConfirmTradesOptions: [],
    numberAccountOptions: [],
    traderOptions: [],
    orderChannel: [],
    conditionTradesOptions: [],
  };
  // Checkbox
  isBuyStatus = false;

  isSellStatus = false;

  isActiveStatus = false;

  isInActiveStatus = false;

  isStopLOTypeTrades = false;

  isStopMPTypeTrades = false;

  headerCalendar = DatePickerNavigationFullDateComponent;

  // Formcontrol

  dateStartControl = new FormControl();

  dateEndControl = new FormControl();

  dataDateRange: string = '';

  dateActiveStartControl = new FormControl();

  dateActiveEndControl = new FormControl();

  dateToDMY = dateToDMY;

  isValidForm = true;

  tradeOrderFilterForm!: FormGroup;

  // Key identifier for customer objects
  readonly customerKey = 'accountNumber';

  readonly stokeKey = 'value';

  customers: IAllAccountNumber[] = [];

  listOfStocks: IItemStoke[] = [];

  initSelectedStoke: string[] = [];

  initSelectedCustomer: string[] = [];
  today = new Date();

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  MAX_ITEM_SELECTED_CUSTOMER = MAX_ITEM_ACCOUNT_SELECTED;

  isDisableStockApply = false;

  isDisableApply = false;

  /**
   * Constructor
   * @param data data
   * @param dialogRef dialogRef
   * @param popoverService PopoverService
   * @param fb
   * @param _destroy
   * @param store
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: IDataFilter,
    public dialogRef: MatDialogRef<TradesOrderFilterComponent>,
    private readonly popoverService: PopoverService,
    private readonly fb: FormBuilder,
    private readonly _destroy: DestroyService,
    private readonly store: Store
  ) {
    this.initForm();

    const { config, listOption, optionFilter } = data;
    if (config) {
      this.optionConfigFilter = {
        ...this.optionConfigFilter,
        ...config,
      };
    }
    if (listOption) {
      this.optionListConfigFilter = {
        ...this.optionListConfigFilter,
        ...listOption,
      };

      Object.keys(listOption).forEach((key) => {
        this.optionListFilter[key] = listOption[key]?.filter((t) => t['isSelect']);
      });
      this.loadCustomerList();
      this.getListStock();
    }
    if (optionFilter) {
      this.updateFilterData(optionFilter);
    }
  }

  private loadCustomerList() {
    const customerInfo$ = this.data.isAccountNumberByEmNo
      ? this.store.select(selectAllAccountByEmNo$)
      : this.store.select(selectAllAccountNumberListByBrokerView$);

    customerInfo$
      .pipe(
        take(1),
        tap((customers) => {
          this.customers = customers;
        })
      )
      .subscribe();
  }

  private getListStock() {
    this.store
      .select(selectAllStockList$)
      .pipe(take(1))
      .subscribe((allStocks) => {
        this.listOfStocks = allStocks.map((t) => ({
          value: t.id,
          stoke: t.stock,
        }));
      });
  }

  private initForm() {
    this.tradeOrderFilterForm = this.fb.group(
      {
        fromValueTradesControl: new FormControl(),
        toValueTradesControl: new FormControl(),

        fromRevenueControl: new FormControl(),
        toRevenueControl: new FormControl(),

        fromNetTradesControl: new FormControl(),
        toNetTradesControl: new FormControl(),

        fromBrokerageCommissionControl: new FormControl(),
        toBrokerageCommissionControl: new FormControl(),
      },
      {
        validators: [
          validFromToValidator('fromValueTradesControl', 'toValueTradesControl'),
          validFromToValidator('fromRevenueControl', 'toRevenueControl'),
          validFromToValidator('fromNetTradesControl', 'toNetTradesControl'),
          validFromToValidator('fromBrokerageCommissionControl', 'toBrokerageCommissionControl'),
        ],
      }
    );

    this.tradeOrderFilterForm.markAllAsTouched();
  }

  /**
   * OpenOptionSelection
   * @param event MouseEvent
   * @param listOption list
   * @param key key object
   */
  openOptionSelection(event: MouseEvent, listOption: any[] | undefined, key: string) {
    let originElement = event.target as HTMLElement;
    if (originElement && originElement.nodeName !== 'DIV') {
      originElement = originElement.parentElement as HTMLElement;
    }
    const popupRef = this.popoverService.open({
      origin: originElement,
      content: CustomDropdownPopupFilterComponent,
      position: 2,
      width: originElement.offsetWidth,
      height: 280,
      data: listOption ?? [],
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100'],
      componentConfig: {
        searchKey: ['value', 'label'],
        displayFnc: (v: any) => v.label,
        listFilterOptions: listOption,
        isAllSelected: listOption?.every((t) => t.isSelect),
        limit: key === 'numberAccountOptions' ? 20 : 0,
      },
    });

    popupRef.afterClosed$.pipe(take(1)).subscribe({
      next: (v: any) => {
        const { data } = v;
        this.optionListFilter[key] = data;
        this.optionListConfigFilter[key]?.forEach((t: any) => {
          t.isSelect = this.optionListFilter[key]?.some((e) => e['value'] === t.value);
        });
      },
    });
  }

  /**
   * UpdateFilterData
   * @param optionFilter IFilterOpenTradesParam
   */
  updateFilterData(optionFilter: IFilterOpenTradesParam) {
    const {
      activeStatus,
      brokerageCommission,
      buySellStatus,
      isStopTypeTrades,
      netTradesFee,
      revenue,
      valueTrades,
      optionSelection,
      dateActiveRange,
      dateRange,
    } = optionFilter;
    this.updateBuySellStatus(buySellStatus);
    this.updateActiveStatus(activeStatus);
    this.updateStopTypeTrades(isStopTypeTrades);

    const {
      codeOptionsSelect,
      // typeTradesOptionsSelect,
      // statusTradesOptionsSelect,
      numberAccountOptionsSelect,
      // traderOptionsSelect,
      // orderChannelOptionSelect,
      // conditionTradesOptionsSelect,
    } = optionSelection;

    this.getFormControl('fromBrokerageCommissionControl').patchValue(brokerageCommission.start);
    this.getFormControl('toBrokerageCommissionControl').patchValue(brokerageCommission.end);
    this.getFormControl('fromNetTradesControl').patchValue(netTradesFee.start);
    this.getFormControl('toNetTradesControl').patchValue(netTradesFee.end);
    this.getFormControl('fromValueTradesControl').patchValue(valueTrades.start);
    this.getFormControl('toValueTradesControl').patchValue(valueTrades.end);
    this.getFormControl('fromRevenueControl').patchValue(revenue.start);
    this.getFormControl('toRevenueControl').patchValue(revenue.end);

    this.updateFormControlValue(dateActiveRange, this.dateActiveStartControl, this.dateActiveEndControl);

    if (dateRange.start && dateRange.end) {
      this.dateStartControl.patchValue(new Date(dateRange.start));
      this.dateEndControl.patchValue(new Date(dateRange.end));
      this.dataDateRange = `${dateRange.start}/${dateRange.end}`;
    }
    this.initSelectedStoke = (codeOptionsSelect ?? []).map((t) => t);
    this.initSelectedCustomer = (numberAccountOptionsSelect ?? []).map((t) => t);
  }

  /**
   * UpdateBuySellStatus
   * @param status
   */
  updateBuySellStatus(status: number[] | null) {
    if (!status?.length) {
      this.isBuyStatus = true;
      this.isSellStatus = true;
    } else {
      status.forEach((t) => {
        if (t === 0) {
          this.isBuyStatus = true;
        } else if (t === 1) {
          this.isSellStatus = true;
        }
      });
    }
  }

  /**
   * UpdateActiveStatus
   * @param status
   */
  updateActiveStatus(status: number[] | null) {
    if (!status?.length) {
      this.isActiveStatus = true;
      this.isInActiveStatus = true;
    } else {
      status.forEach((t) => {
        if (t === 3) {
          this.isActiveStatus = true;
        } else if (t === 2) {
          this.isInActiveStatus = true;
        }
      });
    }
  }

  /**
   * UpdateStopTypeTrades
   * @param status
   */
  updateStopTypeTrades(status: string[] | null) {
    if (status === null) {
      this.isStopMPTypeTrades = true;
      this.isStopLOTypeTrades = true;
    } else {
      status.forEach((t) => {
        if (t === 'STOP MP') {
          this.isStopMPTypeTrades = true;
        } else if (t === 'STOP LO') {
          this.isStopLOTypeTrades = true;
        }
      });
    }
  }

  /**
   * UpdateFormControlValue
   * @param date IRangeFilter
   * @param startControl FormControl
   * @param endControl FormControl
   */
  updateFormControlValue(date: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    const { start, end } = date;
    startControl?.patchValue(start);
    endControl?.patchValue(end);
  }

  validateStockFilterValue(invalid: boolean) {
    this.isDisableStockApply = invalid;
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    if (this.isDisableApply || this.isDisableStockApply) return;

    const { buySellStatus, activeStatus, isStopTypeTrades } = this.checkFilterCheckBox();
    const valueTrades = {
      start: this.getFormControl('fromValueTradesControl').value,
      end: this.getFormControl('toValueTradesControl').value,
    };

    const revenue = {
      start: this.getFormControl('fromRevenueControl').value,
      end: this.getFormControl('toRevenueControl').value,
    };
    const dateActiveRange = {
      start: this.dateActiveStartControl.value?._d
        ? dateToYMD(this.dateActiveStartControl.value._d)
        : this.dateActiveStartControl.value,
      end: this.dateActiveEndControl.value?._d
        ? dateToYMD(this.dateActiveEndControl.value._d)
        : this.dateActiveEndControl.value,
    };

    const netTradesFee = {
      start: this.getFormControl('fromNetTradesControl').value,
      end: this.getFormControl('toNetTradesControl').value,
    };

    const brokerageCommission = {
      start: this.getFormControl('fromBrokerageCommissionControl').value,
      end: this.getFormControl('toBrokerageCommissionControl').value,
    };

    const {
      // codeOptions,
      typeTradesOptions,
      // numberAccountOptions,
      // numberAccountOptionsClone,
      traderOptions,
      orderChannel,
      conditionTradesOptions,
      // statusTradesOptions,
    } = this.optionListFilter;
    // Khách hàng

    const getVirtualScrollById = (id: string) => {
      return this.virtualScroll.find((component) => component.id === id);
    };
    const numberAccountOptionsSelect = (
      (getVirtualScrollById(this.customerKey)?.getChecked() as IAllAccountNumber[]) ?? []
    ).map((c) => c.accountNumber);

    const codeOptionsSelect = ((getVirtualScrollById(this.stokeKey)?.getChecked() as IItemStoke[]) ?? []).map(
      (c) => c.value
    );

    // Loại lệnh
    const typeTradesOptionsSelect =
      this.optionListConfigFilter.typeTradesOptions?.length === typeTradesOptions?.length
        ? []
        : typeTradesOptions?.map((t) => t['value']);
    const typeTradesOptionsSelectValue =
      this.optionListConfigFilter.typeTradesOptions?.length === typeTradesOptions?.length
        ? []
        : typeTradesOptions?.map((t) => t['value']);

    // Trạng thái lệnh
    const statusTradesOptionsSelect = this.optionListConfigFilter.statusTradesOptions
      ?.filter((t) => t['isSelect'])
      .map((t) => t.label);

    const statusTradesOptionsSelectValue =  this.isCheckBoxListAllSelected(this.optionListConfigFilter.statusTradesOptions ?? [])
        ? []
        : this.optionListConfigFilter.statusTradesOptions?.filter((t) => t['isSelect']).map((t) => t['value']);

    // Trạng thái xác nhận lệnh
    const statusConfirmTradesOptionsSelect = this.optionListConfigFilter.statusConfirmTradesOptions
      ?.filter((t) => t['isSelect'])
      .map((t) => t.label ?? '');

    const statusConfirmTradesOptionsSelectValue = this.optionListConfigFilter.statusConfirmTradesOptions
      ?.filter((t) => t['isSelect'])
      .map((t) => t['value']);

    const traderOptionsSelect = traderOptions?.map((t) => t['value']);
    const orderChannelOptionSelect =
      this.optionListConfigFilter.orderChannel?.length === orderChannel?.length
        ? []
        : orderChannel?.map((t) => t['value']);
    const orderChannelOptionSelectValue =
      this.optionListConfigFilter.orderChannel?.length === orderChannel?.length
        ? []
        : orderChannel?.map((t) => t['value']);

    const conditionTradesOptionsSelect =
      this.optionListConfigFilter.conditionTradesOptions?.length === conditionTradesOptions?.length
        ? []
        : this.optionListConfigFilter.conditionTradesOptions?.filter((t) => t['isSelect']).map((t) => t['value']);

    const [start, end] = this.dataDateRange.split('/');

    const dateRange =
      start && end && start !== '1970-01-01' && end !== '1970-01-01' ? { start, end } : { start: null, end: null };

    const selection = {
      numberAccountOptionsSelect,
      codeOptionsSelect,
      buySellStatus,
      activeStatus,
      isStopTypeTrades,
      // codeOptionsSelect as string[],
      // conditionTradesOptionsSelect as string[],
      // numberAccountOptionsSelect as string[],
      // orderChannelOptionSelect as string[],
      // typeTradesOptionsSelect as string[],
      // statusConfirmTradesOptionsSelect as string[],
      // statusTradesOptionsSelectValue as string[],
      valueTrades,
      revenue,
      brokerageCommission,
      netTradesFee,
      dateRange,
      dateActiveRange,
    };

    const isFilter = this.checkStatusFilter(selection);

    this.dialogRef.close({
      type: 'save',
      optionFilter: {
        isFilter: isFilter,
        buySellStatus,
        activeStatus,
        isStopTypeTrades,
        dateActiveRange,
        optionSelection: {
          codeOptionsSelect,
          typeTradesOptionsSelect,
          statusTradesOptionsSelect,
          statusConfirmTradesOptionsSelect,
          numberAccountOptionsSelect,
          traderOptionsSelect,
          orderChannelOptionSelect,
          conditionTradesOptionsSelect,
          statusTradesOptionsSelectValue,
          statusConfirmTradesOptionsSelectValue,
          orderChannelOptionSelectValue,
          typeTradesOptionsSelectValue,
        },
        valueTrades,
        revenue,
        brokerageCommission,
        netTradesFee,
        dateRange,
      },
    });
  }

  checkStatusFilter(selectionState: {
    numberAccountOptionsSelect: string[];
    codeOptionsSelect: string[];
    buySellStatus: number[];
    activeStatus: number[];
    isStopTypeTrades: string[];
    // codeOptionsSelect: string[],
    // conditionTradesOptionsSelect: string[],
    // numberAccountOptionsSelect: string[],
    // orderChannelOptionSelect: string[],
    // typeTradesOptionsSelect: string[],
    // statusConfirmTradesOptionsSelect: string[],
    // statusTradesOptionsSelectValue: string[],
    valueTrades: IRangeFilter;
    revenue: IRangeFilter;
    brokerageCommission: IRangeFilter;
    netTradesFee: IRangeFilter;
    dateRange: IRangeFilter;
    dateActiveRange: IRangeFilter;
  }) {
    const { typeTradesOptions, orderChannel, conditionTradesOptions } = this.optionListFilter;

    const {
      numberAccountOptionsSelect,
      codeOptionsSelect,
      buySellStatus,
      activeStatus,
      isStopTypeTrades,
      valueTrades,
      revenue,
      brokerageCommission,
      netTradesFee,
      dateRange,
      dateActiveRange,
    } = selectionState;

    const isCustomerFilter = numberAccountOptionsSelect.length === 0;
    const isStokeFilter = codeOptionsSelect.length === 0;
    return (
      !isCustomerFilter ||
      !isStokeFilter ||
      buySellStatus.length !== 2 ||
      activeStatus.length !== 2 ||
      isStopTypeTrades.length !== 2 ||
      typeTradesOptions?.length !== this.optionListConfigFilter.typeTradesOptions?.length ||
      orderChannel?.length !== this.optionListConfigFilter.orderChannel?.length ||
      !this.isCheckBoxListAllSelected(this.optionListConfigFilter.statusTradesOptions ?? []) ||
      conditionTradesOptions?.length !== this.optionListConfigFilter.conditionTradesOptions?.length ||
      // codeOptionsSelect.length !== (this.optionListConfigFilter.codeOptions?.length ?? 0) ||
      // conditionTradesOptionsSelect.length !== (this.optionListConfigFilter.conditionTradesOptions?.length ?? 0) ||
      // numberAccountOptionsSelect.length !== (this.optionListConfigFilter.numberAccountOptions?.length ?? 0) ||
      // orderChannelOptionSelect.length !== (this.optionListConfigFilter.orderChannel?.length ?? 0) ||
      // (this.optionConfigFilter.statusConfirmTrades === 'dropdown' &&
      //   (statusConfirmTradesOptionsSelect.length || statusTradesOptionsSelectValue.length) !==
      //     (this.optionListConfigFilter.statusTradesOptions?.length ?? 0)) ||
      // typeTradesOptionsSelect.length !== (this.optionListConfigFilter.typeTradesOptions?.length ?? 0) ||
      // (this.optionConfigFilter.statusTrades !== 'checkbox' &&
      //   statusTradesOptionsSelectValue.length !== (this.optionListConfigFilter.statusTradesOptions?.length ?? 0)) ||
      this.checkHasValueInObject(valueTrades) ||
      this.checkHasValueInObject(revenue) ||
      this.checkHasValueInObject(brokerageCommission) ||
      this.checkHasValueInObject(netTradesFee) ||
      this.checkHasValueInObject(dateRange) ||
      this.checkHasValueInObject(dateActiveRange)
    );
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObject(data: any) {
    return !!data && ((data.start != null && data.start !== '') || (data.end != null && data.end !== ''));
  }

  /**
   * CheckFilterCheckBox
   * @returns {boolean} true/ fasle
   */
  checkFilterCheckBox() {
    let buySellStatus: number[] = [];
    if (this.isBuyStatus && this.isSellStatus) {
      buySellStatus = [0, 1];
    } else if (this.isBuyStatus) {
      buySellStatus.push(0);
    } else if (this.isSellStatus) {
      buySellStatus.push(1);
    }

    let activeStatus: number[] = [];
    if (this.isActiveStatus && this.isInActiveStatus) {
      activeStatus = [3, 2];
    } else if (this.isActiveStatus) {
      activeStatus.push(3);
    } else if (this.isInActiveStatus) {
      activeStatus.push(2);
    }

    const isStopTypeTrades: string[] = [];
    if (this.isStopLOTypeTrades && this.isStopMPTypeTrades) {
      isStopTypeTrades.push('STOP MP', 'STOP LO');
    } else if (this.isStopMPTypeTrades) {
      isStopTypeTrades.push('STOP MP');
    } else if (this.isStopLOTypeTrades) {
      isStopTypeTrades.push('STOP LO');
    }
    return { buySellStatus, activeStatus, isStopTypeTrades };
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }
  /**
   * @param {IDataChangeItem} data
   */
  receiverDataDateRange(data: IDataChangeItem) {
    this.dataDateRange = data.data;
  }

  changeStatusTradeOptions(option: IListOptions, checked: boolean) {
    option['isSelect'] = checked;
  }

  handleFocus(event: FocusEvent) {
    const inputElement = event.target as HTMLInputElement;
    inputElement.blur();
  }

  isCheckBoxListAllSelected(options: IListOptions[]) {
    return options.every((option) => option['isSelect']);
  }

  isCheckboxListAllNotSelected(options: IListOptions[]): boolean {
    return options.every((option) => !option['isSelect']);
  }

  getFormControl(field: string) {
    return this.tradeOrderFilterForm?.get(field) as FormControl;
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customer: IAllAccountNumber): string {
    if (!customer) return '';
    return `${customer.accountNumber} - ${customer.customerName}`;
  }

  displayStokeFilter(stoke: IItemStoke) {
    if (!stoke) return '';
    return `${stoke.value} - ${stoke.stoke}`;
  }
}
