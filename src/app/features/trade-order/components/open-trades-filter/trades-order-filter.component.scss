.open-trades-filter-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .header-cls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid var(--color--other--divider);
    img[alt='x-cross'] {
      cursor: pointer;
    }
  }
  .body-cls {
    display: flex;
    width: 100%;
    flex: 1;
    overflow: hidden;

    &.one-column {
      display: unset;
    }

    .box-body {
      flex: 1;
      overflow: auto;
      &:first-child {
        border-right: 1px solid var(--color--other--divider);
      }
      .drop-down-box {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 12px 24px;
        border-bottom: 1px solid var(--color--other--divider);
        &.right-box {
          gap: 12px;
        }
        .box-dropdown {
          position: relative;
          padding: 8px 16px;
          cursor: pointer;
          border: 1px solid var(--color--other--divider);
          border-radius: 8px;

          img[alt='arrow-down'] {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 16px;
          }
        }

        .check-box-list {
          display: flex;
          flex-direction: column;
          gap: 16px;
          .checkbox-cls {
            ::ng-deep {
              .mdc-label {
                display: flex;
                align-items: center;
                gap: 8px;
                text-transform: uppercase;
                font-size: 12px;
              }
            }
            .img-cls {
              width: 18px;
              height: 18px;
              object-fit: contain;
              vertical-align: middle;
              border-radius: 50%;
            }
          }
        }

        .box-calendar,
        .box-range {
          display: flex;
          gap: 8px;

          .content-from,
          .content-to {
            width: 50%;
            display: flex;
            flex-direction: column;
            gap: 8px;

            app-form-control:has(.holding-period-input) {
              .holding-period-input {
                width: 100%;
              }
            }

            .input-wrapper {
              position: relative;
              cursor: pointer;

              .calendar-input {
                padding: 10px 28px 10px 16px;
                border: 1px solid var(--Neutral-100, #dbdee0);
                border-radius: 8px;
                height: 40px;
                width: 100%;
              }

              img {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                right: 12px;
              }
            }

            .holding-period-input {
              border: 1px solid var(--Neutral-100, #dbdee0);
              padding: 10px 16px;
              border-radius: 8px;
              height: 40px;
            }
          }
        }
      }
    }
  }
  .footer-cls {
    display: flex;
    padding: 16px 24px;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid var(--color--other--divider);
    .btn {
      padding: 12px 16px;
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      cursor: pointer;
      &.brand-color {
        background-color: var(--color--brand--500);
        border: 1px solid var(--color--brand--500);
        color: var(--color--neutral--white);
      }

      &.disable {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

:host {
  display: flex;
  width: 100%;
  height: 100%;
}
.form-container-open-trades {
  width: 100%;
}

.content-to {
  .holding-period-input::placeholder {
    font-size: 12px !important;
  }
}

.err-msg {
  background-color: var(--color--danger--100);
  padding: 6px 12px;
  color: var(--color--danger--600);
}
