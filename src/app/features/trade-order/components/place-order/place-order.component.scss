.section-wrap {
  height: 100%;
  display: flex;
  gap: 24px;

  .left {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .section {
      border-radius: 8px;
      padding: 16px;
      background: var(--color--background--1);

      & > .title {
        font-size: 14px;
        text-align: center;
        margin-bottom: 2rem;
        text-transform: uppercase;
      }
    }
  }

  .right {
    height: 100%;
  }

  .order-type {
    display: flex;
    margin-bottom: 1.5rem;

    .left {
      gap: 0;

      ::ng-deep {
        .mat-mdc-select-value {
          font-size: 12px;
        }

        .disable-select {
          opacity: 0.5;
          pointer-events: none;
          background-color: var(--color--background--disable);
        }

        .disable-select:has(.mat-mdc-select-arrow-wrapper) {
          .mat-mdc-select-arrow-wrapper {
            display: none;
          }
        }
      }
    }

    .right {
      .label {
        opacity: 0;
        pointer-events: none;
      }

      ::ng-deep {
        .mdc-label {
          font-size: 12px;
        }
      }

      mat-checkbox {
        padding-top: 10px;
        padding-left: 20px;

        ::ng-deep {
          .mdc-label {
            padding-bottom: 3px;
          }
        }
      }
    }
  }

  ::ng-deep {
    .mdc-text-field--outlined .mat-mdc-form-field-infix,
    .mdc-text-field--no-label .mat-mdc-form-field-infix {
      padding-top: 8px;
      padding-bottom: 8px;
    }
  }

  .order-price-volume {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;

    .item {
      flex: 1 1;

      input.order-type-holder {
        border: none;
        width: 100%;
        padding: 8px 10px;
        text-align: center;
        font-size: 1rem;
        font-family: 'Inter', sans-serif;
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.0509803922);
      }

      &.flex-full {
        flex-basis: 100%;
      }
    }

    .derivatives-settings {
      flex-basis: 100%;
      display: flex;
      gap: 1rem;
    }
  }

  .order-quick-select {
    display: flex;
    gap: 10px;
    margin-bottom: 1.5rem;

    .option {
      flex: 1 1;
      padding: 4px 0 !important;
      border-radius: 16px !important;
      background: var(--color--neutral--100);

      &.active,
      &:hover {
        background: var(--color--accents--yellow);
      }
    }
  }

  .order-category {
    display: flex;
    gap: 1rem;

    button {
      flex: 1 1;
      display: flex;
      flex-direction: column;
      gap: 3px;
      font-size: 14px;
      color: white;
      opacity: 0.25;

      &.buy {
        background: var(--color--accents--green);
      }

      &.sell {
        background: var(--color--accents--red);
      }

      &.active {
        opacity: 1;
      }

      &.disable {
        pointer-events: none;
      }

      span:first-child {
        text-transform: uppercase;
      }
    }
  }

  .left,
  .right {
    flex: 1 1;
  }

  .label {
    // font-size: 14px;
    margin-bottom: 0.5rem;
  }

  .customer-selector {
    display: flex;
    gap: 8px;
    margin-bottom: 1.5rem;

    .customer,
    .cusGroup {
      flex: 2.5;
    }

    .subAcc {
      flex: 1;
    }
  }

  .customer-asset-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, auto);
    gap: 1.5rem;

    .asset-item {
      font-size: 14px;
      line-height: 20px;

      .label {
        color: var(--color--text--subdued);
      }

      .value {
        font-weight: 500;

        &.green {
          color: var(--color--accents--green);
        }

        &.red {
          color: var(--color--accents--red);
        }
      }
    }
  }

  // KN tới Nhóm khách hàng
  .customer-dropdown {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    border-bottom: 1px solid var(--color--other--divider);

    .customer-input-wrapper {
      position: relative;

      .customer-input {
        padding: 8px 34px 8px 16px;
        height: 40px;
        border-radius: 8px;
        border: 1px solid var(--color--other--divider);
        box-shadow: 0px 1px 2px 0px #0000000d;
        background: var(--Colors-Grey-White, #fff);
        width: 100%;
        cursor: pointer;
        text-overflow: ellipsis;
      }

      img {
        width: 18px;
        height: 18px;
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
      }
    }
  }

  .target-customers.table-wrap {
    margin-top: 1.5rem;

    .row {
      display: flex;

      &.divide {
        border-top: 1px solid var(--color--neutral--100);
      }

      .cell {
        padding: 8px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        font-size: 14px;
        border-bottom: 1px solid var(--SHAv1-Other-Divider, #f1f2f6);

        img {
          cursor: pointer;
        }

        .sub-title {
          color: var(--color--text-vibrant--secondary);
        }

        &.sub-account {
          .input-wrapper-cls {
            position: relative;
          }

          .sub-acc-input {
            width: 100%;
            border-radius: 6px;
            border: 1px solid var(--color--other--divider);
            background: var(--color--neutral--white);
            padding: 8px 12px;
          }

          img[alt='arrow-down'] {
            position: absolute;
            right: 12px;
            width: 13.33px;
            height: 13.33px;
            top: 50%;
            transform: translateY(-50%);
          }
        }

        &.delete {
          cursor: pointer;
        }
      }
    }
  }
}

.dialog-footer {
  .btn {
    width: 160px;
  }
}

.mat-mdc-option {
  // min-height: 36px !important;
  font-size: 12px !important;
}

.disable-input {
  pointer-events: none;
  background-color: var(--color--background--disable);
}

.display-none {
  display: none;
}

.hidden-customer {
  ::ng-deep {
    .err-wrap {
      display: none;
    }
  }
}
