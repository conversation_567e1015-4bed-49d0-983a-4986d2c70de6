<div class="dialog-wrap">
  <div class="dialog-header">
    <div class="title-cls typo-body-14">{{ data.title | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="dialog-body">
    <form [formGroup]="placeOrderForm" class="section-wrap">
      <div class="left">
        <div class="section">
          <div class="title">{{ 'MES-237' | translate }}</div>

          <div class="order-type">
            <div class="left">
              <div class="label typo-body-15">{{ 'MES-173' | translate }}</div>
              <app-form-control>
                <mat-form-field [class.disable-select]="data.element">
                  <mat-select formControlName="type" placeholder="LO" (selectionChange)="onTypeChange($event.value)">
                    <mat-option [value]="item.value" *ngFor="let item of orderTypeOptions" class="mat-option">
                      {{ item.label }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </app-form-control>
            </div>

            <div *ngIf="!data.element || (data.element && data.type === ETagPageOrder.PREORDER)" class="right">
              <div class="label typo-body-15">{{ 'MES-173' | translate }}</div>
              <mat-checkbox class="example-margin" formControlName="advanced">{{ 'MES-244' | translate }}</mat-checkbox>
            </div>
          </div>

          <div class="order-price-volume" *ngIf="orderType">
            <!-- ngày kích hoạt & điều kiện kích hoạt -->
            <div class="derivatives-settings" *ngIf="isDerivatives && orderAdvanced?.value">
              <div class="item">
                <div class="label typo-body-15">{{ 'MES-198' | translate }}</div>
                <mat-form-field>
                  <input
                    class="input-cls-custom typo-body-12"
                    matInput
                    [matDatepicker]="datePicker"
                    formControlName="derivativeDate"
                    (click)="openDatePicker($event)"
                    (keydown)="keydownEvent()"
                    [min]="minDate"
                  />

                  <mat-icon
                    style="cursor: pointer; width: 18px"
                    matSuffix
                    svgIcon="icon:date-picker"
                    (click)="openDatePicker($event)"
                    (keydown)="keydownEvent()"
                  ></mat-icon>

                  <mat-datepicker
                    panelClass="calendar-cls"
                    #datePicker
                    [calendarHeaderComponent]="DatepickerCustomHeader"
                  >
                  </mat-datepicker>
                </mat-form-field>
              </div>

              <div class="item">
                <div class="label typo-body-15">{{ 'MES-259' | translate }}</div>
                <mat-form-field>
                  <mat-select formControlName="derivativeCondition">
                    <mat-option [value]="item.value" *ngFor="let item of derivativesConditions" class="mat-option">
                      {{ item.label | translate }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>

            <!-- giá kích hoạt -->
            <div class="item" *ngIf="orderType.value === EOrderType.STOP_MP || orderType.value === EOrderType.STOP_LO">
              <div class="label typo-body-15">{{ 'MES-175' | translate }}</div>
              <app-amount-selector formControlName="activate" [step]="0.05"></app-amount-selector>
            </div>

            <!-- giá đặt -->
            <div class="item price">
              <div class="label typo-body-15">{{ 'MES-176' | translate }}</div>

              @if (orderType.value === EOrderType.LO || orderType.value === EOrderType.STOP_LO) {
              <app-amount-selector
                formControlName="price"
                [step]="0.05"
                [placeholder]="'MES-118' | translate"
              ></app-amount-selector>
              } @else {
              <input
                class="order-type-holder typo-field-5 fs-12"
                type="text"
                formControlName="price"
                [readonly]="true"
              />
              }
            </div>

            <!-- khối lượng -->
            <div
              class="item"
              [ngClass]="{
                'flex-full': orderType.value === EOrderType.STOP_MP || orderType.value === EOrderType.STOP_LO
              }"
            >
              <div class="label typo-body-15">{{ 'MES-229' | translate }}</div>
              <app-amount-selector
                formControlName="volume"
                [step]="5"
                placeholder="0"
                [maskType]="EAmountSelectorMaskType.VOLUME"
              ></app-amount-selector>
            </div>
          </div>

          <div class="order-category">
            @for (category of orderCategories; track $index) {
            <button
              class="btn outline"
              [ngClass]="{
                active: category.active,
                buy: category.value === EOrderCategory.BUY,
                sell: category.value === EOrderCategory.SELL,
                disable: data.element
              }"
              (click)="data.element ? null : updateOrderCategory(category.value)"
            >
              <span class="typo-body-18">{{ category.label | translate }}</span>

              <span class="typo-body-18">
                {{ 'MES-180' | translate }}: {{ transactionValue > 0 ? (transactionValue | numberFormat) : '-' }}
              </span>
            </button>
            }
          </div>
        </div>

        <div class="section">
          <div class="title">{{ 'MES-15' | translate }}</div>

          <div class="customer-selector">
            <!-- KN tới Khách hàng -->
            <div class="customer-dropdown customer">
              <div class="typo-body-15">{{ 'MES-15' | translate }}</div>
              <button [class.disable]="data.element" (click)="openPopoverCustomer($event)">
                <app-form-control [class.hidden-customer]="isLoadingCustomerInfo">
                  <div class="customer-input-wrapper">
                    <input
                      matInput
                      type="text"
                      class="customer-input"
                      formControlName="customers"
                      [placeholder]="'Tài khoản'"
                      [value]="displayCustomer"
                      readonly
                    />
                    <img [class.display-none]="data.element" src="./assets/icons/arrow-down.svg" alt="arrow-down" />
                  </div>
                </app-form-control>
              </button>
            </div>
          </div>

          <div *ngIf="orderCategoryInfo && isGotSubAccountInView()" class="customer-asset-info">
            <div *ngIf="category?.value === EOrderCategory.BUY && isSingleAccountAndSubAccount()" class="asset-item">
              <!-- Sức mua -->
              <div class="label typo-body-15">{{ 'MES-203' | translate }}</div>
              <div class="value green">
                {{ orderCategoryInfo.number === 0 ? '-' : (orderCategoryInfo.number | numberFormat) }}
              </div>
            </div>

            <!-- KL mua tối đa -->
            <div *ngIf="category?.value === EOrderCategory.BUY && isSingleAccountAndSubAccount()" class="asset-item">
              <div class="label typo-body-15">{{ 'MES-240' | translate }}</div>
              <div class="value green">
                {{ orderCategoryInfo.volume === 0 ? '-' : (orderCategoryInfo.volume | numberFormat) }}
              </div>
            </div>

            <!-- bán tối đa -->
            <div *ngIf="category?.value === EOrderCategory.SELL && isSingleAccountAndSubAccount()" class="asset-item">
              <div class="label typo-body-15">{{ 'MES-239' | translate }}</div>
              <div class="value red">
                {{ orderCategoryInfo.number === 0 ? '-' : (orderCategoryInfo.number | numberFormat) }}
              </div>
            </div>

            <!-- KL bán tối đa -->
            <div *ngIf="category?.value === EOrderCategory.SELL && isSingleAccountAndSubAccount()" class="asset-item">
              <div class="label typo-body-15">{{ 'MES-241' | translate }}</div>
              <div class="value red">
                {{ orderCategoryInfo.volume === 0 ? '-' : (orderCategoryInfo.volume | numberFormat) }}
              </div>
            </div>

            <!-- tổng dư nợ -->
            <div *ngIf="category?.value === EOrderCategory.SELL && isSingleAccountAndSubAccount()" class="asset-item">
              <div class="label typo-body-15">{{ 'MES-242' | translate }}</div>
              <div class="value red">
                {{ orderCategoryInfo.debt === 0 ? '-' : (orderCategoryInfo.debt | numberFormat) }}
              </div>
            </div>
          </div>

          <div *ngIf="this.getFormControl('customers').value" class="target-customers table-wrap">
            <div class="row header" *ngIf="infoCustomer.length">
              <!-- cột tài khoản -->
              <div class="cell" style="width: 115px">
                <div class="title typo-body-12">
                  {{ 'MES-66' | translate }}
                </div>
                <div class="sub-title typo-body-9">
                  {{ 'MES-67' | translate }}
                </div>
              </div>

              <!-- Cột tiểu khoản -->
              <div class="cell sub-account" style="width: 80px">
                <div class="title typo-body-12">
                  {{ 'MES-599' | translate }}
                </div>
                <div class="sub-title typo-body-9">
                  {{ 'MES-274' | translate }}
                </div>
              </div>

              <!-- cột volume -->
              <div class="cell" style="width: 128px">
                <div class="title typo-body-12">
                  {{ 'MES-179' | translate }}
                </div>
              </div>

              <!-- cột GTGD -->
              <div class="cell" style="width: 128px">
                <div class="title typo-body-12">
                  {{ 'MES-180' | translate }}
                </div>
              </div>

              <!-- cột chứa action delete -->
              <div
                *ngIf="!data.element"
                class="cell delete"
                style="flex: 1; text-align: center; color: var(--color--accents--red)"
                (click)="deleteAll()"
                (keydown)="keydownEvent()"
              >
                <div class="title typo-body-12">
                  {{ 'MES-69' | translate }}
                </div>
                <div class="typo-body-12" [style.white-space]="'nowrap'">
                  {{ 'MES-58' | translate | lowercase }}
                </div>
              </div>
            </div>

            <div *ngFor="let customer of infoCustomer" class="row body" [class.divide]="!customer.subAccount">
              <!-- cột tài khoản -->
              <div class="cell" style="width: 115px">
                @if(!customer.subAccount) {
                <div class="title typo-body-12">{{ customer.accountNumber }}</div>
                } @else {
                <div [style.white-space]="'nowrap'" class="title typo-body-12">
                  {{ customer.accountNumber }} - {{ customer.subAccount }}
                </div>
                }
                <div class="sub-title typo-body-9">{{ customer.customerName }}</div>
              </div>

              <!-- Cột tiểu khoản -->
              <div class="cell sub-account" style="width: 80px">
                @if(customer.subAccount) {
                <div></div>
                } @else {
                <div
                  [class.disable-input]="data.element"
                  class="input-wrapper-cls"
                  (click)="openPopoverSubAccount(customer.accountNumber, $event)"
                  (keydown)="keydownEvent()"
                >
                  <input readonly class="sub-acc-input" matInput type="text" placeholder="--" />
                  <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
                </div>
                }
              </div>

              <!-- cột volume -->
              <div class="cell" style="width: 128px">
                @if(!customer.subAccount) {
                <div></div>
                } @else {
                <div class="title typo-body-12">{{ this.placeOrderForm.get('volume')?.value | numberFormat }} CP</div>
                }
              </div>

              <!-- cột GTGD -->
              <div class="cell" style="width: 128px">
                @if(!customer.subAccount) {
                <div></div>
                } @else {
                <div class="title typo-body-12">
                  {{
                    this.placeOrderForm.get('price')?.value * 1000 * this.placeOrderForm.get('volume')?.value === 0
                      ? '-'
                      : (this.placeOrderForm.get('price')?.value * 1000 * this.placeOrderForm.get('volume')?.value
                        | numberFormat)
                  }}
                </div>
                }
              </div>

              <!-- cột chứa action delete -->
              <div
                *ngIf="!data.element"
                class="cell"
                style="flex: 1; align-items: center; justify-content: center"
                (click)="deleteItem(customer)"
                (keydown)="keydownEvent()"
              >
                <img src="assets/icons/delete-icon-red.svg" alt="delete-icon" width="20px" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="right">
        <app-stock-info
          [initialOptions]="data.initialStockOptions"
          [options]="data.stockOptions"
          [showOrderHistory]="true"
          formControlName="stock"
          [initialStock]="data.initialStock"
          [nonSelectedStockLabel]="'MES-603'"
          [canEditStock]="!data.element"
          (stockInfoChange)="stockInfo($event)"
          (stockExchangeChange)="stockExchangeInfo($event)"
        ></app-stock-info>
      </div>
    </form>
  </div>

  <div class="dialog-footer justify-center">
    <button [disabled]="placeOrderForm.invalid || isFormChange" class="btn primary typo-button-3" (click)="onConfirm()">
      {{ 'MES-89' | translate }}
    </button>

    <button mat-dialog-close class="btn outline typo-button-3">{{ 'MES-74' | translate }}</button>
  </div>
</div>
