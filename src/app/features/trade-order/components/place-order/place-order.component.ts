import { ChangeDetectorRef, Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import {Form<PERSON>rray, FormBuilder, FormControl, FormGroup,  Validators } from '@angular/forms';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepicker } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { EAmountSelectorMaskType } from '@shared/models';
import { catchError, combineLatest, debounceTime, distinctUntilChanged, of, take, takeUntil } from 'rxjs';
import { DestroyService, DialogService, MessageService } from 'src/app/core/services';
import { CustomDropdownPopupFilterComponent } from 'src/app/shared/components/custom-dropdown-popup-filter/custom-dropdown-popup-filter.component';
import { DatePickerNavigatorWithInputComponent } from 'src/app/shared/components/date-picker/date-picker-navigator-with-input/date-picker-navigator-with-input.component';
import { DatePickerStore } from 'src/app/shared/components/date-picker/date-picker/date-picker.store';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { MY_DATE_FORMAT } from 'src/app/shared/constants/date-picker';
import { IOption } from 'src/app/shared/models/dropdown-item.model';
import {
  maxOrderVolumeValidator,
  outOfTimeOrderValidator,
  priceOrderValidator,
  stockCompanyValidator,
  timeOrderValidator,
} from 'src/app/shared/validators/form';
import {
  CONVERT_STK_ORD_TYPE,
  EOrderCategory,
  EOrderType,
  EStockExchange,
  ETradingConditions,
  IOptionList,
  IOptionStock,
  IOrderCategoryInfo,
  IOrderListResponse,
  IOrderPayload,
  IEditOrderPayload,
} from '../../models/trade-order';
import { OrderConfirmComponent } from '../order-confirm/order-confirm.component';
import { EStatusTradesOrder, ETagPageOrder, OPENED_ORDERS_SESSIONS } from '../../constants/trade-order';
import { IStockSocketResponse } from 'src/app/shared/models/stock.model';
import { ICustomerOption } from 'src/app/features/recommendations/models/recommendations';
import {
  IAllAccountNumber,
  IAllLevelOfBroker,
  ICustomerListInRecommendationDialog,
} from 'src/app/shared/models/global';
import {
  selectCurrentBrokerView$,
  selectCustomerGroupList$,
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import { Store } from '@ngrx/store';
import { TradesOrderService } from '../../services/trades-order.service';
import { HashService } from 'src/app/shared/services/hash.service';
import { compareObject } from 'src/app/shared/utils/compareForm';
import { ActivatedRoute, Router } from '@angular/router';
import { getListTodayOrder } from '../../stores/trade-order.actions';
import { SharedService } from 'src/app/shared/services/shared.service';
import { HttpErrorResponse } from '@angular/common/http';
import { checkIsError } from '../../helpers/helpers';

enum ERouteTradeOrder {
  OPEN = 'open-trades',
  MATCH = 'match-orders',
  RESERVE = 'reserve-trades',
}

/**
 * Declare new component
 */
@Component({
  selector: 'app-place-order',
  templateUrl: './place-order.component.html',
  styleUrl: './place-order.component.scss',
  providers: [
    provideNativeDateAdapter(),
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
    DatePickerStore,
  ],
})
export class PlaceOrderComponent implements OnInit {
  @ViewChild('datePicker') datePicker!: MatDatepicker<any>;

  EOrderType = EOrderType;
  EOrderCategory = EOrderCategory;
  EAmountSelectorMaskType = EAmountSelectorMaskType;
  DatepickerCustomHeader = DatePickerNavigatorWithInputComponent;

  ETagPageOrder = ETagPageOrder;

  unconditionalOrderTypes: IOption[] = [
    {
      label: EOrderType.ATC,
      value: EOrderType.ATC,
    },
    {
      label: EOrderType.LO,
      value: EOrderType.LO,
    },
    {
      label: EOrderType.MTL,
      value: EOrderType.MTL,
    },
    {
      label: EOrderType.FAK,
      value: EOrderType.FAK,
    },
    {
      label: EOrderType.FOK,
      value: EOrderType.FOK,
    },
    {
      label: EOrderType.PLO,
      value: EOrderType.PLO,
    },
    {
      label: EOrderType.MP,
      value: EOrderType.MP,
    },

    {
      label: EOrderType.ATO,
      value: EOrderType.ATO,
    },
  ];

  conditionalOrderTypes: IOption[] = [
    {
      label: EOrderType.STOP_LO,
      value: EOrderType.STOP_LO,
    },
    {
      label: EOrderType.STOP_MP,
      value: EOrderType.STOP_MP,
    },
  ];

  derivativesConditions: IOption[] = [
    {
      label: 'MES-260',
      value: ETradingConditions.BEFORE_MORNING,
    },
    {
      label: 'MES-261',
      value: ETradingConditions.BEFORE_AFTERNOON,
    },
    {
      label: 'MES-262',
      value: ETradingConditions.BEFORE_ATO,
    },
    {
      label: 'MES-263',
      value: ETradingConditions.BEFORE_ATC,
    },
  ];

  // orderTypeOptions: IOption[] = [...this.unconditionalOrderTypes, ...this.conditionalOrderTypes];
  orderTypeOptions!: IOption[];

  orderCategories = [
    {
      label: 'MES-186',
      value: EOrderCategory.BUY,
      active: true,
    },
    {
      label: 'MES-185',
      value: EOrderCategory.SELL,
      active: false,
    },
  ];

  proportionList = [
    {
      label: '25%',
      value: 0.25,
      active: false,
    },
    {
      label: '50%',
      value: 0.5,
      active: false,
    },
    {
      label: '75%',
      value: 0.75,
      active: false,
    },
    {
      label: '100%',
      value: 1,
      active: false,
    },
  ];

  noInputOrderTypes = [
    EOrderType.MP,
    EOrderType.PLO,
    EOrderType.ATO,
    EOrderType.ATC,
    EOrderType.MTL,
    EOrderType.FOK,
    EOrderType.FAK,
  ];

  placeOrderForm: FormGroup;

  isDerivatives = false;
  showQuickSelect = false;

  selectedCustomers: IOptionList[] = [];
  listCustomerOptions: IOptionList[] = [];

  customerGroup!: string | number;
  selectedCustomerGroups: IOptionList[] = [];
  listCustomerGroupOptions: IOptionList[] = [];

  transactionValue = 0;

  minDate: Date = new Date();
  currentStockInfo!: IStockSocketResponse;

  infoCustomer!: any[];
  allCustomer!: IAllAccountNumber[];

  customerOptions: ICustomerOption[] = [];
  customerGroupOptions: ICustomerOption[] = [];

  orderCategoryInfo!: IOrderCategoryInfo;
  ceilingPrice!: number;
  macAddress!: string;

  customerList!: ICustomerListInRecommendationDialog[];

  customerGroupList!: ICustomerListInRecommendationDialog[];

  isFormChange = false;

  isLoadingCustomerInfo = false;

  /**
   * Constructor
   * @param fb
   * @param dialogService
   * @param popoverService
   * @param dialogRef
   * @param _destroy
   * @param data
   * @param data.isDerivatives
   * @param popoverRef
   */
  constructor(
    private readonly fb: FormBuilder,
    private readonly dialogService: DialogService,
    private readonly popoverService: PopoverService,
    private readonly dialogRef: MatDialogRef<PlaceOrderComponent>,
    private readonly _destroy: DestroyService,
    @Inject(MAT_DIALOG_DATA)
    public data: {
      title: string;
      isDerivatives: boolean;
      stockOptions: IOptionStock[];
      initialStockOptions: IOptionStock[];
      initialStock?: IOptionStock;
      element?: IOrderListResponse; // Khi Edit
      type?: number; // Loại lệnh
    },
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly store: Store,
    private readonly tradesOrderService: TradesOrderService,
    private readonly hashService: HashService,
    private readonly cdr: ChangeDetectorRef,
    private readonly route: Router,
    private readonly shareService: SharedService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly messageService: MessageService
  ) {
    this.placeOrderForm = this.initForm();
    this.isDerivatives = data.isDerivatives;
    if (this.isDerivatives) {
      this.orderCategories = [
        {
          label: 'LONG',
          value: EOrderCategory.BUY,
          active: true,
        },
        {
          label: 'SHORT',
          value: EOrderCategory.SELL,
          active: false,
        },
      ];
    }
  }

  /**
   * get "price" control
   */
  get orderPrice() {
    return this.placeOrderForm.get('price');
  }

  /**
   * get "volume" control
   */
  get orderVolume() {
    return this.placeOrderForm.get('volume');
  }

  /**
   * get "type" control
   */
  get orderType() {
    return this.placeOrderForm.get('type');
  }

  /**
   * get "advanced" control
   */
  get orderAdvanced() {
    return this.placeOrderForm.get('advanced');
  }

  /**
   * get "category" control
   */
  get category() {
    return this.placeOrderForm.get('category');
  }

  /**
   *  get "customers" control
   */
  get customers() {
    return this.placeOrderForm.get('customers');
  }

  /**
   * subAccount form
   */
  get subAccountList(): FormArray {
    return this.placeOrderForm.get('subAccountList') as FormArray;
  }

  /**
   *
   */
  ngOnInit(): void {
    this.setupSubscribersOnPlaceOrderForm();
    this.fillForm();
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(take(1))
      .subscribe((customers) => {
        this.customerList = customers.map((c) => ({
          customerGroup: c.accountNumber,
          customerName: c.customerName,
          // identity: '************',
          // telephone: '**********',
          isSelect: false,
        }));
      });

    this.store
      .select(selectCustomerGroupList$)
      .pipe(take(1))
      .subscribe((groups) => {
        this.customerGroupList = groups.map((g) => ({
          customerGroup: g.name,
          id: g.id,
          isSelect: false,
        }));
      });

    if (this.customerList) {
      this.listCustomerOptions = this.updateCustomerAndGroup(
        this.customerList as ICustomerOption[],
        [''],
        'customerName'
      );
    }

    if (this.customerGroupList) {
      this.listCustomerGroupOptions = this.updateCustomerAndGroup(
        this.customerGroupList as ICustomerOption[],
        [''],
        'customerGroup'
      );
    }

    // update order type options by time
    setInterval(() => this.updateOrderTypeOptions(), 60000);
  }

  /**
   * getAllCustomerAndSubAccount
   */
  getAllCustomerAndSubAccount() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((customerAcc) => {
        if (!customerAcc.length) return;
        this.allCustomer = customerAcc;
      });
  }

  /**
   * init FormGroup
   */
  private initForm(): FormGroup {
    const formConfig = {
      type: [null, [Validators.required]],
      category: [EOrderCategory.BUY, [Validators.required]],
      advanced: [false],
      price: [null, [Validators.required]],
      volume: [null, [Validators.required, maxOrderVolumeValidator()]],
      customers: [null, [Validators.required]],
      customerGroups: [null],
      stock: [null, [Validators.required, stockCompanyValidator()]],
      stockExchange: [null],
      subAccountList: this.fb.array([], [Validators.required]),
    };
   return this.fb.group(formConfig, { validator: this.validValueOrderVolumne }); //NOSONAR
  }

  validValueOrderVolumne(group: FormGroup) {
    const volumeControl = group.controls['volume'];
    const advancedControl = group.controls['advanced'];

    const volume = volumeControl.value;
    const currentErrors = volumeControl.errors || {};
    const { isNotMultipleOf100, isNotMultipleOf100AndLarger100, ...remainingErrors } = currentErrors;

    if (!volume || volume === '') {
      volumeControl.setErrors(checkIsError(remainingErrors));
      return;
    }
    if (advancedControl.value) {
      if (+volume % 100 !== 0) {
        volumeControl.setErrors({ ...currentErrors, isNotMultipleOf100: true });
      } else {
        const { isNotMultipleOf100, ...remainingErrors } = currentErrors;
        volumeControl.setErrors(checkIsError(remainingErrors));
      }
    } else if(+volume % 100 !== 0 && +volume > 100) {
        volumeControl.setErrors({ ...currentErrors, isNotMultipleOf100AndLarger100: true });
    }else {
       const { isNotMultipleOf100AndLarger100, ...remainingErrors } = currentErrors;
        volumeControl.setErrors(checkIsError(remainingErrors));
    }
  }

  /**
   * fillForm
   */
  fillForm() {
    this.getAllCustomerAndSubAccount();

    if (this.data.initialStock) {
      this.placeOrderForm.patchValue({
        stock: this.data.initialStock.value,
        stockExchange: this.data.initialStock.stoke,
      });
    }

    // Edit
    if (this.data.element) {
      const { typeTrades, orderPrice, orderVolume, accountNumber, subAccount, status } = this.data.element;

      const matchAccount = this.allCustomer.filter(
        (customers) => customers.accountNumber === accountNumber.split('-')[0].trim()
      );

      if (matchAccount) {
        const brokerCode = this.activatedRoute.snapshot.queryParams['brokerId'];
        const payloadToGetInfoOfCustomer = {
          accountNumbers: [accountNumber.split('-')[0].trim()],
          brokerCode: [brokerCode],
          userType: '',
          fromBirthYear: '',
          toBirthYear: '',
          searchKey: '',
        };

        this.shareService
          .getPersonalInfoList(payloadToGetInfoOfCustomer)
          .pipe(
            take(1),
            catchError((err: HttpErrorResponse) => of(err))
          )
          .subscribe((data) => {
            if (data instanceof HttpErrorResponse) {
              this.messageService.error(data.message);
              return;
            }

            const { identity } = data[0];

            const accountList = matchAccount.map((d) => ({
              accountNumber: d.accountNumber,
              customerName: d.customerName,
            }));

            this.infoCustomer = [...accountList];

            const subAccountList = matchAccount.map((d) => ({
              acnt_no: d.accountNumber,
              idno: identity,
              sub_no: subAccount,
            }));

            const subAccountInView = matchAccount.map((d) => ({
              accountNumber: d.accountNumber,
              customerName: d.customerName,
              idNo: identity,
              subAccount: subAccount,
            }));

            this.infoCustomer.splice(1, 1, subAccountInView[0]);

            this.orderCategories = this.orderCategories.map((d) => ({
              ...d,
              active: d.value === (status === EStatusTradesOrder.BUY ? EOrderCategory.BUY : EOrderCategory.SELL),
            }));

            this.placeOrderForm.patchValue({
              type: typeTrades,
              price: orderPrice,
              volume: orderVolume,
              customers: accountList,
              category: status === EStatusTradesOrder.BUY ? EOrderCategory.BUY : EOrderCategory.SELL,
            });

            this.patchValueSubAcc(subAccountList);
            this.trackFormChange();
          });
      }

      return;
    }
    this.setValidatorTimeToOrder();
  }

  /**
   * set validator thời gian đặt lệnh khi chọn loại lệnh
   */
  setValidatorTimeToOrder() {
    const typeOrderControl = this.getFormControl('type');
    typeOrderControl.setValidators([
      timeOrderValidator(this.getFormControl('stockExchange').value),
      Validators.required,
    ]);
    typeOrderControl.updateValueAndValidity();
    typeOrderControl.markAsTouched();
    typeOrderControl.markAsDirty();
  }

  /**
   * Thay đổi loại lệnh
   */
  onTypeChange(type: string) {
    this.getFormControl('type').setValue(type);
    if (this.getFormControl('type').value === EOrderType.LO) {
      this.getFormControl('price').setValue(this.currentStockInfo.lastValue);
    }
    this.updateFormOnTypeChange(type);
  }

  /**
   * UpdateCustomerAndGroup
   * @param originlist originlist
   * @param data data
   * @param type type
   * @returns {ICustomerOption[]} new origin
   */
  updateCustomerAndGroup(originlist: ICustomerOption[], data: string[] | null, type: string) {
    const isSelect = (value: string) => data === null || data.includes(value);
    return originlist.map((t) => ({
      ...t,
      isSelect: isSelect(t[type] as string),
    }));
  }

  /**
   * update current stock info from app-stock-info
   * @param {IStockSocketResponse} data
   */
  stockInfo(data: IStockSocketResponse) {
    this.currentStockInfo = data;
    this.ceilingPrice = +this.currentStockInfo.ceilingPrice;

    // Validator price form control
    if (!this.getFormControl('advanced').value) {
      const priceControl = this.getFormControl('price');
      priceControl.setValidators([Validators.required, priceOrderValidator(data.floorPrice, data.ceilingPrice)]);
      priceControl.updateValueAndValidity();
      priceControl.markAsTouched();
      priceControl.markAsDirty();
    }
  }

  /**
   * Dựa vào sàn CK sẽ đổi dropdown this.orderTypeOptions
   * @param {string} stockExchange
   */
  stockExchangeInfo(event: { stockExchange: string; lastValue: string }) {
    const { stockExchange, lastValue } = event;
    this.getFormControl('stockExchange').setValue(stockExchange);
    this.updateOrderTypeOptions();
    if (!this.data.element) {
      this.orderPrice?.patchValue(lastValue);
    }

    this.getPurchasingPowerInfo();
  }

  /**
   * GetFormControl
   * @param field
   * @returns {any} control from form
   */
  getFormControl(field: string) {
    return this.placeOrderForm.get(field) as FormControl;
  }

  /**
   * check valid without stock
   */
  isSpecificControlsValid(): boolean {
    const controlsToCheck = ['type', 'category', 'advanced', 'price', 'volume'];
    return controlsToCheck.every((controlName) => {
      const control = this.placeOrderForm.get(controlName);
      return (
        control &&
        control.valid &&
        (this.getFormControl('customers').value || this.getFormControl('customerGroups').value)
      );
    });
  }

  /**
   *
   * @param orderPrice
   * @param orderVolume
   */
  private calculateTransactionValue(orderPrice: number, orderVolume: number) {
    this.transactionValue = orderPrice * orderVolume * 1000;

    // kiểm tra xem GTGD có phải 1 số hợp lệ không
    // trong trường hợp lệnh thuộc loại MP + ATO + ATC + MTL + FOK +FAK, giá đặt là tên lệnh
    // do đó, transactionValue sẽ không phải là 1 số, tạm thời sẽ hiển thị '-'
    if (isNaN(this.transactionValue)) {
      this.transactionValue = 0;
    }
  }

  /**
   *
   */
  private setupSubscribersOnPlaceOrderForm() {
    combineLatest([this.orderPrice!.valueChanges, this.orderVolume!.valueChanges])
      .pipe(distinctUntilChanged(), takeUntil(this._destroy))
      .subscribe(([orderPrice, orderVolume]) => {
        this.calculateTransactionValue(orderPrice, orderVolume);
        this.trackFormChange();
      });

    this.orderAdvanced!.valueChanges.pipe(takeUntil(this._destroy)).subscribe((advanced) => {
      this.updateFormOnAdvancedChange(advanced);
      this.updateOrderTypeOptions();
      advanced ? this.clearPriceValidators() : this.setPriceValidators();
    });

    this.customers?.valueChanges.pipe(takeUntil(this._destroy)).subscribe((customers) => {
      if (customers && this.data.element) {
        this.customers?.setValue(customers[0].accountNumber, { emitEvent: false });
      }
    });

    this.subAccountList.valueChanges.pipe(takeUntil(this._destroy)).subscribe((subAccs) => {
      // Get Purchasing Power Asset Info
      this.getPurchasingPowerInfo();
    });

    this.placeOrderForm
      .get('stock')
      ?.valueChanges.pipe(takeUntil(this._destroy))
      .subscribe((stock) => {
        if (!stock?.value) {
          this.orderType?.patchValue(null);
          this.placeOrderForm.patchValue({ price: null });
          this.getFormControl('stockExchange').patchValue(null);
          this.orderTypeOptions = [];
        }

        this.getFormControl('price').patchValue(this.currentStockInfo?.lastValue ?? null);
      });

    combineLatest([
      this.getFormControl('type').valueChanges,
      this.getFormControl('price').valueChanges,
      this.subAccountList.valueChanges,
      this.getFormControl('stock').valueChanges,
    ])
      .pipe(
        debounceTime(300),
        distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr))
      )
      .subscribe(([orderType, orderPrice, subAccs, stockInfo]) => {
        this.updateOrderTypeOptions();
        const orderPriceByStockExchange = (stockExchange: string): string => {
          if (stockExchange === EStockExchange.HNX) {
            switch (orderType) {
              case EOrderType.LO:
                return Math.round(+orderPrice * 1000).toString() || '0';

              case EOrderType.ATC:
              case EOrderType.FOK:
              case EOrderType.FAK:
              case EOrderType.MTL:
              case EOrderType.PLO:
                return Math.round(this.ceilingPrice * 1000).toString();

              default:
                return '0';
            }
          }

          if (stockExchange === EStockExchange.HOSE) {
            switch (orderType) {
              case EOrderType.LO:
                return Math.round(+orderPrice * 1000).toString() || '0';

              case EOrderType.ATC:
              case EOrderType.ATO:
              case EOrderType.MP:
                return Math.round(this.ceilingPrice * 1000).toString();

              default:
                return '0';
            }
          }

          if (stockExchange === EStockExchange.UPCOM) {
            return Math.round(+orderPrice * 1000).toString() || '0';
          }

          return Math.round(+orderPrice * 1000).toString();
        };

        const payload = {
          acnt_no: subAccs[0].acnt_no,
          sub_no: subAccs[0].sub_no,
          bank_cd: '9999',
          stk_cd: stockInfo.id,
          mkt_trd_tp: stockInfo.stoke,
          ord_pri: orderPriceByStockExchange(stockInfo.stoke),
        };

        if (orderPrice > this.ceilingPrice) return;
        this.tradesOrderService
          .getBuyableInfo(payload)
          .pipe(
            take(1),
            catchError((error) => {
              this.orderCategoryInfo = {
                number: 0,
                volume: 0,
              };
              return of([]);
            })
          )
          .subscribe((data) => {
            this.orderCategoryInfo = {
              number: +data[0].buying_power || 0,
              volume: +data[0].buy_abl_qty || 0,
            };
          });
      });
  }

  /**
   * update order category to BUY / SELL when broker clicks a button
   * @param selected
   */
  updateOrderCategory(selected: EOrderCategory) {
    // reset the quick select on category changed
    this.proportionList.forEach((item) => {
      item.active = false;
    });

    // patch value to form
    this.placeOrderForm.patchValue({ category: selected });

    // set active state on selected category
    this.orderCategories = this.orderCategories.map((cate) => ({
      ...cate,
      active: cate.value === selected,
    }));

    // display the quick select if broker selects SELL category
    this.showQuickSelect = selected === EOrderCategory.SELL;

    this.getPurchasingPowerInfo();
  }

  /**
   *
   * @param orderType
   */
  private updateFormOnTypeChange(orderType: string) {
    const typeOrderControl = this.getFormControl('type');

    if (this.orderAdvanced?.value) typeOrderControl.setValidators([Validators.required]);
    else
      typeOrderControl.setValidators([
        Validators.required,
        timeOrderValidator(this.getFormControl('stockExchange').value), // Update validator with new stockExchange value
      ]);

    typeOrderControl.updateValueAndValidity(); // Refresh validation
    // const conditionalOrderTypes = [EOrderType.STOP_LO, EOrderType.STOP_MP];
    if (this.noInputOrderTypes.includes(orderType as EOrderType)) {
      this.placeOrderForm.patchValue({ price: orderType });
    }
  }

  /**
   * hiển thị thêm 2 input ngày kích hoạt và điều kiện kích hoạt nếu muốn đặt trước lệnh phái sinh
   * @param status
   */
  private updateFormOnAdvancedChange(status: boolean) {
    if (status) this.getFormControl('type').setValidators([Validators.required]);
    else
      this.getFormControl('type').setValidators([
        Validators.required,
        timeOrderValidator(this.getFormControl('stockExchange').value), // Update validator with new stockExchange value
      ]);

    this.getFormControl('type').updateValueAndValidity();

    if (status && this.isDerivatives) {
      this.placeOrderForm.addControl(
        'derivativeCondition',
        new FormControl(this.derivativesConditions[0].value, Validators.required)
      );

      this.placeOrderForm.addControl('derivativeDate', new FormControl(new Date(), Validators.required));
    } else {
      this.placeOrderForm.removeControl('derivativeCondition');
      this.placeOrderForm.removeControl('derivativeDate');
    }
  }

  private clearPriceValidators() {
    this.getFormControl('price').clearValidators();
    this.getFormControl('price').updateValueAndValidity();
  }

  private setPriceValidators() {
    const priceControl = this.getFormControl('price');
    priceControl.setValidators([
      Validators.required,
      priceOrderValidator(this.currentStockInfo.floorPrice, this.currentStockInfo.ceilingPrice),
    ]);
    priceControl.updateValueAndValidity();
  }

  /**
   *
   * @param itemIdx
   */
  handleQuickSelect(itemIdx: number) {
    this.proportionList = this.proportionList.map((item, idx) => ({
      ...item,
      active: idx <= itemIdx,
    }));
  }

  /**
   *
   * @param event - event
   */
  openPopoverCustomer(event: Event) {
    if (this.data.element) return;

    let originElement = event.target as HTMLElement;
    if (originElement.nodeName !== 'DIV') {
      originElement = originElement.parentElement as HTMLElement;
    }

    const popupRef = this.popoverService.open({
      origin: originElement,
      content: CustomDropdownPopupFilterComponent,
      position: 2,
      width: originElement.offsetWidth,
      height: 280,
      data: this.listCustomerOptions,
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100'],
      componentConfig: {
        searchKey: ['customerGroup', 'customerName'],
        displayFnc: (v: any) => `${v.customerGroup} - ${v.customerName}`,
        listFilterOptions: this.listCustomerOptions,
        // isAllSelected: this.listCustomerOptions?.every((t) => t.isSelect === true),
        allOptions: false, // remove this if multiple choice
      },
    });

    // Single choice
    popupRef.afterClosed$.pipe(take(1)).subscribe({
      next: (value) => {
        if (!value.data?.length) return;

        const selectedCustomer = value.data?.map((d) => ({
          accountNumber: d.customerGroup,
          customerName: d.customerName,
        }));

        const brokerCode = this.activatedRoute.snapshot.queryParams['brokerId'];

        if (
          this.getFormControl('customers').value &&
          compareObject(selectedCustomer[0], this.getFormControl('customers').value[0])
        )
          return;

        this.isLoadingCustomerInfo = true;

        const payloadToGetInfoOfCustomer = {
          accountNumbers: selectedCustomer[0].accountNumber ? [selectedCustomer[0].accountNumber] : [],
          brokerCode: [brokerCode],
          userType: '',
          fromBirthYear: '',
          toBirthYear: '',
          searchKey: '',
        };

        this.shareService
          .getPersonalInfoList(payloadToGetInfoOfCustomer)
          .pipe(
            take(1),
            catchError((err: HttpErrorResponse) => of(err))
          )
          .subscribe((data) => {
            this.isLoadingCustomerInfo = false;
            if (data instanceof HttpErrorResponse) {
              this.messageService.error(data.message);
              return;
            }

            const { identity, telephone } = data[0];

            this.listCustomerOptions = this.listCustomerOptions.map((customer) => ({
              ...customer,
              isSelect: customer.customerGroup === selectedCustomer[0].accountNumber,
            }));

            this.infoCustomer = this.listCustomerOptions
              .filter((customer) => customer.isSelect)
              .map((d: any) => ({
                customerName: d.customerName ?? '',
                accountNumber: d.customerGroup ?? '',
                identity,
                telephone,
              }));

            this.placeOrderForm.patchValue({
              customers: this.infoCustomer.filter((d) => !d.subAccount),
            });
            this.cdr.detectChanges();
          });
      },
    });
  }

  /**
   * OpenPopoverCustomerGroup
   * @param event - event
   */
  openPopoverCustomerGroup(event: Event) {
    let originElement = event.target as HTMLElement;
    if (originElement.nodeName !== 'DIV') {
      originElement = originElement.parentElement as HTMLElement;
    }
    const popupRef = this.popoverService.open({
      origin: originElement,
      content: CustomDropdownPopupFilterComponent,
      position: 2,
      width: originElement.offsetWidth,
      height: 280,
      data: this.listCustomerGroupOptions,
      panelClass: ['dropdown-overlay-common'],
      componentConfig: {
        searchKey: ['customerGroup', 'customerName'],
        displayFnc: (v: any) => v.customerGroup,
        listFilterOptions: this.listCustomerGroupOptions,
        isAllSelected: this.listCustomerGroupOptions?.every((t) => t.isSelect === true),
      },
    });

    popupRef.afterClosed$.pipe(take(1)).subscribe({
      next: (value) => {
        if (!value) return;
        const customerGroupOption = (value.data as IOptionList[]) ?? [];
        this.listCustomerGroupOptions.forEach((t: IOptionList) => {
          t.isSelect = customerGroupOption.some((e) => e.customerGroup == t.customerGroup);
        });

        const customerGroupList = value.data?.map((d) => d.customerGroup);
        this.selectedCustomerGroups = value.data || [];
        const isEqual =
          JSON.stringify(customerGroupList) === JSON.stringify(this.getFormControl('customerGroups').value);
        if (isEqual) return;
        this.placeOrderForm.patchValue({ customerGroups: customerGroupList });
      },
    });
  }

  /**
   * openPopoverSubAccount
   * @param {string} accountNumber
   * @param {Event} event
   */
  openPopoverSubAccount(accountNumber: string, event: Event) {
    let originElement = event.target as HTMLElement;
    if (originElement.nodeName !== 'DIV') {
      originElement = originElement.parentElement as HTMLElement;
    }
    // Tìm account
    const matchAccount = this.allCustomer.find((customers) => customers.accountNumber === accountNumber);
    if (!matchAccount) return;

    this.shareService
      .getAccountNumberAndSubAccount([accountNumber])
      .pipe(take(1))
      .subscribe((data) => {
        if (!data.length) return;
        const listSubAccount = data[0].subAccounts.map((d) => ({
          label: d.subNo,
          value: d.subNo,
          isSelect: this.subAccountList.value.some(
            (subAcc: any) => subAcc.acnt_no === matchAccount.accountNumber && subAcc.sub_no === d.subNo
          ),
        }));

        const popupRef = this.popoverService.open({
          origin: originElement,
          content: CustomDropdownPopupFilterComponent,
          position: 2,
          width: 120,
          height: listSubAccount.length > 1 ? 150 : 120,
          data: listSubAccount,
          panelClass: ['dropdown-overlay-common'],
          componentConfig: {
            searchKey: ['value'],
            displayFnc: (v: any) => `${v.label}`,
            listFilterOptions: listSubAccount,
            // isAllSelected: listSubAccount?.every((t) => t.isSelect === true),
            allOptions: false, // remove this if multiple choice
          },
        });

        popupRef.afterClosed$.pipe(take(1)).subscribe({
          next: (value) => {
            if (!value.data?.length) return;
            const idNo = this.infoCustomer.find((customer) => !customer.subAccount)['identity'];
            const selectedSubAcc = value.data.map((d: any) => ({
              accountNumber: matchAccount.accountNumber,
              subAccount: d.value,
              customerName: matchAccount.customerName,
              idNo,
            }));
            // Update giao diện
            this.infoCustomer.splice(1, 1, selectedSubAcc[0]);
            // Update FormControl
            this.patchValueSubAcc(selectedSubAcc);
            // Get Purchasing Power Asset Info
            this.getPurchasingPowerInfo();
          },
        });
      });
  }

  /**
   *
   * @param $event
   */
  openDatePicker($event: MouseEvent) {
    this.datePicker.open();
  }

  /**
   * deleteItem
   * @param customer
   */
  deleteItem(customer: any) {
    // FIX THIS LATER
    this.infoCustomer = [];
    this.placeOrderForm.patchValue({ customers: null });
    this.subAccountList.clear();
    this.listCustomerOptions = this.listCustomerOptions.map((item) => ({ ...item, isSelect: false }));
  }

  /**
   * deleteAll
   */
  deleteAll() {
    this.infoCustomer = [];
    this.listCustomerOptions.forEach((t: IOptionList) => {
      t.isSelect = false;
    });
    this.selectedCustomers = [];
    if (this.infoCustomer?.length == 0) {
      this.placeOrderForm.patchValue({ customers: null });
      this.subAccountList.clear();
      this.listCustomerOptions = this.listCustomerOptions.map((item) => ({ ...item, isSelect: false }));
    }
  }

  /**
   * getLabelOfInput
   * @param {string} formControlName
   * @param {string} unit
   * @returns {string}
   */
  getLabelOfInput(formControlName: string, unit: string) {
    const value = this.getFormControl(formControlName).value;
    if (!value?.length) return '';
    return `${value.length} ${unit}`;
  }

  /**
   * Updating values into the FormArray
   */
  patchValueSubAcc(subAccountData: any[]) {
    this.subAccountList.clear();
    this.pushSubAccounts(subAccountData);
  }

  /**
   * Push values to FormArray
   */
  pushSubAccounts(subAccountData: any[]): void {
    if (!subAccountData || !Array.isArray(subAccountData)) return; // Ensure subAccountData is an array

    subAccountData.forEach((item) => {
      const exists = this.subAccountList.controls.some(
        (control) => control.value.acnt_no === item.accountNumber && control.value.sub_no === item.subAccount
      );

      if (!exists) {
        this.subAccountList.push(
          new FormGroup({
            acnt_no: new FormControl(item.accountNumber ?? item.acnt_no),
            sub_no: new FormControl(item.subAccount ?? item.sub_no),
            idno: new FormControl(item.idNo ?? item.idno),
          })
        );
      }
    });
  }

  /**
   * Lấy thông tin sức mua,KL ,tỉ lệ CMR,..
   */
  getPurchasingPowerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(take(1))
      .subscribe((user) => {
        this.hashService.hashWithSHA256(user.brokerCode, 'SHA').then((hash) => (this.macAddress = hash));

        switch (this.category?.value) {
          case EOrderCategory.BUY:
            break;

          case EOrderCategory.SELL:
            {
              const { acnt_no, sub_no } = this.subAccountList.value[0];
              const payload = {
                accountNumber: acnt_no,
                subAccount: sub_no,
                brokerCode: user.brokerCode,
                stockCode: this.placeOrderForm.get('stock')?.value.id ?? this.placeOrderForm.get('stock')?.value,
              };
              this.tradesOrderService
                .getInvestBySubAccount(payload)
                .pipe(take(1))
                .subscribe((data) => {
                  if (!data) return;
                  this.orderCategoryInfo = {
                    number:
                      this.ceilingPrice * +data.sell_able_qty === 0 ? 0 : +this.ceilingPrice * +data.sell_able_qty, // volume * gia tran
                    volume: +data.sell_able_qty, // KL giao dich o DMDT
                    debt: +data.loan_total, // debt
                  };
                });
            }

            break;

          default:
            break;
        }
      });
  }

  /**
   * Check chọn 1 tài khoản và 1 subAcc
   */
  isSingleAccountAndSubAccount() {
    const matchAcccount = this.infoCustomer.filter((customer) => customer && !customer.subAccount);
    return matchAcccount.length === 1 && this.subAccountList.value.length === 1;
  }

  /**
   *
   */
  onConfirm() {
    const {
      type,
      category,
      price,
      volume,
      stock,
      subAccountList,
      advanced,
      activate,
      derivativeCondition,
      derivativeDate,
      customers,
    } = this.placeOrderForm.value;

    this.store
      .select(selectCurrentBrokerView$)
      .pipe(take(1))
      .subscribe((user) => {
        const matchCustomer = this.infoCustomer.find((customer) => !customer.subAccount);

        let orderConfirmData: IOrderPayload = {
          hts_user_id: user.userName,
          hts_user_nm: user.brokerName,
          cli_mac_addr: this.macAddress,
          account_list: subAccountList,
          mkt_type: stock.stoke,
          stk_cd: stock.value,
          stk_ord_tp: CONVERT_STK_ORD_TYPE[type],
          ord_qty: volume.toString(),
          ord_pri: this.isNoInputPriceOrder() ? price : (+price * 1000).toString(),
          bank_cd: '9999',
          bank_acnt_no: '9999',
          lang_code: 'V',
          ord_mdm_tp: '01',
        };

        let editOrderConfirmData: IEditOrderPayload | null = null;
        if (this.data.element) {
          const { subAccount, ordNo } = this.data.element;
          editOrderConfirmData = {
            hts_user_id: user.userName,
            hts_user_nm: user.brokerName,
            cli_mac_addr: this.macAddress,
            acnt_no: customers,
            sub_no: subAccount,
            idno: subAccountList[0].idno, // fix later: hard code
            brch_cd: this.data.element ? this.data.element.bnhCd : null,
            ord_no: ordNo.toString(),
            ord_qty: volume.toString(),
            ord_pri: this.isNoInputPriceOrder() ? price : (+price * 1000).toString(),
            ord_mdm_tp: '01',
            stk_cd: stock.value,
            mkt_tp: stock.stoke,
            langCode: 'V',
            stkOrdTp: CONVERT_STK_ORD_TYPE[type],
            bankCd: '9999',
          };
        }

        // Lệnh mua và bán đặt trước
        if (advanced && matchCustomer) {
          const { telephone } = matchCustomer;
          orderConfirmData = {
            ...orderConfirmData,
            tel_no: typeof telephone === 'string' ? telephone : undefined,
          };
        }

        const dataShowInPopup = {
          title: this.data.element ? 'MES-625' : 'MES-245',
          isDerivatives: this.isDerivatives, // Phái sinh
          type,
          category,
          price: this.isNoInputPriceOrder() ? price : +price,
          volume,
          advanced,
          stockCompany: stock.value,
          subAccounts: this.infoCustomer.filter((cus) => cus.subAccount),
          activate,
          derivativeCondition,
          derivativeDate,
        };

        const ref = this.dialogService.open(OrderConfirmComponent, {
          width: '600px',
          maxHeight: '95vh',
          data: {
            dataShowInPopup,
            orderConfirmData: editOrderConfirmData ?? orderConfirmData,
            action: this.data.element ? 'edit' : 'create',
          },
        });

        ref
          .afterClosed()
          .pipe(take(1))
          .subscribe((value) => {
            if (!value) return;
            this.getOrderList();
            this.dialogRef.close();
          });
      });
  }

  /**
   * Check thuộc loại giá không thể điên số
   */
  isNoInputPriceOrder() {
    return this.noInputOrderTypes.includes(this.getFormControl('price').value);
  }

  get displayCustomer(): string {
    const value = this.getFormControl('customers').value;
    return Array.isArray(value) && value.length ? `${value[0].accountNumber}` : value ?? '';
  }

  trackFormChange() {
    if (!this.data.element) return;
    this.isFormChange =
      this.data.element?.orderPrice === +this.orderPrice?.value &&
      this.data.element?.orderVolume === +this.orderVolume?.value;
  }

  getOrderList() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(take(1))
      .subscribe((customers) => {
        if (!customers.length) return;
        const urlSegments = this.route.url.split('?')[0].split('/');
        const url = urlSegments[urlSegments.length - 1] || '';

        let tagPageOrder: ETagPageOrder;
        if (url === ERouteTradeOrder.OPEN) {
          tagPageOrder = ETagPageOrder.OPENTRADE;
        } else if (url === ERouteTradeOrder.MATCH) {
          tagPageOrder = ETagPageOrder.MATCHTRADE;
        } else {
          tagPageOrder = ETagPageOrder.PREORDER;
        }

        this.store
          .select(selectAllBrokerLevelListByBrokerView$)
          .pipe(takeUntil(this._destroy))
          .subscribe((allBrokerLevelList) => {
            const currentBrokerCode = this.activatedRoute.snapshot.queryParams['brokerId'];
            this.store.dispatch(
              getListTodayOrder({
                accountNumbers: [],
                brokerCode: this.getAllChildBrokerCodesPlaceOrder(allBrokerLevelList, currentBrokerCode),
                tagPageOrder,
              })
            );
          });
      });
  }

  updateOrderTypeOptions() {
    if (this.data.element) return;
    const currentTime = new Date();
    const formattedTime = `${this.padTime(currentTime.getHours())}:${this.padTime(currentTime.getMinutes())}`;

    const activeSesstionIndex = OPENED_ORDERS_SESSIONS.findIndex(
      (session) => formattedTime >= session.start && formattedTime <= session.end
    );

    const stockExchange = this.getFormControl('stockExchange').value;
    const isReserved = this.orderAdvanced?.value;
    const currentType = this.orderType?.value;
    let orders: { label: string; value: string }[] = [];
    switch (stockExchange) {
      case EStockExchange.HOSE:
        orders = isReserved
          ? OPENED_ORDERS_SESSIONS[activeSesstionIndex].reservedHoseOrders
          : OPENED_ORDERS_SESSIONS[activeSesstionIndex].hoseOrders;
        break;
      case EStockExchange.HNX:
        orders = isReserved
          ? OPENED_ORDERS_SESSIONS[activeSesstionIndex].reservedHnxOrders
          : OPENED_ORDERS_SESSIONS[activeSesstionIndex].hnxOrders;
        break;

      case EStockExchange.UPCOM:
        orders = isReserved
          ? OPENED_ORDERS_SESSIONS[activeSesstionIndex].reservedUpcomOrders
          : OPENED_ORDERS_SESSIONS[activeSesstionIndex].upcomOrders;
        break;

      default:
        break;
    }

    //patch value to type order
    this.orderTypeOptions = orders;
    if (orders.some((op) => op.value === currentType)) return;
    if (orders.length) {
      this.orderType?.patchValue(orders[0].value);
      this.updateFormOnTypeChange(orders[0].value);
    } else {
      this.orderType?.patchValue(null);
      this.placeOrderForm.patchValue({ price: null });
      this.orderType?.setValidators([outOfTimeOrderValidator]);
      this.orderType?.updateValueAndValidity();
    }

    // const key = stockExchange === EStockExchange.HOSE ?
  }

  private padTime(value: number): string {
    return value < 10 ? `0${value}` : `${value}`;
  }

  /**
   * check có subAccount trong danh sách không
   */
  isGotSubAccountInView() {
    return this.infoCustomer.some((customer) => customer?.subAccount);
  }

  /**
   * Lấy list brokerCode con của brokerCode và brokerCode đó
   * @param {IAllLevelOfBroker[]} allLevelOfBrokerList
   * @param {string} currentBrokerCodeOrder
   * @returns {string[]}
   */
  getAllChildBrokerCodesPlaceOrder(allLevelOfBrokerList: IAllLevelOfBroker[], currentBrokerCodeOrder: string): string[] {
    const currentBrokerOrder = allLevelOfBrokerList.find((b) => b.brokerCode === currentBrokerCodeOrder);
    const result: string[] = [];

    const collectChildBrokerCodes = (brokerItem: IAllLevelOfBroker) => {
      for (const child of brokerItem.children) {
        result.push(child.brokerCode);
        collectChildBrokerCodes(child);
      }
    };

    if (currentBrokerOrder) {
      result.push(currentBrokerOrder.brokerCode);
      collectChildBrokerCodes(currentBrokerOrder);
    }

    return result;
  }

  keydownEvent(){
    console.log('keydown')
  }
}
