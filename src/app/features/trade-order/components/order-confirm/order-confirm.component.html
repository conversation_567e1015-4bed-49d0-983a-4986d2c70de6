<div class="dialog-wrap" *ngIf="dataShowInPopup">
  <div class="dialog-header">
    <div class="title-cls typo-body-2">{{ dataShowInPopup.title | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="dialog-body">
    <div class="section-wrap">
      <div class="section grid">
        <!-- loại lệnh -->
        <div class="grid-item content flex-col">
          <div class="sub-title">{{ 'MES-173' | translate }}</div>

          <div class="title order-category">
            <ng-container
              *ngIf="
                dataShowInPopup.category === EOrderCategory.BUY || dataShowInPopup.category === EOrderCategory.SELL
              "
            >
              <span class="badge" [ngClass]="badgeClass">
                {{ badgeLabel | translate }}
                <span *ngIf="dataShowInPopup.advanced">{{ 'MES-244' | translate }}</span>
              </span>
              -
              <span>{{ dataShowInPopup.type }}</span>
            </ng-container>
          </div>
        </div>

        <!-- mã CK -->
        <div class="grid-item content flex-col">
          <div class="sub-title">{{ 'MES-174' | translate }}</div>
          <div class="title">{{ dataShowInPopup.stockCompany }}</div>
        </div>

        <!-- ngày kích hoạt & điều kiện kích hoạt -->
        <ng-container *ngIf="dataShowInPopup.derivativeCondition">
          <div class="grid-item content flex-col">
            <div class="sub-title">{{ 'MES-198' | translate }}</div>
            <div class="title">
              {{ dataShowInPopup.derivativeDate | date : 'dd/MM/YYYY' }}
            </div>
          </div>

          <div class="grid-item content flex-col">
            <div class="sub-title">{{ 'MES-259' | translate }}</div>
            <div class="title">
              {{ CONVERT_TRADING_CONDITIONS_TO_LABEL[dataShowInPopup.derivativeCondition] | translate }}
            </div>
          </div>
        </ng-container>

        <!-- giá đặt -->
        <div class="grid-item content flex-col">
          <div class="sub-title">{{ 'MES-176' | translate }}</div>

          @if(dataShowInPopup.price > 0) {
          <div class="title">{{ dataShowInPopup.price | numberFormat : 'decimal' : 'en-US' : 2 }}</div>
          } @else {
          <div class="title">{{ dataShowInPopup.price }}</div>
          }
        </div>

        <!-- giá kích hoạt -->
        <div class="grid-item content flex-col" *ngIf="dataShowInPopup.activate">
          <div class="sub-title">{{ 'MES-175' | translate }}</div>
          <div class="title">{{ dataShowInPopup.activate | numberFormat }} CP</div>
        </div>

        <!-- KL đặt -->
        <div
          class="grid-item content flex-col"
          [ngStyle]="{ 'grid-column': dataShowInPopup.activate ? '1/-1' : 'unset' }"
        >
          <div class="sub-title">{{ 'MES-179' | translate }}</div>
          <div class="title">{{ dataShowInPopup.volume | numberFormat }} CP</div>
        </div>

        <ng-container *ngFor="let account of dataShowInPopup.subAccounts">
          <div class="grid-item content flex-col">
            <div class="sub-title">{{ 'MES-66' | translate }}</div>
            <div class="title">{{ account.accountNumber }} - {{ account.subAccount }}</div>
          </div>

          <div class="grid-item content flex-col">
            <div class="sub-title">{{ 'MES-67' | translate }}</div>
            <div class="title">{{ account.customerName }}</div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>

  <div class="dialog-footer justify-center">
    <button [disabled]="isSubmitted" class="btn primary typo-button-3" (click)="confirmPlaceOrder()">
      {{ 'MES-89' | translate }}
    </button>

    <button mat-dialog-close class="btn outline typo-button-3">{{ 'MES-74' | translate }}</button>
  </div>
</div>
