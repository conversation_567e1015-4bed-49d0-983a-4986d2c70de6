.section-wrap {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .section {
    border-radius: 8px;
    padding: 16px;
    background: var(--color--background--1);

    &.grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      /* Two columns of equal width */
      grid-template-rows: 1fr 1fr;
      /* Two rows of equal height */
      gap: 24px;
    }


    .content {
      display: flex;
      justify-content: space-between;

      &.flex-col {
        flex-direction: column;
      }

      .title {
        font-size: 14px;
        font-weight: 500;

        &.order-category {
          display: flex;
          align-items: center;
          gap: 5px;

          .badge {
            text-transform: uppercase;
            border-radius: 16px;
            padding: 2px 8px;
            font-size: 12px;
            color: white;

            &.sell {
              background: var(--color--accents--red);
            }

            &.buy {
              background: var(--color--accents--green);
            }
          }
        }
      }

      .sub-title {
        font-size: 14px;
        color: var(--color--text--subdued);
        margin-bottom: 10px;
      }
    }
  }
}

.dialog-footer {
  .btn {
    width: 160px;
  }
}