import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { of, throwError } from 'rxjs';
import { OrderConfirmComponent } from './order-confirm.component';
import { TradesOrderService } from '../../services/trades-order.service';
import { LoadingService, MessageService } from 'src/app/core/services';
import { EOrderCategory, IOrderPayload, IEditOrderPayload, IPlaceOrderResponse } from '../../models/trade-order';
import { ApiResponse } from 'src/app/core/models/api-response';

describe('OrderConfirmComponent', () => {
  let component: OrderConfirmComponent;
  let fixture: ComponentFixture<OrderConfirmComponent>;
  let mockTradesOrderService: jasmine.SpyObj<TradesOrderService>;
  let mockMessageService: jasmine.SpyObj<MessageService>;
  let mockLoadingService: jasmine.SpyObj<LoadingService>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<OrderConfirmComponent>>;

  const mockDataShowInPopup = {
    title: 'Order Confirmation',
    isDerivatives: false,
    type: 'LO',
    category: EOrderCategory.BUY,
    price: 25000,
    volume: 100,
    advanced: false,
    stockCompany: 'VIC',
    subAccounts: [
      {
        accountNumber: 'ACC001',
        customerName: 'Customer 1',
        idNo: 'ID001',
        subAccount: 'SUB001'
      }
    ]
  };

  const mockOrderConfirmData: IOrderPayload = {
    hts_user_id: 'user123',
    hts_user_nm: 'User Name',
    cli_mac_addr: 'mac123',
    mkt_type: 'HOSE',
    account_list: [
      {
        acnt_no: 'ACC001',
        sub_no: 'SUB001',
        idno: 'ID001'
      }
    ],
    stk_cd: 'VIC',
    stk_ord_tp: '01',
    ord_qty: '100',
    ord_pri: '25000',
    bank_cd: '9999',
    lang_code: 'V',
    ord_mdm_tp: '01'
  };

  const mockDialogData = {
    dataShowInPopup: mockDataShowInPopup,
    orderConfirmData: mockOrderConfirmData,
    action: 'create'
  };

  beforeEach(async () => {
    const tradesOrderServiceSpy = jasmine.createSpyObj('TradesOrderService', [
      'placeBuyOrder',
      'placeBuyAdvanceOrder',
      'placeSellOrder',
      'placeSellAdvanceOrder',
      'editPlaceOrder'
    ]);
    const messageServiceSpy = jasmine.createSpyObj('MessageService', ['success', 'error']);
    const loadingServiceSpy = jasmine.createSpyObj('LoadingService', ['show', 'hide']);
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      declarations: [OrderConfirmComponent],
      providers: [
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        { provide: TradesOrderService, useValue: tradesOrderServiceSpy },
        { provide: MessageService, useValue: messageServiceSpy },
        { provide: LoadingService, useValue: loadingServiceSpy },
        { provide: MatDialogRef, useValue: dialogRefSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(OrderConfirmComponent);
    component = fixture.componentInstance;
    mockTradesOrderService = TestBed.inject(TradesOrderService) as jasmine.SpyObj<TradesOrderService>;
    mockMessageService = TestBed.inject(MessageService) as jasmine.SpyObj<MessageService>;
    mockLoadingService = TestBed.inject(LoadingService) as jasmine.SpyObj<LoadingService>;
    mockDialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<OrderConfirmComponent>>;
  });

  // Component Initialization
  it('No.1: should create component successfully', () => {
    expect(component).toBeTruthy();
  });

  it('No.2: should initialize data from dialog injection', () => {
    expect(component.dataShowInPopup).toEqual(mockDataShowInPopup);
    expect(component.orderConfirmData).toEqual(mockOrderConfirmData);
    expect(component.data).toEqual(mockDialogData);
  });

  it('No.3: should call updateBadgeProperties on initialization', () => {
    // Test that updateBadgeProperties was called by checking the results
    expect(component.badgeClass).toBeDefined();
    expect(component.badgeLabel).toBeDefined();

    // Verify the badge properties are set correctly based on actual component data
    const expectedClass = component.dataShowInPopup.category === EOrderCategory.BUY ? 'buy' : 'sell';
    const expectedLabel = component.dataShowInPopup.isDerivatives
      ? (component.dataShowInPopup.category === EOrderCategory.BUY ? 'LONG' : 'SHORT')
      : (component.dataShowInPopup.category === EOrderCategory.BUY ? 'MES-186' : 'MES-185');

    expect(component.badgeClass).toBe(expectedClass);
    expect(component.badgeLabel).toBe(expectedLabel);
  });

  // Badge Properties Logic
  it('No.4: should set correct badge class for BUY category', () => {
    component.dataShowInPopup.category = EOrderCategory.BUY;
    (component as any).updateBadgeProperties();

    expect(component.badgeClass).toBe('buy');
  });

  it('No.5: should set correct badge class for SELL category', () => {
    component.dataShowInPopup.category = EOrderCategory.SELL;
    (component as any).updateBadgeProperties();

    expect(component.badgeClass).toBe('sell');
  });

  it('No.6: should get correct badge label for derivatives BUY', () => {
    component.dataShowInPopup.isDerivatives = true;
    component.dataShowInPopup.category = EOrderCategory.BUY;

    const result = (component as any).getBadgeLabel();

    expect(result).toBe('LONG');
  });

  it('No.7: should get correct badge label for derivatives SELL', () => {
    component.dataShowInPopup.isDerivatives = true;
    component.dataShowInPopup.category = EOrderCategory.SELL;

    const result = (component as any).getBadgeLabel();

    expect(result).toBe('SHORT');
  });

  it('No.8: should get correct badge label for non-derivatives BUY', () => {
    component.dataShowInPopup.isDerivatives = false;
    component.dataShowInPopup.category = EOrderCategory.BUY;

    const result = (component as any).getBadgeLabel();

    expect(result).toBe('MES-186');
  });

  it('No.9: should get correct badge label for non-derivatives SELL', () => {
    component.dataShowInPopup.isDerivatives = false;
    component.dataShowInPopup.category = EOrderCategory.SELL;

    const result = (component as any).getBadgeLabel();

    expect(result).toBe('MES-185');
  });

  // Order Confirmation Logic
  it('No.10: should call createOrder when action is create', () => {
    spyOn(component, 'createOrder');
    component.data.action = 'create';

    component.confirmPlaceOrder();

    expect(component.createOrder).toHaveBeenCalled();
  });

  it('No.11: should call onEditPlaceOrder when action is edit', () => {
    spyOn(component, 'onEditPlaceOrder');
    component.data.action = 'edit';

    component.confirmPlaceOrder();

    expect(component.onEditPlaceOrder).toHaveBeenCalled();
  });

  // Order Creation Logic
  it('No.12: should handle buy advance order creation', () => {
    spyOn(component, 'onPlaceBuyAdvanceOrder');
    component.dataShowInPopup.isDerivatives = false;
    component.dataShowInPopup.advanced = true;
    component.dataShowInPopup.category = EOrderCategory.BUY;
    component.isSubmitted = false;

    component.createOrder();

    expect(component.onPlaceBuyAdvanceOrder).toHaveBeenCalled();
    expect(component.isSubmitted).toBe(true);
  });

  it('No.13: should handle regular buy order creation', () => {
    spyOn(component, 'onPlaceBuyOrder');
    component.dataShowInPopup.isDerivatives = false;
    component.dataShowInPopup.advanced = false;
    component.dataShowInPopup.category = EOrderCategory.BUY;
    component.isSubmitted = false;

    component.createOrder();

    expect(component.onPlaceBuyOrder).toHaveBeenCalled();
    expect(component.isSubmitted).toBe(true);
  });

  it('No.14: should handle sell advance order creation', () => {
    spyOn(component, 'onPlaceSellAdvanceOrder');
    component.dataShowInPopup.isDerivatives = false;
    component.dataShowInPopup.advanced = true;
    component.dataShowInPopup.category = EOrderCategory.SELL;
    component.isSubmitted = false;

    component.createOrder();

    expect(component.onPlaceSellAdvanceOrder).toHaveBeenCalled();
    expect(component.isSubmitted).toBe(true);
  });

  // Additional Order Creation Logic
  it('No.15: should handle regular sell order creation', () => {
    spyOn(component, 'onPlaceSellOrder');
    component.dataShowInPopup.isDerivatives = false;
    component.dataShowInPopup.advanced = false;
    component.dataShowInPopup.category = EOrderCategory.SELL;
    component.isSubmitted = false;

    component.createOrder();

    expect(component.onPlaceSellOrder).toHaveBeenCalled();
    expect(component.isSubmitted).toBe(true);
  });

  it('No.16: should not create order if already submitted', () => {
    spyOn(component, 'onPlaceBuyOrder');
    component.isSubmitted = true;

    component.createOrder();

    expect(component.onPlaceBuyOrder).not.toHaveBeenCalled();
  });

  it('No.17: should handle derivatives order creation (no action)', () => {
    spyOn(component, 'onPlaceBuyOrder');
    spyOn(component, 'onPlaceSellOrder');
    component.dataShowInPopup.isDerivatives = true;
    component.isSubmitted = false;

    component.createOrder();

    expect(component.onPlaceBuyOrder).not.toHaveBeenCalled();
    expect(component.onPlaceSellOrder).not.toHaveBeenCalled();
    expect(component.isSubmitted).toBe(true);
  });

  // API Call Tests - Buy Order
  it('No.18: should handle successful buy order API call', () => {
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '0',
      error_desc: '[SUCCESS] Order placed successfully',
      success: true,
      count: 1,
      total_record: '1',
      data_list: []
    }];

    mockTradesOrderService.placeBuyOrder.and.returnValue(of(mockResponse));

    component.onPlaceBuyOrder();

    expect(mockTradesOrderService.placeBuyOrder).toHaveBeenCalledWith(mockOrderConfirmData);
    expect(mockMessageService.success).toHaveBeenCalledWith(' Order placed successfully');
    expect(mockDialogRef.close).toHaveBeenCalledWith('save');
  });

  it('No.19: should handle failed buy order API call', () => {
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '1',
      error_desc: '[ERROR] Insufficient funds',
      success: false,
      count: 0,
      total_record: '0',
      data_list: []
    }];

    mockTradesOrderService.placeBuyOrder.and.returnValue(of(mockResponse));

    component.onPlaceBuyOrder();

    expect(mockTradesOrderService.placeBuyOrder).toHaveBeenCalledWith(mockOrderConfirmData);
    expect(mockMessageService.error).toHaveBeenCalledWith(' Insufficient funds');
    expect(mockDialogRef.close).toHaveBeenCalledWith('save');
  });

  it('No.20: should handle buy order API error', () => {
    const mockError = { message: '[ERROR] Network error occurred' };

    mockTradesOrderService.placeBuyOrder.and.returnValue(throwError(() => mockError));

    component.onPlaceBuyOrder();

    expect(mockTradesOrderService.placeBuyOrder).toHaveBeenCalledWith(mockOrderConfirmData);
    expect(mockMessageService.error).toHaveBeenCalledWith(' Network error occurred');
    expect(mockDialogRef.close).toHaveBeenCalledWith();
  });

  // API Call Tests - Buy Advance Order
  it('No.21: should handle successful buy advance order API call', () => {
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '0',
      error_desc: '[SUCCESS] Advance order placed successfully',
      success: true,
      count: 1,
      total_record: '1',
      data_list: []
    }];

    mockTradesOrderService.placeBuyAdvanceOrder.and.returnValue(of(mockResponse));

    component.onPlaceBuyAdvanceOrder();

    expect(mockTradesOrderService.placeBuyAdvanceOrder).toHaveBeenCalledWith(mockOrderConfirmData);
    expect(mockMessageService.success).toHaveBeenCalledWith(' Advance order placed successfully');
    expect(mockDialogRef.close).toHaveBeenCalledWith('save');
  });

  it('No.22: should handle buy advance order API error', () => {
    const mockError = { message: '[ERROR] Advance order failed' };

    mockTradesOrderService.placeBuyAdvanceOrder.and.returnValue(throwError(() => mockError));

    component.onPlaceBuyAdvanceOrder();

    expect(mockMessageService.error).toHaveBeenCalledWith(' Advance order failed');
    expect(mockDialogRef.close).toHaveBeenCalledWith();
  });

  // API Call Tests - Sell Order
  it('No.23: should handle successful sell order API call', () => {
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '0',
      error_desc: '[SUCCESS] Sell order placed successfully',
      success: true,
      count: 1,
      total_record: '1',
      data_list: []
    }];

    mockTradesOrderService.placeSellOrder.and.returnValue(of(mockResponse));

    component.onPlaceSellOrder();

    expect(mockTradesOrderService.placeSellOrder).toHaveBeenCalledWith(mockOrderConfirmData);
    expect(mockMessageService.success).toHaveBeenCalledWith(' Sell order placed successfully');
    expect(mockDialogRef.close).toHaveBeenCalledWith('save');
    expect(mockLoadingService.hide).toHaveBeenCalled();
  });

  it('No.24: should handle sell order API error', () => {
    const mockError = { message: '[ERROR] Sell order failed' };

    mockTradesOrderService.placeSellOrder.and.returnValue(throwError(() => mockError));

    component.onPlaceSellOrder();

    expect(mockMessageService.error).toHaveBeenCalledWith(' Sell order failed');
    expect(mockDialogRef.close).toHaveBeenCalledWith();
    expect(mockLoadingService.hide).toHaveBeenCalled();
  });

  // API Call Tests - Sell Advance Order
  it('No.25: should handle successful sell advance order API call', () => {
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '0',
      error_desc: '[SUCCESS] Sell advance order placed successfully',
      success: true,
      count: 1,
      total_record: '1',
      data_list: []
    }];

    mockTradesOrderService.placeSellAdvanceOrder.and.returnValue(of(mockResponse));

    component.onPlaceSellAdvanceOrder();

    expect(mockTradesOrderService.placeSellAdvanceOrder).toHaveBeenCalledWith(mockOrderConfirmData);
    expect(mockMessageService.success).toHaveBeenCalledWith(' Sell advance order placed successfully');
    expect(mockDialogRef.close).toHaveBeenCalledWith('save');
    expect(mockLoadingService.hide).toHaveBeenCalled();
  });

  it('No.26: should handle sell advance order API error', () => {
    const mockError = { message: '[ERROR] Sell advance order failed' };

    mockTradesOrderService.placeSellAdvanceOrder.and.returnValue(throwError(() => mockError));

    component.onPlaceSellAdvanceOrder();

    expect(mockMessageService.error).toHaveBeenCalledWith(' Sell advance order failed');
    expect(mockDialogRef.close).toHaveBeenCalledWith();
    expect(mockLoadingService.hide).toHaveBeenCalled();
  });

  // Edit Order Tests
  it('No.27: should handle successful edit order API call', () => {
    const mockEditOrderData: IEditOrderPayload = {
      hts_user_id: 'user123',
      hts_user_nm: 'User Name',
      cli_mac_addr: 'mac123',
      acnt_no: 'ACC001',
      sub_no: 'SUB001',
      idno: 'ID001',
      brch_cd: 'BR001',
      ord_no: 'ORD001',
      ord_qty: '100',
      ord_pri: '25000',
      ord_mdm_tp: '01',
      stk_cd: 'VIC',
      mkt_tp: 'HOSE',
      langCode: 'V',
      stkOrdTp: '01',
      bankCd: '9999'
    };

    const mockResponse: ApiResponse<IPlaceOrderResponse[]> = {
      data: [],
      message: 'Order edited successfully',
      statusCode: 200
    };

    component.orderConfirmData = mockEditOrderData;
    mockTradesOrderService.editPlaceOrder.and.returnValue(of(mockResponse));
    component.isSubmitted = false;

    component.onEditPlaceOrder();

    expect(mockTradesOrderService.editPlaceOrder).toHaveBeenCalledWith(mockEditOrderData);
    expect(mockMessageService.success).toHaveBeenCalledWith('Order edited successfully');
    expect(mockDialogRef.close).toHaveBeenCalledWith('save');
    expect(component.isSubmitted).toBe(true);
  });

  it('No.28: should handle edit order API error', () => {
    const mockError = { message: '[ERROR] Edit order failed' };

    mockTradesOrderService.editPlaceOrder.and.returnValue(throwError(() => mockError));
    component.isSubmitted = false;

    component.onEditPlaceOrder();

    expect(mockMessageService.error).toHaveBeenCalledWith(' Edit order failed');
    expect(mockDialogRef.close).toHaveBeenCalledWith();
    expect(component.isSubmitted).toBe(true);
  });

  it('No.29: should not edit order if already submitted', () => {
    component.isSubmitted = true;

    component.onEditPlaceOrder();

    expect(mockTradesOrderService.editPlaceOrder).not.toHaveBeenCalled();
  });

  // Error Message Parsing Tests
  it('No.30: should handle error messages without brackets', () => {
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '1',
      error_desc: 'Simple error message',
      success: false,
      count: 0,
      total_record: '0',
      data_list: []
    }];

    mockTradesOrderService.placeBuyOrder.and.returnValue(of(mockResponse));

    component.onPlaceBuyOrder();

    expect(mockMessageService.error).toHaveBeenCalledWith('Simple error message');
  });

  it('No.31: should handle API errors without brackets', () => {
    const mockError = { message: 'Simple network error' };

    mockTradesOrderService.placeBuyOrder.and.returnValue(throwError(() => mockError));

    component.onPlaceBuyOrder();

    expect(mockMessageService.error).toHaveBeenCalledWith('Simple network error');
  });

  // Component State Tests
  it('No.32: should have correct initial state', () => {
    expect(component.EOrderCategory).toBe(EOrderCategory);
    expect(component.CONVERT_TRADING_CONDITIONS_TO_LABEL).toBeDefined();
    expect(component.badgeClass).toBeDefined();
    expect(component.badgeLabel).toBeDefined();
    expect(component.isSubmitted).toBe(false);

    // Verify badge properties are set based on actual component data
    const expectedClass = component.dataShowInPopup.category === EOrderCategory.BUY ? 'buy' : 'sell';
    const expectedLabel = component.dataShowInPopup.isDerivatives
      ? (component.dataShowInPopup.category === EOrderCategory.BUY ? 'LONG' : 'SHORT')
      : (component.dataShowInPopup.category === EOrderCategory.BUY ? 'MES-186' : 'MES-185');

    expect(component.badgeClass).toBe(expectedClass);
    expect(component.badgeLabel).toBe(expectedLabel);
  });
});
