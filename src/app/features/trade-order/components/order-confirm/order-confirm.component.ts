import { Component, Inject, Input } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  CONVERT_TRADING_CONDITIONS_TO_LABEL,
  EOrderCategory,
  IEditOrderPayload,
  IOrderPayload,
} from '../../models/trade-order';
import { TradesOrderService } from '../../services/trades-order.service';
import { LoadingService, MessageService } from 'src/app/core/services';
import { finalize, take } from 'rxjs';

interface IDataShowInPopup {
  title: string;
  isDerivatives: boolean;
  type: string;
  category: string;
  price: number;
  volume: number;
  advanced: boolean;
  stockCompany: string;
  subAccounts: ISubAccounts[];
  activate?: any; // fix this later
  derivativeCondition?: any; // fix this later
  derivativeDate?: any; // fix this later
}

interface ISubAccounts {
  accountNumber: string;
  customerName: string;
  idNo: string;
  subAccount: string;
}

/**
 * Declare new component
 */
@Component({
  selector: 'app-order-confirm',
  templateUrl: './order-confirm.component.html',
  styleUrl: './order-confirm.component.scss',
})
export class OrderConfirmComponent {
  @Input() dataShowInPopup!: IDataShowInPopup;
  @Input() orderConfirmData!: any;

  EOrderCategory = EOrderCategory;
  CONVERT_TRADING_CONDITIONS_TO_LABEL = CONVERT_TRADING_CONDITIONS_TO_LABEL;

  badgeClass = '';
  badgeLabel = '';

  isSubmitted = false;

  /**
   * @param data
   */
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      dataShowInPopup: IDataShowInPopup;
      orderConfirmData: IOrderPayload | IEditOrderPayload;
      action: string;
    },
    private readonly tradesOrderService: TradesOrderService,
    private readonly messageService: MessageService,
    private readonly loadingService: LoadingService,
    private readonly dialogRef: MatDialogRef<OrderConfirmComponent>
  ) {
    this.dataShowInPopup = this.data.dataShowInPopup;
    this.orderConfirmData = this.data.orderConfirmData;
    this.updateBadgeProperties();
  }

  /**
   * tìm styleClass của ô Mua/bán
   */
  private updateBadgeProperties() {
    this.badgeClass = this.dataShowInPopup.category === EOrderCategory.BUY ? 'buy' : 'sell';

    this.badgeLabel = this.getBadgeLabel();
  }

  /**
   * tìm label của ô Mua/bán
   */
  private getBadgeLabel(): string {
    if (this.dataShowInPopup.isDerivatives) {
      return this.dataShowInPopup.category === EOrderCategory.BUY ? 'LONG' : 'SHORT';
    } else {
      return this.dataShowInPopup.category === EOrderCategory.BUY ? 'MES-186' : 'MES-185';
    }
  }

  confirmPlaceOrder() {
    if (this.data.action === 'create') {
      this.createOrder();
    }

    if (this.data.action === 'edit') {
      this.onEditPlaceOrder();
    }
  }

  createOrder() {
    if (this.isSubmitted) return;
    this.isSubmitted = true;

    const { isDerivatives, advanced, category } = this.dataShowInPopup;
    switch (isDerivatives) {
      // Lệnh phái sinh
      case true:
        break;

      //  Lệnh cơ sở
      case false:
        // Lệnh mua đặt trước
        if (advanced && category === EOrderCategory.BUY) {
          this.onPlaceBuyAdvanceOrder();
          break;
        }
        // Lệnh mua thường
        if (!advanced && category === EOrderCategory.BUY) {
          this.onPlaceBuyOrder();
          break;
        }
        // Lệnh bán đặt trước
        if (advanced && category === EOrderCategory.SELL) {
          this.onPlaceSellAdvanceOrder();
          break;
        }
        // Lệnh bán thường
        if (!advanced && category === EOrderCategory.SELL) {
          this.onPlaceSellOrder();
          break;
        }
        break;
      default:
        break;
    }
  }

  /**
   * call api đặt lệnh mua thường
   */
  onPlaceBuyOrder() {
    this.tradesOrderService
      .placeBuyOrder(this.orderConfirmData)
      .pipe(take(1))
      .subscribe({
        next: (res) => {
          const messageShowBuy = res[0].error_desc.split(']').pop();
          res[0].success
            ? this.messageService.success(messageShowBuy ?? res[0].error_desc)
            : this.messageService.error(messageShowBuy ?? res[0].error_desc);
          this.dialogRef.close('save');
        },
        error: (err) => {
          const messageShowBuy = err.message.split(']').pop();
          this.messageService.error(messageShowBuy ?? err.message);
          this.dialogRef.close();
        },
      });
  }

  /**
   * call api đặt lệnh mua đặt trước
   */
  onPlaceBuyAdvanceOrder() {
    this.tradesOrderService
      .placeBuyAdvanceOrder(this.orderConfirmData)
      .pipe(take(1))
      .subscribe({
        next: (res) => {
          const messageShowOn = res[0].error_desc.split(']').pop();
          res[0].success
            ? this.messageService.success(messageShowOn ?? res[0].error_desc)
            : this.messageService.error(messageShowOn ?? res[0].error_desc);
          this.dialogRef.close('save');
        },
        error: (err) => {
          const messageShowOn = err.message.split(']').pop();
          this.messageService.error(messageShowOn ?? err.message);
          this.dialogRef.close();
        },
      });
  }

  /**
   * call api đặt lệnh bán thường
   */
  onPlaceSellOrder() {
    this.tradesOrderService
      .placeSellOrder(this.orderConfirmData)
      .pipe(
        take(1),
        finalize(() => this.loadingService.hide())
      )
      .subscribe({
        next: (res) => {
          const messageShowSell = res[0].error_desc.split(']').pop();
          res[0].success
            ? this.messageService.success(messageShowSell ?? res[0].error_desc)
            : this.messageService.error(messageShowSell ?? res[0].error_desc);
          this.dialogRef.close('save');
        },
        error: (err) => {
          const messageShowSell = err.message.split(']').pop();
          this.messageService.error(messageShowSell ?? err.message);
          this.dialogRef.close();
        },
      });
  }

  /**
   * call api đặt lệnh bán đặt trước
   */
  onPlaceSellAdvanceOrder() {
    this.tradesOrderService
      .placeSellAdvanceOrder(this.orderConfirmData)
      .pipe(
        take(1),
        finalize(() => this.loadingService.hide())
      )
      .subscribe({
        next: (res) => {
          const messageShowSellAd = res[0].error_desc.split(']').pop();
          res[0].success
            ? this.messageService.success(messageShowSellAd ?? res[0].error_desc)
            : this.messageService.error(messageShowSellAd ?? res[0].error_desc);
          this.dialogRef.close('save');
        },
        error: (err) => {
          const messageShowSellAd = err.message.split(']').pop();
          this.messageService.error(messageShowSellAd ?? err.message);
          this.dialogRef.close();
        },
      });
  }

  /**
   * call api sửa lệnh
   */
  onEditPlaceOrder() {
    if (this.isSubmitted) return;
    this.isSubmitted = true;

    this.tradesOrderService
      .editPlaceOrder(this.orderConfirmData)
      .pipe(take(1))
      .subscribe({
        next: (res) => {
          this.messageService.success(res.message);
          this.dialogRef.close('save');
        },
        error: (err) => {
          const messageShowEdit = err.message.split(']').pop();
          this.messageService.error(messageShowEdit ?? err.message);
          this.dialogRef.close();
        },
      });
  }
}
