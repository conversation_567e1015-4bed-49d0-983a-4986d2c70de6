import { ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { DestroyService, DialogService, MessageService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { TradesOrderService } from '../../services/trades-order.service';
import { HashService } from 'src/app/shared/services/hash.service';
import { ActivatedRoute, Router } from '@angular/router';
import { SharedService } from 'src/app/shared/services/shared.service';
import {
  CONVERT_STK_ORD_TYPE,
  EOrderCategory,
  EOrderType,
  EStockExchange,
  IEditOrderPayload,
  IInvestmentBySubAccountList,
  IOptionStock,
  IOrderCategoryInfo,
  IOrderListResponse,
  IOrderPayload,
  ISubAccountListOption,
} from '../../models/trade-order';
import {
  controlCannotBeZero,
  maxOrderVolumeValidator,
  outOfTimeOrderValidator,
  priceOrderValidator,
  timeOrderValidator,
} from 'src/app/shared/validators/form';
import { SUB_ACCOUNT_TO_LABEL } from 'src/app/core/models/global.constants';
import { catchError, combineLatest, debounceTime, finalize, of, startWith, Subject, take, takeUntil, tap } from 'rxjs';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectAllStockList$,
  selectCurrentBrokerView$,
} from 'src/app/stores/shared/shared.selectors';
import {
  IAllAccountNumber,
  IAllLevelOfBroker,
  IAllStockList,
  IInfoUserLogined,
  IPayloadPersonalList,
} from 'src/app/shared/models/global';
import { IOption } from 'src/app/shared/models/dropdown-item.model';
import { IStockSocketResponse } from 'src/app/shared/models/stock.model';
import { ALL_ORDERS_GROUP, ETagPageOrder, OPENED_ORDERS_SESSIONS } from '../../constants/trade-order';
import { PAYLOAD_DEFAULT_TRADES_ORDER, PAYLOAD_EDIT_DEFAULT_TRADES_ORDER } from 'src/app/shared/constants/global';
import { CustomerService } from 'src/app/features/customers/services/customer.service';
import { ICustomerDataResponse } from 'src/app/features/customers/model/customer';
import { OrderConfirmComponent } from '../order-confirm/order-confirm.component';
import { getListTodayOrder } from '../../stores/trade-order.actions';
import { removeVietnameseTones } from 'src/app/shared/utils/text';
import { checkIsError } from '../../helpers/helpers';

enum ERouteTradeOrder {
  OPEN = 'open-trades',
  MATCH = 'match-orders',
  RESERVE = 'reserve-trades',
}
@Component({
  selector: 'app-place-order-popup',
  templateUrl: './place-order-popup.component.html',
  styleUrl: './place-order-popup.component.scss',
  providers: [ReactiveFormsModule],
})
export class PlaceOrderPopupComponent {
  placeOrderForm!: FormGroup;

  searchCustomerControl = new FormControl();

  overlayElement = document.querySelector('.overlay-place-order') as HTMLElement;

  EOrderCategory = EOrderCategory;

  SUB_ACCOUNT_TO_LABEL = SUB_ACCOUNT_TO_LABEL;

  initCustomerList: IAllAccountNumber[] = [];

  listFilterCustomerNameOptions: IAllAccountNumber[] = [];

  allStockList: IAllStockList[] = [];

  listSubAccountList: ISubAccountListOption[] = [];

  investmentBySubAccountList: IInvestmentBySubAccountList[] = [];

  selectedStock: string | undefined;

  orderTypeOptions: IOption[] = [];

  currentStockInfo!: IStockSocketResponse;

  ceilingPrice!: number;

  macAddress!: string;

  orderCategoryInfo!: IOrderCategoryInfo;

  EOrderType = EOrderType;

  orderCategories = [
    {
      label: 'MES-186',
      value: EOrderCategory.BUY,
      active: true,
    },
    {
      label: 'MES-185',
      value: EOrderCategory.SELL,
      active: false,
    },
  ];

  noInputOrderTypes = [
    EOrderType.MP,
    EOrderType.PLO,
    EOrderType.ATO,
    EOrderType.ATC,
    EOrderType.MTL,
    EOrderType.FOK,
    EOrderType.FAK,
  ];

  isSearchingCustomer = false;

  isFindingOldInvestment = false;

  isSelectFromData = false;

  currentBroker: IInfoUserLogined | null = null;

  currentCustomerSelected: ICustomerDataResponse | null = null;

  currentSubAccount = '';

  isFormChange = false;

  previousCurrentData = '';

  private readonly destroyBuyableApi$ = new Subject<void>();

  /**
   * Khách hàng
   * get "customer" control
   */
  get customer() {
    return this.placeOrderForm.get('customer') as FormControl;
  }

  /**
   * Loại lệnh
   * get "orderType" control
   */
  get orderType() {
    return this.placeOrderForm.get('orderType') as FormControl;
  }
  /**
   * Giá đặt
   * get "price" control
   */
  get orderPrice() {
    return this.placeOrderForm.get('price') as FormControl;
  }

  /**
   * Khối lượng đặt
   * get "volume" control
   */
  get orderVolume() {
    return this.placeOrderForm.get('volume') as FormControl;
  }

  /**
   * Trạng thái Mua / Bán
   * get "orderStatus" control
   */
  get orderStatus() {
    return this.placeOrderForm.get('orderStatus') as FormControl;
  }

  /**
   * Trạng thái đặt trước
   * get "advanced" control
   */
  get orderAdvanced() {
    return this.placeOrderForm.get('advanced') as FormControl;
  }

  /**
   * Mã chứng khoán
   * get "stock" control
   */
  get orderStock() {
    return this.placeOrderForm.get('stock') as FormControl;
  }

  /**
   * Sàn chứng khoán
   * get "stockExchange" control
   */
  get stockExchange() {
    return this.placeOrderForm.get('stockExchange') as FormControl;
  }

  isDisabledVolume = false;
  isDisabledPrice = false;

  stepPrice = 0.01;

  constructor(
    private readonly fb: FormBuilder,
    private readonly dialogService: DialogService,
    private readonly popoverService: PopoverService,
    private readonly dialogRef: MatDialogRef<PlaceOrderPopupComponent>,
    private readonly _destroy: DestroyService,
    @Inject(MAT_DIALOG_DATA)
    public data: {
      title: string;
      initialStock: IOptionStock;
      element?: IOrderListResponse; // Khi Edit
      isDontResetPage?: boolean;
      infoCustomer?: any;
    },
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly store: Store,
    private readonly tradesOrderService: TradesOrderService,
    private readonly hashService: HashService,
    private readonly cdr: ChangeDetectorRef,
    private readonly route: Router,
    private readonly shareService: SharedService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly messageService: MessageService,
    private readonly customerService: CustomerService
  ) {
    this.placeOrderForm = this.initForm();
  }

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((customerList) => {
        this.initCustomerList = [...customerList];
      });

    this.store
      .select(selectAllStockList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((stocks) => {
        this.allStockList = [...stocks];
      });

    this.listenPlaceOrderFormChange();
    this.fillForm();

    this.preventChangePriceAndVolume();

    this.orderPrice.valueChanges.pipe(takeUntil(this._destroy)).subscribe((value) => {
      this.changeStepPrice(+(value ?? 0));
    });
  }

  private loadCustomer(accountNumber: string) {
    const brokerCode = this.activatedRoute.snapshot.queryParams['brokerId'];
    const payload: IPayloadPersonalList = {
      accountNumbers: [accountNumber],
      userType: '',
      brokerCode: [brokerCode],
      toBirthYear: '',
      fromBirthYear: '',
      searchKey: '',
    };
    this.customerService
      .getPersonalInfoList(payload)
      .pipe(take(1))
      .subscribe((customer) => {
        this.currentCustomerSelected = { ...customer[0] };
        // if(this.data.infoCustomer){
        // this.searchCustomerControl.patchValue(customer[0], { emitEvent: false });
        // this.searchCustomerControl.disable({ emitEvent: false });

        // }
      });
  }

  /**
   * init FormGroup
   */
  private initForm(): FormGroup {
    const formConfig = {
      customer: [null, [Validators.required]], // Khách hàng
      price: [null, [controlCannotBeZero]], // Giá đặt
      orderType: [null, [Validators.required]], // Loại lệnh
      volume: [null, [Validators.required, maxOrderVolumeValidator(), controlCannotBeZero]], // Khối lượng đặt
      orderStatus: [EOrderCategory.BUY, [Validators.required]], // Mua / Bán
      advanced: [false], // Đặt trước
      stock: [null, [Validators.required]], // Mã chứng khoán
      stockExchange: [null], // Sàn chứng khoán
    };

    return this.fb.group(formConfig, { validator: this.validValueOrderVolumne }); //NOSONAR
  }

  /**
   * Validate khối lượng đặt lệnh
   * @param {FormGroup} group
   */
  validValueOrderVolumne(group: FormGroup) {
    const volumeControl = group.controls['volume'];
    const advancedControl = group.controls['advanced'];

    const volume = volumeControl.value;
    const currentErrors = volumeControl.errors || {};
    const { isNotMultipleOf100, isNotMultipleOf100AndLarger100, ...remainingErrors } = currentErrors;

    if (!volume || volume === '') {
      volumeControl.setErrors(checkIsError(remainingErrors));
      return;
    }
    if (advancedControl.value) {
      if (+volume % 100 !== 0) {
        volumeControl.setErrors({ ...currentErrors, isNotMultipleOf100: true });
      } else {
        const { isNotMultipleOf100, ...remainingErrors } = currentErrors;
        volumeControl.setErrors(checkIsError(remainingErrors));
      }
    } else if (+volume % 100 !== 0 && +volume > 100) {
      volumeControl.setErrors({ ...currentErrors, isNotMultipleOf100AndLarger100: true });
    } else {
      const { isNotMultipleOf100AndLarger100, ...remainingErrors } = currentErrors;
      volumeControl.setErrors(checkIsError(remainingErrors));
    }
  }

  fillForm() {
    if (this.data.initialStock) {
      this.placeOrderForm.patchValue({
        stock: this.data.initialStock?.id,
        stockExchange: this.data.initialStock?.stoke,
      });
      this.selectedStock = this.data.initialStock?.id;
    }
    const { element } = this.data;
    if (this.data && element) {
      const { accountNumber, subAccount, status, typeTrades, orderVolume, customerName, orderPrice } = element;

      const search = accountNumber.split('-')[0].trim();

      this.searchCustomerControl.patchValue(search);

      const infoCustomer = {
        accountNumber: search,
        customerName,
        subAccount,
      } as any;

      this.getCustomerSelected(infoCustomer);
      this.customer.patchValue(infoCustomer, { emitEvent: false });
      this.loadCustomer(search);

      this.currentSubAccount = subAccount;

      const statusMap: { [key: string]: number } = {
        [EOrderCategory.BUY]: 2,
        [EOrderCategory.SELL]: 1,
      };
      this.orderCategories = this.orderCategories.map((d) => ({
        ...d,
        active: status === statusMap[d.value],
      }));

      this.orderTypeOptions = [
        {
          label: typeTrades,
          value: typeTrades,
        },
      ];

      this.orderStatus.patchValue(status === 1 ? EOrderCategory.SELL : EOrderCategory.BUY);
      this.orderType.patchValue(typeTrades);
      this.orderPrice.patchValue(orderPrice);
      this.orderVolume?.patchValue(orderVolume);

      this.orderStock?.disable({ emitEvent: false });
      this.searchCustomerControl.patchValue(infoCustomer, { emitEvent: false });
      this.searchCustomerControl.disable({ emitEvent: false });
    }

    if (this.data.infoCustomer) {
      const brokerCode = this.activatedRoute.snapshot.queryParams['brokerId'];
      const payload: IPayloadPersonalList = {
        accountNumbers: [this.data.infoCustomer.accountNumber],
        userType: '',
        brokerCode: [brokerCode],
        toBirthYear: '',
        fromBirthYear: '',
        searchKey: '',
      };
      this.customerService
        .getPersonalInfoList(payload)
        .pipe(take(1))
        .subscribe((customer) => {
          this.currentCustomerSelected = { ...customer[0] };
          this.currentSubAccount = this.data.infoCustomer?.subAccount ?? '';
          this.getCustomerSelected(customer[0]);
          this.searchCustomerControl.patchValue(customer[0], { emitEvent: false });
          this.searchCustomerControl.disable({ emitEvent: false });
        });
    }

    this.setValidatorTimeToOrder();
  }

  /**
   * set validator thời gian đặt lệnh khi chọn loại lệnh
   */
  setValidatorTimeToOrder() {
    this.orderType?.setValidators([timeOrderValidator(this.stockExchange?.value), Validators.required]);
    this.orderType?.updateValueAndValidity();
    this.orderType?.markAsTouched();
    this.orderType?.markAsDirty();
  }

  getCustomerSelected(value: IAllAccountNumber) {
    this.isSearchingCustomer = true;

    if (this.previousCurrentData && this.previousCurrentData === value.accountNumber) {
      setTimeout(() => {
        this.isSearchingCustomer = false;
      }, 300);

      return;
    }
    this.previousCurrentData = value.accountNumber;

    this.shareService
      .getAccountNumberAndSubAccount([value.accountNumber])
      .pipe(
        take(1),
        catchError((error) => {
          this.messageService.error(error?.message);
          return [];
        }),
        finalize(() => (this.isSearchingCustomer = false))
      )
      .subscribe((subAccountInfo) => {
        let index = 0;
        this.listSubAccountList = subAccountInfo[0].subAccounts.map((subAccList, i) => {
          const isSelect = this.currentSubAccount ? this.currentSubAccount === subAccList.subNo : false;
          if (isSelect) index = i;
          return {
            customerName: value.customerName,
            accountNumber: value.accountNumber,
            subAccount: subAccList.subNo,
            brokerCode: value.brokerCode,
            isSelect,
          };
        });
        this.selectCustomer(this.listSubAccountList[index]);
      });
  }

  /**
   * listenPlaceOrderFormChange
   */
  listenPlaceOrderFormChange() {
    // search accountNumber
    this.searchCustomerControl.valueChanges
      .pipe(
        startWith(''),
        tap((value) => {
          this.listSubAccountList = [];
          this.previousCurrentData = '';
          // Typing search
          if (typeof value === 'string')
            this.listFilterCustomerNameOptions = this._filter(value || '', this.initCustomerList);

          // Select customer
          if (typeof value === 'object')
            this.listFilterCustomerNameOptions = this._filter(value.accountNumber ?? '', this.initCustomerList);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    combineLatest([
      this.orderStatus.valueChanges.pipe(startWith(this.orderStatus.value)),
      this.customer.valueChanges.pipe(startWith(this.customer.value)),
      this.stockExchange.valueChanges.pipe(startWith(this.stockExchange), debounceTime(300)),
      this.orderPrice.valueChanges.pipe(startWith(this.orderPrice.value), debounceTime(300)),
    ])
      .pipe(takeUntil(this._destroy))
      .subscribe(([orderStatus, customer, stockExchange, price]) => {
        if (customer && orderStatus === EOrderCategory.BUY && stockExchange && price) {
          const { accountNumber, subAccount } = customer;

          const mkt_trd_tp = stockExchange;
          const stk_cd = this.selectedStock ?? '';
          const ord_pri = price;
          const orderType = this.orderType.value;
          const payload = {
            acnt_no: accountNumber,
            sub_no: subAccount,
            bank_cd: '9999',
            stk_cd,
            mkt_trd_tp,
            ord_pri: this.orderPriceByStockExchange(mkt_trd_tp, orderType, ord_pri),
          };

          this.destroyBuyableApi$.next();

          this.tradesOrderService
            .getBuyableInfo(payload)
            .pipe(
              takeUntil(this.destroyBuyableApi$),
              catchError((error) => {
                this.messageService.error(error?.message);
                return of([]);
              })
            )
            .subscribe((res) => {
              if (res.length)
                this.orderCategoryInfo = {
                  number: +res[0].buying_power || 0,
                  volume: +res[0].buy_abl_qty || 0,
                };

              this.validateOrderVolume(orderStatus, this.orderVolume.value, this.orderCategoryInfo.volume);
            });
        }
      });

    // Thay đổi trạng thái MUA / BÁN hoặc tài khoản thay đổi hoặc stock
    combineLatest([
      this.orderStatus.valueChanges.pipe(startWith(this.orderStatus.value)),
      this.customer.valueChanges.pipe(startWith(this.customer.value)),
      this.stockExchange.valueChanges.pipe(startWith(this.stockExchange)),
    ])
      .pipe(takeUntil(this._destroy))
      .subscribe(([orderStatus, customerList, stock]) => {
        // Update overlay width based on orderStatus and customer
        if (this.overlayElement)
          this.overlayElement.style.width = orderStatus === EOrderCategory.SELL && customerList ? '1600px' : '1200px';

        // Fetch investment portfolio if conditions match
        if (customerList && orderStatus === EOrderCategory.SELL) {
          const { accountNumber, subAccount } = customerList;

          const payload = { accountNumber, subAccount };

          this.tradesOrderService
            .getInvestmentPortfolioBySubAccount(payload)
            .pipe(
              take(1),
              catchError((error) => {
                this.messageService.error(error?.message);
                return of([]);
              }),
              finalize(() => {})
            )
            .subscribe((res) => {
              this.investmentBySubAccountList = res.map((r) => {
                const stock = this.findItemByProperty(r.stkCd, this.allStockList, 'id');
                return {
                  symbol: r.stkCd, // Mã chứng khoán
                  exchange: stock?.stock ?? '', // Tên sàn
                  tradableVolume: r.sellAbleQty, // Khối lượng có thể giao dịch
                  totalVolume: r.ownQtyPl, // Tổng khối lượng
                  price: +r.costPri / 1000, // Giá vốn
                };
              });
            });
        }
      });

    this.orderAdvanced.valueChanges.pipe(takeUntil(this._destroy)).subscribe((advanced) => {
      this.updateFormOnAdvancedChange(advanced);
      // Trường hợp không chọn mã CK
      if (!this.orderStock?.value) {
        this.orderTypeOptions = ALL_ORDERS_GROUP;
      } else {
        // Trường hợp đã chọn mã CK
        this.updateOrderTypeOptions();
      }

      // Nếu đặt trước sẽ không check validate của giá đặt
      if (advanced) {
        this.orderPrice?.setValidators([Validators.required]);
        this.orderPrice?.updateValueAndValidity();
      } else {
        this.checkRequiredOrderPrice(this.orderTypeOptions ?? []);
        this.orderPrice?.markAsTouched();
        this.orderPrice?.markAsDirty();
      }

      this.orderVolume.updateValueAndValidity();

      this.validateOrderVolume(this.orderStatus.value, this.orderVolume.value, this.orderCategoryInfo?.volume);
    });

    this.orderStock?.valueChanges.pipe(takeUntil(this._destroy)).subscribe((stock) => {
      if (!stock?.value) {
        this.orderType?.patchValue(null);
        this.orderPrice?.patchValue(null);
        this.stockExchange?.patchValue(null);
        this.orderTypeOptions = ALL_ORDERS_GROUP;
        this.orderPrice.setValidators(Validators.required);
      }

      this.orderType?.setValidators(Validators.required);
    });

    this.orderType?.valueChanges.pipe(takeUntil(this._destroy)).subscribe((type) => {
      if (type === EOrderType.LO) {
        if (this.data.element) {
          this.orderPrice.setValue(this.data.element.orderPrice);
        } else this.orderPrice?.setValue(this.currentStockInfo?.lastValue);

        this.checkRequiredOrderPrice(this.orderTypeOptions ?? []);
        this.orderPrice?.updateValueAndValidity();
      }
    });

    combineLatest([
      this.orderStatus.valueChanges.pipe(startWith(this.orderStatus.value)),
      this.orderPrice.valueChanges.pipe(startWith(this.orderPrice.value)),
      this.orderVolume.valueChanges.pipe(startWith(this.orderVolume.value)),
    ])
      .pipe(takeUntil(this._destroy))
      .subscribe(([status, price, volume]) => {
        this.trackFormChange();

        if (!volume) return;
        this.validateOrderVolume(status, volume, this.orderCategoryInfo?.volume ?? 0);
      });
  }

  /**
   * validate khối lượng đặt lệnh
   * @param {string} orderStatus
   * @param {string} volume
   * @param {number} maxVolume
   */
  validateOrderVolume(orderStatus: string, volume: string, maxVolume: number) {
    if (!volume) return;
    const currentVolume = +volume;
    const errorKey = orderStatus === EOrderCategory.BUY ? 'orderVolumeBuyMinValid' : 'orderVolumeSellMinValid';
    const currentErrors = this.orderVolume.errors || {};
    const { orderVolumeBuyMinValid, orderVolumeSellMinValid, ...remainingErrors } = currentErrors;
    const shouldHaveError = currentVolume > maxVolume;

    // Nếu lệnh đặt trước sẽ bỏ check khối lượng đặt với khối lượng tối đa
    if (this.orderAdvanced.value) {
      this.orderVolume.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
      return;
    }

    if (shouldHaveError) {
      this.orderVolume.setErrors({ ...remainingErrors, [errorKey]: true });
    } else {
      this.orderVolume.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
    }
  }

  preventChangePriceAndVolume() {
    if (!this.data.element) return;
    const { orderVolume, orderPrice } = this.data.element;
    this.orderVolume.valueChanges.pipe(takeUntil(this._destroy)).subscribe((value) => {
      this.isDisabledPrice = +(value ?? 0) !== orderVolume;
    });

    this.orderPrice.valueChanges.pipe(takeUntil(this._destroy)).subscribe((value) => {
      this.isDisabledVolume = +(value ?? 0) !== orderPrice;
    });
  }

  /**
   * Chọn khách hàng
   * @param {ISubAccountListOption} customerInfo
   */
  selectCustomer(customerInfo: ISubAccountListOption) {
    if (this.data?.element) return;
    const { accountNumber, subAccount } = customerInfo;

    this.listSubAccountList = this.listSubAccountList.map((d) => ({
      ...d,
      isSelect: d.accountNumber === accountNumber && d.subAccount === subAccount,
    }));
    this.customer?.patchValue(customerInfo);
    if (!this.data.infoCustomer) this.loadCustomer(customerInfo.accountNumber);
    this.getPurchasingPowerInfo();
  }

  /**
   * Thay đổi trạng thái MUA/BÁN của lệnh
   * @param {string} categoryValue
   */
  changeOrderCategory(categoryValue: string) {
    if (this.data.element) return;
    this.orderCategories = this.orderCategories.map((d) => ({
      ...d,
      active: d.value === categoryValue,
    }));

    this.orderStatus?.setValue(categoryValue);

    if (this.orderStatus?.value === EOrderCategory.SELL && this.customer?.value) {
      this.overlayElement.style.width = '1600px';
    }

    this.getPurchasingPowerInfo();

    if (this.orderStock?.value) {
      this.selectedStock = this.orderStock?.value;
    }
  }

  /**
   * hiển thị thêm 2 input ngày kích hoạt và điều kiện kích hoạt nếu muốn đặt trước lệnh phái sinh
   * @param status
   */
  private updateFormOnAdvancedChange(status: boolean) {
    if (status) this.orderType?.setValidators([Validators.required]);
    else
      this.orderType?.setValidators([
        Validators.required,
        timeOrderValidator(this.stockExchange?.value), // Update validator with new stockExchange value
      ]);
    this.orderType?.markAsDirty({ onlySelf: false });
    this.orderType?.updateValueAndValidity({ emitEvent: false });
  }

  /**
   * Thay đổi loại lệnh
   */
  onTypeChange(type: string) {
    this.orderType?.setValue(type);
    this.updateFormOnTypeChange(type);
  }

  /**
   * update current stock info from app-stock-info
   * @param {IStockSocketResponse} data
   */
  stockInfo(data: IStockSocketResponse) {
    this.currentStockInfo = data;
    this.ceilingPrice = +this.currentStockInfo.ceilingPrice;
    if (this.data.element) {
      this.orderPrice.setValue(this.data.element.orderPrice);
    }
    if (this.orderType?.value === EOrderType.LO && !this.data?.element) {
      this.orderPrice?.setValue((+data?.lastValue).toFixed(2));
    }
    // Validator price form control
    if (this.orderAdvanced?.value) return;
    this.orderPrice?.setValidators([
      Validators.required,
      priceOrderValidator(this.currentStockInfo.floorPrice, this.currentStockInfo.ceilingPrice),
    ]);
    this.checkRequiredOrderPrice(this.orderTypeOptions ?? []);
    this.orderPrice?.markAsTouched();
    this.orderPrice?.markAsDirty();
  }

  /**
   * Dựa vào sàn CK sẽ đổi dropdown this.orderTypeOptions
   * @param {string} stockExchange
   */
  stockNameAndExchange(event: { stockExchange: string; stockName: string }) {
    const { stockExchange, stockName } = event;
    this.stockExchange?.setValue(stockExchange);
    this.selectedStock = stockName;

    this.updateOrderTypeOptions();
    this.getPurchasingPowerInfo();
  }

  /**
   * Lấy thông tin sức mua,KL ,tỉ lệ CMR,..
   */
  getPurchasingPowerInfo() {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(take(1))
      .subscribe((user) => {
        this.currentBroker = { ...user };
        this.hashService.hashWithSHA256(user.brokerCode, 'SHA').then((hash) => (this.macAddress = hash));

        switch (this.orderStatus?.value) {
          case EOrderCategory.BUY:
            {
              const customerValue = this.customer?.value;
              if (!customerValue) return;
              const { accountNumber, subAccount, brokerCode } = customerValue;
              const payload = [{ accountNumber, subAccount, brokerCode }];
              if (!subAccount) return;
              this.tradesOrderService
                .getPurchasingPowerBySubAccount(payload)
                .pipe(take(1))
                .subscribe((data) => {
                  if (!data.length) return;

                  this.orderCategoryInfo = {
                    number: +data[0].purchasingPower,
                    volume:
                      this.ceilingPrice === 0
                        ? 0
                        : +Math.floor(+(data[0].purchasingPower / Math.floor(this.ceilingPrice * 1000))).toFixed(0),
                    marginRate: +data[0].marginRate,
                  };
                });
            }

            break;

          case EOrderCategory.SELL:
            {
              const customerValue = this.customer?.value;
              if (!customerValue) return;
              const { accountNumber, subAccount, brokerCode } = customerValue;
              const payload = {
                accountNumber,
                subAccount,
                brokerCode,
                stockCode: this.orderStock?.value.id ?? this.orderStock?.value,
              };

              this.tradesOrderService
                .getInvestBySubAccount(payload)
                .pipe(take(1))
                .subscribe((data) => {
                  if (!data) return;
                  this.orderCategoryInfo = {
                    number:
                      this.ceilingPrice * +data.sell_able_qty === 0 ? 0 : +this.ceilingPrice * +data.sell_able_qty, // volume * gia tran
                    volume: +data.sell_able_qty, // KL giao dich o DMDT
                    debt: +data.loan_total, // debt
                  };

                  this.validateOrderVolume(
                    this.orderStatus.value,
                    this.orderVolume.value,
                    this.orderCategoryInfo.volume
                  );
                });
            }

            break;

          default:
            break;
        }
      });
  }

  /**
   * Thay đổi lệnh đặt trước
   */
  changeOrderAdvanced() {
    this.orderAdvanced?.setValue(!this.orderAdvanced?.value);
  }

  /**
   * Thưc hiện đặt/sửa lệnh
   */
  onConfirm() {
    if ((this.data.element && !this.isFormChange) || !this.placeOrderForm.valid) return;

    const acountItem = {
      acnt_no: this.customer?.value?.accountNumber,
      sub_no: this.customer?.value?.subAccount,
      idno: this.currentCustomerSelected?.identity ?? '',
    };

    const account_list = [acountItem];
    const price = this.orderPrice?.value ?? 0;
    const volume = this.orderVolume?.value ? (+this.orderVolume?.value).toString() : '0';
    const advanced = this.orderAdvanced?.value ?? false;

    if (this.currentBroker) {
      const infoUser = {
        hts_user_id: this.currentBroker.userName,
        hts_user_nm: this.currentBroker.brokerName,
        cli_mac_addr: this.macAddress,
      };

      let payloadTradeOrder: IOrderPayload = {
        ...infoUser,
        account_list,
        mkt_type: this.stockExchange?.value,
        stk_cd: this.selectedStock ?? '',
        stk_ord_tp: CONVERT_STK_ORD_TYPE[this.orderType?.value],
        ord_qty: volume,
        ord_pri: this.isNoInputPriceOrder() ? '0' : (+price * 1000).toString(),
        ...PAYLOAD_DEFAULT_TRADES_ORDER,
      };

      // Lệnh mua và bán đặt trước
      if (advanced) {
        payloadTradeOrder = {
          ...payloadTradeOrder,
          tel_no: this.currentCustomerSelected?.telephone,
        };
      }

      const dataShowInPopup = {
        title: this.data.element ? 'MES-625' : 'MES-245',
        isDerivatives: false,
        type: this.orderType?.value,
        category: this.orderStatus?.value,
        price,
        volume,
        advanced,
        stockCompany: this.selectedStock,
        subAccounts: [
          {
            accountNumber: account_list[0].acnt_no,
            customerName: this.customer?.value?.customerName,
            subAccount: account_list[0].sub_no,
          },
        ],
        derivativeCondition: null,
        derivativeDate: null,
      };

      if (this.data.element) {
        const { ordNo } = this.data.element;
        let payloadEditTradesOrders: IEditOrderPayload = {
          ...infoUser,
          ...acountItem,
          brch_cd: this.data.element.bnhCd,
          ord_no: ordNo.toString(),
          ord_qty: volume,
          ord_pri: this.isNoInputPriceOrder() ? '0' : (+price * 1000).toString(),
          stkOrdTp: CONVERT_STK_ORD_TYPE[this.orderType?.value],
          ...PAYLOAD_EDIT_DEFAULT_TRADES_ORDER,
          mkt_tp: this.stockExchange?.value,
          stk_cd: this.selectedStock ?? '',
        };

        this.openPopupConfirm(dataShowInPopup, payloadEditTradesOrders);
        return;
      }

      this.openPopupConfirm(dataShowInPopup, payloadTradeOrder);
    }
  }

  private openPopupConfirm(dataShowInPopup: any, payloadTradeOrder: IOrderPayload | IEditOrderPayload) {
    const ref = this.dialogService.open(OrderConfirmComponent, {
      width: '600px',
      maxHeight: '95vh',
      data: {
        dataShowInPopup,
        orderConfirmData: payloadTradeOrder,
        action: this.data.element ? 'edit' : 'create',
      },
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((value) => {
        if (!value) return;
        this.getOrderList();
        this.dialogRef.close();
      });
  }

  private getOrderList() {
    if (this.data.isDontResetPage) return;
    const urlSegments = this.route.url.split('?')[0].split('/');
    const url = urlSegments[urlSegments.length - 1] || '';

    let tagPageOrder: ETagPageOrder;
    if (url === ERouteTradeOrder.OPEN) {
      tagPageOrder = ETagPageOrder.OPENTRADE;
    } else if (url === ERouteTradeOrder.MATCH) {
      tagPageOrder = ETagPageOrder.MATCHTRADE;
    } else {
      tagPageOrder = ETagPageOrder.PREORDER;
    }

    this.store
      .select(selectAllBrokerLevelListByBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((allBrokerLevelList) => {
        const currentBrokerCode = this.activatedRoute.snapshot.queryParams['brokerId'];
        this.store.dispatch(
          getListTodayOrder({
            accountNumbers: [],
            brokerCode: this.getAllChildBrokerCodes(allBrokerLevelList, currentBrokerCode),
            tagPageOrder,
          })
        );
      });
  }

  /**
   * Lấy list brokerCode con của brokerCode và brokerCode đó
   * @param {IAllLevelOfBroker[]} allLevelOfBrokerList
   * @param {string} currentBrokerCode
   * @returns {string[]}
   */
  private getAllChildBrokerCodes(allLevelOfBrokerList: IAllLevelOfBroker[], currentBrokerCode: string): string[] {
    const currentBroker = allLevelOfBrokerList.find((b) => b.brokerCode === currentBrokerCode);
    const result: string[] = [];

    const collectChildBrokerCodes = (brokerItem: IAllLevelOfBroker) => {
      for (const child of brokerItem.children) {
        result.push(child.brokerCode);
        collectChildBrokerCodes(child);
      }
    };

    if (currentBroker) {
      result.push(currentBroker.brokerCode);
      collectChildBrokerCodes(currentBroker);
    }

    return result;
  }

  /**
   * Check thuộc loại giá không thể điên số
   */
  isNoInputPriceOrder() {
    return this.noInputOrderTypes.includes(this.orderPrice?.value);
  }

  /**
   * transformAccountNumber
   * @param {string} value
   * @param {number} index
   * @returns {string}
   */
  transformAccountNumber(value: string, index: number = 4): string {
    if (!value || value.length <= index) return value;
    return value.slice(0, index) + '-' + value.slice(index);
  }

  selectStock(stock: IInvestmentBySubAccountList) {
    if (this.data?.element) return;
    this.selectedStock = stock.symbol;
    this.placeOrderForm.patchValue({
      stock: stock.symbol,
    });
  }

  /**
   *
   * @param orderType
   */
  private updateFormOnTypeChange(orderType: string) {
    if (this.orderAdvanced?.value) this.orderType?.setValidators([Validators.required]);
    else
      this.orderType?.setValidators([
        Validators.required,
        timeOrderValidator(this.stockExchange?.value), // Update validator with new stockExchange value
      ]);

    this.orderType?.updateValueAndValidity(); // Refresh validation
    // const conditionalOrderTypes = [EOrderType.STOP_LO, EOrderType.STOP_MP];
    if (this.noInputOrderTypes.includes(orderType as EOrderType)) {
      this.placeOrderForm.patchValue({ price: orderType });
    }
  }

  updateOrderTypeOptions() {
    if (this.data.element) return;
    const currentTime = new Date();
    const formattedTime = `${this.padTime(currentTime.getHours())}:${this.padTime(currentTime.getMinutes())}`;

    const activeSessionIndex = OPENED_ORDERS_SESSIONS.findIndex(
      (session) => formattedTime >= session.start && formattedTime <= session.end
    );

    const stockExchange = this.stockExchange?.value;
    const isReserved = this.orderAdvanced?.value;
    let orders: { label: string; value: string }[] = [];
    switch (stockExchange) {
      case EStockExchange.HOSE:
        orders = isReserved
          ? OPENED_ORDERS_SESSIONS[activeSessionIndex].reservedHoseOrders
          : OPENED_ORDERS_SESSIONS[activeSessionIndex].hoseOrders;
        break;
      case EStockExchange.HNX:
        orders = isReserved
          ? OPENED_ORDERS_SESSIONS[activeSessionIndex].reservedHnxOrders
          : OPENED_ORDERS_SESSIONS[activeSessionIndex].hnxOrders;
        break;

      case EStockExchange.UPCOM:
        orders = isReserved
          ? OPENED_ORDERS_SESSIONS[activeSessionIndex].reservedUpcomOrders
          : OPENED_ORDERS_SESSIONS[activeSessionIndex].upcomOrders;
        break;

      default:
        break;
    }

    //patch value to type order
    this.orderTypeOptions = orders;
    this.checkRequiredOrderPrice(orders);
    if (orders.length) {
      if (orders.some((op) => op.value === EOrderType.LO)) {
        if (this.orderType.value !== EOrderType.LO) {
          this.orderType?.patchValue(EOrderType.LO);
          this.updateFormOnTypeChange(orders[0].value);
        }
      } else {
        this.orderType?.patchValue(orders[0].value);
        this.updateFormOnTypeChange(orders[0].value);
      }
    } else {
      this.orderType?.patchValue(null);
      this.placeOrderForm.patchValue({ price: null });
      this.orderType?.setValidators([outOfTimeOrderValidator]);
      this.orderType?.updateValueAndValidity();
    }

    // const key = stockExchange === EStockExchange.HOSE ?
  }

  private checkRequiredOrderPrice(order: { value: string; label: string }[]) {
    if (order.length) {
      this.orderPrice?.setValidators([
        priceOrderValidator(this.currentStockInfo?.floorPrice ?? 0, this.currentStockInfo?.ceilingPrice ?? 0),

        Validators.required,
      ]);
      this.orderPrice?.setErrors(null);
    } else {
      this.orderPrice?.clearValidators();

      const errorsControl = this.orderPrice?.errors || {};
      const { required, ...oldError } = errorsControl;
      this.orderPrice?.setErrors({ ...oldError, edit: true });
    }

    this.orderPrice?.updateValueAndValidity();
  }

  private padTime(value: number): string {
    return value < 10 ? `0${value}` : `${value}`;
  }

  /**
   * findItemByProperty
   * @template T - Type of items in the list.
   * @param id - Value match against the specified property of items.
   * @param list - The array of items to search through.
   * @param property - The key of the property to match the value against.
   */
  private findItemByProperty<T>(id: string, list: T[], property: keyof T): T | undefined {
    return list.find((item) => item[property] === id);
  }

  private readonly orderPriceByStockExchange = (
    stockExchange: string,
    orderType: string,
    orderPrice: string | number
  ): string => {
    const isTypeLO = orderType === EOrderType.LO;
    if (stockExchange === EStockExchange.HNX) {
      return isTypeLO
        ? Math.round(+orderPrice * 1000).toString() || '0'
        : Math.round(this.ceilingPrice * 1000).toString();
    }

    if (stockExchange === EStockExchange.HOSE) {
      return isTypeLO
        ? Math.round(+orderPrice * 1000).toString() || '0'
        : Math.round(this.ceilingPrice * 1000).toString();
    }

    if (stockExchange === EStockExchange.UPCOM) {
      return Math.round(+orderPrice * 1000).toString() || '0';
    }

    return Math.round(+orderPrice * 1000).toString();
  };

  trackFormChange() {
    if (!this.data.element) return;

    this.isFormChange =
      (this.data.element.typeTrades === EOrderType.LO
        ? this.data.element?.orderPrice !== +this.orderPrice?.value
        : this.data.element?.orderPrice !== this.orderPrice?.value) ||
      this.data.element?.orderVolume !== +this.orderVolume?.value;
  }

  /**
   * Inner filter function
   * @param {string} value search value
   * @param {IOptionList[]} options
   */
  private _filter(value: string, options: IAllAccountNumber[]): IAllAccountNumber[] {
    const filterValue = value?.toLowerCase();

    return options.filter(
      (option) =>
        removeVietnameseTones(option.customerName).toLowerCase().includes(removeVietnameseTones(filterValue)) ||
        option.accountNumber?.toLowerCase().includes(filterValue)
    );
  }

  displayAutoCompleteFn(customer: IAllAccountNumber) {
    return customer ? customer?.accountNumber + ' - ' + customer?.customerName : '';
  }

  fillOrderPrice(price: number) {
    this.orderPrice.patchValue(price);
  }

  changeStepPrice(price: number) {
    const currentStock = this.stockExchange.value ?? EStockExchange.HOSE;
    switch (currentStock) {
      case EStockExchange.HOSE:
        if (price < 10) this.stepPrice = 0.01;
        else if (price < 50) this.stepPrice = 0.05;
        else this.stepPrice = 0.1;
        break;
      case EStockExchange.HNX:
        this.stepPrice = 0.1;
        break;
      case EStockExchange.UPCOM:
        this.stepPrice = 0.1;
        break;
      default:
        this.stepPrice = 0.01;
    }
  }
}
