<div class="place-order-popup-container">
  <div class="dialog-header">
    <div class="title-cls typo-quicksand-2">{{ data.title | translate }}</div>
    <mat-icon mat-dialog-close svgIcon="icon:cross-circle"></mat-icon>
  </div>

  <div class="border-line"></div>

  <form [formGroup]="placeOrderForm" class="dialog-body">
    <div itemSize="62" class="customer-info column">
      <div class="search-field">
        <mat-form-field>
          <input
            type="text"
            class="input-cls-custom input-style-common"
            [placeholder]="'MES-652' | translate"
            [formControl]="searchCustomerControl"
            matInput
            [matAutocomplete]="subNoSelection"
          />
          <img src="./assets/icons/search-normal.svg" alt="search" class="icon-search-cls" />
          <mat-autocomplete
            #subNoSelection="matAutocomplete"
            [displayWith]="displayAutoCompleteFn"
            (optionSelected)="getCustomerSelected($event.option.value)"
          >
            <cdk-virtual-scroll-viewport class="autocomplete--viewport" itemSize="35">
              <mat-option
                *cdkVirtualFor="let item of listFilterCustomerNameOptions"
                [value]="item"
                class="autocomplete-item"
              >
                <span class="typo-quicksand-1">{{ item.accountNumber + ' - ' + item.customerName }}</span>
              </mat-option>
            </cdk-virtual-scroll-viewport>
          </mat-autocomplete>
        </mat-form-field>
      </div>
      @if ( isSearchingCustomer ) {
      <div class="spinner"><mat-spinner [style.width]="'36px'" [style.height]="'36px'"></mat-spinner></div>

      } @else{ @if(listSubAccountList.length) {
      <cdk-virtual-scroll-viewport itemSize="20" class="list-customers">
        <button
          class="customer box-shadow"
          *cdkVirtualFor="let customer of listSubAccountList; let i = index"
          (click)="selectCustomer(customer)"
          [ngClass]="{ active: customer.isSelect }"
        >
          <div class="name typo-quicksand-4">
            {{ customer.customerName }}
            {{
              SUB_ACCOUNT_TO_LABEL[customer.subAccount]
                ? '| ' + (SUB_ACCOUNT_TO_LABEL[customer.subAccount] | translate)
                : ''
            }}
          </div>
          <div class="accountNumber typo-quicksand-1" [ngClass]="{ active: customer.isSelect }">
            {{ transformAccountNumber(customer.accountNumber) }}-{{ customer.subAccount }}
          </div>
        </button>
      </cdk-virtual-scroll-viewport>
      } @else {
      <div class="typo-quicksand-1" style="margin-top: 16px">{{ 'MES-585' | translate }}</div>
      } }
    </div>

    <!-- Column 2 : Thông tin mã -->
    <div class="stock-info column">
      <app-stock-info-v2
        [initialStockList]="allStockList"
        [initialStock]="data.initialStock"
        formControlName="stock"
        [selectedStockName]="selectedStock"
        (stockInfoChange)="stockInfo($event)"
        (stockNameAndExchangeChange)="stockNameAndExchange($event)"
        (orderPriceEvent$)="fillOrderPrice($event)"
      ></app-stock-info-v2>
    </div>

    <!-- Column 3 : Thông tin đặt lệnh -->
    <div class="order-info column">
      <!-- Mua / Bán -->
      <div class="order-category">
        <button
          class="btn-category typo-quicksand-1 box-shadow"
          *ngFor="let category of orderCategories"
          [ngClass]="{
              buy: category.active && category.value === EOrderCategory.BUY,
              sell:category.active && category.value === EOrderCategory.SELL,
            }"
          (click)="changeOrderCategory(category.value)"
        >
          {{ category.label | translate | uppercase }}
        </button>
      </div>

      <!-- Loại Lệnh -->
      <app-form-control>
        <div class="order-type box-shadow" [class.disable-select]="data.element">
          <mat-form-field>
            <mat-select
              formControlName="orderType"
              [placeholder]="'MES-172' | translate"
              (selectionChange)="onTypeChange($event.value)"
            >
              <mat-option [value]="item.value" *ngFor="let item of orderTypeOptions">
                {{ 'MES-172' | translate }} {{ item.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </app-form-control>

      <!-- Giá đặt -->
      @if (!orderType.value || orderType.value === EOrderType.LO) {
      <app-form-control>
        <div class="order-price box-shadow">
          <app-amount-selector-no-border
            formControlName="price"
            [placeholder]="'MES-176' | translate"
            [step]="stepPrice"
            [height]="'54px'"
            [mask]="'separator.2'"
            [isDisabled]="isDisabledPrice"
          ></app-amount-selector-no-border>
        </div>
      </app-form-control>
      } @else {
      <div></div>
      }

      <!-- Khối lượng giao dịch -->
      <div class="order-volume-container box-shadow">
        <!-- Khối lượng -->
        <app-form-control>
          <div class="volume box-shadow">
            <app-amount-selector-no-border
              formControlName="volume"
              [placeholder]="'MES-229' | translate"
              [step]="100"
              [height]="'54px'"
              [mask]="'separator'"
              [invalidKeys]="[',']"
              [isDisabled]="isDisabledVolume"
            ></app-amount-selector-no-border>
          </div>
        </app-form-control>

        <!-- Khối lượng mua / bán tối đa -->
        <div
          *ngIf="customer?.value && listSubAccountList.length && orderStock?.value"
          class="investment-info box-shadow"
        >
          <div *ngIf="orderStatus?.value === EOrderCategory.BUY" class="purchasing-power typo-quicksand-1">
            <span>{{ 'MES-203' | translate }}</span>
            <span
              >{{
                !orderCategoryInfo || orderCategoryInfo.number === 0
                  ? '-'
                  : (orderCategoryInfo.number | numberFormat) + ' VND'
              }}
            </span>
          </div>

          <div
            class="max-volume typo-quicksand-1"
            [ngClass]="{
              buy: orderStatus.value === EOrderCategory.BUY,
              sell: orderStatus.value === EOrderCategory.SELL
            }"
          >
            <span>{{
              orderStatus.value === EOrderCategory.BUY ? ('MES-654' | translate) : ('MES-655' | translate)
            }}</span>
            <span>
              {{
                !orderCategoryInfo || orderCategoryInfo.volume === 0
                  ? '-'
                  : (orderCategoryInfo.volume | numberFormat) + ' CP'
              }}
            </span>
          </div>
        </div>
      </div>

      <!-- Đặt trước -->
      <button class="order-advanced box-shadow" *ngIf="!data?.element" (click)="changeOrderAdvanced()">
        <mat-icon [svgIcon]="orderAdvanced.value ? 'icon:checkbox-circle' : 'icon:rectangle-5813'"></mat-icon>
        <div class="title typo-quicksand-1" [ngClass]="{ active: orderAdvanced.value }">
          {{ 'MES-665' | translate }}
        </div>
      </button>

      <div class="divider"></div>

      <button
        class="confirm box-shadow"
        [ngClass]="{
          disable: placeOrderForm.invalid || (data.element && !isFormChange),
          buy: orderStatus.value === EOrderCategory.BUY,
          sell: orderStatus.value === EOrderCategory.SELL
        }"
        (click)="onConfirm()"
      >
        <div class="title typo-quicksand-6">{{ 'MES-651' | translate | uppercase }}</div>
        <div class="price">
          {{
            this.placeOrderForm.get('price')?.value * 1000 * this.placeOrderForm.get('volume')?.value === 0
              ? '-'
              : (this.placeOrderForm.get('price')?.value * 1000 * this.placeOrderForm.get('volume')?.value
                | numberFormat)
          }}
          VND
        </div>
      </button>
    </div>

    <!-- Column 4: Thông tin danh mục đầu tư theo tiểu khoản -->
    <div *ngIf="customer?.value && orderStatus?.value === EOrderCategory.SELL" class="order-history column">
      @if ( isFindingOldInvestment ) {
      <div class="spinner"><mat-spinner [style.width]="'36px'" [style.height]="'36px'"></mat-spinner></div>

      } @else{ @if(investmentBySubAccountList.length) {
      <div class="stock-list">
        <button
          *ngFor="let stock of investmentBySubAccountList"
          class="stock-item box-shadow"
          (click)="selectStock(stock)"
          [ngClass]="{ active: selectedStock && selectedStock === stock.symbol }"
        >
          <!-- Mã | Sàn-->
          <div class="stock-info">
            <div class="brand">
              <span
                [ngClass]="{ active: selectedStock && selectedStock === stock.symbol }"
                class="symbol typo-quicksand-1"
                >{{ stock.symbol }}</span
              >
              <span class="exchange typo-quicksand-1">{{ stock.exchange }}</span>
            </div>

            <mat-checkbox
              [checked]="selectedStock && selectedStock === stock.symbol"
              class="custom-checkbox"
            ></mat-checkbox>
          </div>

          <div class="divider"></div>

          <div class="details">
            <!-- KL được GD | Tổng KL -->
            <div class="line">
              <span [style.color]="'var(--color--sha_v2--subtitle)'" class="typo-quicksand-4">
                {{ 'MES-662' | translate }} | {{ 'MES-663' | translate }}</span
              >
              <span [style.color]="'var(--color--sha_v2--black)'" class="typo-quicksand-1"
                >{{ stock.tradableVolume | numberFormat }} | {{ stock.totalVolume | numberFormat }}</span
              >
            </div>

            <!-- Giá vốn -->
            <div class="line">
              <span [style.color]="'var(--color--sha_v2--subtitle)'" class="typo-quicksand-4">{{
                'MES-664' | translate
              }}</span>
              <span [style.color]="'var(--color--sha_v2--black)'" class="typo-quicksand-1">{{
                stock.price | numberFormat : 'decimal' : 'en-US' : 2
              }}</span>
            </div>
          </div>
        </button>
      </div>
      } @else {
      <div [style.color]="'var(--color--sha_v2--subtitle)'" class="typo-quicksand-1">{{ 'MES-585' | translate }}</div>
      } }
    </div>
  </form>
</div>
