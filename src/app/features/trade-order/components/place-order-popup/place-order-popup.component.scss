:host {
  display: flex;
  width: 100%;
  height: 100%;
}
.place-order-popup-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  .title-cls {
    color: var(--color--sha_v2--black);
  }

  mat-icon[data-mat-icon-name='cross-circle'] {
    width: 30px !important;
    height: 30px !important;
  }
}

.border-line {
  border: 1px solid var(--color--sha_v2--border_2);
  margin: 0 8px;
}

.dialog-body {
  display: flex;
  width: 100%;
  height: calc(100% - 90px);

  .spinner {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .column {
    flex: 1;

    &:not(:last-child) {
      border-right: 1px solid var(--color--sha_v2--border_2);
    }

    &.customer-info,
    &.stock-info,
    &.order-info {
      padding: 24px;
    }

    .search-field {
      width: 100%;
      position: relative;
      border: 1px solid var(--color--sha_v2--border_2);
      border-radius: 8px;

      ::ng-deep {
        .mat-mdc-form-field-infix {
          padding: 8px 16px !important;
        }

        .mat-mdc-autocomplete-panel {
          height: 256px;
        }
      }

      .input-cls-custom {
        width: 100%;
        padding-left: 24px;
        font: 500 16px / 20px 'quicksand-custom-font-regular', sans-serif;
      }

      img[alt='search'] {
        width: 20px;
        height: 20px;
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    &.customer-info {
      .list-customers {
        margin-top: 8px;
        height: calc(100vh - 200px);

        &.cdk-virtual-scroll-viewport {
          ::ng-deep {
            .cdk-virtual-scroll-content-wrapper {
              display: flex;
              flex-direction: column;
              gap: 8px;
            }
          }
        }

        .customer {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          gap: 4px;
          padding: 12px;
          border-radius: 8px;
          border: 1px solid var(--color--sha_v2--border_2);
          position: relative;

          &:hover {
            cursor: pointer;
          }

          &.active::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 1px;
            border-radius: 8px;
            background: linear-gradient(135deg, #fa9528 0%, #f76f08 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: exclude;
            mask-composite: exclude;
            pointer-events: none;
          }

          .name {
            color: var(--color--sha_v2--subtitle);
          }

          .accountNumber {
            color: var(--color--sha_v2--black);

            &.active {
              background: var(--color--brand-v2--linear);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }
    }

    &.order-info {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .order-category {
        display: flex;
        align-items: center;
        gap: 8px;
        border-radius: 8px;

        .btn-category {
          display: flex;
          justify-content: center;
          align-items: center;
          flex: 1;
          height: 44px;
          opacity: 0.5;
          border-radius: 8px;
          border: 1px solid var(--color--sha_v2--border_2);

          &:hover {
            cursor: pointer;
          }

          &.buy {
            opacity: 1;
            color: var(--color--sha_v2--green);
            border: 1px solid var(--color--sha_v2--green);
          }

          &.sell {
            opacity: 1;
            color: var(--color--sha_v2--red);
            border: 1px solid var(--color--sha_v2--red);
          }
        }
      }

      .order-type {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        border-radius: 8px;
        border: 1px solid var(--color--sha_v2--border_2);

        &:hover {
          cursor: pointer;
        }

        &.disable-select {
          opacity: 0.5;
          pointer-events: none;
          background-color: var(--color--background--disable);
        }

        ::ng-deep {
          mat-select {
            .mat-mdc-select-value {
              text-align: center;

              .mat-mdc-select-min-line {
                color: var(--color--sha_v2--black);
                font: 600 16px / 20px 'quicksand-custom-font-regular', sans-serif;
              }
            }
          }
        }
      }

      .order-price {
        height: 54px;

        .input-text {
          width: 100%;
          height: 100%;
          border-radius: 8px;
          border: 1px solid var(--color--sha_v2--border_2);
          text-align: center;
        }
      }

      .order-volume-container {
        border: 1px solid var(--color--sha_v2--border_2);
        border-radius: 8px;

        .volume {
          height: 54px;
          app-amount-selector-no-border {
            ::ng-deep {
              .selector-wrap {
                border: none !important;
                border-bottom: 1px solid var(--color--sha_v2--border_2) !important;
                border-bottom-left-radius: 8px !important;
                border-bottom-right-radius: 8px !important;
              }
            }
          }
        }

        .investment-info {
          padding: 10px 12px 8px;

          .purchasing-power,
          .max-volume {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 8px;
            padding: 8px 0;

            &.buy {
              color: var(--color--sha_v2--green);
            }

            &.sell {
              color: var(--color--sha_v2--red);
            }

            .label {
              color: var(--color--sha_v2--black);
            }

            .value {
              color: var(--color--sha_v2--black);
            }
          }
        }
      }

      .order-advanced {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 12px 10px;
        border-radius: 8px;
        border: 1px solid var(--color--sha_v2--border_2);

        &:hover {
          cursor: pointer;
        }

        &.order-advanced:has(.active) {
          position: relative;
          padding: 12px 10px;
          border-radius: 8px !important;
          overflow: hidden;
        }

        &.order-advanced:has(.active)::before {
          content: '';
          position: absolute;
          inset: 0;
          padding: 1px; /* Border thickness */
          border-radius: 8px;
          background: var(--color--brand-v2--linear);
          -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
          mask-composite: exclude;
        }

        .title {
          color: var(--color--sha_v2--subtitle);

          &.active {
            background: var(--color--brand-v2--linear);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }

      .divider {
        width: 100%;
        border: 1px solid var(--color--sha_v2--border_2);
      }

      .confirm {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        border-radius: 8px;
        padding: 8px 0;

        &:hover {
          cursor: pointer;
        }

        .title,
        .price {
          color: var(--color--sha_v2--black);
        }

        &.buy {
          background: var(--color--sha_v2--green);

          .title,
          .price {
            color: var(--color--neutral-v2--white);
          }
        }

        &.sell {
          background: var(--color--sha_v2--red);

          .title,
          .price {
            color: var(--color--neutral-v2--white);
          }
        }

        &.disable {
          opacity: 0.5;
          background: var(--color--sha_v2--border_2);
          border: 1px solid var(--color--sha_v2--border_2);
          pointer-events: none;

          .title,
          .price {
            color: var(--color--sha_v2--subtitle);
          }
        }
      }
    }

    &.order-history {
      height: 100%;
      overflow: auto;
      padding: 24px;

      .stock-list {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .stock-item {
          display: flex;
          flex-direction: column;
          gap: 12px;
          border-radius: 8px;
          padding: 12px;
          position: relative;

          &.active::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 1px;
            border-radius: 8px;
            background: linear-gradient(135deg, #fa9528 0%, #f76f08 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: exclude;
            mask-composite: exclude;
            pointer-events: none;
          }

          .stock-info {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .brand {
              display: flex;
              align-items: center;
              gap: 8px;

              .symbol {
                color: var(--color--sha_v2--black);

                &.active {
                  background: var(--color--brand-v2--linear);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
              }

              .exchange {
                color: var(--color--sha_v2--subtitle);
                padding: 2px 12px;
                background-color: var(--color--sha_v2--background_2);
                border-radius: 8px;
              }
            }
          }
        }

        .divider {
          border: 1px solid var(--color--sha_v2--border_2);
        }

        .details {
          .line {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
        }
      }
    }
  }
}

app-form-control {
  ::ng-deep {
    .form_err_msg {
      font: 500 12px / 14px 'quicksand-custom-font-regular', sans-serif !important;
    }
  }
}

.box-shadow {
  box-shadow: 0px 5px 10px 0px rgba(148, 107, 27, 0.1), 0px -1px 5px 0px rgba(148, 107, 27, 0.03);
}

mat-option {
  font: 500 16px / 20px 'quicksand-custom-font-regular', sans-serif !important;
}
