import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TradesOrderService } from './trades-order.service';
import { ApiService } from 'src/app/core/services';
import { of } from 'rxjs';
import {
  IOrderPayload,
  IPayloadOrderList,
  IPlaceOrderResponse,
  IOrderListResponse,
  IFilterOpenTradesParam,
  IStockQuotePayload,
  IStockQuoteListResponse,
  IDataQuoteSummaryResponse,
  IGetDebtInAssetResponse,
  IPayloadGetDebtInAsset,
  IConfirmOrderTrades,
  IEditOrderPayload,
  ICancelOrderPayload,
  IInvestmentBySubAccountResponse,
  IBuyableInfoPayload,
  EOrderType,
  EStockExchange
} from '../models/trade-order';
import { IPayloadInfoSubAccount, IPurchasingPowerAssetInfo } from '../../assets/models/asset';
import { ApiResponse } from 'src/app/core/models/api-response';
import { CONVERT_TRADE_STATUS_TRADE_TO_ENUM, ESellType } from '../constants/trade-order';

describe('TradesOrderService', () => {
  let service: TradesOrderService;
  let mockApiService: jasmine.SpyObj<ApiService>;
  let httpMock: HttpTestingController;

  const mockApiResponse = <T>(data: T): ApiResponse<T> => ({
    data,
    message: 'Success',
    statusCode: 200
  });

  beforeEach(() => {
    const apiServiceSpy = jasmine.createSpyObj('ApiService', ['get', 'post']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        TradesOrderService,
        { provide: ApiService, useValue: apiServiceSpy }
      ]
    });

    service = TestBed.inject(TradesOrderService);
    mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  // Service Initialization
  it('No.1: should be created successfully', () => {
    expect(service).toBeTruthy();
    expect(service.url).toBe('v1/order');
    expect(service.urlAsset).toBe('v1/assets');
    expect(service.urlSystem).toBe('v1/system');
    expect(service.urlAdvice).toBe('v1/advice');
  });

  // Asset Information Methods
  it('No.2: should get purchasing power by sub account', () => {
    const mockPayload: IPayloadInfoSubAccount[] = [
      { accountNumber: 'ACC001', subAccount: '001', brokerCode: 'BR001' }
    ];
    const mockResponse: IPurchasingPowerAssetInfo[] = [
      {
        accountNumber: 'ACC001',
        purchasingPower: 1000000,
        debt: 200000,
        marginRate: 80.5,
        children: []
      }
    ];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.getPurchasingPowerBySubAccount(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith(
      'v1/assets/assets-info/purchase-power',
      mockPayload
    );
  });

  it('No.3: should get investment by sub account', () => {
    const mockPayload: IPayloadGetDebtInAsset = {
      accountNumber: 'ACC001',
      subAccount: '001',
      brokerCode: 'BR001',
      stockCode: 'VIC'
    };
    const mockResponse: IGetDebtInAssetResponse = {
      sell_able_qty: 100,
      loan_total: 50000
    };

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.getInvestBySubAccount(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith(
      'v1/assets/investment_portfolio/invest',
      mockPayload
    );
  });

  it('No.4: should get investment portfolio by sub account', () => {
    const mockPayload = { accountNumber: 'ACC001', subAccount: '001' };
    const mockResponse: IInvestmentBySubAccountResponse[] = [
      {
        subNo: '001',
        stkCd: 'VIC',
        sellAbleQty: 100,
        costPri: 25000,
        ownQtyPl: 100
      }
    ];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.getInvestmentPortfolioBySubAccount(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith(
      'v1/assets/investment_portfolio/invest-sub',
      mockPayload
    );
  });

  // Order List Methods
  it('No.5: should get list of orders for today with basic parameters', () => {
    const mockPayload: IPayloadOrderList = {
      accountNumbers: ['ACC001'],
      brokerCode: ['BR001'],
      tagPageOrder: 1
    };
    const mockFilter: IFilterOpenTradesParam = {
      isFilter: false,
      buySellStatus: null,
      activeStatus: null,
      isStopTypeTrades: null,
      valueTrades: { start: null, end: null },
      revenue: { start: null, end: null },
      netTradesFee: { start: null, end: null },
      brokerageCommission: { start: null, end: null },
      optionSelection: {
        codeOptionsSelect: null,
        typeTradesOptionsSelect: null,
        statusTradesOptionsSelect: null,
        numberAccountOptionsSelect: null,
        traderOptionsSelect: null,
        conditionTradesOptionsSelect: null,
        orderChannelOptionSelect: null,
        statusTradesOptionsSelectValue: null,
        orderChannelOptionSelectValue: null,
        typeTradesOptionsSelectValue: null
      },
      dateActiveRange: { start: null, end: null },
      dateRange: { start: null, end: null }
    };
    const mockResponse: IOrderListResponse[] = [];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.getListOrderToday(mockPayload, '2024-01-01', mockFilter, 'OPEN', ['1', '2']).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/today', jasmine.any(Object));
  });

  it('No.6: should get list of orders for today with filters applied', () => {
    const mockPayload: IPayloadOrderList = {
      accountNumbers: ['ACC001'],
      brokerCode: ['BR001'],
      tagPageOrder: 1
    };
    const mockFilter: IFilterOpenTradesParam = {
      isFilter: true,
      buySellStatus: [0], // BUY
      activeStatus: null,
      isStopTypeTrades: null,
      valueTrades: { start: '1000000', end: '5000000' },
      revenue: { start: null, end: null },
      netTradesFee: { start: null, end: null },
      brokerageCommission: { start: null, end: null },
      optionSelection: {
        codeOptionsSelect: ['VIC'],
        typeTradesOptionsSelect: null,
        statusTradesOptionsSelect: null,
        numberAccountOptionsSelect: ['ACC001'],
        traderOptionsSelect: null,
        conditionTradesOptionsSelect: null,
        orderChannelOptionSelect: null,
        statusTradesOptionsSelectValue: [1, 2],
        orderChannelOptionSelectValue: null,
        typeTradesOptionsSelectValue: ['LO']
      },
      dateActiveRange: { start: '2024-01-01', end: '2024-01-31' },
      dateRange: { start: '2024-01-01', end: '2024-01-31' }
    };
    const mockResponse: IOrderListResponse[] = [];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.getListOrderToday(mockPayload, '2024-01-01', mockFilter, 'OPEN', ['1', '2']).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/today', jasmine.objectContaining({
      filterOrder: jasmine.objectContaining({
        accountNumbers: ['ACC001'],
        stockCodes: ['VIC'],
        sellBuyTp: ESellType.BUY,
        ordType: ['LO'],
        mthAmt: { from: '1000000', to: '5000000' }
      })
    }));
  });

  it('No.7: should get list of order history with filters', () => {
    const mockPayload: IPayloadOrderList = {
      accountNumbers: ['ACC001'],
      brokerCode: ['BR001'],
      tagPageOrder: 1
    };
    const mockFilter: IFilterOpenTradesParam = {
      isFilter: true,
      buySellStatus: [1], // SELL
      activeStatus: null,
      isStopTypeTrades: null,
      valueTrades: { start: null, end: null },
      revenue: { start: null, end: null },
      netTradesFee: { start: null, end: null },
      brokerageCommission: { start: null, end: null },
      optionSelection: {
        codeOptionsSelect: null,
        typeTradesOptionsSelect: null,
        statusTradesOptionsSelect: null,
        numberAccountOptionsSelect: null,
        traderOptionsSelect: null,
        conditionTradesOptionsSelect: null,
        orderChannelOptionSelect: null,
        statusTradesOptionsSelectValue: null,
        orderChannelOptionSelectValue: null,
        typeTradesOptionsSelectValue: null
      },
      dateActiveRange: { start: null, end: null },
      dateRange: { start: '2024-01-01', end: '2024-01-31' }
    };
    const mockResponse: IOrderListResponse[] = [];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.getListOrderHistory(mockPayload, '2024-01-01', mockFilter, 'HISTORY', ['3', '4'], ['5', '6']).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/histories', jasmine.any(Object));
  });

  // Order Placement Methods
  it('No.8: should place buy order', () => {
    const mockPayload: IOrderPayload = {
      hts_user_id: 'user123',
      hts_user_nm: 'User Name',
      cli_mac_addr: 'mac123',
      mkt_type: 'HOSE',
      account_list: [{ acnt_no: 'ACC001', sub_no: '001', idno: 'ID001' }],
      stk_cd: 'VIC',
      stk_ord_tp: '01',
      ord_qty: '100',
      ord_pri: '25000',
      bank_cd: '9999',
      lang_code: 'V',
      ord_mdm_tp: '01'
    };
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '0',
      error_desc: 'Success',
      success: true,
      count: 1,
      total_record: '1',
      data_list: []
    }];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.placeBuyOrder(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/buy-order', mockPayload);
  });

  it('No.9: should place buy advance order', () => {
    const mockPayload: IOrderPayload = {
      hts_user_id: 'user123',
      hts_user_nm: 'User Name',
      cli_mac_addr: 'mac123',
      mkt_type: 'HOSE',
      account_list: [{ acnt_no: 'ACC001', sub_no: '001', idno: 'ID001' }],
      stk_cd: 'VIC',
      stk_ord_tp: '01',
      ord_qty: '100',
      ord_pri: '25000',
      bank_cd: '9999',
      lang_code: 'V',
      ord_mdm_tp: '01',
      tel_no: '*********'
    };
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '0',
      error_desc: 'Success',
      success: true,
      count: 1,
      total_record: '1',
      data_list: []
    }];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.placeBuyAdvanceOrder(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/buy-advance-order', mockPayload);
  });

  it('No.10: should place sell order', () => {
    const mockPayload: IOrderPayload = {
      hts_user_id: 'user123',
      hts_user_nm: 'User Name',
      cli_mac_addr: 'mac123',
      mkt_type: 'HOSE',
      account_list: [{ acnt_no: 'ACC001', sub_no: '001', idno: 'ID001' }],
      stk_cd: 'VIC',
      stk_ord_tp: '01',
      ord_qty: '100',
      ord_pri: '25000',
      bank_cd: '9999',
      lang_code: 'V',
      ord_mdm_tp: '01'
    };
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '0',
      error_desc: 'Success',
      success: true,
      count: 1,
      total_record: '1',
      data_list: []
    }];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.placeSellOrder(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/sell-order', mockPayload);
  });

  it('No.11: should place sell advance order', () => {
    const mockPayload: IOrderPayload = {
      hts_user_id: 'user123',
      hts_user_nm: 'User Name',
      cli_mac_addr: 'mac123',
      mkt_type: 'HOSE',
      account_list: [{ acnt_no: 'ACC001', sub_no: '001', idno: 'ID001' }],
      stk_cd: 'VIC',
      stk_ord_tp: '01',
      ord_qty: '100',
      ord_pri: '25000',
      bank_cd: '9999',
      lang_code: 'V',
      ord_mdm_tp: '01',
      tel_no: '*********'
    };
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '0',
      error_desc: 'Success',
      success: true,
      count: 1,
      total_record: '1',
      data_list: []
    }];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.placeSellAdvanceOrder(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/sell-advance-order', mockPayload);
  });

  // Order Management Methods
  it('No.12: should edit place order', () => {
    const mockPayload: IEditOrderPayload = {
      hts_user_id: 'user123',
      hts_user_nm: 'User Name',
      cli_mac_addr: 'mac123',
      acnt_no: 'ACC001',
      sub_no: '001',
      idno: 'ID001',
      brch_cd: 'BR001',
      ord_no: 'ORD001',
      ord_qty: '100',
      ord_pri: '25000',
      ord_mdm_tp: '01',
      stk_cd: 'VIC',
      mkt_tp: 'HOSE',
      langCode: 'V',
      stkOrdTp: '01',
      bankCd: '9999'
    };
    const mockResponse: ApiResponse<IPlaceOrderResponse[]> = {
      data: [],
      message: 'Success',
      statusCode: 200
    };

    mockApiService.post.and.returnValue(of(mockResponse));

    service.editPlaceOrder(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/modify-order', mockPayload);
  });

  it('No.13: should cancel place order', () => {
    const mockPayload: ICancelOrderPayload = {
      hts_user_id: 'user123',
      hts_user_nm: 'User Name',
      cli_mac_addr: 'mac123',
      account_list: [{ acnt_no: 'ACC001', sub_no: '001', idno: 'ID001' }],
      ord_no: 'ORD001',
      bank_cd: '9999',
      brch_cd: 'BR001',
      ord_mdm_tp: '01'
    };
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '0',
      error_desc: 'Success',
      success: true,
      count: 1,
      total_record: '1',
      data_list: []
    }];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.cancelPlaceOrder(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/cancel-order', mockPayload);
  });

  it('No.14: should cancel place advance order', () => {
    const mockPayload: ICancelOrderPayload = {
      hts_user_id: 'user123',
      hts_user_nm: 'User Name',
      cli_mac_addr: 'mac123',
      account_list: [{ acnt_no: 'ACC001', sub_no: '001', idno: 'ID001' }],
      ord_no: 'ORD001',
      ord_frt_dt: '2024-01-01',
      lang_code: 'V',
      ord_mdm_tp: '01'
    };
    const mockResponse: IPlaceOrderResponse[] = [{
      error_code: '0',
      error_desc: 'Success',
      success: true,
      count: 1,
      total_record: '1',
      data_list: []
    }];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.cancelPlaceAdvanceOrder(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/cancel-advance-order', mockPayload);
  });

  // Trading Information Methods
  it('No.15: should get stock quote list', () => {
    const mockPayload: IStockQuotePayload = {
      symbol: 'VIC',
      fetchCount: '10',
      lastIndex: '0',
      lastSize: '100'
    };
    const mockResponse: IStockQuoteListResponse = {
      lastIndex: 0,
      lastSize: 0,
      data: [
        {
          o: '25000',
          ti: '09:00:00',
          c: '25500',
          ch: '500',
          h: '26000',
          l: '24500',
          mb: 'B',
          mv: '1000',
          r: '2.0',
          va: '25500000',
          vo: '1000'
        }
      ]
    };

    mockApiService.get.and.returnValue(of(mockApiResponse(mockResponse)));

    service.getStockQuoteList(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/system/trading/stock-quote-list', {
      params: {
        symbol: 'VIC',
        fetchCount: '10',
        lastIndex: '0',
        lastSize: '100'
      }
    });
  });

  it('No.16: should get quote summary', () => {
    const stockCode = 'VIC';
    const mockResponse: IDataQuoteSummaryResponse[] = [
      {
        price: 25000,
        volume: 1000,
        rate: 10.5,
        buyVolume: 600,
        sellVolume: 400,
        bsVolume: 1000,
        buyRate: 60.0,
        sellRate: 40.0,
        bsRate: 100.0
      }
    ];

    mockApiService.get.and.returnValue(of(mockApiResponse(mockResponse)));

    service.getQuoteSummary(stockCode).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.get).toHaveBeenCalledWith('v1/system/trading/quote-summary', {
      params: { symbol: 'VIC' }
    });
  });

  it('No.17: should get buyable info', () => {
    const mockPayload: IBuyableInfoPayload = {
      acnt_no: 'ACC001',
      sub_no: '001',
      bank_cd: '9999',
      stk_cd: 'VIC',
      mkt_trd_tp: 'HOSE',
      ord_pri: '25000'
    };
    const mockResponse = [
      {
        buy_abl_qty: '1000',
        buying_power: '********'
      }
    ];

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.getBuyableInfo(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/advice/buyable-info', mockPayload);
  });

  it('No.18: should get confirm order trades', () => {
    const mockPayload = {
      accNo: ['ACC001'],
      from: '2024-01-01',
      to: '2024-01-31',
      broker: 'BR001',
      pageIndex: 1,
      pageSize: 10,
      emNo: 'EMP001',
      sellBuyTp: 'ALL',
      confirmStatus: 'PENDING'
    };
    const mockResponse = {
      totalPages: 5,
      confirmOrder: [
        {
          dateTrades: '2024-01-15',
          timeTrades: '09:30:00',
          accountNumber: 'ACC001',
          subAccount: '001',
          status: 'PENDING',
          ordNo: 'ORD001',
          typeTrades: 'LO',
          code: 'VIC',
          orderPrice: '25000',
          matchedPrice: '25000',
          matchedVolume: '100',
          orderVolume: '100',
          tradeStatus: 'MATCHED',
          trader: 'Trader1',
          orderChannel: 'WEB'
        }
      ]
    };

    mockApiService.post.and.returnValue(of(mockApiResponse(mockResponse)));

    service.getConfirmOrderTrades(mockPayload).subscribe(result => {
      expect(result).toEqual(mockResponse);
    });

    expect(mockApiService.post).toHaveBeenCalledWith('v1/order/confirm-order', jasmine.objectContaining({
      accountNumbers: ['ACC001'],
      fromDate: '2024-01-01',
      toDate: '2024-01-31',
      page: 1,
      pageSize: 10,
      emNo: 'EMP001',
      sellBuyTp: 'ALL',
      confirmStatus: 'PENDING'
    }));
  });

  // Utility Methods
  it('No.19: should convert trade status correctly', () => {
    const mockTradeStatusArray = [1, 2, 3];
    const expectedResult = [
      CONVERT_TRADE_STATUS_TRADE_TO_ENUM[1],
      CONVERT_TRADE_STATUS_TRADE_TO_ENUM[2],
      CONVERT_TRADE_STATUS_TRADE_TO_ENUM[3]
    ];

    const result = service.convertTradeStatus(mockTradeStatusArray);

    expect(result).toEqual(expectedResult);
    expect(result.length).toBe(3);
  });
});
