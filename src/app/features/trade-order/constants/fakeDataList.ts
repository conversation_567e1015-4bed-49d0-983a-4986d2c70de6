import {
  CONVERT_ORDER_CHANNEL,
  CONVERT_SESSION_TO_LABEL,
  CONVERT_STATUS_CONFIRM_TRADE_TO_LABEL,
  CONVERT_TYPE_TRADES_TO_VALUE,
  EOrderChanel,
  ESession,
  EStatusConfirmTrade,
  EStatusTradeFilter,
  ETypeTradeFilter,
} from './trade-order';

export const codeCKOptions = [
  {
    label: 'SHS : HNX',
    isSelect: true,
  },
  {
    label: 'TCB : HSX',
    isSelect: true,
  },
  {
    label: 'MBB : HSX',
    isSelect: true,
  },
  {
    label: 'SHA : HSX',
    isSelect: true,
  },
  {
    label: 'VCB : HSX',
    isSelect: true,
  },
];

export const typeTradesDropdownOption = [
  {
    label: CONVERT_TYPE_TRADES_TO_VALUE[ETypeTradeFilter.LO],
    value: ETypeTradeFilter.LO,
    isSelect: true,
  },
  {
    label: CONVERT_TYPE_TRADES_TO_VALUE[ETypeTradeFilter.MP],
    value: ETypeTradeFilter.MP,
    isSelect: true,
  },
  {
    label: CONVERT_TYPE_TRADES_TO_VALUE[ETypeTradeFilter.ATO],
    value: ETypeTradeFilter.ATO,
    isSelect: true,
  },
  {
    label: CONVERT_TYPE_TRADES_TO_VALUE[ETypeTradeFilter.ATC],
    value: ETypeTradeFilter.ATC,
    isSelect: true,
  },
  {
    label: CONVERT_TYPE_TRADES_TO_VALUE[ETypeTradeFilter.TT],
    value: ETypeTradeFilter.TT,
    isSelect: true,
  },
  {
    label: CONVERT_TYPE_TRADES_TO_VALUE[ETypeTradeFilter.FOK],
    value: ETypeTradeFilter.FOK,
    isSelect: true,
  },
  {
    label: CONVERT_TYPE_TRADES_TO_VALUE[ETypeTradeFilter.FAK],
    value: ETypeTradeFilter.FAK,
    isSelect: true,
  },
  {
    label: CONVERT_TYPE_TRADES_TO_VALUE[ETypeTradeFilter.MTL],
    value: ETypeTradeFilter.MTL,
    isSelect: true,
  },
  {
    label: CONVERT_TYPE_TRADES_TO_VALUE[ETypeTradeFilter.PLO],
    value: ETypeTradeFilter.PLO,
    isSelect: true,
  },
];

export const statusConfirmTradeOption = [
  {
    label: CONVERT_STATUS_CONFIRM_TRADE_TO_LABEL[EStatusConfirmTrade.NOT_CONFIRM],
    value: EStatusConfirmTrade.NOT_CONFIRM,
    isSelect: true,
  },
  {
    label: CONVERT_STATUS_CONFIRM_TRADE_TO_LABEL[EStatusConfirmTrade.CONFIRMED],
    value: EStatusConfirmTrade.CONFIRMED,
    isSelect: true,
  },
];

export const statusTradesOpenTradesOption = [
  {
    label: 'KHỚP MỘT PHẦN',
    value: EStatusTradeFilter.PARTIAL_MATCH,
    isSelect: true,
  },
  {
    label: 'CHƯA KHỚP',
    value: EStatusTradeFilter.NOT_MATCH,
    isSelect: true,
  },
  {
    label: 'ĐÂ KÍCH HOẠT',
    value: EStatusTradeFilter.ACTIVATED,
    isSelect: true,
  },
  {
    label: 'CHƯA KÍCH HOẠT',
    value: EStatusTradeFilter.NOT_ACTIVATED,
    isSelect: true,
  },
  {
    label: 'HUỶ',
    value: EStatusTradeFilter.CANCELED,
    isSelect: true,
  },
  {
    label: 'TỪ CHỐI',
    value: EStatusTradeFilter.REJECTED,
    isSelect: true,
  },
];

export const accountNumberOptions = [
  {
    label: '069C-125485 - Phạm Thị Thu Trang',
    isSelect: true,
  },
  {
    label: '069C-586547 - Đặng Hoàng An Nhiên',
    isSelect: true,
  },
  {
    label: '069C-586547 - Ngô Thị Hằng',
    isSelect: true,
  },
  {
    label: '069C-918882 - Phạm Tiến Nam Phương',
    isSelect: true,
  },
  {
    label: '069C-883962 - Bùi Thị Hạnh',
    isSelect: true,
  },
  {
    label: '069C-891135 - Trần Văn Hậu',
    isSelect: true,
  },
  {
    label: '069C-316087 - Công ty TNHH Mica Group',
    isSelect: true,
  },
  {
    label: '069C-251114 - Công ty cổ phần địa ốc Ngọc Minh Huy',
    isSelect: true,
  },
  {
    label: '069C-388482 - Công ty cổ phần Money Max',
    isSelect: true,
  },
  {
    label: '069C-637085 - Công ty TNHH Tigon 68',
    isSelect: true,
  },
  {
    label: '069C-862656 - Công ty TNHH du lịch Cá Voi Xanh',
    isSelect: true,
  },
  {
    label: '069C-252138 - Công ty TNHH xây dựng và đầu tư Phú Khang',
    isSelect: true,
  },
];

export const tradersOptions = [
  {
    label: 'chienvm',
    isSelect: true,
  },
  {
    label: 'haln',
    isSelect: true,
  },
  {
    label: 'duongnt',
    isSelect: true,
  },
  {
    label: 'dungds',
    isSelect: true,
  },
  {
    label: 'hanhnt',
    isSelect: true,
  },
  {
    label: 'trangptt',
    isSelect: true,
  },
  {
    label: 'taypv',
    isSelect: true,
  },
  {
    label: 'canhnh',
    isSelect: true,
  },
  {
    label: 'doanhnh',
    isSelect: true,
  },
  {
    label: 'quangtd',
    isSelect: true,
  },
  {
    label: 'nghiadt',
    isSelect: true,
  },
  {
    label: 'datmt',
    isSelect: true,
  },
  {
    label: 'anhnht',
    isSelect: true,
  },
];

export const orderChannelOptions = [
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.SHBos],
    value: EOrderChanel.SHBos,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.DT],
    value: EOrderChanel.DT,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.WTS],
    value: EOrderChanel.WTS,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.HTS],
    value: EOrderChanel.HTS,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.SMS],
    value: EOrderChanel.SMS,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.MTSIE],
    value: EOrderChanel.MTSIE,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.MTSID],
    value: EOrderChanel.MTSID,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.MTSA],
    value: EOrderChanel.MTSA,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.IOS],
    value: EOrderChanel.IOS,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.Android],
    value: EOrderChanel.Android,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.WTSPro],
    value: EOrderChanel.WTSPro,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.APISHS],
    value: EOrderChanel.APISHS,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.OMS],
    value: EOrderChanel.OMS,
    isSelect: true,
  },
  {
    label: CONVERT_ORDER_CHANNEL[EOrderChanel.API],
    value: EOrderChanel.API,
    isSelect: true,
  },
];

export const conditionTradesOptions = [
  {
    label: CONVERT_SESSION_TO_LABEL[ESession.ATO],
    value: ESession.ATO,
    isSelect: true,
  },
  {
    label: CONVERT_SESSION_TO_LABEL[ESession.MORNING],
    value: ESession.MORNING,
    isSelect: true,
  },
  {
    label: CONVERT_SESSION_TO_LABEL[ESession.AFTERNOON],
    value: ESession.AFTERNOON,
    isSelect: true,
  },
  {
    label: CONVERT_SESSION_TO_LABEL[ESession.ATC],
    value: ESession.ATC,
    isSelect: true,
  },
];
