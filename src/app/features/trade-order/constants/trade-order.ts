import { IOption, IOptionCustom } from 'src/app/shared/models/dropdown-item.model';
import { EOrderType } from '../models/trade-order';

export enum EStatusTradesOrder {
  BUY = 2,
  SELL = 1,
}

export const CONVERT_TYPE_TRADES_ORDER_TO_LABLE: { [key: number]: string } = {
  [EStatusTradesOrder.SELL]: 'MES-185',
  [EStatusTradesOrder.BUY]: 'MES-186',
};

export enum EStatusTrades {
  NOT_MATCH,
  PARTIAL_MATCH,
  NOT_ACTIVATED,
  ACTIVATED,
  MATCHED,
  CANCELED,
  REJECTED,
}

export enum EStatusTradesString {
  NOT_MATCH = 'NOT_MATCH',
  PARTIAL_MATCH = 'PARTIAL_MATCH',
  NOT_ACTIVATED = 'NOT_ACTIVATED',
  ACTIVATED = 'ACTIVATED',
  MATCHED = 'FULL_MATCH',
  CANCELED = 'CANCELED',
  REJECTED = 'REJECTED',
}

export const CONVERT_TRADE_STATUS_TRADE_TO_ENUM: { [key: number]: string } = {
  [EStatusTrades.NOT_MATCH]: EStatusTradesString.NOT_MATCH,
  [EStatusTrades.PARTIAL_MATCH]: EStatusTradesString.PARTIAL_MATCH,
  [EStatusTrades.NOT_ACTIVATED]: EStatusTradesString.NOT_ACTIVATED,
  [EStatusTrades.ACTIVATED]: EStatusTradesString.ACTIVATED,
  [EStatusTrades.MATCHED]: EStatusTradesString.MATCHED,
  [EStatusTrades.CANCELED]: EStatusTradesString.CANCELED,
  [EStatusTrades.REJECTED]: EStatusTradesString.REJECTED,
};

// Quy định lệnh thường và lệnh điều kiện
// https://docs.google.com/spreadsheets/d/1IjIMTDOxjSp_-MGbYPfXrFmOZpsxIKl4RVAxgubIXqg/edit?gid=1001394968#gid=1001394968
export enum EOrdStatusNormalTradeType {
  RECEPTION = '0', // Tiếp nhận --> Chưa khớp
  TRANSFER = '1', // Chuyển --> Chưa khớp
  CONFIRM = '2', // Xác nhận Lệnh --> Chưa khớp
  CONFIRM_RECEPTION = '3', // Xác nhận tiếp nhận --> Chưa khớp / Huỷ
  MATCH_ALL = '4', // --> Khớp toàn bộ
  PARTIAL_MATCH = '5', // Khớp một phần
  SO_ACTIVATED = '6', // -> Bỏ qua k xét
  X = 'X', // -->  Từ chối
}

export const CONVERT_ORD_STATUS_NORMAL_TRADE_TYPE_TO_VALUE: { [key: string | number]: number } = {
  [EOrdStatusNormalTradeType.RECEPTION]: EStatusTrades.NOT_MATCH,
  [EOrdStatusNormalTradeType.TRANSFER]: EStatusTrades.NOT_MATCH,
  [EOrdStatusNormalTradeType.CONFIRM]: EStatusTrades.NOT_MATCH,
  [EOrdStatusNormalTradeType.CONFIRM_RECEPTION]: EStatusTrades.NOT_MATCH,
  [EOrdStatusNormalTradeType.MATCH_ALL]: EStatusTrades.MATCHED,
  [EOrdStatusNormalTradeType.PARTIAL_MATCH]: EStatusTrades.PARTIAL_MATCH,
  [EOrdStatusNormalTradeType.X]: EStatusTrades.REJECTED,
};

export enum EOrdStatusPreOrderTradeType {
  NOT_PROCESSED = '1', //  Chưa xử lý --> Chưa kích hoạt
  PROCESS = '2', // Xử lý --> Đã kích hoạt
  CANCEL = '3', // Hủy --> Hủy
  WAIT_CHANGE_STATUS = '4', // Đợi chuyển status --> bỏ qua k xét
  REJECT = '9', // Từ chối --> Từ chối
}

export const CONVERT_STATUS_PREORDER_TRADE_TO_VALUE: { [key: number]: number } = {
  [EOrdStatusPreOrderTradeType.NOT_PROCESSED]: EStatusTrades.NOT_ACTIVATED,
  [EOrdStatusPreOrderTradeType.PROCESS]: EStatusTrades.ACTIVATED,
  [EOrdStatusPreOrderTradeType.CANCEL]: EStatusTrades.CANCELED,
  [EOrdStatusPreOrderTradeType.REJECT]: EStatusTrades.REJECTED,
};

export enum EStatusTradeOrderManagement {
  NOT_CONFIRM = 1,
  CONFIRMED = 2,
}

export const CONVERT_STATUS_TRADES_ORDER_MANAGEMENT_TO_LABLE: { [key: number]: string } = {
  [EStatusTradeOrderManagement.NOT_CONFIRM]: 'MES-611',
  [EStatusTradeOrderManagement.CONFIRMED]: 'MES-612',
};

export enum ELabelStatusTrades {
  NOT_MATCH = 'CHƯA KHỚP',
  PARTIAL_MATCH = 'KHỚP MỘT PHẦN',
  NOT_ACTIVATED = 'CHƯA KÍCH HOẠT',
  ACTIVATED = 'KÍCH HOẠT',
  CANCELED = 'HUỶ',
  REJECTED = 'TỪ CHỐI',
}

export const CONVERT_TYPE_TRADES_STATUS_MATCH_TO_LABLE: { [key: number]: string } = {
  [EStatusTrades.NOT_MATCH]: 'MES-187',
  [EStatusTrades.PARTIAL_MATCH]: 'MES-188',
  [EStatusTrades.NOT_ACTIVATED]: 'MES-189',
  [EStatusTrades.ACTIVATED]: 'MES-199',
  [EStatusTrades.CANCELED]: 'MES-41',
  [EStatusTrades.REJECTED]: 'MES-607',
};

export const CONVERT_TYPE_TRADES_STATUS_MATCH_TO_VALUE: { [key: string]: number } = {
  [ELabelStatusTrades.NOT_MATCH]: EStatusTrades.NOT_MATCH,
  [ELabelStatusTrades.PARTIAL_MATCH]: EStatusTrades.PARTIAL_MATCH,
  [ELabelStatusTrades.NOT_ACTIVATED]: EStatusTrades.NOT_ACTIVATED,
  [ELabelStatusTrades.ACTIVATED]: EStatusTrades.ACTIVATED,
  [ELabelStatusTrades.CANCELED]: EStatusTrades.CANCELED,
  [ELabelStatusTrades.REJECTED]: EStatusTrades.REJECTED,
};

export enum ETypeOrderVolume {
  BUY,
  HOLDING,
}

export const CONVERT_TYPE_ORDER_VOLUME_TO_LABLE: { [key: number]: string } = {
  [ETypeOrderVolume.BUY]: 'MES-203',
  [ETypeOrderVolume.HOLDING]: 'MES-204',
};

export enum ETypeCodeTradeOrder {
  F03101 = 'F03101', // Thông báo kết quả khớp lệnh Mua
  F03102 = 'F03102', // Thông báo kết quả khớp lệnh Bán
  F03180 = 'F03180', // Khớp mua Phái sinh
  F03181 = 'F03181', // Khớp bán Phái sinh
  F03106 = 'F03106', // Hủy lệnh mua
  F03107 = 'F03107', // Hủy lệnh bán
  F03103 = 'F03103', // Từ chối lệnh mua đặt trước
  F03104 = 'F03104', // Từ chối lệnh bán đặt trước
}

export const CONVERT_STATUS_TRADED_ORDER_TO_VALUE: { [key: string]: number } = {
  [ETypeCodeTradeOrder.F03101]: 0,
  [ETypeCodeTradeOrder.F03102]: 1,
};

export const list_HOSE_Order = [
  {
    label: EOrderType.ATO,
    value: EOrderType.ATO,
  },
  {
    label: EOrderType.ATC,
    value: EOrderType.ATC,
  },
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
  {
    label: EOrderType.MP,
    value: EOrderType.MP,
  },
];

export const list_HNX_Order = [
  {
    label: EOrderType.ATC,
    value: EOrderType.ATC,
  },
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
  {
    label: EOrderType.MTL,
    value: EOrderType.MTL,
  },
  {
    label: EOrderType.FAK,
    value: EOrderType.FAK,
  },
  {
    label: EOrderType.FOK,
    value: EOrderType.FOK,
  },
  {
    label: EOrderType.PLO,
    value: EOrderType.PLO,
  },
];

export const list_UPCOM_Order = [
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
];

export enum ETagPageOrder {
  OPENTRADE,
  MATCHTRADE,
  PREORDER,
  MANAGEMENT,
}

/**
 * Lọc theo lệnh thường/lệnh đặt trước
 */
export enum ETradeOrderType {
  NORMAL = '0', // Lệnh mở, đã khớp
  PREORDER = '1', // Lệnh điều kiện
}

export enum EOrderChanel {
  SHBos = '00',
  DT = '01',
  WTS = '03',
  HTS = '04',
  SMS = '05',
  MTSIE = '06',
  MTSID = '07',
  MTSA = '08',
  IOS = '09',
  Android = '10',
  WTSPro = '11',
  APISHS = '12',
  OMS = '20',
  API = '30',
}

export const CONVERT_ORDER_CHANNEL: { [key: string]: string } = {
  [EOrderChanel.SHBos]: 'SHBos',
  [EOrderChanel.DT]: 'SHA',
  [EOrderChanel.WTS]: 'WTS',
  [EOrderChanel.HTS]: 'HTS',
  [EOrderChanel.SMS]: 'SMS',
  [EOrderChanel.MTSIE]: 'MTS-iPhone',
  [EOrderChanel.MTSID]: 'MTS-iPad',
  [EOrderChanel.MTSA]: 'MTS-Android',
  [EOrderChanel.IOS]: 'IOS-Pro',
  [EOrderChanel.Android]: 'Android-Pro',
  [EOrderChanel.WTSPro]: 'WTS-Pro',
  [EOrderChanel.APISHS]: 'API-SHS',
  [EOrderChanel.OMS]: 'OMS',
  [EOrderChanel.API]: 'API',
};
export enum EStatusTradeFilter {
  NOT_MATCH,
  PARTIAL_MATCH,
  NOT_ACTIVATED,
  ACTIVATED,
  MATCHED,
  CANCELED,
  REJECTED,
}

export enum ETypeTradeFilter {
  'LO' = '01',
  'MP' = '02',
  'ATO' = '03',
  'ATC' = '04',
  'TT' = '06',
  'FOK' = '07',
  'FAK' = '08',
  'MTL' = '09',
  'PLO' = '15',
}

export const CONVERT_TYPE_TRADES_TO_VALUE: { [key: string]: string } = {
  [ETypeTradeFilter.LO]: 'LO',
  [ETypeTradeFilter.MP]: 'MP',
  [ETypeTradeFilter.ATO]: 'ATO',
  [ETypeTradeFilter.ATC]: 'ATC',
  [ETypeTradeFilter.TT]: 'THOẢ THUẬN',
  [ETypeTradeFilter.FOK]: 'FOK',
  [ETypeTradeFilter.FAK]: 'FAK',
  [ETypeTradeFilter.MTL]: 'MTL',
  [ETypeTradeFilter.PLO]: 'PLO',
};

export const CONVERT_BUY_SELL_VALUE: { [key: number]: string } = {
  0: '2', // Mua
  1: '1', // Bán
};

export enum ESellType {
  SELL = '1',
  BUY = '2',
  ALL = '%',
}

export enum ESession {
  ATO = 'ATO',
  MORNING = 'MORNING',
  AFTERNOON = 'AFTERNOON',
  ATC = 'ATC',
}

export const CONVERT_SESSION_TO_LABEL: { [key: string]: string } = {
  [ESession.ATO]: 'Đặt trước phiên ATO',
  [ESession.MORNING]: 'Đặt trước phiên sáng',
  [ESession.AFTERNOON]: 'Đặt trước phiên chiều',
  [ESession.ATC]: 'Đặt trước phiên ATC',
};

export enum EStatusConfirmTrade {
  NOT_CONFIRM,
  CONFIRMED,
}

export const CONVERT_STATUS_CONFIRM_TRADE_TO_LABEL: { [key: number]: string } = {
  [EStatusConfirmTrade.NOT_CONFIRM]: 'CHƯA XÁC NHẬN',
  [EStatusConfirmTrade.CONFIRMED]: 'XÁC NHẬN',
};

export const TOTAL_ITEM_IN_LIST = 30;

export const ALL_ORDERS_GROUP = [
  {
    label: EOrderType.ATO,
    value: EOrderType.ATO,
  },
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
  {
    label: EOrderType.MP,
    value: EOrderType.MP,
  },
  {
    label: EOrderType.ATC,
    value: EOrderType.ATC,
  },
  {
    label: EOrderType.MTL,
    value: EOrderType.MTL,
  },
  {
    label: EOrderType.FOK,
    value: EOrderType.FOK,
  },
  {
    label: EOrderType.FAK,
    value: EOrderType.FAK,
  },
  {
    label: EOrderType.PLO,
    value: EOrderType.PLO,
  },
];

export const ORDERS_GROUP_1 = [
  {
    label: EOrderType.ATO,
    value: EOrderType.ATO,
  },
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
];

export const ORDERS_GROUP_2 = [
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
  {
    label: EOrderType.MP,
    value: EOrderType.MP,
  },
];

export const ORDERS_GROUP_3 = [
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
  {
    label: EOrderType.MTL,
    value: EOrderType.MTL,
  },
  {
    label: EOrderType.FOK,
    value: EOrderType.FOK,
  },
  {
    label: EOrderType.FAK,
    value: EOrderType.FAK,
  },
];

export const ORDERS_GROUP_4 = [
  {
    label: EOrderType.ATC,
    value: EOrderType.ATC,
  },
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
];

export const ORDERS_GROUP_5 = [
  {
    label: EOrderType.ATC,
    value: EOrderType.ATC,
  },
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
  {
    label: EOrderType.PLO,
    value: EOrderType.PLO,
  },
];

export const ORDERS_GROUP_6 = [
  {
    label: EOrderType.ATO,
    value: EOrderType.ATO,
  },
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
  {
    label: EOrderType.MP,
    value: EOrderType.MP,
  },
  {
    label: EOrderType.ATC,
    value: EOrderType.ATC,
  },
];

export const ORDERS_GROUP_7 = [
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
  {
    label: EOrderType.MTL,
    value: EOrderType.MTL,
  },
  {
    label: EOrderType.FOK,
    value: EOrderType.FOK,
  },
  {
    label: EOrderType.FAK,
    value: EOrderType.FAK,
  },
  {
    label: EOrderType.ATC,
    value: EOrderType.ATC,
  },
  {
    label: EOrderType.PLO,
    value: EOrderType.PLO,
  },
];

export const ORDERS_GROUP_8 = [
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
];

export const ORDERS_GROUP_9 = [
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
  {
    label: EOrderType.MP,
    value: EOrderType.MP,
  },
  {
    label: EOrderType.ATC,
    value: EOrderType.ATC,
  },
];

export const ORDERS_GROUP_10 = [
  {
    label: EOrderType.ATO,
    value: EOrderType.ATO,
  },
  {
    label: EOrderType.LO,
    value: EOrderType.LO,
  },
  {
    label: EOrderType.MTL,
    value: EOrderType.MTL,
  },
  {
    label: EOrderType.ATC,
    value: EOrderType.ATC,
  },
];

export const OPENED_ORDERS_SESSIONS = [
  {
    start: '09:00',
    end: '09:15',
    hoseOrders: ORDERS_GROUP_10,
    reservedHoseOrders: ORDERS_GROUP_10,
    hnxOrders: ORDERS_GROUP_7,
    reservedHnxOrders: ORDERS_GROUP_7,
    upcomOrders: ORDERS_GROUP_8,
    reservedUpcomOrders: ORDERS_GROUP_8,
  },
  {
    start: '09:15',
    end: '11:30',
    hoseOrders: ORDERS_GROUP_10,
    reservedHoseOrders: ORDERS_GROUP_10,
    hnxOrders: ORDERS_GROUP_7,
    reservedHnxOrders: ORDERS_GROUP_7,
    upcomOrders: ORDERS_GROUP_8,
    reservedUpcomOrders: ORDERS_GROUP_8,
  },
  {
    start: '11:30',
    end: '13:00',
    hoseOrders: [],
    reservedHoseOrders: ORDERS_GROUP_10,
    hnxOrders: ORDERS_GROUP_7,
    reservedHnxOrders: ORDERS_GROUP_7,
    upcomOrders: [],
    reservedUpcomOrders: ORDERS_GROUP_8,
  },
  {
    start: '13:00',
    end: '14:30',
    hoseOrders: ORDERS_GROUP_10,
    reservedHoseOrders: ORDERS_GROUP_10,
    hnxOrders: ORDERS_GROUP_7,
    reservedHnxOrders: ORDERS_GROUP_7,
    upcomOrders: ORDERS_GROUP_8,
    reservedUpcomOrders: ORDERS_GROUP_8,
  },
  {
    start: '14:30',
    end: '14:45',
    hoseOrders: ORDERS_GROUP_10,
    reservedHoseOrders: ORDERS_GROUP_10,
    hnxOrders: ORDERS_GROUP_7,
    reservedHnxOrders: ORDERS_GROUP_7,
    upcomOrders: ORDERS_GROUP_8,
    reservedUpcomOrders: ORDERS_GROUP_8,
  },
  {
    start: '14:45',
    end: '15:00',
    hoseOrders: [],
    reservedHoseOrders: ORDERS_GROUP_10,
    hnxOrders: ORDERS_GROUP_7,
    reservedHnxOrders: ORDERS_GROUP_7,
    upcomOrders: ORDERS_GROUP_8,
    reservedUpcomOrders: ORDERS_GROUP_8,
  },

  // default
  {
    start: '00:00',
    end: '24:00',
    hoseOrders: [],
    reservedHoseOrders: ORDERS_GROUP_10,
    hnxOrders: ORDERS_GROUP_7,
    reservedHnxOrders: ORDERS_GROUP_7,
    upcomOrders: [],
    reservedUpcomOrders: ORDERS_GROUP_8,
  },
];
