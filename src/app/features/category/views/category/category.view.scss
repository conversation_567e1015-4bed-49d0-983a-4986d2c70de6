.category-view-containers {
  height: 100%;
  display: flex;
  flex-direction: column;

  .category-header-cls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px 0px 28px;

    .header-txt {
      text-transform: uppercase;
    }

    .header-btn {
      padding: 6px 10px;
      display: flex;
      align-items: center;
      gap: 8px;
      border-radius: 8px;
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 1px 2px 0px rgba(0, 0, 0, 0.14);
      cursor: pointer;
    }
  }

  .sub-menu-cls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    border-bottom: 1px solid #f1f2f6;
    padding: 5px 16px 10px 28px;
    padding-bottom: 0;

    .search-cls {
      display: flex;
      align-items: center;
      position: relative;
      margin-bottom: 6px;

      .search-icon {
        position: absolute;
        top: 50%;
        left: 10px;
        transform: translateY(-50%);
      }
    }

    .input-cls {
      padding: 6px 10px 6px 34px;
      min-width: 200px;
    }
  }

  mat-tab-group {
    min-width: 0;

    ::ng-deep {
      .mat-mdc-tab-labels {
        gap: 16px;

        .mdc-tab {
          padding: 0;
        }
      }
    }
  }

  .menu-category-cls {
    display: flex;
    align-items: center;
    gap: 16px;

    .box-selection {
      letter-spacing: 0;
      display: flex;
      align-items: center;
      padding: 12px 0;
      gap: 8px;
      border-bottom: 3px solid transparent;
      color: #33343e;

      .mat-icon-cls {
        height: 20px;
        width: 20px;
      }

      &.isSelect {
        // border-bottom: 3px solid #f58220;
        color: #f58220;

        .office-fee-information,
        .position {
          ::ng-deep {
            path {
              fill: #f58220;
            }
          }
        }
      }
    }
  }
}

:host {
  display: flex;
  height: 100%;
  flex-direction: column;
}
