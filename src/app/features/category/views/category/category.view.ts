import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { NavigationStart, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { debounceTime, filter, takeUntil, tap } from 'rxjs';
import { DestroyService, DialogService } from 'src/app/core/services';

@Component({
  selector: 'app-category',
  templateUrl: './category.view.html',
  styleUrl: './category.view.scss',
})
export class CategoryView {
  menuCategories = [
    {
      router: 'office-fee-information',
      name: 'MES-584',
      nameIcon: 'icon:office-fee-info',
    },
    {
      router: 'position',
      name: 'MES-483',
      nameIcon: 'icon:user-circle-add',
    },
  ];

  activeTab = 0;

  searchControl = new FormControl();
  private searchSubscription: any;
  private firstRouteChange = true;

  /**
   * Constructor
   * @param _destroy
   * @param router
   * @param dialogService
   * @param store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly router: Router,
    private readonly dialogService: DialogService,
    private readonly store: Store
  ) {
    this.updateActiveTab(router.url);
  }

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.listenSearchValueChange();
    this.resetSearchControlOnRouteChange();
  }

  /**
   * listenSearchValueChange
   */
  listenSearchValueChange(): void {
    this.searchSubscription = this.searchControl.valueChanges
      .pipe(
        debounceTime(300),
        // tap((value) => {
        //   this.store.dispatch(search({ data: value }));
        // }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * resetSearchControlOnRouteChange
   */
  resetSearchControlOnRouteChange(): void {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationStart),
        tap(() => {
          if (this.firstRouteChange) {
            // Temporarily disable the valueChanges subscription
            if (this.searchSubscription) {
              this.searchSubscription.unsubscribe();
            }

            // Reset search control and update the store
            this.searchControl.setValue('');

            // Re-enable the valueChanges subscription
            this.listenSearchValueChange();

            // Set the flag to false after the first reset
            this.firstRouteChange = false;
          }
        }),
        takeUntil(this._destroy)
      )
      .subscribe((event: any) => {
        this.updateActiveTab(event.url);
      });
  }

  /**
   * Update active tab index based on current URL
   * @param url - Current URL
   */
  updateActiveTab(url: string) {
    this.activeTab = this.menuCategories.findIndex((item) => url.includes(item.router));
  }

  /**
   * OnTabChange
   */
  onTabChange() {
    this.searchControl.patchValue('');
  }
}
