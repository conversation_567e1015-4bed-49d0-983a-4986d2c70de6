<div class="category-view-containers">
  <div class="category-header-cls">
    <div class="header-txt typo-heading-8">{{'MES-583' | uppercase | translate}}</div>
  </div>
  <div class="sub-menu-cls">
    <mat-tab-group animationDuration="200ms" [selectedIndex]="activeTab">
      <mat-tab *ngFor="let item of menuCategories" isActive="item.isActive">
        <ng-template mat-tab-label>
          <div class="menu-category-cls">
            <a class="box-selection" mat-list-item routerLinkActive="isSelect" [routerLink]="item.router">
              <mat-icon
                class="mat-icon-cls"
                aria-hidden="false"
                aria-label="icon"
                [svgIcon]="item.nameIcon"
                [ngClass]="item.router"
              ></mat-icon>
              <div class="typo-body-6">{{item.name | translate}}</div>
            </a>
          </div>
        </ng-template>
      </mat-tab>
    </mat-tab-group>

    <div class="search-cls">
      <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
      <input
        class="input-cls input-style-common typo-body-12"
        type="text"
        [placeholder]="'MES-14' | translate"
        [formControl]="searchControl"
      />
    </div>
  </div>
  <div class="router-container-cls"><router-outlet></router-outlet></div>
</div>
