import { Component, OnInit, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { DestroyService, LoadingService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';

@Component({
  selector: 'app-position',
  templateUrl: './position.container.html',
  styleUrl: './position.container.scss',
})
export class PositionContainer extends BaseTableComponent<any> implements OnInit {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  isEdit = false;

  fakeData: any[] = [
    {
      positionCode: 'GTP',
      positionName: 'Giám đốc tư vấn đầu tư & phát triển kinh doanh',
      abbreviatedPositionName: 'Giám đốc TVĐT & PTKD',
    },
    {
      positionCode: 'GT',
      positionName: 'Giám đốc tư vấn đầu tư ',
      abbreviatedPositionName: 'Giám đốc TVĐT',
    },
    {
      positionCode: 'TTP',
      positionName: 'Trưởng phòng tư vấn đầu tư & phát triển kinh doanh',
      abbreviatedPositionName: 'Trưởng phòng TVĐT & PTKD',
    },
    {
      positionCode: 'TT',
      positionName: 'Trưởng phòng tư vấn đầu tư',
      abbreviatedPositionName: 'Trưởng phòng TVĐT',
    },
    {
      positionCode: 'T',
      positionName: 'Chuyên viên tư vấn đầu tư',
      abbreviatedPositionName: 'Chuyên viên TVĐT',
    },
    {
      positionCode: 'Đ',
      positionName: 'Đối tác phát triển kinh doanh',
      abbreviatedPositionName: 'Đối tác PTKD',
    },
    {
      positionCode: 'Intern',
      positionName: 'Thực tập sinh',
      abbreviatedPositionName: 'Thực tập sinh',
    },
  ];

  tags: string[] = [];
  /**
   * Constructor
   * @param store Store
   * @param _destroy DestroyService
   * @param popperService PopoverService
   * @param loadingService LoadingService
   */
  constructor(
    private readonly store: Store,
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    private readonly loadingService: LoadingService
  ) {
    super();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.export]);
  }

  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.tags = [`${this.data.length} chức vụ`];
    this.initialData = structuredClone(this.data);

    this.columnConfigs = [
      {
        name: 'Mã chức vụ',
        minWidth: 200,
        width: 250,
        tag: 'positionCode',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        isEdit: false,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên chức vụ',
        minWidth: 400,
        width: 750,
        tag: 'positionName',
        isDisplay: true,
        isEdit: false,
        resizable: true,
      },
      {
        name: 'Tên chức vụ viết tắt',
        minWidth: 400,
        width: 750,
        tag: 'abbreviatedPositionName',
        isDisplay: true,
        isEdit: false,
        resizable: true,
      },
    ];
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    if (tag === 'edit') {
      this.toggleEditMode();
    }
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.export]);
  }
}
