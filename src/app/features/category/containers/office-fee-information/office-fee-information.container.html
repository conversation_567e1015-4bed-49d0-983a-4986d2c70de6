<div class="office-fee-information-container">
  <div class="header-office-fee-info">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-584' | translate}}</div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event);"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
    >
    </sha-grid>
  </div>
</div>

<ng-template #inputProportion let-templateInfo="templateInfo" let-element="element" let-tag="tag" let-column="column">
  <app-input-proportion-custom
    [unit]="column.componentConfig.unit"
    [element]="element"
    [tag]="tag"
    [isEdit]="onEditMode"
    [value]="templateInfo"
    [column]="column"
    (dateChangeEvent)="changeDateEvent($event)"
    (unFocusElement)="elementSelectFunc($event)"
  >
  </app-input-proportion-custom>
</ng-template>
