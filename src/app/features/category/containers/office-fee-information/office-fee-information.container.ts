import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { CELL_TYPE } from '@shared/models';
import { finalize, Observable, takeUntil, tap } from 'rxjs';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import { DestroyService, LoadingService, MessageService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { getOfficeFeeInformation } from '../../store/category.action';
import { selectOfficeFeeInformation$ } from '../../store/category.selector';
import { CategoryService } from '../../service/category.service';

@Component({
  selector: 'app-office-fee-information',
  templateUrl: './office-fee-information.container.html',
  styleUrl: './office-fee-information.container.scss',
})
export class OfficeFeeInformationContainer extends BaseTableComponent<any> implements OnInit, ComponentCanDeactivate {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('inputProportion', { static: true }) inputProportion: TemplateRef<any> | null = null;

  isEdit = false;

  fakeData: any[] = [
    {
      feeRateType: 'Tỷ lệ phí trả sở',
      coefficient: 0.00027,
    },
  ];

  /**
   * Constructor
   * @param store Store
   * @param _destroy DestroyService
   * @param popperService PopoverService
   * @param loadingService LoadingService
   */
  constructor(
    private store: Store,
    private _destroy: DestroyService,
    private popoverService: PopoverService,
    private loadingService: LoadingService,
    private messageService: MessageService,
    private categoryService: CategoryService
  ) {
    super();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.export]);
    this.store.dispatch(getOfficeFeeInformation());
  }

  ngOnInit(): void {
    // this.data = deepClone(this.fakeData);
    // this.initialData = this.data;
    const feeRateType = 'Tỷ lệ phí trả sở';

    this.store
      .select(selectOfficeFeeInformation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((officeFee) => {
        if (!officeFee) return;
        this.data = Array.isArray(officeFee) ? structuredClone(officeFee) : [structuredClone(officeFee)];
        this.data.forEach((item) => {
          item.feeRateType = feeRateType;
          if (item.officeFeeRatio) {
            item.officeFeeRatio = item.officeFeeRatio * 100;
          }
        });
        this.initialData = this.data;
      });

    const template = this.inputProportion;

    this.columnConfigs = [
      {
        name: 'Loại tỷ lệ phí',
        minWidth: 200,
        width: 334,
        tag: 'feeRateType',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        isEdit: false,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Hệ số',
        minWidth: 1200,
        width: 1395,
        tag: 'officeFeeRatio',
        isDisplay: true,
        isEdit: true,
        typeValue: CELL_TYPE.TEXT,
        resizable: true,
        componentConfig: {
          unit: '% ',
        },
        cellTemplate: template,
      },
    ];
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'edit':
        this.toggleEditMode();
        break;
      case 'export':
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate((dataSelected) => {
          const officeFeeRatioPayload = dataSelected[0].officeFeeRatio / 100;
          this.categoryService
            .editOfficeFeeRatio(officeFeeRatioPayload)
            .pipe(
              tap(() => this.loadingService.show()),
              takeUntil(this._destroy),
              finalize(() => {
                this.loadingService.hide();
              })
            )
            .subscribe({
              next: (res) => {
                this.messageService.success(res.message);
              },
              error: (err) => {
                this.messageService.error(err.error.message);
              },
            });
        });
        break;
      default:
        break;
    }
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.save, ActionButton.cancel, ActionButton.export]);
  }
}
