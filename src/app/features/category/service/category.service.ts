import { Injectable } from '@angular/core';
import { ApiResponse } from 'src/app/core/models/api-response';
import { ApiService } from 'src/app/core/services';
import { IOfficeFeeInformationResponse } from '../model/category';
import { map } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CategoryService {
  url = 'v1/system';

  /**
   *constructor
   * @param apiService apiService
   */
  constructor(private readonly apiService: ApiService) {}

  getOfficeFeeInformation() {
    return this.apiService
      .get<ApiResponse<IOfficeFeeInformationResponse[]>>(`${this.url}/user/office-fee`)
      .pipe(map((res) => res.data));
  }

  editOfficeFeeRatio(officeFeeRatio: number) {
    const payload = { officeFeeRatio };
    return this.apiService.put<ApiResponse<boolean>>(`${this.url}/user/office-fee`, payload);
  }
}
