import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CategoryView } from './views/category/category.view';
import { OfficeFeeInformationContainer } from './containers/office-fee-information/office-fee-information.container';
import { PositionContainer } from './containers/position/position.container';
import { UnsaveChangeGuard } from 'src/app/core/guards/unsaved-changes.guard';

const routes: Routes = [
  {
    path: '',
    component: CategoryView,
    children: [
      {
        path: '',
        redirectTo: 'office-fee-information',
        pathMatch: 'full',
      },
      {
        path: 'office-fee-information',
        component: OfficeFeeInformationContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'position',
        component: PositionContainer,
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CategoryRoutingModule {}
