import { createReducer, on } from "@ngrx/store";
import { ICategoryState } from "../model/category";
import * as CategoryAction from "./category.action";

export const initialCategoryState: ICategoryState = {
    officeFeeInformation: [],
}

export const categoryReducers = createReducer<ICategoryState>(
    initialCategoryState,

    on(
        CategoryAction.getOfficeFeeInformationSuccess,
        (state, action): ICategoryState => ({
            ...state,
            officeFeeInformation: action.data,
        })
    )
)