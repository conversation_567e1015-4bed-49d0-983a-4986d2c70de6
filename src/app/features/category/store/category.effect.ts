import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { LoadingService, MessageService } from 'src/app/core/services';
import { CategoryService } from '../service/category.service';
import { getOfficeFeeInformation, getOfficeFeeInformationSuccess } from './category.action';
import { map, switchMap } from 'rxjs';

@Injectable()
export class CategoryEffects {
  constructor(
    private readonly actions$: Actions,
    private readonly loadingService: LoadingService,
    private readonly store: Store,
    private readonly messageService: MessageService,
    private readonly categoryService: CategoryService
  ) {}

  getOfficeFeeInformation$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getOfficeFeeInformation),
      switchMap(() => {
        return this.categoryService.getOfficeFeeInformation();
      }),
      map((res) => {
        return getOfficeFeeInformationSuccess({ data: res });
      })
    );
  });
}
