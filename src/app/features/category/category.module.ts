import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CategoryView } from './views/category/category.view';
import { TranslateModule } from '@ngx-translate/core';
import { CategoryRoutingModule } from './category-routing.module';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { ActionBtnComponent } from 'src/app/shared/components/action-btn/action-btn.component';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { FormControlComponent } from 'src/app/shared/components/form-control/form-control.component';
import { CheckBoxForTableComponent } from 'src/app/shared/components/check-box-for-table/check-box-for-table.component';
import { InputComponent } from 'src/app/shared/components/input/input.component';
import { OfficeFeeInformationContainer } from './containers/office-fee-information/office-fee-information.container';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule } from '@angular/material/dialog';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PositionContainer } from './containers/position/position.container';
import { InputProportionComponent } from 'src/app/shared/components/input-custom-for-table/input-proportion/input-proportion.component';
import { InputProportionCustomComponent } from 'src/app/shared/components/input-custom-for-table/input-proportion-custom/input-proportion-custom.component';
import { UnsaveChangeGuard } from 'src/app/core/guards/unsaved-changes.guard';
import { StoreModule } from '@ngrx/store';
import { CATEGORY_STATE_NAME } from './store/category.selector';
import { CategoryEffects } from './store/category.effect';
import { EffectsModule } from '@ngrx/effects';
import { categoryReducers } from './store/category.reducer';

const SHARED = [
  GridComponent,
  ActionBtnComponent,
  DraggableListComponent,
  FormControlComponent,
  CheckBoxForTableComponent,
  InputComponent,
  CheckBoxForTableComponent,
  InputProportionComponent,
  InputProportionCustomComponent,
];

const VIEWS = [CategoryView];

const CONTAINERS = [OfficeFeeInformationContainer, PositionContainer];

@NgModule({
  declarations: [...VIEWS, ...CONTAINERS],
  imports: [
    ...SHARED,
    CommonModule,
    TranslateModule,
    CategoryRoutingModule,

    // Material Module
    MatTableModule,
    MatTabsModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    FormsModule,
    MatDividerModule,
    MatDatepickerModule,
    MatMenuModule,
    MatDialogModule,
    OverlayModule,
    MatIconModule,
    MatCheckboxModule,
    MatTooltipModule,

    StoreModule.forFeature(CATEGORY_STATE_NAME, categoryReducers),
    EffectsModule.forFeature([CategoryEffects]),
  ],
  providers: [UnsaveChangeGuard],
})
export class CategoryModule { }
