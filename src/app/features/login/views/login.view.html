<div class="login-view-cls">
  <div class="logo">
    <img src="./assets/images/shs_logo_vn_big-slogan_blue_rgb.png" alt="" style="height: 200px" />
    <!-- <mat-icon aria-hidden="false" aria-label="menuIcon icon" [svgIcon]="'icon:logo'" class="logo-icon"></mat-icon> -->
  </div>
  <div class="header-cls typo-heading-12">{{'MES-536' | translate}}</div>
  <ng-container [ngSwitch]="currentView">
    <ng-container *ngSwitchCase="ELoginView.LOGIN">
      <div class="title-cls">{{'MES-537' | translate}}</div>
      <div class="login-body">
        <form action="" [formGroup]="loginForm" class="form-custom-login">
          <app-form-control class="login-custom">
            <div class="box-group">
              <!-- Tên đăng nhập -->
              <div class="typo-body-6">{{'MES-538' | translate}}</div>
              <mat-form-field>
                <input
                  matInput
                  [placeholder]="'MES-539'| translate"
                  formControlName="broker"
                  class="typo-body-9 font-text-cls"
                />
              </mat-form-field>
            </div>
          </app-form-control>

          <!-- Mật khẩu -->
          <app-form-control class="login-custom">
            <div class="box-group">
              <div class="typo-body-6">{{'MES-490' | translate}}</div>
              <mat-form-field>
                <input
                  matInput
                  [type]="hidePassword?'password':'text'"
                  [placeholder]="'MES-490'| translate"
                  formControlName="password"
                  class="typo-body-9 font-text-cls"
                />

                <button
                  class="button-cls-custom"
                  mat-icon-button
                  matSuffix
                  type="button"
                  (click)="hidePassword = !hidePassword"
                  [attr.aria-label]="'hidePassword password'"
                  [attr.aria-pressed]="hidePassword"
                  (click)="$event.stopPropagation()"
                >
                  <mat-icon style="cursor: pointer" *ngIf="hidePassword" svgIcon="icon:eye"></mat-icon>
                  <mat-icon style="cursor: pointer" *ngIf="!hidePassword" svgIcon="icon:eye-slash"></mat-icon>
                </button>
              </mat-form-field>
            </div>
          </app-form-control>
          <div class="forgot-pass" (click)="navigateToForgotPassword()">{{'MES-540' | translate}}</div>

          <button [disabled]="loginForm.invalid" (click)="onLogin()" class="btn btn-cls primary typo-button-3">
            {{'MES-541' | translate}}
          </button>
        </form>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ELoginView.FORGOT">
      <div class="title-cls">{{"MES-542"|translate}}</div>
      <div class="login-body">
        <form action="" [formGroup]="emailForm" class="form-custom-login">
          <app-form-control class="login-custom">
            <div class="box-group">
              <!-- Địa chỉ Email -->
              <div class="typo-body-6">{{'MES-543' | translate}}</div>
              <mat-form-field>
                <input
                  matInput
                  placeholder="<EMAIL>"
                  formControlName="email"
                  class="typo-body-9 font-text-cls"
                />
              </mat-form-field>
            </div>
          </app-form-control>
          <button
            [disabled]="emailForm.invalid"
            (click)="navigateToSentEmail()"
            class="btn btn-cls primary typo-button-3"
          >
            {{'MES-89' | translate}}
          </button>
        </form>
      </div>
      <div class="footer" (click)="navigateToLogin()">{{"MES-544" | translate}}</div>
    </ng-container>

    <ng-container *ngSwitchCase="ELoginView.EMAIL">
      <div class="title-cls">{{'MES-545' | translate}} {{emailAddress }}</div>
      <div class="login-body">
        <button (click)="navigateToLogin()" class="btn btn-cls primary typo-button-3">{{'MES-89' | translate}}</button>
      </div>
      <div class="footer" (click)="navigateToForgotPassword()">{{"MES-544" | translate}}</div>
    </ng-container>

    <ng-container *ngSwitchCase="ELoginView.SYSTEM">
      <div class="title-cls typo-body-1">{{'MES-537' | translate}}</div>
      <div class="login-body">
        <button
          data-testid="system-login-btn"
          (click)="navigateToLogin()"
          class="btn btn-cls primary typo-button-2 system"
        >
          {{'MES-555' | translate}}
        </button>
      </div>
    </ng-container>
  </ng-container>
</div>
