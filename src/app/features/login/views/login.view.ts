import { Component, OnInit } from '@angular/core';
import { ELoginView } from '../constants/login';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { AuthService } from 'src/app/core/services';
import { jwtDecode } from 'jwt-decode';
import { IToken, IUser } from 'src/app/core/models/user';
import { Store } from '@ngrx/store';
import { loginSuccess } from 'src/app/stores/auth/auth.actions';

/**
 * Login View
 */
@Component({
  selector: 'app-login-view',
  templateUrl: './login.view.html',
  styleUrls: ['./login.view.scss'],
})
export class LoginView implements OnInit {
  hidePassword = true;
  currentView = ELoginView.SYSTEM;
  ELoginView = ELoginView;

  loginForm: FormGroup;
  emailForm: FormGroup;

  emailAddress = '';
  /**
   * @param fb
   * @param router
   * @param auth
   * @param keyCloak
   * @param store
   */
  constructor(
    private readonly fb: FormBuilder,
    private readonly router: Router,
    private readonly auth: AuthService,
    private readonly keyCloak: KeycloakService,
    private readonly store: Store
  ) {
    this.loginForm = this.fb.group({
      broker: [null, [Validators.required]],
      password: [null, [Validators.required]],
    });

    this.emailForm = this.fb.group({
      email: [null, [Validators.required, Validators.email]],
    });
  }

  /**
   * NgOnint
   */
  ngOnInit() {
    this.init();
  }

  async init() {
    const isLogin = this.keyCloak.isLoggedIn();
    if (isLogin) {
      const { token } = this.keyCloak.getKeycloakInstance();
      if (token) {
        const decodeToke: IToken = this.decodeToken(token);
        this.auth.setLoginSuccessValue(decodeToke, true);
        this.keyCloak.loadUserProfile().then((profile) => {
          const user: IUser = {
            id: profile.id ?? '',
            username: profile.username ?? '',
            firstName: profile.firstName ?? '',
            lastName: profile.lastName ?? '',
            email: profile.email ?? '',
            emailVerified: profile.emailVerified ?? false,
          };
          this.store.dispatch(loginSuccess({ token: decodeToke, isRememberMe: true, user }));
        });
      }
    }
  }

  /**
   * NavigateToForgotPassword
   */
  navigateToForgotPassword() {
    this.currentView = this.ELoginView.FORGOT;
  }

  /**
   * NavigateToLogin
   */
  navigateToLogin() {
    this.auth.login();

  }

  /**
   * NavigateToSentEmail
   */
  navigateToSentEmail() {
    this.emailAddress = this.emailForm.get('email')?.value;
    this.currentView = this.ELoginView.EMAIL;
  }

  /**
   * Login
   */
  onLogin() {
    window.location.href = '/assets/asset-info';
  }

  /**
   * DecodeToken
   * @param token
   * @returns {any} token
   */
  private decodeToken(token: string) {
    const decoded = jwtDecode(token);

    return {
      expiresIn: decoded.exp ?? 0,
      idToken: token,
    };
  }
}
