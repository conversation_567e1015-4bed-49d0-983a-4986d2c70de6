.login-view-cls {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  // margin-top: 170px;
  .logo {
    .logo-icon {
      height: 200px !important;
      width: 199.387px !important;
      min-width: 200px;
      min-height: 199.387px;
    }
  }

  .header-cls {
    text-transform: uppercase;
    margin-top: 49px;
    color: var(--color--brand--500);
  }

  .title-cls {
    color: var(--color--text--default);
    text-align: center;
    margin-top: 19px;
  }

  .login-body {
    margin-top: 56px;
    width: 388px;

    .form-custom-login {
      display: flex;
      gap: 24px;
      flex-direction: column;
      align-items: flex-end;

      .box-group {
        display: flex;
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
        position: relative;
        mat-form-field {
          ::ng-deep {
            .mdc-text-field--invalid {
              .mat-mdc-form-field-flex {
                border: 1px solid var(--color--danger--600);
                border-radius: 8px;
              }
            }
            .mat-mdc-form-field-infix {
              padding-top: 12px;
              padding-bottom: 12px;
            }
            .mat-mdc-form-field-icon-suffix {
              padding: 0 5px;
            }
          }
          .font-text-cls {
            font-size: 12px;
          }
        }

        .button-cls-custom {
          border: 0px;
          background-color: transparent;
        }
      }
      .forgot-pass {
        color: var(--color--brand--500);
        cursor: pointer;
      }
    }
    .btn-cls {
      width: 100%;
      padding: 12px 24px;
      border: none;
      font-size: 16px;

      &.system {
        height: 44px;
      }
    }
  }
  .footer {
    padding-top: 20px;
    color: var(--color--brand--500);
    cursor: pointer;
  }
}

:host {
  display: block;
  height: 100%;
}
