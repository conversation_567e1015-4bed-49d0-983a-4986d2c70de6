import { NgModule } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { LoginView } from './views/login.view';
import { LoginRoutingModule } from './login-routing.module';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { FormControlComponent } from '../../shared/components/form-control/form-control.component';

const VIEWS = [LoginView];

/**
 * Login Module
 */
@NgModule({
  declarations: [...VIEWS],
  imports: [
    MatIconModule,
    LoginRoutingModule,
    TranslateModule,
    CommonModule,
    ReactiveFormsModule,
    MatInputModule,
    FormControlComponent,
  ],
})
export class LoginModule {}
