import { ChangeDetectorRef, Component, Inject, OnInit, Optional, QueryList, ViewChildren } from '@angular/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, provideNativeDateAdapter } from '@angular/material/core';
import { take, tap } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { DatePickerNavigationFullDateComponent } from 'src/app/shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { IOptionList } from 'src/app/shared/models/dropdown-item.model';
import { IItemStoke, IRangeFilter } from '../../models/recommendations';
import { AbstractControl, FormBuilder, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MY_DATE_FORMAT } from 'src/app/shared/constants/date-picker';
import { Store } from '@ngrx/store';
import {
  selectAllAccountNumberListByBrokerView$,
  selectCustomerGroupList$,
  selectAllStockList$,
} from 'src/app/stores/shared/shared.selectors';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';
/**
 * ClosedRecommendationsFilterComponent
 */
@Component({
  selector: 'app-open-recommendations-filter',
  templateUrl: './open-recommendations-filter.component.html',
  styleUrl: './open-recommendations-filter.component.scss',
  providers: [
    provideNativeDateAdapter(),
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
  ],
})
export class OpenRecommendationsFilterComponent implements OnInit {
  @ViewChildren(VirtualScrollListComponent) virtualScroll!: QueryList<VirtualScrollListComponent>;

  headerCalendar = DatePickerNavigationFullDateComponent;

  openRcmForm!: FormGroup;

  readonly customerKey = 'accountNumber';

  customers: IAllAccountNumber[] = [];

  readonly customerGroupKey = 'value';

  customerGroups: IOptionList[] = [];

  readonly stokeKey = 'value';

  readonly stokeId = 'stoke';

  listOfStocks: IItemStoke[] = [];

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  isDisableStockApply = false;

  isDisableApply = false;

  isDisableCustomerGroupApply = false;

  /**
   * Constructor
   * @param _destroy
   * @param popoverService
   * @param data
   * @param dialogRef
   * @param popoverRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<OpenRecommendationsFilterComponent>,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly cdf: ChangeDetectorRef,
    private readonly fb: FormBuilder,
    private readonly store: Store
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.initForm();

    this.patchValueForm();
  }

  private patchValueForm() {
    const { date, rangeDateHold, rangePotential, rangeNotRecord } = this.data.filterOptions;

    this.openRcmForm.patchValue({
      startDateHold: rangeDateHold.start,
      endDateHold: rangeDateHold.end,
      startNotRecord: rangeNotRecord.start,
      endNotRecord: rangeNotRecord.end,
      startPotential: rangePotential.start,
      endPotential: rangePotential.end,
      dateStart: date.start,
      dateEnd: date.end,
    });
  }

  private loadCustomersOp() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(
        take(1),
        tap((customersOp) => {
          this.customers = customersOp;
        })
      )
      .subscribe();
  }

  private loadCustomerGroupList() {
    this.store
      .select(selectCustomerGroupList$)
      .pipe(
        take(1),
        tap((groupsOp) => {
          this.customerGroups = groupsOp
            .filter((t) => t.isShow)
            .map((g) => ({
              label: `${g.groupCode} : ${g.name}`,
              value: g.name,
              groupCode: g.groupCode,
              id: g.id,
            }));
        })
      )
      .subscribe();
  }

  private loadStokeListOp() {
    this.store
      .select(selectAllStockList$)
      .pipe(take(1))
      .subscribe((allStocksOp) => {
        this.listOfStocks = allStocksOp.map((t) => ({
          value: t.id,
          stoke: t.stock,
        }));
      });
  }
  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.loadCustomersOp();
    this.loadCustomerGroupList();
    this.loadStokeListOp();
  }

  /**
   * initForm
   */
  private initForm() {
    this.openRcmForm = this.fb.group(
      {
        dateStart: [null],
        dateEnd: [null],
        startDateHold: [null],
        endDateHold: [null],
        startPotential: [null],
        endPotential: [null],
        startNotRecord: [null],
        endNotRecord: [null],
      },

      {
        validator: this.customValidate,
      }
    );
  }

  customValidate(group: FormGroup) {
    const dateStart = group.get('dateStart');
    const dateEnd = group.get('dateEnd');
    const startPotential = group.get('startPotential');
    const endPotential = group.get('endPotential');
    const startNotRecord = group.get('startNotRecord');
    const endNotRecord = group.get('endNotRecord');
    const startDateHold = group.get('startDateHold');
    const endDateHold = group.get('endDateHold');

    const validFromToValueOp = (startControlOp: any, endControlOp: any) => {
      if (startControlOp && endControlOp) {
        const start = startControlOp.value;
        const end = endControlOp.value;
        if (+end < +start && end !== null && end !== '') {
          return endControlOp.setErrors({ validFromTo: true });
        } else {
          endControlOp?.setErrors(null);
        }
      }
    };

    const dateValidatorOp = (control: AbstractControl | null): void => {
      if (!control) return;

      const value = control.value?._d ?? control.value;
      const currentErrors = control.errors || {};

      if (!value || value === '') {
        const { invalidDate, ...remainingErrors } = currentErrors;
        control.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
        return;
      }

      const parsedDate = typeof value === 'string' ? new Date(value) : value;
      const isValidDate = parsedDate instanceof Date && !isNaN(parsedDate.getTime());

      if (!isValidDate) {
        control.setErrors({ ...currentErrors, invalidDate: true });
      } else {
        const { invalidDate, ...remainingErrors } = currentErrors;
        control.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
      }
    };

    const dateRangeValidatorOp = (startControlOp: AbstractControl | null, endControlOp: AbstractControl | null): void => {
      if (!startControlOp || !endControlOp) return;

      const startErrors = startControlOp.errors || {};
      const endErrors = endControlOp.errors || {};

      if (endErrors['matDatepickerMin']) {
        const { matDatepickerMax, ...remainingErrors } = startErrors;
        startControlOp.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
      }
    };

    validFromToValueOp(startDateHold, endDateHold);
    validFromToValueOp(startPotential, endPotential);
    validFromToValueOp(startNotRecord, endNotRecord);

    dateValidatorOp(dateStart);
    dateValidatorOp(dateEnd);

    dateRangeValidatorOp(dateStart, dateEnd);
  }

  validateStockFilterValue(invalid: boolean) {
    this.isDisableStockApply = invalid;
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  validateCustomerGroupFilterValue(invalid: boolean) {
    this.isDisableCustomerGroupApply = invalid;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    if (this.isDisableApply || this.isDisableCustomerGroupApply || this.isDisableStockApply) return;

    const {
      startDateHold,
      endDateHold,
      startNotRecord,
      endNotRecord,
      startPotential,
      endPotential,
      dateStart,
      dateEnd,
    } = this.openRcmForm.value;
    const getVirtualScrollByIdOp = (id: string) => {
      return this.virtualScroll.find((component) => component.id === id);
    };
    const customer = ((getVirtualScrollByIdOp(this.customerKey)?.getChecked() as IAllAccountNumber[]) ?? []).map(
      (c) => c.accountNumber
    );

    const stockCodes = ((getVirtualScrollByIdOp(this.stokeId)?.getChecked() as IItemStoke[]) ?? []).map((c) => c.value);

    const customerGroup = ((getVirtualScrollByIdOp(this.customerGroupKey)?.getChecked() as IOptionList[]) ?? []).map(
      (c) => c.value as string
    );
    const rangeDateHold = {
      start: startDateHold,
      end: endDateHold,
    };
    const rangePotential = {
      start: startPotential,
      end: endPotential,
    };
    const rangeNotRecord = {
      start: startNotRecord,
      end: endNotRecord,
    };

    const date = {
      start: dateStart?._d ?? dateStart,
      end: dateEnd?._d ?? dateEnd,
    };

    const isFilter = this.checkStatusFilter(
      stockCodes,
      customer,
      customerGroup,
      rangeDateHold,
      rangePotential,
      rangeNotRecord,
      date
    );

    this.dialogRef.close({
      type: 'save',
      optionFilter: {
        stockCodes,
        customer,
        customerGroup,
        rangeNotRecord,
        rangeDateHold,
        date,
        rangePotential,
        isFilter,
      },
    });
  }

  /**
   * CheckStatusFilter
   * @param rangeDateHold
   * @param rangePotential
   * @param date
   * @returns {boolean} true / false
   */
  checkStatusFilter(
    stockCodes: string[],
    customer: string[],
    customerGroup: string[],
    rangeDateHold: IRangeFilter,
    rangePotential: IRangeFilter,
    rangeNotRecord: IRangeFilter,
    date: IRangeFilter
  ) {
    const isStokeFilter = stockCodes.length === 0;
    const isCustomerFilter = customer.length === 0;
    const isCustomerGroupFilter = customerGroup.length === 0;
    return (
      !isStokeFilter ||
      !isCustomerFilter ||
      !isCustomerGroupFilter ||
      this.checkHasValueInObject(rangeDateHold) ||
      this.checkHasValueInObject(rangePotential) ||
      this.checkHasValueInObject(rangeNotRecord) ||
      this.checkHasValueInObject(date)
    );
  }

  /**
   * DefaultFilter
   */
  defaultFilterOp() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObject(dataOp: any) {
    return !!dataOp && ((dataOp.start != null && dataOp.start !== '') || (dataOp.end != null && dataOp.end !== ''));
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customerOp: IAllAccountNumber): string {
    if (!customerOp) return '';
    return `${customerOp.accountNumber} - ${customerOp.customerName}`;
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerGroupFilter(groupOp: IOptionList): string {
    if (!groupOp) return '';
    return `${groupOp.label}`;
  }

  displayStokeFilter(stokeOp: IItemStoke) {
    if (!stokeOp) return '';
    return `${stokeOp.value} - ${stokeOp.stoke}`;
  }
}
