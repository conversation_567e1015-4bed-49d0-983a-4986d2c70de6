<div class="opened-re-fi-cls closed-recommendations-filter-wrap">
  <div class="opened-re-fi-cls closed-rcm-header">
    <div class="opened-re-fi-cls title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="opened-re-fi-cls close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="opened-re-fi-cls box-body-container">
    <form [formGroup]="openRcmForm" class="opened-re-fi-cls closed-rcm-content">
      <!-- Mã CK -->
      <div class="opened-re-fi-cls title typo-body-15">{{ 'MES-157' | translate }}</div>
      <div class="opened-re-fi-cls searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="listOfStocks?.length; else noResultsStoke"
          [id]="stokeId"
          [key]="stokeKey"
          [items]="listOfStocks"
          [selectedKeys]="data.filterOptions.stockCodes ?? []"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          class="opened-re-fi-cls"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-670'"
          [warningMessage]="'MES-668'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-14' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['value', 'stoke']"
          [displayFn]="displayStokeFilter"
          (invalidSelection)="validateStockFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultsStoke>
          <div class="opened-re-fi-cls typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- Thời gian nắm giữ -->
      <div class="opened-re-fi-cls title typo-body-15">{{ 'MES-208' | translate }} (ngày)</div>
      <div class="opened-re-fi-cls holding-period-content">
        <div class="opened-re-fi-cls content-from">
          <div class="opened-re-fi-cls from-label typo-body-11">{{ 'MES-209' | translate }}</div>
          <app-form-control>
            <input
              formControlName="startDateHold"
              [mask]="'0*'"
              [allowNegativeNumbers]="true"
              class="opened-re-fi-cls holding-period-input typo-field-5"
              [placeholder]="0"
            />
          </app-form-control>
        </div>

        <div class="opened-re-fi-cls content-to">
          <div class="opened-re-fi-cls to-label typo-body-11">{{ 'MES-210' | translate }}</div>
          <app-form-control>
            <input
              formControlName="endDateHold"
              [mask]="'0*'"
              [allowNegativeNumbers]="true"
              class="opened-re-fi-cls holding-period-input typo-field-5"
              [placeholder]="'∞'"
            />
          </app-form-control>
        </div>
      </div>

      <!-- Ngày mở khuyến nghị -->
      <div class="opened-re-fi-cls title typo-body-15">{{ 'MES-335' | translate }}</div>
      <div class="opened-re-fi-cls rcm-day-content">
        <div class="opened-re-fi-cls content-from">
          <div class="opened-re-fi-cls from-label typo-body-11">{{ 'MES-209' | translate }} ngày</div>
          <div class="opened-re-fi-cls input-wrapper">
            <app-form-control>
              <input
                type="text"
                class="opened-re-fi-cls calendar-input typo-body-12"
                matInput
                [matDatepicker]="dateFrom"
                [placeholder]="'DD/MM/YYYY'"
                formControlName="dateStart"
                dateInput
                [max]="
                  openRcmForm.get('dateEnd')?.value?._d
                    ? openRcmForm.get('dateEnd')?.value?._d
                    : openRcmForm.get('dateEnd')?.value
                    ? openRcmForm.get('dateEnd')?.value
                    : null
                "
              />
            </app-form-control>
            <img (click)="dateFrom.open()" src="./assets/icons/calendar.svg" alt="" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              panelClass="opened-re-fi-cls calendar-cls"
              #dateFrom
            ></mat-datepicker>
          </div>
        </div>

        <div class="opened-re-fi-cls content-to">
          <div class="opened-re-fi-cls to-label typo-body-11">{{ 'MES-210' | translate }} ngày</div>
          <div class="opened-re-fi-cls input-wrapper">
            <app-form-control>
              <input
                type="text"
                class="opened-re-fi-cls calendar-input typo-body-12"
                matInput
                [matDatepicker]="dateTo"
                formControlName="dateEnd"
                [placeholder]="'DD/MM/YYYY'"
                dateInput
                [min]="
                  openRcmForm.get('dateStart')?.value?._d
                    ? openRcmForm.get('dateStart')?.value?._d
                    : openRcmForm.get('dateStart')?.value
                    ? openRcmForm.get('dateStart')?.value
                    : null
                "
              />
            </app-form-control>
            <img (click)="dateTo.open()" src="./assets/icons/calendar.svg" alt="" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              panelClass="opened-re-fi-cls calendar-cls"
              #dateTo
            ></mat-datepicker>
          </div>
        </div>
      </div>

      <!-- +/- tiềm năng -->
      <div class="opened-re-fi-cls title typo-body-15">{{ 'MES-214' | translate }} (%)</div>
      <div class="opened-re-fi-cls holding-period-content">
        <div class="opened-re-fi-cls content-from">
          <div class="opened-re-fi-cls from-label typo-body-11">{{ 'MES-209' | translate }}</div>
          <app-form-control>
            <input
              class="opened-re-fi-cls holding-period-input typo-field-5"
              [mask]="'separator'"
              [allowNegativeNumbers]="true"
              formControlName="startPotential"
              [placeholder]="'-∞'"
            />
          </app-form-control>
        </div>

        <div class="opened-re-fi-cls content-to">
          <div class="opened-re-fi-cls to-label typo-body-11">{{ 'MES-210' | translate }}</div>
          <app-form-control>
            <input
              class="opened-re-fi-cls holding-period-input typo-field-5"
              [mask]="'separator'"
              [allowNegativeNumbers]="true"
              formControlName="endPotential"
              [placeholder]="'∞'"
            />
          </app-form-control>
        </div>
      </div>

      <!-- +/- chưa ghi nhận -->
      <div class="opened-re-fi-cls title typo-body-15">{{ 'MES-218' | translate }} (%)</div>
      <div class="opened-re-fi-cls holding-period-content">
        <div class="opened-re-fi-cls content-from">
          <div class="opened-re-fi-cls from-label typo-body-11">{{ 'MES-209' | translate }}</div>
          <app-form-control>
            <input
              class="opened-re-fi-cls holding-period-input typo-field-5"
              [mask]="'separator'"
              [allowNegativeNumbers]="true"
              formControlName="startNotRecord"
              [placeholder]="'-∞'"
            />
          </app-form-control>
        </div>

        <div class="opened-re-fi-cls content-to">
          <div class="opened-re-fi-cls to-label typo-body-11">{{ 'MES-210' | translate }}</div>
          <app-form-control>
            <input
              class="opened-re-fi-cls holding-period-input typo-field-5"
              [mask]="'separator'"
              [allowNegativeNumbers]="true"
              formControlName="endNotRecord"
              [placeholder]="'∞'"
            />
          </app-form-control>
        </div>
      </div>
    </form>

    <div class="opened-re-fi-cls right-box">
      <!-- KN tới Khách hàng -->
      <div class="opened-re-fi-cls title typo-body-15">{{ 'MES-160' | translate }}</div>
      <div class="opened-re-fi-cls searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResultCustomer"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers"
          class="opened-re-fi-cls"
          [selectedKeys]="data.filterOptions.customer ?? []"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [messageSelectionComplete]="'MES-669'"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [warningMessage]="'MES-668'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-295' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          [displayFn]="displayCustomerFilter"
          (invalidSelection)="validateFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultCustomer>
          <div class="opened-re-fi-cls typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- KN tới Nhóm khách hàng -->
      <div class="opened-re-fi-cls title typo-body-15">{{ 'MES-161' | translate }}</div>
      <div class="opened-re-fi-cls searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customerGroups?.length; else noResultCustomerGroup"
          [id]="customerGroupKey"
          [key]="customerGroupKey"
          [items]="customerGroups"
          [selectedKeys]="data.filterOptions.customerGroup ?? []"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-669'"
          [warningMessage]="'MES-668'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-14' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['groupCode', 'value']"
          [displayFn]="displayCustomerGroupFilter"
          (invalidSelection)="validateCustomerGroupFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultCustomerGroup>
          <div class="opened-re-fi-cls typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>
    </div>
  </div>

  <div class="opened-re-fi-cls footer-filter">
    <div (click)="defaultFilterOp()" class="opened-re-fi-cls btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <button
      [disabled]="
        openRcmForm.invalid ||
        openRcmForm.get('dateStart')?.invalid ||
        openRcmForm.get('dateEnd')?.invalid ||
        isDisableApply ||
        isDisableCustomerGroupApply ||
        isDisableStockApply
      "
      (click)="applyFilter()"
      class="opened-re-fi-cls btn apply typo-button-3"
    >
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
