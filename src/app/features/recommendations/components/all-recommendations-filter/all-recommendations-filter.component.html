<div class="all-recommendations-filter-wrap">
  <div class="all-rcm-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <form [formGroup]="allRcmForm" class="all-rcm-content">
    <!-- LEFT -->
    <div class="content-left">
      <!-- Mã CK -->
      <div class="title-left typo-body-15">{{ 'MES-157' | translate }}</div>
      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="listOfStocks?.length; else noResultsStoke"
          [id]="stokeId"
          [key]="stokeKey"
          [items]="listOfStocks"
          [selectedKeys]="data.filterOptions.stockCodes ?? []"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-670'"
          [warningMessage]="'MES-668'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-14' | translate"
          [noResultsMessage]="'MES-585' | translate"
          class="all-re-fi"
          [searchKeys]="['value', 'stoke']"
          [displayFn]="displayStokeFilter"
          (invalidSelection)="validateStockFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultsStoke>
          <div class="typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- Trạng thái khuyến nghị -->
      <div class="title-left typo-body-15">{{ 'MES-158' | translate }}</div>
      <div class="option-list-status-rcm-cls">
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isFollowing = $event.checked" [checked]="isFollowing" class="checkbox-cls">{{
            'MES-270' | translate
          }}</mat-checkbox>
        </div>
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isOpen = $event.checked" [checked]="isOpen" class="checkbox-cls">{{
            'MES-271' | translate
          }}</mat-checkbox>
        </div>
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isClose = $event.checked" [checked]="isClose" class="checkbox-cls">{{
            'MES-272' | translate
          }}</mat-checkbox>
        </div>
      </div>

      <!-- Thời gian khuyến nghị -->
      <div class="holding-period">
        <div class="title-left typo-body-15">{{ 'MES-591' | translate }} (ngày)</div>
        <div class="holding-period-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <app-form-control>
              <input
                formControlName="startDateHold"
                [mask]="'0*'"
                class="holding-period-input typo-field-5"
                [placeholder]="0"
              />
            </app-form-control>
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <app-form-control>
              <input
                formControlName="endDateHold"
                [mask]="'0*'"
                class="holding-period-input typo-field-5"
                [placeholder]="'∞'"
              />
            </app-form-control>
          </div>
        </div>
      </div>

      <!-- Ngày tạo khuyến nghị -->
      <div class="rcm-day">
        <div class="title-right typo-body-15">{{ 'MES-582' | translate }}</div>
        <div class="rcm-day-content">
          <!-- Từ ngày -->
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }} ngày</div>
            <div class="input-wrapper">
              <app-form-control>
                <input
                  type="text"
                  class="calendar-input typo-body-12"
                  matInput
                  [matDatepicker]="dateFrom"
                  [placeholder]="'DD/MM/YYYY'"
                  formControlName="dateStart"
                  dateInput
                  [max]="
                    allRcmForm.get('dateEnd')?.value?._d
                      ? allRcmForm.get('dateEnd')?.value?._d
                      : allRcmForm.get('dateEnd')?.value
                      ? allRcmForm.get('dateEnd')?.value
                      : null
                  "
                />
              </app-form-control>

              <img src="./assets/icons/calendar.svg" alt="" (click)="dateFrom.open()" />
              <mat-datepicker
                [calendarHeaderComponent]="headerCalendar"
                panelClass="calendar-cls"
                #dateFrom
              ></mat-datepicker>
            </div>
          </div>

          <!-- Tới ngày -->
          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }} ngày</div>
            <div class="input-wrapper">
              <app-form-control>
                <input
                  type="text"
                  class="calendar-input typo-body-12"
                  matInput
                  [matDatepicker]="dateTo"
                  formControlName="dateEnd"
                  [placeholder]="'DD/MM/YYYY'"
                  dateInput
                  [min]="
                    allRcmForm.get('dateStart')?.value?._d
                      ? allRcmForm.get('dateStart')?.value?._d
                      : allRcmForm.get('dateStart')?.value
                      ? allRcmForm.get('dateStart')?.value
                      : null
                  "
                />
              </app-form-control>

              <img src="./assets/icons/calendar.svg" alt="" (click)="dateTo.open()" />
              <mat-datepicker
                [calendarHeaderComponent]="headerCalendar"
                panelClass="calendar-cls"
                #dateTo
              ></mat-datepicker>
            </div>
          </div>
        </div>
      </div>

      <!-- +/- tiềm năng -->
      <div class="holding-period">
        <div class="title-right typo-body-15">{{ 'MES-214' | translate }} (%)</div>
        <div class="holding-period-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <app-form-control>
              <input
                class="holding-period-input typo-field-5"
                [mask]="'separator'"
                [allowNegativeNumbers]="true"
                formControlName="startPotential"
                [placeholder]="'-∞'"
              />
            </app-form-control>
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <app-form-control>
              <input
                class="holding-period-input typo-field-5"
                [mask]="'separator'"
                [allowNegativeNumbers]="true"
                formControlName="endPotential"
                [placeholder]="'∞'"
              />
            </app-form-control>
          </div>
        </div>
      </div>

      <!-- Chưa ghi nhận -->
      <div class="holding-period">
        <div class="title-right typo-body-15">{{ 'MES-218' | translate }} (%)</div>
        <div class="holding-period-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <app-form-control>
              <input
                class="holding-period-input typo-field-5"
                [mask]="'separator'"
                [allowNegativeNumbers]="true"
                formControlName="startNotRecord"
                [placeholder]="'-∞'"
              />
            </app-form-control>
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <app-form-control>
              <input
                class="holding-period-input typo-field-5"
                [mask]="'separator'"
                [allowNegativeNumbers]="true"
                formControlName="endNotRecord"
                [placeholder]="'∞'"
              />
            </app-form-control>
          </div>
        </div>
      </div>

      <!-- Đã ghi nhận -->
      <div class="holding-period">
        <div class="title-right typo-body-15">{{ 'MES-346' | translate }} (%)</div>
        <div class="holding-period-content">
          <div class="content-from">
            <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
            <app-form-control>
              <input
                class="holding-period-input typo-field-5"
                [mask]="'separator'"
                [allowNegativeNumbers]="true"
                formControlName="startRecord"
                [placeholder]="'-∞'"
              />
            </app-form-control>
          </div>

          <div class="content-to">
            <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
            <app-form-control>
              <input
                class="holding-period-input typo-field-5"
                [mask]="'separator'"
                [allowNegativeNumbers]="true"
                formControlName="endRecord"
                [placeholder]="'∞'"
              />
            </app-form-control>
          </div>
        </div>
      </div>
    </div>

    <!-- RIGHT -->
    <div class="content-right">
      <!-- KN tới Khách hàng -->
      <div class="title-right typo-body-15">{{ 'MES-160' | translate }}</div>
      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResultCustomer"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers"
          class="all-re-fi"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-669'"
          [warningMessage]="'MES-668'"
          [displayFn]="displayCustomerFilter"
          [selectedKeys]="data.filterOptions.customer ?? []"
          [warningMessage]="'MES-668'"
          [searchPlaceholder]="'MES-295' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [selectAllLabel]="'MES-58' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          (invalidSelection)="validateFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultCustomer>
          <div class="typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- KN tới Nhóm khách hàng -->
      <div class="title-right typo-body-15">{{ 'MES-161' | translate }}</div>
      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customerGroups?.length; else noResultCustomerGroup"
          [id]="customerGroupId"
          [key]="customerGroupKey"
          [items]="customerGroups"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-669'"
          [warningMessage]="'MES-668'"
          [selectedKeys]="data.filterOptions.customerGroup ?? []"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-14' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['groupCode', 'value']"
          [displayFn]="displayCustomerGroupFilter"
          (invalidSelection)="validateCustomerGroupFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultCustomerGroup>
          <div class="typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>
    </div>
  </form>

  <div class="footer-filter">
    <div (click)="defaultFilter()" class="btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <button
      [disabled]="
        allRcmForm.invalid ||
        allRcmForm.get('dateStart')?.invalid ||
        allRcmForm.get('dateEnd')?.invalid ||
        isDisableApply ||
        isDisableCustomerGroupApply ||
        isDisableStockApply
      "
      (click)="applyFilter()"
      class="btn apply typo-button-3"
    >
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
