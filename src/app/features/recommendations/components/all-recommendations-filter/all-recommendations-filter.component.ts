import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
  Optional,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup } from '@angular/forms';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, provideNativeDateAdapter } from '@angular/material/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { take, tap } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { DatePickerNavigationFullDateComponent } from 'src/app/shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { MY_DATE_FORMAT } from 'src/app/shared/constants/date-picker';
import { IOptionList } from 'src/app/shared/models/dropdown-item.model';
import { IItemStoke, IRangeFilter } from '../../models/recommendations';
import { initialConfig } from 'src/app/shared/directives/mask/ngx-mask.config';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllStockList$,
  selectCustomerGroupList$,
} from 'src/app/stores/shared/shared.selectors';
import { Store } from '@ngrx/store';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';

interface IDataFilterPopup {
  filterOptions: any;
  listOfStocks: any;
}

/**
 * AllRecommendationsFilterComponent
 */
@Component({
  selector: 'app-all-recommendations-filter',
  templateUrl: './all-recommendations-filter.component.html',
  styleUrl: './all-recommendations-filter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    provideNativeDateAdapter(),
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
  ],
})
export class AllRecommendationsFilterComponent implements OnInit {
  @ViewChild(CdkVirtualScrollViewport) viewport!: CdkVirtualScrollViewport;

  @ViewChildren(VirtualScrollListComponent) virtualScroll!: QueryList<VirtualScrollListComponent>;

  specialCharacters = [...initialConfig.specialCharacters];

  allRcmForm!: FormGroup;

  isFollowing = false;

  isOpen = false;

  isClose = false;

  headerCalendar = DatePickerNavigationFullDateComponent;

  readonly customerKey = 'accountNumber';

  customers: IAllAccountNumber[] = [];

  readonly customerGroupKey = 'value';
  readonly customerGroupId = 'customerGroup';

  customerGroups: IOptionList[] = [];

  readonly stokeKey = 'value';
  readonly stokeId = 'stoke';

  listOfStocks: IItemStoke[] = [];

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  isDisableStockApply = false;

  isDisableApply = false;

  isDisableCustomerGroupApply = false;

  /**
   * Constructor
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param data
   * @param dialogRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: IDataFilterPopup,
    public dialogRef: MatDialogRef<AllRecommendationsFilterComponent>,
    private readonly cdf: ChangeDetectorRef,
    private readonly fb: FormBuilder,
    private readonly store: Store
  ) {
    this.initForm();

    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.patchValueToForm();
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.loadStokeList();
    this.loadCustomerListAll();
    this.loadCustomerGroupListAll();
  }

  /**
   * initForm
   */
  private initForm() {
    this.allRcmForm = this.fb.group(
      {
        startDateHold: [null],
        endDateHold: [null],
        startPotential: [null],
        endPotential: [null],
        startNotRecord: [null],
        endNotRecord: [null],
        startRecord: [null],
        endRecord: [null],
        dateStart: [null],
        dateEnd: [null],
      },
      {
        validator: this.customValidate,
      }
    );
  }

  private loadCustomerListAll() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(
        take(1),
        tap((customersAll) => {
          this.customers = customersAll;
        })
      )
      .subscribe();
  }

  private loadCustomerGroupListAll() {
    this.store
      .select(selectCustomerGroupList$)
      .pipe(
        take(1),
        tap((groupsAll) => {
          this.customerGroups = groupsAll
            .filter((t) => t.isShow)
            .map((g) => ({
              label: `${g.groupCode} : ${g.name}`,
              value: g.name,
              groupCode: g.groupCode,
              id: g.id,
            }));
        })
      )
      .subscribe();
  }

  private loadStokeList() {
    this.store
      .select(selectAllStockList$)
      .pipe(take(1))
      .subscribe((allStocks) => {
        this.listOfStocks = allStocks.map((t) => ({
          value: t.id,
          stoke: t.stock,
        }));
      });
  }

  private patchValueToForm() {
    const { status, date, rangeDateHold, rangeNotRecord, rangePotential, rangeRecord } = this.data.filterOptions;

    status.forEach((t: any) => {
      if (t === 0) {
        this.isClose = true;
      } else if (t === 2) {
        this.isOpen = true;
      } else if (t === 1) {
        this.isFollowing = true;
      }
    });

    this.allRcmForm.patchValue({
      startDateHold: rangeDateHold.start,
      endDateHold: rangeDateHold.end,
      startNotRecord: rangeNotRecord.start,
      endNotRecord: rangeNotRecord.end,
      startPotential: rangePotential.start,
      endPotential: rangePotential.end,
      startRecord: rangeRecord.start,
      endRecord: rangeRecord.end,
      dateStart: date.start,
      dateEnd: date.end,
    });
  }

  /**
   * customValidate
   * @param group allRcmForm
   */
  customValidate(group: FormGroup) {
    const startDateHold = group.get('startDateHold');
    const endDateHold = group.get('endDateHold');
    const startPotential = group.get('startPotential');
    const endPotential = group.get('endPotential');
    const startNotRecord = group.get('startNotRecord');
    const endNotRecord = group.get('endNotRecord');
    const startRecord = group.get('startRecord');
    const endRecord = group.get('endRecord');
    const dateStart = group.get('dateStart');
    const dateEnd = group.get('dateEnd');

    const validFromToValueAll = (startControlAll: any, endControlAll: any) => {
      if (startControlAll && endControlAll) {
        const start = startControlAll.value;
        const end = endControlAll.value;
        if (+end < +start && end !== null && end !== '') {
          return endControlAll.setErrors({ validFromTo: true });
        } else {
          endControlAll?.setErrors(null);
        }
      }
    };

    const dateValidatorAll = (controlAll: AbstractControl | null): void => {
      if (!controlAll) return;

      const value = controlAll.value?._d ?? controlAll.value;
      const currentErrors = controlAll.errors || {};

      if (!value || value === '') {
        const { invalidDate, ...remainingErrors } = currentErrors;
        controlAll.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
        return;
      }

      const parsedDate = typeof value === 'string' ? new Date(value) : value;
      const isValidDate = parsedDate instanceof Date && !isNaN(parsedDate.getTime());

      if (!isValidDate) {
        controlAll.setErrors({ ...currentErrors, invalidDate: true });
      } else {
        const { invalidDate, ...remainingErrors } = currentErrors;
        controlAll.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
      }
    };

    const dateRangeValidatorAll = (startControlAll: AbstractControl | null, endControlAll: AbstractControl | null): void => {
      if (!startControlAll || !endControlAll) return;

      const startErrors = startControlAll.errors || {};
      const endErrors = endControlAll.errors || {};

      if (endErrors['matDatepickerMin']) {
        const { matDatepickerMax, ...remainingErrors } = startErrors;
        startControlAll.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
      }
    };

    validFromToValueAll(startDateHold, endDateHold);
    validFromToValueAll(startPotential, endPotential);
    validFromToValueAll(startNotRecord, endNotRecord);
    validFromToValueAll(startRecord, endRecord);

    dateValidatorAll(dateStart);
    dateValidatorAll(dateEnd);

    dateRangeValidatorAll(dateStart, dateEnd);
  }

  sortByIsSelect(stockList: IOptionList[]): IOptionList[] {
    return stockList.sort((a, b) => {
      return Number(b.isSelect) - Number(a.isSelect);
    });
  }

  validateStockFilterValue(invalid: boolean) {
    this.isDisableStockApply = invalid;
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  validateCustomerGroupFilterValue(invalid: boolean) {
    this.isDisableCustomerGroupApply = invalid;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    if (this.isDisableApply || this.isDisableCustomerGroupApply || this.isDisableStockApply) return;

    const status: number[] = [];

    if (this.isFollowing && this.isOpen && this.isClose) {
      status.push(0, 1, 2);
    } else if (this.isClose && this.isFollowing) {
      status.push(0, 1);
    } else if (this.isClose && this.isOpen) {
      status.push(0, 2);
    } else if (this.isFollowing && this.isOpen) {
      status.push(1, 2);
    } else if (this.isClose) {
      status.push(0);
    } else if (this.isFollowing) {
      status.push(1);
    } else if (this.isOpen) {
      status.push(2);
    }

    const {
      startDateHold,
      endDateHold,
      startNotRecord,
      endNotRecord,
      startPotential,
      endPotential,
      startRecord,
      endRecord,
      dateStart,
      dateEnd,
    } = this.allRcmForm.value;

    const getVirtualScrollByIdAll = (id: string) => {
      return this.virtualScroll.find((component) => component.id === id);
    };
    const customer = ((getVirtualScrollByIdAll(this.customerKey)?.getChecked() as IAllAccountNumber[]) ?? []).map(
      (c) => c.accountNumber
    );

    const stockCodes = ((getVirtualScrollByIdAll(this.stokeId)?.getChecked() as IItemStoke[]) ?? []).map((c) => c.value);

    const customerGroup = ((getVirtualScrollByIdAll(this.customerGroupId)?.getChecked() as IOptionList[]) ?? []).map(
      (c) => c.value as string
    );

    const rangeDateHold = {
      start: startDateHold,
      end: endDateHold,
    };
    const rangePotential = {
      start: startPotential,
      end: endPotential,
    };
    const rangeNotRecord = {
      start: startNotRecord,
      end: endNotRecord,
    };
    const rangeRecord = {
      start: startRecord,
      end: endRecord,
    };

    const date = {
      start: dateStart?._d ?? dateStart,
      end: dateEnd?._d ?? dateEnd,
    };

    const dataFilter = {
      stockCodes,
      customer,
      customerGroup,
      status,
      rangeDateHold,
      rangePotential,
      rangeNotRecord,
      rangeRecord,
      date,
    };

    const isFilter = this.checkStatusFilter(dataFilter);
    this.dialogRef.close({
      type: 'save',
      optionFilter: {
        status,
        stockCodes,
        customer,
        customerGroup,
        rangeDateHold,
        date,
        rangePotential,
        rangeNotRecord,
        rangeRecord,
        isFilter,
      },
    });
  }

  /**
   * CheckStatusFilter
   * @param status
   * @param rangeDateHold
   * @param rangePotential
   * @param rangeNotRecord
   * @param rangeRecord
   * @param date
   * @returns {boolean} true / false
   */
  checkStatusFilter(dataFilter: {
    stockCodes: string[];
    customer: string[];
    customerGroup: string[];
    status: number[];
    rangeDateHold: IRangeFilter;
    rangePotential: IRangeFilter;
    rangeNotRecord: IRangeFilter;
    rangeRecord: IRangeFilter;
    date: IRangeFilter;
  }) {
    const {
      stockCodes,
      customer,
      customerGroup,
      status,
      rangeDateHold,
      rangePotential,
      rangeNotRecord,
      rangeRecord,
      date,
    } = dataFilter;
    const isStokeFilter = stockCodes.length === 0;
    const isCustomerFilter = customer.length === 0;
    const isCustomerGroupFilter = customerGroup.length === 0;
    return (
      !isStokeFilter ||
      !isCustomerFilter ||
      !isCustomerGroupFilter ||
      (status.length !== 3 && 0 !== status.length) ||
      this.checkHasValueInObject(rangeDateHold) ||
      this.checkHasValueInObject(rangePotential) ||
      this.checkHasValueInObject(rangeNotRecord) ||
      this.checkHasValueInObject(rangeRecord) ||
      this.checkHasValueInObject(date)
    );
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObject(data: any) {
    return !!data && ((data.start != null && data.start !== '') || (data.end != null && data.end !== ''));
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customer: IAllAccountNumber): string {
    if (!customer) return '';
    return `${customer.accountNumber} - ${customer.customerName}`;
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerGroupFilter(group: IOptionList): string {
    if (!group) return '';
    return `${group.label}`;
  }

  displayStokeFilter(stoke: IItemStoke) {
    if (!stoke) return '';
    return `${stoke.value} - ${stoke.stoke}`;
  }
}
