.all-recommendations-filter-wrap {
  width: 800px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.all-rcm-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--color--other--divider);

  .close-btn {
    cursor: pointer;
  }
}

.all-rcm-content {
  display: flex;
  flex: 1;
  overflow: auto;

  .content-left,
  .content-right {
    width: 50%;
    display: flex;
    flex-direction: column;
  }
}

.content-left {
  border: 1px solid var(--color--other--divider);
  height: 100%;
  overflow: auto;

  .title-left {
    padding: 12px 24px;
  }

  // Mã CK
  .searchbox-wrap {
    padding: 8px 24px 16px;
    display: flex;
    flex-direction: column;
    gap: 17px;
    overflow: hidden;
    border-bottom: 1px solid var(--color--other--divider);
    min-height: 245px;

    .search-box {
      display: flex;
      position: relative;

      .input-cls-custom {
        padding: 10px 16px;
        padding-left: 32px;
        width: 100%;
      }

      .icon-search-cls {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 10px;
      }
    }

    .option-list-cls {
      height: 164px;
      overflow: auto;
      display: flex;
      flex-direction: column;
      gap: 16px;
      .checkbox-cls {
        margin-bottom: 16px;
        &:last-child {
          margin-bottom: 0;
        }
        ::ng-deep {
          .mdc-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
          }
        }

        .img-cls {
          width: 18px;
          height: 18px;
          object-fit: contain;
          vertical-align: middle;
          border-radius: 50%;
        }
      }

      .checkbox-cls-item {
        margin-bottom: 16px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  //  Trạng thái khuyến nghị
  .option-list-status-rcm-cls {
    padding: 8px 24px 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    border-bottom: 1px solid var(--color--other--divider);

    ::ng-deep {
      .mdc-label {
        font-size: 12px;
      }
    }
  }

  // KN tới Nhóm khách hàng
  .customer-dropdown {
    padding: 12px 24px 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    border-bottom: 1px solid var(--color--other--divider);

    .customer-input-wrapper {
      position: relative;
      cursor: pointer;

      .customer-input {
        padding: 8px 16px;
        height: 48px;
        border-radius: 8px;
        border: 1px solid var(--Neutral-100, #dbdee0);
        background: var(--Colors-Grey-White, #fff);
        width: 100%;
        cursor: pointer;
      }

      img {
        width: 18px;
        height: 18px;
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  .holding-period {
    display: flex;
    flex-direction: column;
    width: 100%;

    .holding-period-content {
      display: flex;
      gap: 8px;
      padding: 8px 24px 16px;

      .content-from,
      .content-to {
        width: 25%;
        display: flex;
        flex-direction: column;
        gap: 4px;
        flex: 1;

        input {
          border: 1px solid var(--Neutral-100, #dbdee0);
          padding: 10px 16px;
          border-radius: 8px;
          height: 40px;
          width: 100%;
        }
      }
    }
  }

  .holding-period,
  .rcm-day {
    display: flex;
    flex-direction: column;
    width: 100%;

    .holding-period-content,
    .rcm-day-content {
      display: flex;
      gap: 8px;
      padding: 8px 24px 16px;

      .content-from,
      .content-to {
        width: 25%;
        display: flex;
        flex-direction: column;
        gap: 4px;
        flex: 1;

        .input-wrapper {
          position: relative;
          cursor: pointer;

          .calendar-input {
            padding: 10px 28px 10px 16px;
            border: 1px solid var(--Neutral-100, #dbdee0);
            border-radius: 8px;
            height: 40px;
            width: 100%;
          }
        }

        img {
          position: absolute;
          top: 12px;
          right: 12px;
        }

        .holding-period-input {
          border: 1px solid var(--Neutral-100, #dbdee0);
          padding: 10px 16px;
          border-radius: 8px;
          height: 40px;
          width: 100%;
        }
      }

      .content-to {
        .holding-period-input::placeholder {
          font-size: 14px !important;
        }
      }
    }
  }
}

.content-right,
.content-left {
  .title-right {
    padding: 12px 24px;
  }

  .searchbox-wrap {
    padding: 8px 24px 16px;
    display: flex;
    flex-direction: column;
    gap: 17px;
    overflow: hidden;
    border-bottom: 1px solid var(--color--other--divider);
    min-height: 245px;
  }
}

.footer-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--color--other--divider);
  padding: 16px 24px;

  .btn {
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid var(--color--other--divider);
  }

  .default {
    background-color: var(--color--neutral--white);
  }

  .apply {
    background-color: var(--color--brand--500);
    color: var(--color--neutral--white);
  }
}

::ng-deep {
  .dropdown-overlay-common {
    ng-component {
      width: 100% !important;
    }
  }
}
