import { ChangeDetectorRef, Component, Inject, OnInit, Optional, QueryList, ViewChildren } from '@angular/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, provideNativeDateAdapter } from '@angular/material/core';
import { take, tap } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { IOptionList } from 'src/app/shared/models/dropdown-item.model';
import { IItemStoke, IRangeFilter } from '../../models/recommendations';
import { AbstractControl, FormBuilder, FormGroup } from '@angular/forms';
import { DatePickerNavigationFullDateComponent } from 'src/app/shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MY_DATE_FORMAT } from 'src/app/shared/constants/date-picker';
import {
  selectAllAccountNumberListByBrokerView$,
  selectCustomerGroupList$,
  selectAllStockList$,
} from 'src/app/stores/shared/shared.selectors';
import { Store } from '@ngrx/store';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';

/**
 * FollowingRecommendationsFilterComponent
 */
@Component({
  selector: 'app-following-recommendations-filter',
  templateUrl: './following-recommendations-filter.component.html',
  styleUrl: './following-recommendations-filter.component.scss',
  providers: [
    provideNativeDateAdapter(),
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
  ],
})
export class FollowingRecommendationsFilterComponent implements OnInit {
  @ViewChildren(VirtualScrollListComponent) virtualScroll!: QueryList<VirtualScrollListComponent>;

  headerCalendar = DatePickerNavigationFullDateComponent;

  followingRcmForm!: FormGroup;

  readonly customerKey = 'accountNumber';

  customers: IAllAccountNumber[] = [];

  readonly customerGroupKey = 'value';

  customerGroups: IOptionList[] = [];

  readonly stokeKey = 'value';
  readonly stokeId = 'stoke';

  listOfStocks: IItemStoke[] = [];

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  isDisableStockApply = false;

  isDisableApply = false;

  isDisableCustomerGroupApply = false;

  /**
   * Constructor
   * @param _destroy
   * @param popoverService
   * @param data
   * @param dialogRef
   * @param popoverRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<FollowingRecommendationsFilterComponent>,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly cdf: ChangeDetectorRef,
    private readonly fb: FormBuilder,
    private readonly storeFo: Store
  ) {
    this.initForm();
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.patchValueToForm();
  }

  private patchValueToForm() {
    const { date, rangeDateHold, rangePotential } = this.data.filterOptions;
    this.followingRcmForm.patchValue({
      startDateHold: rangeDateHold.start,
      endDateHold: rangeDateHold.end,
      startPotential: rangePotential.start,
      endPotential: rangePotential.end,
      dateStart: date.start,
      dateEnd: date.end,
    });
  }

  private loadCustomersFo() {
    this.storeFo
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(
        take(1),
        tap((customersFo) => {
          this.customers = customersFo;
        })
      )
      .subscribe();
  }

  private loadCustomerGroupListFo() {
    this.storeFo
      .select(selectCustomerGroupList$)
      .pipe(
        take(1),
        tap((groupsFo) => {
          this.customerGroups = groupsFo
            .filter((t) => t.isShow)
            .map((g) => ({
              label: `${g.groupCode} : ${g.name}`,
              value: g.name,
              groupCode: g.groupCode,
              id: g.id,
            }));
        })
      )
      .subscribe();
  }

  private loadStokeList() {
    this.storeFo
      .select(selectAllStockList$)
      .pipe(take(1))
      .subscribe((allStocksFo) => {
        this.listOfStocks = allStocksFo.map((t) => ({
          value: t.id,
          stoke: t.stock,
        }));
      });
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.loadCustomersFo();
    this.loadCustomerGroupListFo();
    this.loadStokeList();
  }

  /**
   * initForm
   */
  private initForm() {
    this.followingRcmForm = this.fb.group(
      {
        dateStart: [null],
        dateEnd: [null],
        startDateHold: [null],
        endDateHold: [null],
        startPotential: [null],
        endPotential: [null],
      },
      {
        validator: this.customValidate,
      }
    );
  }

  /**
   * customValidate
   * @param group followingRcmForm
   */
  customValidate(group: FormGroup) {
    const dateStart = group.get('dateStart');
    const dateEnd = group.get('dateEnd');
    const startDateHold = group.get('startDateHold');
    const endDateHold = group.get('endDateHold');
    const startPotential = group.get('startPotential');
    const endPotential = group.get('endPotential');

    const validFromToValueFo = (startControl: any, endControl: any) => {
      if (startControl && endControl) {
        const startFo = startControl.value;
        const endFo = endControl.value;
        if (+endFo < +startFo && endFo !== null && endFo !== '') {
          return endControl.setErrors({ validFromTo: true });
        } else {
          endControl?.setErrors(null);
        }
      }
    };

    const dateValidatorFo = (control: AbstractControl | null): void => {
      if (!control) return;

      const valueFo = control.value?._d ?? control.value;
      const currentErrorsFo = control.errors || {};

      if (!valueFo || valueFo === '') {
        const { invalidDate, ...remainingErrors } = currentErrorsFo;
        control.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
        return;
      }

      const parsedDate = typeof valueFo === 'string' ? new Date(valueFo) : valueFo;
      const isValidDate = parsedDate instanceof Date && !isNaN(parsedDate.getTime());

      if (!isValidDate) {
        control.setErrors({ ...currentErrorsFo, invalidDate: true });
      } else {
        const { invalidDate, ...remainingErrors } = currentErrorsFo;
        control.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
      }
    };

    const dateRangeValidatorFo = (startControl: AbstractControl | null, endControl: AbstractControl | null): void => {
      if (!startControl || !endControl) return;

      const startErrorsFo = startControl.errors || {};
      const endErrorsFo = endControl.errors || {};

      if (endErrorsFo['matDatepickerMin']) {
        const { matDatepickerMax, ...remainingErrors } = startErrorsFo;
        startControl.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
      }
    };

    validFromToValueFo(startPotential, endPotential);
    validFromToValueFo(startDateHold, endDateHold);

    dateValidatorFo(dateStart);
    dateValidatorFo(dateEnd);

    dateRangeValidatorFo(dateStart, dateEnd);
  }

  validateStockFilterValue(invalid: boolean) {
    this.isDisableStockApply = invalid;
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  validateCustomerGroupFilterValue(invalid: boolean) {
    this.isDisableCustomerGroupApply = invalid;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    if (this.isDisableApply || this.isDisableCustomerGroupApply || this.isDisableStockApply) return;

    const { dateStart, dateEnd, startDateHold, endDateHold, startPotential, endPotential } =
      this.followingRcmForm.value;

    const getVirtualScrollByIdFo = (id: string) => {
      return this.virtualScroll.find((component) => component.id === id);
    };
    const customer = ((getVirtualScrollByIdFo(this.customerKey)?.getChecked() as IAllAccountNumber[]) ?? []).map(
      (c) => c.accountNumber
    );

    const stockCodes = ((getVirtualScrollByIdFo(this.stokeId)?.getChecked() as IItemStoke[]) ?? []).map((c) => c.value);

    const customerGroup = ((getVirtualScrollByIdFo(this.customerGroupKey)?.getChecked() as IOptionList[]) ?? []).map(
      (c) => c.value as string
    );
    const rangeDateHold = {
      start: startDateHold,
      end: endDateHold,
    };
    const rangePotential = {
      start: startPotential,
      end: endPotential,
    };

    const date = {
      start: dateStart?._d ?? dateStart,
      end: dateEnd?._d ?? dateEnd,
    };

    const isFilter = this.checkStatusFilter(stockCodes, customer, customerGroup, rangeDateHold, rangePotential, date);

    this.dialogRef.close({
      type: 'save',
      optionFilter: {
        stockCodes,
        customer,
        customerGroup,
        rangeDateHold,
        date,
        rangePotential,
        isFilter,
      },
    });
  }

  /**
   * CheckStatusFilter
   * @param rangeDateHold
   * @param rangePotential
   * @param date
   * @returns {boolean} true / false
   */
  checkStatusFilter(
    stockCodes: string[],
    customer: string[],
    customerGroup: string[],
    rangeDateHold: IRangeFilter,
    rangePotential: IRangeFilter,
    date: IRangeFilter
  ) {
    const isStokeFilter = stockCodes.length === 0;
    const isCustomerFilter = customer.length === 0;
    const isCustomerGroupFilter = customerGroup.length === 0;
    return (
      !isStokeFilter ||
      !isCustomerFilter ||
      !isCustomerGroupFilter ||
      this.checkHasValueInObjectFo(rangeDateHold) ||
      this.checkHasValueInObjectFo(rangePotential) ||
      this.checkHasValueInObjectFo(date)
    );
  }

  /**
   * DefaultFilter
   */
  defaultFilterFo() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObjectFo(dataFo: any) {
    return !!dataFo && ((dataFo.start != null && dataFo.start !== '') || (dataFo.end != null && dataFo.end !== ''));
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customerFo: IAllAccountNumber): string {
    if (!customerFo) return '';
    return `${customerFo.accountNumber} - ${customerFo.customerName}`;
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerGroupFilter(groupFo: IOptionList): string {
    if (!groupFo) return '';
    return `${groupFo.label}`;
  }

  displayStokeFilter(stokeFo: IItemStoke) {
    if (!stokeFo) return '';
    return `${stokeFo.value} - ${stokeFo.stoke}`;
  }
}
