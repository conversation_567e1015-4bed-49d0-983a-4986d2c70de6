<div class="fo-filter-cls following-recommendations-filter-wrap">
  <div class="fo-filter-cls following-rcm-header">
    <div class="fo-filter-cls title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="fo-filter-cls close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="fo-filter-cls box-body-container">
    <form [formGroup]="followingRcmForm" class="fo-filter-cls following-rcm-content">
      <!-- Mã CK -->
      <div class="fo-filter-cls title typo-body-15">{{ 'MES-157' | translate }}</div>
      <div class="fo-filter-cls searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="listOfStocks?.length; else noResultsStoke"
          [id]="stokeId"
          [key]="stokeKey"
          [items]="listOfStocks"
          [selectedKeys]="data.filterOptions.stockCodes ?? []"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-670'"
          [warningMessage]="'MES-668'"
          class="fo-filter-cls"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-14' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['value', 'stoke']"
          [displayFn]="displayStokeFilter"
        ></app-virtual-scroll-list>

        <ng-template #noResultsStoke>
          <div class="fo-filter-cls typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- Ngày tạo khuyến nghị -->
      <div class="fo-filter-cls title typo-body-15">{{ 'MES-582' | translate }}</div>
      <div class="fo-filter-cls rcm-day-content">
        <div class="fo-filter-cls content-from">
          <div class="fo-filter-cls from-label typo-body-11 fs-12">{{ 'MES-209' | translate }} ngày</div>
          <div class="fo-filter-cls input-wrapper">
            <app-form-control>
              <input
                type="text"
                class="fo-filter-cls calendar-input typo-body-12"
                matInput
                [matDatepicker]="dateFrom"
                [placeholder]="'DD/MM/YYYY'"
                formControlName="dateStart"
                dateInput
                [max]="
                  followingRcmForm.get('dateEnd')?.value?._d
                    ? followingRcmForm.get('dateEnd')?.value?._d
                    : followingRcmForm.get('dateEnd')?.value
                    ? followingRcmForm.get('dateEnd')?.value
                    : null
                "
              />
            </app-form-control>
            <img (click)="dateFrom.open()" src="./assets/icons/calendar.svg" alt="" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              panelClass="fo-filter-cls calendar-cls"
              #dateFrom
            ></mat-datepicker>
          </div>
        </div>

        <div class="fo-filter-cls content-to">
          <div class="fo-filter-cls to-label typo-body-11">{{ 'MES-210' | translate }} ngày</div>
          <div class="fo-filter-cls input-wrapper">
            <app-form-control>
              <input
                type="text"
                class="fo-filter-cls calendar-input typo-body-12"
                matInput
                [matDatepicker]="dateTo"
                formControlName="dateEnd"
                [placeholder]="'DD/MM/YYYY'"
                dateInput
                [min]="
                  followingRcmForm.get('dateStart')?.value?._d
                    ? followingRcmForm.get('dateStart')?.value?._d
                    : followingRcmForm.get('dateStart')?.value
                    ? followingRcmForm.get('dateStart')?.value
                    : null
                "
              />
            </app-form-control>
            <img (click)="dateTo.open()" src="./assets/icons/calendar.svg" alt="" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              panelClass="fo-filter-cls calendar-cls"
              #dateTo
            ></mat-datepicker>
          </div>
        </div>
      </div>

      <!-- Thời gian nắm giữ -->
      <div class="fo-filter-cls title typo-body-15">{{ 'MES-421' | translate }} (ngày)</div>
      <div class="fo-filter-cls holding-period-content">
        <div class="fo-filter-cls content-from">
          <div class="fo-filter-cls from-label typo-body-11">{{ 'MES-209' | translate }}</div>
          <app-form-control>
            <input
              formControlName="startDateHold"
              [mask]="'0*'"
              [allowNegativeNumbers]="true"
              class="fo-filter-cls holding-period-input typo-field-5"
              [placeholder]="0"
            />
          </app-form-control>
        </div>

        <div class="fo-filter-cls content-to">
          <div class="fo-filter-cls to-label typo-body-11">{{ 'MES-210' | translate }}</div>
          <app-form-control>
            <input
              formControlName="endDateHold"
              [mask]="'0*'"
              [allowNegativeNumbers]="true"
              class="fo-filter-cls holding-period-input typo-field-5"
              [placeholder]="'∞'"
            />
          </app-form-control>
        </div>
      </div>

      <!-- +/- tiềm năng -->
      <div class="fo-filter-cls title typo-body-15">{{ 'MES-214' | translate }} (%)</div>
      <div class="fo-filter-cls holding-period-content">
        <div class="fo-filter-cls content-from">
          <div class="fo-filter-cls from-label typo-body-11">{{ 'MES-209' | translate }}</div>
          <app-form-control>
            <input
              class="fo-filter-cls holding-period-input typo-field-5"
              [mask]="'separator'"
              [allowNegativeNumbers]="true"
              formControlName="startPotential"
              [placeholder]="'-∞'"
            />
          </app-form-control>
        </div>

        <div class="fo-filter-cls content-to">
          <div class="fo-filter-cls to-label typo-body-11">{{ 'MES-210' | translate }}</div>
          <app-form-control>
            <input
              class="fo-filter-cls holding-period-input typo-field-5"
              [mask]="'separator'"
              [allowNegativeNumbers]="true"
              formControlName="endPotential"
              [placeholder]="'∞'"
            />
          </app-form-control>
        </div>
      </div>
    </form>

    <div class="fo-filter-cls right-box">
      <!-- KN tới Khách hàng -->
      <div class="fo-filter-cls title typo-body-15">{{ 'MES-160' | translate }}</div>
      <div class="fo-filter-cls searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customers?.length; else noResultCustomer"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers"
          [selectedKeys]="data.filterOptions.customer ?? []"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [warningMessage]="'MES-668'"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-669'"
          [selectAllLabel]="'MES-58' | translate"
          class="fo-filter-cls"
          [searchPlaceholder]="'MES-295' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [displayFn]="displayCustomerFilter"
          [searchKeys]="['accountNumber', 'customerName']"
          (invalidSelection)="validateFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultCustomer>
          <div class="fo-filter-cls typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- KN tới Nhóm khách hàng -->
      <div class="fo-filter-cls title typo-body-15">{{ 'MES-161' | translate }}</div>
      <div class="fo-filter-cls searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customerGroups?.length; else noResultCustomerGroup"
          [id]="customerGroupKey"
          [key]="customerGroupKey"
          [selectedKeys]="data.filterOptions.customerGroup ?? []"
          [items]="customerGroups"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [searchPlaceholder]="'MES-14' | translate"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-669'"
          [warningMessage]="'MES-668'"
          [selectAllLabel]="'MES-58' | translate"
          [noResultsMessage]="'MES-585' | translate"
          class="fo-filter-cls"
          [searchKeys]="['groupCode', 'value']"
          [displayFn]="displayCustomerGroupFilter"
          (invalidSelection)="validateCustomerGroupFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultCustomerGroup>
          <div class="fo-filter-cls typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>
    </div>
  </div>

  <div class="fo-filter-cls footer-filter">
    <div (click)="defaultFilterFo()" class="fo-filter-cls btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <button
      [disabled]="
        followingRcmForm.invalid ||
        followingRcmForm.get('dateStart')?.invalid ||
        followingRcmForm.get('dateEnd')?.invalid ||
        isDisableApply ||
        isDisableCustomerGroupApply ||
        isDisableStockApply
      "
      (click)="applyFilter()"
      class="fo-filter-cls btn apply typo-button-3"
    >
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
