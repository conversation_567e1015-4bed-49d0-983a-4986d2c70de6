.show-details-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 6px;
  background: var(--color--neutral--white);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.25), 0px 4px 16px 0px rgba(155, 158, 164, 0.25);
  cursor: pointer;

  .show-details-wrapper {
    display: flex;
    padding: 8px 13px;
    align-items: center;
    gap: 8px;
    border-radius: 6px;
    align-self: stretch;

    &:hover {
      background: var(--color--background--hover);
    }
  }
}

.setting-recommend-container {
  padding: 8px 0;
  border-radius: 6px;

  .option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 13px;
    cursor: pointer;

    .red {
      color: var(--color--danger--600);
    }

    img {
      width: 18px;
      height: 18px;
    }

    &:hover {
      background: var(--color--background--hover);
    }
  }
}

.opacity-50 {
  opacity: 0.5;
  pointer-events: none;
}

::ng-deep {
  .popover {
    padding: 0 !important;
    width: 184px;
  }
}
