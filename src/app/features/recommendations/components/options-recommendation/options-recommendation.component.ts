import { Component, Inject, Input } from '@angular/core';
import { take } from 'rxjs';
import { DialogService, LoadingService } from 'src/app/core/services';
import { GuaranteeConfirmComponent } from 'src/app/shared/components/guarantee-confirm/guarantee-confirm.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { RecommendationDialogComponent } from 'src/app/shared/components/stock/recommendation-dialog/recommendation-dialog.component';
import { IRecommendationDialogData } from 'src/app/shared/models/stock.model';
import { Store } from '@ngrx/store';
import { updateRecommendationData } from '../../stores/recommendation.actions';
import { AllocationResultComponent } from 'src/app/features/assets/components/allocation-result/allocation-result.component';
import {
  CONVERT_RECOMMENDATION_TYPE_TO_LABEL,
  CONVERT_STATUS_RECOMMENDATION_TYPE_TO_LABEL,
  ERecommendationStatus,
  RecommendationStatus,
} from '../../constants/recommendations';
import { DetailRecommendationDialogComponent } from '../detail-recommendation-dialog/detail-recommendation-dialog.component';
import { ICustomerListInRecommendationDialog } from 'src/app/shared/models/global';
import { RecommendationStore } from 'src/app/shared/components/stock/recommendation-dialog/recommendation.store';

/**
 * OptionsRecommendationComponent
 */
@Component({
  selector: 'app-options-recommendation',
  templateUrl: './options-recommendation.component.html',
  styleUrls: ['./options-recommendation.component.scss'],
})
export class OptionsRecommendationComponent {
  @Input() element: any;
  @Input() type: string = RecommendationStatus.ALL;

  @Input() customerList: ICustomerListInRecommendationDialog[] = [];
  @Input() customerGroupList: ICustomerListInRecommendationDialog[] = [];

  @Input() isDisableButton = false;

  ERecommendationStatus = ERecommendationStatus;

  /**
   * Constructor
   * @param popoverRef - PopoverRef
   * @param dialogService - DialogService
   * @param messageService - MessageService
   * @param store Store
   */
  constructor(
    @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly dialogService: DialogService,
    private readonly store: Store,
    private readonly recommendationStore: RecommendationStore,
    private readonly loadingService: LoadingService
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }

  /**
   * openRecommendation
   * @param element MouseEvent
   */
  openRecommendation(element: any) {
    if (
      element.status === ERecommendationStatus.OPENED ||
      (this.isDisableButton && element.status === ERecommendationStatus.WATCHING)
    )
      return;

    this.popoverRef.close();
    const { id, status, stockCode, lossPrice, recommendTo, targetPrice } = element;
    const ref = this.dialogService.openRightDialog(RecommendationDialogComponent, {
      width: '560px',
      data: <IRecommendationDialogData>{
        title: 'MES-127',
        action: 'open',
        type: CONVERT_STATUS_RECOMMENDATION_TYPE_TO_LABEL[status],
        status: this.type,
        customerList: this.customerList,
        customerGroupList: this.customerGroupList,
        recommendationData: {
          id,
          status: this.element.status, // Trạng thái
          stock: stockCode.code, // Mã CK
          stockName: stockCode.name,
          stopLoss: lossPrice, // Giá cắt lỗ
          customerGroup: recommendTo.group,
          customerName: recommendTo.personal,
          takeProfit1: targetPrice.to, // Giá mục tiêu thấp
          takeProfit2: targetPrice.from ?? null, // Giá mục tiêu cao
        },
      },
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe({
        next: (value) => {
          if (!value) return;
          this.store.dispatch(updateRecommendationData({ data: value }));
        },
      });
  }

  /**
   * editRecommendation
   * @param element MouseEvent
   */
  editRecommendation(element: any) {
    if (
      (this.isDisableButton && element.status === ERecommendationStatus.WATCHING) ||
      (this.isDisableButton && element.status === ERecommendationStatus.OPENED)
    )
      return;

    this.popoverRef.close();
    const { id, status, stockCode, openPrice, recommendTo, closePrice, targetPrice, lossPrice, recommendPrice } =
      element;
    const ref = this.dialogService.openRightDialog(RecommendationDialogComponent, {
      width: '560px',
      data: <IRecommendationDialogData>{
        title: status === ERecommendationStatus.WATCHING ? 'MES-125' : 'MES-126',
        action: 'edit',
        type: CONVERT_STATUS_RECOMMENDATION_TYPE_TO_LABEL[status],
        status: this.type,
        editMode: status === ERecommendationStatus.WATCHING,
        customerList: this.customerList,
        customerGroupList: this.customerGroupList,
        recommendationData: {
          id,
          status,
          stock: stockCode.code,
          stockName: stockCode.name,
          stopLoss: lossPrice,
          open: openPrice,
          open1: recommendPrice?.from,
          open2: recommendPrice?.to,
          customerGroup: recommendTo.group,
          customerName: recommendTo.personal ?? recommendTo.personalFo,
          close: closePrice,
          takeProfit: targetPrice.to,
          takeProfit1: targetPrice.from,
          takeProfit2: targetPrice.to,
        },
      },
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe({
        next: (value) => {
          if (!value) return;

          this.store.dispatch(updateRecommendationData({ data: value }));
        },
      });
  }

  /**
   * closeRecommendation
   * @param element
   */
  closeRecommendation(element: any) {
    if (
      element.status === ERecommendationStatus.WATCHING ||
      (this.isDisableButton && element.status === ERecommendationStatus.OPENED)
    )
      return;

    this.popoverRef.close();
    const { id, status, openPrice, closePrice, recommendTo, stockCode } = element;
    this.dialogService.openRightDialog(RecommendationDialogComponent, {
      width: '560px',
      data: <IRecommendationDialogData>{
        title: 'MES-128',
        action: 'close',
        status: this.type,
        type: CONVERT_STATUS_RECOMMENDATION_TYPE_TO_LABEL[status],
        recommendationData: {
          id,
          status,
          stock: stockCode.code,
          stockName: stockCode.name,
          open: openPrice,
          close: closePrice,
          customerGroup: recommendTo.group,
          customerName: recommendTo.personal,
        },
      },
    });
  }

  /**
   * cancelRecommendation
   * @param element MouseEvent
   */
  cancelRecommendation(element: any) {
    if (
      element.status === ERecommendationStatus.OPENED ||
      (this.isDisableButton && element.status === ERecommendationStatus.WATCHING)
    )
      return;

    this.popoverRef.close();

    const { group, personal } = element.recommendTo;
    const isNotCustomersAndGroups = !personal?.length && (group ?? []).every((item: any) => item.active === false);
    if (isNotCustomersAndGroups) {
      this.dialogService.openPopUp(GuaranteeConfirmComponent, {
        width: '360px',
        height: 'fit-content',
        panelClass: [''],
        data: {
          action: '',
          info: [],
          title: [],
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-644',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
        },
      });

      return;
    }

    const isContainInActiveGroup = (group ?? []).some((item: any) => item.active === false);
    const ref = this.dialogService.openPopUp(GuaranteeConfirmComponent, {
      width: '360px',
      height: 'fit-content',
      panelClass: [''],
      data: {
        action: 'cancelRcm',
        info: [`${element.stockCode.code} - ${element.stockCode.name}`],
        title: ['MES-157'],
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: isContainInActiveGroup ? 'MES-643' : 'MES-303',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v || v.type !== 'save') return;

        return this.recommendationStore.cancelRecommendation({ id: element.id });
      });
  }

  /**
   * showDetails
   * @param element
   */
  showDetails(element: any) {
    this.popoverRef.close();

    const { allocationProportion, allocationTo, allocationFrom, personSent, recommendTo, recommendDate, content } =
      element;

    const allocatedSource = {
      type: CONVERT_RECOMMENDATION_TYPE_TO_LABEL[allocationFrom],
      oldValue: {
        amount: allocationProportion?.cash,
        percentage: allocationProportion?.percent,
      },
      newValue: {
        amount: allocationProportion?.cash,
        percentage: allocationProportion?.percent,
      },
    };

    const allocatedTarget = {
      type: CONVERT_RECOMMENDATION_TYPE_TO_LABEL[allocationTo],
      oldValue: {
        amount: allocationProportion.cash,
        percentage: allocationProportion.percent,
      },
      newValue: {
        amount: allocationProportion.cash,
        percentage: allocationProportion.percent,
      },
    };

    const allocateResult = {
      title: 'MES-334',
      allocatedSource,
      allocatedTarget,
      sentBy: personSent.nickName,
      sentDate: recommendDate,
      accountNumber: recommendTo,
      customerName: personSent.customerName,
      note: content,
      type: 'open',
    };

    this.dialogService.openRightDialog(AllocationResultComponent, {
      width: '560px',
      data: allocateResult,
    });
  }

  /**
   * showDetails
   * @param element
   */
  showRecommendationDetails(element: any) {
    this.loadingService.show();
    this.dialogService.openRightDialog(DetailRecommendationDialogComponent, {
      width: '492px',
      data: element,
    });
    this.popoverRef.close();
  }
}
