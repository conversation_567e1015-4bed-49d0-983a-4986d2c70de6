@if( element.status === ERecommendationStatus.WATCHING || element.status === ERecommendationStatus.OPENED) {
<div class="setting-recommend-container">
  <!-- Mở khuyến nghị -->
  <button
    class="option"
    [ngClass]="{
      'opacity-50':
        element.status === ERecommendationStatus.OPENED ||
        (isDisableButton && element.status === ERecommendationStatus.WATCHING)
    }"
    (click)="openRecommendation(element)"

  >
    <img src="./assets/icons/play.svg" alt="play-icon" />
    <span>{{ 'MES-119' | translate }}</span>
  </button>
  <!-- S<PERSON>a khuyến nghị -->
  <button
    class="option"
    [ngClass]="{
      'opacity-50':
        (isDisableButton && element.status === ERecommendationStatus.WATCHING) ||
        (isDisableButton && element.status === ERecommendationStatus.OPENED)
    }"
    (click)="editRecommendation(element)"

  >
    <img src="./assets/icons/edit-icon.svg" alt="play-icon" />
    <span>{{ 'MES-120' | translate }}</span>
  </button>
  <!-- Đóng khuyến nghị -->
  <button
    class="option"
    [ngClass]="{
      'opacity-50':
        element.status === ERecommendationStatus.WATCHING ||
        (isDisableButton && element.status === ERecommendationStatus.OPENED)
    }"
    (click)="closeRecommendation(element)"
  >
    <img src="./assets/icons/close-black.svg" alt="play-icon" />
    <span>{{ 'MES-121' | translate }}</span>
  </button>
  <!-- Huỷ khuyến nghị -->
  <button
    class="option"
    [ngClass]="{
      'opacity-50':
        element.status === ERecommendationStatus.OPENED ||
        (isDisableButton && element.status === ERecommendationStatus.WATCHING)
    }"
    (click)="cancelRecommendation(element)"
  >
    <img src="./assets/icons/close-red.svg" alt="play-icon" />
    <span class="red">{{ 'MES-122' | translate }}</span>
  </button>
</div>
} @else if(element.status === ERecommendationStatus.CLOSED) {
<div class="show-details-container">
  <button class="show-details-wrapper" (click)="showRecommendationDetails(element)"
  >
    <img src="./assets/icons/menu.svg" alt="" />
    <div class="typo-body-12">{{ 'MES-206' | translate }}</div>
  </button>
</div>
} @else {
<div class="show-details-container">
  <button class="show-details-wrapper" (click)="showDetails(element)"
  >
    <img src="./assets/icons/menu.svg" alt="" />
    <div class="typo-body-12">{{ 'MES-206' | translate }}</div>
  </button>
</div>
}
