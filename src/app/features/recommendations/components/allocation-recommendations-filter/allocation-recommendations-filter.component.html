<div class="allocation-recommendations-filter-wrap">
  <div class="allocation-rcm-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="allocation-recommendations-body">
    <div class="title typo-body-15">{{ 'MES-275' | translate }}</div>

    <div class="searchbox-wrap">
      <div class="search-box">
        <input
          type="text"
          class="input-search input-style-common typo-body-11"
          [placeholder]="'MES-14' | translate"
          [formControl]="searchCodeControl"
        />
        <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
      </div>
      <div class="option-list-cls">
        <mat-checkbox (change)="changeSections($event.checked, 'all')" [checked]="isSelectAll" class="checkbox-cls">{{
          'MES-58' | translate
        }}</mat-checkbox>
        @for(item of listFilterBrokerOptions; let i = $index; track item){
        <div class="checkbox-cls-item">
          <mat-checkbox
            (change)="changeSections($event.checked, 'item', item)"
            [checked]="item.isSelect"
            class="checkbox-cls"
            >{{ item.value }}</mat-checkbox
          >
        </div>
        }
      </div>
    </div>
    <div class="typo-body-15 title">{{ 'MES-207' | translate }}</div>
    <!-- Ngày khuyến nghị -->
    <div class="box-calendar">
      <!-- Từ ngày -->
      <div class="content-from">
        <div class="from-label typo-body-12">{{ 'MES-209' | translate }} ngày</div>
        <div class="input-wrapper">
          <input
            type="text"
            class="fs-12 calendar-input typo-body-12"
            matInput
            [matDatepicker]="dateFrom"
            [placeholder]="'DD/MM/YYYY'"
            [formControl]="startDate"
            [max]="endDate.value ? endDate.value : null"
            (dateInput)="onDateToChange()"
          />
          <img src="./assets/icons/calendar.svg" alt="" (click)="dateFrom.open()" />
          <mat-datepicker
            [calendarHeaderComponent]="headerCalendar"
            #dateFrom
            panelClass="calendar-cls"
          ></mat-datepicker>
        </div>
      </div>

      <!-- Tới ngày -->
      <div class="content-to">
        <div class="to-label typo-body-12">{{ 'MES-210' | translate }} ngày</div>
        <div class="input-wrapper">
          <input
            type="text"
            class="fs-12 calendar-input typo-body-12"
            matInput
            [matDatepicker]="dateTo"
            [placeholder]="'DD/MM/YYYY'"
            [formControl]="endDate"
            [min]="startDate.value ? startDate.value : null"
          />
          <img src="./assets/icons/calendar.svg" alt="" (click)="dateTo.open()" />
          <mat-datepicker [calendarHeaderComponent]="headerCalendar" #dateTo panelClass="calendar-cls"></mat-datepicker>
        </div>
      </div>
    </div>
    <div class="dropdown-container">
      <!-- KN tới Khách hàng -->
      <div class="customer-dropdown">
        <div class="typo-body-15">{{ 'MES-160' | translate }}</div>
        <div class="customer-input-wrapper" (click)="openPopoverCustomer($event)">
          <input
            matInput
            type="text"
            class="customer-input fs-12"
            [value]="
              customerOptions.length === listCustomerOptions.length ? 'Tất cả' : customerOptions.length + ' khách hàng'
            "
            readonly
          />
          <img src="./assets/icons/arrow-down.svg" alt="" />
        </div>
      </div>
    </div>

    <!-- Phân bổ  -->
    <div class="alllocation-assets-container">
      <div class="allocation-dropdown">
        <div class="typo-body-15">{{ 'MES-83' | translate }}</div>
        <div class="allocation-input-wrapper" (click)="openPopoverAllocationAsset($event, 'from')">
          <input
            matInput
            type="text"
            class="allocation-input fs-12"
            [value]="
              allocationAssetFrom.length === listAllocationAssetFrom.length
                ? 'Tất cả'
                : allocationAssetFrom.length + ' phân bổ'
            "
            readonly
          />
          <img src="./assets/icons/arrow-down.svg" alt="" />
        </div>
      </div>
      <div class="allocation-dropdown">
        <div class="typo-body-15">{{ 'MES-80' | translate }}</div>
        <div class="allocation-input-wrapper" (click)="openPopoverAllocationAsset($event, 'to')">
          <input
            matInput
            type="text"
            class="allocation-input fs-12"
            (click)="openPopoverAllocationAsset($event, 'to')"
            [value]="
              allocationAssetTo.length === listAllocationAssetTo.length
                ? 'Tất cả'
                : allocationAssetTo.length + ' phân bổ'
            "
            readonly
          />
          <img src="./assets/icons/arrow-down.svg" alt="" />
        </div>
      </div>
    </div>

    <!-- Tỷ trọng phân bổ -->
    <div class="typo-body-15 allocation-proportion-title">{{ 'MES-278' | translate }}</div>
    <div class="allocation-prportion-container">
      <div class="allocation-proportion-from">
        <div class="typo-body-11">{{ 'MES-209' | translate }}</div>
        <div class="proportion-input-wrapper">
          <input
            type="text"
            matInput
            [formControl]="startProportion"
            class="proportion-input fs-12 typo-body-12"
            [placeholder]="'0%'"
            [mask]="'percent.2'"
          />
        </div>
      </div>

      <div class="allocation-proportion-to">
        <div class="typo-body-11">{{ 'MES-210' | translate }}</div>
        <div class="proportion-input-wrapper">
          <input
            type="text"
            [formControl]="endProportion"
            class="proportion-input fs-12 typo-body-12"
            [placeholder]="'∞'"
            [mask]="'percent.2'"
          />
        </div>
      </div>
    </div>
  </div>

  <div class="allocation-recommendations-filter-footer">
    <div (click)="defaultFilter()" class="btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <div (click)="applyFilter()" class="btn apply typo-button-3">{{ 'MES-21' | translate }}</div>
  </div>
</div>
