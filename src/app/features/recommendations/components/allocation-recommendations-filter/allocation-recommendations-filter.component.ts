import { Component, Inject, OnInit, Optional } from '@angular/core';
import { Form, FormControl } from '@angular/forms';
import { IOptionList } from '../../../../shared/models/dropdown-item.model';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { MY_DATE_FORMAT } from '../../../../shared/constants/date-picker';
import {
  IAssetTypeOptions,
  ICustomerOption,
  IFilterAllocationRecomendationsParam,
  IRangeFilter,
} from '../../models/recommendations';
import { DatePickerNavigationFullDateComponent } from '../../../../shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';
import { CustomDropdownPopupFilterComponent } from '../../../../shared/components/custom-dropdown-popup-filter/custom-dropdown-popup-filter.component';
import { DestroyService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { debounceTime, startWith, take, takeUntil, tap } from 'rxjs';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';

const LIST_OF_CUSTOMER = [
  {
    customerGroup: '069C-586547',
    // customerName: 'Đặng Hoàng An Nhiên',
  },
  {
    customerGroup: '069C-316087',
    // customerName: 'Nguyễn Văn A',
  },
  {
    customerGroup: '069C-388482',
    // customerName: 'Nguyễn Văn B',
  },
  {
    customerGroup: '069C-862656',
    // customerName: 'Nguyễn Văn C',
  },
  {
    customerGroup: '069C-252138',
    // customerName: 'Nguyễn Văn D',
  },
  {
    customerGroup: '069C-400190',
    // customerName: 'Ngô Thị Hằng',
  },
  {
    customerGroup: '069C-883962',
    // customerName: 'Bùi Thị Hạnh',
  },
];

const LIST_ASSETS_TYPE = [
  {
    recommended: 'TIỀN MẶT',
  },
  {
    recommended: 'CỔ PHIẾU',
  },
  {
    recommended: 'TRÁI PHIẾU',
  },
  {
    recommended: 'PHÁI SINH',
  },
  {
    recommended: 'CHỨNG CHỈ QUỸ',
  },
];
const LIST_BROKER = [
  {
    label: 'Phòng MG-05',
    id: 'MG-015',
    value: 'duongnc - Nguyễn Cảnh Dương',
  },
  {
    label: 'Phòng MG-04',
    id: 'MG-021',
    value: 'haln - Lê Ngọc Hà',
  },
  {
    label: 'Phòng MG-06',
    id: 'MG-016',
    value: 'canhnh - Nguyễn Hoàng Cảnh',
  },
  {
    label: 'Phòng MG-08',
    id: 'MG-020',
    value: 'chienvm - Vũ Minh Chiến',
  },
  {
    label: 'Phòng MG-07',
    id: 'MG-018',
    value: 'taypm - Phạm Văn Tây',
  },
  {
    label: 'Phòng MG-10',
    id: 'MG-05',
    value: 'anhnht - Nguyễn Hoàng Tuấn Anh',
  },
  {
    label: 'Phòng MG-10',
    id: 'MG-06',
    value: 'datmt - Mai Tiến Đạt',
  },
];

/**
 * AllocationRecommendationsFilterComponent
 */
@Component({
  selector: 'app-allocation-recommendations-filter-component',
  templateUrl: './allocation-recommendations-filter.component.html',
  styleUrl: './allocation-recommendations-filter.component.scss',
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE],
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
  ],
})
export class AllocationRecommendationsFilterComponent implements OnInit {
  isSelectAll = true;
  customerOptions: ICustomerOption[] = [];
  listFilterBrokerOptions: IOptionList[] = [];
  listCustomerOptions: ICustomerOption[] = [];
  listFilterCustomerOptions: string[] = [];
  listFilterStore: IOptionList[] = [];
  listFilterAssetAllocation: IAssetTypeOptions[] = [];
  allocationAssetFrom: IAssetTypeOptions[] = [];
  listAllocationAssetFrom: IAssetTypeOptions[] = [];
  allocationAssetTo: IAssetTypeOptions[] = [];
  listAllocationAssetTo: IAssetTypeOptions[] = [];

  startDate = new FormControl();

  endDate = new FormControl();

  startProportion = new FormControl();

  endProportion = new FormControl();

  searchCodeControl = new FormControl();

  headerCalendar = DatePickerNavigationFullDateComponent;

  /**
   * @param data
   * @param _destroy
   * @param popoverService
   * @param popoverRef
   * @param dialogRef
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: IFilterAllocationRecomendationsParam,
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    public dialogRef: MatDialogRef<AllocationRecommendationsFilterComponent>
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
    const { personSent, customer, date, assetsAllocationFrom, assetsAllocationTo, rangeProportion } = data;
    this.updateBrokerList(personSent);
    this.listCustomerOptions = this.updateCustomer(LIST_OF_CUSTOMER as ICustomerOption[], customer, 'customerName');
    this.customerOptions = this.listCustomerOptions.filter((t) => t.isSelect);
    this.listAllocationAssetFrom = this.updateAssetType(LIST_ASSETS_TYPE as IAssetTypeOptions[], assetsAllocationFrom);
    this.allocationAssetFrom = this.listAllocationAssetFrom.filter((t) => t.isSelect);
    this.listAllocationAssetTo = this.updateAssetType(LIST_ASSETS_TYPE as IAssetTypeOptions[], assetsAllocationTo);
    this.allocationAssetTo = this.listAllocationAssetTo.filter((t) => t.isSelect);
    this.updateFormControlValue(date, this.startDate, this.endDate);
    this.updateFormControlValue(rangeProportion, this.startProportion, this.endProportion);
  }
  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param type Kiểu giá trị được thay đổi
   * @param item Item thay đổi
   */
  changeSections(checked: boolean, type: string, item?: IOptionList) {
    if (type === 'all') {
      this.isSelectAll = checked;
      this.listFilterBrokerOptions.forEach((i) => {
        i.isSelect = checked;
      });
    }
    if (type === 'item' && item) {
      item.isSelect = checked;
      this.isSelectAll = this.checkIsShowAll();
    }
  }

  /**
   * The Onit
   */
  ngOnInit(): void {
    this.listFilterStore = this.listFilterBrokerOptions;
    this.searchCodeControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterBrokerOptions = this._filter(value ?? '');
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * @param originList originlist
   * @param data data
   * @param type type
   * @returns {ICustomerOption[]} new origin
   */
  updateCustomer(originList: ICustomerOption[], data: string[] | null, type: string) {
    const isSelect = (value: string) => data === null || data?.includes(value);
    return originList.map((t) => {
      return {
        ...t,
        isSelect: isSelect(t[type] as string),
      };
    });
  }

  /**
   * @param {IOptionList[] } originList
   * @param {string[] | null} data
   */
  updateAssetType(originList: IAssetTypeOptions[], data: string[] | null) {
    const isSelect = (value: string) => data === null || data.includes(value);
    return originList.map((t) => ({
      ...t,
      isSelect: isSelect(t.recommended),
    }));
  }

  /**
   * Kiểm tra xem list có được show hết hay không?
   */
  checkIsShowAll() {
    for (const obj of this.listFilterBrokerOptions) {
      if (!('isSelect' in obj)) {
        return false;
      }

      if (!obj.isSelect) {
        return false;
      }
    }

    return true;
  }

  /**
   * PpdateBrokerList
   * @param broker broker
   */
  updateBrokerList(broker: string[] | null) {
    const isSelect = (value: string) => broker === null || broker?.includes(value);
    this.listFilterBrokerOptions = LIST_BROKER.map((brokerItem) => {
      return {
        label: brokerItem.label,
        value: brokerItem.value,
        id: brokerItem.id,
        isSelect: isSelect(brokerItem.value),
      };
    });

    this.isSelectAll = this.listFilterBrokerOptions.every((t) => t.isSelect === true);
  }

  /**
   *
   */
  onDateToChange() {}

  /**
   * openPopoverCustomer
   * @param event
   */
  openPopoverCustomer(event: Event) {
    let originElement = event.target as HTMLElement;

    if (originElement.tagName !== 'DIV') {
      originElement = originElement.parentElement as HTMLElement;
    }

    const popupRef = this.popoverService.open({
      origin: originElement,
      content: CustomDropdownPopupFilterComponent,
      position: 2,
      width: originElement.offsetWidth,
      height: 280,
      data: LIST_OF_CUSTOMER,
      panelClass: ['dropdown-overlay-common-width-auto'],
      componentConfig: {
        searchKey: ['customerGroup'],
        displayFnc: (v: any) => `${v.customerGroup}`,
        listFilterOptions: this.listCustomerOptions,
        isAllSelected: this.listCustomerOptions.every((t) => t.isSelect === true),
      },
    });
    popupRef.afterClosed$.pipe(take(1)).subscribe({
      next: (value) => {
        if (!value) return;
        this.customerOptions = (value.data as ICustomerOption[]) ?? [];
        this.listCustomerOptions.forEach((t: ICustomerOption) => {
          if (this.customerOptions.some((e) => e.customerGroup == t.customerGroup)) {
            t.isSelect = true;
          } else {
            t.isSelect = false;
          }
        });
      },
    });
  }

  /**
   * openPopoverAllocationAsset
   * @param {Event} event
   * @param {string} tag
   */
  openPopoverAllocationAsset(event: Event, tag: string) {
    let originElement = event.target as HTMLElement;

    if (originElement.tagName !== 'DIV') {
      originElement = originElement.parentElement as HTMLElement;
    }

    const popupRef = this.popoverService.open({
      origin: originElement,
      content: CustomDropdownPopupFilterComponent,
      position: 2,
      width: originElement.offsetWidth,
      height: 280,
      data: LIST_ASSETS_TYPE,
      panelClass: ['dropdown-overlay-common-width-auto'],
      componentConfig: {
        searchKey: ['recommended'],
        displayFnc: (v: any) => `${v.recommended}`,
        listFilterOptions: tag === 'from' ? this.listAllocationAssetFrom : this.listAllocationAssetTo,
        isAllSelected:
          tag === 'from'
            ? this.listAllocationAssetFrom.every((t) => t.isSelect === true)
            : this.listAllocationAssetTo.every((t) => t.isSelect === true),
      },
    });
    popupRef.afterClosed$.pipe(take(1)).subscribe({
      next: (value) => {
        if (!value) return;
        if (tag === 'from') {
          this.allocationAssetFrom = (value.data as IAssetTypeOptions[]) ?? [];
          this.listAllocationAssetFrom.forEach((t: IAssetTypeOptions) => {
            if (this.allocationAssetFrom.some((e) => e.recommended == t.recommended)) {
              t.isSelect = true;
            } else {
              t.isSelect = false;
            }
          });
        } else {
          this.allocationAssetTo = (value.data as IAssetTypeOptions[]) ?? [];
          this.listAllocationAssetTo.forEach((t: IAssetTypeOptions) => {
            if (this.listAllocationAssetTo.some((e) => e.recommended == t.recommended)) {
              t.isSelect = true;
            } else {
              t.isSelect = false;
            }
          });
        }
      },
    });
  }

  /**
   * @param {IRangeFilter} date date
   * @param {FormControl} startControl startControl
   * @param {FormControl} endControl endControl
   */
  updateFormControlValue(date: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    const { start, end } = date;
    startControl.patchValue(start);
    endControl.patchValue(end);
  }

  /**
   * @param {string} value search value
   */
  private _filter(value: string): IOptionList[] {
    const filterValue = value.toString().toLowerCase();

    return this.listFilterStore.filter((option) => option.value?.toString().toLowerCase()?.includes(filterValue));
  }

  /**
   * applyFilter
   */
  applyFilter() {
    const broker = this.listFilterBrokerOptions.filter((t) => t.isSelect).map((t) => t.value);
    const customer =
      this.customerOptions.length === this.listCustomerOptions.length
        ? null
        : this.customerOptions.map((t) => t.customerName);

    const assetsAllocationFrom =
      this.allocationAssetFrom.length === this.listAllocationAssetFrom.length
        ? this.listAllocationAssetFrom.map((t) => t.recommended)
        : this.allocationAssetFrom.map((t) => t.recommended);

    const assetsAllocationTo =
      this.allocationAssetTo.length === this.listAllocationAssetTo.length
        ? this.listAllocationAssetTo.map((t) => t.recommended)
        : this.allocationAssetTo.map((t) => t.recommended);

    const rangeProportion = {
      start: this.startProportion.value,
      end: this.endProportion.value,
    };

    const date = {
      start: this.startDate.value?._d ?? this.startDate.value,
      end: this.endDate.value?._d ?? this.endDate.value,
    };

    const isFilter = this.checkStatusFilter(rangeProportion, date);

    this.dialogRef.close({
      type: 'save',
      optionFilter: {
        broker,
        customer,
        assetsAllocationFrom,
        assetsAllocationTo,
        rangeProportion,
        date,
        isFilter,
      },
    });
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObject(data: any) {
    return data !== null && (data.start != null || data.end != null);
  }

  /**
   * @param rangeProportion
   * @param date
   */
  checkStatusFilter(rangeProportion: IRangeFilter, date: IRangeFilter) {
    return (
      !this.isSelectAll ||
      this.customerOptions.length !== this.listCustomerOptions.length ||
      this.allocationAssetFrom.length !== this.listAllocationAssetFrom.length ||
      this.allocationAssetTo.length !== this.listAllocationAssetTo.length ||
      this.checkHasValueInObject(rangeProportion) ||
      this.checkHasValueInObject(date)
    );
  }

  /**
   * DefaultFilter
   */
  defaultFilter() {
    this.dialogRef.close({
      type: 'default',
    });
  }
}
