.allocation-recommendations-filter-wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  .allocation-rcm-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid var(--color--other--divider);
    img[alt='x-cross'] {
      cursor: pointer;
    }
  }

  .allocation-recommendations-body {
    flex: 1;
    overflow: auto;
    .title {
      padding: 12px 24px;
    }

    .searchbox-wrap {
      padding: 8px 24px 16px;
      display: flex;
      flex-direction: column;
      gap: 17px;
      overflow: hidden;
      border-bottom: 1px solid var(--color--other--divider);

      .search-box {
        display: flex;
        position: relative;
        .input-search {
          padding: 10px 16px;
          width: 100%;
          padding-left: 32px;
        }

        .icon-search-cls {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 10px;
        }
      }
    }

    .option-list-cls {
      height: 164px;
      overflow: auto;
      display: flex;
      flex-direction: column;
      gap: 16px;
      .checkbox-cls {
        ::ng-deep {
          .mdc-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
          }
        }

        .img-cls {
          width: 18px;
          height: 18px;
          object-fit: contain;
          vertical-align: middle;
          border-radius: 50%;
        }
      }
    }

    .box-calendar {
      padding: 8px 24px 16px 24px;
      display: flex;
      gap: 24px;
      border-bottom: 1px solid var(--color--other--divider);

      .content-from,
      .content-to {
        width: 50%;
        display: flex;
        flex-direction: column;
        gap: 8px;
        .input-wrapper {
          position: relative;
          input {
            padding: 10px 16px 10px 12px;
            border-radius: 8px;
            height: 40px;
            width: 100%;
            border: 1px solid var(--Neutral-100, #dbdee0);
          }
          img {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 10px;
            cursor: pointer;
          }
        }
      }
    }

    .dropdown-container {
      padding: 12px 24px 16px;
      display: flex;
      align-items: center;
      gap: 8px;
      border-bottom: 1px solid var(--color--other--divider);

      .customer-dropdown {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      .customer-input-wrapper {
        cursor: pointer;
        position: relative;
        .customer-input {
          padding: 8px 16px;
          height: 48px;
          border-radius: 8px;
          border: 1px solid var(--Neutral-100, #dbdee0);
          background: var(--Colors-Grey-White, #fff);
          width: 100%;
          cursor: pointer;
        }

        img {
          width: 18px;
          height: 18px;
          position: absolute;
          right: 20px;
          top: 33%;
        }
      }
    }

    .alllocation-assets-container {
      display: flex;
      gap: 8px;
      padding: 12px 24px 16px;
      align-items: center;
      border-bottom: 1px solid var(--color--other--divider);

      .allocation-dropdown {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 50%;
        .allocation-input-wrapper {
          position: relative;
          .allocation-input {
            padding: 8px 16px;
            height: 48px;
            border-radius: 8px;
            border: 1px solid var(--Neutral-100, #dbdee0);
            background: var(--Colors-Grey-White, #fff);
            width: 100%;
            cursor: pointer;
          }

          img {
            width: 18px;
            height: 18px;
            position: absolute;
            right: 20px;
            top: 33%;
            cursor: pointer;
          }
        }
      }
    }

    .allocation-proportion-title {
      padding: 12px 24px;
    }

    .allocation-prportion-container {
      display: flex;
      gap: 8px;
      align-items: cemter;
      padding: 12px 24px 16px;
      border-bottom: 1px solid var(--color--other--divider);

      .allocation-proportion-from,
      .allocation-proportion-to {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 50%;
        .proportion-input-wrapper {
          position: relative;
          .proportion-input {
            height: 40px !important;
            padding: 10px 16px;
            height: 48px;
            border-radius: 8px;
            border: 1px solid var(--Neutral-100, #dbdee0);
            background: var(--Colors-Grey-White, #fff);
            width: 100%;
          }
        }
      }
    }
  }

  .allocation-recommendations-filter-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--color--other--divider);
    padding: 16px 24px;

    .btn {
      padding: 12px 16px;
      border-radius: 8px;
      border: 1px solid var(--color--other--divider);
    }

    .default {
      background-color: var(--color--neutral--white);
    }

    .apply {
      background-color: var(--color--brand--500);
      color: var(--color--neutral--white);
    }
  }
}
