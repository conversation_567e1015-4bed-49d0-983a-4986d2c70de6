import { Component, Inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { DestroyService, DialogService } from 'src/app/core/services';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';

@Component({
  selector: 'app-tooltip-list-customer',
  templateUrl: './tooltip-list-customer.component.html',
  styleUrl: './tooltip-list-customer.component.scss',
})
export class TooltipListCustomerComponent {
  list!: any;

  constructor(
    @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly dialogService: DialogService,
    private readonly _destroy: DestroyService,
    private readonly store: Store
  ) {
    this.list = this.popoverRef.componentConfig.list;
  }
}
