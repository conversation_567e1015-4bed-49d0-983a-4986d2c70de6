<div class="closed-recommendations-filter-wrap">
  <div class="closed-rcm-header">
    <div class="title-cls typo-body-14">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="box-body-container">
    <form [formGroup]="closeRcmForm" class="closed-rcm-content">
      <!-- Mã CK -->
      <div class="title typo-body-15">{{ 'MES-157' | translate }}</div>
      <div class="searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="listOfStocks?.length; else noResultsStoke"
          [id]="stokeId"
          [key]="stokeKey"
          [items]="listOfStocks"
          [noResultsMessage]="'MES-585' | translate"
          [selectedKeys]="data.filterOptions.stockCodes ?? []"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [warningMessage]="'MES-668'"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-670'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-14' | translate"
          class="closed-re-filter"
          [searchKeys]="['value', 'stoke']"
          [displayFn]="displayStokeFilter"
          (invalidSelection)="validateStockFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultsStoke>
          <div class="typo-body-15 empty-message-global closed-re-cls">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- Thời gian nắm giữ -->
      <div class="title typo-body-15 closed-re-cls">{{ 'MES-208' | translate }} (ngày)</div>
      <div class="closed-re-cls holding-period-content">
        <div class="closed-re-cls content-from">
          <div class="closed-re-cls from-label typo-body-11">{{ 'MES-209' | translate }}</div>
          <app-form-control>
            <input
              formControlName="startDateHold"
              [mask]="'0*'"
              [allowNegativeNumbers]="true"
              class="closed-re-cls holding-period-input typo-field-5"
              [placeholder]="0"
            />
          </app-form-control>
        </div>

        <div class="closed-re-cls content-to">
          <div class="closed-re-cls to-label typo-body-11">{{ 'MES-210' | translate }}</div>
          <app-form-control>
            <input
              formControlName="endDateHold"
              [mask]="'0*'"
              [allowNegativeNumbers]="true"
              class="closed-re-cls holding-period-input typo-field-5"
              [placeholder]="'∞'"
            />
          </app-form-control>
        </div>
      </div>

      <!-- Ngày đóng khuyến nghị -->
      <div class="title typo-body-15">{{ 'MES-592' | translate }}</div>
      <div class="rcm-day-content">
        <div class="content-from">
          <div class="from-label typo-body-11">{{ 'MES-209' | translate }} ngày</div>
          <div class="input-wrapper">
            <app-form-control>
              <input
                type="text"
                class="calendar-input typo-body-12"
                matInput
                [matDatepicker]="dateFrom"
                [placeholder]="'DD/MM/YYYY'"
                formControlName="dateStart"
                dateInput
                [max]="
                  closeRcmForm.get('dateEnd')?.value?._d
                    ? closeRcmForm.get('dateEnd')?.value?._d
                    : closeRcmForm.get('dateEnd')?.value
                    ? closeRcmForm.get('dateEnd')?.value
                    : null
                "
              />
            </app-form-control>
            <img (click)="dateFrom.open()" src="./assets/icons/calendar.svg" alt="" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              panelClass="calendar-cls"
              #dateFrom
            ></mat-datepicker>
          </div>
        </div>

        <div class="content-to">
          <div class="to-label typo-body-11">{{ 'MES-210' | translate }} ngày</div>
          <div class="input-wrapper">
            <app-form-control>
              <input
                type="text"
                class="calendar-input typo-body-12"
                matInput
                [matDatepicker]="dateTo"
                formControlName="dateEnd"
                [placeholder]="'DD/MM/YYYY'"
                dateInput
                [min]="
                  closeRcmForm.get('dateStart')?.value?._d
                    ? closeRcmForm.get('dateStart')?.value?._d
                    : closeRcmForm.get('dateStart')?.value
                    ? closeRcmForm.get('dateStart')?.value
                    : null
                "
              />
            </app-form-control>
            <img (click)="dateTo.open()" src="./assets/icons/calendar.svg" alt="" />
            <mat-datepicker
              [calendarHeaderComponent]="headerCalendar"
              panelClass="calendar-cls closed-re-cls"
              #dateTo
            ></mat-datepicker>
          </div>
        </div>
      </div>

      <!-- +/- tiềm năng -->
      <div class="closed-re-cls title typo-body-15">{{ 'MES-214' | translate }} (%)</div>
      <div class="closed-re-cls holding-period-content">
        <div class="closed-re-cls content-from">
          <div class="closed-re-cls from-label typo-body-11">{{ 'MES-209' | translate }}</div>
          <app-form-control>
            <input
              class="closed-re-cls holding-period-input typo-field-5"
              [mask]="'separator'"
              [allowNegativeNumbers]="true"
              formControlName="startPotential"
              [placeholder]="'-∞'"
            />
          </app-form-control>
        </div>

        <div class="closed-re-cls content-to">
          <div class="closed-re-cls to-label typo-body-11">{{ 'MES-210' | translate }}</div>
          <app-form-control>
            <input
              class="closed-re-cls holding-period-input typo-field-5"
              [mask]="'separator'"
              [allowNegativeNumbers]="true"
              formControlName="endPotential"
              [placeholder]="'∞'"
            />
          </app-form-control>
        </div>
      </div>

      <!-- +/- đã ghi nhận -->
      <div class="closed-re-cls title typo-body-15">{{ 'MES-346' | translate }} (%)</div>
      <div class="closed-re-cls holding-period-content">
        <div class="closed-re-cls content-from">
          <div class="closed-re-cls from-label typo-body-11">{{ 'MES-209' | translate }}</div>
          <app-form-control>
            <input
              class="closed-re-cls holding-period-input typo-field-5"
              [mask]="'separator'"
              [allowNegativeNumbers]="true"
              formControlName="startRecord"
              [placeholder]="'-∞'"
            />
          </app-form-control>
        </div>

        <div class="closed-re-cls content-to">
          <div class="closed-re-cls to-label typo-body-11">{{ 'MES-210' | translate }}</div>
          <app-form-control>
            <input
              class="closed-re-cls holding-period-input typo-field-5"
              [mask]="'separator'"
              [allowNegativeNumbers]="true"
              formControlName="endRecord"
              [placeholder]="'∞'"
            />
          </app-form-control>
        </div>
      </div>
    </form>

    <div class="closed-re-cls right-box">
      <!-- KN tới Khách hàng -->
      <div class="closed-re-cls title typo-body-15">{{ 'MES-160' | translate }}</div>
      <div class="closed-re-cls searchbox-wrap">
        <app-virtual-scroll-list
          class="closed-re-filter"
          *ngIf="customers?.length; else noResultCustomer"
          [id]="customerKey"
          [key]="customerKey"
          [items]="customers"
          [selectedKeys]="data.filterOptions.customer ?? []"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-669'"
          [warningMessage]="'MES-668'"
          [selectAllLabel]="'MES-58' | translate"
          [searchPlaceholder]="'MES-295' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['accountNumber', 'customerName']"
          [displayFn]="displayCustomerFilter"
          (invalidSelection)="validateFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultCustomer>
          <div class="closed-re-cls typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>

      <!-- KN tới Nhóm khách hàng -->
      <div class="closed-re-cls title typo-body-15">{{ 'MES-161' | translate }}</div>
      <div class="closed-re-cls searchbox-wrap">
        <app-virtual-scroll-list
          *ngIf="customerGroups?.length; else noResultCustomerGroup"
          [id]="customerGroupKey"
          [key]="customerGroupKey"
          [items]="customerGroups"
          [selectedKeys]="data.filterOptions.customerGroup ?? []"
          [maxSelectedItems]="MAX_ITEM_SELECTED"
          [searchPlaceholder]="'MES-14' | translate"
          [translateParams]="{ maxItem: MAX_ITEM_SELECTED }"
          [messageSelectionComplete]="'MES-669'"
          [warningMessage]="'MES-668'"
          [selectAllLabel]="'MES-58' | translate"
          [noResultsMessage]="'MES-585' | translate"
          [searchKeys]="['groupCode', 'value']"
          [displayFn]="displayCustomerGroupFilter"
          (invalidSelection)="validateCustomerGroupFilterValue($event)"
        ></app-virtual-scroll-list>

        <ng-template #noResultCustomerGroup>
          <div class="closed-re-cls typo-body-15 empty-message-global">{{ 'MES-585' | translate }}</div>
        </ng-template>
      </div>
    </div>
  </div>

  <div class="closed-re-cls footer-filter">
    <div (click)="defaultFilterCl()" class="closed-re-cls btn default typo-button-3">{{ 'MES-20' | translate }}</div>
    <button
      [disabled]="
        closeRcmForm.invalid ||
        closeRcmForm.get('dateStart')?.invalid ||
        closeRcmForm.get('dateEnd')?.invalid ||
        isDisableApply ||
        isDisableCustomerGroupApply ||
        isDisableStockApply
      "
      (click)="applyFilter()"
      class="closed-re-cls btn apply typo-button-3"
    >
      {{ 'MES-21' | translate }}
    </button>
  </div>
</div>
