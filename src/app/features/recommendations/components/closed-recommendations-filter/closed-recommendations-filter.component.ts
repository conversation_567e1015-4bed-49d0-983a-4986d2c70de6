import { ChangeDetectorRef, Component, Inject, OnInit, Optional, QueryList, ViewChildren } from '@angular/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, provideNativeDateAdapter } from '@angular/material/core';
import { take, tap } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { DatePickerNavigationFullDateComponent } from 'src/app/shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { IOptionList } from 'src/app/shared/models/dropdown-item.model';
import { IItemStoke, IRangeFilter } from '../../models/recommendations';
import { AbstractControl, FormBuilder, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MY_DATE_FORMAT } from 'src/app/shared/constants/date-picker';
import { Store } from '@ngrx/store';
import {
  selectAllAccountNumberListByBrokerView$,
  selectCustomerGroupList$,
  selectAllStockList$,
} from 'src/app/stores/shared/shared.selectors';
import { IAllAccountNumber } from 'src/app/shared/models/global';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { MAX_ITEM_SELECTED } from 'src/app/shared/constants/global';

/**
 * ClosedRecommendationsFilterComponent
 */
@Component({
  selector: 'app-closed-recommendations-filter',
  templateUrl: './closed-recommendations-filter.component.html',
  styleUrl: './closed-recommendations-filter.component.scss',
  providers: [
    provideNativeDateAdapter(),
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
  ],
})
export class ClosedRecommendationsFilterComponent implements OnInit {
  @ViewChildren(VirtualScrollListComponent) virtualScroll!: QueryList<VirtualScrollListComponent>;

  headerCalendar = DatePickerNavigationFullDateComponent;

  closeRcmForm!: FormGroup;

  readonly customerKey = 'accountNumber';

  customers: IAllAccountNumber[] = [];

  readonly customerGroupKey = 'value';

  customerGroups: IOptionList[] = [];

  readonly stokeKey = 'value';
  readonly stokeId = 'stoke';

  listOfStocks: IItemStoke[] = [];

  MAX_ITEM_SELECTED = MAX_ITEM_SELECTED;

  isDisableStockApply = false;

  isDisableApply = false;

  isDisableCustomerGroupApply = false;

  /**
   * Constructor
   * @param _destroy
   * @param popoverService
   * @param data
   * @param dialogRef
   * @param popoverRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<ClosedRecommendationsFilterComponent>,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly cdf: ChangeDetectorRef,
    private readonly fb: FormBuilder,
    private readonly store: Store
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    this.initForm();

    this.patchValueToForm();
  }

  private patchValueToForm() {
    const { date, rangeDateHold, rangePotential, rangeRecord } = this.data.filterOptions;

    this.closeRcmForm.patchValue({
      startDateHold: rangeDateHold.start,
      endDateHold: rangeDateHold.end,
      startPotential: rangePotential.start,
      endPotential: rangePotential.end,
      startRecord: rangeRecord.start,
      endRecord: rangeRecord.end,
      dateStart: date.start,
      dateEnd: date.end,
    });
  }

  private loadCustomersCl() {
    this.store
      .select(selectAllAccountNumberListByBrokerView$)
      .pipe(
        take(1),
        tap((customersCl) => {
          this.customers = customersCl;
        })
      )
      .subscribe();
  }

  private loadCustomerGroupListCl() {
    this.store
      .select(selectCustomerGroupList$)
      .pipe(
        take(1),
        tap((groupsCl) => {
          this.customerGroups = groupsCl
            .filter((t) => t.isShow)
            .map((g) => ({
              label: `${g.groupCode} : ${g.name}`,
              value: g.name,
              groupCode: g.groupCode,
              id: g.id,
            }));
        })
      )
      .subscribe();
  }

  private loadStokeListCl() {
    this.store
      .select(selectAllStockList$)
      .pipe(take(1))
      .subscribe((allStocksCl) => {
        this.listOfStocks = allStocksCl.map((t) => ({
          value: t.id,
          stoke: t.stock,
        }));
      });
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.loadCustomersCl();
    this.loadCustomerGroupListCl();
    this.loadStokeListCl();
  }

  /**
   * initForm
   */
  private initForm() {
    this.closeRcmForm = this.fb.group(
      {
        startDateHold: [null],
        endDateHold: [null],
        startPotential: [null],
        endPotential: [null],
        startRecord: [null],
        endRecord: [null],
        dateStart: [null],
        dateEnd: [null],
      },

      {
        validator: this.customValidate,
      }
    );
  }

  /**
   * customValidate
   */
  customValidate(group: FormGroup) {
    const startDateHold = group.get('startDateHold');
    const endDateHold = group.get('endDateHold');
    const startPotential = group.get('startPotential');
    const endPotential = group.get('endPotential');
    const startRecord = group.get('startRecord');
    const endRecord = group.get('endRecord');
    const dateStart = group.get('dateStart');
    const dateEnd = group.get('dateEnd');

    const validFromToValueCl = (startControlCl: any, endControlCl: any) => {
      if (startControlCl && endControlCl) {
        const start = startControlCl.value;
        const end = endControlCl.value;
        if (+end < +start && end !== null && end !== '') {
          return endControlCl.setErrors({ validFromTo: true });
        } else {
          endControlCl?.setErrors(null);
        }
      }
    };

    const dateValidatorCl = (controlCl: AbstractControl | null): void => {
      if (!controlCl) return;

      const value = controlCl.value?._d ?? controlCl.value;
      const currentErrors = controlCl.errors || {};

      if (!value || value === '') {
        const { invalidDate, ...remainingErrors } = currentErrors;
        controlCl.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
        return;
      }

      const parsedDate = typeof value === 'string' ? new Date(value) : value;
      const isValidDate = parsedDate instanceof Date && !isNaN(parsedDate.getTime());

      if (!isValidDate) {
        controlCl.setErrors({ ...currentErrors, invalidDate: true });
      } else {
        const { invalidDate, ...remainingErrors } = currentErrors;
        controlCl.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
      }
    };

    const dateRangeValidatorCl = (startControlCl: AbstractControl | null, endControlCl: AbstractControl | null): void => {
      if (!startControlCl || !endControlCl) return;

      const startErrors = startControlCl.errors || {};
      const endErrors = endControlCl.errors || {};

      if (endErrors['matDatepickerMin']) {
        const { matDatepickerMax, ...remainingErrors } = startErrors;
        startControlCl.setErrors(Object.keys(remainingErrors).length > 0 ? remainingErrors : null);
      }
    };

    validFromToValueCl(startDateHold, endDateHold);
    validFromToValueCl(startPotential, endPotential);
    validFromToValueCl(startRecord, endRecord);

    dateValidatorCl(dateStart);
    dateValidatorCl(dateEnd);

    dateRangeValidatorCl(dateStart, dateEnd);
  }

  validateStockFilterValue(invalid: boolean) {
    this.isDisableStockApply = invalid;
  }

  validateFilterValue(invalid: boolean) {
    this.isDisableApply = invalid;
  }

  validateCustomerGroupFilterValue(invalid: boolean) {
    this.isDisableCustomerGroupApply = invalid;
  }

  /**
   * ApplyFilter
   */
  applyFilter() {
    if (this.isDisableApply || this.isDisableCustomerGroupApply || this.isDisableStockApply) return;

    const { startDateHold, endDateHold, startPotential, endPotential, startRecord, endRecord, dateStart, dateEnd } =
      this.closeRcmForm.value;
    const getVirtualScrollByIdCl = (id: string) => {
      return this.virtualScroll.find((component) => component.id === id);
    };
    const customer = ((getVirtualScrollByIdCl(this.customerKey)?.getChecked() as IAllAccountNumber[]) ?? []).map(
      (c) => c.accountNumber
    );

    const stockCodes = ((getVirtualScrollByIdCl(this.stokeId)?.getChecked() as IItemStoke[]) ?? []).map((c) => c.value);

    const customerGroup = ((getVirtualScrollByIdCl(this.customerGroupKey)?.getChecked() as IOptionList[]) ?? []).map(
      (c) => c.value as string
    );
    const rangeDateHold = {
      start: startDateHold,
      end: endDateHold,
    };
    const rangePotential = {
      start: startPotential,
      end: endPotential,
    };

    const rangeRecord = {
      start: startRecord,
      end: endRecord,
    };

    const date = {
      start: dateStart?._d ?? dateStart,
      end: dateEnd?._d ?? dateEnd,
    };

    const isFilter = this.checkStatusFilter(
      stockCodes,
      customer,
      customerGroup,
      rangeDateHold,
      rangePotential,
      rangeRecord,
      date
    );

    this.dialogRef.close({
      type: 'save',
      optionFilter: {
        stockCodes,
        customer,
        customerGroup,
        rangeRecord,
        rangeDateHold,
        date,
        rangePotential,
        isFilter,
      },
    });
  }

  /**
   * CheckStatusFilter
   * @param rangeDateHold
   * @param rangePotential
   * @param date
   * @returns {boolean} true / false
   */
  checkStatusFilter(
    stockCodes: string[],
    customer: string[],
    customerGroup: string[],
    rangeDateHold: IRangeFilter,
    rangePotential: IRangeFilter,
    rangeRecord: IRangeFilter,
    date: IRangeFilter
  ) {
    const isStokeFilter = stockCodes.length === 0;
    const isCustomerFilter = customer.length === 0;
    const isCustomerGroupFilter = customerGroup.length === 0;
    return (
      !isStokeFilter ||
      !isCustomerFilter ||
      !isCustomerGroupFilter ||
      this.checkHasValueInObject(rangeDateHold) ||
      this.checkHasValueInObject(rangePotential) ||
      this.checkHasValueInObject(rangeRecord) ||
      this.checkHasValueInObject(date)
    );
  }

  /**
   * DefaultFilter
   */
  defaultFilterCl() {
    this.dialogRef.close({
      type: 'default',
    });
  }

  /**
   * CheckHasValueInObject
   * @param data
   * @returns {boolean} boolean
   */
  checkHasValueInObject(dataCl: any) {
    return !!dataCl && ((dataCl.start != null && dataCl.start !== '') || (dataCl.end != null && dataCl.end !== ''));
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerFilter(customerCl: IAllAccountNumber): string {
    if (!customerCl) return '';
    return `${customerCl.accountNumber} - ${customerCl.customerName}`;
  }

  /**
   * Format display text for each customer in the list
   * @param customer Customer object to format
   * @returns Formatted display string
   */
  displayCustomerGroupFilter(groupCl: IOptionList): string {
    if (!groupCl) return '';
    return `${groupCl.label}`;
  }

  displayStokeFilter(stokeCl: IItemStoke) {
    if (!stokeCl) return '';
    return `${stokeCl.value} - ${stokeCl.stoke}`;
  }
}
