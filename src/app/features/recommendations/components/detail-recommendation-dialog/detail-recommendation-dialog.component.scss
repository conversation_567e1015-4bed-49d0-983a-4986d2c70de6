.detail-recommendation-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 16px 24px;
  border-bottom: 1px solid var(--color--other--divider);

  .img-cls {
    cursor: pointer;
  }
}

.body {
  flex: 1;
  padding: 24px;
  border-bottom: 1px solid var(--color--other--divider);
  overflow: auto;

  .content {
    padding: 16px 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    background-color: var(--color--background--1);
    border-radius: 8px;

    .flex-row {
      display: flex;
      flex-direction: row;
    }

    .flex-column {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .item {
      display: flex;
      align-items: center;

      &.flex-1 {
        flex: 1;
      }

      .left,
      .right {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .stock {
          color: var(--color--brand--500);
        }

        .status {
          padding: 2px 12px;
          border-radius: 16px;
          width: fit-content;

          &.close-cls {
            background-color: var(--color--neutral--100);
          }

          &.following-cls {
            background-color: var(--color--accents--orange);
          }

          &.opened-cls {
            background-color: var(--color--accents--green);
          }
        }
      }
    }

    .customer-info {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }
}

.footer {
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: center;

  .btn-close {
    cursor: pointer;
    padding: 12px 16px;
    border: 1px solid var(--color--other--divider);
    width: 160px;
    text-align: center;
    border-radius: 8px;

    &:hover {
      background-color: var(--color--brand--500);
      color: #fff;
      transition: all 0.3s ease-in-out;
    }
  }
}

.label {
  color: var(--color--text-vibrant--secondary);
}

.positive-class {
  color: var(--color--accents--green);
}

.negative-class {
  color: var(--color--accents--red);
}

.zero-class {
  color: var(--color--accents--yellow);
}

.delete-group {
  color: var(--color--danger--600);
}
