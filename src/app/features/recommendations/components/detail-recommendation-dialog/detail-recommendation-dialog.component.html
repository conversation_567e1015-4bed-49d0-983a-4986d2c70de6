<div class="detail-recommendation-container">
  <div class="header">
    <div class="title typo-body-14">{{ 'MES-129' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="body">
    <div class="content">
      <div class="item">
        <!-- M<PERSON> khuyến nghị -->
        <div class="left">
          <div class="label typo-field-2">{{ 'MES-343' | translate }}</div>
          <div class="stock typo-body-12">{{ stockCode }}</div>
        </div>

        <!-- Trạng thái -->
        <div class="right">
          <div class="label typo-field-2">{{ 'MES-221' | translate }}</div>
          <div
            class="status typo-body-12"
            [ngClass]="{
              'close-cls': status === ERecommendationStatus.CLOSED,
              'following-cls': status === ERecommendationStatus.WATCHING,
              'opened-cls': status === ERecommendationStatus.OPENED
            }"
          >
            {{ RECOMMEND_STATUS_LABEL[status] }}
          </div>
        </div>
      </div>

      <div class="item" *ngIf="status === ERecommendationStatus.OPENED">
        <!-- Ngày tạo khuyến nghị -->
        <div class="left">
          <div class="label typo-field-2">{{ 'MES-582' | translate }}</div>
          <div class="typo-body-12">{{ recommendDate | falsyToHyphen }}</div>
        </div>
      </div>

      <div
        [ngClass]="{
          'flex-row': status === ERecommendationStatus.OPENED,
          'flex-column': status !== ERecommendationStatus.OPENED
        }"
      >
        <div class="item abc" [ngClass]="{ 'flex-1': status === ERecommendationStatus.OPENED }">
          <!-- Ngày mở khuyến nghị -->
          <div class="left">
            <!-- status === Đang theo dõi -->
            @if(status === ERecommendationStatus.WATCHING) {
            <div class="label typo-field-2">
              {{ 'MES-582' | translate }}
            </div>
            <div class="typo-body-12">
              {{ recommendDate !== null && recommendDate !== undefined ? recommendDate : '-' }}
            </div>
            } @else {
            <div class="label typo-field-2">{{ 'MES-335' | translate }}</div>
            <div class="typo-body-12">
              {{ recommendOpenDate !== null && recommendOpenDate !== undefined ? recommendOpenDate : '-' }}
            </div>
            }

            <!-- <div class="typo-body-12">
              {{ recommendDate !== null && recommendDate !== undefined ? recommendDate : '-' }}
            </div> -->
          </div>

          <!-- Giá Khuyến Nghị -->
          <div *ngIf="status === ERecommendationStatus.WATCHING" class="right">
            <div class="label typo-field-2">{{ 'MES-590' | translate }}</div>
            <div class="typo-body-12">
              {{ recommendPrice }}
            </div>
          </div>

          <!-- Ngày đóng khuyến nghị -->
          <div *ngIf="status === ERecommendationStatus.CLOSED" class="right">
            <div class="label typo-field-2">Ngày {{ 'MES-344' | translate }}</div>
            <div class="typo-body-12">{{ detailData.recommendCloseDate | falsyToHyphen }}</div>
          </div>
        </div>

        <div
          *ngIf="status !== ERecommendationStatus.WATCHING"
          class="item"
          [ngClass]="{ 'flex-1': status === ERecommendationStatus.OPENED }"
        >
          <!-- Giá mở khuyến nghị -->
          <div class="left">
            <div class="label typo-field-2">{{ 'MES-136' | translate }}</div>
            <div class="typo-body-12">
              {{ openPrice }}
            </div>
          </div>

          <!-- Giá đóng khuyến nghị -->
          <div *ngIf="status !== ERecommendationStatus.OPENED" class="right">
            <div class="label typo-field-2">Giá {{ 'MES-344' | translate }}</div>
            <div class="typo-body-12">
              {{ closePrice }}
            </div>
          </div>
        </div>
      </div>

      <div class="item">
        <!-- Giá mục tiêu -->
        <div class="left">
          <div class="label typo-field-2">{{ 'MES-133' | translate }}</div>
          <div class="typo-body-12">{{ targetPrice }}</div>
        </div>

        <!-- Giá cắt lỗ -->
        <div class="right">
          <div class="label typo-field-2">{{ 'MES-345' | translate }}</div>
          <div class="typo-body-12">
            {{ lossPrice }}
          </div>
        </div>
      </div>

      <div class="item" *ngIf="status !== ERecommendationStatus.WATCHING">
        <!-- +/- chưa ghi nhận -->
        <div class="left">
          <div class="label typo-field-2">{{ 'MES-218' | translate }} %</div>
          <div class="typo-body-12" [ngClass]="notRecord.class">{{ notRecord.formattedValue }}</div>
        </div>

        <!-- +/- tiềm năng -->
        <div class="right">
          <div class="label typo-field-2">{{ 'MES-214' | translate }} %</div>
          <div class="typo-body-12" [ngClass]="potential.class">{{ potential.formattedValue }}</div>
        </div>
      </div>

      <div class="item">
        <!-- +/- đã ghi nhận -->
        <div class="left" *ngIf="status !== ERecommendationStatus.WATCHING">
          <div class="label typo-field-2">{{ 'MES-346' | translate }} %</div>
          <div class="typo-body-12" [ngClass]="recorded.class">{{ recorded.formattedValue }}</div>
        </div>

        <!-- +/- tiềm năng -->
        <div class="left" *ngIf="status === ERecommendationStatus.WATCHING">
          <div class="label typo-field-2">{{ 'MES-214' | translate }} %</div>
          <div class="typo-body-12" [ngClass]="potential.class">{{ potential.formattedValue }}</div>
        </div>

        <!-- Thời gian nắm giữ -->
        <div class="right">
          @if(status === ERecommendationStatus.WATCHING) {
          <div class="label typo-field-2">{{ 'MES-421' | translate }}</div>
          } @else {
          <div class="label typo-field-2">{{ 'MES-208' | translate }}</div>
          }
          <div class="typo-body-12">
            {{ holdingPeriod !== null && holdingPeriod !== undefined ? holdingPeriod : '-' }}
            ngày
          </div>
        </div>
      </div>

      <div class="customer-info">
        <div class="label typo-field-2">{{ 'MES-347' | translate }}</div>
        <ng-container *ngFor="let group of recommendTo.group">
          <div *ngIf="group?.active; else deletedGroup" class="customer-group typo-body-12">
            {{ group.groupCode }}: {{ group.name }}
          </div>
          <ng-template #deletedGroup>
            <div class="delete-group typo-body-12">
              {{ group.groupCode }}: {{ group.name }} ({{ 'MES-649' | translate }})
            </div>
          </ng-template>
        </ng-container>

        @for(personal of recommendTo.personal; track personal ) {
        <div class="personal typo-body-12">{{ personal.id }}: {{ personal.name }}</div>
        }
      </div>
    </div>
  </div>

  <div class="footer">
    <div mat-dialog-close class="btn-close">{{ 'MES-74' | translate }}</div>
  </div>
</div>
