import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DestroyService, DialogService, LoadingService } from 'src/app/core/services';
import {
  CONVERT_RECOMMENDATION_STATUS_TO_NUMBER,
  ERecommendationStatus,
  RECOMMEND_STATUS_LABEL,
  RecommendationStatus,
} from '../../constants/recommendations';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { RecommendationService } from '../../services/recommendation.service';
import { IRecommendationListResponse } from '../../models/recommendations';
import { catchError, combineLatest, filter, of, takeUntil, tap } from 'rxjs';
import { Store } from '@ngrx/store';
import { selectAllAccountNumberListByBrokerView$ } from 'src/app/stores/shared/shared.selectors';

/**
 * DetailRecommendationDialogComponent
 */
@Component({
  selector: 'app-detail-recommendation-dialog',
  templateUrl: './detail-recommendation-dialog.component.html',
  styleUrl: './detail-recommendation-dialog.component.scss',
})
export class DetailRecommendationDialogComponent {
  stockCode = '';
  targetPrice!: string | number;
  openPrice!: string;
  closePrice!: string;
  lossPrice!: string;
  notRecord = { class: '', formattedValue: '' };
  potential = { class: '', formattedValue: '' };
  recorded = { class: '', formattedValue: '' };
  status!: number;
  recommendDate!: string;
  recommendOpenDate!: string;
  recommendCloseDate!: string;
  holdingPeriod!: number;
  recommendTo: {
    group: { name: string; id: string; active: boolean; groupId: string; groupCode: string }[];
    personal: { name: string; id: string }[];
  } = {
    group: [],
    personal: [],
  };
  recommendPrice!: string;

  RECOMMEND_STATUS_LABEL = RECOMMEND_STATUS_LABEL;

  customNumberFormat = customNumberFormat;
  ERecommendationStatus = ERecommendationStatus;

  detailData!: any;

  accountNumberMap: { [key: string]: string } = {};

  /**
   * constructor
   * @param data
   * @param dialogRef
   * @param dialogService
   * @param recommendationService
   * @param _destroy
   */
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: IRecommendationListResponse,
    public dialogRef: MatDialogRef<DetailRecommendationDialogComponent>,
    private readonly dialogService: DialogService,
    private readonly recommendationService: RecommendationService,
    private readonly _destroy: DestroyService,
    private readonly loadingService: LoadingService,
    private readonly store: Store
  ) {
    combineLatest([
      this.recommendationService.getDetailRecommendation(data.id),
      this.store.select(selectAllAccountNumberListByBrokerView$),
    ])
      .pipe(
        tap(() => this.loadingService.hide()),
        takeUntil(this._destroy),
        filter(([_, accounts]) => {
          return !!accounts?.length;
        }),
        catchError((err) => {
          this.loadingService.hide();
          return of(false);
        })
      )
      .subscribe((res) => {
        if (typeof res === 'boolean') return;
        const [detail, customers] = res;
        customers.forEach((c) => {
          const { accountNumber, customerName } = c;
          this.accountNumberMap[accountNumber] = customerName;
          return {
            customerGroup: accountNumber,
            customerName: customerName,
            isSelect: false,
          };
        });

        const personal: any[] = [];
        (detail.recommendToPersonal ?? []).forEach((d) => {
          if (this.accountNumberMap[d]) {
            personal.push({
              name: this.accountNumberMap[d],
              id: d,
            });
          }
        });
        this.detailData = {
          ...detail,
          recommendOpenDate: new Date(detail.openDate).toLocaleDateString('en-GB'),
          recommendCloseDate: new Date(detail.closeDate).toLocaleDateString('en-GB'),
          recommendDate: new Date(detail.recommendDate).toLocaleDateString('en-GB'),
          status: CONVERT_RECOMMENDATION_STATUS_TO_NUMBER[detail.status],
          stockCode: {
            code: detail.stockCode,
            name: detail.marketCode,
          },
          recommendTo: {
            group: (this.data?.recommendToGroup ?? []).map((d) => ({
              name: d.name,
              id: d.adviceToId,
              active: d.isActive,
              groupId: d.groupId,
              groupCode: d.groupCode,
            })),
            personal,
          },
          potential: detail.potential * 100 || data.potential,
          recorded: detail.recorded * 100 || data.recorded,
          notRecord: detail.notRecord ? detail.notRecord * 100 : data.notRecord,
          recommendPrice: {
            ...detail.recommendPrice,
            to: detail.status === RecommendationStatus.CLOSED ? detail.openPrice : detail.recommendPrice.to,
          },
        };

        if (this.detailData) {
          this.transformView(this.detailData);
        }
      });
  }

  transformView(data: any) {
    this.stockCode = `${data.stockCode.code.toUpperCase()} : ${data.stockCode.name.toUpperCase()}`;

    const fromFormatted =
      data.targetPrice.from !== null ? customNumberFormat(data.targetPrice.from, 'decimal', 'en-US', 2) : '';
    const toFormatted =
      data.targetPrice.to !== undefined ? customNumberFormat(data.targetPrice.to, 'decimal', 'en-US', 2) : '';

    if (
      (data.targetPrice.from === undefined || data.targetPrice.from === 0) &&
      (data.targetPrice.to === undefined || data.targetPrice.to === 0)
    ) {
      this.targetPrice = '-';
    } else if (data.targetPrice.from === undefined || data.targetPrice.from === 0) {
      this.targetPrice = toFormatted;
    } else {
      this.targetPrice =
        fromFormatted && toFormatted ? `${fromFormatted} - ${toFormatted}` : fromFormatted || toFormatted || '';
    }

    this.openPrice = this.formatNumber(data.openPrice);
    this.closePrice = this.formatNumber(data.closePrice);
    this.lossPrice = this.formatNumber(data.lossPrice);
    this.notRecord = this.checkAndFormatNumber(data.notRecord);
    this.potential = this.checkAndFormatNumber(data.potential);
    this.recorded = this.checkAndFormatNumber(data.recorded);
    this.status = this.detailData.status;
    this.recommendDate = this.detailData.recommendDate;
    this.recommendOpenDate = this.detailData.recommendOpenDate;
    this.recommendCloseDate = this.detailData.recommendCloseDate;
    this.holdingPeriod = this.detailData.holdingPeriod;
    this.recommendTo = this.detailData.recommendTo;
    this.recommendPrice = `${this.formatNumber(data.recommendPrice.from)} - ${this.formatNumber(
      data.recommendPrice.to
    )}`;
  }

  formatNumber(value: number | undefined) {
    return value !== undefined ? `${value.toFixed(2)}` : '-';
  }

  checkAndFormatNumber(value: number | undefined) {
    if (value === undefined || isNaN(value)) {
      return { class: '', formattedValue: '-' };
    }
    const formattedValue = `${value.toFixed(2)}`;

    if (value > 0) {
      return { class: 'positive-class', formattedValue: `+${formattedValue} %` };
    } else if (value < 0) {
      return { class: 'negative-class', formattedValue: `${formattedValue} %` };
    } else {
      return { class: 'zero-class', formattedValue: `${formattedValue} %` };
    }
  }
}
