export const RECOMMEND_STATUS_LABEL: { [key: number]: string } = {
  0: 'ĐÃ ĐÓNG',
  1: 'ĐANG THEO DÕI',
  2: 'ĐÃ MỞ',
};

export const RECOMMEND_STATUS_CLASS: { [key: number]: string } = {
  0: 'closed',
  1: 'following',
  2: 'opened',
};

export const RECOMMEND_ALLOCATION_LABEL: { [key: number]: string } = {
  0: 'TIỀN MẶT',
  1: 'CỔ PHIẾU',
  2: 'TRÁI PHIẾU',
  3: 'PHÁI SINH',
  4: 'CHỨNG CHỈ QUỸ',
};

export const RECOMMEND_ALLOCATION_CLASS: { [key: number]: string } = {
  0: 'cash',
  1: 'stock',
  2: 'bonds',
  3: 'derivative',
  4: 'fund-certificates',
};

export const RECOMMEND_ALLOCATION_PROPROTION: { [key: number]: string } = {
  0: 'tiền mặt',
  1: 'cổ phiếu',
  2: 'trái phiếu',
  3: 'phái sinh',
  4: 'CC quỹ',
};

export const CONVERT_RECOMMENDATION_TYPE_TO_LABEL: { [key: number]: string } = {
  0: 'CASH',
  1: 'STOCK',
  2: 'BOND',
  3: 'DERIVATIVE',
  4: 'CERTIFICATE',
};

export enum ERecommendationStatus {
  CLOSED,
  WATCHING,
  OPENED,
}

export const CONVERT_STATUS_RECOMMENDATION_TYPE_TO_LABEL: { [key: number]: string } = {
  [ERecommendationStatus.CLOSED]: 'CLOSED',
  [ERecommendationStatus.WATCHING]: 'WATCHING',
  [ERecommendationStatus.OPENED]: 'OPENED',
};

export enum RecommendationStatus {
  ALL = 'ALL',
  FOLLOWING = 'FOLLOWING',
  OPENED = 'OPENED',
  CLOSED = 'CLOSED',
}

export const CONVERT_RECOMMENDATION_STATUS_TO_NUMBER: { [key: string]: number } = {
  [RecommendationStatus.CLOSED]: 0,
  [RecommendationStatus.FOLLOWING]: 1,
  [RecommendationStatus.OPENED]: 2,
};
