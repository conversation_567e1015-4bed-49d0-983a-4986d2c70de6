import { Injectable } from '@angular/core';
import { ApiResponse } from 'src/app/core/models/api-response';
import { ApiService } from 'src/app/core/services';
import { map } from 'rxjs';
import { IRecommendationPayload } from 'src/app/shared/models/global';
import {
  IPayloadCloseAdvice,
  IPayLoadOpenAdivce,
  IPayloadUpdateFollowingAdvice,
  IPayloadUpdateOpenAdvice,
  IRecommendationListResponse,
} from '../models/recommendations';

@Injectable({
  providedIn: 'root',
})
export class RecommendationService {
  url = 'v1/customer';
  urlAdvice = 'v1/advice';

  /**
   *constructor
   * @param apiService apiService
   */
  constructor(private readonly apiService: ApiService) {}

  createRecommendation(payload: IRecommendationPayload) {
    return this.apiService.post<ApiResponse<boolean>>(`${this.urlAdvice}/stock/broker`, payload);
  }

  getListRecommendation(brokerCodes: string, search: string, tag: string) {
    const payload = {
      brokerCodes: brokerCodes.split(','),
      searchValue: search,
      tag,
    };

    return this.apiService
      .post<ApiResponse<IRecommendationListResponse[]>>(`${this.urlAdvice}/get-all`, payload)
      .pipe(map((res) => res.data));
  }

  closeAdvice(id: string, payload: IPayloadCloseAdvice) {
    return this.apiService.put<ApiResponse<boolean>>(`${this.urlAdvice}/stock/broker/close-advice/${id}`, payload);
  }

  openAdvice(id: string, payload: IPayLoadOpenAdivce) {
    return this.apiService.put<ApiResponse<boolean>>(`${this.urlAdvice}/stock/broker/open-advice/${id}`, payload);
  }

  cancelAdvice(id: string) {
    return this.apiService.delete<ApiResponse<boolean>>(`${this.urlAdvice}/stock/broker/follow-advice/${id}`);
  }
  /**
   * Sửa khuyến nghị đang theo dõi
   * @param {string} id
   * @param {IPayloadUpdateFollowingAdvice} payload
   */
  updateFollowingRecommendation(id: string, payload: IPayloadUpdateFollowingAdvice) {
    return this.apiService.put<ApiResponse<boolean>>(`${this.urlAdvice}/stock/broker/follow-advice/${id}`, payload);
  }

  /**
   * Sửa khuyến nghị đã mở
   * @param {string} id
   * @param {IPayloadUpdateOpenAdvice} payload
   */
  updateOpenedRecommendation(id: string, payload: IPayloadUpdateOpenAdvice) {
    return this.apiService.put<ApiResponse<boolean>>(
      `${this.urlAdvice}/stock/broker/update-open-advice/${id}`,
      payload
    );
  }

  getDetailRecommendation(id: string) {
    return this.apiService
      .get<ApiResponse<IRecommendationListResponse>>(`${this.urlAdvice}/stock/broker/detail-advice/${id}`)
      .pipe(map((res) => res.data));
  }
}
