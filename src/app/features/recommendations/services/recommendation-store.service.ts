import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class RecommendationStoreService {
  private readonly codesSelected = new BehaviorSubject<string[] | null>(null);

  public codesSelected$: Observable<string[] | null> = this.codesSelected.asObservable();

  constructor() {}

  codeListSendMessage(newValue: string[]): void {
    this.codesSelected.next(newValue);
  }

  getCodesSelected() {
    return this.codesSelected.getValue();
  }
}
