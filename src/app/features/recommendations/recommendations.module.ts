import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule } from '@angular/material/dialog';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatIconModule } from '@angular/material/icon';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';

import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { ActionBtnComponent } from 'src/app/shared/components/action-btn/action-btn.component';
import { SearchDropdownCustomComponent } from 'src/app/shared/components/search-dropdown-custom/search-dropdown-custom.component';
import { FilterComponent } from 'src/app/shared/components/filter/filter.component';
import { MatTabsModule } from '@angular/material/tabs';
import { SlideTagComponent } from 'src/app/shared/components/slide-tag/slide-tag.component';
import { MatTooltip } from '@angular/material/tooltip';
import { NumberOnlyDirective } from 'src/app/shared/directives/number-only/number-only.directive';
import { RecommendationsView } from './views/recommendations/recommendations.view';
import { AllRecommendationsContainer } from './containers/all-recommendations/all-recommendations.container';
import { RecommendationsRoutingModule } from './recommendations-routing.module';
import { OptionsRecommendationComponent } from './components/options-recommendation/options-recommendation.component';
import { FollowingRecommendationsContainer } from './containers/following-recommendations/following-recommendations.container';
import { OpenedRecommendationsContainer } from './containers/opened-recommedations/opened-recommendations.container';
import { ClosedRecommendationsContainer } from './containers/closed-recomendations/closed-recommendations.container';
import { AllocationRecommendationsContainer } from './containers/allocation-recommendations/allocation-recommendations.container';
import { NumberFormatPipe } from '../../shared/pipes/format-number/format-number.pipe';
import { AllRecommendationsFilterComponent } from './components/all-recommendations-filter/all-recommendations-filter.component';
import { RangeDatePickerNavigatorWithInputComponent } from 'src/app/shared/components/date-picker/range-date-picker-navigator-with-input/range-date-picker-navigator-with-input.component';
import { CalendarCustomComponent } from 'src/app/shared/components/calendar-custom/calendar-custom.component';
import { FollowingRecommendationsFilterComponent } from './components/following-recommendations-filter/following-recommendations-filter.component';
import { ClosedRecommendationsFilterComponent } from './components/closed-recommendations-filter/closed-recommendations-filter.component';
import { NgxMaskDirective } from 'src/app/shared/directives/mask/ngx-mask.directive';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { RECOMMENDATION_STATE_NAME } from './stores/recommendation.selectors';
import { RecommendationEffects } from './stores/recommendation.effect';
import { recommendationReducers } from './stores/recommendation.reducers';
import { AllocationRecommendationsFilterComponent } from './components/allocation-recommendations-filter/allocation-recommendations-filter.component';
import { DetailRecommendationDialogComponent } from './components/detail-recommendation-dialog/detail-recommendation-dialog.component';
import { FalsyToHyphenPipe } from 'src/app/shared/pipes/falsy-to-hyphen/falsy-to-hyphen.pipe';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { OpenRecommendationsFilterComponent } from './components/open-recommendations-filter/open-recommendations-filter.component';
import { FormControlComponent } from 'src/app/shared/components/form-control/form-control.component';
import { DateInputDirective } from 'src/app/shared/directives/date-input/date-input.directive';
import { TooltipListCustomerComponent } from './components/tooltip-list-customer/tooltip-list-customer.component';
import { VirtualScrollListComponent } from 'src/app/shared/components/virtual-scroll-list/virtual-scroll-list.component';
import { CreatePlaceOrderButton } from 'src/app/shared/components/create-place-order-button/create-place-order-button.component';

const SHARED = [
  GridComponent,
  ActionBtnComponent,
  DraggableListComponent,
  SearchDropdownCustomComponent,
  SlideTagComponent,
  FilterComponent,
  NumberOnlyDirective,
  RangeDatePickerNavigatorWithInputComponent,
  CalendarCustomComponent,
  FormControlComponent,
  DateInputDirective,
  VirtualScrollListComponent,
  CreatePlaceOrderButton,
];

const VIEWS = [RecommendationsView];

const CONTAINERS = [
  AllRecommendationsContainer,
  FollowingRecommendationsContainer,
  OpenedRecommendationsContainer,
  ClosedRecommendationsContainer,
  AllocationRecommendationsContainer,
];

const COMPONENTS = [
  OptionsRecommendationComponent,
  AllRecommendationsFilterComponent,
  FollowingRecommendationsFilterComponent,
  ClosedRecommendationsFilterComponent,
  AllocationRecommendationsFilterComponent,
  DetailRecommendationDialogComponent,
  OpenRecommendationsFilterComponent,
  TooltipListCustomerComponent,
];

const PIPES = [FalsyToHyphenPipe];

/**
 * Customer Module
 */
@NgModule({
  declarations: [...VIEWS, ...CONTAINERS, ...COMPONENTS],
  imports: [
    ...SHARED,
    ...PIPES,
    NgxMaskDirective,
    CommonModule,
    TranslateModule,
    RecommendationsRoutingModule,

    // Material Module
    MatTableModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    FormsModule,
    MatSelectModule,
    MatDividerModule,
    MatDatepickerModule,
    MatMenuModule,
    MatDialogModule,
    OverlayModule,
    MatIconModule,
    MatCheckboxModule,
    MatTabsModule,
    MatTooltip,
    NumberFormatPipe,
    ScrollingModule,
    // Store
    StoreModule.forFeature(RECOMMENDATION_STATE_NAME, recommendationReducers),
    EffectsModule.forFeature([RecommendationEffects]),
  ],
})
export class RecommendationsModule {}
