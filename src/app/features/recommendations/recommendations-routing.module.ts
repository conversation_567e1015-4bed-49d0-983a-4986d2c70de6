import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RecommendationsView } from './views/recommendations/recommendations.view';
import { AllRecommendationsContainer } from './containers/all-recommendations/all-recommendations.container';
import { FollowingRecommendationsContainer } from './containers/following-recommendations/following-recommendations.container';
import { OpenedRecommendationsContainer } from './containers/opened-recommedations/opened-recommendations.container';
import { ClosedRecommendationsContainer } from './containers/closed-recomendations/closed-recommendations.container';
import { AllocationRecommendationsContainer } from './containers/allocation-recommendations/allocation-recommendations.container';

const routes: Routes = [
  {
    path: '',
    component: RecommendationsView,
    children: [
      {
        path: '',
        redirectTo: 'all-recommendations',
        pathMatch: 'full',
      },
      {
        path: 'all-recommendations',
        component: AllRecommendationsContainer,
      },
      {
        path: 'recommendations-following',
        component: FollowingRecommendationsContainer,
      },
      {
        path: 'recommendations-opened',
        component: OpenedRecommendationsContainer,
      },
      {
        path: 'recommendations-closed',
        component: ClosedRecommendationsContainer,
      },
      {
        path: 'recommendations-allocation',
        component: AllocationRecommendationsContainer,
      },
    ],
  },
];
/**
 * Configures and manages asset routes.
 */
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class RecommendationsRoutingModule {}
