import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { LoadingService, MessageService } from 'src/app/core/services';
import { RecommendationService } from '../services/recommendation.service';
import {
  getListRecommendation,
  getListRecommendationFailed,
  getListRecommendationSuccess,
} from './recommendation.actions';
import { catchError, combineLatestWith, finalize, map, of, switchMap, tap } from 'rxjs';
import { selectSearch$, selectTagParam$ } from './recommendation.selectors';
import { SharedService } from 'src/app/shared/services/shared.service';
import { IRecommendationListResponse } from '../models/recommendations';
import { IInfoInitialOfStock } from 'src/app/shared/models/global';

/**
 * RecommendationEffects
 */
@Injectable()
export class RecommendationEffects {
  constructor(
    private readonly actions$: Actions,
    private readonly loadingService: LoadingService,
    private readonly store: Store,
    private readonly messageService: MessageService,
    private readonly recommendationService: RecommendationService,
    private readonly sharedService: SharedService
  ) { }

  getListRecommendation = createEffect(() => {
    return this.actions$.pipe(
      ofType(getListRecommendation),
      combineLatestWith(this.store.select(selectSearch$), this.store.select(selectTagParam$)),
      tap(() => this.loadingService.show()),
      switchMap(([_, searchValue, tagParam]) => {
        return this.recommendationService.getListRecommendation(_.id, searchValue || '', tagParam || '').pipe(
          switchMap((res) => {
            const codes = res.filter((d) => d.status !== 'CLOSED').map((t) => t.stockCode);
            if (codes.length) {
              const uniqueCode = [...new Set(codes)];
              return this.sharedService.getCurrentInfoInitialOfStock(uniqueCode).pipe(
                map((infoStock) => {
                  const recommendationList = this.customRecommendationList(res, infoStock);
                  this.loadingService.hide();
                  return getListRecommendationSuccess({ data: recommendationList });
                }),
                catchError((error) => {
                  this.messageService.error(error.message);
                  this.loadingService.hide();
                  return of(getListRecommendationFailed({ data: [] }));
                }),
                finalize(() => this.loadingService.hide())
              );
            } else {
              this.loadingService.hide();
              return of(getListRecommendationSuccess({ data: res }));
            }
          }),
          catchError((error) => {
            this.loadingService.hide();
            this.messageService.error(error.message);
            return of(getListRecommendationFailed({ data: [] }));
          })
        );
      })
    );
  });

  customRecommendationList(list: IRecommendationListResponse[], infoStock: IInfoInitialOfStock[]) {
    const tagStock = infoStock ? infoStock?.map((stock) => stock.code) : list.map((t) => t.stockCode);

    return list.map((item) => {
      const i = tagStock.findIndex((tag) => tag === item.stockCode);
      let notRecord: number | undefined;
      if (item.status !== 'CLOSED' && item.status === 'OPENED' && item.openPrice && i !== -1) {
        notRecord = (+infoStock[i].last / 1000 - +item.openPrice) / +item.openPrice;
        notRecord = notRecord * 100;
      }
      return {
        ...item,
        ...(item.status !== 'CLOSED' &&
          i !== -1 && {
          currentPrice: (+infoStock[i].last / 1000).toFixed(2).toString(),
        }),
        notRecord,
        ...(item.status !== 'CLOSED' &&
          i !== -1 && {
          referencePrice: (+infoStock[i].ref_price / 1000).toFixed(2).toString(),
        }),
      };
    });
  }
}
