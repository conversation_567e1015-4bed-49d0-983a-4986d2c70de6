import { createAction, props } from '@ngrx/store';
import {
  IFilterAllRecommendationParam,
  IFilterAllocationRecomendationsParam,
  IFilterClosingRecommnendationParam,
  IFilterFollowingRecommnendationParam,
  IFilterOpeningRecommnendationParam,
  IRecommendationListResponse,
  IUpdateRecommendation,
} from '../models/recommendations';

export const updateRecommendationData = createAction(
  '[Recommendation] Update recommendation data',
  props<{ data: IUpdateRecommendation }>()
);

export const setFilterAllRecommendation = createAction(
  '[All recommendation] set filter All recommendation',
  props<{ params: IFilterAllRecommendationParam }>()
);

export const resetFilterAllRecommendation = createAction(
  '[All recommendation] reset filter All recommendation default'
);

export const setFilterFollowingRecommendation = createAction(
  '[Following recommendation] set filter Following recommendation',
  props<{ params: IFilterFollowingRecommnendationParam }>()
);

export const resetFilterFollowingRecommendation = createAction(
  '[Following recommendation] reset filter Following recommendation default'
);

export const setFilterOpeningRecommendation = createAction(
  '[Opening recommendation] set filter Opening recommendation',
  props<{ params: IFilterOpeningRecommnendationParam }>()
);

export const resetFilterOpeningRecommendation = createAction(
  '[Opening recommendation] reset filter Opening recommendation default'
);

export const setFilterClosingRecommendation = createAction(
  '[Closing recommendation] set filter Closing recommendation',
  props<{ params: IFilterClosingRecommnendationParam }>()
);

export const resetFilterClosingRecommendation = createAction(
  '[Closing recommendation] reset filter Closing recommendation default'
);

export const setFilterAllocationRecommendations = createAction(
  '[Allocation recommendation] set filter Allocation recommendation',
  props<{ params: IFilterAllocationRecomendationsParam }>()
);

export const resetFilterAllocationRecommendation = createAction(
  '[Allocation Recommendation] reset filter Allocation recommendation default'
);

export const resetSearchTextValueRecommendations = createAction(
  '[Recommendation] reset search text value all recommendations'
);

// API
export const getListRecommendation = createAction('[Recommendation] get recommendation list', props<{ id: string }>());
export const getListRecommendationSuccess = createAction(
  '[Recommendation] get recommendation list success',
  props<{ data: IRecommendationListResponse[] }>()
);

export const getListRecommendationFailed = createAction(
  '[Recommendation] get recommendation list failed',
  props<{ data: IRecommendationListResponse[] }>()
);

export const getSearchValue = createAction(
  '[Recommendation] Get search recommendation value',
  props<{ search: string }>()
);

export const resetSearchValue = createAction('[Recommendation] reset search recommendation value');

export const getTagParams = createAction('[Recommendation] Get tag param recommendation', props<{ tag: string }>());

export const resetDataAllRecommendation = createAction('[Recommendation] reset data all recommendation');

export const resetDataFollowingRecommendation = createAction('[Recommendation] reset data following recommendation');

export const resetDataOpeningRecommendation = createAction('[Recommendation] reset data open recommendation');

export const resetDataClosingRecommendation = createAction('[Recommendation] reset data close recommendation');
