import { createReducer, on } from '@ngrx/store';
import * as RecommendationActions from './recommendation.actions';
import { IRecommendationState } from '../models/recommendations';

export const initialRecommnendationState: IRecommendationState = {
  editedRecommendationData: {
    customer: null,
    customerGroup: null,
    id: null,
    status: null,
    openPrice: null,
    closePrice: null,
    lossPrice: null,
    takeProfit: null,
    takeProfit1: null,
    takeProfit2: null,
  },
  filterAllRecommendation: {
    status: [0, 1, 2],
    stockCodes: null,
    customer: null,
    customerGroup: null,
    rangeDateHold: {
      start: null,
      end: null,
    },
    date: {
      start: null,
      end: null,
    },
    rangePotential: {
      start: null,
      end: null,
    },
    rangeNotRecord: {
      start: null,
      end: null,
    },
    rangeRecord: {
      start: null,
      end: null,
    },
    isFilter: false,
  },
  filterFollowingRecommendation: {
    stockCodes: null,
    customer: null,
    customerGroup: null,
    rangeDateHold: {
      start: null,
      end: null,
    },
    date: {
      start: null,
      end: null,
    },
    rangePotential: {
      start: null,
      end: null,
    },
    isFilter: false,
  },
  filterOpeningRecommendation: {
    stockCodes: null,
    customer: null,
    customerGroup: null,
    rangeDateHold: {
      start: null,
      end: null,
    },
    date: {
      start: null,
      end: null,
    },
    rangePotential: {
      start: null,
      end: null,
    },
    rangeNotRecord: {
      start: null,
      end: null,
    },
    isFilter: false,
  },
  filterClosingRecommendation: {
    stockCodes: null,
    customer: null,
    customerGroup: null,
    rangeDateHold: {
      start: null,
      end: null,
    },
    date: {
      start: null,
      end: null,
    },
    rangePotential: {
      start: null,
      end: null,
    },
    rangeRecord: {
      start: null,
      end: null,
    },
    isFilter: false,
  },
  filterAllocationRecomendations: {
    personSent: null,
    customer: null,
    date: {
      start: null,
      end: null,
    },
    assetsAllocationFrom: null,
    assetsAllocationTo: null,
    rangeProportion: {
      start: null,
      end: null,
    },
    isFilter: false,
  },
  filterSearchTextValueAllRecommendation: {
    searchText: null,
  },
  filterSearchTextValueFollowingRecommendation: {
    searchText: null,
  },

  // API
  recommendationList: [],
  search: '',
  tag: '',
};

export const recommendationReducers = createReducer<IRecommendationState>(
  initialRecommnendationState,

  on(
    RecommendationActions.updateRecommendationData,
    (state, action): IRecommendationState => ({
      ...state,
      editedRecommendationData: {
        ...action.data,
      },
    })
  ),

  on(
    RecommendationActions.setFilterAllRecommendation,
    (state, action): IRecommendationState => ({
      ...state,
      filterAllRecommendation: { ...action.params },
    })
  ),

  on(
    RecommendationActions.resetFilterAllRecommendation,
    (state): IRecommendationState => ({
      ...state,
      filterAllRecommendation: {
        ...initialRecommnendationState.filterAllRecommendation,
      },
    })
  ),

  on(
    RecommendationActions.setFilterFollowingRecommendation,
    (state, action): IRecommendationState => ({
      ...state,
      filterFollowingRecommendation: { ...action.params },
    })
  ),

  on(
    RecommendationActions.resetFilterFollowingRecommendation,
    (state): IRecommendationState => ({
      ...state,
      filterFollowingRecommendation: {
        ...initialRecommnendationState.filterFollowingRecommendation,
      },
    })
  ),

  on(
    RecommendationActions.setFilterOpeningRecommendation,
    (state, action): IRecommendationState => ({
      ...state,
      filterOpeningRecommendation: { ...action.params },
    })
  ),

  on(
    RecommendationActions.resetFilterOpeningRecommendation,
    (state): IRecommendationState => ({
      ...state,
      filterOpeningRecommendation: {
        ...initialRecommnendationState.filterOpeningRecommendation,
      },
    })
  ),

  on(
    RecommendationActions.setFilterClosingRecommendation,
    (state, action): IRecommendationState => ({
      ...state,
      filterClosingRecommendation: { ...action.params },
    })
  ),

  on(
    RecommendationActions.resetFilterClosingRecommendation,
    (state): IRecommendationState => ({
      ...state,
      filterClosingRecommendation: {
        ...initialRecommnendationState.filterClosingRecommendation,
      },
    })
  ),
  on(
    RecommendationActions.setFilterAllocationRecommendations,
    (state, action): IRecommendationState => ({
      ...state,
      filterAllocationRecomendations: { ...action.params },
    })
  ),
  on(
    RecommendationActions.resetFilterAllocationRecommendation,
    (state): IRecommendationState => ({
      ...state,
      filterAllocationRecomendations: {
        ...initialRecommnendationState.filterAllocationRecomendations,
      },
    })
  ),

  on(
    RecommendationActions.resetSearchTextValueRecommendations,
    (state): IRecommendationState => ({
      ...state,
      filterSearchTextValueAllRecommendation: {
        ...initialRecommnendationState.filterSearchTextValueAllRecommendation,
      },
    })
  ),

  // API

  on(
    RecommendationActions.getSearchValue,
    (state, action): IRecommendationState => ({
      ...state,
      search: action?.search,
    })
  ),

  on(RecommendationActions.resetSearchValue, (state) => ({
    ...state,
    searchValue: initialRecommnendationState.search,
  })),

  on(
    RecommendationActions.getTagParams,
    (state, action): IRecommendationState => ({
      ...state,
      tag: action?.tag,
    })
  ),

  on(
    RecommendationActions.getListRecommendationSuccess,
    (state, action): IRecommendationState => ({
      ...state,
      recommendationList: action.data,
    })
  ),

  on(
    RecommendationActions.resetDataAllRecommendation,
    (state, action): IRecommendationState => ({
      ...state,
      recommendationList: initialRecommnendationState.recommendationList,
      filterAllRecommendation: {
        ...initialRecommnendationState.filterAllRecommendation,
      },
    })
  ),

  on(
    RecommendationActions.resetDataFollowingRecommendation,
    (state, action): IRecommendationState => ({
      ...state,
      recommendationList: initialRecommnendationState.recommendationList,
      filterFollowingRecommendation: {
        ...initialRecommnendationState.filterFollowingRecommendation,
      },
    })
  ),

  on(
    RecommendationActions.resetDataOpeningRecommendation,
    (state, action): IRecommendationState => ({
      ...state,
      recommendationList: initialRecommnendationState.recommendationList,
      filterOpeningRecommendation: {
        ...initialRecommnendationState.filterOpeningRecommendation,
      },
    })
  ),

  on(
    RecommendationActions.resetDataClosingRecommendation,
    (state, action): IRecommendationState => ({
      ...state,
      recommendationList: initialRecommnendationState.recommendationList,
      filterClosingRecommendation: {
        ...initialRecommnendationState.filterClosingRecommendation,
      },
    })
  ),

  on(
    RecommendationActions.getListRecommendationFailed,
    (state, action): IRecommendationState => ({
      ...state,
      recommendationList: initialRecommnendationState.recommendationList,
    })
  )
);
