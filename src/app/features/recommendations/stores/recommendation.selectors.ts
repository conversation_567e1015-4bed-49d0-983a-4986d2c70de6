import { createFeatureSelector, createSelector } from '@ngrx/store';
import { IRecommendationState } from '../models/recommendations';

export const RECOMMENDATION_STATE_NAME = 'RECOMMENDATION';

export const selectRecommendationState = createFeatureSelector<IRecommendationState>(RECOMMENDATION_STATE_NAME);

export const selectSearchValue$ = createSelector(selectRecommendationState, (state) => state?.editedRecommendationData);

export const selectEditedData$ = createSelector(
  selectRecommendationState,
  (state: IRecommendationState) => state.editedRecommendationData
);

export const selectFilterAllRecommendation$ = createSelector(
  selectRecommendationState,
  (state) => state?.filterAllRecommendation
);

export const selectFilterFollowingRecommendation$ = createSelector(
  selectRecommendationState,
  (state) => state?.filterFollowingRecommendation
);

export const selectFilterOpeningRecommendation$ = createSelector(
  selectRecommendationState,
  (state) => state?.filterOpeningRecommendation
);

export const selectFilterClosingRecommendation$ = createSelector(
  selectRecommendationState,
  (state) => state?.filterClosingRecommendation
);

export const selectFilterAllocationRecommendation$ = createSelector(
  selectRecommendationState,
  (state) => state?.filterAllocationRecomendations
);

export const selectSearchTextValueRecommendation$ = createSelector(
  selectRecommendationState,
  (state) => state?.filterSearchTextValueAllRecommendation
);

//  API
export const selectSearch$ = createSelector(selectRecommendationState, (state) => state?.search);

export const selectTagParam$ = createSelector(selectRecommendationState, (state) => state?.tag);

export const selectRecommendationList$ = createSelector(selectRecommendationState, (state) => state.recommendationList);
