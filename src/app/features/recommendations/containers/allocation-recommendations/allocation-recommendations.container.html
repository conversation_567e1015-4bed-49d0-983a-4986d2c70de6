<div class="allocation-recommendations-container">
  <div class="header-allocation-recommendations">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-159' | translate}}</div>
      <div class="number-info-cls">
        @for(tag of tags; track tags) {
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tag}}
        </div>
        }
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'filter-mode-cls' : isFilter
        }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); handleRowDbClick($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls"
    >
    </sha-grid>
  </div>
</div>

<!-- Tỷ trọng phân bổ -->
<ng-template #proportionInfo let-proportionInfo="templateInfo" let-element="element">
  @if(proportionInfo){
  <div>
    <ng-container>
      <div class="proportion-container">
        <div class="label typo-body-12">
          <span class="percent-asset">
            <span class="percentage positive">{{proportionInfo.percent | numberFormat : 'percent'}}</span>
            <span class="type-asset"> {{RECOMMEND_ALLOCATION_PROPROTION[proportionInfo.type]}} </span>
          </span>
          <span class="typo-body-12">&nbsp;≈ {{proportionInfo.cash | numberFormat}}</span>
        </div>
      </div>
    </ng-container>
  </div>
  }@else {
  <div>-</div>
  }
</ng-template>
