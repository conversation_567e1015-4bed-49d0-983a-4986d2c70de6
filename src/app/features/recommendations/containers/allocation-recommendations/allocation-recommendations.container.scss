.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}
.allocation-recommendations-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-allocation-recommendations {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;
      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }
        }

        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }
  .tab-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
    height: 56px;
    overflow: hidden;
    transition: transform 0.5s ease;
    white-space: nowrap;
    text-wrap: nowrap;
    padding: 12px 0px;
    margin: 0 12px;
    position: relative;

    .tab {
      width: 100%;
      display: inline-block;
      vertical-align: top;

      .box-info {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 16px;
        background-color: #f8fafd;
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
        color: #808080;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        white-space: nowrap;
        text-wrap: nowrap;
      }
    }
  }

  .table-view-container {
    height: 100%;
    .table-custom-cls {
      ::ng-deep {
        .cash {
          max-width: 156px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--cyan--200);

            input {
              padding: 2px 8px;
              text-align: center !important;
            }
          }
        }

        .stock {
          max-width: 156px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--success--300);

            input {
              padding: 2px 8px;
              text-align: center !important;
            }
          }
        }
        .bonds {
          max-width: 156px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--danger--300);

            input {
              padding: 2px 8px;
              text-align: center !important;
            }
          }
        }
        .derivative {
          max-width: 156px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--warning--300);

            input {
              padding: 2px 8px;
              text-align: center !important;
            }
          }
        }
        .fund-certificates {
          max-width: 156px;
          margin: 0 auto;

          span {
            border-radius: 16px;
            background-color: var(--color--brand--200);

            input {
              padding: 2px 8px;
              text-align: center !important;
            }
          }
        }
      }
    }
  }

  .label {
    max-width: 316px;
    .percent-asset {
      background-color: var(--color--accents--yellow-dark);
      padding: 3px 8px;
      border-radius: 16px;
      min-width: 144px;
      display: inline-block;
      text-align: center;
    }
  }
}
