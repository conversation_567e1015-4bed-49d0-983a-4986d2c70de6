import { Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { DestroyService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { OptionsRecommendationComponent } from '../../components/options-recommendation/options-recommendation.component';
import {
  CONVERT_RECOMMENDATION_TYPE_TO_LABEL,
  RECOMMEND_ALLOCATION_CLASS,
  RECOMMEND_ALLOCATION_LABEL,
  RECOMMEND_ALLOCATION_PROPROTION,
} from '../../constants/recommendations';
import { AllocationRecommendationsFilterComponent } from '../../components/allocation-recommendations-filter/allocation-recommendations-filter.component';
import {
  IFilterAllocationRecomendationsParam,
  IFilterSearchTextValueRecommendationParam,
} from '../../models/recommendations';
import { Store } from '@ngrx/store';
import { take, takeUntil } from 'rxjs';
import {
  selectFilterAllocationRecommendation$,
  selectSearchTextValueRecommendation$,
} from '../../stores/recommendation.selectors';
import {
  resetFilterAllocationRecommendation,
  setFilterAllocationRecommendations,
} from '../../stores/recommendation.actions';
import { ConvertToDate } from '../../../../shared/utils/date';
import { AllocationResultComponent } from 'src/app/features/assets/components/allocation-result/allocation-result.component';

/**
 * AllocationRecommendations
 */
@Component({
  selector: 'app-allocation-recommendations-container',
  templateUrl: './allocation-recommendations.container.html',
  styleUrl: './allocation-recommendations.container.scss',
})
export class AllocationRecommendationsContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('proportionInfo', { static: true }) proportionInfo: TemplateRef<any> | null = null;

  tags = ['10 khuyến nghị'];
  RECOMMEND_ALLOCATION_PROPROTION = RECOMMEND_ALLOCATION_PROPROTION;
  fakeData: any = [
    // {
    //   recommendDate: '19/04/2024',
    //   personSent: {
    //     nickName: 'duongnc',
    //     customerName: 'Nguyễn Cảnh Dương',
    //   },
    //   recommendTo: '069C-586547',
    //   allocationFrom: 0,
    //   allocationTo: 1,
    //   allocationProportion: {
    //     type: 0,
    //     percent: 50,
    //     cash: 25000000000,
    //   },
    //   content: 'Thị trường đang tiếp cận đáy, quý khách hàng nên giải ngân một phần số dư tài sản',
    // },
    // {
    //   recommendDate: '18/04/2024',
    //   personSent: {
    //     nickName: 'haln',
    //     customerName: 'Lê Ngọc Hà',
    //   },
    //   recommendTo: '069C-316087',
    //   allocationFrom: 1,
    //   allocationTo: 0,
    //   allocationProportion: {
    //     type: 1,
    //     percent: 50,
    //     cash: 25000000000,
    //   },
    //   content: 'Thị trường đang tiếp cận đáy, quý khách hàng nên giải ngân một phần số dư tài sản',
    // },
    // {
    //   recommendDate: '17/04/2024',
    //   personSent: {
    //     nickName: 'canhnh',
    //     customerName: 'Nguyễn Hoàng Cảnh',
    //   },
    //   recommendTo: '069C-388482',
    //   allocationFrom: 2,
    //   allocationTo: 0,
    //   allocationProportion: {
    //     type: 2,
    //     percent: 50,
    //     cash: 25000000000,
    //   },
    //   content: 'Thị trường đang tiếp cận đáy, quý khách hàng nên giải ngân một phần số dư tài sản',
    // },
    // {
    //   recommendDate: '16/04/2024',
    //   personSent: {
    //     nickName: 'chienvm',
    //     customerName: 'Vũ Minh Chiến',
    //   },
    //   recommendTo: '069C-862656',
    //   allocationFrom: 3,
    //   allocationTo: 0,
    //   allocationProportion: {
    //     type: 3,
    //     percent: 50,
    //     cash: 25000000000,
    //   },
    //   content: 'Thị trường đang tiếp cận đáy, quý khách hàng nên giải ngân một phần số dư tài sản',
    // },
    // {
    //   recommendDate: '15/04/2024',
    //   personSent: {
    //     nickName: 'anhnht',
    //     customerName: 'Nguyễn Hoàng Tuấn Anh',
    //   },
    //   recommendTo: '069C-252138',
    //   allocationFrom: 4,
    //   allocationTo: 0,
    //   allocationProportion: {
    //     type: 4,
    //     percent: 50,
    //     cash: 25000000000,
    //   },
    //   content: 'Thị trường đang tiếp cận đáy, quý khách hàng nên giải ngân một phần số dư tài sản',
    // },
  ];

  filterOptions!: IFilterAllocationRecomendationsParam;

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param store : Store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly store: Store
  ) {
    super();
    this.toggleButtonByTags([ActionButton.display, ActionButton.filter]);
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }
  /**
   * The OnInit
   */
  ngOnInit(): void {
    const cellTemplate = this.proportionInfo;
    this.data = structuredClone(this.fakeData);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        name: 'Ngày khuyến nghị',
        minWidth: 156,
        width: 156,
        tag: 'recommendDate',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
      },
      {
        name: 'Người khuyến nghị',
        minWidth: 30,
        width: 250,
        tag: 'personSent',
        isDisplay: true,
        resizable: true,
        align: 'start',
        displayValueFn: (v) => `${v.nickName} - ${v.customerName}`,
      },
      {
        name: 'Khuyến nghị tới',
        minWidth: 30,
        width: 156,
        tag: 'recommendTo',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        name: 'Phân bổ từ',
        minWidth: 30,
        width: 156,
        tag: 'allocationFrom',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => RECOMMEND_ALLOCATION_LABEL[v],
        dynamicClass: (v) => RECOMMEND_ALLOCATION_CLASS[v],
        align: 'center',
      },
      {
        name: 'Phân bổ tới',
        minWidth: 30,
        width: 156,
        tag: 'allocationTo',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => RECOMMEND_ALLOCATION_LABEL[v],
        dynamicClass: (v) => RECOMMEND_ALLOCATION_CLASS[v],
        align: 'center',
      },
      {
        name: 'Tỷ trọng phân bổ',
        minWidth: 30,
        width: 316,
        tag: 'allocationProportion',
        isDisplay: true,
        resizable: true,
        cellTemplate,
        align: 'start',
      },
      {
        name: 'Nội dung',
        minWidth: 30,
        width: 575,
        tag: 'content',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
    ];

    this.store
      .select(selectFilterAllocationRecommendation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchTextValueRecommendation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((params) => {
        this.filterSearchData(params);
      });
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetFilterAllocationRecommendation());
  }

  /**
   * Open filter component
   *@param {string} tag
   */
  override clickButton(tag: string): void {
    if (tag === 'filter') {
      const ref = this.openFilter(AllocationRecommendationsFilterComponent, {
        width: '532px',
        data: this.filterOptions,
      });
      this.closeApplyFilter(ref);
    }
  }

  /**
   * GetClassByStatus
   * @param status - number
   * @returns {string} - class
   */
  getClassByStatus(status: number) {
    return 'option-closed';
  }

  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsRecommendationComponent,
      width: 184,
      hasBackdrop: true,
      position: 1,
      componentConfig: { element: element },
    });
  }

  /**
   * @param {any} ref : dialogRef
   */
  closeApplyFilter(ref: any) {
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        this.applyFilter(v);
      });
  }

  /**
   * @param data
   */
  applyFilter(data: any) {
    const { type, optionFilter } = data;
    if (type === 'save') {
      const newListFilter = this.saveFunc(optionFilter);
      this.data = newListFilter;
      this.filteredData = newListFilter;
    } else if (type === 'default') {
      this.isFilter = false;
      this.filteredData = [];
      this.isSearch
        ? this.store
            .select(selectSearchTextValueRecommendation$)
            .pipe(takeUntil(this._destroy))
            .subscribe((params) => {
              this.filterSearchData(params);
            })
        : (() => {
            this.data = this.initialData;
          })();
      this.store.dispatch(resetFilterAllocationRecommendation());
    }
  }
  /**
   * @param optionFilter
   */
  updateParamInStore(optionFilter: any) {
    const params: IFilterAllocationRecomendationsParam = {
      ...optionFilter,
      isFilter: optionFilter.isFilter,
    };
    this.store.dispatch(setFilterAllocationRecommendations({ params }));
  }

  /**
   * @param optionFilter
   */
  saveFunc(optionFilter: any) {
    this.updateParamInStore(optionFilter);
    const newListFilter = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(this.initialData, optionFilter);
    return newListFilter;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    const newListFilter = data.filter((item, index) => {
      const isBrokerMatch = (optionFilter.broker ?? []).length
        ? (optionFilter.broker ?? []).some((b: string) => b.includes(item.personSent.nickName))
        : true;
      //Tỷ trọng phân bổ

      const isProportionMatchFnc = () => {
        if (optionFilter.rangeProportion?.start && optionFilter.rangeProportion?.end) {
          return (
            item.allocationProportion.percent >= optionFilter.rangeProportion.start &&
            item.allocationProportion.percent <= optionFilter.rangeProportion.end
          );
        } else if (optionFilter.rangeProportion?.start) {
          return item.allocationProportion.percent >= optionFilter.rangeProportion.start;
        } else if (optionFilter.rangeProportion?.end) {
          return item.allocationProportion.percent <= optionFilter.rangeProportion.end;
        } else {
          return true;
        }
      };

      const isProportionMatch = isProportionMatchFnc();

      const isAllocationMatch = this.filterAssetAllocationMatch(optionFilter, item);
      const isMatchDate = this.filterDateRecommendation(optionFilter, item);
      return isBrokerMatch && isProportionMatch && isAllocationMatch && isMatchDate;
    });

    return newListFilter;
  }

  /**
   * @param {any} optionFilter
   * @param {any} data
   */
  filterAssetAllocationMatch(optionFilter: any, data: any) {
    const { assetsAllocationFrom, assetsAllocationTo } = optionFilter;
    if (assetsAllocationFrom.length === 5 && assetsAllocationTo.length === 5) return true;
    return (
      assetsAllocationFrom.includes(RECOMMEND_ALLOCATION_LABEL[data.allocationFrom]) &&
      assetsAllocationTo.includes(RECOMMEND_ALLOCATION_LABEL[data.allocationTo])
    );
  }

  /**
   * @param {any} optionFilter
   * @param {any} data
   */
  filterDateRecommendation(optionFilter: any, data: any) {
    const { date } = optionFilter;
    const dateRecommend = ConvertToDate(data.recommendDate);
    const startDate = new Date(date.start);
    const endDate = new Date(date.end);
    return (
      (optionFilter.date.start ? dateRecommend >= startDate : true) &&
      (optionFilter.date.end ? dateRecommend <= endDate : true)
    );
  }
  /**
   * Filter searching data
   * @param {IFilterSearchTextValueRecommendationParam} data
   */
  filterSearchData(data: IFilterSearchTextValueRecommendationParam) {
    const { searchText } = data;
    let searchData: any[] = [];
    if (searchText !== null) {
      this.isSearch = true;
      searchData = this.isFilter
        ? this.filterData(this.filteredData, searchText)
        : this.filterData(this.initialData, searchText);
    } else {
      this.isSearch = false;
      searchData = this.isFilter ? this.filteredData : this.initialData;
    }
    this.searchedData = searchData;
    this.data = searchData;
  }

  /**
   * @param {any[]} data
   * @param {string} searchText
   */
  filterData(data: any[], searchText: string) {
    return data.filter(
      (item) =>
        item.recommendDate.toLowerCase().includes(searchText.toLowerCase()) ??
        item.recommendTo.toLowerCase().includes(searchText.toLowerCase()) ??
        item.personSent.nickName.toLowerCase().includes(searchText.toLowerCase()) ??
        item.personSent.customerName.toLowerCase().includes(searchText.toLowerCase())
    );
  }

  /**
   * handleRowDbClick
   * @param row
   */
  handleRowDbClick(row: any) {
    const { allocationProportion, allocationTo, allocationFrom, personSent, recommendTo, recommendDate, content } =
      row.element;

    const allocatedSource = {
      type: CONVERT_RECOMMENDATION_TYPE_TO_LABEL[allocationFrom],
      oldValue: {
        amount: allocationProportion.cash,
        percentage: allocationProportion.percent,
      },
      newValue: {
        amount: allocationProportion.cash,
        percentage: allocationProportion.percent,
      },
    };

    const allocatedTarget = {
      type: CONVERT_RECOMMENDATION_TYPE_TO_LABEL[allocationTo],
      oldValue: {
        amount: allocationProportion.cash,
        percentage: allocationProportion.percent,
      },
      newValue: {
        amount: allocationProportion.cash,
        percentage: allocationProportion.percent,
      },
    };

    const allocateResult = {
      title: 'MES-334',
      allocatedSource,
      allocatedTarget,
      sentBy: personSent.nickName,
      sentDate: recommendDate,
      accountNumber: recommendTo,
      customerName: personSent.customerName,
      note: content,
      type: 'open',
    };

    this.dialogService.openRightDialog(AllocationResultComponent, {
      width: '560px',
      data: allocateResult,
    });
  }
}
