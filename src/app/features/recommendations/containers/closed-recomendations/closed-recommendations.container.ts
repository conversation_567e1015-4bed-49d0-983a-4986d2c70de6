import { Compo<PERSON>, ElementRef, Inject, On<PERSON><PERSON>roy, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { DestroyService, LoadingService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { OptionsRecommendationComponent } from '../../components/options-recommendation/options-recommendation.component';
import { ClosedRecommendationsFilterComponent } from '../../components/closed-recommendations-filter/closed-recommendations-filter.component';
import {
  IFilterClosingRecommnendationParam,
  IFilterSearchTextValueRecommendationParam,
  IRangeFilter,
} from '../../models/recommendations';
import { Store } from '@ngrx/store';
import { combineLatest, take, takeUntil, withLatestFrom } from 'rxjs';
import { dateToYMD } from 'src/app/shared/utils/date';
import {
  getListRecommendation,
  resetDataClosingRecommendation,
  resetFilterClosingRecommendation,
  resetSearchValue,
  setFilterClosingRecommendation,
} from '../../stores/recommendation.actions';
import {
  selectFilterClosingRecommendation$,
  selectRecommendationList$,
  selectSearchTextValueRecommendation$,
} from '../../stores/recommendation.selectors';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { DetailRecommendationDialogComponent } from '../../components/detail-recommendation-dialog/detail-recommendation-dialog.component';
import { CONVERT_RECOMMENDATION_STATUS_TO_NUMBER } from '../../constants/recommendations';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectAllStockList$,
  selectBrokerListMap$,
  selectCurrentBrokerView$,
  selectCustomerGroupList$,
} from 'src/app/stores/shared/shared.selectors';
import { IAllLevelOfBroker, ICustomerListInRecommendationDialog } from 'src/app/shared/models/global';
import { deepClone, updateBorkerName } from 'src/app/shared/utils/utils';
import { TooltipListCustomerComponent } from '../../components/tooltip-list-customer/tooltip-list-customer.component';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { getCurrentBrokerView, getListAccountNumberAndLevelByBrokerView } from 'src/app/stores/shared/shared.actions';

/**
 *  ClosedRecommendationsContainer
 */
@Component({
  selector: 'app-closed-recommendations',
  templateUrl: './closed-recommendations.container.html',
  styleUrl: './closed-recommendations.container.scss',
})
export class ClosedRecommendationsContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('currentPrice', { static: true }) currentPrice: TemplateRef<any> | null = null;
  @ViewChild('percentage', { static: true }) percentage: TemplateRef<any> | null = null;
  @ViewChild('recommendTo', { static: true }) recommendTo: TemplateRef<any> | null = null;

  customNumberFormat = customNumberFormat;
  tags!: string[];
  listOfStocks: any[] = [];

  filterOptions!: IFilterClosingRecommnendationParam;

  customerList: ICustomerListInRecommendationDialog[] = [];
  customerGroupList: ICustomerListInRecommendationDialog[] = [];
  LIST_MG: IListOptions[] = [];

  private popoverShowListCustomer!: PopoverRef<any>;
  // private isPopoverOpen = false;
  private isMouseOverPopover = false;

  accountNumberMap: { [key: string]: string } = {};

  currentBrokerCode = '';

  currentBrokerSelected = '';

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param store : store
   * @param popoverRef : PopoverRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    private readonly store: Store,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly http: HttpClient,
    private readonly loadingService: LoadingService,
    private readonly router: Router
  ) {
    super();
    this.brokerInfoClosedRecommendation();
    this.toggleButtonByTags([ActionButton.broker, ActionButton.display, ActionButton.filter]);
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }
  /**
   * The Oninit closed recommendation
   */
  ngOnInit(): void {
    this.store
      .select(selectAllStockList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((stocksClo: any) => {
        this.listOfStocks = stocksClo.map((stockCl: any) => ({
          code: stockCl.shortName,
          name: stockCl.stock,
        }));
      });

    combineLatest([
      this.store.select(selectRecommendationList$),
      this.store.select(selectAllAccountNumberListByBrokerView$),
    ])
      .pipe(takeUntil(this._destroy), withLatestFrom(this.store.select(selectBrokerListMap$)))
      .subscribe(([[closedRecommendationList, customers], brokerMap]) => {
        if (customers.length && customers.length !== this.customerList.length) {
          this.accountNumberMap = {};
          this.customerList = customers.map((c) => {
            const { accountNumber, customerName } = c;
            this.accountNumberMap[accountNumber] = customerName;
            return {
              customerGroup: accountNumber,
              customerName: customerName,
              isSelect: false,
            };
          });
        }
        this.data = closedRecommendationList.map((d) => {
          const personalCl: any[] = [];
          (d.recommendToPersonal ?? []).forEach((d) => {
            if (this.accountNumberMap[d]) {
              personalCl.push({
                name: this.accountNumberMap[d],
                id: d,
              });
            }
          });
          return {
            ...d,
            status: CONVERT_RECOMMENDATION_STATUS_TO_NUMBER[d.status],
            stockCode: {
              code: d.stockCode,
              name: d.marketCode,
            },
            recommendTo: {
              group: d.recommendToGroup.map((d) => ({
                name: d.name,
                active: d.isActive,
                id: d.adviceToId,
                groupId: d.groupId,
                groupCode: d.groupCode,
              })),
              personalCl,
              // personal: d.recommendToPersonal.map((d) => ({ name: d.name, id: d.adviceToId })),
            },
            creator: d.brokerCode + ': ' + brokerMap[d.brokerCode],
            potential: d.potential * 100,
            recorded: d.recorded * 100,
          };
        });

        this.tags = [`${this.data.length} Khuyến nghị`];
        this.initialData = deepClone(this.data);
      });

    this.columnConfigs = [
      {
        name: 'Ngày đóng KN',
        minWidth: 156,
        width: 156,
        tag: 'recommendDate',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        displayValueFn: (v) => {
          return new Date(v).toLocaleDateString('en-GB');
        },
      },
      {
        name: 'Mã CK',
        minWidth: 30,
        width: 156,
        tag: 'stockCode',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          return `${v.code} : ${v.name}`;
        },
      },
      // closed recommendation
      {
        name: 'Giá mở',
        minWidth: 30,
        width: 136,
        tag: 'openPrice',
        isDisplay: true,
        // closed recommendation
        resizable: true,
        displayValueFn: (v) => {
          return v ? customNumberFormat(v, 'decimal', 'en-US', 2) : '-';
        },
        align: 'start',
      },
      // closed recommendation
      {
        name: 'Giá đóng',
        minWidth: 30,
        width: 136,
        tag: 'closePrice',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          return v ? customNumberFormat(v, 'decimal', 'en-US', 2) : '-';
        },
        align: 'start',
      },
      {
        name: '+/- đã ghi nhận',
        minWidth: 30,
        width: 136,
        tag: 'recorded',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.percentage,
        align: 'center',
      },
      // closed recommendation
      {
        name: 'Giá mục tiêu',
        minWidth: 30,
        width: 136,
        tag: 'targetPrice',
        isDisplay: true,
        resizable: true,
        displayValueFn: (vClo) => {
          if (!vClo.from) {
            return customNumberFormat(vClo.to, 'decimal', 'en-US', 2);
          } else
            return `${customNumberFormat(vClo.from, 'decimal', 'en-US', 2)} - ${customNumberFormat(
              vClo.to,
              'decimal',
              'en-US',
              2
            )}`;
        },
        align: 'start',
      },
      {
        name: 'Giá cắt lỗ',
        minWidth: 30,
        width: 136,
        tag: 'lossPrice',
        isDisplay: true,
        resizable: true,
        align: 'start',
        displayValueFn: (vClo) => {
          return vClo ? customNumberFormat(vClo, 'decimal', 'en-US', 2) : '-';
        },
      },
      {
        name: '+/- tiềm năng',
        minWidth: 30,
        width: 136,
        tag: 'potential',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.percentage,
        align: 'center',
      },
      {
        name: 'Thời gian nắm giữ',
        minWidth: 30,
        width: 136,
        tag: 'holdingPeriod',
        align: 'start',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (v == null || v === '') return '-';
          return `${v} ngày`;
        },
      },
      {
        name: 'Khuyến nghị tới',
        minWidth: 30,
        width: 220,
        tag: 'recommendTo',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.recommendTo,
      },
      {
        name: 'Người tạo',
        tag: 'creator',
        minWidth: 30,
        width: 200,
        isDisplay: true,
        resizable: true,
      },
    ];

    this.store
      .select(selectFilterClosingRecommendation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchTextValueRecommendation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((params) => {
        this.filterSearchData(params);
      });

    this.store
      .select(selectCustomerGroupList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((groups) => {
        this.customerGroupList = groups
          .map((g) => ({
            customerGroup: g.name,
            id: g.id,
            isSelect: false,
          }))
          .reverse();
      });
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.store.dispatch(resetDataClosingRecommendation());
    this.store.dispatch(resetSearchValue());
  }

  /**
   * Thông tin của broker
   */
  brokerInfoClosedRecommendation() {
    combineLatest([
      this.store.select(selectCurrentBrokerView$),
      this.store.select(selectInfoUserLogin$),
      this.store.select(selectAllBrokerLevelListByBrokerView$),
    ])
      .pipe(takeUntil(this._destroy))
      .subscribe(([currentBroker, userList, brokers]) => {
        if (!currentBroker) return;
        this.currentBrokerCode = currentBroker.brokerCode;
        const queryParams = this.route.snapshot.queryParams;

        if (!userList) return;

        const getBrokerByParentBrokerId = (brokerObject: IAllLevelOfBroker[]) => {
          let brokerCodes: string[] = [];
          brokerObject.forEach((broker) => {
            brokerCodes.push(broker.brokerCode);

            if (Array.isArray(broker.children) && broker.children.length > 0) {
              brokerCodes = brokerCodes.concat(getBrokerByParentBrokerId(broker.children));
            }
          });
          return brokerCodes;
        };

        const indexBroker = brokers.findIndex((broker) => broker.brokerCode === queryParams['brokerId']);
        if (indexBroker !== -1 && this.currentBrokerSelected !== brokers[indexBroker].brokerCode) {
          this.currentBrokerSelected = brokers[indexBroker].brokerCode;

          const brokerCodeIds = [
            queryParams['brokerId'],
            // ...getBrokerByParentBrokerId(brokers[indexBroker].children)
          ];
          this.store.dispatch(getListRecommendation({ id: brokerCodeIds.join(',') }));
        } else if (indexBroker === -1) {
          const brokerCode = userList[0].brokerCode;
          this.currentBrokerSelected = brokerCode;
          this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode }));

          this.store.dispatch(getListRecommendation({ id: brokerCode }));
          this.router.navigate([], {
            queryParams: { brokerId: brokerCode },
            queryParamsHandling: 'merge',
          });
        }

        const _brokerConvert = updateBorkerName([...brokers], [...userList]);
        const brokerCode = indexBroker === -1 ? userList[0].brokerCode : queryParams['brokerId'];

        const subBroker = userList.map((broker) => {
          return {
            brokerCode: `${broker.brokerCode}`,
            name: `${broker.brokerCode} : ${broker.brokerName}`,
            isSelect: brokerCode ? broker.brokerCode === brokerCode : broker.brokerCode === currentBroker.brokerCode,
          };
        });

        this.LIST_MG = [...subBroker];
        const broker = this.LIST_MG.find((t) => t.isSelect);

        if (broker) {
          this.findButtonByTags(ActionButton.broker).label = broker['name'] ?? '';
        }
      });
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    if (tag === 'filter') {
      const refClosed = this.openFilter(ClosedRecommendationsFilterComponent, {
        width: '800px',
        data: {
          filterOptions: this.filterOptions,
          listOfStocks: this.listOfStocks,
        },
      });

      refClosed
        .afterClosed()
        .pipe(take(1))
        .subscribe({
          next: (closeFilter) => {
            if (!closeFilter) return;
            this.applyFilter(closeFilter);
          },
        });
    } else if (tag === 'broker') {
      this.changeViewBroker();
    }
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;
    if (type === 'save') {
      // open loading
      this.loadingService.show();
      setTimeout(() => {
        this.loadingService.hide();
      }, 1000);

      const newListFilterClo = this.saveFunc(optionFilter);
      this.data = newListFilterClo;
      this.filteredData = newListFilterClo;
      this.tags = [`${this.data.length} Khuyến nghị`];
    } else if (type === 'default') {
      this.loadingService.show();
      setTimeout(() => {
        this.loadingService.hide();
      }, 1000);

      this.isFilter = false;
      this.filteredData = [];
      this.isSearch
        ? this.store
            .select(selectSearchTextValueRecommendation$)
            .pipe(takeUntil(this._destroy))
            .subscribe((params) => {
              this.filterSearchData(params);
            })
        : // closed recommendation
          (() => {
            this.data = this.initialData;
          })();
      this.store.dispatch(resetFilterClosingRecommendation());
      this.tags = [`${this.data.length} Khuyến nghị`];
    }
  }

  /**
   * UpdateParamInStore
   * @param optionFilter
   */
  updateParamInStore(optionFilter: any) {
    const params: IFilterClosingRecommnendationParam = {
      ...optionFilter,
      isFilter: optionFilter.isFilter,
    };
    this.store.dispatch(setFilterClosingRecommendation({ params }));
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilter: any) {
    this.updateParamInStore(optionFilter);
    const newListFilterClosed = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(this.initialData, optionFilter);
    return newListFilterClosed;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    const newListFilterClosed = data.filter((item, index) => {
      // Thời gian nắm giữ
      const isHoldingPeriodMatch = optionFilter.rangeDateHold?.end
        ? item.holdingPeriod >= optionFilter.rangeDateHold?.start &&
          item.holdingPeriod <= optionFilter.rangeDateHold?.end
        : item.holdingPeriod >= optionFilter.rangeDateHold?.start;

      // Tiềm năng
      const isPotentialMatch = this.filterRangeNumber(optionFilter.rangePotential, item.potential);

      // Đã ghi nhận
      const isRecordMatch = this.filterRangeNumber(optionFilter.rangeRecord, item.recorded);

      // Ngày khuyến nghị
      const isDateMatch = this.filterDateRecommendation(item, optionFilter);

      return (
        this.filterCheckBox(optionFilter, item) &&
        isHoldingPeriodMatch &&
        isPotentialMatch &&
        isDateMatch &&
        isRecordMatch
      );
    });
    return newListFilterClosed;
  }

  /**
   * filterRangeNumber
   * @param rangeField
   * @param dataField
   * @returns {boolean} true/ false
   */
  filterRangeNumber(rangeField: IRangeFilter, dataField: any) {
    if (rangeField.end?.toString() && rangeField.start?.toString())
      return +rangeField.start <= +dataField && +dataField <= +rangeField.end;
    if (rangeField?.start?.toString()) return +dataField >= +rangeField.start;
    if (rangeField?.end?.toString()) return +dataField <= +rangeField.end;
    return true;
  }

  /**
   * FilterDateRecommendation
   * @param data
   * @param optionFilter
   * @returns {boolean} true/ false
   */
  filterDateRecommendation(data: any, optionFilter: any) {
    const dateRecommendCl = dateToYMD(data.recommendDate);
    const dateConvertCl = new Date(dateRecommendCl);
    const startDateConvertCl = new Date(optionFilter.date.start).setHours(0, 0, 0, 0);
    const endDateConvertCl = new Date(optionFilter.date.end).setHours(23, 59, 59, 99);
    return (
      (optionFilter.date.start ? dateConvertCl.getTime() >= startDateConvertCl : true) &&
      (optionFilter.date.end ? dateConvertCl.getTime() <= endDateConvertCl : true)
    );
  }

  /**
   * FilterCheckBox
   * @param optionFilter
   * @param data
   * @returns {boolean} true/false
   */
  filterCheckBox(optionFilter: any, data: any) {
    const initStockCodeCl = this.listOfStocks.map((c) => c.code);

    const stockCodeCl = optionFilter.stockCodes?.length ? optionFilter.stockCodes : initStockCodeCl;

    // Mã CK
    const isStockCodesMatch = stockCodeCl.includes(data.stockCode.code);

    // Khách hàng
    const isCustomerMatchCl = (optionFilter.customer ?? []).length
      ? (optionFilter.customer ?? []).some(
          (customer: string) => data.recommendTo?.personal.some((d: any) => d.id === customer) ?? false
        )
      : true;

    const selectedGroups = optionFilter.customerGroup ?? [];
    const isCustomerGroupMatchCl =
      selectedGroups.length === 0 ||
      selectedGroups.length === this.customerGroupList.length - 1 ||
      (optionFilter.customerGroup ?? []).some(
        (group: string) => data.recommendTo?.group.some((d: any) => d.name === group) ?? false
      );

    return isStockCodesMatch && isCustomerMatchCl && isCustomerGroupMatchCl;
  }

  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsRecommendationComponent,
      width: 184,
      hasBackdrop: true,
      position: 1,
      componentConfig: { element },
    });
  }

  /**
   * Filter searching data
   * @param {IFilterSearchTextValueRecommendationParam} data
   */
  filterSearchData(data: IFilterSearchTextValueRecommendationParam) {
    let searchDataCl: any[] = [];
    const { searchText } = data;
    if (searchText !== null) {
      this.isSearch = true;
      searchDataCl = this.isFilter
        ? this.filterData(this.filteredData, searchText)
        : this.filterData(this.initialData, searchText);
    } else {
      this.isSearch = false;
      if (this.isFilter) searchDataCl = this.filteredData;
      else searchDataCl = this.initialData;
    }
    this.searchedData = searchDataCl;
    this.data = searchDataCl;
  }

  /**
   * @param {any[]} data
   * @param {string} searchText
   */
  filterData(dataCl: any[], searchText: string) {
    return dataCl.filter(
      (itemCl) =>
        itemCl.recommendDate.toLowerCase().includes(searchText.toLowerCase()) ||
        itemCl.stockCode.code.toLowerCase().includes(searchText.toLowerCase()) ||
        itemCl.stockCode.name.toLowerCase().includes(searchText.toLowerCase())
    );
  }

  /**
   * handleDetailAdvice
   * @param row
   */
  handleDetailAdvice(rowCl: any) {
    this.loadingService.show();
    this.dialogService.openRightDialog(DetailRecommendationDialogComponent, {
      width: '492px',
      data: rowCl.element,
    });
  }

  /**
   * getPersonalTooltip
   * @param array
   */
  getPersonalTooltip(arrayCl: any[]) {
    return arrayCl.map((item: any) => `${item.id}: ${item.name}`).join('\n');
  }

  /**
   * getGroupTooltip
   * @param array
   */
  getGroupTooltip(arraycl: any[]) {
    return arraycl.map((item: any) => `${item.name}`).join('\n');
  }

  /**
   * openPopoverShowList
   * @param event
   * @param list
   * @param type
   */
  openPopoverShowList(event: Event, list: any[], type: string) {
    const originElementCl = event.target as HTMLElement;

    if (type === 'customer') {
      list = list.map((l) => ({
        name: `${l.id}: ${l.name}`,
      }));
    }

    if (type === 'group') {
      list = list.map((l) => ({
        name: `${l.groupCode}: ${l.name}`,
      }));
    }

    this.popoverShowListCustomer = this.popoverService.open({
      origin: originElementCl,
      content: TooltipListCustomerComponent,
      maxHeight: '230px',
      hasBackdrop: true,
      position: 3,
      panelClass: ['dropdown-overlay-common', 'dropdown-height-100'],
      componentConfig: { list },
    });

    // Add mouse enter/leave event listeners to the popover content
    this.popoverShowListCustomer.overlay.hostElement.addEventListener(
      'mouseenter',
      this.onMouseEnterPopover.bind(this)
    );
    this.popoverShowListCustomer.overlay.hostElement.addEventListener(
      'mouseleave',
      this.onMouseLeavePopoverCl.bind(this)
    );
  }

  /**
   * Handle mouse enter event on the popover
   */
  onMouseEnterPopover(): void {
    this.isMouseOverPopover = true;
  }

  /**
   * Handle mouse leave event on the popover
   */
  onMouseLeavePopoverCl(): void {
    this.isMouseOverPopover = false;
    this.closePopoverCl();
  }

  /**
   * Close the Popover
   */
  closePopoverCl(): void {
    if (this.popoverShowListCustomer && !this.isMouseOverPopover) {
      setTimeout(() => {
        if (!this.isMouseOverPopover) {
          this.popoverService.close(this.popoverShowListCustomer);
        }
      }, 200);
    }
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    const elementRefCo = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidthCo = elementRefCo.nativeElement as HTMLElement;
    const queryParamsCo = this.route.snapshot.queryParams;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRefCo as any,
      width: elementWidthCo.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userListCo) => {
          if (!userListCo) return;
          const currentBrokerCo = userListCo.find((user) => user.brokerCode === itemSelected['brokerCode']);
          const subBroker = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelected['brokerCode']);
          if (queryParamsCo['brokerId'] === itemSelected['brokerCode']) return;
          if (subBroker && !currentBrokerCo) {
            const brokerCode = subBroker['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (currentBrokerCo) {
            if (this.currentBrokerCode === currentBrokerCo.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBrokerCo.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBrokerCo }));
          }

          if (subBroker) {
            this.LIST_MG = this.LIST_MG.map((broker) => ({
              ...broker,
              isSelect: broker['brokerCode'] === subBroker['brokerCode'],
            }));

            this.findButtonByTags(ActionButton.broker).label = subBroker['name'] ?? '';
          }

          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));
          this.store.dispatch(resetFilterClosingRecommendation());
          const subBrokderId = subBroker ? subBroker['brokerCode'] : null;
          this.customerList = [];
          this.router.navigate([], {
            queryParams: {
              ...queryParamsCo,
              brokerId: currentBrokerCo ? currentBrokerCo.brokerCode : subBrokderId,
            },
          });
        });
    });
  }
}
