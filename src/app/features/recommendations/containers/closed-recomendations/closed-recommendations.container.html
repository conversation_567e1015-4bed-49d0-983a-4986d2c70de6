<div class="closed-recommendations-container">
  <div class="header-closed-recommendations">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-267'|translate }}</div>
      <div class="closed-recommendation-cls number-info-cls">
        @for(tag of tags; track tags) {
        <div class="closed-recommendation-cls box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tag}}
        </div>
        }
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [listColumnInitial]="columnConfigsInitial"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
        class="closed-recommendation-cls"
      >
      </app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); handleDetailAdvice($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls closed-recommendation-cls"
    >
    </sha-grid>
  </div>
</div>

<!-- Giá hiện tại -->
<ng-template #currentPrice let-currentPrice="templateInfo" let-element="element">
  @if(currentPrice) {
  <div>
    <ng-container *ngIf="currentPrice > element.referencePrice">
      <div class="price-increase">
        <img src="./assets/icons/up.svg" alt="up-icon" />
        <span class="typo-body-12">{{ customNumberFormat(currentPrice, 'decimal', 'en-US', 2)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="currentPrice < element.referencePrice ">
      <div class="price-reduce">
        <img src="./assets/icons/down.svg" alt="reduce-icon" />
        <span class="typo-body-12">{{ customNumberFormat(currentPrice, 'decimal', 'en-US', 2)}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="currentPrice === element.referencePrice">
      <div class="price-stable">
        <img src="./assets/icons/minus.svg" alt="stable-icon" />
        <span class="typo-body-12"> {{ customNumberFormat(currentPrice, 'decimal', 'en-US', 2)}}</span>
      </div>
    </ng-container>
  </div>
  } @else {
  <div>-</div>
  }
</ng-template>

<!-- +/- Chưa ghi nhận, +/- đã ghi nhận,  +/- tiềm năng  -->
<ng-template #percentage let-percentage="templateInfo" let-element="element">
  @if(percentage) {
  <div class="closed-recommendation-cls box-number-cls">
    <ng-container *ngIf="percentage > 0">
      <div class="closed-recommendation-cls number-increase">
        <span class="closed-recommendation-cls typo-body-12">+{{percentage.toFixed(2)}}%</span>
      </div>
    </ng-container>
    <ng-container *ngIf="percentage < 0">
      <div class="closed-recommendation-cls number-reduce">
        <span class="closed-recommendation-cls typo-body-12">{{percentage.toFixed(2)}}%</span>
      </div>
    </ng-container>
  </div>
  } @else { @if(percentage === 0) {
  <ng-container *ngIf="percentage === 0">
    <div
      [class.change-bg-box]="element.status === 2 && element?.isChange"
      class="closed-recommendation-cls number-static"
    >
      <span class="closed-recommendation-cls typo-body-12">{{percentage.toFixed(2)}}%</span>
    </div>
  </ng-container>
  } @else {
  <div [style.width]="'118px'">-</div>
  } }
</ng-template>
<!-- Khuyến nghị tới -->
<ng-template #recommendTo let-recommendTo="templateInfo">
  <div class="closed-recommendation-cls recommend-to">
    <!-- Nhóm khách hàng -->
    @if(recommendTo.group && recommendTo.group.length > 1) {
    <div class="closed-recommendation-cls tags group typo-body-12">
      <div
        (mouseenter)="openPopoverShowList($event, recommendTo.group, 'group')"
        (mouseleave)="closePopoverCl()"
        class="closed-recommendation-cls absolute"
      ></div>
      {{recommendTo.group.length}} {{'MES-13' | translate}}
    </div>
    } @else {
    <div class="closed-recommendation-cls tags group typo-body-12" *ngFor="let item of recommendTo.group">
      {{item.name}}
    </div>
    }

    <!-- Tài khoản -->
    @if( recommendTo.personalCl && recommendTo.personalCl.length > 1 ) {
    <div class="closed-recommendation-cls tags personal typo-body-12">
      <div
        (mouseenter)="openPopoverShowList($event, recommendTo.personalCl, 'customer')"
        (mouseleave)="closePopoverCl()"
        class="closed-recommendation-cls absolute"
      ></div>
      {{recommendTo.personalCl.length}} {{'MES-15'| translate}}
    </div>
    } @else {
    <div class="tags personal typo-body-12" *ngFor="let item of recommendTo.personalCl">{{item.name}}</div>
    }
  </div>
</ng-template>
