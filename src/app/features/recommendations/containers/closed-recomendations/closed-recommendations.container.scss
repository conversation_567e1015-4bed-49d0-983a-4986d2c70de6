.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.closed-recommendations-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-closed-recommendations {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;
      app-action-btn {
        &.not-accept-save-cls {
          ::ng-deep {
            #box-id--save {
              opacity: 0.5;
            }

            #box-id--broker {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);
            }

            mat-icon[data-mat-icon-name='people-icon'] {
              #Group,
              #Group_2,
              #Group_3 {
                ::ng-deep {
                  path {
                    stroke: var(--color--brand--500);
                  }
                }
              }
            }
          }
        }
        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }

  .tab-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
    height: 56px;
    overflow: hidden;
    transition: transform 0.5s ease;
    white-space: nowrap;
    text-wrap: nowrap;
    padding: 12px 0px;
    margin: 0 12px;
    position: relative;

    .tab {
      width: 100%;
      display: inline-block;
      vertical-align: top;

      .box-info {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 16px;
        background-color: #f8fafd;
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
        color: #808080;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        text-wrap: nowrap;
      }
    }
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        // Trạng thái

        .closed {
          max-width: 156px;
          margin: 0 auto;
          span {
            border-radius: 16px;
            background-color: var(--color--neutral--100);

            input {
              padding: 2px 14px;
            }
          }
        }

        .following {
          max-width: 156px;
          margin: 0 auto;
          span {
            border-radius: 16px;
            background-color: var(--color--accents--orange);

            input {
              padding: 2px 14px;
            }
          }
        }

        .opened {
          max-width: 156px;
          margin: 0 auto;
          span {
            border-radius: 16px;
            background-color: var(--color--accents--green);

            input {
              padding: 2px 14px;
            }
          }
        }

        th {
          input {
            width: 100%;
          }
        }

        .box-number-cls {
          flex: 1;
        }
      }
    }
  }
}

// ngTemplate
// +/- tiềm năng
.price-increase,
.price-reduce,
.price-stable {
  display: flex;
  align-items: center;
  gap: 4px;

  &.price-increase {
    span {
      color: var(--color--accents--green);
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  &.price-reduce {
    span {
      color: var(--color--accents--red);
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  &.price-stable {
    span {
      color: var(--color--warning--500);
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

// +/- Chưa ghi nhận, +/- đã ghi nhận
.number-increase {
  background-color: var(--color--success--200);
  border-radius: 16px;
  text-align: center;
  padding: 2px 0;
  max-width: 118px;
  span {
    white-space: nowrap;
    text-wrap: nowrap;
    max-width: 184px;
    color: var(--color--success--500);
  }
}

.number-reduce {
  background-color: var(--color--danger--300);
  border-radius: 16px;
  text-align: center;
  padding: 2px 0;
  max-width: 118px;
  span {
    white-space: nowrap;
    text-wrap: nowrap;
    max-width: 184px;
    color: var(--color--danger--600);
  }
}

.number-static {
  background-color: var(--color--warning--300);
  border-radius: 16px;
  text-align: center;
  padding: 2px 0;
  max-width: 118px;
  width: 100%;

  &.change-bg-box {
    background-color: var(--color--warning--400);
  }

  span {
    white-space: nowrap;
    text-wrap: nowrap;
    max-width: 184px;
    color: var(--color--warning--600);
  }
}

// Khuyến nghị tới
.recommend-to {
  min-width: 140%;
  display: flex;
  align-items: center;
  gap: 2px;

  .tags {
    padding: 2px 8px;
    border-radius: 16px;
    position: relative;

    &:hover {
      cursor: pointer;
    }

    .absolute {
      width: 100%;
      height: 100%;
      cursor: pointer;
      display: none;
    }

    &:hover {
      .absolute {
        display: block;
        z-index: 100000000;
        position: absolute;
        left: 0;
      }
    }
  }

  .group {
    background-color: var(--color--accents--yellow);
  }

  .personal {
    background-color: var(--color--neutral--100);
  }
}

// Tuỳ chọn
.options-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 8px;
  border-radius: 16px;
  cursor: pointer;
  border: 1px solid var(--color--other--divider);
  position: relative;

  img {
    width: 21px;
    height: 21px;
  }

  .options-popup {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
  }
}

.option-closed {
  opacity: 0.5;
  pointer-events: none !important;
}

::ng-deep .custom-tooltip {
  .mat-mdc-tooltip-surface {
    white-space: pre-line !important;
    left: 0px;
    top: 5px;
  }
}
