import { Component, ElementRef, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { DestroyService, LoadingService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { OptionsRecommendationComponent } from '../../components/options-recommendation/options-recommendation.component';
import { Store } from '@ngrx/store';
import { combineLatest, take, takeUntil, withLatestFrom } from 'rxjs';
import { dateToYMD } from 'src/app/shared/utils/date';
import {
  IFilterOpeningRecommnendationParam,
  IFilterSearchTextValueRecommendationParam,
  IRangeFilter,
  IRecommendationListResponse,
  IUpdateRecommendation,
} from '../../models/recommendations';
import {
  getListRecommendation,
  resetDataOpeningRecommendation,
  resetFilterOpeningRecommendation,
  resetSearchValue,
  setFilterOpeningRecommendation,
} from '../../stores/recommendation.actions';
import {
  selectEditedData$,
  selectFilterOpeningRecommendation$,
  selectRecommendationList$,
  selectSearchTextValueRecommendation$,
} from '../../stores/recommendation.selectors';
import { deepClone, updateBorkerName } from 'src/app/shared/utils/utils';
import { DetailRecommendationDialogComponent } from '../../components/detail-recommendation-dialog/detail-recommendation-dialog.component';
import { CONVERT_RECOMMENDATION_STATUS_TO_NUMBER, RecommendationStatus } from '../../constants/recommendations';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectAllStockList$,
  selectBrokerListMap$,
  selectCurrentBrokerView$,
  selectCustomerGroupList$,
} from 'src/app/stores/shared/shared.selectors';
import { IAllLevelOfBroker, ICustomerListInRecommendationDialog } from 'src/app/shared/models/global';
import { OpenRecommendationsFilterComponent } from '../../components/open-recommendations-filter/open-recommendations-filter.component';
import { isTradingSession, stockQuoteResponseConverter } from 'src/app/shared/utils/trading';
import { TooltipListCustomerComponent } from '../../components/tooltip-list-customer/tooltip-list-customer.component';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { getCurrentBrokerView, getListAccountNumberAndLevelByBrokerView } from 'src/app/stores/shared/shared.actions';
import { SockJSClientService } from 'src/app/shared/services/realtime/sockjs-client.service';
import { BODY_MESSAGE, DESTINATION_MESSAGE, TOPIC } from 'src/app/shared/constants/trading';
import { RecommendationStoreService } from '../../services/recommendation-store.service';

/**
 * OptionsRecommendationContainer
 */
@Component({
  selector: 'app-opened-recommendations',
  templateUrl: './opened-recommendations.container.html',
  styleUrl: './opened-recommendations.container.scss',
})
export class OpenedRecommendationsContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('currentPrice', { static: true }) currentPrice: TemplateRef<any> | null = null;
  @ViewChild('percentage', { static: true }) percentage: TemplateRef<any> | null = null;
  @ViewChild('recommendTo', { static: true }) recommendTo: TemplateRef<any> | null = null;
  @ViewChild('percentageNotRecord', { static: true }) percentageNotRecord: TemplateRef<any> | null = null;

  customNumberFormat = customNumberFormat;
  tags!: string[];
  listOfStocks: any[] = [];

  updatedValue!: IUpdateRecommendation;

  filterOptions!: IFilterOpeningRecommnendationParam;

  customerList: ICustomerListInRecommendationDialog[] = [];
  customerGroupList: ICustomerListInRecommendationDialog[] = [];

  isOutPage = false;

  LIST_MG: IListOptions[] = [];

  private popoverShowListCustomer!: PopoverRef<any>;
  private isMouseOverPopover = false;

  messageBody = '';

  accountNumberMap: { [key: string]: string } = {};

  currentBrokerCode = '';

  currentBrokerSelected = '';

  /**
   * Constructor
   * @param dialogService DialogService
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param store : store
   * @param popoverRef : PopoverRef
   * @param cdf ChangeDetectorRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    private readonly store: Store,
    private readonly http: HttpClient,
    private readonly loadingService: LoadingService,
    private readonly socketJS: SockJSClientService,
    private readonly recommendationStoreService: RecommendationStoreService,
    private readonly router: Router
  ) {
    super();
    this.brokerInfoOpenedRe();
    this.toggleButtonByTags([ActionButton.broker, ActionButton.display, ActionButton.filter]);
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    this.store
      .select(selectAllStockList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((stocks: any) => {
        this.listOfStocks = stocks.map((stockOp: any) => ({
          code: stockOp.shortName,
          name: stockOp.stock,
        }));
      });

    combineLatest([
      this.store.select(selectRecommendationList$),
      this.store.select(selectAllAccountNumberListByBrokerView$),
    ])
      .pipe(takeUntil(this._destroy), withLatestFrom(this.store.select(selectBrokerListMap$)))
      .subscribe(([[openedRecommendationList, customers], brokerMap]) => {
        if (customers.length && customers.length !== this.customerList.length) {
          this.accountNumberMap = {};
          this.customerList = customers.map((c) => {
            const { accountNumber, customerName } = c;
            this.accountNumberMap[accountNumber] = customerName;
            return {
              customerGroup: accountNumber,
              customerName: customerName,
              isSelect: false,
            };
          });
        }
        if (this.isOutPage) return;
        this.data = openedRecommendationList.map((dataOp: IRecommendationListResponse) => {
          const personal: any[] = [];
          (dataOp.recommendToPersonal ?? []).forEach((d) => {
            if (this.accountNumberMap[d]) {
              personal.push({
                name: this.accountNumberMap[d],
                id: d,
              });
            }
          });
          return {
            ...dataOp,
            status: CONVERT_RECOMMENDATION_STATUS_TO_NUMBER[dataOp.status],
            stockCode: {
              code: dataOp.stockCode,
              name: dataOp.marketCode,
            },
            recommendTo: {
              group: dataOp.recommendToGroup.map((d) => ({
                name: d.name,
                id: d.adviceToId,
                active: d.isActive,
                groupId: d.groupId,
                groupCode: d.groupCode,
              })),
              personal,
            },
            creator: dataOp.brokerCode + ': ' + brokerMap[dataOp.brokerCode],

            potential: dataOp.potential * 100,
          };
        });

        this.getStockQuotesFromDataOpenedRe();
        this.tags = [`${this.data.length} Khuyến nghị`];
        this.initialData = deepClone(this.data);
      });

    this.columnConfigs = [
      {
        name: 'Ngày mở KN',
        minWidth: 156,
        width: 156,
        tag: 'recommendDate',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        align: 'start',
        pinned: 'left',
        displayValueFn: (v) => {
          return new Date(v).toLocaleDateString('en-GB');
        },
      },
      {
        name: 'Mã CK',
        minWidth: 30,
        width: 156,
        tag: 'stockCode',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          return `${v.code} : ${v.name}`;
        },
        align: 'start',
      },
      {
        name: 'Giá mở',
        minWidth: 30,
        width: 136,
        tag: 'openPrice',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          return v ? customNumberFormat(v, 'decimal', 'en-US', 2) : '-';
        },
      },
      {
        name: 'Giá hiện tại',
        minWidth: 30,
        width: 136,
        tag: 'currentPrice',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          return v ? customNumberFormat(v, 'decimal', 'en-US', 2) : '-';
        },
        align: 'start',
        cellTemplate: this.currentPrice,
      },
      {
        name: '+/- chưa ghi nhận',
        minWidth: 30,
        width: 136,
        align: 'center',
        tag: 'notRecord',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.percentageNotRecord,
      },
      {
        name: 'Giá mục tiêu',
        minWidth: 30,
        resizable: true,
        width: 136,
        tag: 'targetPrice',
        isDisplay: true,
        displayValueFn: (v) => {
          if (!v.from) {
            return customNumberFormat(v.to, 'decimal', 'en-US', 2);
          } else
            return `${customNumberFormat(v.from, 'decimal', 'en-US', 2)} - ${customNumberFormat(
              v.to,
              'decimal',
              'en-US',
              2
            )}`;
        },
        align: 'start',
      },
      {
        resizable: true,
        name: 'Giá cắt lỗ',
        minWidth: 30,
        width: 136,
        tag: 'lossPrice',
        isDisplay: true,
        displayValueFn: (v) => {
          return v ? customNumberFormat(v, 'decimal', 'en-US', 2) : '-';
        },
        align: 'start',
      },
      {
        name: '+/- tiềm năng',
        minWidth: 30,
        cellTemplate: this.percentage,
        width: 136,
        tag: 'potential',
        isDisplay: true,
        resizable: true,
        align: 'center',
      },
      {
        name: 'Thời gian nắm giữ',
        resizable: true,
        minWidth: 30,
        width: 136,
        tag: 'holdingPeriod',
        isDisplay: true,
        displayValueFn: (v) => {
          if (v == null || v === '') return '-';
          return `${v} ngày`;
        },
        align: 'start',
      },
      {
        name: 'Khuyến nghị tới',
        minWidth: 30,
        width: 220,
        isDisplay: true,
        resizable: true,
        cellTemplate: this.recommendTo,
        tag: 'recommendTo',
      },
      {
        tag: 'creator',
        name: 'Người tạo',
        minWidth: 30,
        width: 200,
        isDisplay: true,
        resizable: true,
      },
    ];

    this.store
      .select(selectFilterOpeningRecommendation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchTextValueRecommendation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((params) => {
        this.filterSearchData(params);
      });

    this.store
      .select(selectEditedData$)
      .pipe(takeUntil(this._destroy))
      .subscribe((editedData) => {
        this.updatedValue = editedData;
        // Check nếu có id được truyền vào mới chạy hàm update
        if (this.updatedValue.id) {
          this.updateData();
        }
      });

    this.store
      .select(selectCustomerGroupList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((groups) => {
        this.customerGroupList = groups
          .map((g) => ({
            customerGroup: g.name,
            id: g.id,
            isSelect: false,
          }))
          .reverse();
      });
  }

  getStockQuotesFromDataOpenedRe() {
    // lấy những code của khuyến nghị không đóng
    const codesOpenedRe = this.data.filter((d) => d.status !== 0).map((t) => t.stockCode.code);
    if (!codesOpenedRe.length) return;
    const uniqueCode = [...new Set(codesOpenedRe)];
    const isTrading = isTradingSession();
    if (!isTrading) return;

    const messageOpenedRe = {
      destination: DESTINATION_MESSAGE.market,
      body: `sub${BODY_MESSAGE.QT}${uniqueCode.join('|')}`,
    };

    if (this.messageBody) {
      const messageUnSub = {
        ...messageOpenedRe,
        body: `unsub${this.messageBody}`,
      };
      this.socketJS.sendMessage(messageUnSub.destination, messageUnSub.body);
    }
    this.messageBody = `${BODY_MESSAGE.QT}${uniqueCode.join('|')}`;
    this.recommendationStoreService.codeListSendMessage(uniqueCode);

    const topic = TOPIC.market;

    this.socketJS.initSocket(messageOpenedRe, topic, (data) => {
      const quotesOpenedRe = stockQuoteResponseConverter(data);
      if (!quotesOpenedRe) return;
      if (quotesOpenedRe.service.includes('auto.qt')) {
        const { code, lastValue, referencePrice } = quotesOpenedRe;

        this.data = this.updateDataFromSocket(this.data, code, lastValue, referencePrice);
        this.initialData = this.updateDataFromSocket(this.initialData, code, lastValue, referencePrice, true);
      }
    });
  }

  updateDataFromSocket(
    data: any[],
    code: string,
    lastValue: string,
    referencePrice: string,
    isInitial?: boolean
  ): any[] {
    const index = data.findIndex((item) => item.stockCode.code === code && item.status !== 0);
    if (index === -1) return data;

    const item = { ...data[index] };
    const { openPrice } = item;
    let { notRecord } = item;

    if (item.status === 2) {
      notRecord = ((+lastValue / 1000 - +openPrice) / +openPrice) * 100;
    }

    item.currentPrice = (+lastValue / 1000).toFixed(2).toString();
    item.notRecord = +notRecord;
    item.referencePrice = (+referencePrice / 1000).toFixed(2).toString();
    if (!isInitial) item.isChange = true;

    data[index] = item;
    return data;
  }
  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.isOutPage = true;
    this.store.dispatch(resetDataOpeningRecommendation());
    this.store.dispatch(resetSearchValue());

    const messageUnSub = {
      destination: DESTINATION_MESSAGE.market,
      body: `unsub${this.messageBody}`,
    };
    if (this.messageBody) this.socketJS.sendMessage(messageUnSub.destination, messageUnSub.body);
  }

  /**
   * Thông tin của broker
   */
  brokerInfoOpenedRe() {
    combineLatest([
      this.store.select(selectCurrentBrokerView$),
      this.store.select(selectInfoUserLogin$),
      this.store.select(selectAllBrokerLevelListByBrokerView$),
    ])
      .pipe(takeUntil(this._destroy))
      .subscribe(([currentBrokerOpened, userListOpened, brokersRe]) => {
        if (!currentBrokerOpened) return;
        this.currentBrokerCode = currentBrokerOpened.brokerCode;

        const queryParams = this.route.snapshot.queryParams;

        if (!userListOpened) return;
        const getBrokerByParentBrokerId = (brokerObject: IAllLevelOfBroker[]) => {
          let brokerCodes: string[] = [];
          brokerObject.forEach((broker) => {
            brokerCodes.push(broker.brokerCode);

            if (Array.isArray(broker.children) && broker.children.length > 0) {
              brokerCodes = brokerCodes.concat(getBrokerByParentBrokerId(broker.children));
            }
          });
          return brokerCodes;
        };

        const indexBroker = brokersRe.findIndex((broker) => broker.brokerCode === queryParams['brokerId']);
        if (indexBroker !== -1 && this.currentBrokerSelected !== brokersRe[indexBroker].brokerCode) {
          this.currentBrokerSelected = brokersRe[indexBroker].brokerCode;
          const brokerCodeIds = [
            queryParams['brokerId'],
            // ...getBrokerByParentBrokerId(brokersRe[indexBroker].children)
          ];
          this.store.dispatch(getListRecommendation({ id: brokerCodeIds.join(',') }));
        } else if (indexBroker === -1) {
          const brokerCode = userListOpened[0].brokerCode;
          this.currentBrokerSelected = brokerCode;

          this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode }));

          this.store.dispatch(getListRecommendation({ id: brokerCode }));
          this.router.navigate([], {
            queryParams: { brokerId: brokerCode },
            queryParamsHandling: 'merge',
          });
        }

        const _brokerConvert = updateBorkerName([...brokersRe], [...userListOpened]);
        const brokerCode = indexBroker === -1 ? userListOpened[0].brokerCode : queryParams['brokerId'];

        const subBroker = userListOpened.map((broker) => {
          return {
            brokerCode: `${broker.brokerCode}`,
            name: `${broker.brokerCode} : ${broker.brokerName}`,
            isSelect: brokerCode
              ? broker.brokerCode === brokerCode
              : broker.brokerCode === currentBrokerOpened.brokerCode,
          };
        });

        this.LIST_MG = [...subBroker];
        const broker = this.LIST_MG.find((t) => t.isSelect);

        if (broker) {
          this.findButtonByTags(ActionButton.broker).label = broker['name'] ?? '';
        }
      });
  }

  /**
   * update gía trị sau apply popup khuyến nghị
   */
  updateData() {
    let updatedData = [];
    const dataClone = deepClone(this.data);
    if (this.updatedValue) {
      updatedData = dataClone.map((item) => {
        const { openPrice, lossPrice, customer, customerGroup, takeProfit1, takeProfit2, status } = this.updatedValue;
        if (item.id === this.updatedValue.id) {
          return {
            ...item,
            status,
            recommendTo: {
              personal: customer,
              group: customerGroup,
            },
            openPrice,
            lossPrice,
            targetPrice: {
              from: takeProfit1,
              to: takeProfit2,
            },
          };
        }

        return item;
      });

      this.isSearch = true; // Set isSearch to true when updateData in table
    } else {
      updatedData = [...this.initialData];
      this.isSearch = false; // Set isSearch to false when not updateData in table
    }

    this.data = [...updatedData];
  }

  /**
   * ClickButton
   * @param tag
   */
  override clickButton(tag: string): void {
    if (tag === 'filter') {
      const refOpened = this.openFilter(OpenRecommendationsFilterComponent, {
        width: '800px',
        data: {
          filterOptions: this.filterOptions,
          listOfStocks: this.listOfStocks,
        },
      });

      refOpened
        .afterClosed()
        .pipe(take(1))
        .subscribe({
          next: (closedFilter) => {
            if (!closedFilter) return;
            this.applyFilter(closedFilter);
          },
        });
    } else if (tag === 'broker') {
      this.changeViewBrokerOp();
    }
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(dataOp: any) {
    const { optionFilter, type } = dataOp;
    if (type === 'save') {
      this.loadingService.show();
      setTimeout(() => {
        this.loadingService.hide();
      }, 1000);

      const newListFilterOpened = this.saveFunc(optionFilter);
      this.data = newListFilterOpened;
      this.filteredData = newListFilterOpened;
      this.tags = [`${this.data.length} Khuyến nghị`];
    } else if (type === 'default') {
      this.loadingService.show();
      setTimeout(() => {
        this.loadingService.hide();
      }, 1000);

      this.isFilter = false;
      this.filteredData = [];
      if (this.isSearch) {
        this.store
          .select(selectSearchTextValueRecommendation$)
          .pipe(takeUntil(this._destroy))
          .subscribe((params) => {
            this.filterSearchData(params);
          });
      } else this.data = this.initialData;
      this.store.dispatch(resetFilterOpeningRecommendation());
      this.tags = [`${this.data.length} Khuyến nghị`];
    }
  }

  updateStatusChange(elementOp: any) {
    elementOp.isChange = false;
    this.cdf.detectChanges();
  }

  /**
   * UpdateParamInStore
   * @param optionFilter
   */
  updateParamInStore(optionFilterOp: any) {
    const params: IFilterOpeningRecommnendationParam = {
      ...optionFilterOp,
      isFilter: optionFilterOp.isFilter,
    };
    this.store.dispatch(setFilterOpeningRecommendation({ params }));
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilterOp: any) {
    this.updateParamInStore(optionFilterOp);
    const newListFilter = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilterOp)
      : this.logicFilter(this.initialData, optionFilterOp);

    return newListFilter;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilterOp: any) {
    const newListFilter = data.filter((item, index) => {
      // Thời gian nắm giữ
      const isHoldingPeriodMatch = optionFilterOp.rangeDateHold?.end
        ? item.holdingPeriod >= optionFilterOp.rangeDateHold?.start &&
          item.holdingPeriod <= optionFilterOp.rangeDateHold?.end
        : item.holdingPeriod >= optionFilterOp.rangeDateHold?.start;

      // Tiềm năng
      const isPotentialMatch = this.filterRangeNumber(optionFilterOp.rangePotential, item.potential);

      // Chưa ghi nhận
      const isNotRecordMatch = this.filterRangeNumber(optionFilterOp.rangeNotRecord, item.notRecord);

      // Ngày khuyến nghị

      const isDateMatch = this.filterDateRecommendation(item, optionFilterOp);
      return (
        this.filterCheckBox(optionFilterOp, item) &&
        isHoldingPeriodMatch &&
        isPotentialMatch &&
        isDateMatch &&
        isNotRecordMatch
      );
    });

    return newListFilter;
  }

  /**
   * filterRangeNumber
   * @param rangeField
   * @param dataField
   * @returns {boolean} true/ false
   */
  filterRangeNumber(rangeFieldOp: IRangeFilter, dataField: any) {
    if (rangeFieldOp.end?.toString() && rangeFieldOp.start?.toString())
      return +rangeFieldOp.start <= +dataField && +dataField <= +rangeFieldOp.end;
    if (rangeFieldOp?.start?.toString()) return +dataField >= +rangeFieldOp.start;
    if (rangeFieldOp?.end?.toString()) return +dataField <= +rangeFieldOp.end;
    return true;
  }

  /**
   * FilterDateRecommendation
   * @param data
   * @param optionFilter
   * @returns {boolean} true/ false
   */
  filterDateRecommendation(data: any, optionFilter: any) {
    const dateRecommendOp = dateToYMD(data.recommendDate);
    const dateConvert = new Date(dateRecommendOp);
    const startDateConvertOp = new Date(optionFilter.date.start).setHours(0, 0, 0, 0);
    const endDateConvertOp = new Date(optionFilter.date.end).setHours(23, 59, 59, 99);
    return (
      (optionFilter.date.start ? dateConvert.getTime() >= startDateConvertOp : true) &&
      (optionFilter.date.end ? dateConvert.getTime() <= endDateConvertOp : true)
    );
  }

  /**
   * FilterCheckBox
   * @param optionFilter
   * @param data
   * @returns {boolean} true/false
   */
  filterCheckBox(optionFilterOp: any, data: any) {
    const initStockCode = this.listOfStocks.map((c) => c.code);

    const stockCode = optionFilterOp.stockCodes?.length ? optionFilterOp.stockCodes : initStockCode;

    // Mã CK
    const isStockCodesMatch = stockCode.includes(data.stockCode.code);
    // Khách hàng
    const isCustomerMatch = (optionFilterOp.customer ?? []).length
      ? (optionFilterOp.customer ?? []).some(
          (customer: string) => data.recommendTo?.personal.some((d: any) => d.id === customer) ?? false
        )
      : true;

    const selectedGroups = optionFilterOp.customerGroup ?? [];
    const isCustomerGroupMatch =
      selectedGroups.length === 0 ||
      selectedGroups.length === this.customerGroupList.length - 1 ||
      selectedGroups.some((group: string) => data.recommendTo?.group?.some((d: any) => d.name === group));

    return isStockCodesMatch && isCustomerMatch && isCustomerGroupMatch;
  }

  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElementOp = html;

    this.popoverService.open({
      origin: originElementOp,
      content: OptionsRecommendationComponent,
      width: 184,
      position: 1,
      componentConfig: {
        element: element,
        type: RecommendationStatus.OPENED,
        closePrice: element.currentPrice,
      },
      hasBackdrop: true,
    });
  }

  /**
   * Filter searching data
   * @param {IFilterSearchTextValueRecommendationParam} data
   */
  filterSearchData(data: IFilterSearchTextValueRecommendationParam) {
    const { searchText } = data;
    let searchDataOp: any[] = [];
    if (searchText !== null) {
      if (this.isFilter) searchDataOp = this.filterData(this.filteredData, searchText);
      else searchDataOp = this.filterData(this.initialData, searchText);
      this.isSearch = true;
    } else {
      this.isSearch = false;
      if (this.isFilter) searchDataOp = this.filteredData;
      else searchDataOp = this.initialData;
    }
    this.searchedData = searchDataOp;
    this.data = searchDataOp;
  }

  /**
   * @param {any[]} data
   * @param {string} searchText
   */
  filterData(data: any[], searchText: string) {
    return data.filter(
      (itemOp) =>
        itemOp.recommendDate.toLowerCase().includes(searchText.toLowerCase()) ||
        itemOp.stockCode.code.toLowerCase().includes(searchText.toLowerCase()) ||
        itemOp.stockCode.name.toLowerCase().includes(searchText.toLowerCase())
    );
  }

  /**
   * handleDetailAdvice
   * @param row
   */
  handleDetailAdvice(rowOp: any) {
    this.loadingService.show();
    this.dialogService.openRightDialog(DetailRecommendationDialogComponent, {
      width: '492px',
      data: rowOp.element,
    });
  }

  /**
   * getPersonalTooltip
   * @param array
   */
  getPersonalTooltip(arrayOp: any[]) {
    return arrayOp.map((item: any) => `${item.id}: ${item.name}`).join('\n');
  }

  /**
   * getGroupTooltip
   * @param array
   */
  getGroupTooltip(arrayOp: any[]) {
    return arrayOp.map((item: any) => `${item.name}`).join('\n');
  }

  /**
   * openPopoverShowList
   * @param event
   * @param list
   * @param type
   */
  openPopoverShowList(event: Event, list: any[], type: string) {
    const originElementOp = event.target as HTMLElement;

    if (type === 'customer') {
      list = list.map((l) => ({
        name: `${l.id}: ${l.name}`,
      }));
    }

    if (type === 'group') {
      list = list.map((l) => ({
        name: `${l.groupCode}: ${l.name}`,
      }));
    }

    this.popoverShowListCustomer = this.popoverService.open({
      origin: originElementOp,
      content: TooltipListCustomerComponent,
      maxHeight: '230px',
      hasBackdrop: true,
      panelClass: ['dropdown-overlay-common', 'dropdown-height-100'],
      componentConfig: { list },
      position: 3,
    });

    // Add mouse enter/leave event listeners to the popover content
    this.popoverShowListCustomer.overlay.hostElement.addEventListener(
      'mouseenter',
      this.onMouseEnterPopoverOp.bind(this)
    );
    this.popoverShowListCustomer.overlay.hostElement.addEventListener(
      'mouseleave',
      this.onMouseLeavePopover.bind(this)
    );
  }

  /**
   * Handle mouse enter event on the popover
   */
  onMouseEnterPopoverOp(): void {
    this.isMouseOverPopover = true;
  }

  /**
   * Handle mouse leave event on the popover
   */
  onMouseLeavePopover(): void {
    this.isMouseOverPopover = false;
    this.closePopoverOp();
  }

  /**
   * Close the Popover
   */
  closePopoverOp(): void {
    if (this.popoverShowListCustomer && !this.isMouseOverPopover) {
      setTimeout(() => {
        if (!this.isMouseOverPopover) {
          this.popoverService.close(this.popoverShowListCustomer);
        }
      }, 200);
    }
  }

  /**
   * changeViewBrokerOp
   */
  changeViewBrokerOp() {
    const elementRefOp = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidthOp = elementRefOp.nativeElement as HTMLElement;
    const queryParamsOp = this.route.snapshot.queryParams;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRefOp as any,
      width: elementWidthOp.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userListOp) => {
          if (!userListOp) return;
          const currentBrokerOp = userListOp.find((user) => user.brokerCode === itemSelected['brokerCode']);

          const subBroker = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelected['brokerCode']);
          if (queryParamsOp['brokerId'] === itemSelected['brokerCode']) return;
          if (subBroker && !currentBrokerOp) {
            const brokerCode = subBroker['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (currentBrokerOp) {
            if (this.currentBrokerCode === currentBrokerOp.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBrokerOp.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBrokerOp }));
          }

          if (subBroker) {
            this.LIST_MG = this.LIST_MG.map((brokerOp) => ({
              ...brokerOp,
              isSelect: brokerOp['brokerCode'] === subBroker['brokerCode'],
            }));

            this.findButtonByTags(ActionButton.broker).label = subBroker['name'] ?? '';
          }

          this.store.dispatch(resetFilterOpeningRecommendation());
          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));
          const subBrokerCode = subBroker ? subBroker['brokerCode'] : null;
          this.customerList = [];
          this.router.navigate([], {
            queryParams: {
              ...queryParamsOp,
              brokerId: currentBrokerOp ? currentBrokerOp.brokerCode : subBrokerCode,
            },
          });
        });
    });
  }
}
