<div class="opened-re-cls opened-recommendations-container">
  <div class="opened-re-cls header-opened-recommendations">
    <div class="opened-re-cls left-box">
      <div class="opened-re-cls typo-heading-9">{{'MES-154'|translate }}</div>
      <div class="opened-re-cls number-info-cls">
        @for(tag of tags; track tags) {
        <div class="opened-re-cls box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tag}}
        </div>
        }
      </div>
    </div>

    <div class="opened-re-cls right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [listColumnInitial]="columnConfigsInitial"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      >
      </app-action-btn>
    </div>
  </div>

  <div class="opened-re-cls table-view-container">
    <sha-grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); handleDetailAdvice($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="opened-re-cls table-custom-cls"
    >
    </sha-grid>
  </div>
</div>

<!-- Giá hiện tại -->
<ng-template #currentPrice let-currentPrice="templateInfo" let-element="element">
  @if(currentPrice) {
  <div class="opened-re-cls box-flex-1-cls">
    <ng-container *ngIf="currentPrice > element.referencePrice">
      <div class="opened-re-cls price-increase typo-body-12">
        <div
          [class.isChangeBgBox]="element?.isChange"
          (animationend)="updateStatusChange(element)"
          class="opened-re-cls box-price"
        >
          <img src="./assets/icons/up.svg" alt="up-icon" />
          <span class="opened-re-cls typo-body-12">{{ currentPrice}}</span>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="currentPrice < element.referencePrice ">
      <div class="opened-re-cls price-reduce typo-body-12">
        <div
          [class.isChangeBgBox]="element?.isChange"
          (animationend)="updateStatusChange(element)"
          class="opened-re-cls box-price"
        >
          <img src="./assets/icons/down.svg" alt="reduce-icon" />
          <span class="opened-re-cls typo-body-12">{{ currentPrice}}</span>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="currentPrice === element.referencePrice">
      <div class="opened-re-cls price-stable typo-body-12">
        <div
          [class.isChangeBgBox]="element?.isChange"
          (animationend)="updateStatusChange(element)"
          class="opened-re-cls box-price"
        >
          <img src="./assets/icons/minus.svg" alt="stable-icon" />
          <span class="opened-re-cls typo-body-12"> {{currentPrice}}</span>
        </div>
      </div>
    </ng-container>
  </div>
  } @else {
  <div class="opened-re-cls box-flex-1-cls">-</div>
  }
</ng-template>

<!-- +/- Chưa ghi nhận, +/- đã ghi nhận,  +/- tiềm năng  -->
<ng-template #percentage let-percentage="templateInfo" let-element="element">
  @if(percentage) {
  <div class="opened-re-cls box-number-cls">
    <ng-container *ngIf="percentage > 0">
      <div class="opened-re-cls number-increase">
        <span class="opened-re-cls typo-body-12">+{{percentage.toFixed(2)}}%</span>
      </div>
    </ng-container>
    <ng-container *ngIf="percentage < 0">
      <div class="opened-re-cls number-reduce">
        <span class="opened-re-cls typo-body-12">{{percentage.toFixed(2)}}%</span>
      </div>
    </ng-container>
  </div>
  } @else { @if(percentage === 0) {
  <ng-container *ngIf="percentage === 0">
    <div [class.change-bg-box]="element.status === 2 && element?.isChange" class="opened-re-cls number-static">
      <span class="opened-re-cls typo-body-12">{{percentage.toFixed(2)}}%</span>
    </div>
  </ng-container>
  } @else {
  <div [style.width]="'118px'">-</div>
  } }
</ng-template>

<ng-template #percentageNotRecord let-percentage="templateInfo" let-element="element">
  @if(percentage) {
  <div class="opened-re-cls box-number-cls">
    <ng-container *ngIf="percentage >= 0">
      <div [class.change-bg-box]=" element?.isChange" class="opened-re-cls number-increase">
        <span class="opened-re-cls typo-body-12">+{{percentage.toFixed(2)}}%</span>
      </div>
    </ng-container>
    <ng-container *ngIf="percentage < 0">
      <div [class.change-bg-box]=" element?.isChange" class="opened-re-cls number-reduce">
        <span class="opened-re-cls typo-body-12">{{percentage.toFixed(2)}}%</span>
      </div>
    </ng-container>
  </div>
  } @else { @if(percentage === 0) {
  <ng-container *ngIf="percentage === 0">
    <div [class.change-bg-box]="element.status === 2 && element?.isChange" class="opened-re-cls number-static">
      <span class="opened-re-cls typo-body-12">{{percentage.toFixed(2)}}%</span>
    </div>
  </ng-container>
  } @else {
  <div [style.width]="'118px'">-</div>
  } }
</ng-template>

<!-- Khuyến nghị tới -->
<ng-template #recommendTo let-recommendTo="templateInfo">
  <div class="opened-re-cls recommend-to">
    <!-- Nhóm khách hàng -->
    @if(recommendTo.group && recommendTo.group.length > 1) {
    <div class="opened-re-cls tags group typo-body-12">
      <div
        (mouseenter)="openPopoverShowList($event, recommendTo.group, 'group')"
        (mouseleave)="closePopoverOp()"
        class="opened-re-cls absolute"
      ></div>
      {{recommendTo.group.length}} {{'MES-13' | translate}}
    </div>
    } @else {
    <div class="opened-re-cls tags group typo-body-12" *ngFor="let item of recommendTo.group">{{item.name}}</div>
    }

    <!-- Tài khoản -->
    @if( recommendTo.personal && recommendTo.personal.length > 1 ) {
    <div class="opened-re-cls tags personal typo-body-12">
      <div
        (mouseenter)="openPopoverShowList($event, recommendTo.personal, 'customer')"
        (mouseleave)="closePopoverOp()"
        class="opened-re-cls absolute"
      ></div>
      {{recommendTo.personal.length}} {{'MES-15'| translate}}
    </div>
    } @else {
    <div class="opened-re-cls tags personal typo-body-12" *ngFor="let item of recommendTo.personal">{{item.name}}</div>
    }
  </div>
</ng-template>
