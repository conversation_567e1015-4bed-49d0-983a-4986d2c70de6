import { Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { DestroyService, LoadingService } from '../../../../core/services';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { PopoverRef } from '../../../../shared/components/popover/popover-ref';
import { BaseTableComponent } from '../../../../shared/components/table-custom/base-table.component';
import { customNumberFormat } from '../../../../shared/utils/currency';
import { OptionsRecommendationComponent } from '../../components/options-recommendation/options-recommendation.component';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { FollowingRecommendationsFilterComponent } from '../../components/following-recommendations-filter/following-recommendations-filter.component';
import { combineL<PERSON><PERSON>, take, takeUntil, withLatestFrom } from 'rxjs';
import { dateToYMD } from 'src/app/shared/utils/date';
import { Store } from '@ngrx/store';
import {
  IFilterFollowingRecommnendationParam,
  IFilterSearchTextValueRecommendationParam,
  IRangeFilter,
  IRecommendationListResponse,
  IUpdateRecommendation,
} from '../../models/recommendations';
import {
  selectEditedData$,
  selectFilterFollowingRecommendation$,
  selectRecommendationList$,
  selectSearchTextValueRecommendation$,
} from '../../stores/recommendation.selectors';
import {
  getListRecommendation,
  resetDataFollowingRecommendation,
  resetFilterFollowingRecommendation,
  resetSearchValue,
  setFilterFollowingRecommendation,
} from '../../stores/recommendation.actions';
import { DetailRecommendationDialogComponent } from '../../components/detail-recommendation-dialog/detail-recommendation-dialog.component';
import { deepClone, updateBorkerName } from 'src/app/shared/utils/utils';
import { CONVERT_RECOMMENDATION_STATUS_TO_NUMBER, RecommendationStatus } from '../../constants/recommendations';
import { Router } from '@angular/router';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectAllStockList$,
  selectBrokerListMap$,
  selectCurrentBrokerView$,
  selectCustomerGroupList$,
} from 'src/app/stores/shared/shared.selectors';
import { IAllLevelOfBroker, ICustomerListInRecommendationDialog } from 'src/app/shared/models/global';
import { HttpClient } from '@angular/common/http';
import { isTradingSession, stockQuoteResponseConverter } from 'src/app/shared/utils/trading';
import { TooltipListCustomerComponent } from '../../components/tooltip-list-customer/tooltip-list-customer.component';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { getCurrentBrokerView, getListAccountNumberAndLevelByBrokerView } from 'src/app/stores/shared/shared.actions';
import { BODY_MESSAGE, DESTINATION_MESSAGE, TOPIC } from 'src/app/shared/constants/trading';
import { SockJSClientService } from 'src/app/shared/services/realtime/sockjs-client.service';
import { RecommendationStoreService } from '../../services/recommendation-store.service';

/**
 *FollowingRecommendationsContainer
 */
@Component({
  selector: 'app-following-recommendations',
  templateUrl: './following-recommendations.container.html',
  styleUrl: './following-recommendations.container.scss',
})
export class FollowingRecommendationsContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('currentPrice', { static: true }) currentPrice: TemplateRef<any> | null = null;
  @ViewChild('percentage', { static: true }) percentage: TemplateRef<any> | null = null;
  @ViewChild('recommendTo', { static: true }) recommendTo: TemplateRef<any> | null = null;
  customNumberFormat = customNumberFormat;

  tags!: string[];

  updatedValue!: IUpdateRecommendation;
  listOfStocks: any[] = [];

  filterOptions!: IFilterFollowingRecommnendationParam;

  customerList: ICustomerListInRecommendationDialog[] = [];
  customerGroupList: ICustomerListInRecommendationDialog[] = [];

  isOutPage = false;

  LIST_MG: IListOptions[] = [];

  messageBody = '';

  accountNumberMap: { [key: string]: string } = {};

  currentBrokerCode = '';

  currentBrokerSelected = '';

  private popoverShowListCustomer!: PopoverRef<any>;
  private isMouseOverPopover = false;
  /**
   * Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param store : store
   * @param popoverRef : PopoverRef
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    private readonly store: Store,
    private readonly http: HttpClient,
    private readonly router: Router,
    private readonly socketJS: SockJSClientService,
    private readonly recommendationStoreService: RecommendationStoreService,
    private readonly loadingService: LoadingService
  ) {
    super();
    this.brokerInfoFollowingRe();
    this.toggleButtonByTags([ActionButton.broker, ActionButton.display, ActionButton.filter]);
  }

  /**
   * The OnInit
   */
  ngOnInit(): void {
    this.store
      .select(selectAllStockList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((stocks: any) => {
        this.listOfStocks = stocks.map((stockFo: any) => ({
          code: stockFo.shortName,
          name: stockFo.stock,
        }));
      });

    combineLatest([
      this.store.select(selectRecommendationList$),
      this.store.select(selectAllAccountNumberListByBrokerView$),
    ])
      .pipe(takeUntil(this._destroy), withLatestFrom(this.store.select(selectBrokerListMap$)))

      .subscribe(([[followingRecommendationList, customers], brokerMap]) => {
        if (customers.length && customers.length !== this.customerList.length) {
          this.accountNumberMap = {};
          this.customerList = customers.map((c) => {
            const { accountNumber, customerName } = c;
            this.accountNumberMap[accountNumber] = customerName;
            return {
              customerGroup: accountNumber,
              customerName: customerName,
              isSelect: false,
            };
          });
        }
        if (this.isOutPage) return;
        this.data = followingRecommendationList.map((dataFo: IRecommendationListResponse) => {
          const personalFo: any[] = [];
          (dataFo.recommendToPersonal ?? []).forEach((dataFo) => {
            if (this.accountNumberMap[dataFo]) {
              personalFo.push({
                name: this.accountNumberMap[dataFo],
                id: dataFo,
              });
            }
          });
          return {
            ...dataFo,
            status: CONVERT_RECOMMENDATION_STATUS_TO_NUMBER[dataFo.status],
            stockCode: {
              code: dataFo.stockCode,
              name: dataFo.marketCode,
            },
            recommendTo: {
              group: dataFo.recommendToGroup.map((d) => ({
                name: d.name,
                id: d.adviceToId,
                active: d.isActive,
                groupId: d.groupId,
                groupCode: d.groupCode,
              })),
              personalFo,
              // personal: d.recommendToPersonal.map((d) => ({ name: d.name, id: d.adviceToId })),
            },
            creator: dataFo.brokerCode + ': ' + brokerMap[dataFo.brokerCode],

            potential: dataFo.potential * 100,
          };
        });

        this.getStockQuotesFromDataFollowing();
        this.tags = [`${this.data.length} Khuyến nghị`];
        this.initialData = deepClone(this.data);
      });

    this.columnConfigs = [
      {
        name: 'Ngày tạo KN',
        minWidth: 156,
        width: 156,
        tag: 'recommendDate',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        align: 'start',
        pinned: 'left',
        displayValueFn: (vOp) => {
          return new Date(vOp).toLocaleDateString('en-GB');
        },
      },
      {
        name: 'Mã CK',
        minWidth: 30,
        width: 156,
        tag: 'stockCode',
        isDisplay: true,
        align: 'start',
        resizable: true,
        displayValueFn: (vOp) => {
          return `${vOp.code} : ${vOp.name}`;
        },
      },
      {
        name: 'Giá khuyến nghị',
        minWidth: 30,
        width: 136,
        tag: 'recommendPrice',
        isDisplay: true,
        resizable: true,
        displayValueFn: (vOp) => {
          return `${customNumberFormat(vOp.from, 'decimal', 'en-US', 2)} - ${customNumberFormat(
            vOp.to,
            'decimal',
            'en-US',
            2
          )}`;
        },
        align: 'start',
      },
      {
        name: 'Giá hiện tại',
        minWidth: 30,
        width: 136,
        align: 'start',
        tag: 'currentPrice',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.currentPrice,
        displayValueFn: (v) => {
          return v ? customNumberFormat(v, 'decimal', 'en-US', 2) : '-';
        },
      },
      {
        name: 'Giá mục tiêu',
        minWidth: 30,
        width: 136,
        tag: 'targetPrice',
        isDisplay: true,
        resizable: true,
        align: 'start',
        displayValueFn: (v) => {
          if (!v.from) {
            return customNumberFormat(v.to, 'decimal', 'en-US', 2);
          } else
            return `${customNumberFormat(v.from, 'decimal', 'en-US', 2)} - ${customNumberFormat(
              v.to,
              'decimal',
              'en-US',
              2
            )}`;
        },
      },
      {
        name: 'Giá cắt lỗ',
        minWidth: 30,
        width: 136,
        tag: 'lossPrice',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          return v ? customNumberFormat(v, 'decimal', 'en-US', 2) : '-';
        },
        align: 'start',
      },
      {
        name: '+/- tiềm năng',
        minWidth: 30,
        width: 136,
        tag: 'potential',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.percentage,
        align: 'center',
      },
      {
        name: 'Thời gian theo dõi',
        minWidth: 30,
        tag: 'holdingPeriod',
        width: 136,
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          if (v == null || v === '') return '-';
          return `${v} ngày`;
        },
        align: 'start',
      },
      {
        name: 'Khuyến nghị tới',
        minWidth: 30,
        width: 220,
        tag: 'recommendTo',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.recommendTo,
      },
      {
        name: 'Người tạo',
        minWidth: 30,
        width: 200,
        isDisplay: true,
        resizable: true,
        tag: 'creator',
      },
    ];

    this.store
      .select(selectFilterFollowingRecommendation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchTextValueRecommendation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((params) => {
        this.filterSearchData(params);
      });

    this.store
      .select(selectEditedData$)
      .pipe(takeUntil(this._destroy))
      .subscribe((editedData) => {
        this.updatedValue = editedData;
        // Check nếu có id được truyền vào mới chạy hàm update
        if (this.updatedValue.id) {
          this.updateData();
        }
      });

    this.store
      .select(selectCustomerGroupList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((groups) => {
        this.customerGroupList = groups.map((g) => ({
          customerGroup: g.name,
          id: g.id,
          isSelect: false,
        }));
      });
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.isOutPage = true;
    this.store.dispatch(resetDataFollowingRecommendation());
    this.store.dispatch(resetSearchValue());

    const messageUnSub = {
      destination: DESTINATION_MESSAGE.market,
      body: `unsub${this.messageBody}`,
    };
    if (this.messageBody) this.socketJS.sendMessage(messageUnSub.destination, messageUnSub.body);
  }

  getStockQuotesFromDataFollowing() {
    // lấy những code của khuyến nghị không đóng
    const codes = this.data.filter((d) => d.status !== 0).map((t) => t.stockCode.code);
    if (!codes.length) return;
    const uniqueCode = [...new Set(codes)];
    const isTrading = isTradingSession();
    if (!isTrading) return;

    const message = {
      destination: DESTINATION_MESSAGE.market,
      body: `sub${BODY_MESSAGE.QT}${uniqueCode.join('|')}`,
    };

    if (this.messageBody) {
      const messageUnSub = {
        ...message,
        body: `unsub${this.messageBody}`,
      };
      this.socketJS.sendMessage(messageUnSub.destination, messageUnSub.body);
    }
    this.messageBody = `${BODY_MESSAGE.QT}${uniqueCode.join('|')}`;
    this.recommendationStoreService.codeListSendMessage(uniqueCode);

    const topic = TOPIC.market;

    this.socketJS.initSocket(message, topic, (data) => {
      const quotes = stockQuoteResponseConverter(data);
      if (!quotes) return;
      if (quotes.service.includes('auto.qt')) {
        const { code, lastValue, referencePrice } = quotes;

        this.data = this.updateDataFromSocket(this.data, code, lastValue, referencePrice);
        this.initialData = this.updateDataFromSocket(this.initialData, code, lastValue, referencePrice, true);
      }
    });
  }

  updateDataFromSocket(
    data: any[],
    code: string,
    lastValue: string,
    referencePrice: string,
    isInitial?: boolean
  ): any[] {
    const index = data.findIndex((item) => item.stockCode.code === code && item.status !== 0);
    if (index === -1) return data;

    const item = { ...data[index] };
    const { openPrice } = item;
    let { notRecord } = item;

    if (item.status === 2) {
      notRecord = ((+lastValue / 1000 - +openPrice) / +openPrice) * 100;
    }

    item.currentPrice = (+lastValue / 1000).toFixed(2).toString();
    item.notRecord = +notRecord;
    item.referencePrice = (+referencePrice / 1000).toFixed(2).toString();
    if (!isInitial) item.isChange = true;

    data[index] = item;
    return data;
  }

  updateStatusChange(element: any) {
    element.isChange = false;
    this.cdf.detectChanges();
  }

  /**
   * Thông tin của broker
   */
  brokerInfoFollowingRe() {
    combineLatest([
      this.store.select(selectCurrentBrokerView$),
      this.store.select(selectInfoUserLogin$),
      this.store.select(selectAllBrokerLevelListByBrokerView$),
    ])
      .pipe(takeUntil(this._destroy))
      .subscribe(([currentBrokerFo, userListFo, brokersFo]) => {
        if (!currentBrokerFo) return;
        this.currentBrokerCode = currentBrokerFo.brokerCode;
        const queryParams = this.route.snapshot.queryParams;

        if (!userListFo) return;

        const getBrokerByParentBrokerId = (brokerObject: IAllLevelOfBroker[]) => {
          let brokerCodes: string[] = [];
          brokerObject.forEach((broker) => {
            brokerCodes.push(broker.brokerCode);

            if (Array.isArray(broker.children) && broker.children.length > 0) {
              brokerCodes = brokerCodes.concat(getBrokerByParentBrokerId(broker.children));
            }
          });
          return brokerCodes;
        };

        const indexBroker = brokersFo.findIndex((broker) => broker.brokerCode === queryParams['brokerId']);

        if (indexBroker !== -1 && this.currentBrokerSelected !== brokersFo[indexBroker].brokerCode) {
          this.currentBrokerSelected = brokersFo[indexBroker].brokerCode;
          const brokerCodeIds = [
            queryParams['brokerId'],
            //  ...getBrokerByParentBrokerId(brokersFo[indexBroker].children)
          ];
          this.store.dispatch(getListRecommendation({ id: brokerCodeIds.join(',') }));
        } else if (indexBroker === -1) {
          const brokerCode = userListFo[0].brokerCode;
          this.currentBrokerSelected = brokerCode;

          this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode }));
          this.store.dispatch(getListRecommendation({ id: brokerCode }));
          this.router.navigate([], {
            queryParams: { brokerId: brokerCode },
            queryParamsHandling: 'merge',
          });
        }

        const _brokerConvert = updateBorkerName([...brokersFo], [...userListFo]);
        const brokerCode = indexBroker === -1 ? userListFo[0].brokerCode : queryParams['brokerId'];

        const subBroker = userListFo.map((broker) => {
          return {
            brokerCode: `${broker.brokerCode}`,
            name: `${broker.brokerCode} : ${broker.brokerName}`,
            isSelect: brokerCode ? broker.brokerCode === brokerCode : broker.brokerCode === currentBrokerFo.brokerCode,
          };
        });

        this.LIST_MG = [...subBroker];
        const brokerFo = this.LIST_MG.find((t) => t.isSelect);

        if (brokerFo) {
          this.findButtonByTags(ActionButton.broker).label = brokerFo['name'] ?? '';
        }
      });
  }

  /**
   * update gía trị sau apply popup khuyến nghị
   */
  updateData() {
    let updatedData = [];
    const dataClone = deepClone(this.data);
    if (this.updatedValue) {
      updatedData = dataClone.map((item) => {
        const { lossPrice, customer, customerGroup, takeProfit, takeProfit1, takeProfit2, status, open1, open2 } =
          this.updatedValue;
        if (item.id === this.updatedValue.id) {
          return {
            ...item,
            status,
            recommendTo: {
              personal: customer,
              group: customerGroup,
            },
            lossPrice,
            targetPrice: {
              from: takeProfit1,
              to: takeProfit2 ?? takeProfit,
            },
            recommendPrice: {
              from: open1 ?? item.recommendPrice.from,
              to: open2 ?? item.recommendPrice.to,
            },
          };
        }

        return item;
      });

      this.isSearch = true; // Set isSearch to true when updateData in table
    } else {
      updatedData = [...this.initialData];
      this.isSearch = false; // Set isSearch to false when not updateData in table
    }

    this.data = [...updatedData];
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string): void {
    if (tag === 'filter') {
      const refFo = this.openFilter(FollowingRecommendationsFilterComponent, {
        width: '800px',
        data: {
          filterOptions: this.filterOptions,
          listOfStocks: this.listOfStocks,
        },
      });

      refFo
        .afterClosed()
        .pipe(take(1))
        .subscribe({
          next: (foFilter) => {
            if (!foFilter) return;
            this.applyFilter(foFilter);
          },
        });
    } else if (tag === 'broker') {
      this.changeViewBrokerFo();
    }
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;
    if (type === 'save') {
      this.loadingService.show();
      setTimeout(() => {
        this.loadingService.hide();
      }, 1000);

      const newListFilterFo = this.saveFunc(optionFilter);
      this.data = newListFilterFo;
      this.filteredData = newListFilterFo;
      this.tags = [`${this.data.length} Khuyến nghị`];
    } else if (type === 'default') {
      this.loadingService.show();
      setTimeout(() => {
        this.loadingService.hide();
      }, 1000);

      this.isFilter = false;
      this.filteredData = [];
      this.isSearch
        ? this.store
            .select(selectSearchTextValueRecommendation$)
            .pipe(takeUntil(this._destroy))
            .subscribe((paramsFo) => {
              this.filterSearchData(paramsFo);
            })
        : (() => {
            this.data = this.initialData;
          })();
      this.store.dispatch(resetFilterFollowingRecommendation());
      this.tags = [`${this.data.length} Khuyến nghị`];
    }
  }

  /**
   * UpdateParamInStore
   * @param optionFilter
   */
  updateParamInStore(optionFilter: any) {
    const paramsFo: IFilterFollowingRecommnendationParam = {
      ...optionFilter,
      isFilter: optionFilter.isFilter,
    };
    this.store.dispatch(setFilterFollowingRecommendation({ params: paramsFo }));
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilterFo: any) {
    this.updateParamInStore(optionFilterFo);
    const logicFilter = (data: any[], optionFilterFo: any) => {
      const filteredData = data.filter((item, index) => {
        // Thời gian nắm giữ
        const isHoldingPeriodMatch = optionFilterFo.rangeDateHold?.end
          ? item.holdingPeriod >= optionFilterFo.rangeDateHold?.start &&
            item.holdingPeriod <= optionFilterFo.rangeDateHold?.end
          : item.holdingPeriod >= optionFilterFo.rangeDateHold?.start;

        // Tiềm năng
        const isPotentialMatch = this.filterRangeNumber(optionFilterFo.rangePotential, item.potential);

        // Ngày khuyến nghị

        const isDateMatch = this.filterDateRecommendation(item, optionFilterFo);
        return this.filterCheckBox(optionFilterFo, item) && isHoldingPeriodMatch && isPotentialMatch && isDateMatch;
      });
      return filteredData;
    };
    const newListFilter = this.isSearch
      ? logicFilter(this.searchedData, optionFilterFo)
      : logicFilter(this.initialData, optionFilterFo);
    return newListFilter;
  }

  /**
   * filterRangeNumber
   * @param rangeField
   * @param dataField
   * @returns {boolean} true/ false
   */
  filterRangeNumber(rangeFieldFo: IRangeFilter, dataField: any) {
    if (rangeFieldFo.end?.toString() && rangeFieldFo.start?.toString())
      return +rangeFieldFo.start <= +dataField && +dataField <= +rangeFieldFo.end;
    if (rangeFieldFo?.start?.toString()) return +dataField >= +rangeFieldFo.start;
    if (rangeFieldFo?.end?.toString()) return +dataField <= +rangeFieldFo.end;
    return true;
  }

  /**
   * FilterDateRecommendation
   * @param data
   * @param optionFilter
   * @returns {boolean} true/ false
   */
  filterDateRecommendation(dataFo: any, optionFilter: any) {
    const dateRecommend = dateToYMD(dataFo.recommendDate);
    const dateConvert = new Date(dateRecommend);
    const startDateConvertFo = new Date(optionFilter.date.start).setHours(0, 0, 0, 0);
    const endDateConvertFo = new Date(optionFilter.date.end).setHours(23, 59, 59, 99);
    return (
      (optionFilter.date.start ? dateConvert.getTime() >= startDateConvertFo : true) &&
      (optionFilter.date.end ? dateConvert.getTime() <= endDateConvertFo : true)
    );
  }

  /**
   * FilterCheckBox
   * @param optionFilter
   * @param data
   * @returns {boolean} true/false
   */
  filterCheckBox(optionFilterFo: any, data: any) {
    const initStockCode = this.listOfStocks.map((c) => c.code);
    const stockCode = optionFilterFo.stockCodes?.length ? optionFilterFo.stockCodes : initStockCode;
    // Mã CK
    const isStockCodesMatch = stockCode.includes(data.stockCode.code);
    // Khách hàng
    const isCustomerMatch = (optionFilterFo.customer ?? []).length
      ? (optionFilterFo.customer ?? []).some(
          (customer: string) => data.recommendTo?.personal.some((d: any) => d.id === customer) ?? false
        )
      : true;

    const selectedGroups = optionFilterFo.customerGroup ?? [];
    const isCustomerGroupMatch =
      selectedGroups.length === 0 ||
      selectedGroups.length === this.customerGroupList.length - 1 ||
      selectedGroups.some((group: string) => data.recommendTo?.group?.some((d: any) => d.name === group));

    return isStockCodesMatch && isCustomerMatch && isCustomerGroupMatch;
  }

  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsRecommendationComponent,
      hasBackdrop: true,
      position: 1,
      width: 184,
      componentConfig: {
        element: element,
        type: RecommendationStatus.FOLLOWING,

        customerList: this.customerList,
        customerGroupList: this.customerGroupList,
      },
    });
  }

  /**
   * Filter searching data
   * @param {IFilterSearchTextValueRecommendationParam} data
   */
  filterSearchData(data: IFilterSearchTextValueRecommendationParam) {
    const { searchText } = data;
    let searchDataFo: any[] = [];

    if (searchText !== null) {
      if (this.isFilter) searchDataFo = this.filterData(this.filteredData, searchText);
      else searchDataFo = this.filterData(this.initialData, searchText);
      this.isSearch = true;
    } else {
      this.isSearch = false;
      if (this.isFilter) searchDataFo = this.filteredData;
      else searchDataFo = this.initialData;
    }
    this.searchedData = searchDataFo;
    this.data = searchDataFo;
  }

  /**
   * @param {any[]} data
   * @param {string} searchText
   */
  filterData(dataFo: any[], searchText: string) {
    return dataFo.filter(
      (itemFo) =>
        itemFo.recommendDate.toLowerCase().includes(searchText.toLowerCase()) ||
        itemFo.stockCode.code.toLowerCase().includes(searchText.toLowerCase()) ||
        itemFo.stockCode.name.toLowerCase().includes(searchText.toLowerCase())
    );
  }

  /**
   * handleDetailAdvice
   * @param row
   */
  handleDetailAdvice(rowFo: any) {
    this.loadingService.show();
    this.dialogService.openRightDialog(DetailRecommendationDialogComponent, {
      width: '492px',
      data: rowFo.element,
    });
  }

  /**
   * getPersonalTooltip
   * @param array
   */
  getPersonalTooltip(arrayFo: any[]) {
    return arrayFo.map((item: any) => `${item.id}: ${item.name}`).join('\n');
  }

  /**
   * getGroupTooltip
   * @param array
   */
  getGroupTooltip(arrayFo: any[]) {
    return arrayFo.map((item: any) => `${item.name}`).join('\n');
  }

  /**
   * openPopoverShowList
   * @param event
   * @param list
   * @param type
   */
  openPopoverShowList(event: Event, list: any[], type: string) {
    const originElementFo = event.target as HTMLElement;

    if (type === 'customer') {
      list = list.map((l) => ({
        name: `${l.id}: ${l.name}`,
      }));
    }

    if (type === 'group') {
      list = list.map((l) => ({
        name: `${l.groupCode}: ${l.name}`,
      }));
    }

    this.popoverShowListCustomer = this.popoverService.open({
      origin: originElementFo,
      content: TooltipListCustomerComponent,
      maxHeight: '230px',
      hasBackdrop: true,
      panelClass: ['dropdown-overlay-common', 'dropdown-height-100'],
      componentConfig: { list },
      position: 3,
    });

    // Add mouse enter/leave event listeners to the popover content
    this.popoverShowListCustomer.overlay.hostElement.addEventListener(
      'mouseenter',
      this.onMouseEnterPopover.bind(this)
    );
    this.popoverShowListCustomer.overlay.hostElement.addEventListener(
      'mouseleave',
      this.onMouseLeavePopover.bind(this)
    );
  }

  /**
   * Handle mouse enter event on the popover
   */
  onMouseEnterPopover(): void {
    this.isMouseOverPopover = true;
  }

  /**
   * Handle mouse leave event on the popover
   */
  onMouseLeavePopover(): void {
    this.isMouseOverPopover = false;
    this.closePopoverFo();
  }

  /**
   * Close the Popover
   */
  closePopoverFo(): void {
    if (this.popoverShowListCustomer && !this.isMouseOverPopover) {
      setTimeout(() => {
        if (!this.isMouseOverPopover) {
          this.popoverService.close(this.popoverShowListCustomer);
        }
      }, 200);
    }
  }

  /**
   * changeViewBrokerFo
   */
  changeViewBrokerFo() {
    const queryParamsFo = this.route.snapshot.queryParams;
    const elementRefFo = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidth = elementRefFo.nativeElement as HTMLElement;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRefFo as any,
      width: elementWidth.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelected = res.data.find((i) => i.isSelect);
      if (!itemSelected) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userListFo) => {
          if (!userListFo) return;
          const currentBrokerFo = userListFo.find((user) => user.brokerCode === itemSelected['brokerCode']);

          const subBroker = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelected['brokerCode']);
          if (queryParamsFo['brokerId'] === itemSelected['brokerCode']) return;
          if (subBroker && !currentBrokerFo) {
            const brokerCode = subBroker['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (currentBrokerFo) {
            if (this.currentBrokerCode === currentBrokerFo.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBrokerFo.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBrokerFo }));
          }

          if (subBroker) {
            this.LIST_MG = this.LIST_MG.map((brokerFo) => ({
              ...brokerFo,
              isSelect: brokerFo['brokerCode'] === subBroker['brokerCode'],
            }));

            this.findButtonByTags(ActionButton.broker).label = subBroker['name'] ?? '';
          }
          this.store.dispatch(resetFilterFollowingRecommendation());
          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));
          const subBrokerCode = subBroker ? subBroker['brokerCode'] : null;
          this.customerList = [];
          this.router.navigate([], {
            queryParams: {
              ...queryParamsFo,
              brokerId: currentBrokerFo ? currentBrokerFo.brokerCode : subBrokerCode,
            },
          });
        });
    });
  }
}
