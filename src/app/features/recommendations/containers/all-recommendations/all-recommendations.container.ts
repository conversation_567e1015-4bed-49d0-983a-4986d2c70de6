import { Component, ElementRef, Inject, On<PERSON><PERSON>roy, OnInit, Optional, TemplateRef, ViewChild } from '@angular/core';
import { DestroyService, LoadingService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import {
  CONVERT_RECOMMENDATION_STATUS_TO_NUMBER,
  RECOMMEND_STATUS_CLASS,
  RECOMMEND_STATUS_LABEL,
  RecommendationStatus,
} from '../../constants/recommendations';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { OptionsRecommendationComponent } from '../../components/options-recommendation/options-recommendation.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { AllRecommendationsFilterComponent } from '../../components/all-recommendations-filter/all-recommendations-filter.component';
import { combineLatest, take, takeUntil, withLatestFrom } from 'rxjs';
import { dateToYMD } from 'src/app/shared/utils/date';
import {
  IFilterAllRecommendationParam,
  IFilterSearchTextValueRecommendationParam,
  IRangeFilter,
  IRecommendationListResponse,
  IUpdateRecommendation,
} from '../../models/recommendations';
import { Store } from '@ngrx/store';
import {
  getListRecommendation,
  resetDataAllRecommendation,
  resetFilterAllRecommendation,
  resetSearchValue,
  setFilterAllRecommendation,
} from '../../stores/recommendation.actions';
import {
  selectFilterAllRecommendation$,
  selectRecommendationList$,
  selectSearchTextValueRecommendation$,
} from '../../stores/recommendation.selectors';
import { deepClone, updateBorkerName } from 'src/app/shared/utils/utils';
import { DetailRecommendationDialogComponent } from '../../components/detail-recommendation-dialog/detail-recommendation-dialog.component';
import {
  selectAllAccountNumberListByBrokerView$,
  selectAllBrokerLevelListByBrokerView$,
  selectAllStockList$,
  selectBrokerListMap$,
  selectCurrentBrokerView$,
  selectCustomerGroupList$,
} from 'src/app/stores/shared/shared.selectors';
import { IAllLevelOfBroker, ICustomerListInRecommendationDialog } from 'src/app/shared/models/global';
import { HttpClient } from '@angular/common/http';
import { isTradingSession, stockQuoteResponseConverter } from 'src/app/shared/utils/trading';
import { TooltipListCustomerComponent } from '../../components/tooltip-list-customer/tooltip-list-customer.component';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { getCurrentBrokerView, getListAccountNumberAndLevelByBrokerView } from 'src/app/stores/shared/shared.actions';
import { IListOptions } from 'src/app/features/assets/constant/assets';
import { Router } from '@angular/router';
import { SockJSClientService } from 'src/app/shared/services/realtime/sockjs-client.service';
import { BODY_MESSAGE, DESTINATION_MESSAGE, TOPIC } from 'src/app/shared/constants/trading';
import { RecommendationStoreService } from '../../services/recommendation-store.service';

/**
 * AllRecommendationsContainer
 */
@Component({
  selector: 'app-all-recommendations',
  templateUrl: './all-recommendations.container.html',
  styleUrl: './all-recommendations.container.scss',
})
export class AllRecommendationsContainer extends BaseTableComponent<any> implements OnInit, OnDestroy {
  @ViewChild('currentPrice', { static: true }) currentPrice: TemplateRef<any> | null = null;
  @ViewChild('percentage', { static: true }) percentage: TemplateRef<any> | null = null;
  @ViewChild('recommendTo', { static: true }) recommendTo: TemplateRef<any> | null = null;
  listOfStocks: any[] = [];
  isShow = false;
  selectElement = null;
  customNumberFormat = customNumberFormat;
  tags!: string[];

  updatedValue!: IUpdateRecommendation;

  filterOptions!: IFilterAllRecommendationParam;

  CONVERT_RECOMMENDATION_STATUS_TO_NUMBER = CONVERT_RECOMMENDATION_STATUS_TO_NUMBER;

  customerList: ICustomerListInRecommendationDialog[] = [];
  customerGroupList: ICustomerListInRecommendationDialog[] = [];

  isOutPage = false;

  LIST_MG: IListOptions[] = [];

  private popoverShowListCustomer!: PopoverRef<any>;
  private isMouseOverPopover = false;

  messageBody = '';

  accountNumberMap: { [key: string]: string } = {};

  currentBrokerCode = '';

  currentBrokerSelected = '';
  /**
   * Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   * @param popoverRef : PopoverRef
   * @param store : Store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    @Optional() @Inject(PopoverRef) private readonly popoverRef: PopoverRef,
    private readonly store: Store,
    private readonly http: HttpClient,
    private readonly loadingService: LoadingService,
    private readonly router: Router,
    private readonly socketJS: SockJSClientService,
    private readonly recommendationStoreService: RecommendationStoreService
  ) {
    super();
    this.brokerInfoAllRecommendations();
    this.toggleButtonByTags([ActionButton.broker, ActionButton.display, ActionButton.filter]);
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }
  }

  /**
   * The OnInit all recommendation
   */
  ngOnInit(): void {
    this.store
      .select(selectAllStockList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((stocks: any) => {
        this.listOfStocks = stocks.map((stock: any) => ({
          code: stock.shortName,
          name: stock.stock,
        }));
      });

    this.columnConfigs = [
      {
        name: 'Ngày tạo KN',
        minWidth: 156,
        width: 156,
        tag: 'recommendDate',
        isDisplay: true,
        resizable: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        displayValueFn: (v) => {
          return new Date(v).toLocaleDateString('en-GB');
        },
      },
      {
        name: 'Trạng thái',
        minWidth: 30,
        width: 165,
        tag: 'status',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v: number) => RECOMMEND_STATUS_LABEL[v],
        dynamicClass: (v: number) => RECOMMEND_STATUS_CLASS[v],
        align: 'center',
      },
      {
        name: 'Mã CK',
        minWidth: 30,
        width: 156,
        tag: 'stockCode',
        isDisplay: true,
        resizable: true,
        displayValueFn: (v) => {
          return `${v.code} : ${v.name}`;
        },
      },
      {
        name: 'Giá hiện tại',
        minWidth: 30,
        width: 124,
        tag: 'currentPrice',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.currentPrice,
        displayValueFn: (v) => {
          return v ? customNumberFormat(v, 'decimal', 'en-US', 2) : '-';
        },
        align: 'center',
      },
      {
        name: 'Giá khuyến nghị',
        minWidth: 30,
        width: 140,
        tag: 'recommendPrice',
        resizable: true,
        isDisplay: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          if (!v?.from) {
            return v.to ? customNumberFormat(v.to, 'decimal', 'en-US', 2) : '-';
          } else
            return `${customNumberFormat(v.from, 'decimal', 'en-US', 2)} - ${customNumberFormat(
              v.to,
              'decimal',
              'en-US',
              2
            )}`;
        },
        align: 'center',
      },
      {
        name: 'Giá đóng',
        minWidth: 30,
        width: 124,
        isDisplay: true,
        resizable: true,
        tag: 'closePrice',
        displayValueFn: (v) => {
          return v ? customNumberFormat(v, 'decimal', 'en-US', 2) : '-';
        },
        align: 'start',
      },
      {
        name: 'Giá mục tiêu',
        minWidth: 30,
        width: 140,
        isDisplay: true,
        resizable: true,
        tag: 'targetPrice',
        displayValueFn: (v) => {
          if (!v?.from) {
            return v.to ? customNumberFormat(v.to, 'decimal', 'en-US', 2) : '-';
          } else
            return `${customNumberFormat(v.from, 'decimal', 'en-US', 2)} - ${customNumberFormat(
              v.to,
              'decimal',
              'en-US',
              2
            )}`;
        },
        align: 'center',
      },
      {
        name: 'Giá cắt lỗ',
        resizable: true,
        minWidth: 30,
        width: 124,
        tag: 'lossPrice',
        isDisplay: true,
        displayValueFn: (v) => {
          return v ? customNumberFormat(v, 'decimal', 'en-US', 2) : '-';
        },
        align: 'start',
      },
      {
        name: '+/- tiềm năng',
        cellTemplate: this.percentage,
        minWidth: 30,
        width: 136,
        tag: 'potential',
        isDisplay: true,
        resizable: true,
        align: 'center',
      },
      {
        name: '+/- chưa ghi nhận',
        minWidth: 30,
        width: 136,
        tag: 'notRecord',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.percentage,
        align: 'center',
      },
      {
        name: '+/- đã ghi nhận',
        minWidth: 30,
        width: 136,
        tag: 'recorded',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.percentage,
        align: 'center',
      },
      {
        name: 'Thời gian khuyến nghị',
        minWidth: 30,
        width: 136,
        tag: 'holdingPeriod',
        isDisplay: true,
        resizable: true,
        align: 'start',
        displayValueFn: (v) => {
          if (v == null || v === '') return '-';
          return `${v} ngày`;
        },
      },
      {
        name: 'Khuyến nghị tới',
        minWidth: 30,
        width: 220,
        tag: 'recommendTo',
        isDisplay: true,
        resizable: true,
        cellTemplate: this.recommendTo,
      },
      {
        name: 'Người tạo',
        minWidth: 30,
        width: 200,
        tag: 'creator',
        isDisplay: true,
        resizable: true,
      },
    ];

    combineLatest([
      this.store.select(selectRecommendationList$),
      this.store.select(selectAllAccountNumberListByBrokerView$),
    ])
      .pipe(takeUntil(this._destroy), withLatestFrom(this.store.select(selectBrokerListMap$)))
      .subscribe(([[allRecommendationList, customers], brokerMap]) => {
        if (customers.length && customers.length !== this.customerList.length) {
          this.accountNumberMap = {};
          this.customerList = customers.map((c) => {
            const { accountNumber, customerName } = c;
            this.accountNumberMap[accountNumber] = customerName;
            return {
              customerGroup: accountNumber,
              customerName: customerName,
              isSelect: false,
            };
          });
        }

        this.getCountTag(allRecommendationList);
        if (this.isOutPage) return;
        this.data = allRecommendationList.map((d: IRecommendationListResponse) => {
          const personal: any[] = [];
          (d.recommendToPersonal ?? []).forEach((d) => {
            if (this.accountNumberMap[d]) {
              personal.push({
                name: this.accountNumberMap[d],
                id: d,
              });
            }
          });
          return {
            ...d,
            status: CONVERT_RECOMMENDATION_STATUS_TO_NUMBER[d.status],
            stockCode: {
              code: d.stockCode,
              name: d.marketCode,
            },
            recommendPrice:
              d.status === RecommendationStatus.CLOSED
                ? {
                    to: d.openPrice,
                  }
                : { ...d.recommendPrice },
            recommendTo: {
              group: d.recommendToGroup?.map((d) => ({
                name: d?.name,
                id: d?.adviceToId,
                active: d.isActive,
                groupId: d.groupId,
                groupCode: d.groupCode,
              })),
              personal,
            },

            creator: d.brokerCode + ': ' + brokerMap[d.brokerCode],

            potential: d.potential * 100,
            recorded: d.recorded * 100,
          };
        });
        this.getStockQuotesFromDataAllRe();
        this.getCountTag(this.data);
        this.initialData = deepClone(this.data);
      });

    this.store
      .select(selectFilterAllRecommendation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchTextValueRecommendation$)
      .pipe(takeUntil(this._destroy))
      .subscribe((params) => {
        this.searchData(params);
      });

    this.store
      .select(selectCustomerGroupList$)
      .pipe(takeUntil(this._destroy))
      .subscribe((groups) => {
        this.customerGroupList = groups
          .map((g) => ({
            customerGroup: g.name,
            id: g.id,
            isSelect: false,
          }))
          .reverse();
      });
  }

  getStockQuotesFromDataAllRe() {
    // lấy những code của khuyến nghị không đóng
    const codesAllRe = this.data.filter((d) => d.status !== 0).map((t) => t.stockCode.code);
    if (!codesAllRe.length) return;
    const uniqueCode = [...new Set(codesAllRe)];
    const isTrading = isTradingSession();
    if (!isTrading) return;

    const messageAllRe = {
      destination: DESTINATION_MESSAGE.market,
      body: `sub${BODY_MESSAGE.QT}${uniqueCode.join('|')}`,
    };

    if (this.messageBody) {
      const messageUnSub = {
        ...messageAllRe,
        body: `unsub${this.messageBody}`,
      };
      this.socketJS.sendMessage(messageUnSub.destination, messageUnSub.body);
    }
    this.messageBody = `${BODY_MESSAGE.QT}${uniqueCode.join('|')}`;
    this.recommendationStoreService.codeListSendMessage(uniqueCode);

    const topic = TOPIC.market;

    this.socketJS.initSocket(messageAllRe, topic, (data) => {
      const quotesAllRe = stockQuoteResponseConverter(data);
      if (!quotesAllRe) return;
      if (quotesAllRe.service.includes('auto.qt')) {
        const { code, lastValue, referencePrice } = quotesAllRe;

        this.data = this.updateDataFromSocket(this.data, code, lastValue, referencePrice);
        this.initialData = this.updateDataFromSocket(this.initialData, code, lastValue, referencePrice, true);
      }
    });
  }

  updateDataFromSocket(
    data: any[],
    code: string,
    lastValue: string,
    referencePrice: string,
    isInitial?: boolean
  ): any[] {
    const index = data.findIndex((item) => item.stockCode.code === code && item.status !== 0);
    if (index === -1) return data;

    const item = { ...data[index] };
    const { openPrice } = item;
    let { notRecord } = item;

    if (item.status === 2) {
      notRecord = ((+lastValue / 1000 - +openPrice) / +openPrice) * 100;
    }

    item.currentPrice = (+lastValue / 1000).toFixed(2).toString();
    item.notRecord = +notRecord;
    item.referencePrice = (+referencePrice / 1000).toFixed(2).toString();
    if (!isInitial) item.isChange = true;

    data[index] = item;
    return data;
  }

  /**
   * NgOnDestroy
   */
  ngOnDestroy(): void {
    this.isOutPage = true;
    const messageUnSub = {
      destination: DESTINATION_MESSAGE.market,
      body: `unsub${this.messageBody}`,
    };
    if (this.messageBody) this.socketJS.sendMessage(messageUnSub.destination, messageUnSub.body);

    this.store.dispatch(resetDataAllRecommendation());
    this.store.dispatch(resetSearchValue());
  }

  /**
   * Thông tin của broker
   */
  brokerInfoAllRecommendations() {
    combineLatest([
      this.store.select(selectCurrentBrokerView$),
      this.store.select(selectInfoUserLogin$),
      this.store.select(selectAllBrokerLevelListByBrokerView$),
    ])
      .pipe(takeUntil(this._destroy))
      .subscribe(([currentBrokerAllRe, userListAllRe, brokersAllRe]) => {
        if (!currentBrokerAllRe) return;
        this.currentBrokerCode = currentBrokerAllRe?.brokerCode;
        const queryParams = this.route.snapshot.queryParams;

        if (!userListAllRe) return;

        const getBrokerByParentBrokerId = (brokerObject: IAllLevelOfBroker[]) => {
          let brokerCodes: string[] = [];
          brokerObject.forEach((broker) => {
            brokerCodes.push(broker.brokerCode);

            if (Array.isArray(broker.children) && broker.children.length > 0) {
              brokerCodes = brokerCodes.concat(getBrokerByParentBrokerId(broker.children));
            }
          });
          return brokerCodes;
        };

        const indexBroker = userListAllRe.findIndex((broker) => broker.brokerCode === queryParams['brokerId']);
        if (indexBroker !== -1 && this.currentBrokerSelected !== userListAllRe[indexBroker].brokerCode) {
          this.currentBrokerSelected = brokersAllRe[indexBroker]?.brokerCode;
          const brokerCodeIds = [
            queryParams['brokerId'],
            //  ...getBrokerByParentBrokerId(brokersAllRe[indexBroker].children)
          ];
          this.store.dispatch(getListRecommendation({ id: brokerCodeIds.join(',') }));
        } else if (indexBroker === -1) {
          const brokerCode = userListAllRe[0].brokerCode;
          this.currentBrokerSelected = brokerCode;
          this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode }));

          this.store.dispatch(getListRecommendation({ id: brokerCode }));
          this.router.navigate([], {
            queryParams: { brokerId: brokerCode },
            queryParamsHandling: 'merge',
          });
        }
        const _brokerConvert = updateBorkerName([...brokersAllRe], [...userListAllRe]);
        const brokerCode = indexBroker === -1 ? userListAllRe[0].brokerCode : queryParams['brokerId'];
        const subBroker = userListAllRe.map((broker) => {
          return {
            brokerCode: `${broker.brokerCode}`,
            name: `${broker.brokerCode} : ${broker.brokerName}`,
            isSelect: brokerCode
              ? broker.brokerCode === brokerCode
              : broker.brokerCode === currentBrokerAllRe.brokerCode,
          };
        });
        this.LIST_MG = [...subBroker];
        const broker = this.LIST_MG.find((t) => t.isSelect);

        if (broker) {
          this.findButtonByTags(ActionButton.broker).label = broker['name'] ?? '';
        }
      });
  }

  /**
   * Đếm tổng tag khuyến nghị
   * @param data
   */
  getCountTag(data: any[]) {
    const acc = data.reduce((acc, item) => {
      acc[item.status] = (acc[item.status] || 0) + 1;
      return acc;
    }, {});

    this.tags = [`${acc['1'] ?? 0} Đang theo dõi `, `${acc['2'] ?? 0} Đã mở `, `${acc['0'] ?? 0} Đã đóng`];
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    if (tag === 'filter') {
      const ref = this.openFilter(AllRecommendationsFilterComponent, {
        width: '800px',
        data: {
          filterOptions: this.filterOptions,
          listOfStocks: this.listOfStocks,
        },
      });

      ref
        .afterClosed()
        .pipe(take(1))
        .subscribe({
          next: (v) => {
            if (!v) return;
            this.applyFilter(v);
          },
        });
    } else if (tag === 'broker') {
      this.changeViewBroker();
    }
  }

  /**
   * ApplyFilter
   * @param data
   */
  applyFilter(data: any) {
    const { optionFilter, type } = data;
    if (type === 'save') {
      this.loadingService.show();
      setTimeout(() => {
        this.loadingService.hide();
      }, 1000);

      const newListFilter = this.saveFunc(optionFilter);
      this.data = newListFilter;
      this.filteredData = newListFilter;
      this.getCountTag(this.data);
    } else if (type === 'default') {
      this.loadingService.show();
      setTimeout(() => {
        this.loadingService.hide();
      }, 1000);

      this.isSearch
        ? this.store
            .select(selectSearchTextValueRecommendation$)
            .pipe(takeUntil(this._destroy))
            .subscribe((params) => {
              this.searchData(params);
            })
        : (() => {
            this.data = this.initialData;
          })();
      this.store.dispatch(resetFilterAllRecommendation());
      this.getCountTag(this.data);
    }
  }

  /**
   * UpdateParamInStore
   * @param optionFilter
   */
  updateParamInStore(optionFilter: any) {
    const params: IFilterAllRecommendationParam = {
      ...optionFilter,
      isFilter: optionFilter.isFilter,
    };
    this.store.dispatch(setFilterAllRecommendation({ params }));
  }

  /**
   * SaveFunc
   * @param optionFilter
   * @returns {any} new list
   */
  saveFunc(optionFilter: any) {
    this.updateParamInStore(optionFilter);
    const newListFilter = this.isSearch
      ? this.logicFilter(this.searchedData, optionFilter)
      : this.logicFilter(this.initialData, optionFilter);
    return newListFilter;
  }

  /**
   * @param data
   * @param optionFilter
   */
  logicFilter(data: any[], optionFilter: any) {
    const newListFilter = data.filter((data, index) => {
      // Thời gian nắm giữ
      const isHoldingPeriodMatch = optionFilter.rangeDateHold?.end
        ? data.holdingPeriod >= optionFilter.rangeDateHold?.start &&
          data.holdingPeriod <= optionFilter.rangeDateHold?.end
        : data.holdingPeriod >= optionFilter.rangeDateHold?.start;

      // Tiềm năng
      const isPotentialMatch = this.filterRangeNumber(optionFilter.rangePotential, data.potential);

      // Chưa ghi nhận
      const isNotRecordMatch = this.filterRangeNumber(optionFilter.rangeNotRecord, data.notRecord);

      // Ghi nhận
      const isRecorddMatch = this.filterRangeNumber(optionFilter.rangeRecord, data.recorded);

      // Ngày khuyến nghị
      const isDateMatch = this.filterDateRecommendation(data, optionFilter);

      return (
        this.filterCheckBox(optionFilter, data) &&
        isHoldingPeriodMatch &&
        isPotentialMatch &&
        isNotRecordMatch &&
        isRecorddMatch &&
        isDateMatch
      );
    });
    return newListFilter;
  }

  /**
   * FilterDateRecommendation
   * @param data
   * @param optionFilter
   * @returns {boolean} true/ false
   */
  filterDateRecommendation(data: any, optionFilter: any) {
    const dateRecommend = dateToYMD(data.recommendDate);
    const dateConvert = new Date(dateRecommend);
    const startDateConvert = new Date(optionFilter.date.start).setHours(0, 0, 0, 0);
    const endDateConvert = new Date(optionFilter.date.end).setHours(23, 59, 59, 99);
    return (
      (optionFilter.date.start ? dateConvert.getTime() >= startDateConvert : true) &&
      (optionFilter.date.end ? dateConvert.getTime() <= endDateConvert : true)
    );
  }

  /**
   * filterRangeNumber
   * @param rangeField
   * @param dataField
   * @returns {boolean} true/ false
   */
  filterRangeNumber(rangeField: IRangeFilter, dataField: number) {
    if (rangeField.end?.toString() && rangeField.start?.toString())
      return +rangeField.start <= +dataField?.toFixed(2) && +dataField?.toFixed(2) <= +rangeField.end;
    if (rangeField?.start?.toString()) return +dataField.toFixed(2) >= +rangeField.start;
    if (rangeField?.end?.toString()) return +dataField?.toFixed(2) <= +rangeField.end;
    return true;
  }

  /**
   * FilterCheckBox
   * @param optionFilter
   * @param data
   * @returns {boolean} true/false
   */
  filterCheckBox(optionFilter: IFilterAllRecommendationParam, data: any) {
    const initStockCode = this.listOfStocks.map((c) => c.code);

    const customer = optionFilter.customer?.length ? optionFilter.customer : [];
    const stockCode = optionFilter.stockCodes?.length ? optionFilter.stockCodes : initStockCode;
    // Trạng thái
    const isStatusMatch = optionFilter.status.includes(data?.status);
    const isStockCodesMatch = stockCode.includes(data.stockCode.code);
    // Khách hàng
    const isCustomerMatch = customer.length
      ? (optionFilter.customer ?? []).some(
          (customer: string) => data.recommendTo?.personal.some((d: any) => d.id === customer) ?? false
        )
      : true;

    // Nhóm khách hàng
    // const isCustomerGroupMatch = (optionFilter.customerGroup ?? []).length
    //   ? (optionFilter.customerGroup ?? []).some(
    //       (group: string) => data.recommendTo?.group.some((d: any) => d.name === group) ?? false
    //     )
    //   : true;

    const selectedGroups = optionFilter.customerGroup ?? [];
    const isCustomerGroupMatch =
      selectedGroups.length === 0 ||
      selectedGroups.length === this.customerGroupList.length - 1 ||
      selectedGroups.some((group) => data.recommendTo?.group?.some((d: any) => d.name === group));

    return isStatusMatch && isCustomerMatch && isCustomerGroupMatch && isStockCodesMatch;
  }

  /**
   * HandleMoreAction
   * @param {} event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    this.popoverService.open({
      origin: originElement,
      content: OptionsRecommendationComponent,
      width: 184,
      hasBackdrop: true,
      position: 1,
      componentConfig: {
        element: element,
        type: RecommendationStatus.ALL,

        customerList: this.customerList,
        customerGroupList: this.customerGroupList,

        isDisableButton: !element.recommendTo.group.length && !element.recommendTo.personal.length,
      },
    });
  }

  /**
   * Filter searching data
   * @param {IFilterSearchTextValueRecommendationParam} data
   */
  searchData(data: IFilterSearchTextValueRecommendationParam) {
    const { searchText } = data;
    let searchData: any[] = [];
    if (searchText !== null) {
      if (this.isFilter) searchData = this.filterData(this.filteredData, searchText);
      else searchData = this.filterData(this.initialData, searchText);

      this.isSearch = true;
    } else {
      this.isSearch = false;
      if (this.isFilter) searchData = this.filteredData;
      else searchData = this.initialData;
    }
    this.searchedData = searchData;
    this.data = searchData;
  }

  /**
   * @param {any[]} data
   * @param {string} searchText
   */
  filterData(data: any[], searchText: string) {
    return data.filter(
      (item) =>
        item.recommendDate.toLowerCase().includes(searchText.toLowerCase()) ||
        RECOMMEND_STATUS_LABEL[item.status].toLowerCase().includes(searchText.toLowerCase()) ||
        item.stockCode.code.toLowerCase().includes(searchText.toLowerCase()) ||
        item.stockCode.name.toLowerCase().includes(searchText.toLowerCase())
    );
  }

  /**
   * handleDetailAdvice
   * @param row
   */
  handleDetailAdvice(row: any) {
    this.loadingService.show();
    this.dialogService.openRightDialog(DetailRecommendationDialogComponent, {
      width: '492px',
      data: row.element,
    });
  }

  /**
   * getPersonalTooltip
   * @param array
   */
  getPersonalTooltip(array: any[]) {
    return array.map((item: any) => `${item.id}: ${item.name}`).join('\n');
  }

  /**
   * getGroupTooltip
   * @param array
   */
  getGroupTooltip(array: any[]) {
    return array.map((item: any) => `${item.name}`).join('\n');
  }

  /**
   * openPopoverShowList
   * @param event
   * @param list
   * @param type
   */
  openPopoverShowList(event: Event, list: any[], type: string) {
    const originElement = event.target as HTMLElement;
    if (type === 'customer') {
      list = list.map((l) => ({
        name: `${l.id}: ${l.name}`,
      }));
    }

    if (type === 'group') {
      list = list.map((l) => ({
        name: `${l.groupCode}: ${l.name}`,
      }));
    }

    this.popoverShowListCustomer = this.popoverService.open({
      origin: originElement,
      content: TooltipListCustomerComponent,
      maxHeight: '230px',
      position: 3,
      hasBackdrop: true,
      panelClass: ['dropdown-overlay-common', 'dropdown-height-100'],
      componentConfig: { list },
    });

    // Add mouse enter/leave event listeners to the popover content
    this.popoverShowListCustomer.overlay.hostElement.addEventListener(
      'mouseenter',
      this.onMouseEnterPopover.bind(this)
    );
    this.popoverShowListCustomer.overlay.hostElement.addEventListener(
      'mouseleave',
      this.onMouseLeavePopover.bind(this)
    );
  }

  /**
   * Handle mouse enter event on the popover
   */
  onMouseEnterPopover(): void {
    this.isMouseOverPopover = true;
  }

  /**
   * Handle mouse leave event on the popover
   */
  onMouseLeavePopover(): void {
    this.isMouseOverPopover = false;
    this.closePopover();
  }

  /**
   * Close the Popover
   */
  closePopover(): void {
    if (this.popoverShowListCustomer && !this.isMouseOverPopover) {
      setTimeout(() => {
        if (!this.isMouseOverPopover) {
          this.popoverService.close(this.popoverShowListCustomer);
        }
      }, 200);
    }
  }

  updateStatusChange(element: any) {
    element.isChange = false;
    this.cdf.detectChanges();
  }

  /**
   * changeViewBroker
   */
  changeViewBroker() {
    const queryParamsAll = this.route.snapshot.queryParams;

    const elementRefAll = new ElementRef(document.querySelector(`#box-id--broker`));
    const elementWidthAll = elementRefAll.nativeElement as HTMLElement;

    const ref = this.popoverService.open<IListOptions[]>({
      content: DraggableListComponent,
      origin: elementRefAll as any,
      width: elementWidthAll.offsetWidth,
      position: 2,
      data: this.LIST_MG,
      componentConfig: {
        canSelectAll: false,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((res) => {
      if (!res.data) return;
      const itemSelectedAll = res.data.find((i) => i.isSelect);
      if (!itemSelectedAll) return;
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userList) => {
          if (!userList) return;
          const currentBrokerAll = userList.find((user) => user.brokerCode === itemSelectedAll['brokerCode']);

          const subBroker = this.LIST_MG.find((broker) => broker['brokerCode'] === itemSelectedAll['brokerCode']);
          if (queryParamsAll['brokerId'] === itemSelectedAll['brokerCode']) return;
          if (subBroker && !currentBrokerAll) {
            const brokerCode = subBroker['brokerCode'] as string;
            this.store.dispatch(getListAccountNumberAndLevelByBrokerView({ brokerCode, isPrimaryBrokerCode: true }));
          }

          if (currentBrokerAll) {
            if (this.currentBrokerCode === currentBrokerAll.brokerCode) {
              this.store.dispatch(
                getListAccountNumberAndLevelByBrokerView({
                  brokerCode: currentBrokerAll.brokerCode,
                  isPrimaryBrokerCode: true,
                })
              );
            } else this.store.dispatch(getCurrentBrokerView({ data: currentBrokerAll }));
          }

          if (subBroker) {
            this.LIST_MG = this.LIST_MG.map((broker) => ({
              ...broker,
              isSelect: broker['brokerCode'] === subBroker['brokerCode'],
            }));

            this.findButtonByTags(ActionButton.broker).label = subBroker['name'] ?? '';
          }
          this.store.dispatch(resetFilterAllRecommendation());
          this.columnConfigs = this.columnConfigsInitial.map((t) => ({
            ...t,
            isDisplay: true,
          }));
          this.customerList = [];
          const subBrokerId = subBroker ? subBroker['brokerCode'] : null;
          this.router.navigate([], {
            queryParams: {
              ...queryParamsAll,
              brokerId: currentBrokerAll ? currentBrokerAll.brokerCode : subBrokerId,
            },
          });
        });
    });
  }
}
