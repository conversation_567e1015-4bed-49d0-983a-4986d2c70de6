<div class="all-re-cls all-recommendations-container">
  <div class="all-re-cls header-all-recommendations">
    <div class="all-re-cls left-box">
      <div class="all-re-cls typo-heading-9">{{'MES-117' | translate}}</div>
      <div class="all-re-cls number-info-cls">
        <div class="all-re-cls box-info typo-body-11" *ngFor="let tag of tags">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tag}}
        </div>
      </div>
    </div>

    <div class="all-re-cls right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        [listColumnInitial]="columnConfigsInitial"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>

  <div class="all-re-cls table-view-container">
    <sha-grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event); handleDetailAdvice($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="all-re-cls table-custom-cls"
    >
    </sha-grid>
  </div>
</div>

<!-- Giá hiện tại -->
<ng-template #currentPrice let-currentPrice="templateInfo" let-element="element">
  @if(currentPrice) {
  <div class="all-re-cls box-flex-1-cls">
    <ng-container *ngIf="currentPrice > element.referencePrice">
      <div class="all-re-cls price-increase typo-body-12">
        <div
          [class.isChangeBgBox]="element?.isChange"
          (animationend)="updateStatusChange(element)"
          class="all-re-cls box-price"
        >
          <img src="./assets/icons/up.svg" alt="up-icon" />
          <span class="all-re-cls typo-body-12">{{ currentPrice }}</span>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="currentPrice < element.referencePrice ">
      <div class="all-re-cls price-reduce typo-body-12">
        <div
          [class.isChangeBgBox]="element?.isChange"
          (animationend)="updateStatusChange(element)"
          class="all-re-cls box-price"
        >
          <img src="./assets/icons/down.svg" alt="reduce-icon" />
          <span class="all-re-cls typo-body-12">{{ currentPrice }}</span>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="currentPrice === element.referencePrice">
      <div class="all-re-cls price-stable typo-body-12">
        <div
          [class.isChangeBgBox]="element?.isChange"
          (animationend)="updateStatusChange(element)"
          class="all-re-cls box-price"
        >
          <img src="./assets/icons/minus.svg" alt="stable-icon" />
          <span class="all-re-cls typo-body-12"> {{currentPrice }}</span>
        </div>
      </div>
    </ng-container>
  </div>
  } @else {
  <div class="all-re-cls box-flex-1-cls">-</div>
  }
</ng-template>

<!-- +/- Chưa ghi nhận, +/- đã ghi nhận,  +/- tiềm năng  -->
<ng-template #percentage let-percentage="templateInfo" let-element="element">
  @if(percentage) {
  <div class="all-re-cls box-number-cls">
    <ng-container *ngIf="percentage > 0">
      <div
        [class.change-bg-box]="element.status === 2 && element?.isChange"
        (animationend)="updateStatusChange(element)"
        class="all-re-cls number-increase"
      >
        <span class="all-re-cls typo-body-12">+{{percentage.toFixed(2)}}%</span>
      </div>
    </ng-container>
    <ng-container *ngIf="percentage < 0">
      <div
        [class.change-bg-box]="element.status === 2 && element?.isChange"
        (animationend)="updateStatusChange(element)"
        class="all-re-cls number-reduce"
      >
        <span class="all-re-cls typo-body-12">{{percentage.toFixed(2)}}%</span>
      </div>
    </ng-container>
  </div>
  } @else { @if(percentage === 0) {
  <ng-container *ngIf="percentage === 0">
    <div
      [class.change-bg-box]="element.status === 2 && element?.isChange"
      (animationend)="updateStatusChange(element)"
      class="all-re-cls number-static"
    >
      <span class="all-re-cls typo-body-12">{{percentage.toFixed(2)}}%</span>
    </div>
  </ng-container>
  } @else {
  <div [style.width]="'118px'">-</div>
  } }
</ng-template>

<!-- Khuyến nghị tới -->
<ng-template #recommendTo let-recommendTo="templateInfo">
  <div class="all-re-cls recommend-to">
    <!-- Nhóm khách hàng -->
    @if(recommendTo.group && recommendTo.group.length > 1) {
    <div class="all-re-cls tags group typo-body-12">
      <div
        (mouseenter)="openPopoverShowList($event, recommendTo.group, 'group')"
        (mouseleave)="closePopover()"
        class="all-re-cls absolute"
      ></div>
      {{recommendTo.group.length}} {{'MES-13' | translate}}
    </div>
    } @else {
    <div class="all-re-cls tags group typo-body-12" *ngFor="let item of recommendTo.group">{{item.name}}</div>
    }

    <!-- Tài khoản -->
    @if( recommendTo.personal && recommendTo.personal.length > 1 ) {
    <div class="all-re-cls tags personal typo-body-12">
      <div
        (mouseenter)="openPopoverShowList($event, recommendTo.personal, 'customer')"
        (mouseleave)="closePopover()"
        class="all-re-cls absolute"
      ></div>
      {{recommendTo.personal.length}} {{'MES-15'| translate}}
    </div>
    } @else {
    <div class="all-re-cls tags personal typo-body-12" *ngFor="let item of recommendTo.personal">{{item.name}}</div>
    }
  </div>
</ng-template>
