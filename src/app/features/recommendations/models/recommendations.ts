export interface ICustomerOption {
  customerGroup: string;
  customerName: string;
  isSelect: boolean;
  [key: string]: string | boolean;
}

export interface IFilterAllRecommendationParam {
  status: number[];
  stockCodes: string[] | null;
  customer: string[] | null;
  customerGroup: string[] | null;
  rangeDateHold: IRangeFilter;
  date: IRangeFilter;
  rangePotential: IRangeFilter;
  rangeNotRecord: IRangeFilter;
  rangeRecord: IRangeFilter;
  isFilter: boolean;
}

export interface IRangeFilter {
  start: string | null | number;
  end: string | null;
}

export interface IRecommendationState {
  editedRecommendationData: IUpdateRecommendation;
  filterAllRecommendation: IFilterAllRecommendationParam;
  filterFollowingRecommendation: IFilterFollowingRecommnendationParam;
  filterOpeningRecommendation: IFilterOpeningRecommnendationParam;
  filterClosingRecommendation: IFilterClosingRecommnendationParam;
  filterAllocationRecomendations: IFilterAllocationRecomendationsParam;
  filterSearchTextValueAllRecommendation: IFilterSearchTextValueRecommendationParam;
  filterSearchTextValueFollowingRecommendation: IFilterSearchTextValueRecommendationParam;

  //  API
  recommendationList: IRecommendationListResponse[];
  search: string;
  tag: string;
}

export interface IUpdateRecommendation {
  customer: string[] | null;
  customerGroup: string[] | null;
  id: string | null;
  status?: number | null;
  openPrice?: number | null;
  closePrice?: number | null;
  open1?: number | null;
  open2?: number | null;
  lossPrice?: number | null;
  takeProfit?: number | null;
  takeProfit1?: number | null;
  takeProfit2?: number | null;
}

export interface IFilterFollowingRecommnendationParam {
  stockCodes: string[] | null;
  customer: string[] | null;
  customerGroup: string[] | null;
  rangeDateHold: IRangeFilter;
  date: IRangeFilter;
  rangePotential: IRangeFilter;
  isFilter: boolean;
}

export interface IFilterOpeningRecommnendationParam {
  stockCodes: string[] | null;
  customer: string[] | null;
  customerGroup: string[] | null;
  rangeDateHold: IRangeFilter;
  date: IRangeFilter;
  rangePotential: IRangeFilter;
  rangeNotRecord: IRangeFilter;
  isFilter: boolean;
}

export interface IFilterClosingRecommnendationParam {
  stockCodes: string[] | null;
  customer: string[] | null;
  customerGroup: string[] | null;
  rangeDateHold: IRangeFilter;
  date: IRangeFilter;
  rangePotential: IRangeFilter;
  rangeRecord: IRangeFilter;
  isFilter: boolean;
}

export interface IFilterAllocationRecomendationsParam {
  personSent: string[] | null;
  customer: string[] | null;
  date: IRangeFilter;
  assetsAllocationFrom: string[] | null;
  assetsAllocationTo: string[] | null;

  rangeProportion: IRangeFilter;
  isFilter: boolean;
}

export interface IAssetTypeOptions {
  recommended: string;
  isSelect: boolean;
  [key: string]: string | boolean;
}

export interface IFilterSearchTextValueRecommendationParam {
  searchText: string | null;
}

export interface IRecommendationListResponse {
  id: string;
  recommendDate: string;
  status: string;
  stockCode: string;
  marketCode: string;
  openDate: string;
  closeDate: string;
  recommendPrice: {
    from?: number;
    to?: number;
  };
  openPrice?: number;
  closePrice?: number;
  currentPrice?: number | string;
  notRecord?: number;
  recorded: number;
  targetPrice: {
    from?: number;
    to?: number;
  };
  lossPrice: number;
  potential: number;
  holdingPeriod: number;
  recommendToGroup: [
    {
      adviceToId: string;
      name: string;
      isActive: boolean;
      groupId: string;
      groupCode?: string;
    }
  ];
  recommendToPersonal: string[];
  referencePrice?: string;
  brokerCode: string;
  brokerName: string;
}

export interface IPayloadCloseAdvice {
  priceClose: number;
}

export interface IPayLoadOpenAdivce {
  priceOpen: number;
  priceAdviceCutloss: number;
  priceAdviceTargetTo: number;
  priceAdviceTargetFrom: number;
  accountNumbers: ICustomerAndGroup[];
  customerGroups: ICustomerAndGroup[];
}

export interface ICustomerAndGroup {
  adviceToId: string;
  name: string;
}

export interface IPayloadUpdateFollowingAdvice {
  accountNumbers: ICustomerAndGroup[];
  customerGroups: ICustomerAndGroup[];
  priceAdviceFrom: number;
  priceAdviceTo: number;
  priceAdviceCutloss: number;
  priceAdviceTarget: number;
}

export interface IPayloadUpdateOpenAdvice {
  priceAdviceTargetFrom: number;
  priceAdviceTargetTo: number;
}

export interface IItemStoke {
  value: string;
  stoke: string;
}
