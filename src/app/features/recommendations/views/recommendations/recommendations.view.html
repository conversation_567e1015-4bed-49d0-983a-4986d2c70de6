<div class="recommendations-view-containers">
  <div class="recommendations-header-cls">
    <div class="header-txt typo-heading-8">{{'MES-116' | translate}}</div>
    <div class="header-btn-wrapper">
      <app-create-place-order-component></app-create-place-order-component>

      <div class="header-btn typo-button-5" (click)="openRecommendationDialog()">
        <img src="./assets/icons/add-item.svg" alt="add-item" />
        {{'MES-63' | translate}}
      </div>
    </div>
  </div>
  <div class="sub-menu-cls">
    <!-- <div class="menu-recommendations-cls">
      <a
        class="box-selection"
        mat-list-item
        routerLinkActive="isSelect"
        [routerLink]="item.router"
        *ngFor="let item of menuRecommendations"
      >
        <mat-icon
          class="mat-icon-cls"
          aria-hidden="false"
          aria-label="icon"
          [svgIcon]="item.nameIcon"
          [ngClass]="item.router"
        ></mat-icon>
        <div class="typo-body-6">{{item.name | translate}}</div>
      </a>
    </div> -->

    <mat-tab-group animationDuration="200ms" [selectedIndex]="activeTab">
      <mat-tab *ngFor="let item of menuRecommendations" isActive="item.isActive">
        <ng-template mat-tab-label>
          <div class="menu-recommendations-cls">
            <a
              class="box-selection"
              mat-list-item
              routerLinkActive="isSelect"
              [routerLink]="item.router"
              [queryParams]="brokerId ? {brokerId} : null"
            >
              <mat-icon
                class="mat-icon-cls"
                aria-hidden="false"
                aria-label="icon"
                [svgIcon]="item.nameIcon"
                [ngClass]="item.router"
              ></mat-icon>
              <div class="typo-body-6">{{item.name | translate}}</div>
            </a>
          </div>
        </ng-template>
      </mat-tab>
    </mat-tab-group>

    <div class="search-cls">
      <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
      <input
        [formControl]="searchTextControl"
        class="input-cls input-style-common typo-body-12"
        type="text"
        [placeholder]="'MES-14' | translate"
      />
    </div>
  </div>
  <div class="router-container-cls"><router-outlet></router-outlet></div>
</div>
