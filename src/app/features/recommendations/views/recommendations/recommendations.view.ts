import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { DestroyService, DialogService } from 'src/app/core/services';
import { RecommendationDialogComponent } from 'src/app/shared/components/stock/recommendation-dialog/recommendation-dialog.component';
import { ERecommendationType, IRecommendationDialogData } from 'src/app/shared/models/stock.model';
import { debounceTime, filter, take, takeUntil } from 'rxjs';
import { Store } from '@ngrx/store';
import { getSearchValue, getTagParams, resetSearchValue } from '../../stores/recommendation.actions';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { getCurrentBrokerView, getMainBrokerCustomerGroups } from 'src/app/stores/shared/shared.actions';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';
import { ICustomerListInRecommendationDialog } from 'src/app/shared/models/global';
import { selectInfoUserLogin$ } from 'src/app/stores/auth/auth.selectors';
import { SockJSClientService } from 'src/app/shared/services/realtime/sockjs-client.service';
import { TOPIC } from 'src/app/shared/constants/trading';
import { RecommendationStoreService } from '../../services/recommendation-store.service';

export enum ERecommendationUrl {
  ALL = 'all-recommendations',
  FOLLOWING = 'recommendations-following',
  OPENED = 'recommendations-opened',
  CLOSED = 'recommendations-closed',
  ALLOCATION = 'recommendations-allocation',
}

export const CONVERT_RECOMMENDATION_URL_MATCH_TO_TAG_PARAM: { [key: string]: string } = {
  [ERecommendationUrl.ALL]: 'ALL',
  [ERecommendationUrl.FOLLOWING]: 'FOLLOWING',
  [ERecommendationUrl.OPENED]: 'OPENED',
  [ERecommendationUrl.CLOSED]: 'CLOSED',
  [ERecommendationUrl.ALLOCATION]: 'ALLOCATION',
};

/**
 * RecommendationsView
 */
@Component({
  selector: 'app-recommendations',
  templateUrl: './recommendations.view.html',
  styleUrl: './recommendations.view.scss',
  providers: [DestroyService],
})
export class RecommendationsView implements OnInit, OnDestroy {
  searchTextControl = new FormControl();
  selectedTab: number = 0;
  activeTab = 0;
  menuRecommendations = [
    {
      router: 'all-recommendations',
      name: 'MES-111',
      nameIcon: 'icon:all-recommendations',
    },
    {
      router: 'recommendations-following',
      name: 'MES-112',
      nameIcon: 'icon:eye',
    },
    {
      router: 'recommendations-opened',
      name: 'MES-113',
      nameIcon: 'icon:play',
    },
    {
      router: 'recommendations-closed',
      name: 'MES-114',
      nameIcon: 'icon:menu-close',
    },
    // {
    //   router: 'recommendations-allocation',
    //   name: 'MES-115',
    //   nameIcon: 'icon:percentage-icon',
    // },
  ];

  customerList: ICustomerListInRecommendationDialog[] = [];
  customerGroupList: ICustomerListInRecommendationDialog[] = [];
  brokerCode = '';
  brokerId = '';

  /**
   * Find active route on page reload
   * @param dialogService
   * @param _destroy
   * @param store
   * @param router
   */
  constructor(
    private readonly dialogService: DialogService,
    private readonly _destroy: DestroyService,
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly socketJs: SockJSClientService,
    private readonly recommendationStoreService: RecommendationStoreService
  ) {
    this.updateActiveTab(router.url);
    this.getTagParam();

    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this._destroy)
      )
      .subscribe((event: any) => {
        const url = event.urlAfterRedirects;
        this.updateActiveTab(url);
        this.resetSearchControl();
        this.updateBrokerId();
        this.getTagParam();
      });

    this.route.queryParams.pipe(takeUntil(this._destroy)).subscribe((param) => {
      this.brokerId = param['brokerId'];
    });

    this.searchTextControl.valueChanges.pipe(debounceTime(300), takeUntil(this._destroy)).subscribe((searchValue) => {
      if (!searchValue.trim() && searchValue.length) return;
      this.store.dispatch(getSearchValue({ search: searchValue.trim().toLowerCase() }));
    });


    this.store.select(selectInfoUserLogin$)
  }

  ngOnDestroy(): void {
    this.socketJs.unSubscribe(TOPIC.market);
  }
  /**
   * Update active tab index based on current URL
   * @param url - Current URL
   */
  updateActiveTab(url: string) {
    this.activeTab = this.menuRecommendations.findIndex((item) => url.includes(item.router));
  }

  /**
   * handle open recommendation dialog
   */
  openRecommendationDialog() {
    this.dialogService.openRightDialog(RecommendationDialogComponent, {
      width: '560px',
      data: <IRecommendationDialogData>{
        title: 'MES-124',
        type: ERecommendationType.CREATE,
        brokerCode: this.brokerCode,
        codesSelected: this.recommendationStoreService.getCodesSelected(),
      },
    });
  }

  /**
   * getTagParam
   */
  getTagParam() {
    const urlMatch = this.menuRecommendations.find((menu) => this.router.url.includes(menu.router))?.router;
    if (urlMatch && urlMatch !== ERecommendationUrl.ALLOCATION) {
      this.store.dispatch(getTagParams({ tag: CONVERT_RECOMMENDATION_URL_MATCH_TO_TAG_PARAM[urlMatch] }));
    }
  }

  /**
   * The Onit
   */
  ngOnInit() {
    this.updateBrokerId();

    this.store.select(selectInfoUserLogin$).pipe(takeUntil(this._destroy)).subscribe((users) => {
          if(!users?.length) return;
      const { brokerCode} = users[0];
      this.store.dispatch(getMainBrokerCustomerGroups({ brokerCode}));
    })

    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        if (!user) return;
        this.brokerCode = user.brokerCode;
      });
  }

  /**
   * Resets the searchControl value
   */
  resetSearchControl() {
    this.store.dispatch(resetSearchValue());
    this.searchTextControl.patchValue('');
  }

  updateBrokerId() {
    const queryParamsRe = this.route.snapshot.queryParams;
    if (queryParamsRe['brokerId']) {
      this.store
        .select(selectInfoUserLogin$)
        .pipe(take(1))
        .subscribe((userListRe) => {
          if (!userListRe) return;
          const currentBroker = userListRe.find((user) => user.brokerCode === queryParamsRe['brokerId']);
          if (!currentBroker) return;
          this.store.dispatch(getCurrentBrokerView({ data: currentBroker }));
        });
    } else {
      this.store
        .select(selectCurrentBrokerView$)
        .pipe(take(1))
        .subscribe((brokerRe) => {
          this.router.navigate([], {
            queryParams: {
              ...queryParamsRe,
              brokerId: brokerRe.brokerCode,
            },
            queryParamsHandling: 'merge',
          });
        });
    }
  }
}
