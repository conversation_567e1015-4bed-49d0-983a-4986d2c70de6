import { Component, OnInit } from '@angular/core';
import { IColumnConfig } from '@shared/models';
import { CONVERT_CUSTOMER_LEVEL_TO_LABLE, ECustomerLevel } from '../../constants/account-info';
import { Store } from '@ngrx/store';
import { getCustomerAccountInfo } from '../../stores/account-info.actions';
import { selectCustomerAccountInfo$ } from '../../stores/account-info.selectors';
import { takeUntil } from 'rxjs';
import { DestroyService } from 'src/app/core/services';

/**
 * ManagerCustomerComponent
 */
@Component({
  selector: 'app-manager-customer',
  templateUrl: './manager-customer.component.html',
  styleUrl: './manager-customer.component.scss',
})
export class ManagerCustomerComponent implements OnInit {
  columnConfigs: IColumnConfig[] = [];

  data: any = [
    {
      name: 'CTCP Quỹ đầu tư VVV',
      accountNumber: '069C-000000',
      customerLevel: 'Normal',
      customerGroup: 'Nhóm Lướt sóng',
    },
    {
      name: 'CTCP Quỹ đầu tư VVV',
      accountNumber: '069C-000000',
      customerLevel: 'Platinum',
      customerGroup: 'Nhóm Lướt sóng',
    },
    {
      name: 'CTCP Quỹ đầu tư VVV',
      accountNumber: '069C-000000',
      customerLevel: 'Gold',
      customerGroup: 'Nhóm Đầu tư lâu dài',
    },
    {
      name: 'CTCP Quỹ đầu tư VVV',
      accountNumber: '069C-000000',
      customerLevel: 'Diamond',
      customerGroup: 'Nhóm Lướt sóng',
    },
    {
      name: 'CTCP Quỹ đầu tư VVV',
      accountNumber: '069C-000000',
      customerLevel: 'Normal',
      customerGroup: 'Nhóm Đầu tư trung hạn',
    },
    {
      name: 'Nguyễn Tuấn Anh',
      accountNumber: '069C-000001',
      customerLevel: 'Gold',
      customerGroup: 'Nhóm Lướt sóng',
    },
    {
      name: 'Nguyễn Tuấn Anh',
      accountNumber: '069C-000001',
      customerLevel: 'Normal',
      customerGroup: 'Nhóm Đầu tư trung hạn',
    },
  ];

  constructor(private readonly store: Store, private readonly _destroy: DestroyService) {}

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.store.dispatch(getCustomerAccountInfo());

    this.store
      .select(selectCustomerAccountInfo$)
      .pipe(takeUntil(this._destroy))
      .subscribe((accountInfo) => {
        this.data = accountInfo.map((acc) => ({
          name: acc.customerName,
          accountNumber: acc.accountNumber,
          customerLevel: acc.customerLevel,
          customerGroup: acc.customerGroup,
        }));
      });

    this.columnConfigs = [
      {
        // Tên khách hàng
        name: 'MES-184',
        minWidth: 30,
        width: 200,
        tag: 'name',
        isDisplay: true,
        resizable: true,
      },
      {
        // Số tài khoản
        name: 'MES-66',
        minWidth: 30,
        width: 110,
        tag: 'accountNumber',
        isDisplay: true,
        resizable: true,
        align: 'start',
      },
      {
        // Hạng khách hàng
        name: 'MES-310',
        minWidth: 30,
        width: 140,
        tag: 'customerLevel',
        isDisplay: true,
        displayValueFn: (value) => {
          if (!value) return '-';
          return CONVERT_CUSTOMER_LEVEL_TO_LABLE[value];
        },
        dynamicClass: (value) => {
          if (value === ECustomerLevel.PLATINUM) {
            return 'custom-span-level platinum-cls';
          } else if (value === ECustomerLevel.DIAMOND) {
            return 'custom-span-level diamond-cls';
          } else if (value === ECustomerLevel.GOLD) {
            return 'custom-span-level gold-cls';
          } else if (value === ECustomerLevel.NORMAL) {
            return 'custom-span-level normal-cls';
          } else return 'custom-span-level';
        },
        align: 'center',
        isEdit: true,
        resizable: true,
      },
      {
        // Nhóm khách hàng
        name: 'MES-13',
        minWidth: 30,
        width: 130,
        tag: 'customerGroup',
        isDisplay: true,
        // displayValueFn: (value) => CONVERT_CUSTOMER_GROUP_TO_LABLE[value],
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
        isEdit: true,
        resizable: true,
      },
    ];
  }
}
