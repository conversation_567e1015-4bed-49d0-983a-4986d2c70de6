.manager-customer-container {
  height: 100%;
  .table-custom-cls {
    ::ng-deep {
      .custom-span-level {
        min-width: 120px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        text-transform: uppercase;
        text-transform: uppercase;
        border-radius: 16px;

        max-width: 120px;
        margin: 0 auto;

        .input-table-view {
          text-align: center;
          text-transform: uppercase;
        }

        .isEditMode {
          border-color: transparent !important;
          text-align: unset;
        }

        &.platinum-cls {
          background-color: var(--color--accents--orange-dark) !important;
        }

        &.diamond-cls {
          background-color: var(--color--accents--mint-dark) !important;
        }

        &.gold-cls {
          background-color: var(--color--accents--yellow-dark) !important;
        }

        &.normal-cls {
          background-color: var(--color--neutral--100) !important;
        }

        &.change-value-mode {
          border-radius: 16px;
          border: 1px solid var(--color--accents--yellow);
          background-color: unset !important;
        }
      }
    }
  }

  .label {
    max-width: 316px;
    .percent-asset {
      background-color: var(--color--accents--yellow-dark);
      padding: 3px 8px;
      border-radius: 16px;
      min-width: 144px;
      display: inline-block;
      text-align: center;
    }
  }
}
