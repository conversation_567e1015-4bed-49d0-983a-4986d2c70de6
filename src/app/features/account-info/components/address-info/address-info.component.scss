.address-info-component {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 16px;
  flex-direction: column;
  .address-info-top {
    display: flex;
    width: 100%;
    height: 100%;
    gap: 16px;
  }
  .box-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 16px;
    .top-box-cls {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    .bottom-box-cls {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    .box-info {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .box-edit-mode {
        background-color: var(--color--neutral--white);
        padding: 8px;
        border: 1px solid var(--color--other--divider);
        border-radius: 8px;
        position: relative;
        .p-3-px {
          padding: 3px;
        }
        .input-cls-box {
          border-radius: 0;
          height: 100%;
          width: 100%;
          border: 0;
          padding: 3px;
          &:hover,
          &:focus {
            border: 0;
            border-color: transparent;
          }
        }

        &.dropdown-box {
          display: flex;
          align-items: center;
          cursor: pointer;
          height: 100%;
          width: 100%;
          justify-content: space-between;
          & > span {
            min-height: 22px;
          }
          img[alt='calendar-black'] {
            height: 24px;
          }
        }
      }
    }
    .opacity-50 {
      opacity: 0.5;
    }
  }

  .gap-8-px-cls {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .gray-color-cls {
    color: var(--color--text--subdued);
  }
}
