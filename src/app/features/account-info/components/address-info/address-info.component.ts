import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { take, takeUntil } from 'rxjs';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { LIST_OF_NATIONALITY } from 'src/app/shared/constants/nationality';
import { DestroyService } from 'src/app/core/services';
import { compareObject } from 'src/app/shared/utils/compareForm';
import { LocationService } from 'src/app/core/services/location.service';

/**
 * AddressInfoComponent
 */
@Component({
  selector: 'app-address-info',
  templateUrl: './address-info.component.html',
  styleUrl: './address-info.component.scss',
})
export class AddressInfoComponent implements OnInit {
  @Input() isEditMode = false;

  @Output() dataChangeEvent = new EventEmitter<boolean>();

  addressInfoForm: FormGroup;

  initialData: any;

  locations: any[] = [];
  districts: any[] = [];
  wards: any[] = [];

  /**
   * Constructor
   * @param fb
   * @param popoverService
   * @param _destroy
   * @param locationService
   */
  constructor(
    private readonly fb: FormBuilder,
    private readonly popoverService: PopoverService,
    private readonly _destroy: DestroyService,
    private readonly locationService: LocationService
  ) {
    this.addressInfoForm = this.fb.group({
      nationality: [null, [Validators.required]],
      province: [null, [Validators.required]],
      district: [null, [Validators.required]],
      ward: [null, [Validators.required]],
      address: [null],
    });
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.addressInfoForm.patchValue({
      nationality: '-',
      province: '-',
      district: '-',
      ward: '-',
      address: '-',
    });

    this.initialData = {
      nationality: '-',
      province: '-',
      district: '-',
      ward: '-',
      address: '-',
    };

    this.addressInfoForm.valueChanges.pipe(takeUntil(this._destroy)).subscribe((value) => {
      if (this.isEditMode) {
        this.dataChangeEvent.emit(!(compareObject(this.initialData, value) || this.addressInfoForm.invalid));
      }
    });

    // Remove when needed
    // this.locationService
    //   .getLocations()
    //   .pipe(takeUntil(this._destroy))
    //   .subscribe((data) => {
    //     this.locations = data;
    //     // Check initial value of province and update districts
    //     const initialProvince = this.getFormControl('province')?.value;
    //     if (initialProvince) {
    //       const province = this.locations.find((location) => location.name === initialProvince);
    //       this.districts = province ? province.district : [];
    //     }
    //     // Check initial value of district and update ward
    //     const initialDistrict = this.getFormControl('district')?.value;
    //     if (initialDistrict) {
    //       const district = this.districts.find((d) => d.name === initialDistrict);
    //       this.wards = district ? district.ward : [];
    //     }
    //   });
    // this.addressInfoForm
    //   .get('province')
    //   ?.valueChanges.pipe(takeUntil(this._destroy))
    //   .subscribe((city) => {
    //     this.updateDistricts(city);
    //   });
    // this.addressInfoForm
    //   .get('district')
    //   ?.valueChanges.pipe(takeUntil(this._destroy))
    //   .subscribe((district) => {
    //     this.updateWards(district);
    //   });
  }

  /**
   * updateDistricts
   * @param provinceName provinceName
   */
  updateDistricts(provinceName: string): void {
    const province = this.locations.find((location) => location.name === provinceName);
    this.districts = province ? province.district : [];
    this.resetDataDistrict();
    this.wards = [];
    this.resetDataWard();
  }

  /**
   * updateWards
   * @param districtName districtName
   */
  updateWards(districtName: string): void {
    const district = this.districts.find((d) => d.name === districtName);
    this.wards = district ? district.ward : [];
    this.resetDataWard();
  }

  /**
   * GetFormControl
   * @param field
   * @returns {any} control from form
   */
  getFormControl(field: string) {
    return this.addressInfoForm.get(field) as FormControl;
  }

  /**
   * OpenSelectionNationality
   * @param event
   */
  openSelectionNationality(event: MouseEvent) {
    let origin = event.target as HTMLElement;
    if (origin.tagName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }
    const nationality = this.getFormControl('nationality')?.value;

    const value = LIST_OF_NATIONALITY.filter((t) => t.name === nationality) ?? [];

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      width: origin.offsetWidth,
      height: '300px',
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: LIST_OF_NATIONALITY,
        value,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.getFormControl('nationality')?.patchValue(item[0].name);
      this.resetDataProvince();
      this.resetDataDistrict();
      this.resetDataWard();
    });
  }

  /**
   * OpenSelectionProvince
   * @param event
   * @returns {void} prevent event
   */
  openSelectionProvince(event: MouseEvent) {
    if (!this.getFormControl('nationality').value) return;
    let origin = event.target as HTMLElement;
    if (origin.tagName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }
    const province = this.getFormControl('province')?.value;

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      width: origin.offsetWidth,
      height: '300px',
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: this.locations,
        value: [province],
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.getFormControl('province')?.patchValue(item[0].name);
      this.resetDataDistrict();
      this.resetDataWard();
    });
  }

  /**
   * OpenSelectionDistrict
   * @param event
   * @returns {void} prevent event
   */
  openSelectionDistrict(event: MouseEvent) {
    if (!this.getFormControl('province').value) return;
    let origin = event.target as HTMLElement;
    if (origin.tagName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }
    const district = this.getFormControl('district')?.value;

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      width: origin.offsetWidth,
      height: '300px',
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: this.districts,
        value: [district],
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.getFormControl('district')?.patchValue(item[0].name);
      this.resetDataWard();
    });
  }

  /**
   * OpenSelectionWard
   * @param event
   * @returns {void} prevent event
   */
  openSelectionWard(event: MouseEvent) {
    if (!this.getFormControl('district').value) return;
    let origin = event.target as HTMLElement;
    if (origin.tagName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }
    const ward = this.getFormControl('ward')?.value;

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      width: origin.offsetWidth,
      height: '300px',
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: this.wards,
        value: [ward],
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.getFormControl('ward')?.patchValue(item[0].name);
    });
  }

  /**
   * ResetDataProvince
   */
  resetDataProvince() {
    this.getFormControl('province').setValue(null);
  }

  /**
   * ResetDataDistrict
   */
  resetDataDistrict() {
    this.getFormControl('district').setValue(null);
  }

  /**
   * ResetDataWard
   */
  resetDataWard() {
    this.getFormControl('ward').setValue(null);
  }

  /**
   * ResetValueForm
   */
  resetValueForm() {
    this.addressInfoForm.patchValue({
      ...this.initialData,
    });
  }

  /**
   * UpdateValueForm
   */
  updateValueForm() {
    this.initialData = {
      ...this.addressInfoForm.value,
    };
  }
}
