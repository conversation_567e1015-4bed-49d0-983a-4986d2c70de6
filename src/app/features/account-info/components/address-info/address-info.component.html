<div class="address-info-component">
  <div class="address-info-top">
    <div class="box-container">
      <div class="top-box-cls">
        <!-- Quốc gia -->
        <div class="box-info">
          <div class="typo-body-9 gray-color-cls">{{ 'MES-353' | translate }}</div>
          <div *ngIf="!isEditMode" class="typo-body-8">{{ getFormControl('nationality').value }}</div>
          <div *ngIf="isEditMode" class="box-edit-mode dropdown-box" (click)="openSelectionNationality($event)">
            <span class="typo-body-9 p-3-px">{{ getFormControl('nationality').value }}</span>
            <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
          </div>
        </div>
        <!-- Quận / Huyện -->
        <div class="box-info">
          <div class="typo-body-9 gray-color-cls">{{ 'MES-356' | translate }}</div>
          <div *ngIf="!isEditMode" class="typo-body-8">{{ getFormControl('district').value }}</div>
          <div
            [class.opacity-50]="!getFormControl('province').value"
            *ngIf="isEditMode"
            class="box-edit-mode dropdown-box"
            (click)="getFormControl('nationality').value === 'Việt Nam' ? openSelectionDistrict($event) : null"
          >
            <span class="typo-body-9 p-3-px">{{ getFormControl('district').value }}</span>
            <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
          </div>
        </div>
      </div>
    </div>
    <div class="box-container">
      <div class="bottom-box-cls">
        <!-- Tỉnh / Thành phố -->
        <div class="box-info">
          <div class="typo-body-9 gray-color-cls">{{ 'MES-357' | translate }}</div>
          <div *ngIf="!isEditMode" class="typo-body-8">
            {{ getFormControl('province').value }}
          </div>
          <div
            [class.opacity-50]="!getFormControl('nationality').value"
            *ngIf="isEditMode"
            class="box-edit-mode dropdown-box"
            (click)="getFormControl('nationality').value === 'Việt Nam' ? openSelectionProvince($event) : null"
          >
            <span class="typo-body-9 p-3-px">{{ getFormControl('province').value }}</span>
            <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
          </div>
        </div>
        <!-- Xã / Phường -->
        <div class="box-info">
          <div class="typo-body-9 gray-color-cls">{{ 'MES-355' | translate }}</div>
          <div *ngIf="!isEditMode" class="typo-body-8">{{ getFormControl('ward').value }}</div>
          <div
            [class.opacity-50]="!getFormControl('district').value"
            *ngIf="isEditMode"
            class="box-edit-mode dropdown-box"
            (click)="getFormControl('nationality').value === 'Việt Nam' ? openSelectionWard($event) : null"
          >
            <span class="typo-body-9 p-3-px">{{ getFormControl('ward').value }}</span>
            <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="box-container">
    <div class="bottom-box-cls">
      <!-- Địa chỉ -->
      <div class="box-info">
        <div class="typo-body-9 gray-color-cls">{{ 'MES-354' | translate }}</div>
        <div *ngIf="!isEditMode" class="typo-body-8">{{ getFormControl('address').value }}</div>
        <div *ngIf="isEditMode" class="box-edit-mode">
          <input [formControl]="getFormControl('address')" class="input-style-common typo-body-9 input-cls-box" />
        </div>
      </div>
    </div>
  </div>
</div>
