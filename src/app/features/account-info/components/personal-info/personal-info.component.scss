.personal-info-component {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 16px;
  flex-direction: column;
  .box-container {
    display: flex;
    flex: 1;
    // flex-direction: column;
    gap: 16px;
    .top-box-cls {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      &:has(.avatar-box) {
        align-items: center;
        justify-content: center;
        .avatar-box {
          position: relative;
          width: 125px;
          height: auto;
          .btn-edit {
            position: absolute;
            text-align: center;
            bottom: 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            width: 100%;
            padding: 14px 12px;
            background-color: var(--color--materials-controls--popover-dark);
            cursor: pointer;
          }
        }
      }
    }
    .bottom-box-cls {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      app-form-control {
        &:has(mat-error) {
          ::ng-deep {
            .form_err_msg {
              display: none;
            }
          }
        }
      }
    }
    .box-info {
      display: flex;
      flex-direction: column;
      gap: 10px;

      & > div > img[alt='phone'] {
        height: 20px;
      }
      .box-edit-mode {
        background-color: var(--color--neutral--white);
        padding: 8px;
        border: 1px solid var(--color--other--divider);
        border-radius: 8px;
        position: relative;
        .p-3-px {
          padding: 3px;
        }
        &.box-phone {
          padding-left: 0;
          app-phone-number-table-component {
            ::ng-deep {
              .phone-number-cls {
                padding: 0px 4px;
                &:focus {
                  border-color: transparent;
                }
              }
            }
          }
        }
        .input-cls-box {
          border-radius: 0;
          height: 100%;
          width: 100%;
          border: 0;
          padding: 3px;
          padding-right: 32px;
          &:hover,
          &:focus {
            border: 0;
            border-color: transparent;
          }
        }

        img[alt='sms'] {
          position: absolute;
          top: 50%;
          right: 8px;
          transform: translateY(-50%);
        }

        &.dropdown-box {
          display: flex;
          align-items: center;
          cursor: pointer;
          height: 100%;
          width: 100%;
          justify-content: space-between;
          img[alt='calendar-black'] {
            height: 24px;
          }
        }
      }
    }
  }

  .gap-8-px-cls {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .gray-color-cls {
    color: var(--color--text--subdued);
  }
}

form {
  &:has(.personal-info-component) {
    width: 100%;
    height: 100%;
  }
}
