import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, provideNativeDateAdapter } from '@angular/material/core';
import { DatePickerNavigationFullDateComponent } from 'src/app/shared/components/date-picker/date-picker-navigation-full-date/date-picker-navigation-full-date.component';
import { MY_DATE_FORMAT } from 'src/app/shared/constants/date-picker';
import { dateToDMY } from 'src/app/shared/utils/date';
import { CONVERT_GENDER_LEVEL_TO_LABLE, List_options_gender } from '../../constants/account-info';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { take, takeUntil } from 'rxjs';
import { LIST_OF_NATIONALITY } from 'src/app/shared/constants/nationality';
import { DestroyService } from 'src/app/core/services';
import { compareObject } from 'src/app/shared/utils/compareForm';
import { IInfoUserLogined } from 'src/app/shared/models/global';

/**
 * PersonalInfoComponent
 * Thông tin cá nhân
 */
@Component({
  selector: 'app-personal-info',
  templateUrl: './personal-info.component.html',
  styleUrl: './personal-info.component.scss',
  providers: [
    { provide: MAT_DATE_LOCALE, useValue: 'vi' },
    provideNativeDateAdapter(),
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
  ],
})
export class PersonalInfoComponent implements OnInit, OnChanges {
  @Input() isEditMode = false;

  @Input() personalInfoData!: IInfoUserLogined | null;

  personalInfoForm: FormGroup;

  phoneNumber = '';

  DatepickerCustomHeader = DatePickerNavigationFullDateComponent;

  CONVERT_GENDER_LEVEL_TO_LABLE = CONVERT_GENDER_LEVEL_TO_LABLE;

  initialData: any;

  @Output() dataChangeEvent = new EventEmitter<boolean>();

  /**
   * Constructor
   * @param fb
   * @param popoverService
   * @param _destroy
   */
  constructor(
    private readonly fb: FormBuilder,
    private readonly popoverService: PopoverService,
    private readonly _destroy: DestroyService
  ) {
    this.personalInfoForm = this.fb.group({
      phoneNumber: [null],
      email: [null, [Validators.email, Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')]],
      name: [null],
      nationality: [null],
      gender: [0],
      dob: [null],
    });
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.phoneNumber = this.getFormControl('phoneNumber')?.value;

    this.personalInfoForm.valueChanges.pipe(takeUntil(this._destroy)).subscribe((value) => {
      if (this.isEditMode) {
        this.dataChangeEvent.emit(!(compareObject(this.initialData, value) || this.personalInfoForm.invalid));
      }
    });
  }

  /**
   * NgOnChanges
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    const { isEditMode, personalInfoData } = changes;

    if (isEditMode) {
      // update lại phoneNumber
      this.phoneNumber = this.getFormControl('phoneNumber')?.value;
    }

    if (personalInfoData) {
      // FIXME: update this later
      this.personalInfoForm.patchValue({
        name: this.personalInfoData?.brokerName,
        phoneNumber: '-',
        // email: '<EMAIL>',
        // gender: 0,
        nationality: '-',
        // dob: ConvertToDate('07/07/1997'),
      });

      this.personalInfoForm.updateValueAndValidity();
    }
  }

  /**
   * getFormControl
   * @param field
   * @returns {any} new control
   */
  getFormControl(field: string) {
    return this.personalInfoForm.get(field) as FormControl;
  }

  /**
   * ConvertTimeShow
   * @param date
   * @returns {any} new date
   */
  convertTimeShow(date: Date | string) {
    return dateToDMY(date);
  }

  /**
   * OpenSelectionGender
   * @param event
   */
  openSelectionGender(event: MouseEvent) {
    let origin = event.target as HTMLElement;
    if (origin.tagName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }
    const gender = this.getFormControl('gender')?.value;

    const value = List_options_gender.filter((t) => t.value === gender);

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      width: origin.offsetWidth,
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100'],
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'label',
        options: List_options_gender,
        value,
        displayOptionFn: (v: any) => v.label,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.getFormControl('gender')?.patchValue(item[0].value);
    });
  }

  /**
   * OpenSelectionNationality
   * @param event
   */
  openSelectionNationality(event: MouseEvent) {
    let origin = event.target as HTMLElement;
    if (origin.tagName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }
    const nationality = this.getFormControl('nationality')?.value;

    const value = LIST_OF_NATIONALITY.filter((t) => t.name === nationality);

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      width: origin.offsetWidth,
      height: '300px',
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: LIST_OF_NATIONALITY,
        value,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.getFormControl('nationality')?.patchValue(item[0].name);
    });
  }

  /**
   * PhoneNumberChange
   * @param event
   */
  phoneNumberChange(event: string) {
    this.getFormControl('phoneNumber')?.patchValue(event);
  }

  /**
   * ResetValueForm
   */
  resetValueForm() {
    this.personalInfoForm.patchValue({
      ...this.initialData,
    });
  }

  /**
   * UpdateValueForm
   */
  updateValueForm() {
    this.initialData = {
      ...this.personalInfoForm.value,
    };
  }
}
