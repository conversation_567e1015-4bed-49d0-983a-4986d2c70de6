<form [formGroup]="personalInfoForm">
  <div class="personal-info-component">
    <div class="box-container">
      <div class="top-box-cls">
        <!-- Avatar -->
        <div class="avatar-box">
          <img src="./assets/images/Avatar-detail.png" alt="avatar" />
          <!-- FIXME: bỏ comment sau khi có API -->
          <!-- <div class="btn-edit typo-body-10">
            <img src="./assets/icons/edit-black.svg" alt="edit-icon" />{{ 'MES-16' | translate }}
          </div> -->
        </div>
      </div>
      <div class="bottom-box-cls">
        <!-- S<PERSON> điện thoại -->
        <div class="box-info">
          <div class="typo-body-9 gray-color-cls">{{ 'MES-349' | translate }}</div>
          <div *ngIf="!isEditMode" class="typo-body-8 gap-8-px-cls">
            <img src="./assets/icons/phone.svg" alt="phone" />
            {{ getFormControl('phoneNumber').value }}
          </div>
          <div *ngIf="isEditMode" class="box-edit-mode box-phone">
            <app-phone-number-table-component
              [isEdit]="isEditMode"
              [data]="phoneNumber"
              [isShowArrowDown]="true"
              [phoneNumberOutSide]="true"
              (phoneNumberUpdateEvent)="phoneNumberChange($event)"
            ></app-phone-number-table-component>
          </div>
        </div>

        <!-- Email -->
        <div class="box-info">
          <div class="typo-body-9 gray-color-cls">
            {{ 'MES-394' | translate }}
          </div>
          <div *ngIf="!isEditMode" class="typo-body-8 gap-8-px-cls">
            <img src="./assets/icons/sms.svg" alt="sms" />
            {{ getFormControl('email').value ? getFormControl('email').value : '-' }}
          </div>
          <app-form-control>
            <div *ngIf="isEditMode" class="box-edit-mode">
              <input formControlName="email" class="input-style-common typo-body-9 input-cls-box" />
              <img src="./assets/icons/sms-black.svg" alt="sms" />
            </div>
            <mat-error class="typo-body-9" *ngIf="getFormControl('email').invalid">{{
              'MES-102' | translate
            }}</mat-error>
          </app-form-control>
        </div>
      </div>
    </div>
    <div class="box-container">
      <div class="top-box-cls">
        <!-- Họ Tên -->
        <div class="box-info">
          <div class="typo-body-9 gray-color-cls">{{ 'MES-348' | translate }}</div>
          <div *ngIf="!isEditMode" class="typo-body-8">{{ getFormControl('name').value }}</div>
          <app-form-control>
            <div *ngIf="isEditMode" class="box-edit-mode">
              <input formControlName="name" class="input-style-common typo-body-9 input-cls-box" />
            </div>
          </app-form-control>
        </div>
        <!-- Giới tính -->
        <div class="box-info">
          <div class="typo-body-9 gray-color-cls">{{ 'MES-351' | translate }}</div>
          <div *ngIf="!isEditMode" class="typo-body-8">
            <!-- FIXME: thay đổi khi có dữ liệu ( value có thể là 0  -> fasle) -->
            {{ getFormControl('gender').value ? CONVERT_GENDER_LEVEL_TO_LABLE[getFormControl('gender').value] : '-' }}
          </div>
          <app-form-control>
            <div *ngIf="isEditMode" class="box-edit-mode dropdown-box" (click)="openSelectionGender($event)">
              <span class="typo-body-9 p-3-px">{{
                CONVERT_GENDER_LEVEL_TO_LABLE[getFormControl('gender').value]
              }}</span>
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>
      <div class="bottom-box-cls">
        <!-- Quốc tịch -->
        <div class="box-info">
          <div class="typo-body-9 gray-color-cls">{{ 'MES-352' | translate }}</div>
          <div *ngIf="!isEditMode" class="typo-body-8">{{ getFormControl('nationality').value }}</div>
          <app-form-control>
            <div *ngIf="isEditMode" class="box-edit-mode dropdown-box" (click)="openSelectionNationality($event)">
              <span class="typo-body-9 p-3-px">{{ getFormControl('nationality').value }}</span>
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
        <!-- Ngày sinh -->
        <div class="box-info">
          <div class="typo-body-9 gray-color-cls">
            {{ 'MES-350' | translate }}
          </div>
          <div *ngIf="!isEditMode" class="typo-body-8">
            {{ getFormControl('dob').value ? convertTimeShow(getFormControl('dob').value) : '-' }}
          </div>
          <app-form-control>
            <div *ngIf="isEditMode" class="box-edit-mode dropdown-box">
              <input
                [matDatepicker]="datePicker"
                formControlName="dob"
                class="input-style-common typo-body-9 input-cls-box"
              />

              <img src="./assets/icons/calendar-black.svg" alt="calendar-black" (click)="datePicker.open()" />

              <mat-datepicker
                panelClass="calendar-margin-top-bottom-12-px"
                #datePicker
                panelClass="calendar-cls fs-12-px"
                [calendarHeaderComponent]="DatepickerCustomHeader"
              >
              </mat-datepicker>
            </div>
          </app-form-control>
        </div>
      </div>
    </div>
  </div>
</form>
