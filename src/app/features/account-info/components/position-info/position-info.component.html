<div class="position-info-component">
  <div class="box-container">
    <div class="top-box-cls">
      <!-- Phòng ban -->
      <div class="box-info">
        <div class="typo-body-9 gray-color-cls">{{ 'MES-481' | translate }}</div>
        <div *ngIf="!isEditMode" class="typo-body-8">{{ getFormControl('brokerRoom').value }}</div>
        <div *ngIf="isEditMode" class="box-edit-mode dropdown-box" (click)="openSelectionBrokerRoom($event)">
          <span class="typo-body-9 p-3-px">{{ getFormControl('brokerRoom').value }}</span>
          <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
        </div>
      </div>
      <!-- Mã nhân viên -->
      <div class="box-info">
        <div class="typo-body-9 gray-color-cls">
          {{ 'MES-482' | translate }}
        </div>
        <div *ngIf="!isEditMode" class="typo-body-8">{{ getFormControl('code').value }}</div>
        <div *ngIf="isEditMode" class="box-edit-mode">
          <input [formControl]="getFormControl('code')" class="input-style-common typo-body-9 input-cls-box" />
        </div>
      </div>
    </div>
  </div>
  <div class="box-container">
    <div class="bottom-box-cls">
      <!-- Chức vụ -->
      <div class="box-info">
        <div class="typo-body-9 gray-color-cls">
          {{ 'MES-483' | translate }}
        </div>
        <div *ngIf="!isEditMode" class="typo-body-8">{{ labelPostion | translate }}</div>
        <div *ngIf="isEditMode" class="box-edit-mode dropdown-box" (click)="openSelectionPosition($event)">
          <span class="typo-body-9 p-3-px">{{ labelPostion | translate }}</span>
          <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
        </div>
      </div>
      <!-- Văn phòng -->
      <div class="box-info">
        <div class="typo-body-9 gray-color-cls">{{ 'MES-484' | translate }}</div>
        <div *ngIf="!isEditMode" class="typo-body-8">{{ getFormControl('address').value }}</div>
        <div *ngIf="isEditMode" class="box-edit-mode">
          <textarea
            [formControl]="getFormControl('address')"
            class="input-style-common typo-body-9 input-cls-box text-area-cls"
          >
          </textarea>
        </div>
      </div>
    </div>
  </div>
</div>
