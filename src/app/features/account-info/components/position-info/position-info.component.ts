import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { fake_broker_room, LIST_OF_POSITION } from '../../constants/account-info';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import { take, takeUntil } from 'rxjs';
import { DestroyService } from 'src/app/core/services';
import { compareObject } from 'src/app/shared/utils/compareForm';
import { IInfoUserLogined } from 'src/app/shared/models/global';

/**
 * PositionInfoComponent
 */
@Component({
  selector: 'app-position-info',
  templateUrl: './position-info.component.html',
  styleUrl: './position-info.component.scss',
})
export class PositionInfoComponent implements OnInit, OnChanges {
  @Input() isEditMode = false;

  @Input() personalInfoData!: IInfoUserLogined | null;

  @Output() dataChangeEvent = new EventEmitter<boolean>();

  labelPostion = '';

  positionInfoForm: FormGroup;

  initialData: any;

  /**
   * Constructor
   * @param fb
   * @param popoverService
   * @param _destroy
   */
  constructor(private readonly fb: FormBuilder, private readonly popoverService: PopoverService, private readonly _destroy: DestroyService) {
    this.positionInfoForm = this.fb.group({
      brokerRoom: [null],
      position: [null],
      code: [null],
      address: [null],
    });
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.labelPostion = LIST_OF_POSITION.find((t) => t.value === this.getFormControl('position').value)?.name ?? '-';

    this.positionInfoForm.valueChanges.pipe(takeUntil(this._destroy)).subscribe((value) => {
      if (this.isEditMode) {
        this.dataChangeEvent.emit(!compareObject(this.initialData, value));
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    const { personalInfoData } = changes;

    if (personalInfoData) {
      this.positionInfoForm.patchValue({
        brokerRoom: this.personalInfoData?.saleGroupNo,
        // position: 'manager',
        code: this.personalInfoData?.brokerCode,
        address: '-',
      });
    }
  }

  /**
   * GetFormControl
   * @param field
   * @returns {any} new control
   */
  getFormControl(field: string) {
    return this.positionInfoForm.get(field) as FormControl;
  }

  /**
   * OpenSelectionBrokerRoom
   * @param event
   */
  openSelectionBrokerRoom(event: MouseEvent) {
    let origin = event.target as HTMLElement;
    if (origin.tagName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }
    const brokerRoom = this.getFormControl('brokerRoom')?.value;

    const value = fake_broker_room.filter((t) => t.name === brokerRoom) ?? [];

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      width: origin.offsetWidth,
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: fake_broker_room,
        value,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.getFormControl('brokerRoom')?.patchValue(item[0].name);
    });
  }

  /**
   * OpenSelectionPosition
   * @param event
   */
  openSelectionPosition(event: MouseEvent) {
    let origin = event.target as HTMLElement;
    if (origin.tagName !== 'DIV') {
      origin = origin.parentElement as HTMLElement;
    }
    const position = this.getFormControl('position')?.value;

    const value = LIST_OF_POSITION.filter((t) => t.value === position) ?? [];

    const ref = this.popoverService.open<any>({
      origin,
      content: SearchListComponent,
      position: 2,
      width: origin.offsetWidth,
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100', 'over-flow-auto'],
      componentConfig: {
        multiple: false,
        isSearch: false,
        isShowBtn: false,
        searchKey: 'name',
        options: LIST_OF_POSITION,
        value,
        displayOptionFn: (v: any) => v.name,
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe((v) => {
      const { data } = v;
      if (!data) return;
      const { item } = data;
      this.getFormControl('position')?.patchValue(item[0].value);
      this.labelPostion = LIST_OF_POSITION.find((t) => t.value === item[0].value)?.name ?? '';
    });
  }

  /**
   * ResetValueForm
   */
  resetValueForm() {
    this.positionInfoForm.patchValue({
      ...this.initialData,
    });
  }

  /**
   * UpdateValueForm
   */
  updateValueForm() {
    this.initialData = {
      ...this.positionInfoForm.value,
    };
  }
}
