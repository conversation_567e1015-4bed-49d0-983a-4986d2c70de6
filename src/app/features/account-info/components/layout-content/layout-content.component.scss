.layout-content-box {
  .header-cls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    margin-bottom: 4px;
    .header-text-cls {
      color: var(--color--text--subdued);
    }
    .btn-box {
      cursor: pointer;
      color: var(--color--brand--500);
      display: flex;
      align-items: center;
      gap: 4px;
    }
    .btn-box-edit {
      display: flex;
      align-items: center;
      gap: 8px;
      .btn-cls {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding: 4px 8px;
        border-radius: 8px;
        border: 1px solid var(--color--danger--600);
        color: var(--color--danger--600);
        cursor: pointer;
        &.primary {
          color: var(--color--brand--500);
          border-color: var(--color--brand--500);
          opacity: 0.5;
          &.isChange {
            opacity: 1;
          }
        }
      }
    }
  }
  .body-content {
    background-color: var(--color--background--1);
    padding: 16px 24px;
    border: 1px solid var(--color--other--divider);
    border-radius: 8px;
  }
}

:host {
  display: block;
}
