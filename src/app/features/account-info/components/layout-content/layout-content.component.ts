import { Component, EventEmitter, Input, Output } from '@angular/core';

/**
 * LayoutContentComponent
 */
@Component({
  selector: 'app-layout-content',
  templateUrl: './layout-content.component.html',
  styleUrl: './layout-content.component.scss',
})
export class LayoutContentComponent {
  @Input() title = '';

  @Input() isShowButtonEdit = true;

  @Input() isChange = false;

  @Input() isEditMode = false;

  @Output() transferEditModeEvent = new EventEmitter<boolean>();

  @Output() cancelEditModeEvent = new EventEmitter();

  @Output() saveDataEvent = new EventEmitter();

  /**
   * TransferEditMode
   */
  transferEditMode() {
    this.transferEditModeEvent.emit(true);
  }
}
