<div class="layout-content-box">
  <div class="header-cls">
    <div class="typo-body-6 header-text-cls">{{ title | translate }}</div>
    <!-- FIXME: chưa có API -->
    <!-- <div class="typo-button-7 btn-box" *ngIf="isShowButtonEdit && !isEditMode" (click)="transferEditMode()">
      <img src="./assets/icons/edit-orange.svg" alt="edit-orange" />
      {{ 'MES-340' | translate }}
    </div> -->
    <div class="btn-box-edit" *ngIf="isShowButtonEdit && isEditMode">
      <div class="btn-cls primary typo-button-7" [class.isChange]="isChange" (click)="saveDataEvent.emit()">
        {{ 'MES-40' | translate }}
        <img src="./assets/icons/tick-circle.svg" alt="tick-circle" />
      </div>
      <div class="btn-cls typo-button-7" (click)="cancelEditModeEvent.emit()">
        {{ 'MES-41' | translate }}
        <img src="./assets/icons/x-cross-red.svg" alt="x-cross-red" />
      </div>
    </div>
    <!-- FIXME: chưa có API -->
    <!-- <div class="typo-button-7 btn-box" *ngIf="!isShowButtonEdit">
      <img src="./assets/icons/export-orange.svg" alt="export-orange" />
      {{ 'MES-480' | translate }}
    </div> -->
  </div>
  <div class="body-content">
    <ng-content></ng-content>
  </div>
</div>
