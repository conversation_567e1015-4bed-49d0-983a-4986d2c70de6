import { Component, OnInit } from '@angular/core';
import { DestroyService, DialogService, LoadingService, MessageService } from 'src/app/core/services';
import { ChangePasswordComponent } from 'src/app/shared/components/change-password/change-password.component';
import { PersonalInfoComponent } from '../../components/personal-info/personal-info.component';
import { AddressInfoComponent } from '../../components/address-info/address-info.component';
import { PositionInfoComponent } from '../../components/position-info/position-info.component';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { take, takeUntil } from 'rxjs';
import { Store } from '@ngrx/store';
import { IInfoUserLogined } from 'src/app/shared/models/global';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';

/**
 * AccountInfoDetailContainer
 * Thông tin chi tiết tài khoản
 */
@Component({
  selector: 'app-account-info-detail',
  templateUrl: './account-info-detail.container.html',
  styleUrl: './account-info-detail.container.scss',
})
export class AccountInfoDetailContainer implements OnInit {
  isPersonalInfoEdit = false;
  isPersonalInfoChange = false;

  isAddressInfoEdit = false;
  isAddressInfoChange = false;

  isPossitionInfoEdit = false;
  isPossitionInfoChange = false;

  userInfo!: IInfoUserLogined | null;

  /**
   * Constructor
   * @param dialogService
   * @param _destroy
   * @param messageService
   * @param store
   * @param loadingService
   */
  constructor(
    private readonly dialogService: DialogService,
    private readonly _destroy: DestroyService,
    private readonly messageService: MessageService,
    private readonly store: Store,
    private readonly loadingService: LoadingService
  ) {}

  ngOnInit(): void {
    this.store
      .select(selectCurrentBrokerView$)
      .pipe(takeUntil(this._destroy))
      .subscribe((user) => {
        if (!user) return;
        this.userInfo = { ...user };
      });
  }

  /**
   * ChangePassword
   */
  changePassword() {
    this.dialogService.openPopUp(ChangePasswordComponent, {
      width: '600px',
      height: '540px',
      panelClass: ['popup-confirm', 'not-padding-popup', 'popup-visible-cls'],
    });
  }

  /**
   * CancelEditMode
   * @param ref
   * @param type
   * @returns {void} prevent event
   */
  cancelEditMode(
    ref: PersonalInfoComponent | AddressInfoComponent | PositionInfoComponent,
    type: 'personal' | 'address' | 'position'
  ) {
    switch (type) {
      case 'personal':
        if (this.isPersonalInfoChange) {
          const dialog = this.openConfirmCancel();
          dialog
            .afterClosed()
            .pipe(take(1))
            .subscribe((v) => {
              if (v === 'save') {
                ref.resetValueForm();
                this.isPersonalInfoEdit = false;
                this.isPersonalInfoChange = false;
              }
            });
          return;
        }

        ref.resetValueForm();
        this.isPersonalInfoEdit = false;
        break;

      case 'address':
        if (this.isAddressInfoChange) {
          const dialog = this.openConfirmCancel();
          dialog
            .afterClosed()
            .pipe(take(1))
            .subscribe((v) => {
              if (v === 'save') {
                ref.resetValueForm();
                this.isAddressInfoEdit = false;
                this.isAddressInfoChange = false;
              }
            });
          return;
        }
        ref.resetValueForm();
        this.isAddressInfoEdit = false;

        break;

      case 'position':
        if (this.isPersonalInfoChange) {
          const dialog = this.openConfirmCancel();
          dialog
            .afterClosed()
            .pipe(take(1))
            .subscribe((v) => {
              if (v === 'save') {
                ref.resetValueForm();
                this.isPossitionInfoEdit = false;
                this.isPersonalInfoChange = false;
              }
            });
          return;
        }
        ref.resetValueForm();
        this.isPossitionInfoEdit = false;

        break;

      default:
        break;
    }
  }

  /**
   * OpenConfirmCancel
   * @returns {any} ref dialog service
   */
  openConfirmCancel() {
    return this.dialogService.openPopUp(DeleteNotificationComponent, {
      data: {
        isActive: true,
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-97',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
      height: '210px',
      width: '340px',
      panelClass: ['popup-confirm', 'not-padding-popup'],
    });
  }

  /**
   * SaveData
   * @param ref
   * @param type
   * @returns {void} prevent event
   */
  saveData(
    ref: PersonalInfoComponent | AddressInfoComponent | PositionInfoComponent,
    type: 'personal' | 'address' | 'position'
  ) {
    switch (type) {
      case 'personal':
        if (!this.isPersonalInfoChange) return;
        ref.updateValueForm();
        this.isPersonalInfoEdit = false;
        this.isPersonalInfoChange = false;
        this.messageService.success('Cập nhật thành công');

        break;

      case 'address':
        if (!this.isAddressInfoChange) return;
        ref.updateValueForm();
        this.isAddressInfoEdit = false;
        this.isAddressInfoChange = false;
        this.messageService.success('Cập nhật thành công');

        break;

      case 'position':
        if (!this.isPossitionInfoChange) return;
        ref.updateValueForm();
        this.isPossitionInfoEdit = false;
        this.isPossitionInfoChange = false;
        this.messageService.success('Cập nhật thành công');

        break;

      default:
        break;
    }
  }
}
