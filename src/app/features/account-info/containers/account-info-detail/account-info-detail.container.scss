.account-info-detail-box {
  height: 100%;
  width: 100%;
  border: 1px solid var(--color--other--divider);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .account-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    padding: 16px 24px;
    border-bottom: 1px solid var(--color--other--divider);
    .left-box {
      display: flex;
      flex-direction: column;
      gap: 8px;
      .top-box {
        display: flex;
        align-items: center;
        gap: 20px;
        .box-color-cls {
          padding: 2px 13px;
          border-radius: 16px;
          background-color: var(--color--accents--yellow);
        }
      }
      .bottom-box {
        color: var(--color--text--subdued);
      }
    }
    .right-box {
      .change-password-box {
        padding: 8px 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        border: 1px solid var(--color--other--divider);
        border-radius: 8px;
        cursor: pointer;
      }
    }
  }
  .account-detail-body {
    padding: 24px;
    display: flex;
    gap: 24px;
    overflow: hidden;
    .box-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
      &::-webkit-scrollbar {
        // display: none;
      }
    }
  }

  .mt-24-px {
    margin-top: 24px;
  }
}

:host {
  height: 100%;
  width: 100%;
  display: block;
}
