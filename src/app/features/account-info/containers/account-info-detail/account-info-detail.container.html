<div class="account-info-detail-box">
  <div class="account-detail-header">
    <div class="left-box">
      <div class="top-box">
        <div class="name-txt-cls typo-body-3">{{userInfo?.brokerName}}</div>
        <div class="box-color-cls typo-body-9">Admin</div>
      </div>
      <div class="bottom-box typo-body-9">{{userInfo?.brokerCode}}</div>
    </div>
    <!-- FIXME: chưa có API-->
    <!-- <div class="right-box">
      <div class="change-password-box typo-body-9" (click)="changePassword()">
        <img src="./assets/icons/lock.svg" alt="lock" />
        {{'MES-485' | translate}}
      </div>
    </div> -->
  </div>
  <div class="account-detail-body">
    <div class="box-content">
      <app-layout-content
        [title]="'Thông Tin Cá Nhân'"
        [isEditMode]="isPersonalInfoEdit"
        [isChange]="isPersonalInfoChange"
        (transferEditModeEvent)="isPersonalInfoEdit = $event"
        (cancelEditModeEvent)="cancelEditMode(personalInfoRef, 'personal')"
        (saveDataEvent)="saveData(personalInfoRef, 'personal')"
      >
        <app-personal-info
          #personalInfoRef
          [isEditMode]="isPersonalInfoEdit"
          [personalInfoData]="userInfo"
          (dataChangeEvent)="isPersonalInfoChange = $event"
        ></app-personal-info>
      </app-layout-content>
      <app-layout-content
        class="mt-24-px"
        [title]="'Địa Chỉ'"
        [isEditMode]="isAddressInfoEdit"
        [isChange]="isAddressInfoChange"
        (transferEditModeEvent)="isAddressInfoEdit = $event"
        (saveDataEvent)="saveData(addressRef, 'address')"
        (cancelEditModeEvent)="cancelEditMode(addressRef, 'address')"
      >
        <app-address-info
          #addressRef
          [isEditMode]="isAddressInfoEdit"
          (dataChangeEvent)="isAddressInfoChange = $event"
        ></app-address-info>
      </app-layout-content>
    </div>
    <div class="box-content">
      <app-layout-content
        [title]="'Chức vụ'"
        [isEditMode]="isPossitionInfoEdit"
        [isChange]="isPossitionInfoChange"
        (transferEditModeEvent)="isPossitionInfoEdit = $event"
        (saveDataEvent)="saveData(positionRef, 'position')"
        (cancelEditModeEvent)="cancelEditMode(positionRef, 'position')"
      >
        <app-position-info
          #positionRef
          [isEditMode]="isPossitionInfoEdit"
          [personalInfoData]="userInfo"
          (dataChangeEvent)="isPossitionInfoChange = $event"
        ></app-position-info>
      </app-layout-content>

      <app-layout-content class="mt-24-px" [title]="'Khách Hàng Đang Quản Lý'" [isShowButtonEdit]="false">
        <app-manager-customer></app-manager-customer>
      </app-layout-content>
    </div>
  </div>
</div>
