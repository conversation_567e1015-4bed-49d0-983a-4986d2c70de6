import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltip } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { ActionBtnComponent } from 'src/app/shared/components/action-btn/action-btn.component';
import { FilterComponent } from 'src/app/shared/components/filter/filter.component';
import { InputNumberCustomComponent } from 'src/app/shared/components/input-custom-for-table/input-number-custom/input-number-custom.component';
import { InputProportionDropdownComponent } from 'src/app/shared/components/input-custom-for-table/input-proportion-dropdown/input-proportion-dropdown.component';
import { InputProportionComponent } from 'src/app/shared/components/input-custom-for-table/input-proportion/input-proportion.component';
import { SearchDropdownCustomComponent } from 'src/app/shared/components/search-dropdown-custom/search-dropdown-custom.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { NgxMaskDirective } from 'src/app/shared/directives/mask/ngx-mask.directive';
import { NumberOnlyDirective } from 'src/app/shared/directives/number-only/number-only.directive';
import { NumberFormatPipe } from 'src/app/shared/pipes/format-number/format-number.pipe';
import { AccountInfoRoutingModule } from './account-info-routing.module';
import { AccountInfoView } from './views/account-info.view';
import { AccountInfoDetailContainer } from './containers/account-info-detail/account-info-detail.container';
import { LayoutContentComponent } from './components/layout-content/layout-content.component';
import { PersonalInfoComponent } from './components/personal-info/personal-info.component';
import { AddressInfoComponent } from './components/address-info/address-info.component';
import { PositionInfoComponent } from './components/position-info/position-info.component';
import { ManagerCustomerComponent } from './components/manager-customer/manager-customer.component';
import { ChangePasswordComponent } from 'src/app/shared/components/change-password/change-password.component';
import { PhoneNumberTableComponent } from 'src/app/shared/components/phone-number-table/phone-number-table.component';
import { FormControlComponent } from '../../shared/components/form-control/form-control.component';
import { StoreModule } from '@ngrx/store';
import { ACCOUNT_INFO_STATE_NAME } from './stores/account-info.selectors';
import { accountInfoReducers } from './stores/account-info.reducers';
import { EffectsModule } from '@ngrx/effects';
import { AccountInfoEffects } from './stores/account-info.effects';

const VIEWS = [AccountInfoView];

const CONTAINERS = [AccountInfoDetailContainer];

const COMPONENTS = [
  LayoutContentComponent,
  PersonalInfoComponent,
  AddressInfoComponent,
  PositionInfoComponent,
  ManagerCustomerComponent,
];

const SHARED = [
  GridComponent,
  ActionBtnComponent,
  SearchDropdownCustomComponent,
  FilterComponent,
  NumberOnlyDirective,
  InputProportionComponent,
  InputProportionDropdownComponent,
  InputNumberCustomComponent,
  ChangePasswordComponent,
  PhoneNumberTableComponent,
  FormControlComponent,
];

/**
 * AccountInfo Module
 */
@NgModule({
  declarations: [...VIEWS, ...CONTAINERS, ...COMPONENTS],
  imports: [
    ...SHARED,
    NgxMaskDirective,
    CommonModule,
    TranslateModule,
    AccountInfoRoutingModule,
    // Material Module
    MatTableModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    FormsModule,
    MatSelectModule,
    MatDividerModule,
    MatDatepickerModule,
    MatMenuModule,
    MatDialogModule,
    OverlayModule,
    MatIconModule,
    MatCheckboxModule,
    MatTabsModule,
    MatTooltip,
    NumberFormatPipe,
    FormControlComponent,

    StoreModule.forFeature(ACCOUNT_INFO_STATE_NAME, accountInfoReducers),
    EffectsModule.forFeature([AccountInfoEffects]),
  ],
})
export class AccountInfoModule {}
