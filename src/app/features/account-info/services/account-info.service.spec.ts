import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { AccountInfoService } from './account-info.service';
import { ApiService } from 'src/app/core/services';

describe('AccountInfoService', () => {
  let service: AccountInfoService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['get']);

    TestBed.configureTestingModule({
      providers: [
        AccountInfoService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(AccountInfoService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  it('No.1: should properly initialize with all required dependencies', () => {
    expect(service).toBeTruthy();
    expect(apiService).toBeTruthy();
  });

  it('No.2: should get customer account info with valid broker code', () => {
    // Arrange
    const brokerCode = 'TEST123';
    const mockResponse = { data: { accountInfo: 'test data' } };
    apiService.get.and.returnValue(of(mockResponse));

    // Act
    service.getCustomerAccountInfo(brokerCode).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(apiService.get).toHaveBeenCalledWith(`customer/account/broker/${brokerCode}`);
    });
  });

  it('No.3: should handle API call with empty broker code', () => {
    // Arrange
    const brokerCode = '';
    const mockResponse = { data: { accountInfo: 'test data' } };
    apiService.get.and.returnValue(of(mockResponse));

    // Act
    service.getCustomerAccountInfo(brokerCode).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(apiService.get).toHaveBeenCalledWith('customer/account/broker/');
    });
  });

  it('No.4: should handle API call with null broker code', () => {
    // Arrange
    const brokerCode = null as any;
    const mockResponse = { data: { accountInfo: 'test data' } };
    apiService.get.and.returnValue(of(mockResponse));

    // Act
    service.getCustomerAccountInfo(brokerCode).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(apiService.get).toHaveBeenCalledWith('customer/account/broker/null');
    });
  });

  it('No.5: should handle API call with special characters in broker code', () => {
    // Arrange
    const brokerCode = 'TEST@123#$%';
    const mockResponse = { data: { accountInfo: 'test data' } };
    apiService.get.and.returnValue(of(mockResponse));

    // Act
    service.getCustomerAccountInfo(brokerCode).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(apiService.get).toHaveBeenCalledWith(`customer/account/broker/${brokerCode}`);
    });
  });

  it('No.6: should return observable from API service', () => {
    // Arrange
    const brokerCode = 'TEST123';
    const mockResponse = { data: { accountInfo: 'test data' } };
    const mockObservable = of(mockResponse);
    apiService.get.and.returnValue(mockObservable);

    // Act
    const result = service.getCustomerAccountInfo(brokerCode);

    // Assert
    expect(result).toBe(mockObservable);
    expect(apiService.get).toHaveBeenCalledWith(`customer/account/broker/${brokerCode}`);
  });

  it('No.7: should handle API service errors', () => {
    // Arrange
    const brokerCode = 'TEST123';
    const mockError = new Error('API Error');
    apiService.get.and.returnValue(throwError(mockError));

    // Act & Assert
    service.getCustomerAccountInfo(brokerCode).subscribe({
      next: () => fail('Should have failed'),
      error: (error) => {
        expect(error).toBe(mockError);
        expect(apiService.get).toHaveBeenCalledWith(`customer/account/broker/${brokerCode}`);
      }
    });
  });

  it('No.8: should use correct HTTP method for API call', () => {
    // Arrange
    const brokerCode = 'TEST123';
    const mockResponse = { data: { accountInfo: 'test data' } };
    apiService.get.and.returnValue(of(mockResponse));

    // Act
    service.getCustomerAccountInfo(brokerCode);

    // Assert
    expect(apiService.get).toHaveBeenCalled();
    expect(apiService.get).toHaveBeenCalledWith(`customer/account/broker/${brokerCode}`);
  });

  it('No.9: should construct correct API endpoint URL', () => {
    // Arrange
    const brokerCode = 'TEST123';
    const expectedUrl = 'customer/account/broker/TEST123';
    const mockResponse = { data: { accountInfo: 'test data' } };
    apiService.get.and.returnValue(of(mockResponse));

    // Act
    service.getCustomerAccountInfo(brokerCode);

    // Assert
    expect(apiService.get).toHaveBeenCalledWith(expectedUrl);
  });

  it('No.10: should handle multiple consecutive calls', () => {
    // Arrange
    const brokerCode1 = 'BROKER1';
    const brokerCode2 = 'BROKER2';
    const brokerCode3 = 'BROKER3';
    const mockResponse = { data: { accountInfo: 'test data' } };
    apiService.get.and.returnValue(of(mockResponse));

    // Act
    service.getCustomerAccountInfo(brokerCode1);
    service.getCustomerAccountInfo(brokerCode2);
    service.getCustomerAccountInfo(brokerCode3);

    // Assert
    expect(apiService.get).toHaveBeenCalledTimes(3);
    expect(apiService.get).toHaveBeenCalledWith(`customer/account/broker/${brokerCode1}`);
    expect(apiService.get).toHaveBeenCalledWith(`customer/account/broker/${brokerCode2}`);
    expect(apiService.get).toHaveBeenCalledWith(`customer/account/broker/${brokerCode3}`);
  });
});
