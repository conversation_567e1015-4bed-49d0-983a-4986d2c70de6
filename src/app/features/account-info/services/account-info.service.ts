import { Injectable } from '@angular/core';
import { ApiService } from 'src/app/core/services';

/**
 * AccountInfoService
 */
@Injectable({
  providedIn: 'root',
})
export class AccountInfoService {
  /**
   *constructor
   * @param apiService apiService
   */
  constructor(private readonly apiService: ApiService) {}

  // account info
  getCustomerAccountInfo(brokerCode: string) {
    return this.apiService.get<any>(`customer/account/broker/${brokerCode}`);
  }
}
