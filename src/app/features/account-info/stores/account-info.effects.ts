import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { LoadingService, MessageService } from 'src/app/core/services';
import { getCustomerAccountInfo, getCustomerAccountInfoSuccess } from './account-info.actions';
import { finalize, map, switchMap, tap } from 'rxjs';
import { concatLatestFrom } from '@ngrx/operators';
import { AccountInfoService } from '../services/account-info.service';
import { selectCurrentBrokerView$ } from 'src/app/stores/shared/shared.selectors';

@Injectable()
export class AccountInfoEffects {
  constructor(
    private readonly actions$: Actions,
    private readonly loadingService: LoadingService,
    private readonly store: Store,
    private readonly messageService: MessageService,
    private readonly accountInfoService: AccountInfoService
  ) {}

  getCustomerAccountInfo$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getCustomerAccountInfo),
      tap(() => this.loadingService.show()),
      concatLatestFrom(() => [this.store.select(selectCurrentBrokerView$)]),
      switchMap(([_, user]) => {
        return this.accountInfoService
          .getCustomerAccountInfo(user?.brokerCode ?? '')
          .pipe(finalize(() => this.loadingService.hide()));
      }),
      map((res) => {
        return getCustomerAccountInfoSuccess({ data: res });
      })
    );
  });
}
