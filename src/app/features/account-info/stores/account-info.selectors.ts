import { createFeatureSelector, createSelector } from '@ngrx/store';
import { IAccountInfoState } from '../models/account-info.model';

export const ACCOUNT_INFO_STATE_NAME = 'ACCOUNT_INFO';

export const selectCustomerState = createFeatureSelector<IAccountInfoState>(ACCOUNT_INFO_STATE_NAME);

// Thông tin khách hàng quản lý

export const selectCustomerAccountInfo$ = createSelector(selectCustomerState, (state) => state?.customerAccountInfo);
