import { createReducer, on } from '@ngrx/store';
import { IAccountInfoState } from '../models/account-info.model';
import * as AccountInfoAction from './account-info.actions';

export const initialAccountInfoState: IAccountInfoState = {
  customerAccountInfo: [],
};

export const accountInfoReducers = createReducer<IAccountInfoState>(
  initialAccountInfoState,

  on(
    AccountInfoAction.getCustomerAccountInfoSuccess,
    (state, action): IAccountInfoState => ({
      ...state,
      customerAccountInfo: action.data,
    })
  )
);
