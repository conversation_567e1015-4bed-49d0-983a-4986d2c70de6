import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { DocumentView } from "./views/document.view";
import { InstructionManualContainer } from "./containers/instruction-manual.container";
import { UnsaveChangeGuard } from "src/app/core/guards/unsaved-changes.guard";

const routes: Routes = [
    {
        path: '',
        component: DocumentView,
        children: [
            {
                path: '',
                redirectTo: 'instruction-manual',
                pathMatch: 'full'
            },
            {
                path: 'instruction-manual',
                component: InstructionManualContainer,
                canDeactivate: [UnsaveChangeGuard]
            }
        ]
    }
]

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})

export class DocumentRoutingModule { }