import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { DestroyService, DialogService } from 'src/app/core/services';

@Component({
  selector: 'app-document',
  templateUrl: './document.view.html',
  styleUrl: './document.view.scss',
})
export class DocumentView {
  menuDocuments = [
    {
      router: 'instruction-manual',
      name: 'MES-616',
      nameIcon: 'icon:instruction-manual',
    },
  ];

  activeTab = 0;

  searchControl = new FormControl();

  /**
   * Constructor
   * @param _destroy
   * @param router
   * @param dialogService
   * @param store
   */
  constructor(
    private readonly _destroy: DestroyService,
    private readonly router: Router,
    private readonly dialogService: DialogService,
    private readonly store: Store
  ) {
    this.updateActiveTab(router.url);
  }

  /**
   * Update active tab index based on current URL
   * @param url - Current URL
   */
  updateActiveTab(url: string) {
    this.activeTab = this.menuDocuments.findIndex((item) => url.includes(item.router));
  }
}
