import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { DestroyService } from 'src/app/core/services';

@Component({
  selector: 'app-add-instruction-manual',
  templateUrl: './add-instruction-manual.component.html',
  styleUrl: './add-instruction-manual.component.scss',
  providers: [DestroyService],
})
export class AddInstructionManualComponent {
  addInstructionManual: FormGroup;
  selectedFile: File | null = null;
  fileError: string | null = null;

  constructor(
    private readonly destroyService: DestroyService,
    private readonly dialogRef: MatDialogRef<AddInstructionManualComponent>,
    private readonly store: Store,
    private readonly fb: FormBuilder
  ) {
    this.addInstructionManual = this.fb.group({
      fileName: ['', Validators.required],
    });
  }

  /**
   * Handle selected file
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      const file = input.files[0];

      if (file.size > 100 * 1024 * 1024) {
        this.fileError = 'MES-633';
        this.selectedFile = null;
      } else {
        this.fileError = null;
        this.selectedFile = file;
      }
    }
  }

  /**
   * confirm and send data
   */
  confirm(): void {
    if (this.addInstructionManual.invalid || !this.selectedFile) {
      this.fileError = this.selectedFile ? null : 'MES-634';
      return;
    }

    const newInstruction = {
      nameInstructionManual: this.addInstructionManual.get('fileName')?.value,
      nameFile: this.selectedFile.name,
    };

    this.dialogRef.close(newInstruction);
  }

  /**
   * Remove file selected
   */
  removeFile(): void {
    this.selectedFile = null;
    this.fileError = null;
  }

  /**
   * Close up popup
   */
  closePopUp(): void {
    this.dialogRef.close();
  }
}
