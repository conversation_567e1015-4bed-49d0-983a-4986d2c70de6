<div class="pop-up-container">
  <div class="header-pop-up">
    <div class="title-cls typo-body-2">{{ 'MES-617' | translate }}</div>
    <img mat-dialog-close class="close-btn" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>
  <form [formGroup]="addInstructionManual" class="instruction-manual-content">
    <div class="file-name-wrap">
      <div class="title typo-body-5">{{ 'MES-618' | translate }} <span [style.color]="'red'">*</span></div>
      <div class="file-name">
        <input
          type="text"
          class="input-cls-custom input-style-common typo-field-1"
          [placeholder]="'MES-618' | translate"
          formControlName="fileName"
        />
      </div>
    </div>
    <div class="title typo-body-5">{{ 'MES-619' | translate }} <span [style.color]="'red'">*</span></div>
    <div class="file-attach-wrap">
      <div class="file-attach-container">
        <div *ngIf="selectedFile" class="file-preview">
          <div class="icon-file">
            <img class="icon-file-img" src="./assets/icons/Icon.svg" [alt]="'icon-file-img'" />
          </div>
          <div class="selected-file-name-wrap">
            <div class="selected-file-name">
              <div class="title-file typo-body-7">
                {{ selectedFile.name }}
              </div>
              <button class="button-remove-file" (click)="removeFile()"

              >
                <img class="close-btn" src="./assets/icons/trailing-icon.svg" [alt]="'close-btn'" />
              </button>
            </div>
            <div class="file-memory typo-body-22">{{ (selectedFile.size / 1024 / 1024).toFixed(1) }} MB</div>
          </div>
        </div>
        <button class="file-attach" (click)="fileInput.click()"

        >
          <img src="./assets/icons/Upload.svg" class="icon-upload-file" [alt]="'icon-upload-file'" />
          <div class="title-file-attach">
            <div class="description-title typo-body-6">{{ 'MES-23' | translate }}</div>
            <div class="description typo-body-6">{{ 'MES-24' | translate }}</div>
          </div>
          <input #fileInput type="file" hidden (change)="onFileSelected($event)" />
          <mat-error *ngIf="fileError" class="typo-body-7">{{ fileError }}</mat-error>
        </button>
      </div>
      <div class="hint typo-body-9">{{ 'MES-635' | translate }}</div>
    </div>
  </form>
  <div class="footer-pop-up">
    <button [disabled]="addInstructionManual.invalid" (click)="confirm()"  class="btn confirm typo-button-3">
      {{ 'MES-89' | translate }}
    </button>
    <button mat-dialog-close (click)="closePopUp()"  class="btn close typo-button-3">{{ 'MES-74' | translate }}</button>
  </div>
</div>
