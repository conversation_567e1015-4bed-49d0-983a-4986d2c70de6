:host {
  display: block;
  height: 100%;
  width: 100%;
}

.pop-up-container {
  width: 100%;
  height: 100%;
  position: relative;
  min-height: 100%;
}

.header-pop-up {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--color--other--divider);

  .close-btn {
    cursor: pointer;
  }
}

.title {
  padding-bottom: 4px;
}
.instruction-manual-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 940px;
  padding: 24px;
  gap: 10px;

  .file-name-wrap {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .input-cls-custom {
      padding: 8px 16px;
      border: 1px solid var(--color--neutral--100);
      border-radius: 8px;
      width: 402px;
      height: 48px;
    }
  }

  .file-attach-wrap {
    width: 100%;
    height: 96px;
    display: flex;
    flex-direction: column;
    .file-attach-container {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .file-preview {
      display: flex;
      width: 100%;
      gap: 8px;
      border: 1px solid var(--color--cyan--600);
      padding: 6px 8px 6px 4px;
      border-radius: 6px;
      .icon-file-img {
        width: 32px;
        height: 32px;
      }
      .selected-file-name-wrap {
        width: 100%;
        overflow: hidden;
        .selected-file-name {
          display: flex;
          gap: 8px;
          justify-content: space-between;
          overflow: hidden;
          .title-file {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .button-remove-file {
            cursor: pointer;
          }
        }
        .file-memory {
          color: var(--color--text--subdued);
        }
      }
    }
    .file-attach {
      cursor: pointer;
      border: 1px dashed var(--color--blue--300);
      border-radius: 6px;
      padding: 18px 8px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 80px;
      .title-file-attach {
        display: flex;
        .description-title {
          color: var(--color--text--subdued);
          padding-right: 7px;
        }

        .description {
          color: var(--color--cyan--600);
        }
      }
    }
    .hint {
      color: var(--color--text--disabled);
    }
  }
}

.footer-pop-up {
  display: flex;
  position: absolute;
  bottom: 0;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--color--other--divider);
  padding: 16px 24px;

  .btn {
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid var(--color--other--divider);
  }

  .confirm {
    background-color: var(--color--brand--500);
    color: var(--color--neutral--white);
  }

  .close {
    background-color: var(--color--neutral--white);
    color: var(--color--text-default);
  }
}
