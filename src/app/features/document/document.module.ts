import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule } from '@angular/material/dialog';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { UnsaveChangeGuard } from 'src/app/core/guards/unsaved-changes.guard';
import { DocumentView } from './views/document.view';
import { InstructionManualContainer } from './containers/instruction-manual.container';
import { DocumentRoutingModule } from './document.routing.module';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { ActionBtnComponent } from 'src/app/shared/components/action-btn/action-btn.component';
import { DraggableListComponent } from 'src/app/shared/components/dragable-list/draggable-list.component';
import { FormControlComponent } from 'src/app/shared/components/form-control/form-control.component';
import { CheckBoxForTableComponent } from 'src/app/shared/components/check-box-for-table/check-box-for-table.component';
import { AddInstructionManualComponent } from './components/add-instruction-manual/add-instruction-manual.component';
import { InputComponent } from 'src/app/shared/components/input/input.component';

const SHARED = [
  GridComponent,
  ActionBtnComponent,
  DraggableListComponent,
  FormControlComponent,
  CheckBoxForTableComponent,
  CheckBoxForTableComponent,
  InputComponent
]

const VIEWS = [DocumentView];

const CONTAINERS = [InstructionManualContainer]

const COMPONENTS = [AddInstructionManualComponent]

@NgModule({
  declarations: [
    ...VIEWS,
    ...CONTAINERS,
    ...COMPONENTS
  ],
  imports: [
    ...SHARED,
    CommonModule,
    TranslateModule,
    DocumentRoutingModule,

    // Material Module
    MatTableModule,
    MatTabsModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    FormsModule,
    MatDividerModule,
    MatDatepickerModule,
    MatMenuModule,
    MatDialogModule,
    OverlayModule,
    MatIconModule,
    MatCheckboxModule,
    MatTooltipModule
  ],
  providers: [UnsaveChangeGuard]
})
export class DocumentModule { }
