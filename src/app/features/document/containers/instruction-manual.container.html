<div class="instruction-manual-container">
  <div class="header-instruction-manual">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-616' | translate}}</div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
            'not-accept-save-cls' : checkEnableSaveBtn(),
            'filter-mode-cls': isFilter
          }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event);"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
    >
    </sha-grid>
  </div>
</div>

<ng-template #iconTemplate let-tag="tag">
  <ng-container *ngIf="tag === 'eye'">
    <div class="icon-container">
      <img class="image-icon eye-icon" src="./assets/icons/eye-blue.svg" [alt]="'eye-icon'" />
    </div>
  </ng-container>
  <ng-container *ngIf="tag === 'delete'">
    <div class="icon-container">
      <img class="image-icon delete-icon" src="./assets/icons/delete-icon-border-red.svg" [alt]="'delete-icon'" />
    </div>
  </ng-container>
</ng-template>
