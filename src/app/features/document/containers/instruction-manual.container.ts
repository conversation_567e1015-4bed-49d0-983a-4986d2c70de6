import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { DestroyService, LoadingService, MessageService } from 'src/app/core/services';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { Observable, take } from 'rxjs';
import { deepClone } from 'src/app/shared/utils/utils';
import { AddInstructionManualComponent } from '../components/add-instruction-manual/add-instruction-manual.component';

@Component({
  selector: 'app-instruction-manual',
  templateUrl: './instruction-manual.container.html',
  styleUrl: './instruction-manual.container.scss',
})
export class InstructionManualContainer extends BaseTableComponent<any> implements OnInit, ComponentCanDeactivate {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('iconTemplate', { static: true }) iconTemplate!: TemplateRef<any>;

  fakeData: any[] = [
    {
      nameInstructionManual: 'Hướng dẫn sử dụng phase 1 (golive 31/03/2025)',
      nameFile: 'HDSD_Phase_1.pdf',
    },
  ];

  /**
   * Constructor
   * @param store Store
   * @param _destroy DestroyService
   * @param popperService PopoverService
   * @param loadingService LoadingService
   */
  constructor(
    private readonly store: Store,
    private readonly _destroy: DestroyService,
    private readonly popoverService: PopoverService,
    private readonly loadingService: LoadingService,
    private readonly messageService: MessageService
  ) {
    super();
    this.toggleButtonByTags([ActionButton.add, ActionButton.export]);
  }

  ngOnInit(): void {
    this.data = deepClone(this.fakeData);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        name: 'MES-636',
        minWidth: 500,
        width: 832,
        tag: 'nameInstructionManual',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'MES-637',
        minWidth: 500,
        width: 705,
        tag: 'nameFile',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'MES-638',
        minWidth: 30,
        width: 110,
        tag: 'eye',
        isDisplay: true,
        resizable: true,
        align: 'center',
        cellTemplate: this.iconTemplate,
        isCompare: true,
      },
      {
        name: 'MES-69',
        minWidth: 30,
        width: 110,
        tag: 'delete',
        isDisplay: true,
        align: 'center',
        resizable: true,
        cellTemplate: this.iconTemplate,
        isCompare: true,
      },
    ];
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'add':
        {
          const ref = this.dialogService.openRightDialog(AddInstructionManualComponent, {
            width: '450px',
          });
          ref
            .afterClosed()
            .pipe(take(1))
            .subscribe({
              next: (v) => {
                if (!v) return;

                this.data = [...this.data, v];
                this.initialData = deepClone(this.data);
              },
            });
        }
        break;

      case 'export':
        break;

      default:
        break;
    }
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([
      ActionButton.edit,
      ActionButton.save,
      ActionButton.add,
      ActionButton.cancel,
      ActionButton.export,
    ]);
  }
}
