import { ETypeCustomerClass, ICustomerClass } from '../../models/admin';

export const fakeData: ICustomerClass[] = [
  {
    name: 'ĐIỀU KIỆN',
    normal: {
      //FIXME: update later, giá trị sẽ là null và tỉnh tổng phần % theo từng children
      value: 1,
      type: ETypeCustomerClass.TOTAL,
    },
    gold: {
      //FIXME: update later, giá trị sẽ là null và tỉnh tổng phần % theo từng children
      value: 1,
      type: ETypeCustomerClass.TOTAL,
    },
    diamond: {
      //FIXME: update later, giá trị sẽ là null và tỉnh tổng phần % theo từng children
      value: 1,
      type: ETypeCustomerClass.TOTAL,
    },
    platinum: {
      //FIXME: update later, giá trị sẽ là null và tỉnh tổng phần % theo từng children
      value: 1,
      type: ETypeCustomerClass.TOTAL,
    },
    children: [
      {
        name: 'Tiền mặt bình quân',
        type: 'percent',
        normal: {
          value: 0.07,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        diamond: {
          value: 0.07,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        gold: {
          value: 0.07,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        platinum: {
          value: 0.07,
          type: ETypeCustomerClass.PERCENTSUM,
        },
      },
      {
        name: 'Tiền mặt cuối kỳ',
        type: 'percent',
        normal: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        diamond: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        gold: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        platinum: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
      },
      {
        name: 'Tài sản đảm bảo (NAV) bình quân',
        type: 'percent',
        normal: {
          value: 0.1,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        diamond: {
          value: 0.1,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        gold: {
          value: 0.1,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        platinum: {
          value: 0.1,
          type: ETypeCustomerClass.PERCENTSUM,
        },
      },
      {
        name: 'Tài sản đảm bảo (NAV) cuối kỳ',
        type: 'percent',
        normal: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        diamond: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        gold: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        platinum: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
      },
      {
        name: 'Giá trị giao dịch bình quân tháng',
        type: 'percent',
        normal: {
          value: 0.25,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        diamond: {
          value: 0.25,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        gold: {
          value: 0.25,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        platinum: {
          value: 0.25,
          type: ETypeCustomerClass.PERCENTSUM,
        },
      },
      {
        name: 'Vòng quay bình quân',
        type: 'percent',
        normal: {
          value: 0.05,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        diamond: {
          value: 0.05,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        gold: {
          value: 0.05,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        platinum: {
          value: 0.05,
          type: ETypeCustomerClass.PERCENTSUM,
        },
      },
      {
        name: 'Dư nợ bình quân',
        type: 'percent',
        normal: {
          value: 0.09,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        diamond: {
          value: 0.09,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        gold: {
          value: 0.09,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        platinum: {
          value: 0.09,
          type: ETypeCustomerClass.PERCENTSUM,
        },
      },
      {
        name: 'Dư nợ cuối kỳ',
        type: 'percent',
        normal: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        diamond: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        gold: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        platinum: {
          value: 0.03,
          type: ETypeCustomerClass.PERCENTSUM,
        },
      },
      {
        name: 'Lợi nhuận bình quân tháng',
        type: 'percent',
        normal: {
          value: 0.35,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        diamond: {
          value: 0.35,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        gold: {
          value: 0.35,
          type: ETypeCustomerClass.PERCENTSUM,
        },
        platinum: {
          value: 0.35,
          type: ETypeCustomerClass.PERCENTSUM,
        },
      },
    ],
  },
  {
    name: 'LỢI ÍCH',
    normal: null,
    gold: null,
    diamond: null,
    platinum: null,
    children: [
      {
        name: 'Phí giao dịch 00',
        type: 'dropdown',
        normal: {
          value: 'STANDARD',
          type: ETypeCustomerClass.DROPDOWN,
        },
        diamond: {
          value: 'VVIP',
          type: ETypeCustomerClass.DROPDOWN,
        },
        gold: {
          value: 'VIP',
          type: ETypeCustomerClass.DROPDOWN,
        },
        platinum: {
          value: 'SVIP',
          type: ETypeCustomerClass.DROPDOWN,
        },
      },
      {
        name: 'Phí giao dịch 01',
        type: 'dropdown',
        normal: {
          value: 'STANDARD',
          type: ETypeCustomerClass.DROPDOWN,
        },
        diamond: {
          value: 'VVIP',
          type: ETypeCustomerClass.DROPDOWN,
        },
        gold: {
          value: 'VIP',
          type: ETypeCustomerClass.DROPDOWN,
        },
        platinum: {
          value: 'SVIP',
          type: ETypeCustomerClass.DROPDOWN,
        },
      },
      {
        name: 'Tỷ lệ vay 01',
        type: 'dropdown',

        normal: {
          value: '5 : 5',
          type: ETypeCustomerClass.DROPDOWN,
        },
        diamond: {
          value: '5 : 5',
          type: ETypeCustomerClass.DROPDOWN,
        },
        gold: {
          value: '5 : 5',
          type: ETypeCustomerClass.DROPDOWN,
        },
        platinum: {
          value: '5 : 5',
          type: ETypeCustomerClass.DROPDOWN,
        },
      },
      {
        name: 'Lãi suất vay 01',
        type: 'proportion',
        normal: {
          value: 0.125,
          type: ETypeCustomerClass.PROPORTION,
        },
        diamond: {
          value: 0.125,
          type: ETypeCustomerClass.PROPORTION,
        },
        gold: {
          value: 0.125,
          type: ETypeCustomerClass.PROPORTION,
        },
        platinum: {
          value: 0.125,
          type: ETypeCustomerClass.PROPORTION,
        },
      },
      {
        name: 'Hạn mức vay 01',
        type: 'number',
        normal: {
          value: 3000000000,
          type: ETypeCustomerClass.NUMBER,
        },
        diamond: {
          value: 3000000000,
          type: ETypeCustomerClass.NUMBER,
        },
        gold: {
          value: 3000000000,
          type: ETypeCustomerClass.NUMBER,
        },
        platinum: {
          value: 3000000000,
          type: ETypeCustomerClass.NUMBER,
        },
      },
      {
        name: 'Tỷ lệ Margin Call',
        type: 'percent',
        normal: {
          value: 0.35,
          type: ETypeCustomerClass.PERCENT,
        },
        diamond: {
          value: 0.35,
          type: ETypeCustomerClass.PERCENT,
        },
        gold: {
          value: 0.325,
          type: ETypeCustomerClass.PERCENT,
        },
        platinum: {
          value: 0.325,
          type: ETypeCustomerClass.PERCENT,
        },
      },
      {
        name: 'Tỷ lệ Force Sell',
        type: 'percent',
        normal: {
          value: 0.25,
          type: ETypeCustomerClass.PERCENT,
        },
        diamond: {
          value: 0.25,
          type: ETypeCustomerClass.PERCENT,
        },
        gold: {
          value: 0.25,
          type: ETypeCustomerClass.PERCENT,
        },
        platinum: {
          value: 0.25,
          type: ETypeCustomerClass.PERCENT,
        },
      },
    ],
  },
];
