<div class="customer-class-container">
  <div class="header-customer-class">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-316' | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          4 Hạng
        </div>

        <div *ngIf="onEditMode" class="text-edit-cls">{{'MES-156' | translate}}: {{getCountDataEdited()}}</div>
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
    >
    </sha-grid>
  </div>
</div>

<ng-template #templateName let-name="templateInfo" let-element="element">
  @if (element?.children && element?.children?.length) {

  <div class="typo-body-6">{{name}}</div>
  } @else {
  <div class="typo-body-12">{{name}}</div>
  }
</ng-template>

<ng-template #templateData let-dataValue="templateInfo" let-element="element" let-tag="tag" let-column="column">
  <div class="space-wrapper">
    @if (dataValue?.type === ETypeCustomerClass.TOTAL) {
    <div
      *ngIf="dataValue.value"
      class="total-percent"
      [ngClass]="{
      'limit': dataValue.value > 1 && onEditMode
    }"
    >
      {{ (dataValue.value * 100 ).toFixed(2).replace('.', ',')}}%
    </div>
    <div *ngIf="!dataValue.value"></div>

    } @else if ( dataValue?.type === ETypeCustomerClass.PERCENT || dataValue?.type === ETypeCustomerClass.PERCENTSUM ||
    dataValue?.type === ETypeCustomerClass.PROPORTION) {
    <app-input-proportion-component
      [unit]="dataValue?.type === ETypeCustomerClass.PROPORTION  ?  column.componentConfig.proportion.unit : column.componentConfig.percent.unit "
      [element]="element"
      [tag]="tag"
      [isEdit]="onEditMode"
      [value]="dataValue.value"
      [initialValue]="dataValue"
      [column]="column"
      [type]="'value'"
      [typeValue]="dataValue?.type === ETypeCustomerClass.PROPORTION  ?  column.componentConfig.proportion.type : column.componentConfig.percent.type  "
      (dateChangeEvent)="updateEventUnit($event)"
      (unFocusElement)="elementSelectFunc($event)"
      [class.invalid]="onEditMode && element[this.tag].isError"
      [isInValid]="element[this.tag].isError"
    ></app-input-proportion-component>

    } @else if (dataValue?.type === ETypeCustomerClass.DROPDOWN) {
    <app-input-dropdown-table-custom
      [element]="element"
      [tag]="tag"
      [isEdit]="onEditMode"
      [value]="dataValue.value"
      [column]="column"
      [typeValue]="column.componentConfig.dropdown.type"
      [typeAccount]="dataValue.value"
      [option]="CustomerRankMap.includes(dataValue.value) ?  CUSTOMER_RANK_OPTION : RATIO_OPTION "
      (dateChangeEvent)=" updateDropdownEvent($event)"
      (unFocusElement)="elementSelectFunc($event)"
      [ngClass]="onEditMode ? '' : CONVERT_TITLE_TO_CLASS[dataValue.value]"
    >
    </app-input-dropdown-table-custom>
    } @else if (dataValue?.type === ETypeCustomerClass.NUMBER) {
    <app-input-number-custom-component
      [value]="dataValue.value"
      [isEdit]="onEditMode"
      [element]="element"
      [tag]="tag"
      (dateChangeEvent)="updateNumberEvent($event)"
      (unFocusElement)="elementSelectFunc($event)"
    ></app-input-number-custom-component>
    } @else {
    <div>{{dataValue?.value}}</div>
    }
  </div>
</ng-template>
