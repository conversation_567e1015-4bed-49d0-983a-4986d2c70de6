import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  viewChild,
} from '@angular/core';
import { DestroyService, DialogService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { fakeData } from './fakedata';
import { ETypeCustomerClass, ICustomerClass, ICustomRankConfirmParam } from '../../models/admin';
import { ETypeValueInput } from 'src/app/shared/components/input-custom-for-table/input-proportion-dropdown/input-proportion';
import {
  CONVERT_TITLE_TO_CLASS,
  CUSTOMER_RANK_OPTION,
  CustomerRankMap,
  RATIO_OPTION,
  RatioMap,
} from '../../constants/admin';
import { Store } from '@ngrx/store';
import { selectAddCustomerRank$ } from '../../store/admin.selections';
import { Observable, takeUntil } from 'rxjs';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { deepClone } from 'src/app/shared/utils/utils';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { ActivatedRoute } from '@angular/router';
import { StorageService } from 'src/app/core/services/storage.service';

/**
 * CustomerClassContainer
 * Hạng khách hàng
 */
@Component({
  selector: 'app-customer-class-container',
  templateUrl: './customer-class.container.html',
  styleUrl: './customer-class.container.scss',
})
export class CustomerClassContainer extends BaseTableComponent<any> implements OnInit {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('templateData', { static: true }) templateData: TemplateRef<any> | null = null;

  @ViewChild('templateName', { static: true }) templateName: TemplateRef<any> | null = null;

  fakeData = fakeData;

  ETypeCustomerClass = ETypeCustomerClass;

  CONVERT_TITLE_TO_CLASS = CONVERT_TITLE_TO_CLASS;

  customNumberFormat = customNumberFormat;

  customerRankData: ICustomRankConfirmParam = {
    nameRank: null,
    avrCash: null,
    finalCash: null,
    avrCollateral: null,
    finalCollateral: null,
    avrGmv: null,
    avrSpin: null,
    avrDebt: null,
    finalDebt: null,
    avrProfit: null,
    rank01: null,
    rank02: null,
    loanRate: null,
    loanInterestRate: null,
    borrowLimit: null,
    marginCallRate: null,
    forceSellRate: null,
    totalProportion: null,
  };
  CustomerRankMap = CustomerRankMap;
  CUSTOMER_RANK_OPTION = CUSTOMER_RANK_OPTION;
  RATIO_OPTION = RATIO_OPTION;

  /**
   * Constructor
   * @param store
   * @param _destroy
   */
  constructor(private store: Store, private _destroy: DestroyService) {
    super();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.export]);
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    const dataClone = this.cloneData(this.fakeData);
    this.data = structuredClone(dataClone);
    this.initialData = deepClone(this.data);
    this.columnConfigs = [
      {
        name: '',
        minWidth: 156,
        width: 156,
        tag: 'name',
        isDisplay: true,
        resizable: true,
        pinned: 'left',
        cellTemplate: this.templateName,
      },
      {
        name: 'NORMAL',
        minWidth: 156,
        width: 156,
        tag: 'normal',
        isDisplay: true,
        resizable: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        isEdit: true,
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            unit: '% ',
            type: ETypeValueInput.PERCENT,
          },
          dropdown: {
            unit: '',
            type: ETypeValueInput.DROPDOWN,
          },
          proportion: {
            unit: '%/năm',
            type: ETypeValueInput.PERCENT,
          },
          percentSum: {
            unit: '% ',
            type: ETypeCustomerClass.PERCENTSUM, // custom ETypeValueInput.PERCENT để check type tính sum
          },
        },
        cellTemplate: this.templateData,
      },
      {
        name: 'GOLD',
        minWidth: 156,
        width: 156,
        tag: 'gold',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            unit: '% ',
            type: ETypeValueInput.PERCENT,
          },
          dropdown: {
            unit: '',
            type: ETypeValueInput.DROPDOWN,
          },
          proportion: {
            unit: '%/năm',
            type: ETypeValueInput.PERCENT,
          },
          percentSum: {
            unit: '% ',
            type: ETypeCustomerClass.PERCENTSUM, // custom ETypeValueInput.PERCENT để check type tính sum
          },
        },
        cellTemplate: this.templateData,
      },
      {
        name: 'DIAMOND',
        minWidth: 156,
        width: 156,
        tag: 'diamond',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            unit: '% ',
            type: ETypeValueInput.PERCENT,
          },
          dropdown: {
            unit: '',
            type: ETypeValueInput.DROPDOWN,
          },
          proportion: {
            unit: '%/năm',
            type: ETypeValueInput.PERCENT,
          },
          percentSum: {
            unit: '% ',
            type: ETypeCustomerClass.PERCENTSUM, // custom ETypeValueInput.PERCENT để check type tính sum
          },
        },
        cellTemplate: this.templateData,
      },
      {
        name: 'PLATINUM',
        minWidth: 156,
        width: 156,
        tag: 'platinum',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            unit: '% ',
            type: ETypeValueInput.PERCENT,
          },
          dropdown: {
            unit: '',
            type: ETypeValueInput.DROPDOWN,
          },
          proportion: {
            unit: '%/năm',
            type: ETypeValueInput.PERCENT,
          },
          percentSum: {
            unit: '% ',
            type: ETypeCustomerClass.PERCENTSUM, // custom ETypeValueInput.PERCENT để check type tính sum
          },
        },
        cellTemplate: this.templateData,
      },
    ];

    this.store
      .select(selectAddCustomerRank$)
      .pipe(takeUntil(this._destroy))
      .subscribe((data) => {
        if (!data) return;
        this.customerRankData = { ...data };
        this.applyAddCustomerRank(this.customerRankData);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'edit':
        this.toggleEditMode();
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * HandleMoreAction
   * @param data
   */
  handleMoreAction(data: any) {
    console.log(data);
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.save, ActionButton.cancel, ActionButton.export]);
  }

  /**
   * @param data
   */
  applyAddCustomerRank(data: ICustomRankConfirmParam) {
    const dataClone = this.addCustomerRank(data);
    this.data = this.cloneData(dataClone);
    if (this.customerRankData.nameRank) {
      this.columnConfigs = [
        ...this.columnConfigs,
        {
          name: `${this.customerRankData.nameRank.toUpperCase()}`,
          minWidth: 156,
          width: 156,
          tag: `${this.customerRankData.nameRank}`,
          isDisplay: true,
          resizable: true,
          align: 'center',
          panelClass: 'interest-rate-cls',
          displayValueFn: (v) => {
            if (!v) return '';
            return v;
          },
          componentConfig: {
            percent: {
              unit: '% ',
              type: ETypeValueInput.PERCENT,
            },
            dropdown: {
              unit: '',
            },
            proportion: {
              unit: '%/năm',
              type: ETypeValueInput.PERCENT,
            },
          },
          cellTemplate: this.templateData,
        },
      ];
    }
  }

  /**
   * @param data
   */
  addCustomerRank(data: ICustomRankConfirmParam) {
    const {
      nameRank,
      totalProportion,
      avrCash,
      finalCash,
      avrCollateral,
      finalCollateral,
      avrGmv,
      avrSpin,
      avrDebt,
      finalDebt,
      avrProfit,
      rank01,
      rank02,
      loanRate,
      loanInterestRate,
      borrowLimit,
      marginCallRate,
      forceSellRate,
    } = data;

    const conditionLabelData: Record<string, any> = {
      'Tiền mặt bình quân': avrCash,
      'Tiền mặt cuối kỳ': finalCash,
      'Tài sản đảm bảo (NAV) bình quân': avrCollateral,
      'Tài sản đảm bảo (NAV) cuối kỳ': finalCollateral,
      'Giá trị giao dịch bình quân tháng': avrGmv,
      'Vòng quay bình quân': avrSpin,
      'Dư nợ bình quân': avrDebt,
      'Dư nợ cuối kỳ': finalDebt,
      'Lợi nhuận bình quân tháng': avrProfit,
    };

    const benefitLabelData = {
      'Phí giao dịch 00': rank01,
      'Phí giao dịch 01': rank02,
      'Tỷ lệ vay 01': loanRate,
      'Lãi suất vay 01': loanInterestRate,
      'Hạn mức vay 01': borrowLimit,
      'Tỷ lệ Margin Call': marginCallRate,
      'Tỷ lệ Force Sell': forceSellRate,
    };

    return this.fakeData.map((item: any) => {
      const newItem = { ...item };
      if (item.name === 'ĐIỀU KIỆN') {
        newItem[nameRank!] = {
          value: totalProportion,
          type: ETypeCustomerClass.TOTAL,
        };
      } else {
        newItem[nameRank!] = null;
      }

      if (item.children) {
        const fakeData: Record<string, any> = item.name === 'ĐIỀU KIỆN' ? conditionLabelData : benefitLabelData;
        newItem.children = item.children.map((child: any) => {
          const newChild = { ...child };
          if (fakeData[child.name] !== undefined) {
            newChild[nameRank!] = {
              value: fakeData[child.name],
              type:
                child.type === 'percent'
                  ? ETypeCustomerClass.PERCENT
                  : child.type === 'number'
                  ? ETypeCustomerClass.NUMBER
                  : child.type === 'proportion'
                  ? ETypeCustomerClass.PROPORTION
                  : ETypeCustomerClass.DROPDOWN,
            };
            return newChild;
          }
        });
      }
      return newItem;
    });
  }

  /**
   * @param fakeData
   */
  cloneData(fakeData: any) {
    const dataClone: ICustomerClass[] = [];
    fakeData.forEach((t: ICustomerClass) => {
      if (t.children) {
        const parentItem = { ...t };
        dataClone.push(parentItem);
        t.children.forEach((c) => dataClone.push(c));
      } else {
        dataClone.push(t);
      }
    });
    return dataClone;
  }

  /**
   * calculateTotals
   * @param tag
   */
  calculateTotals(tag: string): number {
    let total = 0;
    if (this.data && this.data.length > 0) {
      total = this.data[0].children?.reduce((sum: number, child: any) => sum + (child[tag]?.value || 0), 0);

      return total;
    }
    return 0;
  }

  /**
   * updateEvent
   * @param event event
   */
  updateEventUnit(event: any) {
    const { date, tag, element } = event;
    // Update element match
    const matchElement = this.data.find((e) => e[tag] === element[tag]);
    if (matchElement) {
      matchElement[tag].value = date;

      // Cập nhật lại element
      // Object.assign(element, matchElement);
    }

    // Update children
    const childrenElement = this.data[0].children.find((e: any) => e.name === element.name);
    if (childrenElement) {
      childrenElement[tag].value = date;
    }

    // Update parent
    const parentElement = this.data.find((t) => t.id === element.parent.id);
    // Update lại type của Total
    parentElement[tag].type = ETypeCustomerClass.TOTAL;

    // Nếu type là PERCENTSUM thì sẽ update tổng
    if (matchElement[tag].type === ETypeCustomerClass.PERCENTSUM) {
      parentElement[tag].value = this.calculateTotals(tag);
    }

    if (parentElement[tag]?.value > 1 && matchElement[tag].type === ETypeCustomerClass.PERCENTSUM) {
      matchElement[tag] = {
        ...matchElement[tag],
        isError: true, // add to change css
      };
    } else if (matchElement.parent[tag]?.value && matchElement[tag].type === ETypeCustomerClass.PERCENTSUM) {
      this.data.map((e) => {
        e.checked = true;
        e[tag] = {
          ...e[tag],
          isError: false,
        };
      });
    }
    parentElement.isInValid = parentElement[tag]?.value > 1;
  }

  /**
   * updateNumberEvent
   * @param event event
   */
  updateNumberEvent(event: any) {
    const { date, tag, element } = event;
    const matchElement = this.data.find((e) => e[tag] === element[tag]);
    if (matchElement) {
      matchElement[tag] = {
        value: date,
        type: ETypeCustomerClass.NUMBER,
      };
    }
  }

  /**
   * updateDropdown
   * @param event event
   */
  updateDropdownEvent(event: any) {
    const { date, tag, element } = event;

    const matchElement = this.data.find((e) => e[tag] === element[tag]);
    if (matchElement) {
      matchElement[tag] = { ...date };
    }
  }
}
