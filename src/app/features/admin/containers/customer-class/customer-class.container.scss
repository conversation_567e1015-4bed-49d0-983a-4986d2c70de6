.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.customer-class-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-customer-class {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          // text
          white-space: nowrap;
          text-wrap: nowrap;
        }

        .text-edit-cls {
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          white-space: nowrap;
          text-wrap: nowrap;
          color: var(--color--brand--500);
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;

      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--cancel {
            color: var(--color--danger--600);
            border: 1px solid var(--color--danger--600);
          }
        }

        &.not-accept-save-cls {
          ::ng-deep {
            #box-id--save {
              opacity: 0.5;
            }
          }
        }
        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }
  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        // Trạng
        td:has(mat-icon[data-mat-icon-name='arrow-right']) {
          mat-icon {
            display: none;
          }
        }

        .interest-rate-cls {
          text-align: center !important;
          .container-box-proportion {
            padding: 0px 14px 0px 8px;
            background-color: var(--color--accents--yellow-dark);
            border-radius: 16px;
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 170px;

            input {
              text-align: center;
            }
          }
          .total-cls {
            .container-box-proportion {
              background-color: var(--color--neutral--100);
            }
          }
        }

        //total
        .interest-rate-cls:has(.content-center):has(.limit) {
          border: 1px solid var(--color--danger--700) !important;
        }

        .interest-rate-cls:has(app-input-proportion-component):has(.content-center):has(.invalid) {
          border: 1px solid var(--color--danger--700) !important;
        }

        .content-center:has(.total-percent) {
          justify-content: center;

          .total-percent {
            padding: 2px 14px;
            background-color: var(--color--neutral--100);
            border-radius: 16px;
            white-space: nowrap;
            text-wrap: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 170px;

            &.limit {
              color: var(--color--danger--700);
            }
          }
        }

        .space-wrapper {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        td:has(app-input-proportion-component) {
          padding: 0; // Để  focus full chiều dài ô
          app-input-proportion-component {
            width: 100%; // Để  focus full chiều dài ô
          }
          .content-center {
            justify-content: center;
          }
        }

        td:has(app-input-dropdown-table-custom) {
          padding: 0; // Để  focus full chiều dài ô
          app-input-dropdown-table-custom {
            width: 100%; // Để  focus full chiều dài ô
          }
          .content-center {
            justify-content: center;
          }
          .container-box-proportion-dropdown {
            justify-content: center;
            min-width: 170px;
            padding: 0;
            .box-left {
              display: none;
            }
          }
          .box-section-change {
            width: 100%;
            justify-content: center;
          }
          .box-container-dropdown-cls {
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .standard-cls {
            .box-section-change {
              background-color: var(--color--neutral--100);
              border-color: var(--color--neutral--100);
            }
          }
          .vvip-cls {
            .box-section-change {
              background-color: var(--color--accents--mint-dark);
              border-color: var(--color--accents--mint-dark);
            }
          }

          .svip-cls {
            .box-section-change {
              background-color: var(--color--accents--orange-dark);
              border-color: var(--color--accents--orange-dark);
            }
          }
        }

        td:has(app-input-number-custom-component) {
          padding: 0; // Để  focus full chiều dài ô
          app-input-number-custom-component {
            width: 100%; // Để  focus full chiều dài ô
          }
          .content-center {
            justify-content: center;
          }
          .input-number-custom-cls {
            .input-cls {
              text-align: center;
            }
          }
        }
      }
    }
  }
}
