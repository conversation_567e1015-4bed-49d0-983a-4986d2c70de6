import { AfterViewInit, ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { DialogService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { Observable } from 'rxjs';
import { ETypeValueInput } from 'src/app/shared/components/input-custom-for-table/input-proportion/input-proportion';
import { CONVERT_TITLE_TO_CLASS, CONVERT_UNIT_TRANSACTION_TO_LABEL } from '../../constants/admin';
import { fakeData } from './fakeData';
import { ActivatedRoute } from '@angular/router';
import { StorageService } from 'src/app/core/services/storage.service';

/**
 * MarginTransactionFeeContainer
 * Phí giao dịch ký quỹ
 */
@Component({
  selector: 'app-margin-transaction-fee',
  templateUrl: './margin-transaction-fee.container.html',
  styleUrl: './margin-transaction-fee.container.scss',
})
export class MarginTransactionFeeContainer extends BaseTableComponent<any> implements OnInit {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('templateData', { static: true }) templateData: TemplateRef<any> | null = null;

  fakeData = fakeData;

  ETypeValueInput = ETypeValueInput;

  CONVERT_TITLE_TO_CLASS = CONVERT_TITLE_TO_CLASS;
  CONVERT_UNIT_TRANSACTION_TO_LABEL = CONVERT_UNIT_TRANSACTION_TO_LABEL;

  valueOptions = [
    {
      label: '% /giao dịch',
      value: ETypeValueInput.PERCENT,
    },
    {
      label: 'VND /giao dịch',
      value: ETypeValueInput.NUMBER,
    },
  ];

  /**
   * Constructor
   */
  constructor() {
    super();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.export]);
  }

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        name: 'Khoảng NAV',
        minWidth: 156,
        width: 156,
        tag: 'name',
        isDisplay: true,
        resizable: true,
        pinned: 'left',
      },
      {
        name: 'STANDARD',
        minWidth: 156,
        width: 156,
        tag: 'standard',
        isDisplay: true,
        resizable: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        isEdit: true,
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            type: ETypeValueInput.PERCENT,
            option: this.valueOptions,
          },
          number: {
            type: ETypeValueInput.NUMBER,
            option: this.valueOptions,
          },
        },
        cellTemplate: this.templateData,
      },
      {
        name: 'VIP',
        minWidth: 156,
        width: 156,
        tag: 'vip',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            type: ETypeValueInput.PERCENT,
            option: this.valueOptions,
          },
          number: {
            type: ETypeValueInput.NUMBER,
            option: this.valueOptions,
          },
        },
        cellTemplate: this.templateData,
      },
      {
        name: 'VVIP',
        minWidth: 156,
        width: 156,
        tag: 'vvip',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            type: ETypeValueInput.PERCENT,
            option: this.valueOptions,
          },
          number: {
            type: ETypeValueInput.NUMBER,
            option: this.valueOptions,
          },
        },
        cellTemplate: this.templateData,
      },
      {
        name: 'SVIP',
        minWidth: 156,
        width: 156,
        tag: 'svip',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            type: ETypeValueInput.PERCENT,
            option: this.valueOptions,
          },
          number: {
            type: ETypeValueInput.NUMBER,
            option: this.valueOptions,
          },
        },
        cellTemplate: this.templateData,
      },
    ];
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'edit':
        this.toggleEditMode();
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * HandleMoreAction
   * @param data
   */
  handleMoreAction(data: any) {
    console.log(data);
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.save, ActionButton.cancel, ActionButton.export]);
  }

  /**
   * updateBasetransactionFee
   * @param data
   */
  updateBaseTransactionFee(data: any) {
    const { date, tag, element } = data;

    const matchElement = this.data.find((e) => e[tag] === element[tag]);
    if (matchElement) {
      if (typeof date === 'object' && date !== null) {
        // Sửa kiểu và giá trị
        matchElement[tag].type = date.type;
        matchElement[tag].value = date.value;
      } else {
        // Sửa giá trị
        matchElement[tag].value = date;
      }
    }
  }
}
