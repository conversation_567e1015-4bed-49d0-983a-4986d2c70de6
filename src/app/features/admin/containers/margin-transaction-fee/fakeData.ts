import { ETypeCustomerClass, ITransactionFee } from '../../models/admin';

export const fakeData: ITransactionFee[] = [
  {
    name: '0 - 49.999.999',
    standard: {
      value: 100000,
      type: ETypeCustomerClass.NUMBER,
    },
    vip: {
      value: 75000,
      type: ETypeCustomerClass.NUMBER,
    },
    svip: {
      value: 50000,
      type: ETypeCustomerClass.NUMBER,
    },
    vvip: {
      value: 50000,
      type: ETypeCustomerClass.NUMBER,
    },
  },
  {
    name: '50.000.000 - 99.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '100.000.000 - 249.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },

  {
    name: '250.000.000 - 499.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '500.000.000 - 749.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '750.000.000 - 999.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '************* - 2.499.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '2.500.000.000 - 4.999.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '************* - 7.499.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '7.500.000.000 - 9.999.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '************** - 14.999.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '************** - 19.999.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '************** - 24.999.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '************** - 34.999.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '3************* - 39.999.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '************** - 49.999.999.999',
    standard: {
      value: 0.0035,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.003,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.002,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0025,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    name: '≥ **************',
    standard: {
      value: 0.0008,
      type: ETypeCustomerClass.PERCENT,
    },
    vip: {
      value: 0.0008,
      type: ETypeCustomerClass.PERCENT,
    },
    svip: {
      value: 0.0008,
      type: ETypeCustomerClass.PERCENT,
    },
    vvip: {
      value: 0.0008,
      type: ETypeCustomerClass.PERCENT,
    },
  },
];
