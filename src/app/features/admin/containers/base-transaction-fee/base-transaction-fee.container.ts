import { AfterViewInit, ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild, viewChild } from '@angular/core';
import { DialogService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { ETypeValueInput } from 'src/app/shared/components/input-custom-for-table/input-proportion-dropdown/input-proportion';
import {
  CONVERT_FEE_GROUP_TO_LABEL,
  CONVERT_STOCK_MARKET_TYPE,
  CONVERT_TITLE_TO_CLASS,
  CONVERT_UNIT_TRANSACTION_TO_LABEL,
} from '../../constants/admin';
import { fakeData } from './fakedata';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { deepClone } from 'src/app/shared/utils/utils';
import { Observable } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { StorageService } from 'src/app/core/services/storage.service';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { CELL_TYPE } from '@shared/models';

/**
 * BaseTransactionFeeContainer
 * Quản lý giao dịch cơ sở
 */
@Component({
  selector: 'app-base-transaction-fee-container',
  templateUrl: './base-transaction-fee.container.html',
  styleUrl: './base-transaction-fee.container.scss',
})
export class BaseTransactionFeeContainer extends BaseTableComponent<any> implements OnInit {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('templateData', { static: true }) templateData: TemplateRef<any> | null = null;

  @ViewChild('calendarTemplate', { static: true }) calendarTemplate: TemplateRef<any> | null = null;

  fakeData = fakeData;

  ETypeValueInput = ETypeValueInput;

  CONVERT_TITLE_TO_CLASS = CONVERT_TITLE_TO_CLASS;
  CONVERT_UNIT_TRANSACTION_TO_LABEL = CONVERT_UNIT_TRANSACTION_TO_LABEL;
  customNumberFormat = customNumberFormat;

  valueOptions = [
    {
      label: '% /giao dịch',
      value: ETypeValueInput.PERCENT,
    },
    {
      label: 'VND /giao dịch',
      value: ETypeValueInput.NUMBER,
    },
  ];

  /**
   * Constructor
   */
  constructor() {
    super();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.export]);
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.initialData = deepClone(this.data);
    this.columnConfigs = [
      {
        name: 'Nhóm lệ phí',
        minWidth: 156,
        width: 250,
        tag: 'feeGroup',
        isDisplay: true,
        resizable: true,
        pinned: 'left',
        isEdit: false,
        align: 'start',
        displayValueFn: (v) => {
          return CONVERT_FEE_GROUP_TO_LABEL[v];
        },
      },
      {
        name: 'Tổng tiền tối thiểu',
        minWidth: 156,
        width: 250,
        tag: 'minimumTotalAmount',
        isDisplay: true,
        type: 'text',
        typeValue: CELL_TYPE.TEXT,
        isEdit: true,
        displayValueFn: (v) => {
          if (v === 0) return '0';
          return customNumberFormat(v);
        },
        resizable: true,
        align: 'end',
      },
      {
        name: 'Tổng tiền lớn nhất',
        minWidth: 156,
        width: 250,
        tag: 'maximumTotalAmount',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        type: 'text',
        typeValue: CELL_TYPE.TEXT,
        displayValueFn: (v) => {
          return customNumberFormat(v);
        },
        align: 'end',
      },
      {
        name: 'Phân loại thị trường CK',
        minWidth: 156,
        width: 250,
        tag: 'stockMarketType',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'start',
        displayValueFn: (v) => CONVERT_STOCK_MARKET_TYPE[v],
        typeValue: CELL_TYPE.DROPDOWN,
        componentConfig: {
          multiple: false,
          isSearch: false,
          searchKey: 'value',
          displayOptionFn: (v: any) => v.name,
          options: [
            {
              name: 'Tất cả',
              value: 'ALL',
            },
            {
              name: 'UPCOM',
              value: 'UPCOM',
            },
            {
              name: 'HOSE',
              value: 'HOSE',
            },
            {
              name: 'HNX',
              value: 'HNX',
            },
          ],
        },
      },
      {
        name: 'Ngày áp dụng',
        minWidth: 156,
        width: 250,
        tag: 'dateOfApplication',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'start',
        typeValue: CELL_TYPE.CALENDAR,
        cellTemplate: this.calendarTemplate,
      },
      {
        name: 'Tỉ lệ áp dụng',
        minWidth: 156,
        width: 250,
        tag: 'applicableRate',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            type: ETypeValueInput.PERCENT,
            option: this.valueOptions,
          },
          number: {
            type: ETypeValueInput.NUMBER,
            option: this.valueOptions,
          },
        },
        cellTemplate: this.templateData,
      },
    ];
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'edit':
        this.toggleEditMode();
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   * HandleMoreAction
   * @param data
   */
  handleMoreAction(data: any) {
    console.log(data);
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.save, ActionButton.cancel, ActionButton.export]);
  }

  /**
   * updateBasetransactionFee
   * @param data
   */
  updateBaseTransactionFee(data: any) {
    const { date, tag, element } = data;

    const matchElement = this.data.find((e) => e[tag] === element[tag]);
    if (matchElement) {
      if (typeof date === 'object' && date !== null) {
        // Sửa kiểu và giá trị
        matchElement[tag].type = date.type;
        matchElement[tag].value = date.value;
      } else {
        // Sửa giá trị
        matchElement[tag].value = date;
      }
    }
  }
}
