import { ETypeCustomerClass } from '../../models/admin';

export interface IBaseTransactionFee {
  feeGroup: number; // 0: KH thường
  minimumTotalAmount: number;
  maximumTotalAmount: number;
  stockMarketType: string;
  dateOfApplication: string;
  applicableRate: {
    value: number;
    type: number;
  };
}

export const fakeData = [
  {
    feeGroup: 0,
    minimumTotalAmount: 0,
    maximumTotalAmount: 49999999999,
    stockMarketType: 'UPCOM',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 50000000000,
    maximumTotalAmount: 199999999999,
    stockMarketType: 'UPCOM',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 200000000000,
    maximumTotalAmount: 9999999999999,
    stockMarketType: 'UPCOM',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 200000000000,
    maximumTotalAmount: 100000000000000,
    stockMarketType: 'HOSE',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 0,
    maximumTotalAmount: 49999999999,
    stockMarketType: 'HOSE',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 50000000000,
    maximumTotalAmount: 199999999999,
    stockMarketType: 'HOSE',
    dateOfApplication: '17/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 200000000000,
    maximumTotalAmount: 100000000000000,
    stockMarketType: 'HOSE',
    dateOfApplication: '12/07/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 0,
    maximumTotalAmount: 49999999999,
    stockMarketType: 'HNX',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 50000000000,
    maximumTotalAmount: 199999999999,
    stockMarketType: 'HNX',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 0,
    maximumTotalAmount: 49999999999,
    stockMarketType: 'ALL',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 0,
    maximumTotalAmount: 49999999999,
    stockMarketType: 'ALL',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 0,
    maximumTotalAmount: 49999999999,
    stockMarketType: 'ALL',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 0,
    maximumTotalAmount: 49999999999,
    stockMarketType: 'ALL',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 0,
    maximumTotalAmount: 49999999999,
    stockMarketType: 'ALL',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
  {
    feeGroup: 0,
    minimumTotalAmount: 0,
    maximumTotalAmount: 49999999999,
    stockMarketType: 'ALL',
    dateOfApplication: '01/01/2012',
    applicableRate: {
      value: 0.001,
      type: ETypeCustomerClass.PERCENT,
    },
  },
];
