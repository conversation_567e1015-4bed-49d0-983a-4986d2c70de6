import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnInit,
  QueryList,
  TemplateRef,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import { DestroyService, DialogService } from 'src/app/core/services';
import { ActionButton, IActionBtn } from 'src/app/shared/components/action-btn/action-btn.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { CONVERT_ROLE_TO_LABEL, ERoleGroupData } from '../../constants/admin';
import { deepClone } from 'src/app/shared/utils/utils';
import { menuData } from './menuData';
import { CELL_TYPE, IColumnConfig } from '@shared/models';
import { ELevelFeature } from '../../models/admin';
import { ActivatedRoute } from '@angular/router';
import { StorageService } from 'src/app/core/services/storage.service';

/**
 * Nhóm quyền
 */
@Component({
  selector: 'app-role-group',
  templateUrl: './role-group.container.html',
  styleUrl: './role-group.container.scss',
})
export class RoleGroupContainer extends BaseTableComponent<any> implements OnInit, ComponentCanDeactivate {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('groupName', { static: true }) groupNameTemplate: TemplateRef<any> | null = null;

  @ViewChild('menu', { static: true }) pageTemplate: TemplateRef<any> | null = null;

  @ViewChild('detail', { static: true }) detailFeatureTemplate: TemplateRef<any> | null = null;

  @ViewChildren('permissionGroupName') permissionGroupNames!: QueryList<ElementRef>;

  isEdit = false;

  CONVERT_ROLE_TO_LABEL = CONVERT_ROLE_TO_LABEL;

  menuData = menuData;

  permissionChosen = 'SUPER ADMIN';
  menuChosen = 'Quản lý khách hàng';
  pageChosen = 'Thông tin cá nhân';
  actionChosen = 'Xem danh sách';
  menuCheck: string[] = [];
  pageCheck: string[] = [];
  actionCheck: string[] = [];
  featureCheck: string[] = [];

  fakeData: any = [
    {
      permissionGroupName: 'SUPER ADMIN',
      menuFeature: {
        menu: {
          name: 'Quản lý Khách hàng',
        },
        page: {
          name: 'Thông tin cá nhân',
        },
      },
      detailFeature: {
        action: {
          name: 'Xem danh sách',
        },
        feature: {
          name: 'Số tài khoản',
        },
      },
    },
    {
      permissionGroupName: 'BACK ADMIN',
      menuFeature: {
        menu: {
          name: 'Quản lý Tài sản',
        },
        page: {
          name: 'Thông tin ngân hàng',
        },
      },
      detailFeature: {
        action: {
          name: 'Xem chi tiết',
        },
        feature: {
          name: 'Tên khách hàng',
        },
      },
    },
    {
      permissionGroupName: 'BROKER ADMIN',
      menuFeature: {
        menu: {
          name: 'Quản lý Khuyến nghị',
        },
        page: {
          name: 'Thông tin tài liệu',
        },
      },
      detailFeature: {
        action: {
          name: 'Cập nhật thông tin',
        },
        feature: {
          name: 'Loại tài khoản',
        },
      },
    },
    {
      permissionGroupName: 'BACK',
      menuFeature: {
        menu: {
          name: 'Quản lý Lệnh đặt',
        },
        page: {
          name: 'Thông tin tài khoản',
        },
      },
      detailFeature: {
        action: {
          name: 'Xuất file PDF',
        },
        feature: {
          name: 'Số CMND / CCCD HC',
        },
      },
    },
    {
      permissionGroupName: 'BROKER',
      menuFeature: {
        menu: {
          name: 'Hiệu suất công việc (Môi giới)',
        },
        page: {
          name: 'Thông tin uỷ quyền',
        },
      },
      detailFeature: {
        action: {
          name: 'Xuất file SVG',
        },
        feature: {
          name: 'Ngày cấp',
        },
      },
    },
    {
      permissionGroupName: 'REMISER',
      menuFeature: {
        menu: {
          name: 'Hiệu suất công việc (Trưởng phòng)',
        },
        page: {
          name: 'Nhóm khách hàng',
        },
      },
      detailFeature: {
        action: {
          name: null,
        },
        feature: {
          name: 'Nơi cấp',
        },
      },
    },
    {
      permissionGroupName: null,
      menuFeature: {
        menu: {
          name: 'Hiệu suất công việc (Admin)',
        },
        page: {
          name: null,
        },
      },
      detailFeature: {
        action: {
          name: null,
        },
        feature: {
          name: 'Quốc tịch',
        },
      },
    },
    {
      permissionGroupName: null,
      menuFeature: {
        menu: {
          name: 'Quản lý dành cho Admin',
        },
        page: {
          name: null,
        },
      },
      detailFeature: {
        action: {
          name: null,
        },
        feature: {
          name: 'Ngày sinh',
        },
      },
    },
    {
      permissionGroupName: null,
      menuFeature: {
        menu: {
          name: null,
        },
        page: {
          name: null,
        },
      },
      detailFeature: {
        action: {
          name: null,
        },
        feature: {
          name: 'Giới tính',
        },
      },
    },
    {
      permissionGroupName: null,
      menuFeature: {
        menu: {
          name: null,
        },
        page: {
          name: null,
        },
      },
      detailFeature: {
        action: {
          name: null,
        },
        feature: {
          name: 'Số điện thoại',
        },
      },
    },
    {
      permissionGroupName: null,
      menuFeature: {
        menu: {
          name: null,
        },
        page: {
          name: null,
        },
      },
      detailFeature: {
        action: {
          name: null,
        },
        feature: {
          name: 'Email',
        },
      },
    },
    {
      permissionGroupName: null,
      menuFeature: {
        menu: {
          name: null,
        },
        page: {
          name: null,
        },
      },
      detailFeature: {
        action: {
          name: null,
        },
        feature: {
          name: 'Địa chỉ',
        },
      },
    },
    {
      permissionGroupName: null,
      menuFeature: {
        menu: {
          name: null,
        },
        page: {
          name: null,
        },
      },
      detailFeature: {
        action: {
          name: null,
        },
        feature: {
          name: 'Số ĐKKD',
        },
      },
    },
    {
      permissionGroupName: null,
      menuFeature: {
        menu: {
          name: null,
        },
        page: {
          name: null,
        },
      },
      detailFeature: {
        action: {
          name: null,
        },
        feature: {
          name: 'Ngày cấp ĐKKD',
        },
      },
    },
  ];

  additionalButton: IActionBtn = {
    label: 'MES-28',
    icons: 'icon:add-icon',
    name: 'add-icon',
    isDisplayed: false,
    tag: ActionButton.add,
  };

  /**
   * Constructor
   * @param cdr ChangeDetectorRef
   */
  constructor(private cdr: ChangeDetectorRef) {
    super();
    this.actionButtons.unshift(this.additionalButton);
    this.toggleButtonByTags([
      ActionButton.add,
      ActionButton.edit,
      ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);
  }

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.fakeData.unshift({
      type: 'search',
    });
    const dataClone = this.fakeData.map((item: any) => {
      if (item.menuFeature) {
        item.menuFeature.menu = { ...item.menuFeature.menu, isSelect: false };
        item.menuFeature.page = { ...item.menuFeature.page, isSelect: false };
      }
      if (item.detailFeature) {
        item.detailFeature.action = { ...item.detailFeature.action, isSelect: false };
        item.detailFeature.feature = { ...item.detailFeature.feature, isSelect: false };
      }
      return item;
    });

    this.data = [...dataClone];
    this.initialData = structuredClone(this.data);

    const permissionGroupNameTemplate = this.groupNameTemplate;
    const permissionPageTemplate = this.pageTemplate;
    const permissionDetailFeatureTemplate = this.detailFeatureTemplate;

    this.columnConfigs = [
      {
        name: 'Tên nhóm quyền',
        minWidth: 30,
        width: 263,
        tag: 'permissionGroupName',
        isDisplay: true,
        dragDisabled: true,
        isEdit: true,
        pinned: 'left',
        resizable: true,
        typeValue: CELL_TYPE.TEXT,
        type: 'text',
        cellTemplate: permissionGroupNameTemplate,
      },
      {
        name: 'Menu Tính năng',
        minWidth: 30,
        width: 559,
        tag: 'menuFeature',
        isDisplay: true,
        isEdit: true,
        resizable: true,
        cellTemplate: permissionPageTemplate,
      },
      {
        name: 'Chi tiết tính năng',
        minWidth: 30,
        width: 485,
        tag: 'detailFeature',
        isDisplay: true,
        isEdit: true,
        resizable: true,
        cellTemplate: permissionDetailFeatureTemplate,
      },
    ];
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'add':
        this.addBtn();
        break;
      case 'filter':
        break;
      case 'edit':
        this.toggleEditMode();
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * addBtn
   */
  addBtn() {
    const dataClone = deepClone(this.data);
    const permissionGroupNameLength = dataClone.filter((item) => item.permissionGroupName !== null).length;

    const newRow = {
      permissionGroupName: '',
      tag: `tag-${permissionGroupNameLength}`,
    };

    dataClone[permissionGroupNameLength] = {
      ...dataClone[permissionGroupNameLength],
      ...newRow,
    };

    this.cdr.detectChanges();
    const newRowTag = dataClone[permissionGroupNameLength].tag;
    setTimeout(() => {
      const rowElement = this.permissionGroupNames.find(
        (element) => element.nativeElement.getAttribute('data-tag') === newRowTag
      );

      if (rowElement) {
        rowElement.nativeElement.querySelector('.focus-input').focus();
      }
    }, 0);

    this.data = deepClone(dataClone);
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([
      ActionButton.add,
      ActionButton.edit,
      ActionButton.save,
      ActionButton.cancel,
      ActionButton.export,
    ]);
  }

  /**
   * Click To Show Menu Column
   */
  showMenu(element: any) {
    this.permissionChosen = element.permissionGroupName;
    this.menuChosen = '';
    this.pageChosen = '';
    this.featureCheck = [];
    let dataClone = deepClone(this.data);
    dataClone.forEach((data, index) => {
      if (index < 1) return;
      if (index - 1 < this.menuData.length) {
        data.menuFeature.menu.name = this.menuData[index - 1].name;
      }

      data.menuFeature.menu.isSelect = false;
      data.menuFeature.page.name = null;
      data.detailFeature.action.name = null;
      data.detailFeature.feature.name = null;
    });

    const filteredData = this.filteredRowData(dataClone);
    dataClone = dataClone.slice(0, filteredData.length);
    this.data = deepClone(dataClone);
  }

  /**
   * Click To Show Page Column
   * @param element
   */
  showPage(element: any) {
    this.menuChosen = element.menuFeature.menu.name;
    this.pageChosen = '';
    this.featureCheck = [];
    // Find matching Menu
    const matchingMenu = this.menuData.find(
      (menu) => menu.name.toLowerCase() === element.menuFeature.menu.name.toLowerCase()
    );
    this.mapData(this.data, matchingMenu, ELevelFeature.PAGE);
    let dataClone = deepClone(this.data);
    // Add new items if matchingMenu.children.length > data.length
    const matchingMenuLength = matchingMenu?.children.length ?? 0;
    if (matchingMenuLength > dataClone.length) {
      this.addNewItem(dataClone, matchingMenuLength, matchingMenu, ELevelFeature.PAGE);
    }
    if (matchingMenuLength < this.data.length) {
      // Update only the children fields for existing elements
      this.data.forEach((_, i) => {
        if (i < 1) return; // Skip the first element
        dataClone[i].menuFeature.page.name = i - 1 < matchingMenuLength ? matchingMenu?.children[i - 1].name : null;
      });
    }
    // update data with new click page
    dataClone.forEach((data: any) => {
      if (data.detailFeature) {
        data.detailFeature.action.name = null;
        data.detailFeature.feature.name = null;
      }
    });

    const filteredData = this.filteredRowData(dataClone);
    dataClone = dataClone.slice(0, filteredData.length);
    this.data = deepClone(dataClone);
  }

  /**
   * Click To Show Action Column
   * @param element
   */
  showAction(element: any) {
    this.pageChosen = element.menuFeature.page.name;
    this.actionChosen = '';
    this.featureCheck = [];
    // Find matching page
    let matchingPage: any;
    this.menuData.some((menu) => {
      const matchingChild = menu.children?.find(
        (child) => child.name.toLowerCase() === element.menuFeature.page.name.toLowerCase()
      );
      if (matchingChild) {
        matchingPage = matchingChild;
        return true;
      }
      return false;
    });
    this.mapData(this.data, matchingPage, ELevelFeature.ACTION);
    let dataClone = deepClone(this.data);
    // Add new items if matchingPage.children.length > data.length
    const matchingActionButtonLength = matchingPage?.actionButton.length ?? 0;
    if (matchingActionButtonLength > dataClone.length) {
      this.addNewItem(dataClone, matchingActionButtonLength, matchingPage, ELevelFeature.ACTION);
    }
    if (matchingActionButtonLength < this.data.length) {
      // Update only the children fields for existing elements
      this.data.forEach((_, i) => {
        if (i < 1) return; // Skip the first element
        dataClone[i].detailFeature.action.name =
          i - 1 < matchingActionButtonLength ? matchingPage?.actionButton[i - 1].name : null;
        dataClone[i].detailFeature.feature.name = null;
      });
    }
    // update data with new click action
    dataClone.forEach((data: any) => {
      if (data.detailFeature) {
        data.detailFeature.feature.name = null;
      }
    });

    const filteredData = this.filteredRowData(dataClone);
    dataClone = dataClone.slice(0, filteredData.length);
    this.data = deepClone(dataClone);
  }

  /**
   * Click To Show Feature Column
   * @param element
   */
  showFeature(element: any) {
    this.actionChosen = element.detailFeature.action.name;
    this.featureCheck = [];
    // Find matching ActionButton
    const matchPage = this.menuData
      .flatMap((menu) => menu.children)
      .find((page) => page.name.toLowerCase() === this.pageChosen.toLowerCase());

    const matchAction = matchPage?.actionButton.find(
      (action) => action.name.toLowerCase() === element.detailFeature.action.name.toLowerCase()
    );
    this.mapData(this.data, matchAction, ELevelFeature.FEATURE);
    let dataClone = deepClone(this.data);
    const matchingFeatureLength = matchAction?.feature.length ?? 0;

    if (matchingFeatureLength > dataClone.length) {
      this.addNewItem(dataClone, matchingFeatureLength, matchAction, ELevelFeature.FEATURE);
    } else {
      for (let i = 1; i < matchingFeatureLength; i++) {
        dataClone[i].detailFeature.feature.name =
          i - 1 < matchingFeatureLength ? matchAction?.feature[i - 1].name : null;
      }
    }

    if (matchingFeatureLength < this.data.length) {
      // Update only the children fields for existing elements
      this.data.forEach((_, i) => {
        if (i < 1) return; // Skip the first element
        dataClone[i].detailFeature.feature.name =
          i - 1 < matchingFeatureLength ? matchAction?.feature[i - 1].name : null;
      });
    }
    const filteredData = this.filteredRowData(dataClone);
    dataClone = dataClone.slice(0, filteredData.length);
    this.data = deepClone(dataClone);
  }

  /**
   * addNewItem
   * @param dataClone
   * @param matchingFieldLength
   * @param matchField
   * @param level
   */
  addNewItem(dataClone: any, matchingFieldLength: number, matchField: any, level: string) {
    for (let i = dataClone.length; i < matchingFieldLength; i++) {
      dataClone.push({
        permissionGroupName: null,
        menuFeature: {
          menu: {
            name: null,
            isSelect: false,
          },
          page: {
            name: level === ELevelFeature.PAGE ? matchField?.children[i - 1].name : null,
            isSelect: false,
          },
        },
        detailFeature: {
          action: {
            name: level === ELevelFeature.ACTION ? matchField?.actionButton[i - 1].name : null,
            isSelect: false,
          },
          feature: {
            name: level === ELevelFeature.FEATURE ? matchField?.feature[i - 1].name : null,
            isSelect: false,
          },
        },
        initIndex: i,
        level: 0,
      });
    }
  }

  /**
   * mapData
   * @param thisData
   * @param matchField
   * @param level
   */
  mapData(thisData: any, matchField: any, level: string) {
    thisData.forEach((data: any, index: number) => {
      if (index < 1) return; // Skip first element

      // Generic function to map field values
      const mapField = (target: any, matchArray: any[], level: string) => {
        target[level].name = null;
        if (index - 1 < matchArray.length) {
          target[level].name = matchArray[index - 1].name;
          target[level].isSelect = false;
        }
      };

      switch (level) {
        case 'page':
          if (matchField?.children) {
            mapField(data.menuFeature, matchField.children, ELevelFeature.PAGE);
          }
          break;
        case 'action':
          if (matchField?.actionButton) {
            mapField(data.detailFeature, matchField.actionButton, ELevelFeature.ACTION);
          }
          break;
        case 'feature':
          if (matchField?.feature) {
            mapField(data.detailFeature, matchField.feature, ELevelFeature.FEATURE);
          }
          break;
        default:
          break;
      }
    });
  }

  filteredRowData(data: any) {
    const filteredData = data.filter(
      (item: any) =>
        item.permissionGroupName !== null ||
        item.menuFeature.menu.name !== null ||
        item.menuFeature.page.name !== null ||
        item.detailFeature.action.name !== null ||
        item.detailFeature.feature.name !== null
    );
    return filteredData;
  }

  /**
   * isSuperAdmin
   * @param {string} permissionGroupName
   */
  isSuperAdmin(permissionGroupName: string) {
    return permissionGroupName === 'SUPER ADMIN';
  }

  /**
   * handleUpdateSelection
   * @param dataClone
   * @param checked
   * @param level
   */
  handleUpdateSelection(dataClone: ERoleGroupData[], checked: boolean, level: string) {
    dataClone.forEach((item: ERoleGroupData) => {
      switch (level) {
        case ELevelFeature.MENU:
          // Update các lựa chọn của page & action & feature khi chọn menu
          if (item.menuFeature.page) item.menuFeature.page.isSelect = false;
          if (item.detailFeature.action) item.detailFeature.action.isSelect = false;
          if (item.detailFeature.feature) item.detailFeature.feature.isSelect = false;
          break;
        case ELevelFeature.PAGE:
          // Update các lựa chọn của menu & action & feature khi chọn page
          if (item.menuFeature?.menu)
            item.menuFeature.menu.isSelect = item.menuFeature.menu?.name === this.menuChosen ? checked : false;
          item.detailFeature.action.isSelect = false;
          item.detailFeature.feature.isSelect = false;
          break;
        case ELevelFeature.ACTION:
          // Update các lựa chọn của menu & page & feature khi chọn action
          if (item.menuFeature?.menu)
            item.menuFeature.menu.isSelect = item.menuFeature.menu?.name === this.menuChosen ? checked : false;
          if (item.menuFeature?.page)
            item.menuFeature.page.isSelect = item.menuFeature.page?.name === this.pageChosen ? checked : false;
          item.detailFeature.feature.isSelect = false;
          break;
        case ELevelFeature.FEATURE:
          // Update các lựa chọn của menu & page & action khi chọn feature
          if (item.menuFeature?.menu)
            item.menuFeature.menu.isSelect = item.menuFeature.menu?.name === this.menuChosen ? checked : false;
          if (item.menuFeature?.page)
            item.menuFeature.page.isSelect = item.menuFeature.page?.name === this.pageChosen ? checked : false;
          if (item.detailFeature?.action)
            item.detailFeature.action.isSelect =
              item.detailFeature.action?.name === this.actionChosen ? checked : false;
          break;
        default:
          break;
      }
    });
  }

  /**
   * handleSelectMenu
   * @param {boolean} checked
   * @param menuFeature
   */
  changeMenu(checked: boolean, menuFeature: any) {
    menuFeature.menu.isSelect = checked;
    const dataClone = deepClone(this.data);

    if (checked) {
      this.menuCheck.push(menuFeature.menu.name); // Push vào mảng khi menu được select
      this.handleUpdateSelection(dataClone, checked, ELevelFeature.MENU);
    } else {
      // Filter khỏi mảng khi menu unselect
      this.menuCheck = this.menuCheck.filter((item) => item !== menuFeature.menu?.name);

      if (!this.menuCheck.length) {
        // Khi unselect tất cả menu, unselect tất cả phần còn lại
        dataClone.forEach((item: any) => {
          if (item.menuFeature?.page) item.menuFeature.page.isSelect = false;
          if (item.detailFeature?.action) item.detailFeature.action.isSelect = false;
          if (item.detailFeature?.feature) item.detailFeature.feature.isSelect = false;
        });
      }
    }

    this.data = deepClone(dataClone);
  }

  /**
   * handle Select Page
   * @param checked
   * @param menuFeature
   */
  changePage(checked: boolean, menuFeature: any) {
    menuFeature.page.isSelect = checked;
    const dataClone = deepClone(this.data);

    if (checked) {
      // Mảng lưu các lựa chọn của page
      this.pageCheck.push(menuFeature.page.name); // Push vào mảng khi page được select
      this.handleUpdateSelection(dataClone, checked, ELevelFeature.PAGE);
    } else {
      // Filter khỏi mảng khi page unselect
      this.pageCheck = this.pageCheck.filter((item) => item !== menuFeature.page?.name);
      if (!this.pageCheck.length) {
        // Khi unselect tất cả page, unselect tất cả phần còn lại
        dataClone.forEach((item: any) => {
          if (item.menuFeature?.menu) item.menuFeature.menu.isSelect = false;
          if (item.detailFeature?.action) item.detailFeature.action.isSelect = false;
          if (item.detailFeature?.feature) item.detailFeature.feature.isSelect = false;
        });
      }
    }

    this.data = deepClone(dataClone);
  }

  /**
   * handle select Action
   * @param checked
   * @param detailFeature
   */
  changeAction(checked: boolean, detailFeature: any) {
    detailFeature.action.isSelect = checked;
    const dataClone = deepClone(this.data);

    if (checked) {
      this.actionCheck.push(detailFeature.action.name);
      this.handleUpdateSelection(dataClone, checked, ELevelFeature.ACTION);
    } else {
      this.actionCheck = this.actionCheck.filter((item) => item !== detailFeature.action?.name);
      if (!this.actionCheck.length) {
        // Khi unselect tất cả action, unselect tất cả phần còn lại
        dataClone.forEach((item: any) => {
          if (item.menuFeature?.menu) item.menuFeature.menu.isSelect = false;
          if (item.menuFeature?.page) item.menuFeature.page.isSelect = false;
          if (item.detailFeature?.feature) item.detailFeature.feature.isSelect = false;
        });
      }
    }

    this.data = deepClone(dataClone);
  }

  /**
   * changeFeature
   * @param checked
   * @param detailFeature
   */
  changeFeature(checked: boolean, detailFeature: any) {
    detailFeature.feature.isSelect = checked;
    const dataClone = deepClone(this.data);
    if (checked) {
      this.featureCheck.push(detailFeature.feature?.name);
      this.handleUpdateSelection(dataClone, checked, ELevelFeature.FEATURE);
    } else {
      this.featureCheck = this.featureCheck.filter((item) => item !== detailFeature.feature?.name);
      if (!this.featureCheck.length) {
        // Khi unselect tất cả feature, unselect tất cả phần còn lại
        dataClone.forEach((item: any) => {
          if (item.menuFeature?.menu) item.menuFeature.menu.isSelect = false;
          if (item.menuFeature?.page) item.menuFeature.page.isSelect = false;
          if (item.detailFeature?.action) item.detailFeature.action.isSelect = false;
        });
      }
    }

    this.data = deepClone(dataClone);
  }

  /**
   * deleteGroupName
   * @param permissionGroupName
   */
  deleteGroupName(permissionGroupName: any) {
    const dataClone = deepClone(this.data);
    const index = dataClone.findIndex((d) => d.permissionGroupName === permissionGroupName);

    dataClone.forEach((data, i) => {
      // Skip the first element
      if (i === index) {
        // Nullify the permissionGroupName for the matched item
        data.permissionGroupName = null;
      }

      // Shift the permissionGroupName from the next item
      if (i > index && dataClone[i - 1].permissionGroupName === null) {
        dataClone[i - 1].permissionGroupName = data.permissionGroupName;
        data.permissionGroupName = null;
      }
    });
    this.data = deepClone(dataClone);
  }

  /**
   * transformToUpperCase
   * @param element
   */
  transformToUpperCase(element: any) {
    element.permissionGroupName = element.permissionGroupName?.toUpperCase() || '';
  }

  /**
   * onBlurInput
   * @param event
   * @param element
   */
  onBlur(event: Event, element: any) {
    element.tag = null; // To Disable Focus Event
    this.addCustomer(element);
    (event?.target as HTMLElement).blur();
  }

  /**
   * addCustomer
   * @param element
   */
  addCustomer(element: any) {
    console.log('element', element);
  }

  /**
   * changeGroupName
   * @param event
   * @param element
   * @param tag
   * @param column
   */
  changeGroupName(event: Event, element: any, tag: string, column: IColumnConfig) {
    const valueInput = (event.target as HTMLInputElement).value.toLocaleUpperCase();
    const sameValue = this.data.find((d) => d.permissionGroupName === element.permissionGroupName);
    if (sameValue) {
      element.checked = true;
      element.isChange = true;
    }
    element.permissionGroupName = valueInput;
  }
}
