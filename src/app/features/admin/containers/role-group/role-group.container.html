<div class="role-group-container">
  <div class="header-role-group">
    <div class="left-box">
      <div class="typo-heading-9">{{ 'MES-556' | translate }}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          6 {{ 'MES-559' | translate }}
        </div>
        <div *ngIf="onEditMode" class="text-edit-cls">{{ 'MES-156' | translate }}: {{ getCountDataEdited() }}</div>
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
            'not-accept-save-cls': checkEnableSaveBtn(),
            'filter-mode-cls': isFilter
          }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
    >
    </sha-grid>
  </div>
</div>

<!-- Tên nhóm quyền -->
<ng-template #groupName let-templateInfo="templateInfo" let-element="element" let-tag="tag" let-column="column">
  @if(element.type) {
  <div class="search-box">
    <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
    <input class="input-cls input-style-common typo-body-12" type="text" [placeholder]="'MES-14' | translate" />
  </div>
  } @else if(!element.type && element.tag) {
  <!-- Add nhóm tài khoản -->
  <div class="group-field">
    <a #permissionGroupName class="group-name typo-body-9" [attr.data-tag]="element.tag">
      <input
        matInput
        class="focus-input typo-field-2"
        [(ngModel)]="element.permissionGroupName"
        (ngModelChange)="transformToUpperCase(element)"
        (blur)="onBlur($event,element)"
      />
    </a>
  </div>
  } @else {
  <div class="group-field" [ngClass]="{'permission-chosen': permissionChosen === element.permissionGroupName}">
    <a *ngIf="!onEditMode" class="group-name typo-body-9" (click)="showMenu(element) "
      >{{element.permissionGroupName}}</a
    >
    <a
      *ngIf="!onEditMode || !permissionChosen === element.permissionGroupName"
      class="trash-icon"
      (click)="deleteGroupName(element.permissionGroupName)"
    >
      <mat-icon *ngIf="element.permissionGroupName" svgIcon="icon:trash-icon"></mat-icon>
    </a>
    <input
      *ngIf="onEditMode"
      [style.border]="'none'"
      [style.text-transform]="'uppercase'"
      (change)="changeGroupName($event, element, tag, column)"
      [value]="element.permissionGroupName"
      class="typo-field-2"
    />
  </div>
  }
</ng-template>

<!-- Menu tính năng -->
<ng-template #menu let-templateInfo="templateInfo" let-element="element" let-tag="tag">
  <div class="column-wrapper">
    <!-- Menu -->
    @if(element.type) {
    <div class="search-box-wrap">
      <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
      <input class="input-cls input-style-common typo-body-12" type="text" [placeholder]="'MES-14' | translate" />
    </div>
    } @else if(element.menuFeature.menu) {
    <a
      class="wrapper"
      [ngClass]="{'item-chosen': element.menuFeature.menu.name && menuChosen === element.menuFeature.menu.name}"
      (click)="showPage(element)"
    >
      <mat-checkbox
        *ngIf="element.menuFeature.menu.name"
        [checked]="element.menuFeature.menu.isSelect"
        (change)="changeMenu($event.checked, element.menuFeature)"
      ></mat-checkbox>
      <div class="typo-body-9">{{ element.menuFeature.menu.name }}</div>
    </a>
    } @else {
    <div class="typo-body-9"></div>
    }

    <!-- Page -->
    @if(element.type) {
    <div class="search-box-wrap">
      <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
      <input class="input-cls input-style-common typo-body-12" type="text" [placeholder]="'MES-14' | translate" />
    </div>
    } @else { @if(element.menuFeature.page) {
    <a
      class="wrapper"
      [ngClass]="{'item-chosen': element.menuFeature.page.name && pageChosen === element.menuFeature.page.name}"
      (click)="showAction(element)"
    >
      <mat-checkbox
        (change)="changePage($event.checked, element.menuFeature)"
        [checked]="element.menuFeature.page.isSelect"
        *ngIf="element.menuFeature.page.name"
      ></mat-checkbox>
      <div class="typo-body-9">{{ element.menuFeature.page.name }}</div>
    </a>
    } @else {
    <div></div>
    } }
  </div>
</ng-template>

<!-- Chi tiết tính năng -->
<ng-template #detail let-templateInfo="templateInfo" let-element="element" let-tag="tag">
  <div class="column-wrapper">
    <!-- Action -->
    @if(element.type) {
    <div class="search-box-wrap">
      <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
      <input class="input-cls input-style-common typo-body-12" type="text" [placeholder]="'MES-14' | translate" />
    </div>
    } @else { @if(element.detailFeature) {
    <a
      class="wrapper"
      [ngClass]="{'item-chosen': element.detailFeature.action.name && actionChosen === element.detailFeature.action.name}"
      (click)="showFeature(element)"
    >
      <mat-checkbox
        (change)="changeAction($event.checked, element.detailFeature)"
        [checked]="element.detailFeature.action.isSelect"
        *ngIf="element.detailFeature.action.name"
      ></mat-checkbox>
      <div class="typo-body-9">{{ element.detailFeature.action.name }}</div>
    </a>

    } @else {
    <div class="wrapper">
      <div class="search-box-wrap">
        <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
        <input class="input-cls input-style-common typo-body-12" type="text" [placeholder]="'MES-14' | translate" />
      </div>
    </div>
    } }

    <!-- Feature -->
    @if(element.type) {
    <div class="search-box-wrap">
      <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
      <input class="input-cls input-style-common typo-body-12" type="text" [placeholder]="'MES-14' | translate" />
    </div>
    } @else {
    <!-- Not Search -->
    @if(element.detailFeature) {
    <div class="wrapper">
      <mat-checkbox
        (change)="changeFeature($event.checked, element.detailFeature)"
        [checked]="element.detailFeature.feature.isSelect"
        *ngIf="element.detailFeature.feature.name"
      >
        <div class="typo-body-9">{{ element.detailFeature.feature.name }}</div>
      </mat-checkbox>
    </div>

    } @else {
    <div class="wrapper">
      <div class="search-box-wrap">
        <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
        <input class="input-cls input-style-common typo-body-12" type="text" [placeholder]="'MES-14' | translate" />
      </div>
    </div>
    } }
  </div>
</ng-template>
