.typo-text-table {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #33343e;
}

.role-group-container {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f1f2f6;
  display: flex;
  flex-direction: column;

  .header-role-group {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 12px;
    white-space: nowrap;
    text-wrap: nowrap;

    .left-box {
      display: flex;
      align-items: center;
      gap: 24px;

      .number-info-cls {
        display: flex;
        align-items: center;
        gap: 12px;

        .box-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 16px;
          background-color: #f8fafd;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
          color: #808080;
          white-space: nowrap;
          text-wrap: nowrap;
        }
        .text-edit-cls {
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          white-space: nowrap;
          text-wrap: nowrap;
          color: var(--color--brand--500);
        }
      }
    }

    .right-box {
      flex: 1.4;
      display: flex;
      justify-content: flex-end;

      app-action-btn {
        ::ng-deep {
          #box-id--save {
            color: var(--color--brand--500);
            border: 1px solid var(--color--brand--500);
          }

          #box-id--cancel {
            color: var(--color--danger--600);
            border: 1px solid var(--color--danger--600);
          }
        }

        &.not-accept-save-cls {
          ::ng-deep {
            #box-id--save {
              opacity: 0.5;
            }
          }
        }
        &.filter-mode-cls {
          ::ng-deep {
            #box-id--filter {
              color: var(--color--brand--500);
              border: 1px solid var(--color--brand--500);

              .icons-cls {
                path {
                  stroke: var(--color--brand--500);
                }
              }
            }
          }
        }
      }
    }
  }

  .table-view-container {
    height: 100%;

    .table-custom-cls {
      ::ng-deep {
        .table-container {
          position: relative;
        }

        tr[data-row='0']:has(td.col-action):has(img[alt='more-action']) {
          img[alt='more-action'] {
            display: none;
          }
        }

        td.mat-column-permissionGroupName:has(.search-box) {
          padding: 8px 10px 8px 0 !important;
        }

        td.mat-column-permissionGroupName:has(.permission-chosen) {
          background: var(--color--background--selected);
          color: var(--color--brand--500);
        }

        td:has(.content-center):has(.search-box) {
          padding: 8px 10px;
        }

        td:has(.content-center):has(.group-name) {
          .group-name {
            display: flex;
            align-items: center;
            width: 100%;
          }
        }

        th#menuFeature,
        th#detailFeature {
          .box-show {
            input {
              text-align: center !important;
            }
          }
        }

        .group-field {
          width: 100%;
          display: flex;
          justify-content: space-between;
          padding: 8px;

          .trash-icon {
            display: flex;
            align-items: center;
          }

          mat-icon {
            width: 20px !important;
            height: 20px !important;
          }
        }

        td.mat-column-menuFeature,
        td.mat-column-detailFeature {
          padding: 0px !important;

          .wrapper {
            display: flex;
            align-items: center;
            padding: 8px;
            height: 36px;
          }

          .search-box-wrap {
            padding: 8px 10px;
          }

          .wrapper,
          .search-box-wrap {
            border-right: 1px solid var(--color--neutral--50);
          }
        }
      }

      .column-wrapper {
        display: flex;
        width: 100%;
      }

      .wrapper {
        display: flex;
        align-items: center;
        min-width: 50%;
      }

      .search-box,
      .search-box-wrap {
        display: flex;
        align-items: center;
        position: relative;
        width: 100%;

        input {
          width: 100%;
          height: 40px;
          padding: 10px 16px 10px 40px;
          border-radius: 8px;
          border: 1px solid var(--color--other--divider);
        }
      }

      .search-box > .search-icon {
        left: 16px;
      }

      .search-box-wrap > .search-icon {
        left: 26px;
      }

      .search-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }

      .focus-input {
        &:focus {
          border: none;
          text-transform: uppercase;
        }
      }

      .item-chosen {
        background: var(--color--background--selected);
        color: var(--color--brand--500);
      }
    }
  }
}
