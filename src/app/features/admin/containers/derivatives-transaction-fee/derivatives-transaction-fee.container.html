<div class="base-transaction-fee-container">
  <div class="header-base-transaction-fee">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-331' | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          4 nhóm
        </div>

        <div *ngIf="onEditMode" class="text-edit-cls">{{'MES-156' | translate}}: {{getCountDataEdited()}}</div>
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls' : checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      (clickMoreAction)="handleMoreAction($event)"
      class="table-custom-cls"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
    >
    </sha-grid>
  </div>
</div>
<ng-template #templateData let-dataValue="templateInfo" let-element="element" let-tag="tag" let-column="column">
  @if ( dataValue?.type === ETypeValueInput.PERCENT || dataValue?.type === ETypeValueInput.NUMBER ) {
  <app-input-number-and-dropdown
    [unit]="CONVERT_UNIT_CONTRACT_TO_LABLE[dataValue.type]"
    [element]="element"
    [tag]="tag"
    [isEdit]="onEditMode"
    [value]="dataValue.value"
    [column]="column"
    [typeValue]="dataValue?.type === ETypeValueInput.NUMBER  ?  column.componentConfig.number.type : column.componentConfig.percent.type  "
    [option]="column.componentConfig.number?.option ?? column.componentConfig.percent?.option"
    (dateChangeEvent)="updateBaseTransactionFee($event)"
    (unFocusElement)="elementSelectFunc($event)"
  ></app-input-number-and-dropdown>

  } @else {
  <div>{{dataValue?.value}}</div>
  }
</ng-template>
