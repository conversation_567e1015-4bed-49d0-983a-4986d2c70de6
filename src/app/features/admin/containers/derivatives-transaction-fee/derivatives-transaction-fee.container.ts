import { AfterViewInit, ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild, viewChild } from '@angular/core';
import { DialogService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { ETypeValueInput } from 'src/app/shared/components/input-custom-for-table/input-proportion-dropdown/input-proportion';
import { CONVERT_TITLE_TO_CLASS, CONVERT_UNIT_CONTRACT_TO_LABLE } from '../../constants/admin';
import { fakeData } from './fakedata';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { Observable } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { StorageService } from 'src/app/core/services/storage.service';

/**
 * DerivativesTransactionFeeContainer
 * <PERSON>u<PERSON>n lý hợp đồng phái sinh
 */
@Component({
  selector: 'app-derivatives-transaction-fee-container',
  templateUrl: './derivatives-transaction-fee.container.html',
  styleUrl: './derivatives-transaction-fee.container.scss',
})
export class DerivativesTransactionFeeContainer extends BaseTableComponent<any> implements OnInit {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('templateData', { static: true }) templateData: TemplateRef<any> | null = null;

  fakeData = fakeData;

  ETypeValueInput = ETypeValueInput;

  CONVERT_TITLE_TO_CLASS = CONVERT_TITLE_TO_CLASS;
  CONVERT_UNIT_CONTRACT_TO_LABLE = CONVERT_UNIT_CONTRACT_TO_LABLE;

  valueOptions = [
    {
      label: '% /hợp đồng',
      value: ETypeValueInput.PERCENT,
    },
    {
      label: 'VND /hợp đồng',
      value: ETypeValueInput.NUMBER,
    },
  ];
  /**
   * Constructor
   */
  constructor() {
    super();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.export]);
  }

  /**
   * NgOnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.initialData = this.data;
    this.columnConfigs = [
      {
        name: 'Khoảng NAV',
        minWidth: 156,
        width: 156,
        tag: 'name',
        isDisplay: true,
        resizable: true,
        pinned: 'left',
      },
      {
        name: 'STANDARD',
        minWidth: 156,
        width: 156,
        tag: 'standard',
        isDisplay: true,
        resizable: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        isEdit: true,
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            type: ETypeValueInput.PERCENT,
            option: this.valueOptions,
          },
          number: {
            type: ETypeValueInput.NUMBER,
            option: this.valueOptions,
          },
        },
        cellTemplate: this.templateData,
      },
      {
        name: 'VIP',
        minWidth: 156,
        width: 156,
        tag: 'vip',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            type: ETypeValueInput.PERCENT,
            option: this.valueOptions,
          },
          number: {
            type: ETypeValueInput.NUMBER,
            option: this.valueOptions,
          },
        },
        cellTemplate: this.templateData,
      },
      {
        name: 'VVIP',
        minWidth: 156,
        width: 156,
        tag: 'vvip',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            type: ETypeValueInput.PERCENT,
            option: this.valueOptions,
          },
          number: {
            type: ETypeValueInput.NUMBER,
            option: this.valueOptions,
          },
        },
        cellTemplate: this.templateData,
      },
      {
        name: 'SVIP',
        minWidth: 156,
        width: 156,
        tag: 'svip',
        isDisplay: true,
        resizable: true,
        isEdit: true,
        align: 'center',
        panelClass: 'interest-rate-cls',
        displayValueFn: (v) => {
          if (!v) return '';
          return v;
        },
        componentConfig: {
          percent: {
            type: ETypeValueInput.PERCENT,
            option: this.valueOptions,
          },
          number: {
            type: ETypeValueInput.NUMBER,
            option: this.valueOptions,
          },
        },
        cellTemplate: this.templateData,
      },
    ];
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   *  Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'edit':
        this.toggleEditMode();
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * HandleMoreAction
   * @param data
   */
  handleMoreAction(data: any) {
    console.log(data);
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([ActionButton.edit, ActionButton.save, ActionButton.cancel, ActionButton.export]);
  }

  /**
   * updateBaseTransactionFee
   * @param data data
   */
  updateBaseTransactionFee(data: any) {
    const { date, tag, element } = data;

    const matchElement = this.data.find((e) => e[tag] === element[tag]);
    if (matchElement) {
      if (typeof date === 'object' && date !== null) {
        // Sửa kiểu và giá trị
        matchElement[tag].type = date.type;
        matchElement[tag].value = date.value;
      } else {
        // Sửa giá trị
        matchElement[tag].value = date;
      }
    }
  }
}
