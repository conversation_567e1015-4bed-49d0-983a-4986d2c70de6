import { AfterViewInit, ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { CELL_TYPE } from '@shared/models';
import { Observable, takeUntil } from 'rxjs';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import { DestroyService, DialogService } from 'src/app/core/services';
import { CONVERT_GENDER_TO_LABLE, EGender } from 'src/app/features/customers/constants/customers';
import { ActionButton, IActionBtn } from 'src/app/shared/components/action-btn/action-btn.component';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { LIST_OF_NATIONALITY } from 'src/app/shared/constants/nationality';
import { deepClone } from 'src/app/shared/utils/utils';
import { CONVERT_ROLE_TO_LABEL, ERole } from '../../constants/admin';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { AccountOptionsComponent } from '../../components/account-options/account-options.component';
import { ActivatedRoute } from '@angular/router';
import { StorageService } from 'src/app/core/services/storage.service';

/**
 * Phân quyền tài khoản
 */
@Component({
  selector: 'app-account-authorization',
  templateUrl: './account-authorization.container.html',
  styleUrl: './account-authorization.container.scss',
})
export class AccountAuthorizationContainer extends BaseTableComponent<any> implements OnInit, ComponentCanDeactivate {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('calendarPicker', { static: true }) calendarPicker: TemplateRef<any> | null = null;

  @ViewChild('numberPhone', { static: true }) numberPhoneTemplate: TemplateRef<any> | null = null;

  isEdit = false;

  LIST_OF_NATIONALITY = LIST_OF_NATIONALITY;

  fakeData: any = [
    {
      employeeCode: 'duongnc',
      fullName: 'Nguyễn Cảnh Dương',
      accountName: 'duongnc',
      roles: 'back admin',
      room: 'Phòng 05',
      position: 'Trưởng phòng',
      officeAddress: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
      email: '<EMAIL>',
      areaCode: '+84',
      phoneNumber: *********,
      gender: 0,
      dateOfBirth: '28/08/2024',
      nationality: 'Việt Nam',
      address: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
    },
    {
      employeeCode: 'duongnc',
      fullName: 'Nguyễn Cảnh Dương',
      accountName: 'duongnc',
      roles: 'broker admin',
      room: 'Phòng 05',
      position: 'Trưởng phòng',
      officeAddress: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
      email: '<EMAIL>',
      areaCode: '+84',
      phoneNumber: *********,
      gender: 0,
      dateOfBirth: '28/08/2024',
      nationality: 'Việt Nam',
      address: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
    },
    {
      employeeCode: 'duongnc',
      fullName: 'Nguyễn Cảnh Dương',
      accountName: 'duongnc',
      roles: 'back',
      room: 'Phòng 05',
      position: 'Trưởng phòng',
      officeAddress: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
      email: '<EMAIL>',
      areaCode: '+84',
      phoneNumber: *********,
      gender: 0,
      dateOfBirth: '28/08/2024',
      nationality: 'Việt Nam',
      address: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
    },
    {
      employeeCode: 'duongnc',
      fullName: 'Nguyễn Cảnh Dương',
      accountName: 'duongnc',
      roles: 'broker',
      room: 'Phòng 05',
      position: 'Trưởng phòng',
      officeAddress: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
      email: '<EMAIL>',
      areaCode: '+84',
      phoneNumber: *********,
      gender: 0,
      dateOfBirth: '28/08/2024',
      nationality: 'Việt Nam',
      address: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
    },
    {
      employeeCode: 'duongnc',
      fullName: 'Nguyễn Cảnh Dương',
      accountName: 'duongnc',
      roles: 'remiser',
      room: 'Phòng 05',
      position: 'Trưởng phòng',
      officeAddress: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
      email: '<EMAIL>',
      areaCode: '+84',
      phoneNumber: *********,
      gender: 0,
      dateOfBirth: '28/08/2024',
      nationality: 'Việt Nam',
      address: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
    },
    {
      employeeCode: 'duongnc',
      fullName: 'Nguyễn Cảnh Dương',
      accountName: 'duongnc',
      roles: 'back admin',
      room: 'Phòng 05',
      position: 'Trưởng phòng',
      officeAddress: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
      email: '<EMAIL>',
      areaCode: '+84',
      phoneNumber: *********,
      gender: 0,
      dateOfBirth: '28/08/2024',
      nationality: 'Việt Nam',
      address: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
    },
    {
      employeeCode: 'duongnc',
      fullName: 'Nguyễn Cảnh Dương',
      accountName: 'duongnc',
      roles: 'back admin',
      room: 'Phòng 05',
      position: 'Trưởng phòng',
      officeAddress: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
      email: '<EMAIL>',
      areaCode: '+84',
      phoneNumber: *********,
      gender: 0,
      dateOfBirth: '28/08/2024',
      nationality: 'Việt Nam',
      address: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
    },
    {
      employeeCode: 'duongnc',
      fullName: 'Nguyễn Cảnh Dương',
      accountName: 'duongnc',
      roles: 'super admin',
      room: 'Phòng 05',
      position: 'Trưởng phòng',
      officeAddress: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
      email: '<EMAIL>',
      areaCode: '+84',
      phoneNumber: *********,
      gender: 0,
      dateOfBirth: '28/08/2024',
      nationality: 'Việt Nam',
      address: '41 Ngô Quyền, Phan Chu Trinh, Hoàn Kiếm, Hà Nội',
    },
  ];

  additionalButton: IActionBtn = {
    label: 'MES-557',
    icons: 'icon:add-icon',
    name: 'add-icon',
    isDisplayed: false,
    tag: ActionButton.add,
  };

  /**
   * Constructor
   * @param _destroy DestroyService
   * @param popoverService PopoverService
   */
  constructor(private _destroy: DestroyService, private popoverService: PopoverService) {
    super();
    this.actionButtons.unshift(this.additionalButton);
    this.toggleButtonByTags([
      ActionButton.add,
      ActionButton.edit,
      ActionButton.export,
      ActionButton.display,
      ActionButton.filter,
    ]);
  }

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    const dataClone = deepClone(this.fakeData);
    this.data = dataClone.map((d: any) => {
      d.areaCode = d.areaCode + ' ' + d.phoneNumber.toString();
      return d;
    });
    this.initialData = deepClone(this.data);
    this.fakeData = dataClone;
    const template = this.calendarPicker;
    const numberTemplate = this.numberPhoneTemplate;
    this.columnConfigs = [
      {
        name: 'Mã nhân viên',
        minWidth: 30,
        width: 100,
        tag: 'employeeCode',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        isEdit: false,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Họ tên',
        minWidth: 30,
        width: 180,
        tag: 'fullName',
        isDisplay: true,
        isEdit: false,
      },
      {
        name: 'Tên tài khoản',
        minWidth: 30,
        width: 100,
        tag: 'accountName',
        isDisplay: true,
        isEdit: false,
        resizable: true,
      },
      {
        name: 'Nhóm quyền',
        minWidth: 30,
        width: 160,
        tag: 'roles',
        displayValueFn: (value) => CONVERT_ROLE_TO_LABEL[value],
        dynamicClass: (value) => {
          return 'admin-role';
        },
        isDisplay: true,
        isEdit: true,
        resizable: true,
        align: 'center',
        typeValue: CELL_TYPE.DROPDOWN,
        componentConfig: {
          multiple: false,
          isSearch: false,
          searchKey: 'value',
          displayOptionFn: (v: any) => v.name,
          options: [
            {
              name: CONVERT_ROLE_TO_LABEL[ERole.BACKADMIN],
              value: ERole.BACKADMIN,
            },
            {
              name: CONVERT_ROLE_TO_LABEL[ERole.BROKERADMIN],
              value: ERole.BROKERADMIN,
            },
            {
              name: CONVERT_ROLE_TO_LABEL[ERole.BACK],
              value: ERole.BACK,
            },
            {
              name: CONVERT_ROLE_TO_LABEL[ERole.BROKER],
              value: ERole.BROKER,
            },
            {
              name: CONVERT_ROLE_TO_LABEL[ERole.REMISER],
              value: ERole.REMISER,
            },
          ],
        },
      },
      {
        name: 'Phòng',
        minWidth: 30,
        width: 100,
        tag: 'room',
        type: 'text',
        isDisplay: true,
        typeValue: CELL_TYPE.TEXT,
        isEdit: true,
        resizable: true,
      },
      {
        name: 'Chức vụ',
        minWidth: 30,
        width: 120,
        tag: 'position',
        type: 'text',
        typeValue: CELL_TYPE.TEXT,
        isDisplay: true,
        isEdit: true,
        resizable: true,
      },
      {
        name: 'Văn phòng',
        minWidth: 30,
        width: 360,
        tag: 'officeAddress',
        type: 'text',
        url: './assets/icons/location.svg',
        panelClass: 'location-cls',
        isDisplay: true,
        isEdit: true,
        resizable: true,
      },
      {
        name: 'Email',
        minWidth: 30,
        width: 240,
        tag: 'email',
        url: './assets/icons/sms.svg',
        panelClass: 'location-cls',
        typeValue: CELL_TYPE.TEXT,
        type: 'text',
        isDisplay: true,
        isEdit: true,
        isEmail: true,
        resizable: true,
      },
      {
        name: 'Số điện thoại',
        minWidth: 30,
        width: 170,
        tag: 'areaCode',
        typeValue: CELL_TYPE.TEXT,
        type: 'text',
        isDisplay: true,
        isEdit: true,
        cellTemplate: numberTemplate,
      },
      {
        name: 'Giới tính',
        minWidth: 30,
        width: 100,
        tag: 'gender',
        displayValueFn: (value) => CONVERT_GENDER_TO_LABLE[value],
        typeValue: CELL_TYPE.DROPDOWN,
        componentConfig: {
          displayOptionFn: (v: any) => {
            return CONVERT_GENDER_TO_LABLE[v.value];
          },
          multiple: false,
          searchKey: 'value',
          isSearch: false,
          options: [
            {
              name: 'Nam',
              value: EGender.MALE,
            },
            {
              name: 'Nữ',
              value: EGender.FEMALE,
            },
          ],
        },
        isDisplay: true,
        isEdit: true,
        resizable: true,
      },
      {
        name: 'Ngày sinh',
        minWidth: 30,
        width: 136,
        tag: 'dateOfBirth',
        typeValue: CELL_TYPE.CALENDAR,
        cellTemplate: template,
        isDisplay: true,
        isEdit: true,
        resizable: true,
        componentConfig: {
          isValidBirthday: true,
        },
      },
      {
        name: 'Quốc tịch',
        minWidth: 30,
        width: 120,
        tag: 'nationality',
        typeValue: CELL_TYPE.NATIONALITY,
        isDisplay: true,
        isEdit: true,
        componentConfig: {
          displayOptionFn: (v: any) => v.name,
          multiple: false,
          searchKey: 'name',
          options: this.LIST_OF_NATIONALITY,
          isShowLogo: false,
          isShowAreaCode: false,
        },
        resizable: true,
      },
      {
        name: 'Địa chỉ',
        minWidth: 30,
        width: 360,
        tag: 'address',
        type: 'text',
        url: './assets/icons/location.svg',
        isDisplay: true,
        panelClass: 'location-cls',
        isEdit: true,
        resizable: true,
      },
    ];
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'add':
        this.createAccount();
        break;
      case 'filter':
        break;
      case 'edit':
        this.toggleEditMode();
        break;
      case 'cancel':
        this.restoreData();
        break;
      case 'save':
        this.saveDate();
        break;
      default:
        break;
    }
  }

  /**
   * handleMoreActionInTable
   * @param event Event
   * @param {HTMLElement} event.html
   * @param {any} event.element
   */
  handleMoreAction(event: { html: HTMLElement; element: any }) {
    const { html, element } = event;
    const originElement = html;
    const ref = this.popoverService.open({
      origin: originElement,
      content: AccountOptionsComponent,
      width: 184,
      hasBackdrop: true,
      position: 3,
      componentConfig: { element },
    });

    ref.afterClosed$.pipe(takeUntil(this._destroy)).subscribe((res) => {
      if (!res.data) return;
      const dataClone = deepClone(this.data).filter((d) => d.id !== res.data);
      this.data = deepClone(dataClone);
    });
  }

  /**
   * Create Customer
   */
  createAccount() {
    console.log('Create Account');
  }

  /**
   * Toggle edit mode
   */
  override toggleEditMode() {
    super.toggleEditMode();
    this.toggleButtonByTags([
      ActionButton.add,
      ActionButton.edit,
      ActionButton.save,
      ActionButton.cancel,
      ActionButton.export,
    ]);
  }
}
