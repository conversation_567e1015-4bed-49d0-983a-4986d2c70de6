<div class="account-authorization-container">
  <div class="header-account-authorization">
    <div class="left-box">
      <div class="typo-heading-9">{{ 'MES-556' | translate }}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          100 {{ 'MES-15' | translate }}
        </div>
        <div *ngIf="onEditMode" class="text-edit-cls">{{ 'MES-156' | translate }}: {{ getCountDataEdited() }}</div>
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
          'not-accept-save-cls': checkEnableSaveBtn(),
          'filter-mode-cls': isFilter
        }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
      (clickMoreAction)="handleMoreAction($event)"
    >
    </sha-grid>
  </div>
</div>

<ng-template #numberPhone let-templateInfo="templateInfo" let-element="element" let-tag="tag">
  <app-phone-number-table-component
    [data]="templateInfo"
    [isEdit]="onEditMode"
    [element]="element"
    (unFocusElement)="elementSelectFunc($event)"
    [tag]="tag"
    (dateChangeEvent)="changeDateEvent($event)"
  ></app-phone-number-table-component>
</ng-template>

<ng-template #calendarPicker let-templateInfo="templateInfo" let-element="element" let-tag="tag" let-column="column">
  <div class="calendar-template-info-cls">
    <app-date-picker-component
      (dateChangeEvent)="changeDateEvent($event)"
      [isEdit]="onEditMode"
      [startDate]="templateInfo"
      [element]="element"
      [tag]="tag"
      [isExpiredDate]="column.componentConfig && column.componentConfig?.isExpiredDate ? true : false"
      [isValidateDate]="column.componentConfig && column.componentConfig?.isValidateDate ? true : false"
      [isValidBirthday]="column.componentConfig && column.componentConfig?.isValidBirthday ? true : false"
      (unFocusElement)="elementSelectFunc($event)"
      #calendarCustom
    ></app-date-picker-component>
  </div>
</ng-template>
