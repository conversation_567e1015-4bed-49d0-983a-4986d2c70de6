import { Component } from '@angular/core';
import { FlatTreeControl } from '@angular/cdk/tree';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { organizationData } from './data';
import { CONVERT_ROLE_ORGANIZATION_TO_ICON } from '../../constants/admin';
import { deepClone } from 'src/app/shared/utils/utils';
import {
  CONVERT_TYPE_ACCOUNT_TO_CLASS,
  CONVERT_TYPE_ACCOUNT_TO_LABLE,
} from 'src/app/features/customers/constants/customers';

interface IOrganizationData {
  name: string;
  role?: string;
  children?: IOrganizationData[];
  customer?: any[];
}

interface dataFlatNode {
  expandable: boolean;
  name: string;
  role?: string;
  level: number;
  children?: IOrganizationData[];
  customer?: any[];
}

@Component({
  selector: 'app-departments',
  templateUrl: './departments.container.html',
  styleUrl: './departments.container.scss',
})
export class DepartmentsContainer {
  tags = ['100 phòng MG', '100 môi giới', '100 CTV', '100 Khách hàng'];

  CONVERT_ROLE_ORGANIZATION_TO_ICON = CONVERT_ROLE_ORGANIZATION_TO_ICON;
  displayedColumns: string[] = ['name'];

  private transformer = (node: IOrganizationData, level: number): dataFlatNode => {
    return {
      expandable: !!node.children && node.children.length > 0,
      name: node.name,
      role: node.role,
      level: level,
      children: node.children,
      customer: node.customer,
    };
  };

  treeControl = new FlatTreeControl<dataFlatNode>(
    (node) => node.level,
    (node) => node.expandable
  );

  treeFlattener = new MatTreeFlattener(
    this.transformer,
    (node) => node.level,
    (node) => node.expandable,
    (node) => node.children
  );

  organizationSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);
  customerSource = [];

  readonly customerColumnsWidth: string[] = ['5%', '22%', '25%', '17%', '31%'];
  readonly customerDisplayColumns: string[] = ['MES-569', 'MES-66', 'MES-184', 'MES-25', 'MES-570'];
  readonly customerColumnsMinWidth: string[] = ['30px', '130px', '100px', '100px', '150px'];

  CONVERT_TYPE_ACCOUNT_TO_LABLE = CONVERT_TYPE_ACCOUNT_TO_LABLE;
  CONVERT_TYPE_ACCOUNT_TO_CLASS = CONVERT_TYPE_ACCOUNT_TO_CLASS;

  constructor() {
    this.organizationSource.data = organizationData;
    this.treeControl.expandAll();
  }

  hasChild = (_: number, node: dataFlatNode) => node.expandable;

  showCustomerData(customer: []) {
    this.customerSource = deepClone(customer);
  }
}
