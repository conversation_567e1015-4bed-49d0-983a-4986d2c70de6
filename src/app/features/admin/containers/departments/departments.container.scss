:host {
  width: 100%;
  height: 100%;
}

.department-info-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 8px;
  border: 1px solid var(--color--other--divider);
}

.header {
  display: flex;
  justify-content: space-between;
  padding: 12px;

  .left-header {
    float: left;
    display: flex;
    align-items: center;
    gap: 24px;

    .number-info-cls {
      display: flex;
      align-items: center;
      gap: 24px;

      .box-info {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 16px;
        background-color: #f8fafd;
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
        color: #808080;
        white-space: nowrap;
        text-wrap: nowrap;
      }
    }
  }

  .right-header {
    display: flex;
    align-items: center;
    float: right;
    gap: 16px;

    .btn-box {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 10px;
      border-radius: 8px;
      border: 1px solid var(--color--other--divider);
      background: var(--color--neutral--white);
      cursor: pointer;

      mat-icon {
        width: 16px;
        height: 16px;
      }
    }
  }
}

.table-wrapper {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}

.organinze-table-wrapper {
  width: 37%;
  padding: 0 4px 0 0;
  border-right: 1px solid var(--color--neutral--400);

  .table-container {
    height: 100%;
    overflow: auto;
    border: 1px solid var(--color--other--divider);
  }

  th {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 36px;
    padding: 8px 4px 8px 8px;
    color: var(--color--text-vibrant--secondary);
    background-color: var(--color--background--1);
    border-bottom: 1px solid var(--color--other--divider);
    white-space: nowrap;
  }

  td {
    padding: 0px !important;
    border-bottom-color: var(--color--other--divider);
    white-space: nowrap;

    .row {
      display: flex;
      align-items: center;
      height: 36px;

      .arrow,
      .role {
        cursor: pointer;
      }

      .arrow,
      .role-img {
        padding: 10px 4px;
        height: 100%;
        display: flex;
        align-items: center;
      }

      .role {
        padding: 8px 0;
        width: 100%;
      }
    }
  }
}

.infomation-customer-table {
  width: 63%;
  padding: 0 0 0 4px;
  height: 100%;
  overflow: auto;
  position: relative;

  .customer-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border: 1px solid var(--color--other--divider);
    height: 38px;
    background-color: var(--color--background--1);
    color: var(--color--text-vibrant--secondary);
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .table-container {
    height: 100%;
  }

  th,
  td {
    border: 1px solid var(--color--other--divider);
    white-space: nowrap;
    padding: 0 !important;
  }

  th {
    background-color: var(--color--background--1);
    height: 36px;
  }

  td {
    white-space: nowrap;

    &.item-center {
      display: flex;
      justify-content: center;
    }
  }

  tr {
    &.sticky {
      z-index: 100;
      position: sticky;
      top: 36px;
    }

    &.table-tr-custom {
      height: 36px !important;
    }
  }

  .pd-8 {
    padding: 8px;
  }

  .account-type {
    display: flex;
    justify-content: center;
    border-radius: 16px;
    padding: 2px 8px;
    width: fit-content;

    &.individual {
      background: var(--color--accents--yellow-dark);
    }

    &.organization {
      background: var(--color--accents--green-dark);
    }
  }
}

::-webkit-scrollbar {
  width: 3px;
}
