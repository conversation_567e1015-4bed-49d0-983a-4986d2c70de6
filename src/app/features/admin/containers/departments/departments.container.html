<div class="department-info-container">
  <div class="header">
    <div class="left-header">
      <div class="typo-heading-9">{{"MES-481" | translate}}</div>
      <div class="number-info-cls">
        <div class="box-info typo-body-11" *ngFor="let tag of tags">
          <img src="./assets/icons/table_sum.svg" alt="table_sum" />
          {{tag}}
        </div>
      </div>
    </div>

    <div class="right-header">
      <!-- Thêm phòng MG -->
      <div class="btn-box">
        <div class="typo-button-3">{{'MES-568' | translate}}</div>
        <mat-icon svgIcon="icon:add-icon"></mat-icon>
      </div>

      <!-- Sửa -->
      <div class="btn-box">
        <div class="typo-button-3">{{ 'MES-16' | translate }}</div>
        <mat-icon svgIcon="icon:edit-icon"></mat-icon>
      </div>

      <!-- Xuất -->
      <div class="btn-box">
        <div class="typo-button-3">{{ "MES-17" | translate }}</div>
        <mat-icon svgIcon="icon:export-icon"></mat-icon>
      </div>

      <!-- Lọc -->
      <div class="btn-box">
        <div class="typo-button-3">{{ "MES-19" | translate }}</div>
        <mat-icon svgIcon="icon:filter-icon"></mat-icon>
      </div>
    </div>
  </div>

  <!-- Table -->
  <div class="table-wrapper">
    <!-- Tổ chức -->
    <div class="organinze-table-wrapper">
      <div class="table-container">
        <table mat-table [dataSource]="organizationSource">
          <ng-container matColumnDef="name">
            <th class="organize" mat-header-cell *matHeaderCellDef>
              <div class="typo-body-9">{{ 'MES-27' | translate }}</div>
            </th>
            <td class="organize" mat-cell *matCellDef="let data">
              <div
                class="row typo-body-9"
                [style.marginLeft.px]="data.level * 24"
                (click)="showCustomerData(data.customer)"
              >
                <div class="arrow" *ngIf="data.children.length" (click)="treeControl.toggle(data)">
                  <img
                    width="16px"
                    height="16px"
                    [src]="treeControl.isExpanded(data) ? 'assets/icons/arrow-down.svg' : 'assets/icons/arrow-right.svg' "
                    [alt]="treeControl.isExpanded(data) ? 'arrow-down.svg' : 'arrow-right'"
                  />
                </div>

                <div class="role-img">
                  <img [src]="CONVERT_ROLE_ORGANIZATION_TO_ICON[data.role]" [alt]="data.role" />
                </div>

                <div class="role"><span>{{data.name}}</span></div>
              </div>
            </td>
          </ng-container>

          <tr [style.height]="'38px'" mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr [style.height]="'38px'" mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </div>

    <!-- Danh sách khách hàng -->
    <div class="infomation-customer-table">
      <div class="customer-header typo-body-9">{{ "MES-571" | translate }}</div>
      <div class="table-container">
        <table mat-table [dataSource]="customerSource">
          <!-- STT -->
          <ng-container [matColumnDef]="customerDisplayColumns[0]">
            <th
              mat-header-cell
              *matHeaderCellDef
              [ngStyle]="{'width': customerColumnsWidth[0], 'text-align': 'center'}"
            >
              <div class="typo-body-12 pd-8">{{ customerDisplayColumns[0] | translate }}</div>
            </th>
            <td
              mat-cell
              *matCellDef="let element; let i = index"
              [ngStyle]="{'min-width': customerColumnsMinWidth[0], 'text-align': 'center'}"
            >
              <div class="typo-body-12 pd-8">{{ i + 1 }}</div>
            </td>
          </ng-container>

          <!-- Số tài khoản -->
          <ng-container [matColumnDef]="customerDisplayColumns[1]">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'width': customerColumnsWidth[1], 'text-align': 'start'}">
              <div class="typo-body-12 pd-8">{{ customerDisplayColumns[1] | translate}}</div>
            </th>

            <td [style.minWidth]="customerColumnsMinWidth[1]" mat-cell *matCellDef="let element">
              <div class="typo-body-12 pd-8">{{element.accountNumber ?? '-'}}</div>
            </td>
          </ng-container>

          <!-- Tên khách hàng -->
          <ng-container [matColumnDef]="customerDisplayColumns[2]">
            <th mat-cell-header *matHeaderCellDef [ngStyle]="{'width': customerColumnsWidth[2], 'text-align': 'start'}">
              <div class="typo-body-12 pd-8">{{customerDisplayColumns[2] | translate}}</div>
            </th>

            <td [style.minWidth]="customerColumnsMinWidth[2]" mat-cell *matCellDef="let element">
              <div class="typo-body-12 pd-8">{{ element.customerName ?? '-' }}</div>
            </td>
          </ng-container>

          <!-- Loại tài khoản -->
          <ng-container [matColumnDef]="customerDisplayColumns[3]">
            <th
              mat-cell-header
              *matHeaderCellDef
              [ngStyle]="{'width': customerColumnsWidth[3], 'text-align': 'center'}"
            >
              <div class="typo-body-12 pd-8">{{customerDisplayColumns[3] | translate}}</div>
            </th>

            <td class="item-center" [style.minWidth]="customerColumnsMinWidth[3]" mat-cell *matCellDef="let element">
              <div [style.padding]="'6px 8px'">
                <div class="account-type typo-body-12" [ngClass]="CONVERT_TYPE_ACCOUNT_TO_CLASS[element.accountType]">
                  {{ CONVERT_TYPE_ACCOUNT_TO_LABLE[element.accountType] }}
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Người phụ trách -->
          <ng-container [matColumnDef]="customerDisplayColumns[4]">
            <th
              mat-cell-header
              *matHeaderCellDef
              [ngStyle]="{'width': customerColumnsWidth[4], 'text-align': 'center'}"
            >
              <div class="typo-body-12 pd-8">{{customerDisplayColumns[4] | translate}}</div>
            </th>

            <td [style.minWidth]="customerColumnsMinWidth[4]" mat-cell *matCellDef="let element">
              <div class="typo-body-12 pd-8">{{ element.personInCharge ?? '-' }}</div>
            </td>
          </ng-container>

          <tr class="sticky" [style.height]="'38px'" mat-header-row *matHeaderRowDef="customerDisplayColumns"></tr>
          <tr
            [style.height]="'38px'"
            mat-row
            class="table-tr-custom"
            *matRowDef="let row; columns: customerDisplayColumns"
          ></tr>
        </table>
      </div>
    </div>
  </div>
</div>
