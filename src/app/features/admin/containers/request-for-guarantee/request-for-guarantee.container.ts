import { AfterViewInit, ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { Store } from '@ngrx/store';
import { Observable, take, takeUntil } from 'rxjs';
import { ComponentCanDeactivate } from 'src/app/core/guards/unsaved-changes.guard';
import { DestroyService, DialogService, LoadingService } from 'src/app/core/services';
import { ActionButton } from 'src/app/shared/components/action-btn/action-btn.component';
import { GuaranteeConfirmComponent } from 'src/app/shared/components/guarantee-confirm/guarantee-confirm.component';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { BaseTableComponent } from 'src/app/shared/components/table-custom/base-table.component';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { customNumberFormat } from 'src/app/shared/utils/currency';
import { RequestForGuaranteeFilterComponent } from '../../components/request-for-guarantee-filter/request-for-guarantee-filter.component';
import { IFilterRequestGuaranteeParam, requestGuaranteeStatus } from '../../models/admin';
import { selectFilterRequestGuarantee$, selectSearchValue$ } from '../../store/admin.selections';
import { deepClone } from 'src/app/shared/utils/utils';
import { resetFilterRequestGuarantee, setFilterRequestGuarantee } from '../../store/admin.actions';
import { ActivatedRoute } from '@angular/router';
import { StorageService } from 'src/app/core/services/storage.service';

/**
 * Yêu cầu cấp bảo lãnh
 */
@Component({
  selector: 'app-request-for-guarantee',
  templateUrl: './request-for-guarantee.container.html',
  styleUrl: './request-for-guarantee.container.scss',
})
export class RequestForGuaranteeContainer extends BaseTableComponent<any> implements OnInit, ComponentCanDeactivate {
  @ViewChild('grid') gridTemplate!: GridComponent<any>;

  @ViewChild('timeRangePicker', { static: true }) timeRangePicker: TemplateRef<any> | null = null;
  @ViewChild('checkboxTemplate', { static: true }) checkboxTemplate: TemplateRef<any> | null = null;

  isEdit = false;
  searchValue: string = '';

  fakeData = [
    {
      accountNumber: '069C-000000-00',
      customerName: 'Nguyễn Văn A',
      bailMoney: *********,
      guaranteeIssuancePeriod: {
        time: '18:35:00',
        day: '2024-11-22',
      },
      personGrantGuarantee: 'MG01: Mai Tiến Đạt',
      approveStatus: 0,
    },
    {
      accountNumber: '069C-000000-01',
      customerName: 'Nguyễn Văn B',
      bailMoney: *********,
      guaranteeIssuancePeriod: {
        time: '18:35:00',
        day: '2024-11-22',
      },
      personGrantGuarantee: 'MG01: Mai Tiến Đạt',
      approveStatus: 0,
    },
    {
      accountNumber: '069C-000000-02',
      customerName: 'Nguyễn Văn C',
      bailMoney: *********,
      guaranteeIssuancePeriod: {
        time: '18:35:00',
        day: '2024-11-22',
      },
      personGrantGuarantee: 'MG01: Mai Tiến Đạt',
      approveStatus: 1,
    },
  ];

  requestGuaranteeStatus = requestGuaranteeStatus;
  filterOptions!: IFilterRequestGuaranteeParam;

  /**
   * Constructor
   * @param store
   * @param _destroy
   * @param loadingService
   */
  constructor(private store: Store, private _destroy: DestroyService, private loadingService: LoadingService) {
    super();
    this.toggleButtonByTags([ActionButton.export, ActionButton.filter]);
  }

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.data = structuredClone(this.fakeData);
    this.initialData = this.data;

    const templateRangeTime = this.timeRangePicker;
    this.columnConfigs = [
      {
        name: 'Số tài khoản',
        minWidth: 146,
        width: 146,
        tag: 'accountNumber',
        isDisplay: true,
        dragDisabled: true,
        disable: true,
        pinned: 'left',
        resizable: true,
      },
      {
        name: 'Tên khách hàng',
        minWidth: 30,
        width: 300,
        tag: 'customerName',
        isDisplay: true,
        resizable: true,
      },
      {
        name: 'Tiền bảo lãnh',
        minWidth: 30,
        width: 150,
        tag: 'bailMoney',
        isDisplay: true,
        displayValueFn: (v: number) => {
          if (!v) return '-';
          return customNumberFormat(v);
        },
      },
      {
        name: 'Thời gian cấp bảo lãnh',
        minWidth: 30,
        width: 230,
        tag: 'guaranteeIssuancePeriod',
        isDisplay: true,
        align: 'center',
        resizable: true,
        cellTemplate: templateRangeTime,
      },
      {
        name: 'Người cấp bảo lãnh',
        minWidth: 30,
        width: 190,
        tag: 'personGrantGuarantee',
        isDisplay: true,
        displayValueFn: (v) => {
          if (!v) return '-';
          return v;
        },
      },
      {
        name: 'Phê duyệt',
        minWidth: 30,
        width: 190,
        tag: 'approveStatus',
        isDisplay: true,
        align: 'center',
        resizable: true,
        cellTemplate: this.checkboxTemplate,
      },
    ];

    this.store
      .select(selectFilterRequestGuarantee$)
      .pipe(takeUntil(this._destroy))
      .subscribe((filter) => {
        this.filterOptions = { ...filter };
        this.isFilter = filter.isFilter;
      });

    this.store
      .select(selectSearchValue$)
      .pipe(takeUntil(this._destroy))
      .subscribe((value) => {
        this.searchMultipleLevel(value ?? '', ['customerName', 'accountNumber']);
      });
  }

  /**
   * NgAfterViewInit
   */
  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.grid = this.gridTemplate;
  }

  /**
   * CanDeactivate
   * @returns {Observable<boolean> | boolean} true/false
   */
  canDeactivate(): Observable<boolean> | boolean {
    return this.checkDataChangeNotSave();
  }

  /**
   * Open filter component
   * @param {string} tag
   */
  override clickButton(tag: string) {
    switch (tag) {
      case 'filter':
        {
          const ref = this.openFilter(RequestForGuaranteeFilterComponent, {
            width: '450px',
            data: this.filterOptions,
          });
          this.closeApplyFilter(ref);
        }
        break;
      default:
        break;
    }
  }

  /**
   * changeApproveStatus
   * @param checked
   * @param element
   * @param typeStatus
   */
  changeApproveStatus(event: MatCheckboxChange, element: any, typeStatus: string) {
    if (!event.source.checked) return;
    event.source.checked = false;

    const { accountNumber, bailMoney } = element;

    const ref = this.dialogService.openPopUp(GuaranteeConfirmComponent, {
      width: '360px',
      height: 'fit-content',
      panelClass: [''],
      data: {
        title: ['MES-15', 'MES-264'],
        info: [accountNumber, bailMoney],
        action: typeStatus === 'approve' ? 'approveRequestGuarantee' : 'notApproveRequestGuarantee',
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleBold: true,
          subTitleConfirm:
            typeStatus === 'approve'
              ? `Bạn có chắc chắn muốn <b>duyệt</b> yêu cầu cấp tiền bảo lãnh?`
              : `Bạn có chắc chắn muốn <b>không duyệt</b> yêu cầu cấp tiền bảo lãnh?`,
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;

        if (typeStatus === 'approve') {
          element.approveStatus = requestGuaranteeStatus.CONFIRM;
          event.source.checked = false;
        } else {
          element.approveStatus = requestGuaranteeStatus.CANCEL;
          event.source.checked = true;
        }
        this.dialogService.closeAll();
      });
  }

  /**
   * closeApplyFilter
   * @param ref
   */
  closeApplyFilter(ref: any) {
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (!v) return;
        this.applyFilter(v);
      });
  }

  /**
   * logicFilter
   * @param {any[]} data
   * @param {IFilterRequestGuaranteeParam} optionFilter
   */
  logicFilter(data: any[], optionFilter: IFilterRequestGuaranteeParam) {
    const rangeMatch = (value: number, range: any) =>
      range?.start && range?.end
        ? value >= +range.start && value <= +range.end
        : range?.start
        ? value >= +range.start
        : range?.end
        ? value <= +range.end
        : true;

    const newListFilter = data.filter((customer, index) => {
      const { accountNumber, bailMoney, approveStatus } = optionFilter;

      const isCustomerMatch = accountNumber.length ? accountNumber.includes(customer.accountNumber) : true;
      const isBailMoneyMatch = rangeMatch(customer.bailMoney, bailMoney);
      const isApproveStatusMatch = approveStatus.length ? approveStatus.includes(customer.approveStatus) : true;

      return isCustomerMatch && isBailMoneyMatch && isApproveStatusMatch;
    });

    return newListFilter;
  }

  /**
   * applyFilter
   */
  applyFilter(v: any) {
    const { type, optionFilter } = v;
    const { accountNumber, bailMoney, approveStatus, isFilter } = optionFilter;
    const dataClone = deepClone(this.initialData);

    if (type === 'save') {
      const params: IFilterRequestGuaranteeParam = {
        accountNumber,
        bailMoney,
        approveStatus,
        isFilter: true,
      };

      this.store.dispatch(setFilterRequestGuarantee({ params }));
      const newListFilter = this.isSearch
        ? this.logicFilter(this.searchedData, optionFilter)
        : this.logicFilter(dataClone, optionFilter);
      this.loadingService.hide();
      this.filteredData = newListFilter;
      this.data = newListFilter;
    }
  }
}
