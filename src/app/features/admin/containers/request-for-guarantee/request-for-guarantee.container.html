<div class="request-for-guarantee-container">
  <div class="header-request-for-guarantee">
    <div class="left-box">
      <div class="typo-heading-9">{{'MES-573' | translate}}</div>

      <div class="number-info-cls">
        <!-- Tổng tài khoản -->
        <!-- <div class="box-info typo-body-11">
              <img src="./assets/icons/table_sum.svg" alt="table_sum" />
              {{totalTags.totalAccount}} {{'MES-15' | translate}}
            </div> -->
        <!-- S<PERSON> lượng đã chỉnh sửa -->
        <!-- <div *ngIf="onEditMode" class="text-edit-cls">{{'MES-156' | translate}}: {{getCountDataEdited()}}</div> -->
      </div>
    </div>

    <div class="right-box">
      <app-action-btn
        [listAction]="actionButtons"
        [listColumn]="columnConfigs"
        (eventUpdateValue$)="updateColumnValue($event)"
        (eventClickEmit$)="clickButton($event)"
        [ngClass]="{
              'not-accept-save-cls' : checkEnableSaveBtn(),
              'filter-mode-cls': isFilter
            }"
      ></app-action-btn>
    </div>
  </div>

  <div class="table-view-container">
    <sha-grid
      #grid
      [columnConfigs]="columnConfigs"
      [data]="data"
      (clickColumn)="clickOnColumn($event)"
      (resizeColumn)="resizeColumn($event)"
      class="table-custom-cls"
      [isInValid]="isInValid"
      [editable]="onEditMode"
      [elementTagSelect]="elementTagSelect"
    >
    </sha-grid>
  </div>
</div>

<ng-template #timeRangePicker let-templateInfo="templateInfo" let-element="element" let-tag="tag" let-column="column">
  @if(templateInfo) {
  <div class="time-range">{{templateInfo.time}} - {{templateInfo.day | date : 'dd/MM/yyyy'}}</div>
  } @else {
  <div>-</div>
  }
</ng-template>

<ng-template #checkboxTemplate let-approveStatus="templateInfo" let-element="element" let-tag="tag" let-column="column">
  <div class="approve-status">
    <!-- Duyệt -->
    <div class="approve">
      <mat-checkbox
        (change)="changeApproveStatus($event,element ,'approve')"
        [checked]="approveStatus === requestGuaranteeStatus.CONFIRM"
        [disabled]="approveStatus === requestGuaranteeStatus.CONFIRM ||approveStatus === requestGuaranteeStatus.CANCEL"
      ></mat-checkbox>
      {{"MES-574" | translate}}
    </div>

    <!-- Không duyệt -->
    <div class="not-confirm">
      <mat-checkbox
        (change)="changeApproveStatus($event,element ,'notApprove')"
        [checked]="approveStatus === requestGuaranteeStatus.CANCEL"
        [disabled]="approveStatus === requestGuaranteeStatus.CONFIRM ||approveStatus === requestGuaranteeStatus.CANCEL"
      ></mat-checkbox
      >{{"MES-575" | translate}}
    </div>
  </div>
</ng-template>
