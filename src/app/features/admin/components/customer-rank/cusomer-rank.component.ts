import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { IOption } from 'src/app/shared/models/dropdown-item.model';
import { ECustomerRank, ELoanRate, ICustomRankConfirmParam } from '../../models/admin';
import { AbstractControl, Form, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PopoverService } from '../../../../shared/components/popover/popover.service';
import { MatDialogRef } from '@angular/material/dialog';
import { DestroyService, DialogService } from '../../../../core/services';
import { GuaranteeConfirmComponent } from '../../../../shared/components/guarantee-confirm/guarantee-confirm.component';
import { map, Subscription, take, takeUntil } from 'rxjs';
import { totalProportionValidator } from '../../../../shared/validators/form';

/**
 * CustomerRankComponent
 */
@Component({
  selector: 'app-customer-rank',
  templateUrl: './cusomer-rank.component.html',
  styleUrl: './cusomer-rank.component.scss',
})
export class CustomerRankComponent {
  customersRank: IOption[] = [
    {
      label: ECustomerRank.STANDARD,
      value: ECustomerRank.STANDARD,
    },
    {
      label: ECustomerRank.VIP,
      value: ECustomerRank.VIP,
    },
    {
      label: ECustomerRank.VVIP,
      value: ECustomerRank.VVIP,
    },
    {
      label: ECustomerRank.SVIP,
      value: ECustomerRank.SVIP,
    },
  ];

  loanRate: IOption[] = [
    {
      label: ELoanRate.RATE01,
      value: ELoanRate.RATE01,
    },
    {
      label: ELoanRate.RATE02,
      value: ELoanRate.RATE02,
    },
    {
      label: ELoanRate.RATE03,
      value: ELoanRate.RATE03,
    },
  ];
  totalProportion: number = 0;
  conditionRankForm: FormGroup;
  benefitRankForm: FormGroup;
  private formChangesSubscription: Subscription;
  private currentControl: AbstractControl | null = null;
  /**
   * @param fb
   * @param popoverService
   * @param dialogRef
   * @param dialogService
   * @param _destroy
   */
  constructor(
    private fb: FormBuilder,
    private popoverService: PopoverService,
    private dialogRef: MatDialogRef<CustomerRankComponent>,
    private dialogService: DialogService,
    private _destroy: DestroyService
  ) {
    this.conditionRankForm = this.initConditionForm();
    this.benefitRankForm = this.initBenefitForm();
    this.formChangesSubscription = this.conditionRankForm.valueChanges
      .pipe(
        takeUntil(_destroy),
        map((value) => {
          const avrCash = parseFloat(value.avrCash) || 0;
          const finalCash = parseFloat(value.finalCash) || 0;
          const avrCollateral = parseFloat(value.avrCollateral) || 0;
          const finalCollateral = parseFloat(value.finalCollateral) || 0;
          const avrGmv = parseFloat(value.avrGmv) || 0;
          const avrSpin = parseFloat(value.avrSpin) || 0;
          const avrDebt = parseFloat(value.avrDebt) || 0;
          const finalDebt = parseFloat(value.finalDebt) || 0;
          const avrProfit = parseFloat(value.avrProfit) || 0;

          return (
            avrCash + finalCash + avrCollateral + finalCollateral + avrGmv + avrSpin + avrDebt + finalDebt + avrProfit
          );
        })
      )
      .subscribe((total) => {
        this.totalProportion = total;
        // this.conditionRankForm.setValidators(totalProportionValidator(this.totalProportion));
        // this.updateValidators();
        Object.keys(this.conditionRankForm.controls).forEach((key) => {
          this.conditionRankForm
            .get(key)
            ?.valueChanges.pipe(takeUntil(this._destroy))
            .subscribe(() => {
              this.conditionRankForm.get(key);
              this.currentControl = this.conditionRankForm.get(key);
              this.updateValidators();
            });
        });
      });
  }

  /**
   *
   */
  private updateValidators() {
    if (!this.currentControl) return;
    const controlName = Object.keys(this.conditionRankForm.controls).find(
      (key) => this.conditionRankForm.get(key) === this.currentControl
    );

    Object.keys(this.conditionRankForm.controls).forEach((key) => {
      const control = this.conditionRankForm.get(key);
      if (control && key === controlName) {
        control.setValidators([Validators.required, totalProportionValidator(100)]);
      } else if (control) {
        control.clearValidators();
        control.setValidators([Validators.required]);
      }
      control?.updateValueAndValidity({ emitEvent: false });
    });
  }

  /**
   * Tạo form group
   */
  private initConditionForm() {
    const formConfig = {
      nameRank: [null, [Validators.required]],
      avrCash: [null, [Validators.required]],
      finalCash: [null, [Validators.required]],
      avrCollateral: [null, [Validators.required]],
      finalCollateral: [null, [Validators.required]],
      avrGmv: [null, [Validators.required]],
      avrSpin: [null, [Validators.required]],
      avrDebt: [null, [Validators.required]],
      finalDebt: [null, [Validators.required]],
      avrProfit: [null, [Validators.required]],
    };

    return this.fb.group(formConfig);
  }

  /**
   *
   */
  private initBenefitForm() {
    const formConfig = {
      rank01: [ECustomerRank.STANDARD, [Validators.required]],
      rank02: [ECustomerRank.STANDARD, [Validators.required]],
      loanRate: [ELoanRate.RATE01, [Validators.required]],
      loanInterestRate: [null, [Validators.required]],
      borrowLimit: [null, [Validators.required]],
      marginCallRate: [null, [Validators.required]],
      forceSellRate: [null, Validators.required],
    };

    return this.fb.group(formConfig);
  }

  /**
   * Viết hoa text trong input
   * @param event
   */
  toUpperCase(event: any): void {
    event.target.value = event.target.value.toUpperCase();
  }

  /**
   * @param fb
   */
  devidePercentValueForm(fb: FormGroup) {
    const valueForm: { [key: string]: string | number } = {};
    for (const key in fb.value) {
      // eslint-disable-next-line no-prototype-builtins
      if (fb.value.hasOwnProperty(key)) {
        if (key === 'nameRank' || key === 'rank01' || key === 'rank02' || key === 'loanRate') {
          valueForm[key] = fb.value[key];
        } else {
          key === 'borrowLimit'
            ? (valueForm[key] = Number(fb.value[key]))
            : (valueForm[key] = Number(fb.value[key]) / 100);
        }
      }
    }
    return valueForm;
  }

  /**
   * submot form
   */
  onConfirm() {
    const valueConditonForm = this.devidePercentValueForm(this.conditionRankForm);
    const valueBenefitForm = this.devidePercentValueForm(this.benefitRankForm);
    const {
      nameRank,
      avrCash,
      finalCash,
      avrCollateral,
      finalCollateral,
      avrGmv,
      avrSpin,
      avrDebt,
      finalDebt,
      avrProfit,
    } = valueConditonForm;

    const { rank01, rank02, loanRate, loanInterestRate, borrowLimit, marginCallRate, forceSellRate } = valueBenefitForm;
    const ref = this.dialogService.openPopUp(GuaranteeConfirmComponent, {
      width: '370px',
      height: '275px',
      panelClass: [''],
      data: {
        value: {
          nameRank: nameRank.toString().toLowerCase(),
          avrCash,
          finalCash,
          avrCollateral,
          finalCollateral,
          avrGmv,
          avrSpin,
          avrDebt,
          finalDebt,
          avrProfit,
          rank01,
          rank02,
          loanRate,
          loanInterestRate,
          borrowLimit,
          marginCallRate,
          forceSellRate,
          totalProportion: this.totalProportion / 100,
        },
        action: 'customerRank',
        info: [nameRank.toString().toUpperCase()],
        title: ['MES-492'],
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-519',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
    });
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        if (v.type === 'save') {
          this.dialogService.closeAll();
        }
      });
    // const customerRankConfirmData =
  }
}
