<div class="customer-rank-wrap">
  <div class="customer-rank-header">
    <div class="title-cls typo-body-14">{{ 'MES-491' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="customer-rank-body">
    <form [formGroup]="conditionRankForm" class="section-wrap">
      <!-- TOP -->
      <div class="content-top">
        <div class="header-content typo-body-12">{{ 'MES-502' | translate }}</div>
        <!-- 1 -->
        <div class="children">
          <div class="left">
            <div class="label-input typo-body-12">{{ 'MES-492' | translate }}</div>

            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                formControlName="nameRank"
                class="input-cls typo-body-12"
                type="text"
                (input)="toUpperCase($event)"
              />
            </app-form-control>
          </div>
          <div class="right total">
            <div class="label-input label-proportion typo-body-12">{{ 'MES-503' | translate }}</div>
            <div class="total-proportion typo-body-12" [ngClass]="{ 'red-over': totalProportion > 100 }">
              {{ totalProportion }}%
            </div>
          </div>
        </div>
        <!-- 2 -->
        <div class="children">
          <div class="left">
            <div class="label-input typo-body-12">{{ 'MES-493' | translate }} (%)</div>
            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                mask="percent.2"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="avrCash"
                class="input-cls typo-body-12"
                type="text"
                placeholder="%"
              />
            </app-form-control>
          </div>
          <div class="right">
            <div class="label-input typo-body-12">{{ 'MES-494' | translate }} (%)</div>
            <app-form-control [standaloneControl]="" [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                mask="percent.2"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="finalCash"
                type="text"
                class="input-cls typo-body-12"
                placeholder="%"
              />
            </app-form-control>
          </div>
        </div>
        <!-- 3 -->
        <div class="children">
          <div class="left">
            <div class="label-input typo-body-12">{{ 'MES-495' | translate }} (%)</div>
            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                [mask]="'percent.2'"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="avrCollateral"
                class="input-cls typo-body-12"
                type="text"
                placeholder="%"
              />
            </app-form-control>
          </div>
          <div class="right">
            <div class="label-input typo-body-12">{{ 'MES-496' | translate }} (%)</div>
            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                [mask]="'percent.2'"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="finalCollateral"
                type="text"
                class="input-cls typo-body-12"
                placeholder="%"
              />
            </app-form-control>
          </div>
        </div>

        <!-- 4 -->

        <div class="children">
          <div class="left">
            <div class="label-input typo-body-12">{{ 'MES-497' | translate }} (%)</div>

            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                [mask]="'percent.2'"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="avrGmv"
                class="input-cls typo-body-12"
                type="text"
                placeholder="%"
              />
            </app-form-control>
          </div>
          <div class="right">
            <div class="label-input typo-body-12">{{ 'MES-498' | translate }} (%)</div>

            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                [mask]="'percent.2'"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="avrSpin"
                type="text"
                class="input-cls typo-body-12"
                placeholder="%"
              />
            </app-form-control>
          </div>
        </div>
        <!-- 5 -->
        <div class="children">
          <div class="left">
            <div class="label-input typo-body-12">{{ 'MES-499' | translate }} (%)</div>

            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                [mask]="'percent.2'"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="avrDebt"
                class="input-cls typo-body-12"
                type="text"
                placeholder="%"
              />
            </app-form-control>
          </div>
          <div class="right">
            <div class="label-input typo-body-12">{{ 'MES-500' | translate }} (%)</div>

            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                [mask]="'percent.2'"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="finalDebt"
                type="text"
                class="input-cls typo-body-12"
                placeholder="%"
              />
            </app-form-control>
          </div>
        </div>
        <!-- 6 -->
        <div class="children">
          <div class="all">
            <div class="label-input typo-body-12">{{ 'MES-501' | translate }} (%)</div>

            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                [mask]="'percent.2'"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="avrProfit"
                type="text"
                class="input-cls typo-body-12"
                placeholder="%"
              />
            </app-form-control>
          </div>
        </div>
      </div>
    </form>

    <form [formGroup]="benefitRankForm" class="section-wrap">
      <!-- BOTTOM -->
      <div class="content-bottom">
        <div class="header-content typo-body-12">{{ 'MES-504' | translate }}</div>
        <div class="children">
          <div class="left">
            <div class="label-input typo-body-12">{{ 'MES-505' | translate }}</div>
            <!-- <input type="text" class="input-cls typo-body-12" /> -->
            <mat-form-field>
              <mat-select formControlName="rank01" placeholder="STANDARD">
                <mat-option [value]="item.value" *ngFor="let item of customersRank" class="mat-option"
                  >{{ item.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="right">
            <div class="label-input typo-body-12">{{ 'MES-506' | translate }}</div>
            <!-- <input type="text" class="input-cls typo-body-12" /> -->
            <mat-form-field>
              <mat-select formControlName="rank02" placeholder="STANDARD">
                <mat-option [value]="item.value" *ngFor="let item of customersRank" class="mat-option"
                  >{{ item.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

        <div class="children">
          <div class="left">
            <div class="label-input typo-body-12">{{ 'MES-507' | translate }}</div>
            <!-- <input type="text" class="input-cls typo-body-12" />
          -->
            <mat-form-field>
              <mat-select formControlName="loanRate" placeholder="5 : 5">
                <mat-option [value]="item.value" *ngFor="let item of loanRate" class="mat-option"
                  >{{ item.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="right">
            <div class="label-input typo-body-12">{{ 'MES-508' | translate }} (%)</div>
            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                type="text"
                mask="percent.2"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="loanInterestRate"
                class="input-cls typo-body-12"
                placeholder="%"
              />
            </app-form-control>
          </div>
        </div>

        <div class="children">
          <div class="left">
            <div class="label-input typo-body-12">{{ 'MES-509' | translate }} (VND)</div>

            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                mask="separator.2"
                type="text"
                thousandSeparator=","
                decimalMarker="."
                formControlName="borrowLimit"
                class="input-cls typo-body-12"
                placeholder="VND"
              />
            </app-form-control>
          </div>
          <div class="right">
            <div class="label-input typo-body-12">{{ 'MES-510' | translate }} (%)</div>

            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                type="text"
                mask="percent.2"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="marginCallRate"
                class="input-cls typo-body-12"
                placeholder="%"
              />
            </app-form-control>
          </div>
        </div>

        <div class="children">
          <div class="all">
            <div class="label-input typo-body-12">{{ 'MES-511' | translate }} (%)</div>

            <app-form-control [errStyles]="{ 'text-align': 'start' }" class="no-padding">
              <input
                type="text"
                mask="percent.2"
                thousandSeparator=","
                decimalMarker="."
                [leadZero]="true"
                formControlName="forceSellRate"
                class="input-cls typo-body-12"
                placeholder="%"
              />
            </app-form-control>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="customer-rank-footer">
    <button
      class="btn primary typo-button-3"
      [disabled]="conditionRankForm.invalid || benefitRankForm.invalid"
      (click)="onConfirm()"
    >
      {{ 'MES-89' | translate }}
    </button>
    <button class="btn outline typo-button-3">{{ 'MES-74' | translate }}</button>
  </div>
</div>
