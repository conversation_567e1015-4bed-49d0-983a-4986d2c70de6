.customer-rank-wrap {
  width: 532px;
  display: flex;
  flex-direction: column;
  height: 100%;
  .customer-rank-header {
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid var(--color--other--divider);
    align-items: center;
    img[alt='x-cross'] {
      cursor: pointer;
    }
  }

  .customer-rank-body {
    padding: 24px;
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 16px;
    overflow: auto;
    .content-top {
      background-color: var(--color--other--table-title);
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      padding: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .children {
        width: 100%;
        display: flex;
        gap: 8px;
        flex-grow: 1;
        .total {
          display: flex;
          align-items: center;
          flex-direction: column;
          gap: 4px;
          width: 100%;
          .total-proportion {
            padding: 8px 16px;
            height: 48px;
          }
          .label-proportion {
            color: var(--color--text--subdued);
          }
        }
        .left,
        .right,
        .all {
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 4px;
          .input-cls {
            width: 100%;
            padding: 8px 16px;
            height: 48px;
            border-radius: 8px;
            border: 1px solid var(--Neutral-100, #dbdee0);
          }
        }
      }
    }

    .content-bottom {
      background-color: var(--color--other--table-title);
      border: 1px solid var(--color--other--divider);
      border-radius: 8px;
      padding: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .children {
        width: 100%;
        display: flex;
        gap: 8px;
        flex-grow: 1;
        .total {
          display: flex;
          align-items: center;
          flex-direction: column;
          gap: 4px;
          width: 100%;
          .total-proportion {
            padding: 8px 16px;
            height: 48px;
          }

          .label-proportion {
            color: var(--color--text--subdued);
          }
        }
        .left,
        .right,
        .all {
          ::ng-deep {
            .mdc-text-field--outlined .mat-mdc-form-field-infix,
            .mdc-text-field--no-label .mat-mdc-form-field-infix {
              padding-top: 8px;
              padding-bottom: 8px;
              height: 48px;
              display: flex;
              align-items: center;
            }

            .mat-mdc-select-value {
              font-size: 12px;
            }
          }

          ::ng-deep app-form-control .form_err_msg {
            padding-left: 0px !important;
          }

          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 4px;

          .input-cls {
            width: 100%;
            padding: 8px 16px;
            height: 48px;
            border-radius: 8px;
            border: 1px solid var(--Neutral-100, #dbdee0);
          }
        }
      }
    }
  }
  .customer-rank-footer {
    display: flex;
    padding: 16px 24px;
    align-items: center;
    justify-content: center;
    gap: 24px;
    border-top: 1px solid var(--color--other--divider);
  }
  .btn {
    display: flex;
    align-items: center;
    // border: 1px solid var(--color--other--divider);
    width: 160px;
    // padding: 12px 16px;
    // border-radius: 8px;
    cursor: pointer;
  }
}

.mat-mdc-option {
  font-size: 12px !important;
}

.red-over {
  color: red;
}
