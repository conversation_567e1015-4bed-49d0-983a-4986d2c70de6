import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { takeUntil } from 'rxjs';
import { DestroyService, DialogService, I18nService } from 'src/app/core/services';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';

/**
 * AccountOptionsComponent
 */
@Component({
  selector: 'app-account-options',
  templateUrl: './account-options.component.html',
  styleUrl: './account-options.component.scss',
})
export class AccountOptionsComponent implements OnInit {
  detailData: any;
  /**
   * constructor
   * @param _destroy
   * @param popoverRef
   * @param router
   * @param dialogService
   * @param i18n
   */
  constructor(
    private _destroy: DestroyService,
    @Inject(PopoverRef) private popoverRef: PopoverRef,
    private router: Router,
    private dialogService: DialogService,
    private i18n: I18nService
  ) {}

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.detailData = this.popoverRef.componentConfig.element;
  }

  /**
   * go to detail account
   */
  handleDetailAccount() {
    this.popoverRef.close();
    this.router.navigate(['/account-info']);
  }

  /**
   * handleDeleteAccount
   * @param detailData
   */
  handleDeleteAccount(detailData: any) {
    const ref = this.dialogService.openPopUp(DeleteNotificationComponent, {
      data: {
        isActive: false,
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: `${this.i18n.translate('MES-564')} ${detailData.accountName} ?`,
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
        isReverse: true,
      },
      height: '260px',
      width: '360px',
      panelClass: ['popup-confirm', 'not-padding-popup'],
    });

    ref
      .afterClosed()
      .pipe(takeUntil(this._destroy))
      .subscribe((v) => {
        if (v === 'save') {
          this.popoverRef.close(detailData.id);
        }
      });
  }
}
