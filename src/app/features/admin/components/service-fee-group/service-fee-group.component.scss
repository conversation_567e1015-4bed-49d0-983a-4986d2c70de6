.dialog-service-fee-group-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 16px 24px;
  border-bottom: 1px solid var(--color--other--divider);
}

.dialog-body {
  flex: 1;
  overflow: auto;
  padding: 24px;

  .body-wrapper {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background-color: var(--color--other--table-title);

    .row {
      &.divide {
        display: flex;
        gap: 8px;
        flex-grow: 1;

        .nav-value {
          flex: 1 0 0;
          min-width: 330px;
        }
      }
    }

    .dropdown {
      position: relative;

      input {
        cursor: pointer;
      }

      img {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 16px;
      }
    }

    .label {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 8px;

      .require {
        color: var(--color--danger--700);
      }
    }

    input {
      display: flex;
      align-items: center;
      height: 48px;
      padding: 8px 16px;
      width: 100%;
      border-radius: 8px;
      border: 1px solid var(--color--neutral--100);
      background: var(--color--neutral--white);

      &.uppercase-field-input {
        text-transform: uppercase;
      }
    }

    ::-webkit-input-placeholder {
      /* WebKit browsers */
      text-transform: none;
    }
    :-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      text-transform: none;
    }
    ::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      text-transform: none;
    }
    :-ms-input-placeholder {
      /* Internet Explorer 10+ */
      text-transform: none;
    }
    ::placeholder {
      /* Recent browsers */
      text-transform: none;
    }
  }
}

.dialog-footer {
  border-top: 1px solid var(--color--other--divider);
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 16px 24px;

  .btn-wrapper {
    flex: 1;
    display: flex;
    align-items: center;

    .btn {
      display: flex;
      width: 160px;
      padding: 12px 16px;
      justify-content: center;
      align-items: center;
      border-radius: 8px;

      &.confirm {
        margin-left: auto;
        background-color: var(--color--brand--500);
        color: var(--color--neutral--white);
      }

      &.close {
        margin-right: auto;
        background-color: var(--color--neutral--white);
      }
    }
  }
}
