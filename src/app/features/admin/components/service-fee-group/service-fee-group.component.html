<div class="dialog-service-fee-group-container">
  <div class="dialog-header">
    <div class="typo-body-2">{{ 'MES-547' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="dialog-body">
    <form [formGroup]="serviceFeeGroupForm" class="body-wrapper">
      <!-- Loại phí giao dịch -->
      <div class="row">
        <div class="label typo-body-6">{{ 'MES-548' | translate }} <span class="require typo-body-6">*</span></div>
        <app-form-control>
          <div class="dropdown">
            <input
              matInput
              formControlName="typeOfTransactionFee"
              [placeholder]="'MES-548' | translate"
              readonly
              (click)="openTypeOfTransactionFee($event, 'typeOfTransactionFee')"
            />
            <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
          </div>
        </app-form-control>
      </div>

      <!-- Tên hạng phí -->
      <div class="row">
        <div class="label typo-body-6">{{ 'MES-549' | translate }}<span class="require typo-body-6">*</span></div>
        <app-form-control>
          <input
            matInput
            class="uppercase-field-input"
            formControlName="feeGroupName"
            [placeholder]="'MES-492' | translate"
          />
        </app-form-control>
      </div>

      <!-- Khoảng NAV 0 - 49.999.999 -->
      <div class="row divide" formGroupName="nav0To50">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 0 - 49.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav0To50') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav0To50') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div #element class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav0To50')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 50.000.000 - 99.999.999 -->
      <div class="row divide" formGroupName="nav50To100">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 50.000.000 - 99.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav50To100') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav50To100') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav50To100')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 100.000.000 - 249.999.999 -->
      <div class="row divide" formGroupName="nav100To250">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 100.000.000 - 249.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav100To250') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav100To250') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav100To250')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 250.000.000 - 499.999.999 -->
      <div class="row divide" formGroupName="nav250To500">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 250.000.000 - 499.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav250To500') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav250To500') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav250To500')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 500.000.000 - 749.999.999 -->
      <div class="row divide" formGroupName="nav500To750">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 500.000.000 - 749.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav500To750') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav500To750') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav500To750')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 750.000.000 - 999.999.999 -->
      <div class="row divide" formGroupName="nav750To1000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 750.000.000 - 999.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav750To1000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav750To1000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav750To1000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 1.000.000.000 - 2.499.999.999 -->
      <div class="row divide" formGroupName="nav1000To2500">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 1.000.000.000 - 2.499.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav1000To2500') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav1000To2500') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav1000To2500')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 2.500.000.000 - 4.999.999.999 -->
      <div class="row divide" formGroupName="nav2500To5000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 2.500.000.000 - 4.999.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav2500To5000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav2500To5000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav2500To5000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 5.000.000.000 - 7.499.999.999 -->
      <div class="row divide" formGroupName="nav5000To7500">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 5.000.000.000 - 7.499.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav5000To7500') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav5000To7500') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav5000To7500')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 7.500.000.000 - 9.999.999.999 -->
      <div class="row divide" formGroupName="nav7500To10000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 7.500.000.000 - 9.999.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav7500To10000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav7500To10000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav7500To10000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 10.000.000.000 - 14.999.999.999 -->
      <div class="row divide" formGroupName="nav10000To15000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 10.000.000.000 - 14.999.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav10000To15000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav10000To15000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav10000To15000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 15.000.000.000 - 19.999.999.999 -->
      <div class="row divide" formGroupName="nav15000To20000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 15.000.000.000 - 19.999.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav15000To20000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav15000To20000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav15000To20000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 20.000.000.000 - 24.999.999.999 -->
      <div class="row divide" formGroupName="nav20000To25000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 20.000.000.000 - 24.999.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav20000To25000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav20000To25000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav20000To25000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 25.000.000.000 - 29.999.999.999 -->
      <div class="row divide" formGroupName="nav25000To30000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 25.000.000.000 - 29.999.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav25000To30000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav25000To30000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav25000To30000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 30.000.000.000 - 34.999.999.999 -->
      <div class="row divide" formGroupName="nav30000To35000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 30.000.000.000 - 34.999.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav30000To35000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav30000To35000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav30000To35000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 35.000.000.000 - 39.999.999.999 -->
      <div class="row divide" formGroupName="nav35000To40000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 35.000.000.000 - 39.999.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav35000To40000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav35000To40000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav35000To40000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV 40.000.000.000 - 49.999.999.999 -->
      <div class="row divide" formGroupName="nav40000To45000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} 40.000.000.000 - 49.999.999.999</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('nav40000To45000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('nav40000To45000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'nav40000To45000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>

      <!-- Khoảng NAV ≥ 50.000.000.000 -->
      <div class="row divide" formGroupName="navBiggerThan50000">
        <div class="nav-value">
          <div class="label typo-body-6">{{ 'MES-550' | translate }} ≥ 50.000.000.000</div>
          <app-form-control>
            <input
              matInput
              formControlName="value"
              [mask]="isTypePercent('navBiggerThan50000') ? 'percent.2' : 'separator.2'"
              [placeholder]="isTypePercent('navBiggerThan50000') ? '0,35' : '100.000'"
            />
          </app-form-control>
        </div>

        <div class="nav-type">
          <div class="label typo-body-6">{{ 'MES-551' | translate }}</div>
          <app-form-control>
            <div class="dropdown">
              <input matInput formControlName="type" readonly (click)="openTypeOfNav($event, 'navBiggerThan50000')" />
              <img src="./assets/icons/arrow-down.svg" alt="arrow-down" />
            </div>
          </app-form-control>
        </div>
      </div>
    </form>
  </div>

  <div class="dialog-footer">
    <div class="btn-wrapper">
      <button
        mat-dialog-close
        class="btn confirm typo-button-3"
        [disabled]="serviceFeeGroupForm.invalid"
        (click)="submitForm()"
      >
        {{ 'MES-89' | translate }}
      </button>
    </div>
    <div class="btn-wrapper">
      <button mat-dialog-close class="btn close typo-button-3">{{ 'MES-74' | translate }}</button>
    </div>
  </div>
</div>
