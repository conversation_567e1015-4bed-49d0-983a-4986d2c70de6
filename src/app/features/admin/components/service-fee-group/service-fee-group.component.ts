import { Component } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { take, takeUntil } from 'rxjs';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { SearchListComponent } from 'src/app/shared/components/search-list/search-list.component';
import {
  CONVERT_UNIT_CONTRACT_TO_LABLE,
  CONVERT_UNIT_TRANSACTION_TO_LABEL,
  ETypeOfNav,
  typeOfNavPerContractOptions,
  typeOfNavPerTransactionOptions,
  typeOfTransactionFee,
} from '../../constants/admin';
import { DestroyService, DialogService,  } from 'src/app/core/services';
import { GuaranteeConfirmComponent } from 'src/app/shared/components/guarantee-confirm/guarantee-confirm.component';

interface DataDropdown {
  item: Item[];
  isMultiple: boolean;
}

interface Item {
  label: string;
  value: string;
}

/**
 * ServiceFeeGroupComponent
 */
@Component({
  selector: 'app-service-fee-group',
  templateUrl: './service-fee-group.component.html',
  styleUrl: './service-fee-group.component.scss',
})
export class ServiceFeeGroupComponent {
  serviceFeeGroupForm!: FormGroup;

  derivativeType = 'Phái sinh';
  percentTransactionType = '% /giao dịch';
  percentContractType = '% /hợp đồng';

  CONVERT_UNIT_TRANSACTION_TO_LABEL = CONVERT_UNIT_TRANSACTION_TO_LABEL;
  CONVERT_UNIT_CONTRACT_TO_LABLE = CONVERT_UNIT_CONTRACT_TO_LABLE;

  /**
   * constructor
   * @param fb FormBuilder
   * @param popoverService PopoverService
   * @param _destroy
   * @param messageService - messageService
   * @param dialogService - dialogService
   */
  constructor(
    private fb: FormBuilder,
    private popoverService: PopoverService,
    private _destroy: DestroyService,
    private dialogService: DialogService
  ) {
    this.initForm();
    this.listenTypeOfTransactionFeeChange();
  }

  /**
   * initForm
   */
  private initForm() {
    this.serviceFeeGroupForm = this.fb.group({
      typeOfTransactionFee: [null, [Validators.required]],
      feeGroupName: [null, [Validators.required]],
      nav0To50: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT]],
      }),
      nav50To100: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav100To250: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav250To500: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav500To750: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav750To1000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav1000To2500: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav2500To5000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav5000To7500: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav7500To10000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav10000To15000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav15000To20000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav20000To25000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav25000To30000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav30000To35000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav35000To40000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      nav40000To45000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
      navBiggerThan50000: this.fb.group({
        value: [null, [Validators.required, this.valueValidator('value', 'type')]],
        type: [CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT], [Validators.required]],
      }),
    });
  }

  /**
   * listenTypeOfTransactionFeeChange
   */
  private listenTypeOfTransactionFeeChange() {
    const navGroups = [
      'nav0To50',
      'nav50To100',
      'nav100To250',
      'nav250To500',
      'nav500To750',
      'nav750To1000',
      'nav1000To2500',
      'nav2500To5000',
      'nav5000To7500',
      'nav7500To10000',
      'nav10000To15000',
      'nav15000To20000',
      'nav20000To25000',
      'nav25000To30000',
      'nav30000To35000',
      'nav35000To40000',
      'nav40000To45000',
      'navBiggerThan50000',
    ];

    navGroups.forEach((group) => {
      const control = this.serviceFeeGroupForm.get('typeOfTransactionFee');
      control?.valueChanges.pipe(takeUntil(this._destroy)).subscribe((newValue) => {
        if (newValue === this.derivativeType) {
          this.updateType(group, CONVERT_UNIT_CONTRACT_TO_LABLE[ETypeOfNav.PERCENT]);
        } else {
          this.updateType(group, CONVERT_UNIT_TRANSACTION_TO_LABEL[ETypeOfNav.PERCENT]);
        }
      });
    });
  }

  /**
   * valueValidator
   * @param valueKey value
   * @param typeKey type
   */
  valueValidator(valueKey: string, typeKey: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const formGroup = control.parent as FormGroup;

      if (!formGroup) return null;

      // Regular expression to match numbers with up to 2 decimal places
      const regex = /^\d+(\.\d{1,2})?$/;

      const value = formGroup.get(valueKey)?.value;
      const type = formGroup.get(typeKey)?.value;
      if ((type === this.percentTransactionType || type === this.percentContractType) && value && !regex.test(value)) {
        return { valueInvalid: true };
      }

      return null; // No error if the condition is not met
    };
  }

  /**
   * updateType
   * @param groupName
   * @param newType
   */
  private updateType(groupName: string, newType: any): void {
    const typeControl = this.serviceFeeGroupForm.get(groupName)?.get('type');
    typeControl?.setValue(newType, { emitEvent: false });
  }

  /**
   * openTypeOfTransactionFee
   * @param event event
   * @param controlName controlName
   */
  openTypeOfTransactionFee(event: Event, controlName: string) {
    event.preventDefault();
    const control = this.serviceFeeGroupForm.get(controlName);
    const originElement = event.target as HTMLElement;
    const ref = this.popoverService.open({
      origin: originElement,
      content: SearchListComponent,
      position: 2,
      width: originElement.offsetWidth,
      panelClass: ['dropdown-overlay-common', 'dropdown-width-100'],
      componentConfig: {
        searchKey: 'label',
        displayOptionFn: (v: any) => v.label,
        options: typeOfTransactionFee,
        isSearch: false,
        multiple: false,
        value: [control?.value],
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe({
      next: (v) => {
        const { data } = v;
        if (!data) return;
        const { item } = data as DataDropdown;
        const { label } = item[0];

        control?.setValue(label);
      },
    });
  }

  /**
   * openTypeOfNav
   * @param event event
   * @param controlName controlName
   */
  openTypeOfNav(event: Event, controlName: string) {
    event.preventDefault();
    const controlType = this.serviceFeeGroupForm.get(`${controlName}.type`);
    const originElement = event?.target as HTMLElement;

    const ref = this.popoverService.open({
      origin: originElement,
      content: SearchListComponent,
      position: 2,
      componentConfig: {
        searchKey: 'label',
        displayOptionFn: (v: any) => v.label,
        options: this.isDerivative() ? typeOfNavPerContractOptions : typeOfNavPerTransactionOptions,
        isSearch: false,
        multiple: false,
        value: [controlType?.value],
      },
    });

    ref.afterClosed$.pipe(take(1)).subscribe({
      next: (v) => {
        const { data } = v;
        if (!data) return;
        const { item } = data as DataDropdown;
        const { label } = item[0];

        controlType?.setValue(label);
      },
    });
  }

  /**
   * Check kiểu percent hay number
   * @param controlName
   */
  isTypePercent(controlName: string): boolean {
    const control = this.serviceFeeGroupForm.get(controlName);
    return control?.value?.type === this.percentTransactionType || control?.value?.type === this.percentContractType;
  }

  /**
   * Check kiểu có phải kiểu Phái sinh hay không
   * isDerivative
   */
  isDerivative() {
    return this.serviceFeeGroupForm.get('typeOfTransactionFee')?.value === this.derivativeType;
  }

  /**
   * submitForm
   */
  submitForm() {
    // Process the form data
    const formData = this.serviceFeeGroupForm.value;

    const { feeGroupName } = formData;
    const ref = this.dialogService.openPopUp(GuaranteeConfirmComponent, {
      width: '370px',
      height: '275px',
      panelClass: [''],
      data: {
        action: 'createServiceFeeGroup',
        info: [feeGroupName.toString().toUpperCase()],
        title: ['MES-553'],
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-554',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
    });

    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((v: any) => {
        console.log('refclose', v);
      });
  }
}
