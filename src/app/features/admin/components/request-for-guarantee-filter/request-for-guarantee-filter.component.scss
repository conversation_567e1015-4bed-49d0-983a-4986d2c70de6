.request-for-guarantee-filter-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  .header-filter {
    padding: 16px 16px 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--color--other--divider);
  }

  .body-filter {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow: auto;

    .customer-list {
      display: flex;
      flex-direction: column;
      min-height: fit-content;
      max-height: 416px;

      .title {
        color: var(--color--text--default);
        font-feature-settings: 'clig' off, 'liga' off;
        padding: 12px 24px;
      }

      .search-box {
        margin: 8px 24px 17px;
        display: flex;
        position: relative;
        .input-search {
          padding: 10px 16px;
          padding-left: 32px;
          width: 100%;
        }

        .icon-search-cls {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 10px;
        }
      }

      .option-list-cls {
        display: flex;
        flex-direction: column;
        gap: 16px;
        overflow: auto;
        padding: 0 24px;

        .checkbox-cls-item {
          display: flex;
          align-items: center;

          ::ng-deep {
            .mdc-label {
              font-size: 12px;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          ::ng-deep {
            .checkbox-cls {
              width: 100% !important;

              .mdc-form-field {
                width: 100% !important;
              }
            }
          }
        }
      }
    }

    .from-to {
      display: flex;
      flex-direction: column;
      width: 100%;

      .title {
        padding: 12px 24px;
      }

      .guarantee-money-content {
        display: flex;
        gap: 8px;
        padding: 8px 24px 16px;

        .content-from,
        .content-to {
          width: 50%;
          display: flex;
          flex-direction: column;
          gap: 4px;

          input {
            border: 1px solid var(--Neutral-100, #dbdee0);
            padding: 10px 16px;
            border-radius: 8px;
            height: 40px;
          }
        }
      }
    }

    .request-guarantee-status {
      display: flex;
      flex-direction: column;
      border-bottom: 1px solid var(--color--other--divider);

      .title {
        padding: 12px 24px;
      }

      .option-list-cls {
        display: flex;
        flex-direction: column;
        .checkbox-cls {
          padding: 8px 24px;

          ::ng-deep {
            .mdc-label {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .footer-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--color--other--divider);
    padding: 16px 24px;

    .btn {
      padding: 12px 16px;
      border-radius: 8px;
      border: 1px solid var(--color--other--divider);
    }

    .apply {
      background-color: var(--color--brand--500);
      color: var(--color--neutral--white);
    }
  }
}
