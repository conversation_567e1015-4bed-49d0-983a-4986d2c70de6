<div class="request-for-guarantee-filter-wrapper">
  <!-- HEADER -->
  <div class="header-filter">
    <div class="typo-body-16">{{ 'MES-19' | translate }}</div>
    <img mat-dialog-close class="img-cls" src="./assets/icons/x-cross.svg" alt="x-cross" />
  </div>

  <div class="body-filter">
    <div class="customer-list">
      <div class="title typo-body-15">{{ 'MES-96' | translate }}</div>

      <div class="search-box">
        <input
          type="text"
          class="input-search input-style-common typo-body-12 fs-12"
          [placeholder]="'MES-14' | translate"
          [formControl]="customerNameControl"
        />
        <img src="./assets/icons/search-normal.svg" alt="search-normal" class="icon-search-cls" />
      </div>

      <div class="option-list-cls">
        @for (item of listFilterCustomerNameOptions; track item.accountNumber) {
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="changeSections($event.checked, item)" [checked]="item.isSelect" class="checkbox-cls">
            {{ item.name | translate }} - {{ item.accountNumber | translate }}</mat-checkbox
          >
        </div>
        }
      </div>
    </div>

    <div class="from-to">
      <div class="title typo-body-15">{{ 'MES-294' | translate }}</div>
      <div class="guarantee-money-content">
        <div class="content-from">
          <div class="from-label typo-body-12">{{ 'MES-209' | translate }}</div>
          <input
            [mask]="'separator.2'"
            [allowNegativeNumbers]="true"
            class="from-to-input typo-field-5"
            [placeholder]="0"
            [formControl]="startBailMoney"
          />
        </div>

        <div class="content-to">
          <div class="to-label typo-body-12">{{ 'MES-210' | translate }}</div>
          <input
            [mask]="'separator.2'"
            [allowNegativeNumbers]="true"
            class="from-to-input typo-field-5"
            [placeholder]="'∞'"
            [formControl]="endBailMoney"
          />
        </div>
      </div>
    </div>

    <div class="request-guarantee-status">
      <div class="title typo-body-15">{{ 'MES-576' | translate }}</div>
      <div class="option-list-cls">
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isPending = $event.checked" [checked]="isPending" class="checkbox-cls">{{
            'MES-577' | translate
          }}</mat-checkbox>
        </div>
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isConfirm = $event.checked" [checked]="isConfirm" class="checkbox-cls">{{
            'MES-578' | translate
          }}</mat-checkbox>
        </div>
        <div class="checkbox-cls-item">
          <mat-checkbox (change)="isCancel = $event.checked" [checked]="isCancel" class="checkbox-cls">{{
            'MES-579' | translate
          }}</mat-checkbox>
        </div>
      </div>
    </div>
  </div>

  <!-- FOOTER -->
  <div class="footer-filter">
    <div mat-dialog-close (click)="applyFilter()" class="btn apply typo-button-3">
      {{ 'MES-89' | translate }}
    </div>
    <div mat-dialog-close (click)="defaultFilter()" class="btn default typo-button-3">{{ 'MES-74' | translate }}</div>
  </div>
</div>
