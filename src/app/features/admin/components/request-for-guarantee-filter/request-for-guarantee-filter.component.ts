import { Component, Inject, Optional } from '@angular/core';
import { DestroyService, LoadingService } from 'src/app/core/services';
import { PopoverService } from 'src/app/shared/components/popover/popover.service';
import { RequestForGuaranteeContainer } from '../../containers/request-for-guarantee/request-for-guarantee.container';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormControl } from '@angular/forms';
import { PopoverRef } from 'src/app/shared/components/popover/popover-ref';
import { Store } from '@ngrx/store';
import { debounceTime, startWith, takeUntil, tap } from 'rxjs';
import { IOptionList } from 'src/app/features/customers/components/customer-group-filter/customer-group-filter.component';
import { IRangeFilter } from 'src/app/features/assets/models/asset';
import { IFilterRequestGuaranteeParam, requestGuaranteeStatus } from '../../models/admin';

const FAKE_LIST_OF_CUSTOMER = [
  {
    accountNumber: '069C-000000-00',
    customerName: 'Nguyễn Văn A',
  },
  {
    accountNumber: '069C-000000-01',
    customerName: 'Nguyễn Văn B',
  },
  {
    accountNumber: '069C-000000-02',
    customerName: 'Nguyễn Văn C',
  },
];

@Component({
  selector: 'app-request-for-guarantee-filter',
  templateUrl: './request-for-guarantee-filter.component.html',
  styleUrl: './request-for-guarantee-filter.component.scss',
})
export class RequestForGuaranteeFilterComponent {
  customerNameControl = new FormControl();
  listFilterCustomerNameOptions: any[] = [];

  private _customerNameOption!: IOptionList[];

  startBailMoney = new FormControl();
  endBailMoney = new FormControl();

  isPending = true;
  isConfirm = true;
  isCancel = true;
  /**
   * Constructor
   * @param _destroy
   * @param popoverService
   * @param dialogRef
   * @param fb
   * @param popoverRef
   * @param data
   * @param store
   * @param loadingService
   */
  constructor(
    private _destroy: DestroyService,
    private popoverService: PopoverService,
    public dialogRef: MatDialogRef<RequestForGuaranteeContainer>,
    private fb: FormBuilder,
    @Optional() @Inject(PopoverRef) private popoverRef: PopoverRef,
    @Inject(MAT_DIALOG_DATA) public data: IFilterRequestGuaranteeParam,
    private store: Store,
    private loadingService: LoadingService
  ) {
    if (this.popoverRef) {
      Object.assign(this, this.popoverRef.componentConfig);
    }

    const { accountNumber, bailMoney, approveStatus } = this.data;
    this.updateListOfCustomer(accountNumber);
    this.updateFormControlValue(bailMoney, this.startBailMoney, this.endBailMoney);
    this.isPending = approveStatus.includes(requestGuaranteeStatus.PENDING);
    this.isConfirm = approveStatus.includes(requestGuaranteeStatus.CONFIRM);
    this.isCancel = approveStatus.includes(requestGuaranteeStatus.CANCEL);
  }

  /**
   * ngOnInit
   */
  ngOnInit() {
    this.customerNameControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        tap((value) => {
          this.listFilterCustomerNameOptions = this._filter(value || '', this._customerNameOption);
        }),
        takeUntil(this._destroy)
      )
      .subscribe();

    // REMOVE when have api
    //   this.store
    //   .select(selectCustomerList$)
    //   .pipe(takeUntil(this._destroy))
    //   .subscribe((personalList) => {
    //     this._customerNameOptions = personalList.map((customer) => ({
    //       name: customer.customerName,
    //       accountNumber: customer.accountNumber,
    //       value: customer.brokerId,
    //     }));

    //     this.listFilterCustomerNameOptions = this._customerNameOption;
    //   });

    // if (this.data.accountNumber.length > 0) {
    //   const newDataCustomerFilter = this.listFilterCustomerNameOptions.map((r) => {
    //     const customerSelected = this.data.accountNumber.includes(r.accountNumber ?? '');
    //     if (customerSelected) {
    //       r.isSelect = true;
    //     } else r.isSelect = false;
    //     return r;
    //   });
    //   this.listFilterCustomerNameOptions = structuredClone(newDataCustomerFilter);
    // }
  }

  /**
   * updateListOfCustomer
   * @param {string[]} accountNumber
   */
  updateListOfCustomer(accountNumber: string[]) {
    this._customerNameOption = FAKE_LIST_OF_CUSTOMER.map((customer) => ({
      name: customer.customerName,
      accountNumber: customer.accountNumber,
    }));

    this.listFilterCustomerNameOptions = this._customerNameOption;

    if (accountNumber.length > 0) {
      const newDataNewCustomerFilter = this.listFilterCustomerNameOptions.map((c) => {
        const customerSelected = accountNumber.includes(c.accountNumber);
        c.isSelect = customerSelected ? true : false;
        return c;
      });
      this.listFilterCustomerNameOptions = structuredClone(newDataNewCustomerFilter);
    }
  }

  /**
   * Hàm thay đổi chọn trong list column
   * @param checked Giá trị thay đổi
   * @param item Item thay đổi
   */
  changeSections(checked: boolean, item?: IOptionList) {
    if (item) {
      item.isSelect = checked;
    }
  }

  /**
   * Inner filter function
   * @param {string} value search value
   * @param {IOptionList[]} options
   */
  private _filter(value: string, options: IOptionList[]): IOptionList[] {
    const filterValue = value.toLowerCase();

    return options.filter(
      (option) =>
        option.name?.toLowerCase().includes(filterValue) || option.accountNumber?.toLowerCase().includes(filterValue)
    );
  }

  /**
   * UpdateFormControlValue
   * @param date date
   * @param startControl startControl
   * @param endControl endControl
   */
  updateFormControlValue(date: IRangeFilter, startControl: FormControl, endControl: FormControl) {
    const { start, end } = date;
    startControl.patchValue(start);
    endControl.patchValue(end);
  }

  /**
   * applyFilter
   */
  applyFilter() {
    this.loadingService.show();
    const selectedCustomers = this.listFilterCustomerNameOptions.filter((option) => option.isSelect);
    const accountNumber: string[] = selectedCustomers
      .filter((customer) => customer.isSelect)
      .map((c) => c.accountNumber);

    const bailMoney = {
      start: this.startBailMoney.value,
      end: this.endBailMoney.value,
    };

    const approveStatus: number[] = [];
    if (this.isPending && this.isConfirm && this.isCancel)
      approveStatus.push(requestGuaranteeStatus.PENDING, requestGuaranteeStatus.CONFIRM, requestGuaranteeStatus.CANCEL);
    else if (this.isPending) approveStatus.push(requestGuaranteeStatus.PENDING);
    else if (this.isConfirm) approveStatus.push(requestGuaranteeStatus.CONFIRM);
    else approveStatus.push(requestGuaranteeStatus.CANCEL);

    const optionFilter = {
      accountNumber,
      bailMoney,
      approveStatus,
    };

    this.dialogRef.close({
      type: 'save',
      optionFilter,
    });
  }

  /**
   * defaultFilter
   */
  defaultFilter() {
    this.dialogRef.close();
  }
}
