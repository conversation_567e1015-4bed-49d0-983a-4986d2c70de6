import { createReducer, on } from '@ngrx/store';
import { IAdminState } from '../models/admin';
import * as AdminActions from './admin.actions';
export const initialAdminState: IAdminState = {
  searchValue: '',
  addCustomerRank: {
    nameRank: null,
    avrCash: null,
    finalCash: null,
    avrCollateral: null,
    finalCollateral: null,
    avrGmv: null,
    avrSpin: null,
    avrDebt: null,
    finalDebt: null,
    avrProfit: null,
    rank01: null,
    rank02: null,
    loanRate: null,
    loanInterestRate: null,
    borrowLimit: null,
    marginCallRate: null,
    forceSellRate: null,
    totalProportion: null,
  },
  filterRequestGuarantee: {
    accountNumber: [],
    bailMoney: {
      start: null,
      end: null,
    },
    approveStatus: [0, 1, 2],
    isFilter: false,
  },
};
export const adminReducers = createReducer<IAdminState>(
  initialAdminState,

  on(
    AdminActions.search,
    (state, action): IAdminState => ({
      ...state,
      searchValue: action.data,
    })
  ),

  on(
    AdminActions.setAddCustomerRank,
    (state, action): IAdminState => ({
      ...state,
      addCustomerRank: action.params,
    })
  ),

  on(
    AdminActions.resetAddCustomerRank,
    (state): IAdminState => ({
      ...state,
      addCustomerRank: { ...initialAdminState.addCustomerRank },
    })
  ),

  on(
    AdminActions.setFilterRequestGuarantee,
    (state, action): IAdminState => ({
      ...state,
      filterRequestGuarantee: { ...action.params },
    })
  ),

  on(
    AdminActions.resetFilterRequestGuarantee,
    (state): IAdminState => ({
      ...state,
      filterRequestGuarantee: {
        ...initialAdminState.filterRequestGuarantee,
      },
    })
  )
);
