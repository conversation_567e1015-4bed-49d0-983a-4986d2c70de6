import { createFeatureSelector, createSelector } from '@ngrx/store';
import { IAdminState } from '../models/admin';

export const ADMIN_STATE_NAME = 'ADMIN';

export const selectAdminState = createFeatureSelector<IAdminState>(ADMIN_STATE_NAME);

export const selectSearchValue$ = createSelector(selectAdminState, (state) => state?.searchValue);

export const selectAddCustomerRank$ = createSelector(selectAdminState, (state) => state?.addCustomerRank);

export const selectFilterRequestGuarantee$ = createSelector(selectAdminState, (state) => state?.filterRequestGuarantee);
