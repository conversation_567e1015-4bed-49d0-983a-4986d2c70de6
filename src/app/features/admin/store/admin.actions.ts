import { createAction, props } from '@ngrx/store';
import { ICustomRankConfirmParam, IFilterRequestGuaranteeParam } from '../models/admin';

export const search = createAction('[Admin] search', props<{ data: string }>());

export const setAddCustomerRank = createAction(
  '[Admin] create add customer rank',
  props<{ params: ICustomRankConfirmParam }>()
);

export const resetAddCustomerRank = createAction('[Admin] reset customer rank');

export const setFilterRequestGuarantee = createAction(
  '[Request Guarantee] set filter request for guarantee',
  props<{ params: IFilterRequestGuaranteeParam }>()
);

export const resetFilterRequestGuarantee = createAction(
  '[Request Guarantee] reset filter  request for guarantee default'
);
