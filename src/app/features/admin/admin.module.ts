import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltip } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { NgxMaskDirective } from 'src/app/shared/directives/mask/ngx-mask.directive';
import { NumberFormatPipe } from 'src/app/shared/pipes/format-number/format-number.pipe';
import { AdminView } from './views/admin.view';
import { AdminRoutingModule } from './admin-routing.module';
import { CustomerClassContainer } from './containers/customer-class/customer-class.container';
import { GridComponent } from 'src/app/shared/components/table-custom/grid.component';
import { ActionBtnComponent } from 'src/app/shared/components/action-btn/action-btn.component';
import { SearchDropdownCustomComponent } from 'src/app/shared/components/search-dropdown-custom/search-dropdown-custom.component';
import { FilterComponent } from 'src/app/shared/components/filter/filter.component';
import { NumberOnlyDirective } from 'src/app/shared/directives/number-only/number-only.directive';
import { InputProportionComponent } from 'src/app/shared/components/input-custom-for-table/input-proportion/input-proportion.component';
import { InputProportionDropdownComponent } from 'src/app/shared/components/input-custom-for-table/input-proportion-dropdown/input-proportion-dropdown.component';
import { InputNumberCustomComponent } from 'src/app/shared/components/input-custom-for-table/input-number-custom/input-number-custom.component';
import { BaseTransactionFeeContainer } from './containers/base-transaction-fee/base-transaction-fee.container';
import { DerivativesTransactionFeeContainer } from './containers/derivatives-transaction-fee/derivatives-transaction-fee.container';
import { BondTransactionFeeContainer } from './containers/bond-transaction-fee/bond-transaction-fee.container';
import { CustomerRankComponent } from './components/customer-rank/cusomer-rank.component';
import { FormControlComponent } from '../../shared/components/form-control/form-control.component';
import { StoreModule } from '@ngrx/store';
import { ADMIN_STATE_NAME } from './store/admin.selections';
import { adminReducers } from './store/admin.reducers';
import { InputDropdownTableCustomComponent } from 'src/app/shared/components/input-custom-for-table/input-dropdown-table-custom/input-dropdown-table-custom.component';
import { InputNumberAndDropdownComponent } from 'src/app/shared/components/input-custom-for-table/input-number-and-dropdown/input-number-and-dropdown.component';
import { UnsaveChangeGuard } from 'src/app/core/guards/unsaved-changes.guard';
import { ServiceFeeGroupComponent } from './components/service-fee-group/service-fee-group.component';
import { PhoneNumberTableComponent } from 'src/app/shared/components/phone-number-table/phone-number-table.component';
import { CalendarCustomComponent } from 'src/app/shared/components/date-picker/date-picker/date-picker.component';
import { AccountOptionsComponent } from './components/account-options/account-options.component';
import { AccountAuthorizationContainer } from './containers/account-authorization/account-authorization.container';
import { RoleGroupContainer } from './containers/role-group/role-group.container';
import { MarginTransactionFeeContainer } from './containers/margin-transaction-fee/margin-transaction-fee.container';
import { DepartmentsContainer } from './containers/departments/departments.container';
import { MatTreeModule } from '@angular/material/tree';
import { CdkTreeModule } from '@angular/cdk/tree';
import { RequestForGuaranteeContainer } from './containers/request-for-guarantee/request-for-guarantee.container';
import { RequestForGuaranteeFilterComponent } from './components/request-for-guarantee-filter/request-for-guarantee-filter.component';

const VIEWS = [AdminView];

const CONTAINERS = [
  CustomerClassContainer,
  BaseTransactionFeeContainer,
  DerivativesTransactionFeeContainer,
  BondTransactionFeeContainer,
  AccountAuthorizationContainer,
  RoleGroupContainer,
  MarginTransactionFeeContainer,
  DepartmentsContainer,
  RequestForGuaranteeContainer,
];

const SHARED = [
  GridComponent,
  ActionBtnComponent,
  SearchDropdownCustomComponent,
  FilterComponent,
  NumberOnlyDirective,
  InputProportionComponent,
  InputProportionDropdownComponent,
  InputNumberCustomComponent,
  InputDropdownTableCustomComponent,
  InputNumberAndDropdownComponent,
  FormControlComponent,
  PhoneNumberTableComponent,
  CalendarCustomComponent,
];

const COMPONENT = [
  CustomerRankComponent,
  ServiceFeeGroupComponent,
  AccountOptionsComponent,
  RequestForGuaranteeFilterComponent,
];

/**
 * Customer Module
 */
@NgModule({
  declarations: [...VIEWS, ...CONTAINERS, ...COMPONENT],
  imports: [
    ...SHARED,
    NgxMaskDirective,
    CommonModule,
    TranslateModule,
    AdminRoutingModule,
    // Material Module
    MatTreeModule,
    CdkTreeModule,
    MatTableModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    FormsModule,
    MatSelectModule,
    MatDividerModule,
    MatDatepickerModule,
    MatMenuModule,
    MatDialogModule,
    OverlayModule,
    MatIconModule,
    MatCheckboxModule,
    MatTabsModule,
    MatTooltip,
    NumberFormatPipe,
    FormControlComponent,
    StoreModule.forFeature(ADMIN_STATE_NAME, adminReducers),
  ],
  providers: [UnsaveChangeGuard],
})
export class AdminModulde {}
