import { IRangeFilter } from '../../assets/models/asset';

export enum ETypeCustomerClass {
  PERCENT, // 7,00%
  NUMBER, // 3.000.000.000
  DROPDOWN, // STANDARD  | 5 : 5
  PROPORTION, // 12,05 %/năm
  TOTAL, // 100%
  PERCENTSUM, // 7,00% nhưng để tính tổng
}

export interface ICustomerClass {
  name: string;
  normal: null | { value: number | string | null; type: ETypeCustomerClass };
  gold: null | { value: number | string | null; type: ETypeCustomerClass };
  diamond: null | { value: number | string | null; type: ETypeCustomerClass };
  platinum: null | { value: number | string | null; type: ETypeCustomerClass };
  [key: string]: any | { value: number | string | null; type: ETypeCustomerClass };
  children?: ICustomerClass[];
}

export interface ITransactionFee {
  name: string;
  standard: { value: number; type: ETypeCustomerClass; unit?: number };
  vip: { value: number; type: ETypeCustomerClass; unit?: number };
  vvip: { value: number; type: ETypeCustomerClass; unit?: number };
  svip: { value: number; type: ETypeCustomerClass; unit?: number };
}

export enum ECustomerRank {
  STANDARD = 'STANDARD',
  VIP = 'VIP',
  VVIP = 'VVIP',
  SVIP = 'SVIP',
}

export enum ELoanRate {
  RATE01 = '5 : 5',
  RATE02 = '5.5 : 4.5',
  RATE03 = '6 : 4',
}

export interface ICustomRankConfirmParam {
  nameRank: string | null;
  avrCash: number | null;
  finalCash: number | null;
  avrCollateral: number | null;
  finalCollateral: number | null;
  avrGmv: number | null;
  avrSpin: number | null;
  avrDebt: number | null;
  finalDebt: number | null;
  avrProfit: number | null;
  rank01: ECustomerRank | null;
  rank02: ECustomerRank | null;
  loanRate: ELoanRate | null;
  loanInterestRate: number | null;
  borrowLimit: number | null;
  marginCallRate: number | null;
  forceSellRate: number | null;
  totalProportion: number | null;
}

export interface IAdminState {
  searchValue: string;
  addCustomerRank: ICustomRankConfirmParam;
  filterRequestGuarantee: IFilterRequestGuaranteeParam;
}

export enum ELevelFeature {
  MENU = 'menu',
  PAGE = 'page',
  ACTION = 'action',
  FEATURE = 'feature',
}

export enum requestGuaranteeStatus {
  PENDING,
  CONFIRM,
  CANCEL,
}

export interface IFilterRequestGuaranteeParam {
  accountNumber: string[];
  bailMoney: IRangeFilter;
  approveStatus: number[];
  isFilter: boolean;
}
