import { IOption } from 'src/app/shared/models/dropdown-item.model';
import { ECustomerRank } from '../models/admin';
import { ETypeValueInput } from 'src/app/shared/components/input-custom-for-table/input-proportion/input-proportion';

export const CONVERT_FEE_GROUP_TO_LABEL: { [key: number]: string } = {
  0: 'KH Thường',
};

export const CONVERT_STOCK_MARKET_TYPE: { [key: string]: string } = {
  ALL: 'Tất cả',
  UPCOM: 'UPCOM',
  HOSE: 'HOSE',
  HNX: 'HNX',
};

export const CONVERT_TITLE_TO_CLASS: { [key: string]: string } = {
  STANDARD: 'standard-cls',
  VVIP: 'vvip-cls',
  VIP: 'vip-cls',
  SVIP: 'svip-cls',
};

export enum ETypeOfNav {
  PERCENT,
  NUMBER,
}

export const CONVERT_UNIT_TRANSACTION_TO_LABEL: { [key: number]: string } = {
  [ETypeOfNav.PERCENT]: '% /giao dịch',
  [ETypeOfNav.NUMBER]: 'VND /giao dịch',
};

export const CONVERT_UNIT_CONTRACT_TO_LABLE: { [key: number]: string } = {
  [ETypeOfNav.PERCENT]: '% /hợp đồng',
  [ETypeOfNav.NUMBER]: 'VND /hợp đồng',
};

export const CONVERT_RATIO_TO_CLASS: { [key: string]: string } = {
  '5 : 5': 'yellow-bg',
  '5.5 : 4.5': 'yellow-bg',
  '6: 4': 'yellow-bg',
};

export const CustomerRankMap = ['STANDARD', 'VVIP', 'VIP', 'SVIP'];
export const RatioMap = ['5 : 5', '5.5 : 4.5', '6 : 4'];

export const CUSTOMER_RANK_OPTION: IOption[] = [
  {
    label: ECustomerRank.STANDARD,
    value: ECustomerRank.STANDARD,
  },
  {
    label: ECustomerRank.VIP,
    value: ECustomerRank.VIP,
  },
  {
    label: ECustomerRank.VVIP,
    value: ECustomerRank.VVIP,
  },
  {
    label: ECustomerRank.SVIP,
    value: ECustomerRank.SVIP,
  },
];

export const RATIO_OPTION: IOption[] = [
  {
    label: '5 : 5',
    value: '5 : 5',
  },
  {
    label: '5.5 : 4.5',
    value: '5.5 : 4.5',
  },
  {
    label: '6 : 4',
    value: '6 : 4',
  },
];

export const typeOfNavPerTransactionOptions = [
  {
    label: '% /giao dịch',
    value: ETypeValueInput.PERCENT,
  },
  {
    label: 'VND /giao dịch',
    value: ETypeValueInput.NUMBER,
  },
];

export const typeOfNavPerContractOptions = [
  {
    label: '% /hợp đồng',
    value: ETypeValueInput.PERCENT,
  },
  {
    label: 'VND /hợp đồng',
    value: ETypeValueInput.NUMBER,
  },
];

export enum ETypeOfTransactionFee {
  BASE,
  DERIVATIVE,
  BOND,
}

export const typeOfTransactionFee = [
  {
    label: 'Cơ sở',
    value: ETypeOfTransactionFee.BASE,
  },
  {
    label: 'Phái sinh',
    value: ETypeOfTransactionFee.DERIVATIVE,
  },
  {
    label: 'Trái phiếu',
    value: ETypeOfTransactionFee.BOND,
  },
];

export enum ERole {
  SUPERADMIN = 'super admin',
  BACKADMIN = 'back admin',
  BROKERADMIN = 'broker admin',
  BACK = 'back',
  BROKER = 'broker',
  REMISER = 'remiser',
}

export const CONVERT_ROLE_TO_LABEL: { [key: string]: string } = {
  [ERole.SUPERADMIN]: 'SUPER ADMIN',
  [ERole.BACKADMIN]: 'BACK ADMIN',
  [ERole.BROKERADMIN]: 'BROKER ADMIN',
  [ERole.BACK]: 'BACK',
  [ERole.BROKER]: 'BROKER',
  [ERole.REMISER]: 'REMISER',
};

export interface ERoleGroupData {
  permissionGroupName: string;
  menuFeature: {
    menu: {
      name: string;
      isSelect?: boolean;
    };
    page: {
      name: string;
      isSelect?: boolean;
    };
  };
  detailFeature: {
    action: {
      name: string;
      isSelect?: boolean;
    };
    feature: {
      name: string;
      isSelect?: boolean;
    };
  };
}

export enum ERoleOrganization {
  SUPERADMIN = 'super admin',
  ADMIN = 'admin',
  LEADER = 'leader',
  BROKER = 'broker',
  COLLABORATOR = 'collaborator',
}

export const CONVERT_ROLE_ORGANIZATION_TO_ICON: { [key: string]: string } = {
  [ERoleOrganization.SUPERADMIN]: 'assets/icons/home.svg',
  [ERoleOrganization.ADMIN]: 'assets/icons/user-octagon.svg',
  [ERoleOrganization.LEADER]: 'assets/icons/people.svg',
  [ERoleOrganization.BROKER]: 'assets/icons/user.svg',
  [ERoleOrganization.COLLABORATOR]: 'assets/icons/profile-circle-stroke.svg',
};
