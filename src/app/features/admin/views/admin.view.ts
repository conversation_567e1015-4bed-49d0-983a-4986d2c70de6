import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { NavigationEnd, NavigationStart, Router } from '@angular/router';
import { debounceTime, filter, takeUntil, tap } from 'rxjs';
import { DestroyService, DialogService } from 'src/app/core/services';
import { CustomerRankComponent } from '../components/customer-rank/cusomer-rank.component';
import { ServiceFeeGroupComponent } from '../components/service-fee-group/service-fee-group.component';
import { Store } from '@ngrx/store';
import { search } from '../store/admin.actions';

/**
 * AdminView
 */
@Component({
  selector: 'app-admin',
  templateUrl: './admin.view.html',
  styleUrl: './admin.view.scss',
})
export class AdminView {
  searchTextControl = new FormControl();
  private searchSubscription: any;
  private firstRouteChange = true;

  selectedTab: number = 0;
  activeTab = 0;
  menuAdmin = [
    {
      router: 'request-for-guarantee',
      name: 'MES-573',
      nameIcon: 'menu:request-for-guarantee',
    },
    {
      router: 'customer-class',
      name: 'MES-310',
      nameIcon: 'menu:customer-class',
    },
    {
      router: 'base-transaction-fee',
      name: 'MES-311',
      nameIcon: 'menu:base-transaction-fee',
    },
    {
      router: 'derivatives-transaction-fee',
      name: 'MES-312',
      nameIcon: 'menu:derivatives-transaction-fee',
    },
    {
      router: 'bond-transaction-fee',
      name: 'MES-313',
      nameIcon: 'menu:bond-transaction-fee',
    },
    {
      router: 'margin-transaction-fee',
      name: 'MES-560',
      nameIcon: 'menu:money-icon',
    },
    {
      router: 'account-authorization',
      name: 'MES-556',
      nameIcon: 'menu:account-authorization',
    },
    {
      router: 'role-group',
      name: 'MES-559',
      nameIcon: 'menu:role-group',
    },
    {
      router: 'departments',
      name: 'MES-481',
      nameIcon: 'menu:departments',
    },
  ];

  /**
   * Find active route on page reload
   * @param store
   * @param router
   * @param dialogService
   * @param _destroy
   */
  constructor(
    private store: Store,
    private router: Router,
    private dialogService: DialogService,
    private _destroy: DestroyService
  ) {
    this.updateActiveTab(router.url);

    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this._destroy)
      )
      .subscribe((event: any) => {
        this.updateActiveTab(event.urlAfterRedirects);
      });
  }

  /**
   * ngOnInit
   */
  ngOnInit(): void {
    this.listenSearchValueChange();
    this.resetSearchControlOnRouteChange();
  }

  /**
   * Update active tab index based on current URL
   * @param url - Current URL
   */
  updateActiveTab(url: string) {
    this.activeTab = this.menuAdmin.findIndex((item) => url.includes(item.router));
  }

  /**
   * handle open customer rank dialog
   */
  openCustomerRankDialog() {
    this.dialogService.openRightDialog(CustomerRankComponent, {
      width: '532px',
      data: {
        k: 'hehe',
      },
    });
  }

  /**
   * createServiceGroup
   */
  createServiceFeeGroup() {
    this.dialogService.openRightDialog(ServiceFeeGroupComponent, {
      width: '594px',
    });
  }

  /**
   * listenSearchValueChange
   */
  listenSearchValueChange(): void {
    this.searchSubscription = this.searchTextControl.valueChanges
      .pipe(
        debounceTime(300),

        tap((value) => {
          this.store.dispatch(search({ data: value }));
        }),
        takeUntil(this._destroy)
      )
      .subscribe();
  }

  /**
   * resetSearchControlOnRouteChange
   */
  resetSearchControlOnRouteChange(): void {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationStart),
        tap(() => {
          if (this.firstRouteChange) {
            // Temporarily disable the valueChanges subscription
            if (this.searchSubscription) {
              this.searchSubscription.unsubscribe();
            }

            // Reset search control and update the store
            this.searchTextControl.setValue('');
            this.store.dispatch(search({ data: '' }));

            // Re-enable the valueChanges subscription
            this.listenSearchValueChange();

            // Set the flag to false after the first reset
            this.firstRouteChange = false;
          }
        }),
        takeUntil(this._destroy)
      )
      .subscribe((event: any) => {
        this.updateActiveTab(event.url);
      });
  }
}
