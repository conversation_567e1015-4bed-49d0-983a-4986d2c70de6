<div class="admin-view-containers">
  <div class="admin-header-cls">
    <div class="header-txt typo-heading-8">{{'MES-572' | translate}}</div>
    <div class="header-btn-wrapper">
      <div class="header-btn typo-button-5" (click)="openCustomerRankDialog()">
        <img src="./assets/icons/add-item.svg" alt="add-item" />
        {{'MES-314' | translate}}
      </div>
      <div class="header-btn typo-button-5" (click)="createServiceFeeGroup()">
        <img src="./assets/icons/add-item.svg" alt="add-item" />
        {{'MES-315' | translate}}
      </div>
    </div>
  </div>
  <div class="sub-menu-cls">
    <!-- <div class="menu-admin-cls">
      <a
        class="box-selection"
        mat-list-item
        routerLinkActive="isSelect"
        [routerLink]="item.router"
        *ngFor="let item of menuAdmin"
      >
        <mat-icon
          class="mat-icon-cls"
          aria-hidden="false"
          aria-label="icon"
          [svgIcon]="item.nameIcon"
          [ngClass]="item.router"
        ></mat-icon>
        <div class="typo-body-6">{{item.name | translate}}</div>
      </a>
    </div> -->

    <mat-tab-group animationDuration="200ms" [selectedIndex]="activeTab">
      <mat-tab *ngFor="let item of menuAdmin" isActive="item.isActive">
        <ng-template mat-tab-label>
          <div class="menu-admin-cls">
            <a class="box-selection" mat-list-item routerLinkActive="isSelect" [routerLink]="item.router">
              <mat-icon
                class="mat-icon-cls"
                aria-hidden="false"
                aria-label="icon"
                [svgIcon]="item.nameIcon"
                [ngClass]="item.router"
              ></mat-icon>
              <div class="typo-body-6">{{item.name | translate}}</div>
            </a>
          </div>
        </ng-template>
      </mat-tab>
    </mat-tab-group>

    <div class="search-cls">
      <img class="search-icon" src="./assets/icons/search-normal.svg" alt="search-normal" />
      <input
        [formControl]="searchTextControl"
        class="input-cls input-style-common typo-body-12"
        type="text"
        [placeholder]="'MES-14' | translate"
      />
    </div>
  </div>
  <div class="router-container-cls"><router-outlet></router-outlet></div>
</div>
