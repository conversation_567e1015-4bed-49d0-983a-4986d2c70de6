import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AdminView } from './views/admin.view';
import { CustomerClassContainer } from './containers/customer-class/customer-class.container';
import { BaseTransactionFeeContainer } from './containers/base-transaction-fee/base-transaction-fee.container';
import { DerivativesTransactionFeeContainer } from './containers/derivatives-transaction-fee/derivatives-transaction-fee.container';
import { BondTransactionFeeContainer } from './containers/bond-transaction-fee/bond-transaction-fee.container';
import { UnsaveChangeGuard } from 'src/app/core/guards/unsaved-changes.guard';
import { AccountAuthorizationContainer } from './containers/account-authorization/account-authorization.container';
import { RoleGroupContainer } from './containers/role-group/role-group.container';
import { MarginTransactionFeeContainer } from './containers/margin-transaction-fee/margin-transaction-fee.container';
import { DepartmentsContainer } from './containers/departments/departments.container';
import { RequestForGuaranteeContainer } from './containers/request-for-guarantee/request-for-guarantee.container';

const routes: Routes = [
  {
    path: '',
    component: AdminView,
    children: [
      {
        path: '',
        redirectTo: 'customer-class',
        pathMatch: 'full',
      },
      {
        path: 'customer-class',
        component: CustomerClassContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'base-transaction-fee',
        component: BaseTransactionFeeContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'derivatives-transaction-fee',
        component: DerivativesTransactionFeeContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'bond-transaction-fee',
        component: BondTransactionFeeContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'margin-transaction-fee',
        component: MarginTransactionFeeContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'account-authorization',
        component: AccountAuthorizationContainer,
        canDeactivate: [UnsaveChangeGuard],
      },
      {
        path: 'role-group',
        component: RoleGroupContainer,
      },
      {
        path: 'departments',
        component: DepartmentsContainer,
      },
      {
        path: 'request-for-guarantee',
        component: RequestForGuaranteeContainer,
      },
    ],
  },
];
/**
 * Configures and manages asset routes.
 */
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminRoutingModule {}
