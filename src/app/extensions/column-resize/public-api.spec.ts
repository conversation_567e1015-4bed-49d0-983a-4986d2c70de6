import * as PublicApi from './public-api';

describe('Public API', () => {
  it('No.1: should export ColumnResize from column-resize', () => {
    expect(PublicApi.ColumnResize).toBeDefined();
    expect(typeof PublicApi.ColumnResize).toBe('function');
  });

  it('No.2: should export CdkColumnResize from column-resize-directives', () => {
    expect(PublicApi.CdkColumnResize).toBeDefined();
    expect(typeof PublicApi.CdkColumnResize).toBe('function');
  });

  it('No.3: should export CdkColumnResizeFlex from column-resize-directives', () => {
    expect(PublicApi.CdkColumnResizeFlex).toBeDefined();
    expect(typeof PublicApi.CdkColumnResizeFlex).toBe('function');
  });

  it('No.4: should export CdkColumnResizeModule from column-resize-module', () => {
    expect(PublicApi.CdkColumnResizeModule).toBeDefined();
    expect(typeof PublicApi.CdkColumnResizeModule).toBe('function');
  });

  it('No.5: should export ColumnSize interface from column-resize-notifier', () => {
    // Interfaces are not available at runtime, but we can check if the export exists
    // by checking if it's imported without error
    expect(() => {
      const { ColumnSize } = require('./public-api');
    }).not.toThrow();
  });

  it('No.6: should export ColumnSizeAction interface from column-resize-notifier', () => {
    // Interfaces are not available at runtime, but we can check if the export exists
    expect(() => {
      const { ColumnSizeAction } = require('./public-api');
    }).not.toThrow();
  });

  it('No.7: should export ColumnResizeNotifierSource from column-resize-notifier', () => {
    expect(PublicApi.ColumnResizeNotifierSource).toBeDefined();
    expect(typeof PublicApi.ColumnResizeNotifierSource).toBe('function');
  });

  it('No.8: should export ColumnResizeNotifier from column-resize-notifier', () => {
    expect(PublicApi.ColumnResizeNotifier).toBeDefined();
    expect(typeof PublicApi.ColumnResizeNotifier).toBe('function');
  });

  it('No.9: should export ColumnSizeStore from column-size-store', () => {
    expect(PublicApi.ColumnSizeStore).toBeDefined();
    expect(typeof PublicApi.ColumnSizeStore).toBe('function');
  });

  it('No.10: should export HeaderRowEventDispatcher from event-dispatcher', () => {
    expect(PublicApi.HeaderRowEventDispatcher).toBeDefined();
    expect(typeof PublicApi.HeaderRowEventDispatcher).toBe('function');
  });

  it('No.11: should export Resizable from resizable', () => {
    expect(PublicApi.Resizable).toBeDefined();
    expect(typeof PublicApi.Resizable).toBe('function');
  });

  it('No.12: should export ResizeRef from resize-ref', () => {
    expect(PublicApi.ResizeRef).toBeDefined();
    expect(typeof PublicApi.ResizeRef).toBe('function');
  });

  it('No.13: should export ResizeStrategy from resize-strategy', () => {
    expect(PublicApi.ResizeStrategy).toBeDefined();
    expect(typeof PublicApi.ResizeStrategy).toBe('function');
  });

  it('No.14: should export TableLayoutFixedResizeStrategy from resize-strategy', () => {
    expect(PublicApi.TableLayoutFixedResizeStrategy).toBeDefined();
    expect(typeof PublicApi.TableLayoutFixedResizeStrategy).toBe('function');
  });

  it('No.15: should export CdkFlexTableResizeStrategy from resize-strategy', () => {
    expect(PublicApi.CdkFlexTableResizeStrategy).toBeDefined();
    expect(typeof PublicApi.CdkFlexTableResizeStrategy).toBe('function');
  });

  it('No.16: should export TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER from resize-strategy', () => {
    expect(PublicApi.TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER).toBeDefined();
    expect(typeof PublicApi.TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER).toBe('object');
  });

  it('No.17: should export FLEX_RESIZE_STRATEGY_PROVIDER from resize-strategy', () => {
    expect(PublicApi.FLEX_RESIZE_STRATEGY_PROVIDER).toBeDefined();
    expect(typeof PublicApi.FLEX_RESIZE_STRATEGY_PROVIDER).toBe('object');
  });

  it('No.18: should export ResizeOverlayHandle from overlay-handle', () => {
    expect(PublicApi.ResizeOverlayHandle).toBeDefined();
    expect(typeof PublicApi.ResizeOverlayHandle).toBe('function');
  });

  it('No.19: should export TABLE_PROVIDERS from constants', () => {
    expect(PublicApi.TABLE_PROVIDERS).toBeDefined();
    expect(Array.isArray(PublicApi.TABLE_PROVIDERS)).toBe(true);
  });

  it('No.20: should export FLEX_PROVIDERS from constants', () => {
    expect(PublicApi.FLEX_PROVIDERS).toBeDefined();
    expect(Array.isArray(PublicApi.FLEX_PROVIDERS)).toBe(true);
  });
});
