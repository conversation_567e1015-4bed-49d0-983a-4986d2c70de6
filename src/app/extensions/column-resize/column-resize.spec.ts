import { ElementRef, Ng<PERSON>one } from '@angular/core';
import { Subject } from 'rxjs';
import { ColumnResize } from './column-resize';
import { ColumnResizeNotifier, ColumnResizeNotifierSource } from './column-resize-notifier';
import { HeaderRowEventDispatcher } from './event-dispatcher';

// Concrete implementation for testing the abstract class
class TestColumnResize extends ColumnResize {
  constructor(
    public readonly columnResizeNotifier: ColumnResizeNotifier,
    public readonly elementRef: ElementRef<HTMLElement>,
    protected readonly eventDispatcher: <PERSON>er<PERSON>owEventDispatcher,
    protected readonly ngZone: NgZone,
    protected readonly notifier: ColumnResizeNotifierSource
  ) {
    super();
  }
}

describe('ColumnResize', () => {
  let directive: TestColumnResize;
  let mockColumnResizeNotifier: jasmine.SpyObj<ColumnResizeNotifier>;
  let mockElementRef: jasmine.SpyObj<ElementRef<HTMLElement>>;
  let mockEventDispatcher: jasmine.SpyObj<HeaderRowEventDispatcher>;
  let mockNgZone: jasmine.SpyObj<NgZone>;
  let mockNotifier: jasmine.SpyObj<ColumnResizeNotifierSource>;
  let mockElement: HTMLElement;

  beforeEach(() => {
    // Create a real DOM element for testing
    mockElement = document.createElement('div');
    spyOn(mockElement.classList, 'add');
    spyOn(mockElement.classList, 'remove');

    mockColumnResizeNotifier = jasmine.createSpyObj('ColumnResizeNotifier', ['resize']);
    mockElementRef = jasmine.createSpyObj('ElementRef', [], {
      nativeElement: mockElement
    });
    const headerRowSubject = new Subject<Element | null>();
    mockEventDispatcher = jasmine.createSpyObj('HeaderRowEventDispatcher', ['headerCellHovered'], {
      headerRowHoveredOrActiveDistinct: headerRowSubject.asObservable(),
      overlayHandleActiveForCell: new Subject()
    });
    // Store reference to the subject for testing
    (mockEventDispatcher as any)._headerRowSubject = headerRowSubject;
    mockNgZone = jasmine.createSpyObj('NgZone', ['runOutsideAngular']);
    mockNotifier = jasmine.createSpyObj('ColumnResizeNotifierSource', ['getTriggerResize'], {
      triggerResize: new Subject(),
      resizeCompleted: new Subject()
    });

    // Mock ngZone.runOutsideAngular to execute the callback immediately
    mockNgZone.runOutsideAngular.and.callFake((fn: Function) => fn());

    directive = new TestColumnResize(
      mockColumnResizeNotifier,
      mockElementRef,
      mockEventDispatcher,
      mockNgZone,
      mockNotifier
    );
  });

  it('No.1: should be defined as an abstract Directive', () => {
    expect(directive).toBeTruthy();
    expect(directive instanceof ColumnResize).toBe(true);
  });

  it('No.2: should implement AfterViewInit interface', () => {
    expect(typeof directive.ngAfterViewInit).toBe('function');
  });

  it('No.3: should implement OnDestroy interface', () => {
    expect(typeof directive.ngOnDestroy).toBe('function');
  });

  it('No.4: should initialize destroyed Subject', () => {
    expect(directive['destroyed']).toBeDefined();
    expect(directive['destroyed'] instanceof Subject).toBe(true);
  });

  it('No.5: should have abstract columnResizeNotifier property', () => {
    expect(directive.columnResizeNotifier).toBe(mockColumnResizeNotifier);
  });

  it('No.6: should have abstract elementRef property', () => {
    expect(directive.elementRef).toBe(mockElementRef);
  });

  it('No.7: should have abstract eventDispatcher property', () => {
    expect(directive['eventDispatcher']).toBe(mockEventDispatcher);
  });

  it('No.8: should have abstract ngZone property', () => {
    expect(directive['ngZone']).toBe(mockNgZone);
  });

  it('No.9: should have abstract notifier property', () => {
    expect(directive['notifier']).toBe(mockNotifier);
  });

  it('No.10: should generate unique selectorId', () => {
    const directive2 = new TestColumnResize(
      mockColumnResizeNotifier,
      mockElementRef,
      mockEventDispatcher,
      mockNgZone,
      mockNotifier
    );

    expect(directive['selectorId']).toBeDefined();
    expect(directive2['selectorId']).toBeDefined();
    expect(directive['selectorId']).not.toBe(directive2['selectorId']);
  });

  it('No.11: should have optional id property', () => {
    expect(directive.id).toBeUndefined();
    directive.id = 'test-id';
    expect(directive.id).toBe('test-id');
  });

  it('No.12: should add unique CSS class in ngAfterViewInit', () => {
    directive.ngAfterViewInit();

    expect(mockElement.classList.add).toHaveBeenCalledWith(
      jasmine.stringMatching(/^cdk-column-resize-\d+$/)
    );
  });

  it('No.13: should call private methods in ngAfterViewInit', () => {
    spyOn(directive as any, '_listenForRowHoverEvents');
    spyOn(directive as any, '_listenForResizeActivity');
    spyOn(directive as any, '_listenForHoverActivity');

    directive.ngAfterViewInit();

    expect(directive['_listenForRowHoverEvents']).toHaveBeenCalled();
    expect(directive['_listenForResizeActivity']).toHaveBeenCalled();
    expect(directive['_listenForHoverActivity']).toHaveBeenCalled();
  });

  it('No.14: should complete destroyed Subject in ngOnDestroy', () => {
    spyOn(directive['destroyed'], 'next');
    spyOn(directive['destroyed'], 'complete');

    directive.ngOnDestroy();

    expect(directive['destroyed'].next).toHaveBeenCalled();
    expect(directive['destroyed'].complete).toHaveBeenCalled();
  });

  it('No.15: should return correct unique CSS class from getUniqueCssClass', () => {
    const cssClass = directive.getUniqueCssClass();

    expect(cssClass).toMatch(/^cdk-column-resize-\d+$/);
    expect(cssClass).toContain('cdk-column-resize-');
  });

  it('No.16: should add resized CSS class in setResized method', () => {
    directive.setResized();

    expect(mockElement.classList.add).toHaveBeenCalledWith('cdk-column-resize-with-resized-column');
  });

  it('No.17: should listen for mouseover events in _listenForRowHoverEvents', () => {
    directive.ngAfterViewInit();

    expect(mockNgZone.runOutsideAngular).toHaveBeenCalled();
  });

  it('No.18: should listen for mouseleave events in _listenForRowHoverEvents', () => {
    directive.ngAfterViewInit();

    expect(mockNgZone.runOutsideAngular).toHaveBeenCalled();
  });

  it('No.19: should call setResized when resize activity is detected', () => {
    spyOn(directive, 'setResized');

    directive.ngAfterViewInit();

    // Trigger resize activity
    mockNotifier.triggerResize.next({
      columnId: 'test',
      size: 100,
      completeImmediately: true
    });

    expect(directive.setResized).toHaveBeenCalled();
  });

  it('No.20: should manage hover CSS classes in _listenForHoverActivity', () => {
    const mockRow1 = jasmine.createSpyObj('HTMLElement', [], {
      classList: jasmine.createSpyObj('DOMTokenList', ['add', 'remove'])
    });
    const mockRow2 = jasmine.createSpyObj('HTMLElement', [], {
      classList: jasmine.createSpyObj('DOMTokenList', ['add', 'remove'])
    });

    directive.ngAfterViewInit();

    // Simulate hover events
    (mockEventDispatcher as any)._headerRowSubject.next(mockRow1);
    (mockEventDispatcher as any)._headerRowSubject.next(mockRow2);

    expect(mockRow1.classList.add).toHaveBeenCalledWith('cdk-column-resize-hover-or-active');
    expect(mockRow1.classList.remove).toHaveBeenCalledWith('cdk-column-resize-hover-or-active');
    expect(mockRow2.classList.add).toHaveBeenCalledWith('cdk-column-resize-hover-or-active');
  });
});
