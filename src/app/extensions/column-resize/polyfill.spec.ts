import { closest } from './polyfill';

describe('Polyfill', () => {
  let testContainer: HTMLElement;

  beforeEach(() => {
    // Create a test DOM structure
    testContainer = document.createElement('div');
    testContainer.innerHTML = `
      <div class="parent" id="parent">
        <div class="child" data-test="child">
          <span class="grandchild">Text content</span>
        </div>
      </div>
    `;
    document.body.appendChild(testContainer);
  });

  afterEach(() => {
    document.body.removeChild(testContainer);
  });

  it('No.1: should export closest function', () => {
    expect(closest).toBeDefined();
    expect(typeof closest).toBe('function');
  });

  it('No.2: should return null when element is null', () => {
    const result = closest(null, '.test-selector');
    expect(result).toBeNull();
  });

  it('No.3: should return null when element is undefined', () => {
    const result = closest(undefined, '.test-selector');
    expect(result).toBeNull();
  });

  it('No.4: should return null when element is not a Node instance', () => {
    const nonNode = { notANode: true } as any;
    const result = closest(nonNode, '.test-selector');
    expect(result).toBeNull();
  });

  it('No.5: should find closest matching element when element is an Element', () => {
    const childElement = testContainer.querySelector('.child') as Element;
    const result = closest(childElement, '.parent');
    
    expect(result).not.toBeNull();
    expect(result?.classList.contains('parent')).toBe(true);
  });

  it('No.6: should return null when no matching ancestor is found', () => {
    const childElement = testContainer.querySelector('.child') as Element;
    const result = closest(childElement, '.non-existent-class');
    
    expect(result).toBeNull();
  });

  it('No.7: should traverse up the DOM tree to find Element parent', () => {
    const spanElement = testContainer.querySelector('.grandchild') as Element;
    const textNode = spanElement.firstChild as Text;
    
    const result = closest(textNode, '.child');
    
    expect(result).not.toBeNull();
    expect(result?.classList.contains('child')).toBe(true);
  });

  it('No.8: should handle Text nodes correctly', () => {
    const spanElement = testContainer.querySelector('.grandchild') as Element;
    const textNode = spanElement.firstChild as Text;
    
    const result = closest(textNode, '.parent');
    
    expect(result).not.toBeNull();
    expect(result?.classList.contains('parent')).toBe(true);
  });

  it('No.9: should handle Comment nodes correctly', () => {
    const commentNode = document.createComment('test comment');
    const childElement = testContainer.querySelector('.child') as Element;
    childElement.appendChild(commentNode);
    
    const result = closest(commentNode, '.child');
    
    expect(result).not.toBeNull();
    expect(result?.classList.contains('child')).toBe(true);
  });

  it('No.10: should return null when Node has no Element ancestor', () => {
    const orphanTextNode = document.createTextNode('orphan');
    const result = closest(orphanTextNode, '.any-selector');
    
    expect(result).toBeNull();
  });

  it('No.11: should handle complex CSS selectors', () => {
    const grandchildElement = testContainer.querySelector('.grandchild') as Element;
    
    // Test with ID selector
    const resultById = closest(grandchildElement, '#parent');
    expect(resultById?.id).toBe('parent');
    
    // Test with attribute selector
    const resultByAttr = closest(grandchildElement, '[data-test="child"]');
    expect(resultByAttr?.getAttribute('data-test')).toBe('child');
    
    // Test with class selector
    const resultByClass = closest(grandchildElement, '.parent');
    expect(resultByClass?.classList.contains('parent')).toBe(true);
  });

  it('No.12: should handle EventTarget that is an Element', () => {
    const childElement = testContainer.querySelector('.child') as Element;
    const eventTarget = childElement as EventTarget;
    
    const result = closest(eventTarget, '.parent');
    
    expect(result).not.toBeNull();
    expect(result?.classList.contains('parent')).toBe(true);
  });

  it('No.13: should handle EventTarget that is not an Element', () => {
    const nonElementEventTarget = new EventTarget();
    const result = closest(nonElementEventTarget, '.any-selector');
    
    expect(result).toBeNull();
  });

  it('No.14: should use optional chaining safely', () => {
    const orphanNode = document.createTextNode('orphan');
    
    // This should not throw even though curr becomes null
    expect(() => {
      const result = closest(orphanNode, '.any-selector');
      expect(result).toBeNull();
    }).not.toThrow();
  });

  it('No.15: should return the element itself if it matches the selector', () => {
    const childElement = testContainer.querySelector('.child') as Element;
    const result = closest(childElement, '.child');
    
    expect(result).toBe(childElement);
    expect(result?.classList.contains('child')).toBe(true);
  });
});
