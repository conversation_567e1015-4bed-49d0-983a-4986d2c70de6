import { 
  HEADER_CELL_SELECTOR, 
  HEADER_ROW_SELECTOR, 
  RESIZE_OVERLAY_SELECTOR 
} from './selectors';

describe('Selectors', () => {
  it('No.1: should export HEADER_CELL_SELECTOR constant', () => {
    expect(HEADER_CELL_SELECTOR).toBeDefined();
    expect(typeof HEADER_CELL_SELECTOR).toBe('string');
  });

  it('No.2: should export HEADER_ROW_SELECTOR constant', () => {
    expect(HEADER_ROW_SELECTOR).toBeDefined();
    expect(typeof HEADER_ROW_SELECTOR).toBe('string');
  });

  it('No.3: should export RESIZE_OVERLAY_SELECTOR constant', () => {
    expect(RESIZE_OVERLAY_SELECTOR).toBeDefined();
    expect(typeof RESIZE_OVERLAY_SELECTOR).toBe('string');
  });

  it('No.4: should have correct value for HEADER_CELL_SELECTOR', () => {
    expect(HEADER_CELL_SELECTOR).toBe('.cdk-header-cell, .mat-header-cell');
  });

  it('No.5: should have correct value for HEADER_ROW_SELECTOR', () => {
    expect(HEADER_ROW_SELECTOR).toBe('.cdk-header-row, .mat-header-row');
  });

  it('No.6: should have correct value for RESIZE_OVERLAY_SELECTOR', () => {
    expect(RESIZE_OVERLAY_SELECTOR).toBe('.mat-column-resize-overlay-thumb');
  });

  it('No.7: should include both CDK and Material selectors for header cell', () => {
    expect(HEADER_CELL_SELECTOR).toContain('.cdk-header-cell');
    expect(HEADER_CELL_SELECTOR).toContain('.mat-header-cell');
  });

  it('No.8: should include both CDK and Material selectors for header row', () => {
    expect(HEADER_ROW_SELECTOR).toContain('.cdk-header-row');
    expect(HEADER_ROW_SELECTOR).toContain('.mat-header-row');
  });

  it('No.9: should use comma-separated format for multiple selectors', () => {
    expect(HEADER_CELL_SELECTOR).toContain(', ');
    expect(HEADER_ROW_SELECTOR).toContain(', ');
  });

  it('No.10: should use CSS class selector format', () => {
    expect(HEADER_CELL_SELECTOR.startsWith('.')).toBe(true);
    expect(HEADER_ROW_SELECTOR.startsWith('.')).toBe(true);
    expect(RESIZE_OVERLAY_SELECTOR.startsWith('.')).toBe(true);
  });

  it('No.11: should be immutable string constants', () => {
    const originalHeaderCell = HEADER_CELL_SELECTOR;
    const originalHeaderRow = HEADER_ROW_SELECTOR;
    const originalResizeOverlay = RESIZE_OVERLAY_SELECTOR;

    // Verify constants haven't changed
    expect(HEADER_CELL_SELECTOR).toBe(originalHeaderCell);
    expect(HEADER_ROW_SELECTOR).toBe(originalHeaderRow);
    expect(RESIZE_OVERLAY_SELECTOR).toBe(originalResizeOverlay);
  });

  it('No.12: should have consistent naming convention', () => {
    expect(HEADER_CELL_SELECTOR).toBeDefined();
    expect(HEADER_ROW_SELECTOR).toBeDefined();
    expect(RESIZE_OVERLAY_SELECTOR).toBeDefined();
    
    // All should end with _SELECTOR
    const selectorNames = ['HEADER_CELL_SELECTOR', 'HEADER_ROW_SELECTOR', 'RESIZE_OVERLAY_SELECTOR'];
    selectorNames.forEach(name => {
      expect(name.endsWith('_SELECTOR')).toBe(true);
    });
  });

  it('No.13: should be valid CSS selectors', () => {
    // Test that these can be used with querySelector (basic validation)
    expect(() => {
      document.createElement('div').querySelector(HEADER_CELL_SELECTOR);
    }).not.toThrow();

    expect(() => {
      document.createElement('div').querySelector(HEADER_ROW_SELECTOR);
    }).not.toThrow();

    expect(() => {
      document.createElement('div').querySelector(RESIZE_OVERLAY_SELECTOR);
    }).not.toThrow();
  });

  it('No.14: should support both CDK and Material Design components', () => {
    // Verify that both CDK and Material selectors are present
    const headerCellParts = HEADER_CELL_SELECTOR.split(', ');
    const headerRowParts = HEADER_ROW_SELECTOR.split(', ');

    expect(headerCellParts).toContain('.cdk-header-cell');
    expect(headerCellParts).toContain('.mat-header-cell');
    expect(headerRowParts).toContain('.cdk-header-row');
    expect(headerRowParts).toContain('.mat-header-row');
  });

  it('No.15: should have specific Material Design selector for resize overlay', () => {
    expect(RESIZE_OVERLAY_SELECTOR).toBe('.mat-column-resize-overlay-thumb');
    expect(RESIZE_OVERLAY_SELECTOR).toContain('mat-column-resize-overlay-thumb');
  });
});
