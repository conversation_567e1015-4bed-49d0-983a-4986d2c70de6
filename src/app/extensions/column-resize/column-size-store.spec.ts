import { TestBed } from '@angular/core/testing';
import { Injectable } from '@angular/core';
import { ColumnSizeStore } from './column-size-store';

// Concrete implementation for testing the abstract class
@Injectable()
class TestColumnSizeStore extends ColumnSizeStore {
  private readonly storage = new Map<string, number>();

  getSize(tableId: string, columnId: string): number {
    const key = `${tableId}-${columnId}`;
    return this.storage.get(key) ?? 0;
  }

  setSize(tableId: string, columnId: string): void {
    // Note: The abstract class signature doesn't include size parameter
    // This is likely a design issue in the original code
    const key = `${tableId}-${columnId}`;
    this.storage.set(key, 100); // Default size for testing
  }
}

describe('ColumnSizeStore', () => {
  let store: TestColumnSizeStore;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        { provide: ColumnSizeStore, useClass: TestColumnSizeStore }
      ]
    });
    store = new TestColumnSizeStore();
  });

  it('No.1: should be defined as an abstract Injectable service', () => {
    expect(ColumnSizeStore).toBeDefined();
    expect(typeof ColumnSizeStore).toBe('function');
    // Verify it's abstract by checking it can be extended
    expect(store instanceof ColumnSizeStore).toBe(true);
  });

  it('No.2: should have abstract getSize method', () => {
    expect(typeof store.getSize).toBe('function');
    expect(store.getSize.length).toBe(2); // Should accept 2 parameters
  });

  it('No.3: should have abstract setSize method', () => {
    expect(typeof store.setSize).toBe('function');
    expect(store.setSize.length).toBe(2); // Should accept 2 parameters (tableId, columnId)
  });

  it('No.4: should return number from getSize method', () => {
    const result = store.getSize('table1', 'column1');
    expect(typeof result).toBe('number');
  });

  it('No.5: should accept string tableId parameter in getSize', () => {
    expect(() => {
      store.getSize('testTable', 'column1');
    }).not.toThrow();
  });

  it('No.6: should accept string columnId parameter in getSize', () => {
    expect(() => {
      store.getSize('table1', 'testColumn');
    }).not.toThrow();
  });

  it('No.7: should accept string tableId parameter in setSize', () => {
    expect(() => {
      store.setSize('testTable', 'column1');
    }).not.toThrow();
  });

  it('No.8: should accept string columnId parameter in setSize', () => {
    expect(() => {
      store.setSize('table1', 'testColumn');
    }).not.toThrow();
  });

  it('No.9: should return void from setSize method', () => {
    const result = store.setSize('table1', 'column1');
    expect(result).toBeUndefined();
  });

  it('No.10: should be extendable by concrete implementations', () => {
    expect(store instanceof ColumnSizeStore).toBe(true);
    expect(store instanceof TestColumnSizeStore).toBe(true);
  });

  it('No.11: should be injectable as a service', () => {
    expect(() => {
      TestBed.configureTestingModule({
        providers: [{ provide: ColumnSizeStore, useClass: TestColumnSizeStore }]
      });
      const injectedStore = TestBed.inject(ColumnSizeStore);
      expect(injectedStore).toBeTruthy();
    }).not.toThrow();
  });

  it('No.12: should support different table IDs', () => {
    store.setSize('table1', 'column1');
    store.setSize('table2', 'column1');

    expect(store.getSize('table1', 'column1')).toBe(100);
    expect(store.getSize('table2', 'column1')).toBe(100);
  });

  it('No.13: should support different column IDs', () => {
    store.setSize('table1', 'column1');
    store.setSize('table1', 'column2');

    expect(store.getSize('table1', 'column1')).toBe(100);
    expect(store.getSize('table1', 'column2')).toBe(100);
  });

  it('No.14: should maintain method signatures correctly', () => {
    // Test getSize returns number
    const size = store.getSize('table1', 'column1');
    expect(typeof size).toBe('number');

    // Test setSize returns void
    const result = store.setSize('table1', 'column1');
    expect(result).toBeUndefined();
  });

  it('No.15: should be designed for persistence functionality', () => {
    // Test persistence behavior
    store.setSize('table1', 'column1');
    const retrievedSize = store.getSize('table1', 'column1');

    expect(retrievedSize).toBe(100);
    expect(typeof retrievedSize).toBe('number');
  });
});
