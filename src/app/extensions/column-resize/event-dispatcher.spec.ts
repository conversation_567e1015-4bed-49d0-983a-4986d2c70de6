import { NgZone } from '@angular/core';
import { Subject } from 'rxjs';
import { HeaderRowEventDispatcher } from './event-dispatcher';

describe('HeaderRowEventDispatcher', () => {
  let service: HeaderRowEventDispatcher;
  let mockNgZone: jasmine.SpyObj<NgZone>;
  let mockHeaderRow: HTMLElement;
  let mockHeaderCell: HTMLElement;

  beforeEach(() => {
    mockNgZone = jasmine.createSpyObj('NgZone', ['run']);
    mockNgZone.run.and.callFake((fn: Function) => fn());

    // Create mock DOM elements
    mockHeaderRow = document.createElement('tr');
    mockHeaderRow.classList.add('cdk-header-row');

    mockHeaderCell = document.createElement('th');
    mockHeaderCell.classList.add('cdk-column-test');
    mockHeaderRow.appendChild(mockHeaderCell);

    service = new HeaderRowEventDispatcher(mockNgZone);
  });

  it('No.1: should be defined as an Injectable service', () => {
    expect(service).toBeTruthy();
    expect(service instanceof HeaderRowEventDispatcher).toBe(true);
  });

  it('No.2: should have headerCellHovered Subject property', () => {
    expect(service.headerCellHovered).toBeDefined();
    expect(service.headerCellHovered instanceof Subject).toBe(true);
  });

  it('No.3: should have overlayHandleActiveForCell Subject property', () => {
    expect(service.overlayHandleActiveForCell).toBeDefined();
    expect(service.overlayHandleActiveForCell instanceof Subject).toBe(true);
  });

  it('No.4: should have headerCellHoveredDistinct observable property', () => {
    expect(service.headerCellHoveredDistinct).toBeDefined();
    expect(typeof service.headerCellHoveredDistinct.subscribe).toBe('function');
  });

  it('No.5: should have headerRowHoveredOrActiveDistinct observable property', () => {
    expect(service.headerRowHoveredOrActiveDistinct).toBeDefined();
    expect(typeof service.headerRowHoveredOrActiveDistinct.subscribe).toBe('function');
  });

  it('No.6: should emit values through headerCellHovered Subject', () => {
    let emittedValue: Element | null = null;

    service.headerCellHovered.subscribe(value => emittedValue = value);
    service.headerCellHovered.next(mockHeaderCell);

    expect(emittedValue).toBeTruthy();
    expect((emittedValue as unknown as HTMLElement)?.tagName).toBe('TH');
  });

  it('No.7: should emit values through overlayHandleActiveForCell Subject', () => {
    let emittedValue: Element | null = null;

    service.overlayHandleActiveForCell.subscribe(value => emittedValue = value);
    service.overlayHandleActiveForCell.next(mockHeaderCell);

    expect(emittedValue).toBeTruthy();
    expect((emittedValue as unknown as HTMLElement)?.tagName).toBe('TH');
  });

  it('No.8: should provide resizeOverlayVisibleForHeaderRow method', () => {
    expect(typeof service.resizeOverlayVisibleForHeaderRow).toBe('function');
  });

  it('No.9: should accept Element parameter in resizeOverlayVisibleForHeaderRow', () => {
    expect(() => {
      const result = service.resizeOverlayVisibleForHeaderRow(mockHeaderRow);
      expect(typeof result.subscribe).toBe('function');
    }).not.toThrow();
  });

  it('No.10: should have _enterZone private method', () => {
    expect(typeof service['_enterZone']).toBe('function');
  });

  it('No.11: should maintain _lastSeenRow private property', () => {
    expect(service['_lastSeenRow']).toBeDefined();
  });

  it('No.12: should properly inject NgZone dependency', () => {
    expect(service['_ngZone']).toBe(mockNgZone);
  });

  it('No.13: should handle null values in headerCellHovered', () => {
    expect(() => {
      service.headerCellHovered.next(null);
    }).not.toThrow();
  });

  it('No.14: should handle null values in overlayHandleActiveForCell', () => {
    expect(() => {
      service.overlayHandleActiveForCell.next(null);
    }).not.toThrow();
  });

  it('No.15: should be injectable through Angular DI system', () => {
    expect(service).toBeTruthy();
    expect(service instanceof HeaderRowEventDispatcher).toBe(true);
  });

  // Additional comprehensive tests for better coverage

  it('should emit distinct values only in headerCellHoveredDistinct', () => {
    const emittedValues: (Element | null)[] = [];

    service.headerCellHoveredDistinct.subscribe(value => emittedValues.push(value));

    // Emit same value multiple times
    service.headerCellHovered.next(mockHeaderCell);
    service.headerCellHovered.next(mockHeaderCell);
    service.headerCellHovered.next(null);
    service.headerCellHovered.next(null);
    service.headerCellHovered.next(mockHeaderCell);

    // Should only emit distinct values
    expect(emittedValues.length).toBe(3); // cell, null, cell
  });

  it('should combine hovered and active states correctly', () => {
    const emittedValues: (Element | null)[] = [];

    service.headerRowHoveredOrActiveDistinct.subscribe(value => emittedValues.push(value));

    // Emit hovered cell
    service.headerCellHovered.next(mockHeaderCell);

    // Emit active cell (should take precedence)
    service.overlayHandleActiveForCell.next(mockHeaderCell);

    // Clear active (should fall back to hovered)
    service.overlayHandleActiveForCell.next(null);

    expect(emittedValues.length).toBeGreaterThan(0);
  });

  it('should cache row hover observables correctly', () => {
    const row1 = document.createElement('tr');
    const row2 = document.createElement('tr');

    const observable1a = service.resizeOverlayVisibleForHeaderRow(row1);
    const observable1b = service.resizeOverlayVisibleForHeaderRow(row1);
    const observable2 = service.resizeOverlayVisibleForHeaderRow(row2);

    // Same row should return same observable
    expect(observable1a).toBe(observable1b);
    // Different row should return different observable
    expect(observable1a).not.toBe(observable2);
  });

  it('should update _lastSeenRow when new row is provided', () => {
    const row1 = document.createElement('tr');
    const row2 = document.createElement('tr');

    service.resizeOverlayVisibleForHeaderRow(row1);
    expect(service['_lastSeenRow']).toBe(row1);

    service.resizeOverlayVisibleForHeaderRow(row2);
    expect(service['_lastSeenRow']).toBe(row2);
  });

  it('should emit correct visibility for header row', () => {
    let isVisible: boolean | undefined;

    const observable = service.resizeOverlayVisibleForHeaderRow(mockHeaderRow);
    observable.subscribe(visible => isVisible = visible);

    // Hover over a cell in the row
    service.headerCellHovered.next(mockHeaderCell);

    expect(isVisible).toBe(true);
  });

  it('should handle _enterZone operator correctly', () => {
    const testSubject = new Subject<string>();
    const enterZoneOperator = service['_enterZone']<string>();
    const zonedObservable = testSubject.pipe(enterZoneOperator);

    let receivedValue: string | undefined;
    zonedObservable.subscribe(value => receivedValue = value);

    testSubject.next('test-value');

    expect(mockNgZone.run).toHaveBeenCalled();
    expect(receivedValue).toBe('test-value');
  });

  it('should handle errors in _enterZone operator', () => {
    const testSubject = new Subject<string>();
    const enterZoneOperator = service['_enterZone']<string>();
    const zonedObservable = testSubject.pipe(enterZoneOperator);

    let receivedError: any;
    zonedObservable.subscribe({
      error: err => receivedError = err
    });

    const testError = new Error('test error');
    testSubject.error(testError);

    expect(receivedError).toBe(testError);
  });

  it('should handle completion in _enterZone operator', () => {
    const testSubject = new Subject<string>();
    const enterZoneOperator = service['_enterZone']<string>();
    const zonedObservable = testSubject.pipe(enterZoneOperator);

    let completed = false;
    zonedObservable.subscribe({
      complete: () => completed = true
    });

    testSubject.complete();

    expect(completed).toBe(true);
  });

  it('should skip initial null emissions in headerRowHoveredOrActiveDistinct', () => {
    const emittedValues: (Element | null)[] = [];

    service.headerRowHoveredOrActiveDistinct.subscribe(value => emittedValues.push(value));

    // Initial emissions should be skipped
    expect(emittedValues.length).toBe(0);

    // First real emission
    service.headerCellHovered.next(mockHeaderCell);

    expect(emittedValues.length).toBeGreaterThan(0);
  });

  it('should prioritize active over hovered in headerRowHoveredOrActiveDistinct', () => {
    const emittedValues: (Element | null)[] = [];

    service.headerRowHoveredOrActiveDistinct.subscribe(value => emittedValues.push(value));

    // Set up hovered state
    service.headerCellHovered.next(mockHeaderCell);

    // Set up active state (should take precedence)
    const activeCell = document.createElement('th');
    const activeRow = document.createElement('tr');
    activeRow.classList.add('cdk-header-row');
    activeRow.appendChild(activeCell);

    service.overlayHandleActiveForCell.next(activeCell);

    // The last emitted value should correspond to the active row, not hovered
    expect(emittedValues.length).toBeGreaterThan(0);
  });

  it('should handle elements without parent header row', () => {
    const orphanCell = document.createElement('th');

    let isVisible: boolean | undefined;
    const observable = service.resizeOverlayVisibleForHeaderRow(mockHeaderRow);
    observable.subscribe(visible => isVisible = visible);

    // First emit a cell that IS in the header row to get initial value
    service.headerCellHovered.next(mockHeaderCell);
    expect(isVisible).toBe(true);

    // Then hover over orphan cell (not in a header row)
    service.headerCellHovered.next(orphanCell);

    // Should be false since orphan cell is not in mockHeaderRow
    expect(isVisible).toBe(false);
  });

  it('should share observables correctly', () => {
    // Test that observables are shared (multiple subscriptions get same values)
    const values1: (Element | null)[] = [];
    const values2: (Element | null)[] = [];

    service.headerCellHoveredDistinct.subscribe(value => values1.push(value));
    service.headerCellHoveredDistinct.subscribe(value => values2.push(value));

    service.headerCellHovered.next(mockHeaderCell);

    expect(values1).toEqual(values2);
  });
});
