import { TABLE_PROVIDERS, FLEX_PROVIDERS } from './constants';
import { ColumnResizeNotifier, ColumnResizeNotifierSource } from '../column-resize-notifier';
import { HeaderRowEventDispatcher } from '../event-dispatcher';
import {
  TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER,
  FLEX_RESIZE_STRATEGY_PROVIDER,
} from '../resize-strategy';

describe('Constants', () => {
  it('No.1: should export TABLE_PROVIDERS array', () => {
    expect(TABLE_PROVIDERS).toBeDefined();
    expect(Array.isArray(TABLE_PROVIDERS)).toBe(true);
  });

  it('No.2: should export FLEX_PROVIDERS array', () => {
    expect(FLEX_PROVIDERS).toBeDefined();
    expect(Array.isArray(FLEX_PROVIDERS)).toBe(true);
  });

  it('No.3: should include base PROVIDERS in TABLE_PROVIDERS', () => {
    expect(TABLE_PROVIDERS).toContain(ColumnResizeNotifier);
    expect(TABLE_PROVIDERS).toContain(HeaderRowEventDispatcher);
    expect(TABLE_PROVIDERS).toContain(ColumnResizeNotifierSource);
  });

  it('No.4: should include TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER in TABLE_PROVIDERS', () => {
    expect(TABLE_PROVIDERS).toContain(TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER);
  });

  it('No.5: should include base PROVIDERS in FLEX_PROVIDERS', () => {
    expect(FLEX_PROVIDERS).toContain(ColumnResizeNotifier);
    expect(FLEX_PROVIDERS).toContain(HeaderRowEventDispatcher);
    expect(FLEX_PROVIDERS).toContain(ColumnResizeNotifierSource);
  });

  it('No.6: should include FLEX_RESIZE_STRATEGY_PROVIDER in FLEX_PROVIDERS', () => {
    expect(FLEX_PROVIDERS).toContain(FLEX_RESIZE_STRATEGY_PROVIDER);
  });

  it('No.7: should have correct length for TABLE_PROVIDERS', () => {
    expect(TABLE_PROVIDERS.length).toBe(4);
  });

  it('No.8: should have correct length for FLEX_PROVIDERS', () => {
    expect(FLEX_PROVIDERS.length).toBe(4);
  });

  it('No.9: should contain ColumnResizeNotifier in both provider arrays', () => {
    expect(TABLE_PROVIDERS).toContain(ColumnResizeNotifier);
    expect(FLEX_PROVIDERS).toContain(ColumnResizeNotifier);
  });

  it('No.10: should contain HeaderRowEventDispatcher in both provider arrays', () => {
    expect(TABLE_PROVIDERS).toContain(HeaderRowEventDispatcher);
    expect(FLEX_PROVIDERS).toContain(HeaderRowEventDispatcher);
  });

  it('No.11: should contain ColumnResizeNotifierSource in both provider arrays', () => {
    expect(TABLE_PROVIDERS).toContain(ColumnResizeNotifierSource);
    expect(FLEX_PROVIDERS).toContain(ColumnResizeNotifierSource);
  });

  it('No.12: should have different strategy providers for TABLE and FLEX', () => {
    expect(TABLE_PROVIDERS).toContain(TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER);
    expect(TABLE_PROVIDERS).not.toContain(FLEX_RESIZE_STRATEGY_PROVIDER);
    
    expect(FLEX_PROVIDERS).toContain(FLEX_RESIZE_STRATEGY_PROVIDER);
    expect(FLEX_PROVIDERS).not.toContain(TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER);
  });

  it('No.13: should spread base PROVIDERS correctly in TABLE_PROVIDERS', () => {
    const baseProviders = [ColumnResizeNotifier, HeaderRowEventDispatcher, ColumnResizeNotifierSource];
    baseProviders.forEach(provider => {
      expect(TABLE_PROVIDERS).toContain(provider);
    });
    expect(TABLE_PROVIDERS).toContain(TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER);
  });

  it('No.14: should spread base PROVIDERS correctly in FLEX_PROVIDERS', () => {
    const baseProviders = [ColumnResizeNotifier, HeaderRowEventDispatcher, ColumnResizeNotifierSource];
    baseProviders.forEach(provider => {
      expect(FLEX_PROVIDERS).toContain(provider);
    });
    expect(FLEX_PROVIDERS).toContain(FLEX_RESIZE_STRATEGY_PROVIDER);
  });

  it('No.15: should maintain proper Provider type for all exported arrays', () => {
    // TypeScript type checking ensures this at compile time
    // Runtime verification that all elements are valid providers
    TABLE_PROVIDERS.forEach(provider => {
      expect(provider).toBeDefined();
      expect(typeof provider === 'function' || typeof provider === 'object').toBe(true);
    });

    FLEX_PROVIDERS.forEach(provider => {
      expect(provider).toBeDefined();
      expect(typeof provider === 'function' || typeof provider === 'object').toBe(true);
    });
  });
});
