import { TestBed } from '@angular/core/testing';
import { DOCUMENT } from '@angular/common';
import { CSP_NONCE } from '@angular/core';
import { CdkTable, _CoalescedStyleScheduler } from '@angular/cdk/table';
import {
  ResizeStrategy,
  TableLayoutFixedResizeStrategy,
  CdkFlexTableResizeStrategy,
  TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER,
  FLEX_RESIZE_STRATEGY_PROVIDER
} from './resize-strategy';
import { ColumnResize } from './column-resize';

// Import utility functions for testing
const coercePixelsFromCssValue = (cssValue: string): number => {
  return Number(cssValue.match(/(\d+)px/)?.[1]);
};

const getElementWidth = (element: HTMLElement) => {
  return coercePixelsFromCssValue(element.style.width) || element.offsetWidth;
};

const coercePixelsFromFlexValue = (flexValue: string | undefined): number => {
  return Number(flexValue?.match(/0 0\.01 (\d+)px/)?.[1]);
};

// Concrete implementation for testing the abstract ResizeStrategy class
class TestResizeStrategy extends ResizeStrategy {
  constructor(
    protected readonly columnResize: ColumnResize,
    protected readonly styleScheduler: _CoalescedStyleScheduler,
    protected readonly table: CdkTable<unknown>
  ) {
    super();
  }

  applyColumnSize(): void {
    // Test implementation
  }

  applyMinColumnSize(): void {
    // Test implementation
  }

  applyMaxColumnSize(): void {
    // Test implementation
  }
}

describe('ResizeStrategy', () => {
  let strategy: TestResizeStrategy;
  let mockColumnResize: jasmine.SpyObj<ColumnResize>;
  let mockStyleScheduler: jasmine.SpyObj<_CoalescedStyleScheduler>;
  let mockTable: jasmine.SpyObj<CdkTable<unknown>>;
  let mockElement: HTMLElement;

  beforeEach(() => {
    mockElement = document.createElement('div');
    mockElement.style.width = '100px';

    mockColumnResize = jasmine.createSpyObj('ColumnResize', ['setResized'], {
      elementRef: { nativeElement: mockElement }
    });
    mockStyleScheduler = jasmine.createSpyObj('_CoalescedStyleScheduler', ['schedule', 'scheduleEnd']);
    mockTable = jasmine.createSpyObj('CdkTable', ['updateStickyColumnStyles']);

    // Mock schedule to execute immediately
    mockStyleScheduler.schedule.and.callFake((fn: Function) => fn());
    mockStyleScheduler.scheduleEnd.and.callFake((fn: Function) => fn());

    strategy = new TestResizeStrategy(mockColumnResize, mockStyleScheduler, mockTable);
  });

  it('No.1: should be defined as an abstract Injectable service', () => {
    expect(ResizeStrategy).toBeDefined();
    expect(typeof ResizeStrategy).toBe('function');
  });

  it('No.2: should have abstract columnResize property', () => {
    expect(strategy['columnResize']).toBe(mockColumnResize);
  });

  it('No.3: should have abstract styleScheduler property', () => {
    expect(strategy['styleScheduler']).toBe(mockStyleScheduler);
  });

  it('No.4: should have abstract table property', () => {
    expect(strategy['table']).toBe(mockTable);
  });

  it('No.5: should have abstract applyColumnSize method', () => {
    expect(typeof strategy.applyColumnSize).toBe('function');
  });

  it('No.6: should have abstract applyMinColumnSize method', () => {
    expect(typeof strategy.applyMinColumnSize).toBe('function');
  });

  it('No.7: should have abstract applyMaxColumnSize method', () => {
    expect(typeof strategy.applyMaxColumnSize).toBe('function');
  });

  it('No.8: should initialize _pendingResizeDelta as null', () => {
    expect(strategy['_pendingResizeDelta']).toBeNull();
  });

  it('No.9: should update table width and sticky columns', () => {
    strategy['updateTableWidthAndStickyColumns'](50);

    expect(mockStyleScheduler.schedule).toHaveBeenCalled();
    expect(mockStyleScheduler.scheduleEnd).toHaveBeenCalled();
    expect(mockTable.updateStickyColumnStyles).toHaveBeenCalled();
  });

  it('No.10: should accumulate pending resize deltas', () => {
    strategy['updateTableWidthAndStickyColumns'](30);
    strategy['updateTableWidthAndStickyColumns'](20);

    expect(strategy['_pendingResizeDelta']).toBe(50);
  });
});

describe('TableLayoutFixedResizeStrategy', () => {
  let strategy: TableLayoutFixedResizeStrategy;
  let mockColumnResize: jasmine.SpyObj<ColumnResize>;
  let mockStyleScheduler: jasmine.SpyObj<_CoalescedStyleScheduler>;
  let mockTable: jasmine.SpyObj<CdkTable<unknown>>;
  let mockElement: HTMLElement;

  beforeEach(() => {
    mockElement = document.createElement('div');
    Object.defineProperty(mockElement, 'offsetWidth', { value: 100, configurable: true });

    mockColumnResize = jasmine.createSpyObj('ColumnResize', ['setResized'], {
      elementRef: { nativeElement: mockElement }
    });
    mockStyleScheduler = jasmine.createSpyObj('_CoalescedStyleScheduler', ['schedule', 'scheduleEnd']);
    mockTable = jasmine.createSpyObj('CdkTable', ['updateStickyColumnStyles']);

    mockStyleScheduler.schedule.and.callFake((fn: Function) => fn());
    mockStyleScheduler.scheduleEnd.and.callFake((fn: Function) => fn());

    strategy = new TableLayoutFixedResizeStrategy(mockColumnResize, mockStyleScheduler, mockTable);
  });

  it('No.11: should extend ResizeStrategy base class', () => {
    expect(strategy instanceof ResizeStrategy).toBe(true);
  });

  it('No.12: should properly initialize with dependencies', () => {
    expect(strategy['columnResize']).toBe(mockColumnResize);
    expect(strategy['styleScheduler']).toBe(mockStyleScheduler);
    expect(strategy['table']).toBe(mockTable);
  });

  it('No.13: should apply column size correctly', () => {
    strategy.applyColumnSize('test', mockElement, 150, 100);

    expect(mockElement.style.width).toBe('150px');
    expect(mockStyleScheduler.schedule).toHaveBeenCalled();
  });

  it('No.14: should skip resize when delta is zero', () => {
    strategy.applyColumnSize('test', mockElement, 100, 100);

    expect(mockStyleScheduler.schedule).not.toHaveBeenCalled();
  });

  it('No.15: should apply minimum column size', () => {
    spyOn(strategy, 'applyColumnSize');

    strategy.applyMinColumnSize('test', mockElement, 120);

    expect(strategy.applyColumnSize).toHaveBeenCalledWith('test', mockElement, 120, 100);
  });

  it('No.16: should apply maximum column size', () => {
    spyOn(strategy, 'applyColumnSize');

    strategy.applyMaxColumnSize('test', mockElement, 80);

    expect(strategy.applyColumnSize).toHaveBeenCalledWith('test', mockElement, 80, 100);
  });
});

describe('CdkFlexTableResizeStrategy', () => {
  let strategy: CdkFlexTableResizeStrategy;
  let mockDocument: jasmine.SpyObj<Document>;
  let mockColumnResize: jasmine.SpyObj<ColumnResize>;
  let mockStyleScheduler: jasmine.SpyObj<_CoalescedStyleScheduler>;
  let mockTable: jasmine.SpyObj<CdkTable<unknown>>;
  let mockElement: HTMLElement;
  let mockStyleElement: jasmine.SpyObj<HTMLStyleElement>;
  let mockStyleSheet: jasmine.SpyObj<CSSStyleSheet>;

  beforeEach(() => {
    mockElement = document.createElement('div');
    Object.defineProperty(mockElement, 'offsetWidth', { value: 100, configurable: true });

    const mockHead = jasmine.createSpyObj('HTMLHeadElement', ['appendChild']);
    mockStyleSheet = jasmine.createSpyObj('CSSStyleSheet', ['insertRule', 'deleteRule']);
    mockStyleElement = jasmine.createSpyObj('HTMLStyleElement', ['appendChild', 'remove'], {
      sheet: mockStyleSheet
    });

    mockDocument = jasmine.createSpyObj('Document', ['createElement', 'createTextNode'], {
      head: mockHead
    });
    mockDocument.createElement.and.returnValue(mockStyleElement);
    mockDocument.createTextNode.and.returnValue(document.createTextNode(''));

    mockColumnResize = jasmine.createSpyObj('ColumnResize', ['getUniqueCssClass'], {
      elementRef: { nativeElement: mockElement }
    });
    mockColumnResize.getUniqueCssClass.and.returnValue('unique-table-class');

    mockStyleScheduler = jasmine.createSpyObj('_CoalescedStyleScheduler', ['schedule', 'scheduleEnd']);
    mockStyleScheduler.schedule.and.callFake((fn: Function) => fn());
    mockStyleScheduler.scheduleEnd.and.callFake((fn: Function) => fn());

    mockTable = jasmine.createSpyObj('CdkTable', ['updateStickyColumnStyles']);

    TestBed.configureTestingModule({
      providers: [
        { provide: DOCUMENT, useValue: mockDocument },
        { provide: CSP_NONCE, useValue: 'test-nonce' }
      ]
    });
  });

  it('No.17: should extend ResizeStrategy and implement OnDestroy', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument,
      'test-nonce'
    );

    expect(strategy instanceof ResizeStrategy).toBe(true);
    expect(typeof strategy.ngOnDestroy).toBe('function');
  });

  it('No.18: should properly initialize with all dependencies', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument,
      'test-nonce'
    );

    expect(strategy['_document']).toBe(mockDocument);
    expect(strategy['_nonce']).toBe('test-nonce');
  });

  it('No.19: should initialize with default min and max sizes', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    expect(strategy['defaultMinSize']).toBe(0);
    expect(strategy['defaultMaxSize']).toBe(Number.MAX_SAFE_INTEGER);
  });

  it('No.20: should apply column size using flex properties', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    strategy.applyColumnSize('testColumn', mockElement, 150, 100);

    expect(mockStyleScheduler.schedule).toHaveBeenCalled();
    expect(mockStyleScheduler.scheduleEnd).toHaveBeenCalled();
  });

  it('No.21: should skip resize when delta is zero in flex strategy', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    // Set up applied width to match new size
    strategy['_columnProperties'].set('testColumn', new Map([['flex', '0 0.01 100px']]));

    strategy.applyColumnSize('testColumn', mockElement, 100, 100);

    expect(mockStyleScheduler.schedule).not.toHaveBeenCalled();
  });

  it('No.22: should apply minimum column size using min-width', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    strategy.applyMinColumnSize('testColumn', mockElement, 50);

    expect(mockStyleScheduler.schedule).toHaveBeenCalled();
  });

  it('No.23: should apply maximum column size using max-width', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    strategy.applyMaxColumnSize('testColumn', mockElement, 200);

    expect(mockStyleScheduler.schedule).toHaveBeenCalled();
  });

  it('No.24: should generate correct column CSS class', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    const result = strategy['getColumnCssClass']('testColumn');
    expect(result).toBe('cdk-column-testColumn');
  });

  it('No.25: should create and manage style element', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    const styleSheet = strategy['_getStyleSheet']();

    expect(mockDocument.createElement).toHaveBeenCalledWith('style');
    expect(mockDocument.head.appendChild).toHaveBeenCalledWith(mockStyleElement);
    expect(styleSheet).toBe(mockStyleSheet);
  });

  it('No.26: should apply CSP nonce to style element', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument,
      'test-nonce'
    );

    strategy['_getStyleSheet']();

    expect(mockStyleElement.nonce).toBe('test-nonce');
  });

  it('No.27: should manage column properties map', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    const properties1 = strategy['_getColumnPropertiesMap']('column1');
    const properties2 = strategy['_getColumnPropertiesMap']('column1');
    const properties3 = strategy['_getColumnPropertiesMap']('column2');

    expect(properties1).toBe(properties2); // Same instance for same column
    expect(properties1).not.toBe(properties3); // Different instance for different column
    expect(properties1 instanceof Map).toBe(true);
  });

  it('No.28: should apply CSS rules correctly', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    // Set up properties
    const properties = strategy['_getColumnPropertiesMap']('testColumn');
    properties.set('flex', '0 0.01 150px');
    properties.set('min-width', '50px');

    strategy['_applySizeCss']('testColumn');

    expect(mockStyleSheet.insertRule).toHaveBeenCalledWith(
      '.unique-table-class .cdk-column-testColumn {flex:0 0.01 150px;min-width:50px}',
      0
    );
  });

  it('No.29: should clean up style element on destroy', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    // Create style element
    strategy['_getStyleSheet']();

    strategy.ngOnDestroy();

    expect(mockStyleElement.remove).toHaveBeenCalled();
    expect(strategy['_styleElement']).toBeUndefined();
  });

  it('No.30: should handle property deletion correctly', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    // Apply property with enable=false
    strategy['_applyProperty']('testColumn', 'min-width', '50px', false);

    const properties = strategy['_getColumnPropertiesMap']('testColumn');
    expect(properties.has('min-width')).toBe(false);
  });

  it('No.31: should convert CSS pixel values to numbers', () => {
    expect(coercePixelsFromCssValue('123px')).toBe(123);
    expect(coercePixelsFromCssValue('0px')).toBe(0);
    expect(coercePixelsFromCssValue('invalid')).toBeNaN();
  });

  it('No.32: should return NaN for non-pixel values', () => {
    expect(coercePixelsFromCssValue('123em')).toBeNaN();
    expect(coercePixelsFromCssValue('auto')).toBeNaN();
    expect(coercePixelsFromCssValue('')).toBeNaN();
  });

  it('No.33: should get element width from style or offsetWidth', () => {
    const element = document.createElement('div');
    element.style.width = '150px';
    Object.defineProperty(element, 'offsetWidth', { value: 100, configurable: true });

    expect(getElementWidth(element)).toBe(150); // Should prefer style.width

    element.style.width = '';
    expect(getElementWidth(element)).toBe(100); // Should fall back to offsetWidth
  });

  it('No.34: should convert flex values to pixel numbers', () => {
    expect(coercePixelsFromFlexValue('0 0.01 123px')).toBe(123);
    expect(coercePixelsFromFlexValue('0 0.01 0px')).toBe(0);
    expect(coercePixelsFromFlexValue('invalid')).toBeNaN();
    expect(coercePixelsFromFlexValue(undefined)).toBeNaN();
  });

  it('No.35: should export correct provider objects', () => {
    expect((TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER as any).provide).toBe(ResizeStrategy);
    expect((TABLE_LAYOUT_FIXED_RESIZE_STRATEGY_PROVIDER as any).useClass).toBe(TableLayoutFixedResizeStrategy);

    expect((FLEX_RESIZE_STRATEGY_PROVIDER as any).provide).toBe(ResizeStrategy);
    expect((FLEX_RESIZE_STRATEGY_PROVIDER as any).useClass).toBe(CdkFlexTableResizeStrategy);
  });

  it('should handle _getAppliedWidth correctly', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    // Test with no applied width
    expect(strategy['_getAppliedWidth']('testColumn')).toBeNaN();

    // Test with applied width
    const properties = strategy['_getColumnPropertiesMap']('testColumn');
    properties.set('flex', '0 0.01 150px');
    expect(strategy['_getAppliedWidth']('testColumn')).toBe(150);
  });

  it('should handle _getPropertyValue correctly', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    const properties = strategy['_getColumnPropertiesMap']('testColumn');
    properties.set('flex', '0 0.01 150px');

    expect(strategy['_getPropertyValue']('testColumn', 'flex')).toBe('0 0.01 150px');
    expect(strategy['_getPropertyValue']('testColumn', 'min-width')).toBeUndefined();
  });

  it('should handle deleteRule when updating existing CSS rule', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    // Set up initial rule
    strategy['_columnIndexes'].set('testColumn', 0);
    const properties = strategy['_getColumnPropertiesMap']('testColumn');
    properties.set('flex', '0 0.01 150px');

    strategy['_applySizeCss']('testColumn');

    expect(mockStyleSheet.deleteRule).toHaveBeenCalledWith(0);
    expect(mockStyleSheet.insertRule).toHaveBeenCalled();
  });

  it('should handle empty properties in _applySizeCss', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    // Call with no properties set
    strategy['_applySizeCss']('testColumn');

    // Should not insert any rules
    expect(mockStyleSheet.insertRule).not.toHaveBeenCalled();
  });

  it('should handle min/max size defaults correctly', () => {
    strategy = new CdkFlexTableResizeStrategy(
      mockColumnResize,
      mockStyleScheduler,
      mockTable,
      mockDocument
    );

    // Apply default min size (should not set property)
    strategy.applyMinColumnSize('testColumn', mockElement, 0);
    let properties = strategy['_getColumnPropertiesMap']('testColumn');
    expect(properties.has('min-width')).toBe(false);

    // Apply default max size (should not set property)
    strategy.applyMaxColumnSize('testColumn', mockElement, Number.MAX_SAFE_INTEGER);
    properties = strategy['_getColumnPropertiesMap']('testColumn');
    expect(properties.has('max-width')).toBe(false);
  });
});
