import { TestBed } from '@angular/core/testing';
import { Subject } from 'rxjs';
import { ColumnResizeNotifier, ColumnResizeNotifierSource, ColumnSize } from './column-resize-notifier';

describe('ColumnResizeNotifierSource', () => {
  let service: ColumnResizeNotifierSource;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [ColumnResizeNotifierSource],
    });
    service = TestBed.inject(ColumnResizeNotifierSource);
  });

  it('No.1: should be defined as an Injectable service', () => {
    expect(service).toBeTruthy();
    expect(service instanceof ColumnResizeNotifierSource).toBe(true);
  });

  it('No.2: should initialize with resizeCanceled Subject', () => {
    expect(service.resizeCanceled).toBeDefined();
    expect(service.resizeCanceled instanceof Subject).toBe(true);
  });

  it('No.3: should initialize with resizeCompleted Subject', () => {
    expect(service.resizeCompleted).toBeDefined();
    expect(service.resizeCompleted instanceof Subject).toBe(true);
  });

  it('No.4: should initialize with triggerResize Subject', () => {
    expect(service.triggerResize).toBeDefined();
    expect(service.triggerResize instanceof Subject).toBe(true);
  });

  it('No.5: should return Observable from getTriggerResize method', () => {
    const result = service.getTriggerResize();
    expect(result).toBeDefined();
    expect(typeof result.subscribe).toBe('function');
  });

  it('No.6: should return resizeCompleted observable from getTriggerResize (current implementation)', () => {
    const result = service.getTriggerResize();
    // Note: This test verifies the current implementation which may be a bug
    // The method returns resizeCompleted.asObservable() instead of triggerResize.asObservable()
    expect(result).toBeDefined();
    expect(typeof result.subscribe).toBe('function');
  });
});

describe('ColumnResizeNotifier', () => {
  let service: ColumnResizeNotifier;
  let mockSource: jasmine.SpyObj<ColumnResizeNotifierSource>;

  beforeEach(() => {
    const resizeCompletedSubject = new Subject<ColumnSize>();

    const sourceSpy = jasmine.createSpyObj('ColumnResizeNotifierSource', ['getTriggerResize'], {
      resizeCompleted: resizeCompletedSubject,
      triggerResize: {
        next: jasmine.createSpy('next'),
      },
    });

    TestBed.configureTestingModule({
      providers: [ColumnResizeNotifier, { provide: ColumnResizeNotifierSource, useValue: sourceSpy }],
    });

    service = TestBed.inject(ColumnResizeNotifier);
    mockSource = TestBed.inject(ColumnResizeNotifierSource) as jasmine.SpyObj<ColumnResizeNotifierSource>;
  });

  it('No.7: should be defined as an Injectable service', () => {
    expect(service).toBeTruthy();
    expect(service instanceof ColumnResizeNotifier).toBe(true);
  });

  it('No.8: should properly initialize with ColumnResizeNotifierSource dependency', () => {
    expect(service).toBeTruthy();
    expect(service['_source']).toBe(mockSource);
  });

  it('No.9: should expose resizeCompleted observable from source', () => {
    expect(service.resizeCompleted).toBe(mockSource.resizeCompleted);
  });

  it('No.10: should call source.triggerResize.next when resize method is called', () => {
    const columnId = 'column1';
    const size = 100;

    service.resize(columnId, size);

    expect(mockSource.triggerResize.next).toHaveBeenCalledWith({
      columnId,
      size,
      completeImmediately: true,
      isStickyColumn: true,
    });
  });

  it('No.11: should set completeImmediately to true in resize method', () => {
    const columnId = 'testColumn';
    const size = 150;

    service.resize(columnId, size);

    expect(mockSource.triggerResize.next).toHaveBeenCalledWith(jasmine.objectContaining({ completeImmediately: true }));
  });

  it('No.12: should set isStickyColumn to true in resize method', () => {
    const columnId = 'testColumn';
    const size = 150;

    service.resize(columnId, size);

    expect(mockSource.triggerResize.next).toHaveBeenCalledWith(jasmine.objectContaining({ isStickyColumn: true }));
  });

  it('No.13: should pass correct columnId to triggerResize', () => {
    const columnId = 'testColumn';
    const size = 150;

    service.resize(columnId, size);

    expect(mockSource.triggerResize.next).toHaveBeenCalledWith(jasmine.objectContaining({ columnId: 'testColumn' }));
  });

  it('No.14: should pass correct size to triggerResize', () => {
    const columnId = 'column1';
    const size = 200;

    service.resize(columnId, size);

    expect(mockSource.triggerResize.next).toHaveBeenCalledWith(jasmine.objectContaining({ size: 200 }));
  });

  it('No.15: should handle multiple resize calls correctly', () => {
    service.resize('column1', 100);
    service.resize('column2', 200);
    service.resize('column3', 300);

    expect(mockSource.triggerResize.next).toHaveBeenCalledTimes(3);
    expect(mockSource.triggerResize.next).toHaveBeenCalledWith(
      jasmine.objectContaining({ columnId: 'column1', size: 100 })
    );
    expect(mockSource.triggerResize.next).toHaveBeenCalledWith(
      jasmine.objectContaining({ columnId: 'column2', size: 200 })
    );
    expect(mockSource.triggerResize.next).toHaveBeenCalledWith(
      jasmine.objectContaining({ columnId: 'column3', size: 300 })
    );
  });
});
