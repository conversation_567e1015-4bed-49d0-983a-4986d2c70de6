import {
  ElementRef,
  EventEmitter,
  ChangeDetectorRef,
  Injector,
  NgZone,
  ViewContainerRef,
  Type
} from '@angular/core';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { Directionality } from '@angular/cdk/bidi';
import { CdkColumnDef, _CoalescedStyleScheduler } from '@angular/cdk/table';
import { Subject } from 'rxjs';
import { Resizable } from './resizable';
import { ColumnResize } from './column-resize';
import { ColumnResizeNotifierSource } from './column-resize-notifier';
import { HeaderRowEventDispatcher } from './event-dispatcher';
import { ResizeStrategy } from './resize-strategy';
import { ResizeOverlayHandle } from './overlay-handle';

// Mock handle component
class MockHandleComponent extends ResizeOverlayHandle {
  protected readonly columnDef: any;
  protected readonly document: any;
  protected readonly directionality: any;
  protected readonly elementRef: any;
  protected readonly eventDispatcher: any;
  protected readonly ngZone: any;
  protected readonly resizeNotifier: any;
  protected readonly resizeRef: any;
  protected readonly styleScheduler: any;
}

// Concrete implementation for testing the abstract class
class TestResizable extends Resizable<MockHandleComponent> {
  constructor(
    protected readonly columnDef: CdkColumnDef,
    protected readonly columnResize: ColumnResize,
    protected readonly directionality: Directionality,
    protected readonly document: Document,
    protected readonly elementRef: ElementRef,
    protected readonly eventDispatcher: HeaderRowEventDispatcher,
    protected readonly injector: Injector,
    protected readonly ngZone: NgZone,
    protected readonly overlay: Overlay,
    protected readonly resizeNotifier: ColumnResizeNotifierSource,
    protected readonly resizeStrategy: ResizeStrategy,
    protected readonly styleScheduler: _CoalescedStyleScheduler,
    protected readonly viewContainerRef: ViewContainerRef,
    protected readonly changeDetectorRef: ChangeDetectorRef
  ) {
    super();
  }

  getInlineHandleCssClassName(): string {
    return 'test-inline-handle';
  }

  getOverlayHandleComponentType(): Type<MockHandleComponent> {
    return MockHandleComponent;
  }
}

describe('Resizable', () => {
  let resizable: TestResizable;
  let mockColumnDef: jasmine.SpyObj<CdkColumnDef>;
  let mockColumnResize: jasmine.SpyObj<ColumnResize>;
  let mockDirectionality: jasmine.SpyObj<Directionality>;
  let mockDocument: jasmine.SpyObj<Document>;
  let mockElementRef: jasmine.SpyObj<ElementRef>;
  let mockEventDispatcher: jasmine.SpyObj<HeaderRowEventDispatcher>;
  let mockInjector: jasmine.SpyObj<Injector>;
  let mockNgZone: jasmine.SpyObj<NgZone>;
  let mockOverlay: jasmine.SpyObj<Overlay>;
  let mockResizeNotifier: jasmine.SpyObj<ColumnResizeNotifierSource>;
  let mockResizeStrategy: jasmine.SpyObj<ResizeStrategy>;
  let mockStyleScheduler: jasmine.SpyObj<_CoalescedStyleScheduler>;
  let mockViewContainerRef: jasmine.SpyObj<ViewContainerRef>;
  let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;
  let mockElement: HTMLElement;
  let mockOverlayRef: jasmine.SpyObj<OverlayRef>;

  beforeEach(() => {
    mockElement = document.createElement('th');
    Object.defineProperty(mockElement, 'offsetWidth', { value: 100, configurable: true });
    Object.defineProperty(mockElement, 'offsetHeight', { value: 40, configurable: true });

    // Create header row structure
    const headerRow = document.createElement('tr');
    headerRow.classList.add('cdk-header-row');
    headerRow.appendChild(mockElement);

    mockColumnDef = jasmine.createSpyObj('CdkColumnDef', [], {
      name: 'testColumn',
      cssClassFriendlyName: 'test-column'
    });

    mockColumnResize = jasmine.createSpyObj('ColumnResize', ['setResized']);
    mockDirectionality = jasmine.createSpyObj('Directionality', [], { value: 'ltr' });
    mockDocument = jasmine.createSpyObj('Document', ['createElement']);
    mockElementRef = jasmine.createSpyObj('ElementRef', [], { nativeElement: mockElement });

    mockEventDispatcher = jasmine.createSpyObj('HeaderRowEventDispatcher', ['resizeOverlayVisibleForHeaderRow'], {
      headerRowHoveredOrActiveDistinct: new Subject()
    });
    mockEventDispatcher.resizeOverlayVisibleForHeaderRow.and.returnValue(new Subject<boolean>());

    mockInjector = jasmine.createSpyObj('Injector', ['create']);
    mockNgZone = jasmine.createSpyObj('NgZone', ['run']);
    mockNgZone.run.and.callFake((fn: Function) => fn());

    // Mock overlay and position strategy
    const mockPositionStrategy = jasmine.createSpyObj('FlexibleConnectedPositionStrategy', [
      'flexibleConnectedTo', 'withFlexibleDimensions', 'withGrowAfterOpen',
      'withPush', 'withDefaultOffsetX', 'withPositions'
    ]);
    mockPositionStrategy.flexibleConnectedTo.and.returnValue(mockPositionStrategy);
    mockPositionStrategy.withFlexibleDimensions.and.returnValue(mockPositionStrategy);
    mockPositionStrategy.withGrowAfterOpen.and.returnValue(mockPositionStrategy);
    mockPositionStrategy.withPush.and.returnValue(mockPositionStrategy);
    mockPositionStrategy.withDefaultOffsetX.and.returnValue(mockPositionStrategy);
    mockPositionStrategy.withPositions.and.returnValue(mockPositionStrategy);

    const mockPosition = jasmine.createSpyObj('OverlayPositionBuilder', ['flexibleConnectedTo']);
    mockPosition.flexibleConnectedTo.and.returnValue(mockPositionStrategy);

    const mockScrollStrategy = jasmine.createSpyObj('ScrollStrategy', ['reposition']);
    mockScrollStrategy.reposition.and.returnValue(mockScrollStrategy);

    const mockScrollStrategies = jasmine.createSpyObj('ScrollStrategyOptions', ['reposition']);
    mockScrollStrategies.reposition.and.returnValue(mockScrollStrategy);

    mockOverlayRef = jasmine.createSpyObj('OverlayRef', [
      'attach', 'detach', 'dispose', 'hasAttached', 'updatePosition', 'updateSize'
    ]);
    mockOverlayRef.hasAttached.and.returnValue(true);

    mockOverlay = jasmine.createSpyObj('Overlay', ['create', 'position'], {
      scrollStrategies: mockScrollStrategies
    });
    mockOverlay.position.and.returnValue(mockPosition);
    mockOverlay.create.and.returnValue(mockOverlayRef);

    mockResizeNotifier = jasmine.createSpyObj('ColumnResizeNotifierSource', [], {
      triggerResize: new Subject(),
      resizeCompleted: new Subject(),
      resizeCanceled: new Subject()
    });

    mockResizeStrategy = jasmine.createSpyObj('ResizeStrategy', [
      'applyColumnSize',
      'applyMinColumnSize',
      'applyMaxColumnSize'
    ]);

    mockStyleScheduler = jasmine.createSpyObj('_CoalescedStyleScheduler', ['schedule', 'scheduleEnd']);
    mockStyleScheduler.schedule.and.callFake((fn: Function) => fn());
    mockStyleScheduler.scheduleEnd.and.callFake((fn: Function) => fn());

    mockViewContainerRef = jasmine.createSpyObj('ViewContainerRef', ['createComponent']);
    mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

    // Mock document.createElement
    const mockDiv = document.createElement('div');
    mockDocument.createElement.and.returnValue(mockDiv);

    resizable = new TestResizable(
      mockColumnDef,
      mockColumnResize,
      mockDirectionality,
      mockDocument,
      mockElementRef,
      mockEventDispatcher,
      mockInjector,
      mockNgZone,
      mockOverlay,
      mockResizeNotifier,
      mockResizeStrategy,
      mockStyleScheduler,
      mockViewContainerRef,
      mockChangeDetectorRef
    );
  });

  it('No.1: should be defined as an abstract Directive', () => {
    expect(Resizable).toBeDefined();
    expect(typeof Resizable).toBe('function');
    expect(resizable instanceof Resizable).toBe(true);
  });

  it('No.2: should implement AfterViewInit interface', () => {
    expect(typeof resizable.ngAfterViewInit).toBe('function');
  });

  it('No.3: should implement OnDestroy interface', () => {
    expect(typeof resizable.ngOnDestroy).toBe('function');
  });

  it('No.4: should initialize with default property values', () => {
    expect(resizable['isResizable']).toBe(true);
    expect(resizable['minWidthPxInternal']).toBe(0);
    expect(resizable['maxWidthPxInternal']).toBe(Number.MAX_SAFE_INTEGER);
  });

  it('No.5: should initialize destroyed Subject', () => {
    expect(resizable['destroyed']).toBeDefined();
    expect(resizable['destroyed'] instanceof Subject).toBe(true);
  });

  it('No.6: should have eventResize Output EventEmitter', () => {
    expect(resizable.eventResize).toBeDefined();
    expect(resizable.eventResize instanceof EventEmitter).toBe(true);
  });

  it('No.7: should have minWidthPx getter and setter', () => {
    resizable.minWidthPx = 100;
    expect(resizable.minWidthPx).toBe(100);
    expect(resizable['minWidthPxInternal']).toBe(100);
  });

  it('No.8: should have maxWidthPx getter and setter', () => {
    resizable.maxWidthPx = 500;
    expect(resizable.maxWidthPx).toBe(500);
    expect(resizable['maxWidthPxInternal']).toBe(500);
  });

  it('No.9: should call setResized when minWidthPx is set', () => {
    resizable.minWidthPx = 100;
    expect(mockColumnResize.setResized).toHaveBeenCalled();
  });

  it('No.10: should call setResized when maxWidthPx is set', () => {
    resizable.maxWidthPx = 500;
    expect(mockColumnResize.setResized).toHaveBeenCalled();
  });

  it('No.11: should apply min width after view init when minWidthPx is set', () => {
    spyOn(resizable as any, '_applyMinWidthPx');
    resizable['_viewInitialized'] = true;

    resizable.minWidthPx = 100;

    expect(resizable['_applyMinWidthPx']).toHaveBeenCalled();
  });

  it('No.12: should apply max width after view init when maxWidthPx is set', () => {
    spyOn(resizable as any, '_applyMaxWidthPx');
    resizable['_viewInitialized'] = true;

    resizable.maxWidthPx = 500;

    expect(resizable['_applyMaxWidthPx']).toHaveBeenCalled();
  });

  it('No.13: should initialize properly in ngAfterViewInit when resizable', () => {
    spyOn(resizable as any, '_listenForRowHoverEvents');
    spyOn(resizable as any, '_listenForResizeEvents');
    spyOn(resizable as any, '_appendInlineHandle');

    resizable['isResizable'] = true;
    resizable.ngAfterViewInit();

    expect(resizable['_listenForRowHoverEvents']).toHaveBeenCalled();
    expect(resizable['_listenForResizeEvents']).toHaveBeenCalled();
    expect(resizable['_appendInlineHandle']).toHaveBeenCalled();
  });

  it('No.14: should not initialize when not resizable', () => {
    spyOn(resizable as any, '_listenForRowHoverEvents');
    spyOn(resizable as any, '_listenForResizeEvents');
    spyOn(resizable as any, '_appendInlineHandle');

    resizable['isResizable'] = false;
    resizable.ngAfterViewInit();

    expect(resizable['_listenForRowHoverEvents']).not.toHaveBeenCalled();
    expect(resizable['_listenForResizeEvents']).not.toHaveBeenCalled();
    expect(resizable['_appendInlineHandle']).not.toHaveBeenCalled();
  });

  it('No.15: should set view initialized flag after style scheduler', () => {
    resizable.ngAfterViewInit();

    expect(resizable['_viewInitialized']).toBe(true);
  });

  it('No.16: should clean up properly in ngOnDestroy', () => {
    spyOn(resizable['destroyed'], 'next');
    spyOn(resizable['destroyed'], 'complete');

    // Set up inline handle and overlay
    resizable['inlineHandle'] = document.createElement('div');
    spyOn(resizable['inlineHandle'], 'remove');
    resizable['overlayRef'] = mockOverlayRef;

    resizable.ngOnDestroy();

    expect(resizable['_isDestroyed']).toBe(true);
    expect(resizable['destroyed'].next).toHaveBeenCalled();
    expect(resizable['destroyed'].complete).toHaveBeenCalled();
    expect(resizable['inlineHandle'].remove).toHaveBeenCalled();
    expect(mockOverlayRef.dispose).toHaveBeenCalled();
  });

  it('No.17: should have abstract getInlineHandleCssClassName method', () => {
    expect(typeof resizable.getInlineHandleCssClassName).toBe('function');
    expect(resizable.getInlineHandleCssClassName()).toBe('test-inline-handle');
  });

  it('No.18: should have abstract getOverlayHandleComponentType method', () => {
    expect(typeof resizable.getOverlayHandleComponentType).toBe('function');
    expect(resizable.getOverlayHandleComponentType()).toBe(MockHandleComponent);
  });

  // Additional comprehensive tests for better coverage

  it('should create overlay for handle correctly', () => {
    const overlayRef = resizable['_createOverlayForHandle']();

    expect(mockOverlay.position).toHaveBeenCalled();
    expect(mockOverlay.create).toHaveBeenCalled();
    expect(overlayRef).toBe(mockOverlayRef);
  });

  it('should create overlay with RTL configuration', () => {
    // Test RTL behavior by checking the position strategy calls
    (mockDirectionality as any).value = 'rtl';

    resizable['_createOverlayForHandle']();

    expect(mockOverlay.create).toHaveBeenCalledWith(jasmine.objectContaining({
      direction: 'ltr' // Always LTR for overlay
    }));
  });

  it('should listen for row hover events', () => {
    const hoverSubject = new Subject<boolean>();
    mockEventDispatcher.resizeOverlayVisibleForHeaderRow.and.returnValue(hoverSubject);

    spyOn(resizable as any, '_showHandleOverlay');

    resizable['_listenForRowHoverEvents']();

    // Emit hover true
    hoverSubject.next(true);
    expect(resizable['_showHandleOverlay']).toHaveBeenCalled();

    // Emit hover false
    hoverSubject.next(false);
    expect(mockOverlayRef.detach).toHaveBeenCalled();
  });

  it('should listen for resize events', () => {
    spyOn(resizable as any, '_applySize');
    spyOn(resizable as any, '_completeResizeOperation');

    resizable['_listenForResizeEvents']();

    // Emit resize trigger
    mockResizeNotifier.triggerResize.next({
      columnId: 'testColumn',
      size: 150,
      previousSize: 100,
      completeImmediately: true
    });

    expect(resizable['_applySize']).toHaveBeenCalledWith(150, 100);
    expect(resizable['_completeResizeOperation']).toHaveBeenCalled();
    expect(mockElement.classList.contains('cdk-resizable-overlay-thumb-active')).toBe(true);
  });

  it('should emit eventResize on resize completion', () => {
    spyOn(resizable.eventResize, 'emit');
    spyOn(resizable as any, '_cleanUpAfterResize');

    resizable['_listenForResizeEvents']();

    const resizeData = { columnId: 'testColumn', size: 150 };
    mockResizeNotifier.resizeCompleted.next(resizeData);

    expect(resizable.eventResize.emit).toHaveBeenCalledWith(resizeData);
    expect(resizable['_cleanUpAfterResize']).toHaveBeenCalledWith(resizeData);
  });

  it('should complete resize operation correctly', () => {
    spyOn(mockResizeNotifier.resizeCompleted, 'next');

    resizable['_completeResizeOperation']();

    expect(mockNgZone.run).toHaveBeenCalled();
    expect(mockResizeNotifier.resizeCompleted.next).toHaveBeenCalledWith({
      columnId: 'testColumn',
      size: 100 // offsetWidth
    });
  });

  it('should clean up after resize', () => {
    resizable['overlayRef'] = mockOverlayRef;
    resizable['inlineHandle'] = document.createElement('div');
    spyOn(resizable['inlineHandle'], 'focus');
    spyOn(resizable as any, '_updateOverlayHandleHeight');

    const resizeData = { columnId: 'testColumn', size: 150 };
    resizable['_cleanUpAfterResize'](resizeData);

    expect(mockElement.classList.contains('cdk-resizable-overlay-thumb-active')).toBe(false);
    expect(resizable['_updateOverlayHandleHeight']).toHaveBeenCalled();
    expect(mockOverlayRef.updatePosition).toHaveBeenCalled();
    expect(resizable['inlineHandle'].focus).toHaveBeenCalled();
  });

  it('should create handle portal with correct injector', () => {
    resizable['overlayRef'] = mockOverlayRef;
    resizable.minWidthPx = 50;
    resizable.maxWidthPx = 500;

    // Mock Injector.create static method
    const mockChildInjector = jasmine.createSpyObj('Injector', ['get']);
    spyOn(Injector, 'create').and.returnValue(mockChildInjector);

    const portal = resizable['_createHandlePortal']();

    expect(Injector.create).toHaveBeenCalledWith(jasmine.objectContaining({
      parent: mockInjector,
      providers: jasmine.any(Array)
    }));
    expect(portal.component).toBe(MockHandleComponent);
  });

  it('should show handle overlay', () => {
    resizable['overlayRef'] = mockOverlayRef;
    spyOn(resizable as any, '_updateOverlayHandleHeight');
    spyOn(resizable as any, '_createHandlePortal').and.returnValue({});

    resizable['_showHandleOverlay']();

    expect(resizable['_updateOverlayHandleHeight']).toHaveBeenCalled();
    expect(mockOverlayRef.attach).toHaveBeenCalled();
    expect(mockChangeDetectorRef.markForCheck).toHaveBeenCalled();
  });

  it('should update overlay handle height', () => {
    resizable['overlayRef'] = mockOverlayRef;

    resizable['_updateOverlayHandleHeight']();

    expect(mockOverlayRef.updateSize).toHaveBeenCalledWith({ height: 40 }); // offsetHeight
  });

  it('should apply size with constraints', () => {
    resizable.minWidthPx = 50;
    resizable.maxWidthPx = 200;

    resizable['_applySize'](300, 100); // Size exceeds max

    expect(mockResizeStrategy.applyColumnSize).toHaveBeenCalledWith(
      'test-column',
      mockElement,
      200, // Clamped to max
      100
    );
  });

  it('should apply minimum size below minimum', () => {
    resizable.minWidthPx = 50;

    resizable['_applySize'](30, 100); // Size below min

    expect(mockResizeStrategy.applyColumnSize).toHaveBeenCalledWith(
      'test-column',
      mockElement,
      50, // Clamped to min
      100
    );
  });

  it('should apply min width through resize strategy', () => {
    resizable.minWidthPx = 75;

    resizable['_applyMinWidthPx']();

    expect(mockResizeStrategy.applyMinColumnSize).toHaveBeenCalledWith(
      'test-column',
      mockElement,
      75
    );
  });

  it('should apply max width through resize strategy', () => {
    resizable.maxWidthPx = 300;

    resizable['_applyMaxWidthPx']();

    expect(mockResizeStrategy.applyMaxColumnSize).toHaveBeenCalledWith(
      'test-column',
      mockElement,
      300
    );
  });

  it('should append inline handle', () => {
    spyOn(mockElement, 'appendChild');

    resizable['_appendInlineHandle']();

    expect(mockDocument.createElement).toHaveBeenCalledWith('div');
    expect(mockElement.appendChild).toHaveBeenCalled();

    const createdDiv = mockDocument.createElement.calls.mostRecent().returnValue;
    expect(createdDiv.tabIndex).toBe(0);
    expect(createdDiv.className).toBe('test-inline-handle');
  });

  it('should handle zero minWidthPx value', () => {
    resizable.minWidthPx = 0;
    expect(resizable.minWidthPx).toBe(0);
    expect(mockColumnResize.setResized).toHaveBeenCalled();
  });

  it('should handle zero maxWidthPx value', () => {
    // Zero is falsy, so it won't be set due to the if (value) check
    const originalMax = resizable.maxWidthPx;
    resizable.maxWidthPx = 0;
    expect(resizable.maxWidthPx).toBe(originalMax); // Should remain unchanged
    expect(resizable['maxWidthPxInternal']).toBe(originalMax);
    expect(mockColumnResize.setResized).toHaveBeenCalled();
  });

  it('should not apply min/max width if not view initialized', () => {
    spyOn(resizable as any, '_applyMinWidthPx');
    spyOn(resizable as any, '_applyMaxWidthPx');

    resizable['_viewInitialized'] = false;

    resizable.minWidthPx = 100;
    resizable.maxWidthPx = 500;

    expect(resizable['_applyMinWidthPx']).not.toHaveBeenCalled();
    expect(resizable['_applyMaxWidthPx']).not.toHaveBeenCalled();
  });

  it('should handle destroyed state in ngAfterViewInit', () => {
    resizable['_isDestroyed'] = true;

    resizable.ngAfterViewInit();

    // Should not set _viewInitialized when destroyed
    expect(resizable['_viewInitialized']).toBe(false);
  });

  it('should ignore resize events for other columns', () => {
    spyOn(resizable as any, '_applySize');

    resizable['_listenForResizeEvents']();

    // Emit resize for different column
    mockResizeNotifier.triggerResize.next({
      columnId: 'otherColumn',
      size: 150,
      previousSize: 100
    });

    expect(resizable['_applySize']).not.toHaveBeenCalled();
  });

  it('should handle overlay not attached in cleanup', () => {
    resizable['overlayRef'] = mockOverlayRef;
    mockOverlayRef.hasAttached.and.returnValue(false);

    spyOn(resizable as any, '_updateOverlayHandleHeight');

    const resizeData = { columnId: 'testColumn', size: 150 };
    resizable['_cleanUpAfterResize'](resizeData);

    expect(resizable['_updateOverlayHandleHeight']).not.toHaveBeenCalled();
  });

  it('should not focus inline handle for other columns', () => {
    resizable['overlayRef'] = mockOverlayRef;
    resizable['inlineHandle'] = document.createElement('div');
    spyOn(resizable['inlineHandle'], 'focus');

    const resizeData = { columnId: 'otherColumn', size: 150 };
    resizable['_cleanUpAfterResize'](resizeData);

    expect(resizable['inlineHandle'].focus).not.toHaveBeenCalled();
  });
});
