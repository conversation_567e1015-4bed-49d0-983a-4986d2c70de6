import { ElementRef } from '@angular/core';
import { OverlayRef } from '@angular/cdk/overlay';
import { ResizeRef } from './resize-ref';

describe('ResizeRef', () => {
  let mockElementRef: jasmine.SpyObj<ElementRef<HTMLElement>>;
  let mockOverlayRef: jasmine.SpyObj<OverlayRef>;
  let mockElement: HTMLElement;
  let resizeRef: ResizeRef;

  beforeEach(() => {
    mockElement = document.createElement('div');
    mockElementRef = jasmine.createSpyObj('ElementRef', [], {
      nativeElement: mockElement
    });
    mockOverlayRef = jasmine.createSpyObj('OverlayRef', ['dispose', 'detach']);

    resizeRef = new ResizeRef(mockElementRef, mockOverlayRef, 100, 500);
  });

  it('No.1: should be defined as a class', () => {
    expect(ResizeRef).toBeDefined();
    expect(typeof ResizeRef).toBe('function');
    expect(resizeRef instanceof ResizeRef).toBe(true);
  });

  it('No.2: should properly initialize with all required parameters', () => {
    expect(resizeRef).toBeTruthy();
    expect(resizeRef.origin).toBe(mockElementRef);
    expect(resizeRef.overlayRef).toBe(mockOverlayRef);
    expect(resizeRef.minWidthPx).toBe(100);
    expect(resizeRef.maxWidthPx).toBe(500);
  });

  it('No.3: should have readonly origin property', () => {
    expect(resizeRef.origin).toBe(mockElementRef);
    expect(resizeRef.origin).toBeDefined();
  });

  it('No.4: should have readonly overlayRef property', () => {
    expect(resizeRef.overlayRef).toBe(mockOverlayRef);
    expect(resizeRef.overlayRef).toBeDefined();
  });

  it('No.5: should have readonly minWidthPx property', () => {
    expect(resizeRef.minWidthPx).toBe(100);
    expect(typeof resizeRef.minWidthPx).toBe('number');
  });

  it('No.6: should have readonly maxWidthPx property', () => {
    expect(resizeRef.maxWidthPx).toBe(500);
    expect(typeof resizeRef.maxWidthPx).toBe('number');
  });

  it('No.7: should accept ElementRef as origin parameter', () => {
    const newElementRef = jasmine.createSpyObj('ElementRef', [], {
      nativeElement: document.createElement('span')
    });
    const newResizeRef = new ResizeRef(newElementRef, mockOverlayRef, 50, 300);
    
    expect(newResizeRef.origin).toBe(newElementRef);
  });

  it('No.8: should accept OverlayRef as overlayRef parameter', () => {
    const newOverlayRef = jasmine.createSpyObj('OverlayRef', ['dispose', 'detach']);
    const newResizeRef = new ResizeRef(mockElementRef, newOverlayRef, 50, 300);
    
    expect(newResizeRef.overlayRef).toBe(newOverlayRef);
  });

  it('No.9: should accept number as minWidthPx parameter', () => {
    const newResizeRef = new ResizeRef(mockElementRef, mockOverlayRef, 75, 300);
    
    expect(newResizeRef.minWidthPx).toBe(75);
    expect(typeof newResizeRef.minWidthPx).toBe('number');
  });

  it('No.10: should accept number as maxWidthPx parameter', () => {
    const newResizeRef = new ResizeRef(mockElementRef, mockOverlayRef, 50, 800);
    
    expect(newResizeRef.maxWidthPx).toBe(800);
    expect(typeof newResizeRef.maxWidthPx).toBe('number');
  });

  it('No.11: should maintain all properties after instantiation', () => {
    const testElementRef = jasmine.createSpyObj('ElementRef', [], {
      nativeElement: document.createElement('div')
    });
    const testOverlayRef = jasmine.createSpyObj('OverlayRef', ['dispose']);
    const testMinWidth = 150;
    const testMaxWidth = 600;

    const testResizeRef = new ResizeRef(testElementRef, testOverlayRef, testMinWidth, testMaxWidth);

    expect(testResizeRef.origin).toBe(testElementRef);
    expect(testResizeRef.overlayRef).toBe(testOverlayRef);
    expect(testResizeRef.minWidthPx).toBe(testMinWidth);
    expect(testResizeRef.maxWidthPx).toBe(testMaxWidth);
  });

  it('No.12: should be immutable after construction', () => {
    const originalOrigin = resizeRef.origin;
    const originalOverlayRef = resizeRef.overlayRef;
    const originalMinWidth = resizeRef.minWidthPx;
    const originalMaxWidth = resizeRef.maxWidthPx;

    // Properties should remain unchanged (readonly)
    expect(resizeRef.origin).toBe(originalOrigin);
    expect(resizeRef.overlayRef).toBe(originalOverlayRef);
    expect(resizeRef.minWidthPx).toBe(originalMinWidth);
    expect(resizeRef.maxWidthPx).toBe(originalMaxWidth);
  });

  it('No.13: should track resize events state correctly', () => {
    // ResizeRef should provide access to all necessary resize tracking information
    expect(resizeRef.origin).toBeDefined();
    expect(resizeRef.overlayRef).toBeDefined();
    expect(resizeRef.minWidthPx).toBeDefined();
    expect(resizeRef.maxWidthPx).toBeDefined();
    
    // All properties should be accessible for resize tracking
    expect(typeof resizeRef.minWidthPx).toBe('number');
    expect(typeof resizeRef.maxWidthPx).toBe('number');
  });

  it('No.14: should support different minWidthPx values', () => {
    const resizeRef1 = new ResizeRef(mockElementRef, mockOverlayRef, 50, 300);
    const resizeRef2 = new ResizeRef(mockElementRef, mockOverlayRef, 200, 300);

    expect(resizeRef1.minWidthPx).toBe(50);
    expect(resizeRef2.minWidthPx).toBe(200);
    expect(resizeRef1.minWidthPx).not.toBe(resizeRef2.minWidthPx);
  });

  it('No.15: should support different maxWidthPx values', () => {
    const resizeRef1 = new ResizeRef(mockElementRef, mockOverlayRef, 100, 400);
    const resizeRef2 = new ResizeRef(mockElementRef, mockOverlayRef, 100, 800);

    expect(resizeRef1.maxWidthPx).toBe(400);
    expect(resizeRef2.maxWidthPx).toBe(800);
    expect(resizeRef1.maxWidthPx).not.toBe(resizeRef2.maxWidthPx);
  });
});
