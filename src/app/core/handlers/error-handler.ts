import { Error<PERSON><PERSON>ler, Injectable, Injector, NgZone } from '@angular/core';
import { Logger } from '../helpers/logger';
import { AuthError } from '../models/error';
import { CodeServer } from '../models/server-config/code-server.enum';
import { AuthService, I18nService, LoadingService, MessageService } from '../services';

/**
 * Global Error Handler
 * @description Error handling is important and needs to be loaded first.
 * @description Because of this we should manually inject the services with Injector.
 */
@Injectable()
export class MinionsErrorHandler implements ErrorHandler {
  logger = new Logger(MinionsErrorHandler.name);

  /**
   * Constructor
   * @param {Injector} injector Injector
   * @param {NgZone} ngZone NgZone
   */
  constructor(
    private injector: Injector,
    private ngZone: NgZone
  ) {}

  /**
   * Handle Error
   * @param {Error} error Error information
   */
  handleError(error: Error): void {
    this.loadingService.hide();
    this.logger.error('Global Error', error);
  }

  /**
   * <PERSON>le auth error code
   * @param {AuthError} error AuthError
   * @private
   */
  private handleAuthError(error: AuthError): void {
    const { code } = error;
    let message = '';
    if (code === CodeServer.badRequest) {
      message = this.i18nService.translate('MES-157');
    }

    if (code === CodeServer.unauthorized) {
      message = this.i18nService.translate('MES-158');
      this.logout();
    }

    if (code === CodeServer.notHavePermission) {
      message = this.i18nService.translate('MES-159');
    }

    this.showError(message);
  }

  /**
   * Show error message
   * @param {string} message string
   * @private
   */
  private showError(message: string): void {
    // Activate Change detection that helps show message
    this.runInsideNgZone(() => {
      this.messageService.error(message);
    });
  }

  /**
   * AuthService
   * @returns {AuthService} AuthService
   */
  get authService(): AuthService {
    return this.injector.get(AuthService);
  }

  /**
   * MessageService
   * @returns {MessageService} MessageService
   */
  get messageService(): MessageService {
    return this.injector.get(MessageService);
  }

  /**
   * Logout
   * @private
   */
  private logout() {
    // FIXME: check this after do the authentication
    this.authService.logout();
  }

  /**
   * I18nService
   * @returns {I18nService} I18nService
   * @private
   */
  private get i18nService(): I18nService {
    return this.injector.get(I18nService);
  }

  /**
   * LoadingService
   * @returns {LoadingService} LoadingService
   * @private
   */
  private get loadingService(): LoadingService {
    return this.injector.get(LoadingService);
  }

  /**
   * The error handler runs outside Angular zone
   * Use NgZone run to execute function within Angular zone
   * @param {Function} fn Function
   */
  private runInsideNgZone(fn: () => void): void {
    this.ngZone.run(() => {
      fn();
    });
  }
}
