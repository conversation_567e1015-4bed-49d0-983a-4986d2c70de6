import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ActivatedRouteSnapshot, Router, UrlTree } from '@angular/router';
import { Store } from '@ngrx/store';

/**
 * PermissionGuard
 */
@Injectable({ providedIn: 'root' })
export class PermissionGuard {
  /**
   * Constructor
   * @param {Router} router Router
   * @param {Store} store Store
   */
  constructor(
    private router: Router,
    private store: Store
  ) {}

  /**
   * Can activate child
   * @param {ActivatedRouteSnapshot} route ActivatedRouteSnapshot
   * @returns {Observable} Observable
   */
  canActivateChild(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> {
    const { action, entity } = route.data;
    console.log(action, entity);
    return of(true);

    // This.store.select(selectIsCompleteSetPermissions$).pipe(
    //   Filter((value) => value != null),
    //   Map(() => {
    //     Const hasPermissions = this.ability.can(action, entity);
    //     If (!hasPermissions) {
    //       Return this.router.createUrlTree(['tasks']);
    //     }
    //     Return hasPermissions;
    //   })
    // );
  }
}
