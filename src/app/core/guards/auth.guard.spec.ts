import { TestBed } from '@angular/core/testing';
import { provideMockStore, MockStore } from '@ngrx/store/testing';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { firstValueFrom, of, throwError } from 'rxjs';
import { AuthGuard } from './auth.guard';
import { AuthService, DestroyService, LoadingService, MessageService } from '../services';
import { saveInfoUserLogin } from 'src/app/stores/auth/auth.actions';
import { IInfoUserLogined } from 'src/app/shared/models/global';
import { IAuthState } from '../models/auth/auth-state';

/**
 * AuthGuard Tests
 *
 * These tests verify that the AuthGuard correctly handles authentication flows:
 * - Redirecting unauthenticated users
 * - Loading user info from the Keycloak service
 * - Handling API errors during authentication
 * - Managing user profile information
 */
describe('AuthGuard', () => {
  let guard: AuthGuard;
  let router: Router;
  let keycloakService: jasmine.SpyObj<KeycloakService>;
  let authService: jasmine.SpyObj<AuthService>;
  let loadingService: jasmine.SpyObj<LoadingService>;
  let messageService: jasmine.SpyObj<MessageService>;
  let store: MockStore;
  let destroyService: DestroyService;

  // Test constants
  const mockToken = 'mock-token';
  const mockUserProfile = { username: 'testuser' };

  // Mock user data with complete IInfoUserLogined interface properties
  const mockUserInfo: IInfoUserLogined[] = [
    {
      id: '1',
      brokerCode: 'BC001',
      brokerName: 'Test Broker',
      branchCode: 'BR001',
      saleGroupNo: 'SG001',
      saleGroupName: 'Sales Group 1',
      userName: ''
    }
  ];

  // Mock user data with username added from Keycloak profile
  const mockUserInfoWithUsername: IInfoUserLogined[] = [
    {
      id: '1',
      brokerCode: 'BC001',
      brokerName: 'Test Broker',
      branchCode: 'BR001',
      saleGroupNo: 'SG001',
      saleGroupName: 'Sales Group 1',
      userName: 'testuser'
    }
  ];

  // Initial state matching IAuthState interface
  const initialAuthState: IAuthState = {
    isAuthenticated: null,
    user: null,
    error: null,
    token: null,
    isRememberMe: null,
    setAccountPermissionsTime: 0,
    isCompleteSetAccountPermissions: null,
    userLogin: null
  };

  beforeEach(() => {
    // Create spy objects for all dependencies
    const keycloakSpy = jasmine.createSpyObj('KeycloakService', ['isLoggedIn', 'getKeycloakInstance', 'loadUserProfile']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getInfoUserLogin', 'logout']);
    const loadingServiceSpy = jasmine.createSpyObj('LoadingService', ['show']);
    const messageServiceSpy = jasmine.createSpyObj('MessageService', ['error']);

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        AuthGuard,
        { provide: KeycloakService, useValue: keycloakSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: LoadingService, useValue: loadingServiceSpy },
        { provide: MessageService, useValue: messageServiceSpy },
        DestroyService,
        provideMockStore({ initialState: { AUTH: initialAuthState } })
      ]
    });

    // Get all injected services and dependencies
    guard = TestBed.inject(AuthGuard);
    router = TestBed.inject(Router);
    keycloakService = TestBed.inject(KeycloakService) as jasmine.SpyObj<KeycloakService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    loadingService = TestBed.inject(LoadingService) as jasmine.SpyObj<LoadingService>;
    messageService = TestBed.inject(MessageService) as jasmine.SpyObj<MessageService>;
    store = TestBed.inject(MockStore);

    // Spy on store dispatch
    spyOn(store, 'dispatch');
  });

  afterEach(() => {
    store.resetSelectors();
  });

  /**
   * Verifies that the AuthGuard is created successfully
   */
  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  /**
   * Tests the redirect behavior when user is not logged in
   *
   * If the user is not authenticated, the guard should:
   * - Return Observable<false>
   * - Navigate to the auth page
   */
  it('should redirect to auth page if user is not logged in', (done) => {
    // Arrange: Set up user as not logged in
    keycloakService.isLoggedIn.and.returnValue(false);
    spyOn(router, 'navigate').and.returnValue(Promise.resolve(true));

    // Act: Execute the canActivate method
    guard.canActivate().subscribe(result => {
      // Assert: Verify navigation and return value
      expect(result).toBeFalse();
      expect(router.navigate).toHaveBeenCalledWith(['auth']);
      done();
    });
  });

  /**
   * Tests error handling when user info fetch fails
   *
   * When the API call to get user info fails, the guard should:
   * - Return Observable<false> to prevent route activation
   * - Display an error message
   * - Call the logout method
   */
  it('should handle error when fetching user info fails', (done) => {
    // Arrange: User is logged in but API call fails
    keycloakService.isLoggedIn.and.returnValue(true);
    keycloakService.getKeycloakInstance.and.returnValue({ token: mockToken } as any);
    authService.getInfoUserLogin.and.returnValue(throwError(() => new Error('API error')));

    // Act: Execute the canActivate method
    guard.canActivate().subscribe(result => {
      // Assert: Verify error handling
      expect(result).toBeFalse();
      expect(messageService.error).toHaveBeenCalled();
      expect(authService.logout).toHaveBeenCalled();
      done();
    });
  });

  /**
   * Tests successful authentication flow
   *
   * When authentication succeeds, the guard should:
   * - Return Observable<true> to allow route activation
   * - Load user profile
   * - Dispatch saveInfoUserLogin action with combined user data
   */
  it('should load user profile and dispatch save action when successful', async () => {
    // Arrange: User is logged in and API call succeeds
    keycloakService.isLoggedIn.and.returnValue(true);
    keycloakService.getKeycloakInstance.and.returnValue({ token: mockToken } as any);
    keycloakService.loadUserProfile.and.resolveTo(mockUserProfile);
    authService.getInfoUserLogin.and.returnValue(of(mockUserInfo));

    // Create the expected action
    const expectedAction = saveInfoUserLogin({ data: mockUserInfoWithUsername });

    // Act: Execute the canActivate method and wait for all async operations
    const resultPromise = await firstValueFrom( guard.canActivate());

    // Wait for the profile promise to resolve and store dispatch to be called
    await new Promise(resolve => setTimeout(resolve, 0));

    const result = await resultPromise;

    // Assert: Verify the results
    expect(result).toBeTrue();
    expect(keycloakService.loadUserProfile).toHaveBeenCalled();
    expect(store.dispatch).toHaveBeenCalledWith(expectedAction);
  }, 10000); // Increase timeout to ensure async operations complete

  /**
   * Tests behavior when user info is not available
   *
   * When user info returns null, the guard should:
   * - Return Observable<false> to prevent route activation
   * - Show loading indicator
   */
  it('should return false when user info is not available', (done) => {
    // Arrange: User is logged in but API returns null
    keycloakService.isLoggedIn.and.returnValue(true);
    keycloakService.getKeycloakInstance.and.returnValue({ token: mockToken } as any);
    // Type cast needed to handle null case which is handled in the guard implementation
    authService.getInfoUserLogin.and.returnValue(of(null) as any);

    // Act: Execute the canActivate method
    guard.canActivate().subscribe(result => {
      // Assert: Verify handling of null user info
      expect(result).toBeFalse();
      expect(loadingService.show).toHaveBeenCalled();
      done();
    });
  });

  /**
   * Tests the inForProfile helper method
   *
   * Verifies that inForProfile:
   * - Properly calls loadUserProfile on KeycloakService
   * - Returns the user profile data
   */
  it('should properly handle the inForProfile method', async () => {
    // Arrange: Setup mock user profile
    keycloakService.loadUserProfile.and.resolveTo(mockUserProfile);

    // Act: Call the inForProfile method
    const result = await guard.inForProfile();

    // Assert: Verify profile loading
    expect(result).toEqual(mockUserProfile);
    expect(keycloakService.loadUserProfile).toHaveBeenCalled();
  });
});
