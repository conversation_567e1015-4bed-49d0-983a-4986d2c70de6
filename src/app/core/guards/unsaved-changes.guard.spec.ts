import { TestBed } from '@angular/core/testing';
import { Store } from '@ngrx/store';
import { Observable, of, firstValueFrom } from 'rxjs';
import { DialogService, DestroyService } from '../services';
import { UnsaveChangeGuard } from './unsaved-changes.guard';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
import { MatDialogRef } from '@angular/material/dialog';

describe('UnsaveChangeGuard', () => {
  let guard: UnsaveChangeGuard;
  let dialogService: jasmine.SpyObj<DialogService>;
  let store: jasmine.SpyObj<Store>;
  let destroyService: jasmine.SpyObj<DestroyService>;
  let mockComponent: { canDeactivate: jasmine.Spy };
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<any>>;

  beforeEach(() => {
    dialogService = jasmine.createSpyObj('DialogService', ['openPopUp', 'closeAll']);
    store = jasmine.createSpyObj('Store', ['dispatch']);
    destroyService = jasmine.createSpyObj('DestroyService', ['ngOnDestroy']);
    mockComponent = { canDeactivate: jasmine.createSpy() };
    mockDialogRef = jasmine.createSpyObj<MatDialogRef<any>>(
      'MatDialogRef',
      ['afterClosed', 'close']
    );

    TestBed.configureTestingModule({
      providers: [
        UnsaveChangeGuard,
        { provide: DialogService, useValue: dialogService },
        { provide: Store, useValue: store },
        { provide: DestroyService, useValue: destroyService }
      ]
    });

    guard = TestBed.inject(UnsaveChangeGuard);
  });

  const getResult = async (result: boolean | Observable<boolean> | Promise<boolean>): Promise<boolean> => {
    if (result instanceof Observable) {
      return firstValueFrom(result);
    }
    return result;
  };

  it('No.1: should allow navigation when component can deactivate', async () => {
    // Arrange
    mockComponent.canDeactivate.and.returnValue(true);

    // Act
    const result = await getResult(guard.canDeactivate(mockComponent));

    // Assert
    expect(result).toBe(true);
    expect(dialogService.openPopUp).not.toHaveBeenCalled();
  });

  it('No.2: should show confirmation dialog when component cannot deactivate', async () => {
    // Arrange
    mockComponent.canDeactivate.and.returnValue(false);
    mockDialogRef.afterClosed.and.returnValue(of('cancel'));
    dialogService.openPopUp.and.returnValue(mockDialogRef);

    // Act
    const result = await getResult(guard.canDeactivate(mockComponent));

    // Assert
    expect(dialogService.openPopUp).toHaveBeenCalledWith(
      DeleteNotificationComponent,
      {
        data: {
          labels: {
            headerConfirmLabel: 'MES-87',
            subTitleConfirm: 'MES-97',
            buttonPrimaryLabel: 'MES-74',
            buttonOtherLabel: 'MES-89',
          },
        },
        height: '210px',
        width: '340px',
        panelClass: ['popup-confirm', 'not-padding-popup'],
      }
    );
    expect(result).toBe(false);
  });

  it('No.3: should handle user confirming to leave (save)', async () => {
    // Arrange
    mockComponent.canDeactivate.and.returnValue(false);
    mockDialogRef.afterClosed.and.returnValue(of('save'));
    dialogService.openPopUp.and.returnValue(mockDialogRef);

    // Act
    const result = await getResult(guard.canDeactivate(mockComponent));

    // Assert
    expect(dialogService.closeAll).toHaveBeenCalled();
    expect(result).toBe(true);
  });

  it('No.4: should handle user canceling navigation', async () => {
    // Arrange
    mockComponent.canDeactivate.and.returnValue(false);
    mockDialogRef.afterClosed.and.returnValue(of('cancel'));
    dialogService.openPopUp.and.returnValue(mockDialogRef);

    // Act
    const result = await getResult(guard.canDeactivate(mockComponent));

    // Assert
    expect(dialogService.closeAll).not.toHaveBeenCalled();
    expect(result).toBe(false);
  });

  it('No.5: should properly initialize with required services', () => {
    // Assert
    expect(guard).toBeTruthy();
    expect(guard instanceof UnsaveChangeGuard).toBe(true);
    expect(guard['dialogService']).toBeTruthy();
    expect(guard['store']).toBeTruthy();
    expect(guard['_destroy']).toBeTruthy();
  });
});
