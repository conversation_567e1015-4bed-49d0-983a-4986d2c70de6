import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { NoAuthGuard } from './no-auth.guard';

describe('NoAuthGuard', () => {
  let guard: NoAuthGuard;
  let router: jasmine.SpyObj<Router>;
  let keycloakService: jasmine.SpyObj<KeycloakService>;

  beforeEach(() => {
    // Create spies for Router and KeycloakService
    const routerSpy = jasmine.createSpyObj<Router>('Router', ['navigate']);
    const keycloakSpy = jasmine.createSpyObj<KeycloakService>('KeycloakService', [
      'isLoggedIn',
      'login',
      'logout'
    ]);

    TestBed.configureTestingModule({
      providers: [
        NoAuthGuard,
        { provide: Router, useValue: routerSpy },
        { provide: KeycloakService, useValue: keycloakSpy }
      ]
    });

    guard = TestBed.inject(NoAuthGuard);
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('No.1: should allow access when user is not authenticated', async () => {
    // Arrange
    Object.defineProperty(guard, 'authenticated', {
      get: () => false,
      configurable: true
    });

    // Act
    const result = await guard.isAccessAllowed();

    // Assert
    expect(result).toBe(true);
    expect(router.navigate).not.toHaveBeenCalled();
  });

  it('No.2: should redirect to customers and deny access when user is authenticated', async () => {
    // Arrange
    Object.defineProperty(guard, 'authenticated', {
      get: () => true,
      configurable: true
    });
    router.navigate.and.returnValue(Promise.resolve(true));

    // Act
    const result = await guard.isAccessAllowed();

    // Assert
    expect(router.navigate).toHaveBeenCalledWith(['/customers']);
    expect(result).toBe(false);
  });

  it('No.3: should properly initialize with router and keycloak service', () => {
    // Arrange & Act - guard is already created in beforeEach

    // Assert
    expect(guard).toBeTruthy();
    expect(guard instanceof NoAuthGuard).toBe(true);
    expect(TestBed.inject(Router)).toBeTruthy();
    expect(TestBed.inject(KeycloakService)).toBeTruthy();
  });

  it('No.4: should handle navigation failure when authenticated', async () => {
    // Arrange
    Object.defineProperty(guard, 'authenticated', {
      get: () => true,
      configurable: true
    });
    router.navigate.and.returnValue(Promise.resolve(false));

    // Act
    const result = await guard.isAccessAllowed();

    // Assert
    expect(router.navigate).toHaveBeenCalledWith(['/customers']);
    expect(result).toBe(false);
  });

  it('No.5: should handle authentication state changes', async () => {
    // Arrange
    let authState = false;
    Object.defineProperty(guard, 'authenticated', {
      get: () => authState,
      configurable: true
    });

    // Act & Assert - Test unauthenticated state
    let result = await guard.isAccessAllowed();
    expect(result).toBe(true);
    expect(router.navigate).not.toHaveBeenCalled();

    // Act & Assert - Test authenticated state
    authState = true;
    result = await guard.isAccessAllowed();
    expect(router.navigate).toHaveBeenCalledWith(['/customers']);
    expect(result).toBe(false);
  });
});
