import { Injectable } from '@angular/core';
import { Observable, from, of, switchMap, take } from 'rxjs';
import { DestroyService, DialogService } from '../services';
import { Store } from '@ngrx/store';
import { DeleteNotificationComponent } from 'src/app/shared/components/confirm-quit-notification/confirm-quit-notification.component';
export interface ComponentCanDeactivate {
  canDeactivate: () => boolean | Observable<boolean>;
}

/**
 * UnSaveChangeGuard
 */
@Injectable()
export class UnsaveChangeGuard {
  isAuthenticate = true;
  /**
   *
   * @param {DialogService} dialogService DialogService
   * @param {Store} store Store
   * @param {DestroyService} _destroy DestroyService
   */
  constructor(private dialogService: DialogService, private store: Store, private _destroy: DestroyService) {}

  /**
   * CanDeactivate
   * @param {ComponentCanDeactivate} component ComponentCanDeactivate
   * @returns {Observable} Observable
   */
  canDeactivate(component: ComponentCanDeactivate): Observable<boolean> | Promise<boolean> | boolean {
    return component.canDeactivate() ? true : this.openConfirmPopUp();
  }

  /**
   * OpenConfirmPopUp
   * @returns {boolean} return
   */
  openConfirmPopUp() {
    const ref = this.dialogService.openPopUp(DeleteNotificationComponent, {
      data: {
        labels: {
          headerConfirmLabel: 'MES-87',
          subTitleConfirm: 'MES-97',
          buttonPrimaryLabel: 'MES-74',
          buttonOtherLabel: 'MES-89',
        },
      },
      height: '210px',
      width: '340px',
      panelClass: ['popup-confirm', 'not-padding-popup'],
    });
    return from(ref.afterClosed()).pipe(
      take(1),
      switchMap((value) => {
        if (value === 'save') {
          this.dialogService.closeAll();
          return of(true);
        } else {
          return of(false);
        }
      })
    );
  }
}
