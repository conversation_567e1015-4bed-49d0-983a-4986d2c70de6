import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { KeycloakService } from 'keycloak-angular';
import { catchError, map, Observable, of, takeUntil } from 'rxjs';
import { AuthService, DestroyService, LoadingService, MessageService } from '../services';
import { saveInfoUserLogin } from 'src/app/stores/auth/auth.actions';

/**
 * AuthGuard
 */
@Injectable({ providedIn: 'root' })
export class AuthGuard {
  /**
   * Constructor
   * @param {KeycloakService} keyCloak KeycloakService
   * @param {Router} router Router
   */
  constructor(
    private readonly keyCloak: KeycloakService,
    private readonly _destroy: DestroyService,
    private readonly router: Router,
    private readonly store: Store,
    private readonly authService: AuthService,
    private readonly messageService: MessageService,
    private readonly loadingService: LoadingService
  ) {}

  /**
   * Can activate
   * @returns {Observable} Observable
   */
  canActivate(): Observable<boolean> {
    const isLogin = this.keyCloak.isLoggedIn();
    if (!isLogin) {
      void this.router.navigate([`auth`]);
      return of(false);
    }
    const token = this.keyCloak.getKeycloakInstance().token;
    const profile = this.inForProfile();
    return this.authService.getInfoUserLogin(token).pipe(
      // tap(() => this.loadingService.show()),
      catchError((error) => {
        console.error('Failed to fetch user info:', error);
        this.messageService.error(error.message);
        this.authService.logout();
        return of(false);
      }),
      takeUntil(this._destroy),
      map((user) => {
        if (!user) {
          this.loadingService.show();
          return false;
        }

        profile.then((t) => {
          if (user !== true) {
            const userInfo = user.map((d) => ({
              ...d,
              userName: t.username ?? '',
            }));
            this.store.dispatch(saveInfoUserLogin({ data: userInfo }));
          }
        });
        return true;
      })
    );
  }

  async inForProfile() {
    return await this.keyCloak.loadUserProfile();
  }
}
