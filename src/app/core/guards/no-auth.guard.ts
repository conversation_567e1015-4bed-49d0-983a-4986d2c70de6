import { Injectable } from '@angular/core';
import { Router, UrlTree } from '@angular/router';
import { KeycloakAuthGuard, KeycloakService } from 'keycloak-angular';

/**
 * NoAuthGuard
 */
@Injectable({ providedIn: 'root' })
export class NoAuthGuard extends KeycloakAuthGuard {
  /**
   * Constructor
   * @param router
   * @param keycloak
   */
  constructor(protected override readonly router: Router, protected readonly keycloak: KeycloakService) {
    super(router, keycloak);
  } // Private router: Router // Private store: Store,

  /**
   * Can activate
   * @returns {Observable} Observable
   */
  async isAccessAllowed(): Promise<boolean | UrlTree> {
    if (this.authenticated) {
      await this.router.navigate(['/dashboard']);
    }
    return !this.authenticated;
  }
}
