/**
 * Logger
 */
export class Logger {
  private readonly _prefix: string;
  /**
   * Constructor
   * @param {string} prefix string
   */
  constructor(prefix?: string) {
    this._prefix = prefix ?? 'Logger';
  }

  /**
   * Log
   * @param {string} message string
   * @param {any} context any
   */
  log(message: string, context?: any): void {
    const { now } = this;
    console.log(`${now} LOG | [${this._prefix}] `, message);
    if (context) {
      console.log(`${now} LOG | [${this._prefix}] `, context);
    }
  }

  /**
   * Warn
   * @param {string} message string
   * @param {any} context any
   */
  warn(message: string, context?: any): void {
    const { now } = this;
    console.warn(`${now} | WARN [${this._prefix}] `, message);
    if (context) {
      console.warn(`${now} | WARN [${this._prefix}] `, context);
    }
  }

  /**
   * Error
   * @param {string} message string
   * @param {any} error any
   */
  error(message: string, error?: any) {
    const { now } = this;
    console.error(`${now} | ERROR [${this._prefix}] `, message);
    if (error) {
      console.error(`${now} | ERROR [${this._prefix}] `, error);
    }
  }

  /**
   * Getter Now
   * @returns {string} string
   */
  get now(): string {
    const current = new Date();
    const date = current.getDate();
    const month = current.getMonth() + 1;
    const year = current.getFullYear();
    const hour = current.getHours();
    const minute = current.getMinutes();
    const second = current.getSeconds();
    const milliseconds = current.getMilliseconds();
    return `${date}/${month}/${year}, ${hour}:${minute}:${second}:${milliseconds}`;
  }
}
