import { Injectable } from '@angular/core';
import { CookieService, SameSite } from 'ngx-cookie-service';

/**
 * StorageService
 */
@Injectable({
  providedIn: 'root',
})
export class StorageService {
  /**
   * Constructor
   * @param {CookieService} cookieService CookieService
   */
  constructor(private cookieService: CookieService) {}

  /**
   * Get cookie value by name
   * @param {string} name string
   * @returns {string} cookie
   */
  getCookieByName(name: string): string {
    return this.cookieService.get(name);
  }

  /**
   * Set cookie
   * @param {string} name string
   * @param {string} value string
   * @param {number | Date} expires number | Date
   * @param {string} path /
   * @param {string} domain string
   * @param {boolean} secure boolean
   * @param {SameSite} sameSite SameSite
   */
  setCookie(
    name: string,
    value: string,
    expires?: number | Date,
    path = '/',
    domain?: string,
    secure = true,
    sameSite: SameSite = 'Strict'
  ) {
    this.cookieService.set(name, value, expires, path, domain, secure, sameSite);
  }

  /**
   * Remove cookie
   * @param {string} name string
   */
  removeCookie(name: string): void {
    this.cookieService.delete(name);
  }

  /**
   * Set local storage
   * @param {string} key string
   * @param {unknown} value unknown
   */
  setLocalStorage(key: string, value: unknown) {
    localStorage.setItem(key, JSON.stringify(value));
  }

  /**
   * Get local storage
   * @param {string} key string
   * @returns {boolean} boolean
   */
  getLocalStorage(key: string) {
    const value = localStorage.getItem(key);
    return value != null ? JSON.parse(value) : null;
  }

  /**
   * Remove local storage
   * @param {string} key string
   */
  removeLocalStorage(key: string) {
    localStorage.removeItem(key);
  }

  removeAllLocalStorage() {
    localStorage.clear();
  }
}
