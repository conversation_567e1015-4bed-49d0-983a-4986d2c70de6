import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { retry, tap, of, map } from 'rxjs';
import { ApiService } from './api.service';
import { DialogService } from './dialog.service';
import { jwtDecode } from 'jwt-decode';

import { MessageService } from './message.service';
import { StorageService } from './storage.service';
import { DestroyService } from './destroy.service';
import { IDataLoginResponse } from '../models/auth/login-response';
import { ApiResponse } from '../models/api-response';
import { AUTH_ENDPOINTS, GLOBAL_CONSTANTS, ROUTE_PATH } from '../models/global.constants';
import { IToken } from '../models/user';
import { KeycloakService } from 'keycloak-angular';
import { IInfoUserLogined } from 'src/app/shared/models/global';
import { environment } from 'src/environments/environment';

interface IJWTDecode {
  exp: number;
  iat: number;
  staffID: string;
  userId: string;
}

/**
 * AuthService
 */
@Injectable({
  providedIn: 'root',
})
export class AuthService {
  environment = 'http://localhost:4200';

  /**
   * Constructor
   * @param {ApiService} apiService ApiService
   * @param {Router} router Router
   * @param {StorageService} storageService StorageService
   * @param {Store} store Store
   * @param {DialogService} dialogService DialogService
   * @param {MessageService} messageService MessageService
   * @param {DestroyService} destroy$ DestroyService
   * @param {KeycloakService} keycloak KeycloakService
   */
  constructor(
    private apiService: ApiService,
    private router: Router,
    private storageService: StorageService,
    private store: Store,
    private dialogService: DialogService,
    private messageService: MessageService,
    private destroy$: DestroyService,
    private keycloak: KeycloakService
  ) {}

  private renewTokenTimeout: any;

  /**
   * Login
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  login() {
    this.keycloak
      .login()
      .then(() => {
        return this.keycloak.loadUserProfile();
      })
      .then((profile) => {
        console.log('profile user: ', profile);
      })
      .catch((error) => {
        console.error('Login error:', error);
      })
      .finally(() => {
        console.log('Finished login process');
      });

    // Return this.apiService
    //   .post<ApiResponse<IDataLoginResponse>>(`${this.environment}/accounts/login`, loginRequest)
    //   .pipe(
    //     Map((res) => {
    //       Const { data, message } = res;
    //       Const decoded = this.decodeToken(data.accessToken);
    //       Return {
    //         Token: {
    //           IdToken: data.accessToken,
    //           ExpiresIn: decoded.expiresIn,
    //           SessionID: data.sessionID,
    //         },
    //         User: {
    //           UserId: decoded.userId,
    //         },
    //       };
    //     })
    //   );
  }

  /**
   * Exchange refresh token for new ID Token
   * @returns {any} ApiResponse
   */
  renewToken() {
    this.keycloak
      .updateToken(300)
      .then(() => {
        const { token } = this.keycloak.getKeycloakInstance();
        if (token) {
          const decoded = this.decodeToken(token);
          this.setLoginSuccessValue(decoded, true);
        }
        return of(true);
      })
      .catch((err) => {
        console.error('Keycloak is expired time: ', err);
        return of(false);
      });
    // return this.apiService
    //   .get<ApiResponse<IDataLoginResponse>>(`${this.environment}/${AUTH_ENDPOINTS.renew_token}`)
    //   .pipe(
    //     retry(retryLimit),
    //     map((res) => {
    //       const { data } = res as any; // FIXME : udpdate later
    //       return {
    //         token: {
    //           idToken: data.accessToken,
    //           expiresIn: data.exp,
    //           sessionID: data.sessionID,
    //         },
    //         user: {
    //           userId: data.userId,
    //         },
    //       };
    //     }),
    //     tap({
    //       next: ({ token }) => {
    //         this.setLoginSuccessValue(token);
    //       },
    //       error: (err) => {
    //         this.logout();
    //       },
    //     })
    //   );
  }

  /**
   * Logout
   */
  logout() {
    this.keycloak.logout().then(() => {
      console.log('logout');
      // => LOGOUT
      this.storageService.removeLocalStorage(GLOBAL_CONSTANTS.idToken);
      this.storageService.removeLocalStorage(GLOBAL_CONSTANTS.expiredTime);
      this.storageService.removeLocalStorage(GLOBAL_CONSTANTS.rememberMe);
      this.storageService.removeAllLocalStorage();
    });
  }

  /**
   * This function checks if a token has expired renew it ,else call function to automatically renew it.
   */
  checkToken() {
    const currentTime = new Date().getTime();
    const expiredTime = +this.storageService.getLocalStorage(GLOBAL_CONSTANTS.expiredTime);
    const idToken = this.storageService.getLocalStorage(GLOBAL_CONSTANTS.idToken);

    // Does not have expired time, idToken, remember me value in localstorage -> go to login page
    if (!expiredTime || !idToken) {
      //FIXME : update later
      void this.router.navigate([`/${ROUTE_PATH.login}`]);
      return;
    }

    /*
     * 5 mins = 5 * 60 * 1000
     * token is not expired create new setTimeout(renewToken) with 5 mins earlier than expired time
     * 0.5 mins = 30s
     */
    if (expiredTime > currentTime + 5 * 60 * 1000) {
      const token = {
        idToken,
        expiresIn: expiredTime,
      } as IToken;

      // This.store.dispatch(renewTokenSuccess({ token: token, isRememberMe: !!rememberMe, user: { userId } }));
      // FIXME: update later
      this.startRenewTokenTimer(expiredTime);
    }

    //Token is expired call action renewToken
    // This.store.dispatch(renewToken());
  }

  /**
   * This function setTimeout to renew token before it expired.
   * startRenewTokenTimer => action(renewToken) in store => call API renewToken
   * => receive new Token and expiredTime => setTimeout for function to renew Token
   * @param {number} expiredTime number
   */
  startRenewTokenTimer(expiredTime: number) {
    this.stopRenewTokenTimer();
    const currentTime = new Date().getTime();
    const timeout = expiredTime - currentTime - 5 * 60 * 1000;
    this.renewTokenTimeout = setTimeout(() => {
      this.renewToken();
    }, timeout);
  }

  /**
   * The function stops a timer for renewing a token.
   */
  stopRenewTokenTimer() {
    clearTimeout(this.renewTokenTimeout);
  }

  /**
   * Set expired time, idToken, rememberMe value in browser when login successfully
   * Start timer to call renew token
   * @param {IToken} token IToken
   * @param {boolean} rememberMe boolean
   * @param {string} userId string
   */
  setLoginSuccessValue(token: IToken, rememberMe?: boolean, userId?: string) {
    const expiredTime = token.expiresIn * 1000;
    // Set tokens in cookie and expiredTime in localStorage
    this.storageService.setLocalStorage(GLOBAL_CONSTANTS.expiredTime, expiredTime);
    // this.storageService.setLocalStorage(GLOBAL_CONSTANTS.idToken, token.idToken);

    if (userId) {
      this.storageService.setLocalStorage(GLOBAL_CONSTANTS.userId, userId);
    }

    // Call function that will automatically renew token before it expires.
    this.startRenewTokenTimer(expiredTime);
  }

  /**
   * Send reset password email
   * @param {unknown} value unknown
   * @returns {ApiResponse} ApiResponse
   */
  sendResetPassword(value: unknown) {
    return this.apiService.post<ApiResponse<IDataLoginResponse>>(
      `${this.environment}/${AUTH_ENDPOINTS.reset_password}`,
      value
    );
  }

  /**
   * Forget password
   * @param {unknown} value unknow
   * @returns {ApiResponse} ApiResponse
   */
  forgetPassword(value: unknown) {
    return this.apiService.post<ApiResponse<IDataLoginResponse>>(
      `${this.environment}/${AUTH_ENDPOINTS.forget_password}`,
      value
    );
  }

  /**
   * Change password
   * @param {unknown} value unknown
   * @returns {ApiResponse} ApiResponse
   */
  changePassword(value: unknown) {
    return this.apiService.post<ApiResponse<IDataLoginResponse>>(`${AUTH_ENDPOINTS.change_password}`, value);
  }

  getInfoUserLogin(token: string = 'token') {
    return this.apiService
      .post<ApiResponse<IInfoUserLogined[]>>('v1/system/user/get-user-login', { token })
      .pipe(map((res) => res.data));
  }

  /**
   * Decode token
   * @private
   * @param {string} token string
   */
  private decodeToken(token: string) {
    const decoded = jwtDecode(token);
    // const currentTime = new Date().getTime();

    // const timeout = currentTime + 2 * 60 * 1000;
    return {
      expiresIn: decoded.exp ?? 0,
      idToken: token,
    };
  }
}
