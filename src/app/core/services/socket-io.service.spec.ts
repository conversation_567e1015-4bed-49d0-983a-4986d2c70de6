import { TestBed } from '@angular/core/testing';
import { SocketIoService } from './socket-io.service';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { I18nService } from './i18n.service';
import { ToastrService } from 'ngx-toastr';
import { DestroyService } from './destroy.service';
import { selectIsAuthenticated$, selectToken$, AUTH_STATE_NAME } from '../../stores/auth/auth.selectors';
import { IToken } from '../../core/models/user';
import { IAuthState } from '../../core/models/auth/auth-state';
import { Socket } from 'socket.io-client';

/**
 * SocketIoService unit tests
 */
describe('SocketIoService', () => {
  let service: SocketIoService;
  let store: MockStore;
  let i18nServiceMock: jasmine.SpyObj<I18nService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let destroyServiceMock: DestroyService;
  let onInitConnectionSpy: jasmine.Spy;
  let mockSocketConnection: jasmine.SpyObj<Socket>;
  
  // Define test data
  const mockToken: IToken = {
    idToken: 'mock-token',
    expiresIn: 3600,
    sessionID: 'session-123'
  };
  
  const initialState = {
    [AUTH_STATE_NAME]: {
      isAuthenticated: false,
      user: null,
      error: null,
      token: null,
      isRememberMe: null,
      setAccountPermissionsTime: 0,
      isCompleteSetAccountPermissions: null,
      userLogin: null
    } as IAuthState
  };
  
  beforeEach(() => {
    // Create service mocks
    i18nServiceMock = jasmine.createSpyObj('I18nService', ['translate']);
    i18nServiceMock.translate.and.callFake(key => `translated-${key}`);
    
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'info', 'warning']);
    destroyServiceMock = new DestroyService();
    
    // Create a spy object to use as mocked io connection
    mockSocketConnection = jasmine.createSpyObj('Socket', ['on', 'emit', 'connect', 'disconnect']);
    
    TestBed.configureTestingModule({
      providers: [
        SocketIoService,
        provideMockStore({ initialState }),
        { provide: I18nService, useValue: i18nServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: DestroyService, useValue: destroyServiceMock }
      ]
    });
    
    store = TestBed.inject(MockStore);
    service = TestBed.inject(SocketIoService);
    
    // Spy on the onInitConnection method
    onInitConnectionSpy = spyOn<any>(service, 'onInitConnection').and.callFake((token?: string) => {
      service.socket = mockSocketConnection as Socket;
    });
    
    // Create selector spies
    store.overrideSelector(selectIsAuthenticated$, false);
    store.overrideSelector(selectToken$, null);
  });

  afterEach(() => {
    store.resetSelectors();
  });

  // No.1: should properly initialize with all dependencies
  it('should properly initialize with all dependencies', () => {
    // Arrange
    
    // Act - service is already created in beforeEach
    
    // Assert
    expect(service).toBeTruthy();
  });

  // No.2: should initialize socket connection when user is authenticated
  it('should initialize socket connection when user is authenticated', () => {
    // Arrange - reset the spy to ensure we only count calls in this test
    onInitConnectionSpy.calls.reset();
    
    // Act
    store.overrideSelector(selectIsAuthenticated$, true);
    store.overrideSelector(selectToken$, mockToken);
    store.refreshState();
    
    // Assert
    expect(onInitConnectionSpy).toHaveBeenCalledWith('mock-token');
  });

  // No.3: should not initialize socket connection when user is not authenticated
  it('should not initialize socket connection when user is not authenticated', () => {
    // Arrange
    onInitConnectionSpy.calls.reset();
    
    // Act
    store.overrideSelector(selectIsAuthenticated$, false);
    store.overrideSelector(selectToken$, null);
    store.refreshState();
    
    // Assert
    expect(onInitConnectionSpy).not.toHaveBeenCalled();
  });

  // No.4: should correctly initialize socket connection with token
  it('should correctly initialize socket connection with token', () => {
    // Arrange & Act
    onInitConnectionSpy.and.callThrough();
    service['onInitConnection']('mock-token');
    
    // Assert
    expect(service.socket).toBeDefined();
  });

  // No.5: should play notification sound
  it('should play notification sound', () => {
    // Arrange
    const mockAudio = {
      src: '',
      load: jasmine.createSpy('load'),
      play: jasmine.createSpy('play')
    };
    
    const audioSpy = spyOn(window, 'Audio').and.returnValue(mockAudio as any);
    
    // Act
    service.playSound();
    
    // Assert
    expect(audioSpy).toHaveBeenCalled();
    expect(mockAudio.src).toBe('../../../assets/sounds/notification.wav');
    expect(mockAudio.load).toHaveBeenCalled();
    expect(mockAudio.play).toHaveBeenCalled();
  });

  // No.6: should expose cloneSuccessEvent$ as an Observable
  it('should expose cloneSuccessEvent$ as an Observable', (done) => {
    // Arrange
    const cloneTaskSubject = (service as any).cloneTaskSubject;
    
    // Act & Assert
    service.cloneSuccessEvent$.subscribe(value => {
      expect(value).toBe(true);
      done();
    });
    
    cloneTaskSubject.next(true);
  });
}); 