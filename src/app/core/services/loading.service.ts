import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * LoadingService
 */
@Injectable({
  providedIn: 'root',
})
export class LoadingService {
  private isLoading$ = new BehaviorSubject<boolean>(false);

  /**
   * Constructor
   */
  constructor() {
    // FIXME: remove this
    console.log('time from loading service', new Date().getTime());
  }

  /**
   * Show
   */
  show(): void {
    this.isLoading$.next(true);
  }

  /**
   * Hide
   */
  hide(): void {
    this.isLoading$.next(false);
  }

  /**
   * Loading status
   * @returns {Observable<boolean>} Observable<boolean>
   */
  get isLoading(): Observable<boolean> {
    return this.isLoading$.asObservable();
  }
}
