import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { MessageService } from './message.service';

describe('MessageService', () => {
  let service: MessageService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;

  beforeEach(() => {
    // Create ToastrService spy
    toastrServiceMock = jasmine.createSpyObj('ToastrService', [
      'success', 
      'error', 
      'info', 
      'warning'
    ]);
    
    TestBed.configureTestingModule({
      providers: [
        MessageService,
        { provide: ToastrService, useValue: toastrServiceMock }
      ]
    });
    
    service = TestBed.inject(MessageService);
  });

  // No.1: should properly initialize with ToastrService dependency
  it('should properly initialize with ToastrService dependency', () => {
    // Arrange - done in beforeEach
    
    // Act - service is created in beforeEach
    
    // Assert
    expect(service).toBeTruthy();
  });

  // No.2: should call toastrService.success when success method is called
  it('should call toastrService.success when success method is called', () => {
    // Arrange
    const testMessage = 'Test success message';
    
    // Act
    service.success(testMessage);
    
    // Assert
    expect(toastrServiceMock.success).toHaveBeenCalledWith(testMessage);
  });

  // No.3: should call toastrService.error when error method is called
  it('should call toastrService.error when error method is called', () => {
    // Arrange
    const testMessage = 'Test error message';
    
    // Act
    service.error(testMessage);
    
    // Assert
    expect(toastrServiceMock.error).toHaveBeenCalledWith(testMessage);
  });

  // No.4: should call toastrService.info when info method is called
  it('should call toastrService.info when info method is called', () => {
    // Arrange
    const testMessage = 'Test info message';
    
    // Act
    service.info(testMessage);
    
    // Assert
    expect(toastrServiceMock.info).toHaveBeenCalledWith(testMessage);
  });

  // No.5: should call toastrService.warning when warning method is called
  it('should call toastrService.warning when warning method is called', () => {
    // Arrange
    const testMessage = 'Test warning message';
    
    // Act
    service.warning(testMessage);
    
    // Assert
    expect(toastrServiceMock.warning).toHaveBeenCalledWith(testMessage);
  });
});
