import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

/**
 * LocationService
 */
@Injectable({
  providedIn: 'root',
})
export class LocationService {
  private jsonUrl = 'assets/location/location.json';

  /**
   *constructor
   * @param apiService apiService
   */
  constructor(private apiService: ApiService) {}

  /**
   * getLocations
   */
  getLocations(): Observable<any> {
    return this.apiService.get<any>(this.jsonUrl);
  }
}
