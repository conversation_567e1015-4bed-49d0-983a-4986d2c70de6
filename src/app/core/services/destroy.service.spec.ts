import { TestBed } from '@angular/core/testing';
import { DestroyService } from './destroy.service';

describe('DestroyService', () => {
  let service: DestroyService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [DestroyService],
    });
    service = TestBed.inject(DestroyService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should emit a value and complete when ngOnDestroy is called', () => {
    spyOn(service, 'next');
    spyOn(service, 'complete');

    service.ngOnDestroy();

    expect(service.next).toHaveBeenCalled();
    expect(service.complete).toHaveBeenCalled();
  });
});
