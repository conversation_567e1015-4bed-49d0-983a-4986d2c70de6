import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

/**
 * MessageService
 */
@Injectable({
  providedIn: 'root',
})
export class MessageService {
  /**
   * Constructor
   * @param {ToastrService} toastrService ToastrService
   */
  constructor(private toastrService: ToastrService) {}

  /**
   * Show success notification
   * @param {string} message string
   * @example hello
   */
  success(message: string): void {
    this.toastrService.success(message);
  }

  /**
   * Show error notification
   * @param {string} message string
   */
  error(message: string): void {
    this.toastrService.error(message);
  }

  /**
   * Show info notification
   * @param {string} message string
   */
  info(message: string): void {
    this.toastrService.info(message);
  }

  /**
   * Show warning notification
   * @param {string} message string
   */
  warning(message: string): void {
    this.toastrService.warning(message);
  }
}
