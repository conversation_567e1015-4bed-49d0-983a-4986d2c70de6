import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpEvent, HttpHeaders, HttpParams, HttpRequest } from '@angular/common/http';

export interface ApiOptions {
  headers?: HttpHeaders | { [header: string]: string | string[] };
  params?: HttpParams | { [param: string]: string | string[] };
  [key: string]: any;
}

/**
 * ApiService
 */
@Injectable({ providedIn: 'root' })
export class ApiService {
  /**
   * Constructor
   * @param {HttpClient} http HttpClient
   */
  constructor(private http: HttpClient) {}

  /**
   * Request
   * @param {HttpRequest} req HttpRequest
   * @returns {Observable} Observable
   */
  request(req: HttpRequest<any>): Observable<HttpEvent<any>> {
    return this.http.request(req);
  }

  /**
   * Get Method
   * @param {string} path string
   * @param {ApiOptions} options ApiOptions
   * @returns {Observable} Observable
   */
  get<T>(path: string, options?: ApiOptions): Observable<T> {
    return this.http.get<T>(path, {
      responseType: 'json',
      withCredentials: true,
      ...options,
    });
  }

  /**
   * Post Method
   * @param {string} path string
   * @param {any | null} body any | null
   * @param {ApiOptions} options ApiOptions
   * @returns {Observable} Observable
   */
  post<T>(path: string, body?: any | null, options?: ApiOptions): Observable<T> {
    return this.http.post<T>(path, body, {
      responseType: 'json',
      withCredentials: true,
      ...options,
    });
  }

  /**
   * Put Method
   * @param {string} path string
   * @param {any | null} body any | null
   * @param {ApiOptions} options ApiOptions
   * @returns {Observable} Observable
   */
  put<T>(path: string, body?: any | null, options?: ApiOptions): Observable<T> {
    return this.http.put<T>(path, body, {
      withCredentials: true,
      responseType: 'json',
      ...options,
    });
  }

  /**
   * Patch Method
   * @param {string} path string
   * @param {any | null} body any | null
   * @param {ApiOptions} options ApiOptions
   * @returns {Observable} Observable
   */
  patch<T>(path: string, body: any | null, options?: ApiOptions): Observable<T> {
    return this.http.patch<T>(path, body, {
      withCredentials: true,
      responseType: 'json',
      ...options,
    });
  }

  /**
   * Delete Method
   * @param {string} path string
   * @param {ApiOptions} options ApiOptions
   * @returns {Observable} Observable
   */
  delete<T>(path: string, options?: ApiOptions, body?: any): Observable<T> {
    return this.http.delete<T>(path, {
      body,
      withCredentials: true,
      responseType: 'json',
      ...options,
    });
  }
}
