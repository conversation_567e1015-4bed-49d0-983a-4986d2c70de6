import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { I18nService } from './i18n.service';
import { StorageService } from './storage.service';
import { DEFAULT_LANGUAGE, LOCALSTORAGE_LANG_KEY } from '../models/global.constants';
import { changeCurrentLanguageCode } from '../../stores/shared/shared.actions';

describe('I18nService', () => {
  let service: I18nService;
  let translateService: jasmine.SpyObj<TranslateService>;
  let store: jasmine.SpyObj<Store>;
  let storageService: jasmine.SpyObj<StorageService>;

  beforeEach(() => {
    const translateSpy = jasmine.createSpyObj('TranslateService', [
      'setDefaultLang',
      'use',
      'instant',
      'stream'
    ]);
    const storeSpy = jasmine.createSpyObj('Store', ['dispatch']);
    const storageSpy = jasmine.createSpyObj('StorageService', [
      'setLocalStorage',
      'getLocalStorage'
    ]);

    TestBed.configureTestingModule({
      providers: [
        I18nService,
        { provide: TranslateService, useValue: translateSpy },
        { provide: Store, useValue: storeSpy },
        { provide: StorageService, useValue: storageSpy }
      ]
    });

    service = TestBed.inject(I18nService);
    translateService = TestBed.inject(TranslateService) as jasmine.SpyObj<TranslateService>;
    store = TestBed.inject(Store) as jasmine.SpyObj<Store>;
    storageService = TestBed.inject(StorageService) as jasmine.SpyObj<StorageService>;
  });

  it('No.1: should properly initialize with all required dependencies', () => {
    expect(service).toBeTruthy();
    expect(translateService).toBeTruthy();
    expect(store).toBeTruthy();
    expect(storageService).toBeTruthy();
  });

  it('No.2: should initialize with default language when no language is stored', () => {
    // Arrange
    storageService.getLocalStorage.and.returnValue(undefined);

    // Act
    service = TestBed.inject(I18nService);

    // Assert
    expect(storageService.setLocalStorage).toHaveBeenCalledWith(
      LOCALSTORAGE_LANG_KEY,
      DEFAULT_LANGUAGE
    );
    expect(translateService.setDefaultLang).toHaveBeenCalledWith(DEFAULT_LANGUAGE);
  });

  it('No.3: should change language successfully', () => {
    // Arrange
    const languageCode = 'en';

    // Act
    service.changeLanguage(languageCode);

    // Assert
    expect(translateService.use).toHaveBeenCalledWith(languageCode);
    expect(store.dispatch).toHaveBeenCalledWith(
      changeCurrentLanguageCode({ currentCode: languageCode })
    );
    expect(storageService.setLocalStorage).toHaveBeenCalledWith(
      LOCALSTORAGE_LANG_KEY,
      languageCode
    );
  });

  it('No.4: should get current language code', (done) => {
    // Arrange
    const expectedLanguage = 'en';
    service.changeLanguage(expectedLanguage);

    // Act & Assert
    service.getCurrentLanguageCode().subscribe(language => {
      expect(language).toBe(expectedLanguage);
      done();
    });
  });

  it('No.5: should translate synchronously', () => {
    // Arrange
    const key = 'test.key';
    const expectedTranslation = 'Test Translation';
    translateService.instant.and.returnValue(expectedTranslation);

    // Act
    const result = service.translate(key);

    // Assert
    expect(result).toBe(expectedTranslation);
    expect(translateService.instant).toHaveBeenCalledWith(key, undefined);
  });

  it('No.6: should translate asynchronously', (done) => {
    // Arrange
    const key = 'test.key';
    const expectedTranslation = 'Test Translation';
    translateService.stream.and.returnValue(of(expectedTranslation));

    // Act & Assert
    service.asyncTranslate(key).subscribe(result => {
      expect(result).toBe(expectedTranslation);
      expect(translateService.stream).toHaveBeenCalledWith(key, undefined);
      done();
    });
  });

  it('No.7: should handle array of translation keys', () => {
    // Arrange
    const keys = ['test.key1', 'test.key2'];
    const expectedTranslations = ['Translation 1', 'Translation 2'];
    translateService.instant.and.returnValue(expectedTranslations);

    // Act
    const result = service.translate(keys);

    // Assert
    expect(result).toBe(expectedTranslations.toString());
    expect(translateService.instant).toHaveBeenCalledWith(keys, undefined);
  });

  it('No.8: should handle interpolation params', () => {
    // Arrange
    const key = 'test.key';
    const params = { value: 'test' };
    const expectedTranslation = 'Test value: test';
    translateService.instant.and.returnValue(expectedTranslation);

    // Act
    const result = service.translate(key, params);

    // Assert
    expect(result).toBe(expectedTranslation);
    expect(translateService.instant).toHaveBeenCalledWith(key, params);
  });
});
