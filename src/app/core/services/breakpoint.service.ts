import { Injectable } from '@angular/core';
import { BreakpointObserver } from '@angular/cdk/layout';
import { distinctUntilChanged, map, shareReplay, startWith } from 'rxjs/operators';
import { GLOBAL_CONSTANTS } from '../models/global.constants';

/**
 * BreakpointObserverService
 */
@Injectable({
  providedIn: 'root',
})
export class BreakpointObserverService {
  isMobile$ = this.breakpointObserver.observe([`(max-width: ${GLOBAL_CONSTANTS.screenWidthMax}px)`]).pipe(
    map((breakpointState) => breakpointState.matches),
    startWith(innerWidth <= GLOBAL_CONSTANTS.screenWidthMax),
    distinctUntilChanged(),
    shareReplay(1)
  );

  /**
   * Constructor
   * @param {BreakpointObserver} breakpointObserver BreakpointObserver
   */
  constructor(private breakpointObserver: BreakpointObserver) {}
}
