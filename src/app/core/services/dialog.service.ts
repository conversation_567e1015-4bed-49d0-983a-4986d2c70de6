import { Injectable } from '@angular/core';
import { MatDialog, MatDialogConfig, MatDialogRef } from '@angular/material/dialog';
import { ComponentType } from '@angular/cdk/overlay';
import { PopUpDialogComponent, RightDialogCloseOutsideComponent } from '../../shared/components/dialogs';
import { AutoScaleService } from './auto-scale.service';

/**
 * DialogService
 */
@Injectable({
  providedIn: 'root',
})
export class DialogService {
  /**
   * Constructor
   * @param {MatDialog} dialog MatDialog
   * @param autoScale
   */
  constructor(private dialog: MatDialog, private autoScale: AutoScaleService) { }

  /**
   * Open right dialog
   * @param {ComponentType} embeddedComponent ComponentType
   * @param {MatDialogConfig} options MatDialogConfig
   * @returns {MatDialogRef} MatDialogRef
   */
  openRightDialog<T>(embeddedComponent: ComponentType<T>, options?: MatDialogConfig) {
    const { data, height, width, ...config } = options || {};

    return this.dialog.open(RightDialogCloseOutsideComponent, {
      autoFocus: false,
      position: {
        right: '0px',
        top: '0px',
      },
      height: height ?? 'var(--height--app--full)',
      width: width ?? 'var(--width--right-dialog--default)',
      maxWidth: 'initial',
      panelClass: ['close-outside', 'animate__animated', 'animate__slideInRight'],
      disableClose: false,
      data: {
        embeddedComponent: embeddedComponent,
        ...(data || {}),
      },
      ...(config || {}),
    });
  }

  /**
   * Open pop up
   * @param {ComponentType} embeddedComponent ComponentType
   * @param {MatDialogConfig} options MatDialogConfig
   * @returns {MatDialogRef} MatDialogRef
   */
  openPopUp<T>(embeddedComponent: ComponentType<T>, options?: MatDialogConfig) {
    const { data, id, height, width, panelClass, position, backdropClass, maxWidth, ...config } = options || {};
    return this.dialog.open(PopUpDialogComponent, {
      autoFocus: false,
      id: id,
      height: height ?? '208px',
      width: width ?? '450px',
      position: position,
      panelClass: panelClass ?? ['popup-confirm'],
      backdropClass: backdropClass,
      disableClose: false,
      maxWidth: maxWidth,
      data: {
        embeddedComponent: embeddedComponent,
        ...(data || {}),
      },
      ...(config || {}),
    });
  }

  /**
   * Open dialog
   * @param {ComponentType} component ComponentType
   * @param {MatDialogConfig} options MatDialogConfig
   * @returns {MatDialogRef} MatDialogRef
   */
  open<T, R>(component: ComponentType<T>, options?: MatDialogConfig): MatDialogRef<T, R> {
    return this.dialog.open(component, {
      ...options
    });
  }

  /**
   *
   */
  closeAll(): void {
    this.dialog.closeAll();
  }

  /**
   * Close dialog by ID
   * @param {string} dialogId string
   */
  closeById(dialogId: string): void {
    const dialogRef = this.dialog.getDialogById(dialogId);
    dialogRef?.close();
  }

  /**
   * Get opened dialogs
   * @returns {MatDialogRef[]} MatDialogRef
   */
  get openDialogs() {
    return this.dialog.openDialogs;
  }
}
