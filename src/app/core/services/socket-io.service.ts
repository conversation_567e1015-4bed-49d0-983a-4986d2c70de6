import { Injectable } from '@angular/core';
import { io, Socket } from 'socket.io-client';
import { Store } from '@ngrx/store';
import { Subject, takeUntil } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { I18nService } from './i18n.service';
import { DestroyService } from './destroy.service';
import { selectIsAuthenticated$, selectToken$ } from '../../stores/auth/auth.selectors';
import { concatLatestFrom } from '@ngrx/operators';

/**
 * SocketIoService
 */
@Injectable({
  providedIn: 'root',
})
export class SocketIoService {
  socket?: Socket;

  private cloneTaskSubject = new Subject<boolean>(); // Biến observable để lắng nghe dữ liệu từ socket
  cloneSuccessEvent$ = this.cloneTaskSubject.asObservable();

  /**
   * Constructor
   * @param {Store} store Store
   * @param {I18nService} i18nService I18nService
   * @param {ToastrService} toastrService ToastrService
   * @param {DestroyService} destroy$ DestroyService
   */
  constructor(
    private store: Store,
    private i18nService: I18nService,
    private toastrService: ToastrService,
    private destroy$: DestroyService
  ) {
    this.store
      .select(selectIsAuthenticated$)
      .pipe(
        concatLatestFrom(() => this.store.select(selectToken$)),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: ([isAuth, token]) => {
          if (isAuth && token) {
            this.onInitConnection(token.idToken);
          }

          // TODO: handle disconnect event
        },
      });
  }

  /**
   * Init socketIO connection
   * @param {string} token string
   */
  private onInitConnection(token?: string) {
    const environment = { SOCKET_ENDPOINT: '' }; //FIXME: update later
    this.socket = io(environment.SOCKET_ENDPOINT, {
      transports: ['websocket'],
      query: {
        ...(token && { token }),
      },
    });
  }

  /**
   * Play sound
   */
  playSound() {
    const audio = new Audio();
    audio.src = '../../../assets/sounds/notification.wav';
    audio.load();
    audio.play();
  }
}
