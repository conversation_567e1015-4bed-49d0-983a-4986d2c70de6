import { LoadingService } from './loading.service';
import { TestBed } from '@angular/core/testing';

describe('I18nService', () => {
  let service: LoadingService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [LoadingService],
    });

    service = TestBed.inject(LoadingService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should set isLoading to true', () => {
    service.show();

    service.isLoading.subscribe((isLoading) => {
      expect(isLoading).toBeTrue();
    });
  });

  it('should set isLoading to false', () => {
    service.hide();

    service.isLoading.subscribe((isLoading) => {
      expect(isLoading).toBeFalse();
    });
  });

  it('should return isLoading as observable', () => {
    const result = service.isLoading;

    expect(result).toEqual(service['isLoading$'].asObservable());
  });
});
