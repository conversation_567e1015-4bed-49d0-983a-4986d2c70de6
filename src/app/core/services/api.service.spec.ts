import { TestBed } from '@angular/core/testing';
import { HttpClient, HttpHeaders, HttpParams, HttpRequest } from '@angular/common/http';
import { of } from 'rxjs';
import { ApiService, ApiOptions } from './api.service';

interface TestResponse {
  data: string;
}

describe('ApiService', () => {
  let service: ApiService;
  let httpClient: jasmine.SpyObj<HttpClient>;

  beforeEach(() => {
    const httpSpy = jasmine.createSpyObj('HttpClient', ['request', 'get', 'post', 'put', 'patch', 'delete']);
    
    TestBed.configureTestingModule({
      providers: [
        ApiService,
        { provide: HttpClient, useValue: httpSpy }
      ]
    });

    service = TestBed.inject(ApiService);
    httpClient = TestBed.inject(HttpClient) as jasmine.SpyObj<HttpClient>;
  });

  it('No.1: should properly initialize with HttpClient dependency', () => {
    // Assert
    expect(service).toBeTruthy();
    expect(httpClient).toBeTruthy();
  });

  it('No.2: should make GET request with correct configuration', () => {
    // Arrange
    const path = 'api/test';
    const mockResponse: TestResponse = { data: 'test' };
    const options: ApiOptions = {};
    httpClient.get.and.returnValue(of(mockResponse));

    // Act
    service.get<TestResponse>(path, options).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(httpClient.get).toHaveBeenCalledWith(path, {
        responseType: 'json',
        withCredentials: true,
        ...options
      });
    });
  });

  it('No.3: should make POST request with correct configuration', () => {
    // Arrange
    const path = 'api/test';
    const body = { test: 'data' };
    const mockResponse: TestResponse = { data: 'test' };
    const options: ApiOptions = {};
    httpClient.post.and.returnValue(of(mockResponse));

    // Act
    service.post<TestResponse>(path, body, options).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(httpClient.post).toHaveBeenCalledWith(path, body, {
        responseType: 'json',
        withCredentials: true,
        ...options
      });
    });
  });

  it('No.4: should make PUT request with correct configuration', () => {
    // Arrange
    const path = 'api/test';
    const body = { test: 'data' };
    const mockResponse: TestResponse = { data: 'test' };
    const options: ApiOptions = {};
    httpClient.put.and.returnValue(of(mockResponse));

    // Act
    service.put<TestResponse>(path, body, options).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(httpClient.put).toHaveBeenCalledWith(path, body, {
        responseType: 'json',
        withCredentials: true,
        ...options
      });
    });
  });

  it('No.5: should make PATCH request with correct configuration', () => {
    // Arrange
    const path = 'api/test';
    const body = { test: 'data' };
    const mockResponse: TestResponse = { data: 'test' };
    const options: ApiOptions = {};
    httpClient.patch.and.returnValue(of(mockResponse));

    // Act
    service.patch<TestResponse>(path, body, options).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(httpClient.patch).toHaveBeenCalledWith(path, body, {
        responseType: 'json',
        withCredentials: true,
        ...options
      });
    });
  });

  it('No.6: should make DELETE request with correct configuration', () => {
    // Arrange
    const path = 'api/test';
    const body = { test: 'data' };
    const mockResponse: TestResponse = { data: 'test' };
    const options: ApiOptions = {};
    httpClient.delete.and.returnValue(of(mockResponse));

    // Act
    service.delete<TestResponse>(path, options, body).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(httpClient.delete).toHaveBeenCalledWith(path, {
        body,
        responseType: 'json',
        withCredentials: true,
        ...options
      });
    });
  });

  it('No.7: should handle custom request with HttpRequest object', () => {
    // Arrange
    const request = new HttpRequest<any>('GET', 'api/test');
    const mockResponse = { type: 4, body: { data: 'test' } };
    httpClient.request.and.returnValue(of(mockResponse));

    // Act
    service.request(request).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(httpClient.request).toHaveBeenCalled();
    });
  });

  it('No.8: should handle request with custom headers', () => {
    // Arrange
    const path = 'api/test';
    const headers = new HttpHeaders().set('Custom-Header', 'test');
    const options: ApiOptions = { headers };
    const mockResponse: TestResponse = { data: 'test' };
    httpClient.get.and.returnValue(of(mockResponse));

    // Act
    service.get<TestResponse>(path, options).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(httpClient.get).toHaveBeenCalledWith(path, {
        headers,
        responseType: 'json',
        withCredentials: true
      });
    });
  });

  it('No.9: should handle request with custom params', () => {
    // Arrange
    const path = 'api/test';
    const params = new HttpParams().set('param', 'value');
    const options: ApiOptions = { params };
    const mockResponse: TestResponse = { data: 'test' };
    httpClient.get.and.returnValue(of(mockResponse));

    // Act
    service.get<TestResponse>(path, options).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(httpClient.get).toHaveBeenCalledWith(path, {
        params,
        responseType: 'json',
        withCredentials: true
      });
    });
  });

  it('No.10: should handle request with type safety', () => {
    // Arrange
    interface CustomType {
      id: number;
      name: string;
    }
    const path = 'api/test';
    const mockResponse: CustomType = { id: 1, name: 'test' };
    httpClient.get.and.returnValue(of(mockResponse));

    // Act
    service.get<CustomType>(path).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(response.id).toBeDefined();
      expect(response.name).toBeDefined();
    });
  });
});
