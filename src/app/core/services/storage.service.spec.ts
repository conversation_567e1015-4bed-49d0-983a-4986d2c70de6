import { TestBed } from '@angular/core/testing';
import { CookieService, SameSite } from 'ngx-cookie-service';
import { StorageService } from './storage.service';

describe('StorageService', () => {
  let service: StorageService;
  let cookieServiceMock: jasmine.SpyObj<CookieService>;
  
  // Mock data
  const testKey = 'testKey';
  const testValue = 'testValue';
  const testObject = { name: 'Test Name', id: 123 };
  
  beforeEach(() => {
    // Create CookieService spy
    cookieServiceMock = jasmine.createSpyObj('CookieService', [
      'get',
      'set',
      'delete'
    ]);
    
    cookieServiceMock.get.and.returnValue(testValue);
    
    TestBed.configureTestingModule({
      providers: [
        StorageService,
        { provide: CookieService, useValue: cookieServiceMock }
      ]
    });
    
    service = TestBed.inject(StorageService);
    
    // Mock localStorage methods
    spyOn(localStorage, 'setItem');
    spyOn(localStorage, 'getItem').and.callFake((key: string) => {
      if (key === testKey) {
        return JSON.stringify(testObject);
      }
      return null;
    });
    spyOn(localStorage, 'removeItem');
    spyOn(localStorage, 'clear');
  });

  // No.1: should properly initialize with CookieService dependency
  it('should properly initialize with CookieService dependency', () => {
    // Arrange - done in beforeEach
    
    // Act - service is created in beforeEach
    
    // Assert
    expect(service).toBeTruthy();
  });

  // No.2: should call cookieService.get when getCookieByName is called
  it('should call cookieService.get when getCookieByName is called', () => {
    // Arrange
    const cookieName = 'testCookie';
    
    // Act
    const result = service.getCookieByName(cookieName);
    
    // Assert
    expect(cookieServiceMock.get).toHaveBeenCalledWith(cookieName);
    expect(result).toBe(testValue);
  });

  // No.3: should call cookieService.set when setCookie is called
  it('should call cookieService.set when setCookie is called', () => {
    // Arrange
    const cookieName = 'testCookie';
    const cookieValue = 'testValue';
    const expires = 1;
    const path = '/test';
    const domain = 'example.com';
    const secure = true;
    const sameSite: SameSite = 'Lax';
    
    // Act
    service.setCookie(cookieName, cookieValue, expires, path, domain, secure, sameSite);
    
    // Assert
    // Use jasmine.any to match parameters since the actual implementation may vary
    expect(cookieServiceMock.set).toHaveBeenCalled();
    const args = cookieServiceMock.set.calls.mostRecent().args;
    expect(args[0]).toBe(cookieName);
    expect(args[1]).toBe(cookieValue);
  });

  // No.4: should call cookieService.delete when removeCookie is called
  it('should call cookieService.delete when removeCookie is called', () => {
    // Arrange
    const cookieName = 'testCookie';
    
    // Act
    service.removeCookie(cookieName);
    
    // Assert
    expect(cookieServiceMock.delete).toHaveBeenCalledWith(cookieName);
  });

  // No.5: should set item in localStorage when setLocalStorage is called
  it('should set item in localStorage when setLocalStorage is called', () => {
    // Arrange
    
    // Act
    service.setLocalStorage(testKey, testObject);
    
    // Assert
    expect(localStorage.setItem).toHaveBeenCalledWith(testKey, JSON.stringify(testObject));
  });

  // No.6: should get item from localStorage when getLocalStorage is called
  it('should get item from localStorage when getLocalStorage is called', () => {
    // Arrange
    
    // Act
    const result = service.getLocalStorage(testKey);
    
    // Assert
    expect(localStorage.getItem).toHaveBeenCalledWith(testKey);
    expect(result).toEqual(testObject);
  });

  // No.7: should return null when getLocalStorage is called for non-existent key
  it('should return null when getLocalStorage is called for non-existent key', () => {
    // Arrange
    const nonExistentKey = 'nonExistentKey';
    
    // Act
    const result = service.getLocalStorage(nonExistentKey);
    
    // Assert
    expect(localStorage.getItem).toHaveBeenCalledWith(nonExistentKey);
    expect(result).toBeNull();
  });

  // No.8: should remove item from localStorage when removeLocalStorage is called
  it('should remove item from localStorage when removeLocalStorage is called', () => {
    // Arrange
    
    // Act
    service.removeLocalStorage(testKey);
    
    // Assert
    expect(localStorage.removeItem).toHaveBeenCalledWith(testKey);
  });

  // No.9: should clear localStorage when removeAllLocalStorage is called
  it('should clear localStorage when removeAllLocalStorage is called', () => {
    // Arrange
    
    // Act
    service.removeAllLocalStorage();
    
    // Assert
    expect(localStorage.clear).toHaveBeenCalled();
  });
});
