import { TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogConfig, MatDialogRef } from '@angular/material/dialog';
import { ComponentType } from '@angular/cdk/overlay';
import { DialogService } from './dialog.service';
import { AutoScaleService } from './auto-scale.service';
import { PopUpDialogComponent, RightDialogCloseOutsideComponent } from '../../shared/components/dialogs';
import { Component } from '@angular/core';
import { of } from 'rxjs';

@Component({
  template: '<div>Test Component</div>'
})
class TestComponent {}

describe('DialogService', () => {
  let service: DialogService;
  let dialogMock: jasmine.SpyObj<MatDialog>;
  let autoScaleMock: jasmine.SpyObj<AutoScaleService>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<any>>;

  beforeEach(() => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    
    // Create mock with the openDialogs as a getter
    dialogMock = jasmine.createSpyObj('MatDialog', 
      ['open', 'closeAll', 'getDialogById'],
      { 'openDialogs': [mockDialogRef] }
    );
    
    dialogMock.getDialogById.and.returnValue(mockDialogRef);
    dialogMock.open.and.returnValue(mockDialogRef);
    
    autoScaleMock = jasmine.createSpyObj('AutoScaleService', ['getScale']);
    
    TestBed.configureTestingModule({
      providers: [
        DialogService,
        { provide: MatDialog, useValue: dialogMock },
        { provide: AutoScaleService, useValue: autoScaleMock }
      ]
    });
    
    service = TestBed.inject(DialogService);
  });

  // No.1: should properly initialize with MatDialog and AutoScaleService dependencies
  it('should properly initialize with MatDialog and AutoScaleService dependencies', () => {
    // Arrange - done in beforeEach

    // Act - service is created in beforeEach

    // Assert
    expect(service).toBeTruthy();
  });

  // No.2: should open right dialog with correct configuration
  it('should open right dialog with correct configuration', () => {
    // Arrange
    const testComponent: ComponentType<TestComponent> = TestComponent;
    
    // Act
    const result = service.openRightDialog(testComponent);
    
    // Assert
    expect(dialogMock.open).toHaveBeenCalledWith(
      RightDialogCloseOutsideComponent,
      jasmine.objectContaining({
        autoFocus: false,
        position: {
          right: '0px',
          top: '0px',
        },
        height: 'var(--height--app--full)',
        width: 'var(--width--right-dialog--default)',
        maxWidth: 'initial',
        panelClass: ['close-outside', 'animate__animated', 'animate__slideInRight'],
        disableClose: false,
        data: {
          embeddedComponent: testComponent
        }
      })
    );
    expect(result).toBe(mockDialogRef);
  });

  // No.3: should open right dialog with custom options
  it('should open right dialog with custom options', () => {
    // Arrange
    const testComponent: ComponentType<TestComponent> = TestComponent;
    const customOptions: MatDialogConfig = {
      height: '800px',
      width: '600px',
      data: { customData: 'test' }
    };
    
    // Act
    service.openRightDialog(testComponent, customOptions);
    
    // Assert
    expect(dialogMock.open).toHaveBeenCalledWith(
      RightDialogCloseOutsideComponent,
      jasmine.objectContaining({
        height: '800px',
        width: '600px',
        data: {
          embeddedComponent: testComponent,
          customData: 'test'
        }
      })
    );
  });

  // No.4: should open popup with correct configuration
  it('should open popup with correct configuration', () => {
    // Arrange
    const testComponent: ComponentType<TestComponent> = TestComponent;
    
    // Act
    const result = service.openPopUp(testComponent);
    
    // Assert
    expect(dialogMock.open).toHaveBeenCalledWith(
      PopUpDialogComponent,
      jasmine.objectContaining({
        autoFocus: false,
        height: '208px',
        width: '450px',
        panelClass: ['popup-confirm'],
        disableClose: false,
        data: {
          embeddedComponent: testComponent
        }
      })
    );
    expect(result).toBe(mockDialogRef);
  });

  // No.5: should open popup with custom options
  it('should open popup with custom options', () => {
    // Arrange
    const testComponent: ComponentType<TestComponent> = TestComponent;
    const customOptions: MatDialogConfig = {
      height: '300px',
      width: '600px',
      panelClass: ['custom-class'],
      backdropClass: 'custom-backdrop',
      maxWidth: '800px',
      data: { customData: 'test' }
    };
    
    // Act
    service.openPopUp(testComponent, customOptions);
    
    // Assert
    expect(dialogMock.open).toHaveBeenCalledWith(
      PopUpDialogComponent,
      jasmine.objectContaining({
        height: '300px',
        width: '600px',
        panelClass: ['custom-class'],
        backdropClass: 'custom-backdrop',
        maxWidth: '800px',
        data: {
          embeddedComponent: testComponent,
          customData: 'test'
        }
      })
    );
  });

  // No.6: should open dialog directly with custom options
  it('should open dialog directly with custom options', () => {
    // Arrange
    const testComponent: ComponentType<TestComponent> = TestComponent;
    const customOptions: MatDialogConfig = {
      height: '300px',
      width: '600px'
    };
    
    // Act
    const result = service.open(testComponent, customOptions);
    
    // Assert
    expect(dialogMock.open).toHaveBeenCalledWith(testComponent, customOptions);
    expect(result).toBe(mockDialogRef);
  });

  // No.7: should close all dialogs
  it('should close all dialogs', () => {
    // Arrange - done in beforeEach
    
    // Act
    service.closeAll();
    
    // Assert
    expect(dialogMock.closeAll).toHaveBeenCalled();
  });

  // No.8: should close dialog by ID
  it('should close dialog by ID', () => {
    // Arrange
    const dialogId = 'test-dialog-id';
    
    // Act
    service.closeById(dialogId);
    
    // Assert
    expect(dialogMock.getDialogById).toHaveBeenCalledWith(dialogId);
    expect(mockDialogRef.close).toHaveBeenCalled();
  });

  // No.9: should get open dialogs
  it('should get open dialogs', () => {
    // Arrange - done in beforeEach
    
    // Act
    const openDialogs = service.openDialogs;
    
    // Assert
    expect(openDialogs).toBe(dialogMock.openDialogs);
  });
});
