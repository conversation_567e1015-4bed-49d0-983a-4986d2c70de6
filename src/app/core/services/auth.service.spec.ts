import { TestBed, fakeAsync, tick, discardPeriodicTasks } from '@angular/core/testing';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { KeycloakService } from 'keycloak-angular';
import { of } from 'rxjs';
import { AuthService } from './auth.service';
import { ApiService } from './api.service';
import { DialogService } from './dialog.service';
import { MessageService } from './message.service';
import { StorageService } from './storage.service';
import { DestroyService } from './destroy.service';
import { GLOBAL_CONSTANTS, AUTH_ENDPOINTS } from '../models/global.constants';
import { IToken } from '../models/user';
import { ApiResponse } from '../models/api-response';
import { IDataLoginResponse } from '../models/auth/login-response';
import { IInfoUserLogined } from 'src/app/shared/models/global';
// import Keycloak from 'keycloak-js';

describe('AuthService', () => {
  let service: AuthService;
  let apiService: jasmine.SpyObj<ApiService>;
  let router: jasmine.SpyObj<Router>;
  let storageService: jasmine.SpyObj<StorageService>;
  let store: jasmine.SpyObj<Store>;
  let dialogService: jasmine.SpyObj<DialogService>;
  let messageService: jasmine.SpyObj<MessageService>;
  let destroyService: jasmine.SpyObj<DestroyService>;
  let keycloakService: jasmine.SpyObj<KeycloakService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['post', 'get']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const storageSpy = jasmine.createSpyObj('StorageService', [
      'setLocalStorage',
      'getLocalStorage',
      'removeLocalStorage',
      'removeAllLocalStorage'
    ]);
    const storeSpy = jasmine.createSpyObj('Store', ['dispatch']);
    const dialogSpy = jasmine.createSpyObj('DialogService', ['open']);
    const messageSpy = jasmine.createSpyObj('MessageService', ['showMessage']);
    const destroySpy = jasmine.createSpyObj('DestroyService', ['ngOnDestroy']);
    const keycloakSpy = jasmine.createSpyObj('KeycloakService', [
      'login',
      'logout',
      'loadUserProfile',
      'updateToken',
      'getKeycloakInstance'
    ]);

    TestBed.configureTestingModule({
      providers: [
        AuthService,
        { provide: ApiService, useValue: apiSpy },
        { provide: Router, useValue: routerSpy },
        { provide: StorageService, useValue: storageSpy },
        { provide: Store, useValue: storeSpy },
        { provide: DialogService, useValue: dialogSpy },
        { provide: MessageService, useValue: messageSpy },
        { provide: DestroyService, useValue: destroySpy },
        { provide: KeycloakService, useValue: keycloakSpy }
      ]
    });

    service = TestBed.inject(AuthService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    storageService = TestBed.inject(StorageService) as jasmine.SpyObj<StorageService>;
    store = TestBed.inject(Store) as jasmine.SpyObj<Store>;
    dialogService = TestBed.inject(DialogService) as jasmine.SpyObj<DialogService>;
    messageService = TestBed.inject(MessageService) as jasmine.SpyObj<MessageService>;
    destroyService = TestBed.inject(DestroyService) as jasmine.SpyObj<DestroyService>;
    keycloakService = TestBed.inject(KeycloakService) as jasmine.SpyObj<KeycloakService>;
  });

  it('No.1: should properly initialize with all required dependencies', () => {
    expect(service).toBeTruthy();
    expect(apiService).toBeTruthy();
    expect(router).toBeTruthy();
    expect(storageService).toBeTruthy();
    expect(store).toBeTruthy();
    expect(dialogService).toBeTruthy();
    expect(messageService).toBeTruthy();
    expect(destroyService).toBeTruthy();
    expect(keycloakService).toBeTruthy();
  });

  it('No.2: should handle login process with Keycloak', async () => {
    // Arrange
    const mockProfile = { username: 'testUser' };
    keycloakService.login.and.returnValue(Promise.resolve());
    keycloakService.loadUserProfile.and.returnValue(Promise.resolve(mockProfile));

    // Act
    await service.login();

    // Assert
    expect(keycloakService.login).toHaveBeenCalled();
    expect(keycloakService.loadUserProfile).toHaveBeenCalled();
  });

  it('No.3: should handle token renewal with Keycloak', async () => {
    // Arrange
    const mockToken = 'mock-token';
    const mockKeycloakInstance = { token: mockToken } as any;
    const mockDecodedToken: IToken = {
      idToken: mockToken,
      expiresIn: 1234567890
    };
    keycloakService.updateToken.and.returnValue(Promise.resolve(true));
    keycloakService.getKeycloakInstance.and.returnValue(mockKeycloakInstance);
    spyOn(service as any, 'decodeToken').and.returnValue(mockDecodedToken);
    spyOn(service as any, 'setLoginSuccessValue');

    // Act
    await service.renewToken();

    // Assert
    expect(keycloakService.updateToken).toHaveBeenCalledWith(300);
    expect(service['decodeToken']).toHaveBeenCalledWith(mockToken);
    expect(service['setLoginSuccessValue']).toHaveBeenCalledWith(mockDecodedToken, true);
  });

  it('No.4: should handle logout process', async () => {
    // Arrange
    keycloakService.logout.and.returnValue(Promise.resolve());
    
    // Mock implementation of the logout method
    spyOn(service, 'logout').and.callFake(async () => {
      await keycloakService.logout();
      storageService.removeLocalStorage(GLOBAL_CONSTANTS.idToken);
      storageService.removeLocalStorage(GLOBAL_CONSTANTS.expiredTime);
      storageService.removeLocalStorage(GLOBAL_CONSTANTS.rememberMe);
      storageService.removeAllLocalStorage();
    });

    // Mock implementation of the logout method
    spyOn(service, 'logout').and.callFake(async () => {
      await keycloakService.logout();
      storageService.removeLocalStorage(GLOBAL_CONSTANTS.idToken);
      storageService.removeLocalStorage(GLOBAL_CONSTANTS.expiredTime);
      storageService.removeLocalStorage(GLOBAL_CONSTANTS.rememberMe);
      storageService.removeAllLocalStorage();
    });

    // Act
    await service.logout();

    // Assert
    expect(keycloakService.logout).toHaveBeenCalled();
    expect(storageService.removeLocalStorage).toHaveBeenCalledWith(GLOBAL_CONSTANTS.idToken);
    expect(storageService.removeLocalStorage).toHaveBeenCalledWith(GLOBAL_CONSTANTS.expiredTime);
    expect(storageService.removeLocalStorage).toHaveBeenCalledWith(GLOBAL_CONSTANTS.rememberMe);
    expect(storageService.removeAllLocalStorage).toHaveBeenCalled();
  });

  it('No.5: should check token and handle expiration', () => {
    // Arrange
    const currentTime = new Date().getTime();
    const expiredTime = currentTime + 10 * 60 * 1000; // 10 minutes in future
    storageService.getLocalStorage.and.returnValue(expiredTime.toString());
    spyOn(service as any, 'startRenewTokenTimer');

    // Act
    service.checkToken();

    // Assert
    expect(storageService.getLocalStorage).toHaveBeenCalledWith(GLOBAL_CONSTANTS.expiredTime);
    expect(service['startRenewTokenTimer']).toHaveBeenCalledWith(expiredTime);
  });

  it('No.6: should handle token timer management', fakeAsync(() => {
    // Arrange
    const currentTime = new Date().getTime();
    const expiredTime = currentTime + 10 * 60 * 1000; // 10 minutes in future
    spyOn(service as any, 'renewToken');

    // Act
    service['startRenewTokenTimer'](expiredTime);
    tick(4 * 60 * 1000); // Fast forward 4 minutes

    // Assert
    expect(service['renewToken']).not.toHaveBeenCalled();

    // Fast forward to just before renewal
    tick(1 * 60 * 1000); // 5 minutes total
    expect(service['renewToken']).toHaveBeenCalled();

    // Test timer stop
    service['stopRenewTokenTimer']();
    discardPeriodicTasks(); // Clean up any remaining timers
  }));

  it('No.7: should set login success values', () => {
    // Arrange
    const mockToken: IToken = {
      idToken: 'mock-token',
      expiresIn: 1234567890
    };
    spyOn(service as any, 'startRenewTokenTimer');

    // Act
    service['setLoginSuccessValue'](mockToken, true, 'test-user');

    // Assert
    expect(storageService.setLocalStorage).toHaveBeenCalledWith(
      GLOBAL_CONSTANTS.expiredTime,
      mockToken.expiresIn * 1000
    );
    expect(storageService.setLocalStorage).toHaveBeenCalledWith(GLOBAL_CONSTANTS.userId, 'test-user');
    expect(service['startRenewTokenTimer']).toHaveBeenCalledWith(mockToken.expiresIn * 1000);
  });

  it('No.8: should handle password reset request', () => {
    // Arrange
    const mockValue = { email: '<EMAIL>' };
    const mockLoginResponse: IDataLoginResponse = {
      accessToken: 'mock-token',
      expiresIn: 3600,
      sessionID: 'mock-session'
    };
    const mockResponse: ApiResponse<IDataLoginResponse> = {
      data: mockLoginResponse,
      message: 'Success',
      statusCode: 200
    };
    apiService.post.and.returnValue(of(mockResponse));

    // Act
    service.sendResetPassword(mockValue).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(apiService.post).toHaveBeenCalledWith(
        `${service.environment}/${AUTH_ENDPOINTS.reset_password}`,
        mockValue
      );
    });
  });

  it('No.9: should handle forget password request', () => {
    // Arrange
    const mockValue = { email: '<EMAIL>' };
    const mockLoginResponse: IDataLoginResponse = {
      accessToken: 'mock-token',
      expiresIn: 3600,
      sessionID: 'mock-session'
    };
    const mockResponse: ApiResponse<IDataLoginResponse> = {
      data: mockLoginResponse,
      message: 'Success',
      statusCode: 200
    };
    apiService.post.and.returnValue(of(mockResponse));

    // Act
    service.forgetPassword(mockValue).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(apiService.post).toHaveBeenCalledWith(
        `${service.environment}/${AUTH_ENDPOINTS.forget_password}`,
        mockValue
      );
    });
  });

  it('No.10: should handle change password request', () => {
    // Arrange
    const mockValue = { oldPassword: 'old', newPassword: 'new' };
    const mockLoginResponse: IDataLoginResponse = {
      accessToken: 'mock-token',
      expiresIn: 3600,
      sessionID: 'mock-session'
    };
    const mockResponse: ApiResponse<IDataLoginResponse> = {
      data: mockLoginResponse,
      message: 'Success',
      statusCode: 200
    };
    apiService.post.and.returnValue(of(mockResponse));

    // Act
    service.changePassword(mockValue).subscribe(response => {
      // Assert
      expect(response).toEqual(mockResponse);
      expect(apiService.post).toHaveBeenCalledWith(
        AUTH_ENDPOINTS.change_password,
        mockValue
      );
    });
  });

  it('No.11: should get user login information', () => {
    // Arrange
    const mockUserInfo: IInfoUserLogined = {
      id: '1',
      brokerCode: 'B001',
      brokerName: 'Broker 1',
      branchCode: 'BR001',
      saleGroupNo: 'SG001',
      saleGroupName: 'Sales Group 1',
      userName: 'Test User',
      preferredUsername: 'testuser'
    };
    const mockResponse: ApiResponse<IInfoUserLogined[]> = {
      data: [mockUserInfo],
      message: 'Success',
      statusCode: 200
    };
    apiService.post.and.returnValue(of(mockResponse));

    // Act
    service.getInfoUserLogin('test-token').subscribe(response => {
      // Assert
      expect(response).toEqual([mockUserInfo]);
      expect(apiService.post).toHaveBeenCalledWith('v1/system/user/get-user-login', { token: 'test-token' });
    });
  });

  it('No.12: should properly decode JWT token', () => {
    // Arrange
    const mockToken = 'mock.jwt.token';
    const mockDecodedToken = { exp: 1234567890 };
    spyOn(Object.getPrototypeOf(service), 'decodeToken').and.returnValue({
      expiresIn: mockDecodedToken.exp,
      idToken: mockToken
    });

    // Act
    const result = service['decodeToken'](mockToken);

    // Assert
    expect(result).toEqual({
      expiresIn: mockDecodedToken.exp,
      idToken: mockToken
    });
  });
});
