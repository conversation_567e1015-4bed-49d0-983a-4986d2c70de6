import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { LocationService } from './location.service';
import { ApiService } from './api.service';

describe('LocationService', () => {
  let service: LocationService;
  let apiServiceMock: jasmine.SpyObj<ApiService>;
  
  // Mock location data
  const mockLocationData = {
    countries: [
      { id: 1, name: 'United States' },
      { id: 2, name: 'Canada' }
    ]
  };

  beforeEach(() => {
    // Create ApiService spy
    apiServiceMock = jasmine.createSpyObj('ApiService', ['get']);
    
    // Configure the spy to return mock data
    apiServiceMock.get.and.returnValue(of(mockLocationData));
    
    TestBed.configureTestingModule({
      providers: [
        LocationService,
        { provide: ApiService, useValue: apiServiceMock }
      ]
    });
    
    service = TestBed.inject(LocationService);
  });

  // No.1: should properly initialize with ApiService dependency
  it('should properly initialize with ApiService dependency', () => {
    // Arrange - done in beforeEach
    
    // Act - service is created in beforeEach
    
    // Assert
    expect(service).toBeTruthy();
  });

  // No.2: should call getLocations and return location data
  it('should call getLocations and return location data', () => {
    // Arrange - done in beforeEach
    
    // Act
    let result: any;
    service.getLocations().subscribe(data => {
      result = data;
    });
    
    // Assert
    expect(apiServiceMock.get).toHaveBeenCalledWith('assets/location/location.json');
    expect(result).toEqual(mockLocationData);
  });
});
