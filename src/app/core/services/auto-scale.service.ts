import { HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

/**
 * Auto-scale Service
 */
@Injectable({ providedIn: 'root' })
export class AutoScaleService {
  private _appHeight = 0;
  private _scaleRatio = 1;
  private _renderer: Renderer2;

  /**
   * @param rendererFactory
   */
  constructor(rendererFactory: RendererFactory2) {
    this._renderer = rendererFactory.createRenderer(null, null);
  }
  /**
   * Set height after doing some calculation
   */
  set setAppHeight(value: number) {
    this._appHeight = value;
  }

  /**
   * Get Height in pixels of the screen
   */
  get appHeight(): number {
    return this._appHeight;
  }

  /**
   * Set height after doing some calculation
   */
  set setScaleRatio(value: number) {
    this._scaleRatio = value;
  }

  /**
   * Get height in pixels of the screen
   */
  get scaleRatio(): number {
    return this._scaleRatio;
  }

  /**
   * High-level approach that allows Renderer2 to set multiple styles
   * @param element
   */
  setMultipleStyles(element: HTMLElement, styles: { [key: string]: string }): void {
    for (const [key, value] of Object.entries(styles)) {
      this._renderer.setStyle(element, key, value);
    }
  }
}
