import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Store } from '@ngrx/store';
import { ISuportedLanguages } from '../../shared/models';
import { DEFAULT_LANGUAGE, LOCALSTORAGE_LANG_KEY } from '../models/global.constants';
import { changeCurrentLanguageCode } from '../../stores/shared/shared.actions';
import { StorageService } from './storage.service';

/**
 * I18nService
 */
@Injectable({
  providedIn: 'root',
})
export class I18nService {
  private currentLanguage$ = new BehaviorSubject<string>(DEFAULT_LANGUAGE);

  suportedLanguages: ISuportedLanguages[] = [];

  /**
   * Constructor
   * @param {TranslateService} translateService TranslateService
   * @param {Store} store Store
   * @param {StorageService} storageService StorageService
   */
  constructor(
    private translateService: TranslateService,
    private store: Store,
    private storageService: StorageService
  ) {
    let currentLanguage = undefined;

    if (!currentLanguage) {
      currentLanguage = DEFAULT_LANGUAGE;
      this.storageService.setLocalStorage(LOCALSTORAGE_LANG_KEY, currentLanguage);
      this.translateService.setDefaultLang(currentLanguage);
      return;
    }

    this.changeLanguage(currentLanguage);
  }

  /**
   * Change language
   * @param {string} languageCode string
   */
  changeLanguage(languageCode: string): void {
    this.translateService.use(languageCode);
    this.currentLanguage$.next(languageCode);
    this.store.dispatch(changeCurrentLanguageCode({ currentCode: languageCode }));
    this.storageService.setLocalStorage(LOCALSTORAGE_LANG_KEY, languageCode);
  }

  /**
   * Get current language code
   * @returns {Observable} Observable
   */
  getCurrentLanguageCode(): Observable<string> {
    return this.currentLanguage$.asObservable();
  }

  /**
   * Synchronous Translate
   * @param {string | Array<string>} key key string
   * @param {object} interpolateParams Object
   * @returns {string} string
   */
  translate(key: string | Array<string> = '', interpolateParams?: object): string {
    return String(this.translateService.instant(key, interpolateParams));
  }

  /**
   * Asynchronous Translate
   * @param {string | Array<string>} key string | Array<string>
   * @param {object | undefined} interpolateParams object | undefined
   * @returns {Observable} Observable
   */
  asyncTranslate(key: string | Array<string>, interpolateParams?: object | undefined): Observable<string> {
    return this.translateService.stream(key, interpolateParams).pipe(map((value) => String(value)));
  }
}
