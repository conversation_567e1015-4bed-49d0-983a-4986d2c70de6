import { Inject, Injectable } from '@angular/core';
import { Observable, of, tap, map, switchMap, take, takeUntil, catchError } from 'rxjs';
import { Store } from '@ngrx/store';
import { StorageService } from '../services/storage.service';
import { AuthService, DestroyService, MessageService } from '../services';
import { GLOBAL_CONSTANTS } from '../models/global.constants';
import { KeycloakService } from 'keycloak-angular';
import { saveInfoUserLogin } from 'src/app/stores/auth/auth.actions';
import { Router } from '@angular/router';

/**
 *
 */
@Injectable()
export class AppInitializer {
  /**
   * Constructor
   * @param {StorageService} storageService StorageService
   * @param {AuthService} authService AuthService
   * @param {KeycloakService} keyCloak keyCloak
   */
  constructor(
    @Inject(StorageService) private storageService: StorageService,
    @Inject(AuthService) private authService: AuthService,
    private keyCloak: KeycloakService,
    private store: Store,
    private _destroy: DestroyService,
    private router: Router,
    private messageService: MessageService
  ) {}

  /**
   * Auth
   * @returns {Observable} Observable
   */
  auth() {
    of(null)
      .pipe(
        map(() => {
          // Check token expires and remember me
          const currentTime = new Date().getTime();
          const expiredTime = +this.storageService.getLocalStorage(GLOBAL_CONSTANTS.expiredTime);
          return expiredTime < currentTime;
        }),
        switchMap((isExpired) => {
          if (isExpired) {
            this.authService.renewToken();
            this.storageService.removeAllLocalStorage();
          }

          return of(true);
        })
      )
      .subscribe();
  }
}

/**
 * Initialize Token Factory
 * @param {AppInitializer} appInitializer AppInitializer
 * @returns {Observable} Observable
 */
export function initializeTokenFactory(appInitializer: AppInitializer): () => any {
  return () => appInitializer.auth();
}
