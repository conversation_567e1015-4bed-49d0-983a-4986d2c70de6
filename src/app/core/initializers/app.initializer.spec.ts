import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { KeycloakService } from 'keycloak-angular';
import { AppInitializer, initializeTokenFactory } from './app.initializer';
import { AuthService } from '../services/auth.service';
import { DestroyService } from '../services/destroy.service';
import { MessageService } from '../services/message.service';
import { StorageService } from '../services/storage.service';
import { GLOBAL_CONSTANTS } from '../models/global.constants';

describe('AppInitializer', () => {
  let initializer: AppInitializer;
  let storageService: jasmine.SpyObj<StorageService>;
  let authService: jasmine.SpyObj<AuthService>;
  let keycloakService: jasmine.SpyObj<KeycloakService>;
  let store: jasmine.SpyObj<Store>;
  let destroyService: jasmine.SpyObj<DestroyService>;
  let router: jasmine.SpyObj<Router>;
  let messageService: jasmine.SpyObj<MessageService>;

  beforeEach(() => {
    const storageSpy = jasmine.createSpyObj('StorageService', ['getLocalStorage', 'removeAllLocalStorage']);
    const authSpy = jasmine.createSpyObj('AuthService', ['renewToken']);
    const keycloakSpy = jasmine.createSpyObj('KeycloakService', ['isLoggedIn']);
    const storeSpy = jasmine.createSpyObj('Store', ['dispatch']);
    const destroySpy = jasmine.createSpyObj('DestroyService', ['ngOnDestroy']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const messageSpy = jasmine.createSpyObj('MessageService', ['showMessage']);

    TestBed.configureTestingModule({
      providers: [
        AppInitializer,
        { provide: StorageService, useValue: storageSpy },
        { provide: AuthService, useValue: authSpy },
        { provide: KeycloakService, useValue: keycloakSpy },
        { provide: Store, useValue: storeSpy },
        { provide: DestroyService, useValue: destroySpy },
        { provide: Router, useValue: routerSpy },
        { provide: MessageService, useValue: messageSpy }
      ]
    });

    initializer = TestBed.inject(AppInitializer);
    storageService = TestBed.inject(StorageService) as jasmine.SpyObj<StorageService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    keycloakService = TestBed.inject(KeycloakService) as jasmine.SpyObj<KeycloakService>;
    store = TestBed.inject(Store) as jasmine.SpyObj<Store>;
    destroyService = TestBed.inject(DestroyService) as jasmine.SpyObj<DestroyService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    messageService = TestBed.inject(MessageService) as jasmine.SpyObj<MessageService>;
  });

  it('No.1: should properly initialize with all required services', () => {
    // Assert
    expect(initializer).toBeTruthy();
    expect(storageService).toBeTruthy();
    expect(authService).toBeTruthy();
    expect(keycloakService).toBeTruthy();
    expect(store).toBeTruthy();
    expect(destroyService).toBeTruthy();
    expect(router).toBeTruthy();
    expect(messageService).toBeTruthy();
  });

  it('No.2: should renew token and clear storage when token is expired', () => {
    // Arrange
    const currentTime = new Date().getTime();
    const expiredTime = currentTime - 1000; // Set expired time to 1 second ago
    storageService.getLocalStorage.and.returnValue(expiredTime.toString());

    // Act
    initializer.auth();

    // Assert
    expect(storageService.getLocalStorage).toHaveBeenCalledWith(GLOBAL_CONSTANTS.expiredTime);
    expect(authService.renewToken).toHaveBeenCalled();
    expect(storageService.removeAllLocalStorage).toHaveBeenCalled();
  });

  it('No.3: should not renew token when token is not expired', () => {
    // Arrange
    const currentTime = new Date().getTime();
    const expiredTime = currentTime + 3600000; // Set expired time to 1 hour in future
    storageService.getLocalStorage.and.returnValue(expiredTime.toString());

    // Act
    initializer.auth();

    // Assert
    expect(storageService.getLocalStorage).toHaveBeenCalledWith(GLOBAL_CONSTANTS.expiredTime);
    expect(authService.renewToken).not.toHaveBeenCalled();
    expect(storageService.removeAllLocalStorage).not.toHaveBeenCalled();
  });

  it('No.4: should handle missing expiration time in storage', () => {
    // Arrange
    storageService.getLocalStorage.and.returnValue(null);

    // Act
    initializer.auth();

    // Assert
    expect(storageService.getLocalStorage).toHaveBeenCalledWith(GLOBAL_CONSTANTS.expiredTime);
    expect(authService.renewToken).toHaveBeenCalled();
    expect(storageService.removeAllLocalStorage).toHaveBeenCalled();
  });

  it('No.5: should properly initialize using factory function', () => {
    // Arrange
    const factory = initializeTokenFactory(initializer);
    spyOn(initializer, 'auth');

    // Act
    factory();

    // Assert
    expect(initializer.auth).toHaveBeenCalled();
  });
});
