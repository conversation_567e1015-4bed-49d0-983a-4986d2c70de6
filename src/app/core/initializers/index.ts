import { APP_INITIALIZER } from '@angular/core';
import { AppInitializer, initializeTokenFactory } from './app.initializer';
import { KeycloakService } from 'keycloak-angular';
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';

/**
 * Init keycloak
 * @param {KeycloakService} keycloak
 */
function initializeKeycloak(keycloak: KeycloakService, appInitializer: AppInitializer) {
  const { keycloak: config } = environment;
  const { url, realm, clientId } = config;
  return () =>
    keycloak
      .init({
        config: {
          url,
          realm,
          clientId,
        },
        bearerExcludedUrls: [],
        // loadUserProfileAtStartUp: true,
        initOptions: {
          onLoad: 'check-sso',
          flow: 'implicit',
          // checkLoginIframe: false,
          silentCheckSsoRedirectUri: window.location.origin + '/assets/silent-check-sso.html',
        },
      })
      .then(() => {
        appInitializer.auth();
      });
}

export const appInitializers = [
  // {
  //   provide: APP_INITIALIZER,
  //   useFactory: initializeTokenFactory,
  //   deps: [AppInitializer],
  //   multi: true,
  // },
  {
    provide: APP_INITIALIZER,
    useFactory:
      (i18nService: TranslateService, keycloakService: KeycloakService, appInitializer: AppInitializer) => () => {
        return i18nService
          .use('vi')
          .toPromise()
          .then(() => initializeKeycloak(keycloakService, appInitializer)());
      },
    deps: [TranslateService, KeycloakService, AppInitializer],
    multi: true,
  },
];
