import { CodeServer } from 'src/app/core/models/server-config/code-server.enum';
import { IErrorMessage } from './client-error.model';

interface ErrorInfo {
  field: string;
  message: string;
  value: unknown;
}

interface DataError {
  errors: ErrorInfo[];
}

export interface IResponseError extends IErrorMessage {
  data: DataError | undefined;
}

/**
 * Response Error
 */
export class ResponseError extends Error {
  code: CodeServer;
  data?: DataError;

  /**
   * Constructor
   * @param {IResponseError} Error IResponseError
   */
  constructor({ message, code, data }: IResponseError) {
    super(message);
    this.code = code;
    this.data = data;
  }
}
