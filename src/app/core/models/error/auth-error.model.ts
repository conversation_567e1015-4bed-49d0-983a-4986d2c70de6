import { IErrorMessage } from './client-error.model';

export interface IAuthError extends IErrorMessage {
  description: string;
}

/**
 * AuthError
 */
export class AuthError extends Error {
  code: number;
  description: string;

  /**
   * Constructor
   * @param {IAuthError} error IAuthError
   */
  constructor({ message, code, description }: IAuthError) {
    super(message);
    this.code = code;
    this.description = description;
  }
}
