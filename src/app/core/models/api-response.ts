import { CodeServer } from './server-config/code-server.enum';

export interface IApiResponse<T> {
  message: string;
  data: T;
  statusCode: number;
}

/**
 * ApiResponse
 */
export class ApiResponse<T> implements IApiResponse<T> {
  message: string;
  data!: T;
  statusCode: CodeServer | number;

  /**
   * Constructor
   */
  constructor() {
    this.message = '';
    this.statusCode = 200;
  }
}
