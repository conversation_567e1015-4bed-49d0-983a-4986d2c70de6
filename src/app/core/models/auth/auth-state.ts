import { IToken, IUser } from 'src/app/core/models/user';
import { IInfoUserLogined } from 'src/app/shared/models/global';

export interface IAuthState {
  isAuthenticated: boolean | null;
  user: IUser | null;
  error: string | null;
  token: IToken | null;
  isRememberMe: boolean | null;
  setAccountPermissionsTime: number;
  isCompleteSetAccountPermissions: boolean | null;
  userLogin: IInfoUserLogined[] | null;
}
