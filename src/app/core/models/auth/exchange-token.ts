/**
 * Interface for exchanging a refresh token for an ID token
 */
export interface IExchangeTokenRequest {
  grant_type: 'refresh_token';
  refresh_token: string;
  token: string;
  returnSecureToken: boolean;
  tenantId: string;
}

export interface IExchangeTokenResponse {
  expiresIn: string;
  email: string;
  refresh_token: string;
  localId: string;
  displayName: string;
  idToken: string;
  kind: string;
  emailVerified: boolean;
}
