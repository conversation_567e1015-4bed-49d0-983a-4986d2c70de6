import { IOption } from 'src/app/shared/models/dropdown-item.model';
import { LoginRequest } from './auth';

export const GLOBAL_CONSTANTS = {
  idToken: 'id_token',
  userId: 'user_id',
  rememberMe: 'rememberMe',
  expiredTime: 'expiredTime',
  screenWidthMax: 768,
};

// Danh sách các message thường gặp với lỗi ở form input
export const FORM_ERROR_MSG: Record<string, string> = {
  email: 'MES-102',
  required: 'MES-103',
  min: 'MES-104',
  max: 'MES-104',
  minlength: 'MES-105',
  maxlength: 'MES-106',
  pattern: 'MES-107',
  passwordNotMatch: 'MES-108',
  formArrItemDuplicate: 'MES-109',
  percentInvalid: 'MES-110',
  stockCompanyInvalid: 'MES-141',
  stopLossInvalid: 'MES-301',
  openPriceMinValid: 'MES-302',
  validatorNewPassword: 'MES-472',
  totalProportion: 'MES-534',
  valueInvalid: 'MES-552',
  minValueInvalid: 'MES-580',
  takeProfitInvalid: 'MES-588',
  validFromTo: 'MES-589',
  dateFromToInvalid: 'MES-593',
  invalidPattern: 'MES-594',
  matDatepickerMax: 'MES-595',
  matDatepickerMin: 'MES-593',
  orderVolumeMaxInvalid: 'MES-600',
  orderPriceMinValid: 'MES-601',
  orderPriceMaxValid: 'MES-602',
  orderTimeSessionValid: 'MES-605',
  isNotMultipleOf100: 'MES-647',
  isNotMultipleOf100AndLarger100: 'MES-648',
  orderVolumeBuyMinValid: 'MES-671',
  orderVolumeSellMinValid: 'MES-672',
};

// Một số key đặc biệt dùng để xử lý error message của form
export const VALIDATION_KEY: Record<string, string> = {
  maxlength: 'Length',
  minlength: 'Length',
  pattern: 'Pattern',
};

export const DEFAULT_LANGUAGE = 'vi';

export const SUPPORTED_LANGUAGE_TO_LABEL: { [key: string]: string } = {
  vi: 'Tiếng Việt',
  ja: 'Tiếng Nhật',
};

export const SUPPORTED_LANGUAGE_TO_AREA_CODE: { [key: string]: string } = {
  vi: '+84',
  ja: '+81',
};

export const AREA_CODE_TO_MAX_LENGTH: { [key: string]: number } = {
  vi: 10,
  ja: 11,
};

export const SUPPORTED_LANGUAGE_TO_ICON: { [key: string]: string } = {
  vi: '/assets/icons/vn.svg',
  ja: '/assets/icons/jp.svg',
};

export const LOCALSTORAGE_LANG_KEY = 'language';

export const ROUTE_PATH = {
  login: '/login',
  register: 'auth/register',
  home: 'home',
  forgotPassword: 'reset-password',
  appointments: '/appointments',
  user: 'users',
};

export const AUTH_ENDPOINTS = {
  login: 'Login',
  renew_token: 'accounts/refresh-token',
  reset_password: 'accounts/send-recovery-email',
  forget_password: 'accounts/forget-password',
  change_password: 'accounts/change-password',
};

export enum REALTIME_TYPE {}

export const DEFAULT = {
  DEBOUNCE_TIME: 500,
  PAGINATION: {
    TOTAL: 0,
    PAGE_SIZE: 20,
    PAGE_INDEX: 1,
  },
};

export const CAGEGORY_ID_TO_LABEL: { [key: string]: string } = {
  1: 'MES-78',
  2: 'MES-544',
  3: 'MES-545',
  4: 'MES-80',
  5: 'MES-546',
  6: 'MES-547',
  7: 'MES-79',
  8: 'MES-429',
  9: 'MES-548',
};

export const BASE_TASK_STATUS_OPTIONS_TEXT: IOption[] = [
  {
    label: 'MES-143',
    value: 'not',
  },
  {
    label: 'MES-144',
    value: 'doing',
  },
  {
    label: 'MES-145',
    value: 'pending',
  },
  {
    label: 'MES-146',
    value: 'confirm',
  },
  {
    label: 'MES-116',
    value: 'done',
  },
  {
    label: 'MES-147',
    value: 'cancel',
  },
];

export const MAX_NOTIFICATIONS = 99;

export const SUB_ACCOUNT_TO_LABEL: { [key: string]: string } = {
  '00': 'MES-656',
  '01': 'MES-657',
  '03': 'MES-658',
  '04': 'MES-659',
  '08': 'MES-660',
  '80': 'MES-661',
};

export const LOGINPAYLOAD_TEST : LoginRequest = {
  password: 'password123',  // NOSONAR
  staffID: 'testuser',
  rememberMe: true
}

export const INPUT_PASSWORD_CONFIG = '[data-testid="password-input"]';
