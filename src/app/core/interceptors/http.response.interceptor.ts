import { Injectable } from '@angular/core';
import { HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, of, switchMap, EMPTY } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ApiService, AuthService, MessageService } from '../services';
import { Store } from '@ngrx/store';
import { CodeServer } from '../models/server-config/code-server.enum';

/**
 * Error Response Interceptor
 */
@Injectable()
export class HttpResponseInterceptor implements HttpInterceptor {
  /**
   * Constructor
   * @param {AuthService} authService AuthService
   * @param {ApiService} apiService ApiService
   * @param {Store} store Store
   * @param {MessageService} message MessageService
   */
  constructor(
    private authService: AuthService,
    private apiService: ApiService,
    private store: Store,
    private message: MessageService
  ) {}

  /**
   * Intercept
   * @param {HttpRequest} req HttpRequest
   * @param {HttpHandler} next HttpHandler
   * @returns {Observable} Observable
   */
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((exception: any) => {
        if (exception.status === CodeServer.unauthorized) {
          // return this.authService.renewToken().pipe(
          //   tap(({ token }) => {
          //     console.log('renew ok!');

          //     // Update token in cookie and in store
          //     this.authService.setLoginSuccessValue(token);
          //     // This.store.dispatch(renewTokenSuccess({ token })); //FIXME: update later
          //   }),
          //   // Retry the fail request
          //   switchMap(() => this.apiService.request(req)),
          //   catchError(() => {
          //     // If renew token fail after 2 times of retrying -> force user to logout
          //     // Void this.store.dispatch(renewTokenFailure({ error: '' })); //FIXME: update later
          //     // Void this.store.dispatch(logout());  //FIXME: update later
          //     return of();
          //   })
          // );
        }
        if (exception.status === CodeServer.notHavePermission) {
          this.message.error(exception.error.message);

          // return this.authService.logout().pipe(
          //   tap(() => {
          //     console.log('logout');
          //   }),
          //   switchMap(() => {
          //     // Void this.store.dispatch(logout());  //FIXME: update later
          //     return EMPTY;
          //   }),
          //   catchError(() => {
          //     // Void this.store.dispatch(logout());  //FIXME: update later
          //     return of();
          //   })
          // );
        }

        throw exception;
      })
    );
  }
}
