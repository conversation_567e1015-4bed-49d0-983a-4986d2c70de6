import { Injectable } from '@angular/core';
import { HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Logger } from '../helpers/logger';

/**
 * HttpLoggerInterceptor
 */
@Injectable()
export class HttpLoggerInterceptor implements HttpInterceptor {
  /**
   * Intercept
   * @param {HttpRequest} req HttpRequest
   * @param {HttpHandler} next HttpHandler
   * @returns {Observable} Observable
   */
  intercept(req: HttpRequest<unknown>, next: HttpHand<PERSON>): Observable<HttpEvent<unknown>> {
    const logger = new Logger('HTTP Request');
    logger.log(`Out going request: ${req.url}`, {
      headers: req.headers,
      body: req.body,
      params: req.params,
    });
    return next.handle(req);
  }
}
