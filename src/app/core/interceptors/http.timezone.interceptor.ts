import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';

/**
 * HttpTimeZoneInterceptor
 */
@Injectable()
export class HttpTimeZoneInterceptor implements HttpInterceptor {
  /**
   * Intercept
   * @param {HttpRequest} req HttpRequest
   * @param {HttpHandler} next HttpHandler
   * @returns {Observable} Observable
   */
  intercept(req: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    const clientTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const modifiedRequest = req.clone({
      setHeaders: {
        'X-TimeZone': clientTimeZone,
      },
    });
    return next.handle(modifiedRequest);
  }
}
