import { Inject, Injectable } from '@angular/core';
import { HttpE<PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { StorageService } from '../services/storage.service';

/**
 * HttpTokenInterceptor
 */
@Injectable()
export class HttpTokenInterceptor implements HttpInterceptor {
  /**
   * Constructor
   * @param {StorageService} storageService StorageService
   */

  constructor(@Inject(StorageService) private storageService: StorageService) {}

  /**
   * Intercept
   * @param {HttpRequest} req HttpRequest
   * @param {HttpHandler} next HttpHandler
   * @returns {Observable} Observable
   */
  intercept(req: HttpRequest<unknown>, next: <PERSON>ttpHandler): Observable<HttpEvent<unknown>> {
    const token = '123';
    if (!token) {
      return next.handle(req);
    }
    const authRequest = req.clone({
      headers: req.headers.set('Authorization', 'Bearer ' + token),
    });
    return next.handle(authRequest);
  }
}
