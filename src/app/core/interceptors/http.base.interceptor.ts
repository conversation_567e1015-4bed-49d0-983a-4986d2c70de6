import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest } from '@angular/common/http';
import { catchError, delay, Observable, retry, throwError, timeout } from 'rxjs';
import { environment } from 'src/environments/environment';

/**
 * HttpBaseInterceptor
 */
@Injectable()
export class HttpBaseInterceptor implements HttpInterceptor {
  /**
   * Intercept
   * @param {HttpRequest} req HttpRequest
   * @param {HttpHandler} next HttpHandler
   * @returns {Observable} Observable
   */
  intercept(req: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    const isAbsoluteUrl = req.url.startsWith('http');
    const isImage = req.url.endsWith('.svg');
    if (isImage) {
      return next.handle(req);
    }
    return next
      .handle(
        req.clone({
          url: isAbsoluteUrl ? req.url : `${environment.shaBackend}/${req.url}`,
        })
      )
      .pipe(
        retry(1),
        timeout(30000),
        delay(500),
        catchError((error) => {
          return throwError(() => error.error);
        })
      );
  }
}
