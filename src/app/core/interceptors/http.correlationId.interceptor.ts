import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import * as uuid from 'uuid';
import { Store } from '@ngrx/store';

/**
 * HttpCorrelationIdInterceptor
 */
@Injectable()
export class HttpCorrelationIdInterceptor implements HttpInterceptor {
  /**
   * Constructor
   * @param {Store} store Store
   */
  constructor(private store: Store) {}
  /**
   * Intercept
   * @param {HttpRequest} req HttpRequest
   * @param {HttpHandler} next HttpHandler
   * @returns {Observable} Observable
   */
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const reqHeader = {
      'Correlation-Id': uuid.v4(),
    };
    req = req.clone({
      setHeaders: {
        ...reqHeader,
      },
    });
    return next.handle(req);
  }
}
