import { Inject, Injectable, InjectionToken } from '@angular/core';
import { HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { timeout } from 'rxjs/operators';

export const DEFAULT_TIMEOUT = new InjectionToken<number>('defaultTimeout');

/**
 * HttpTimeoutInterceptor
 */
@Injectable()
export class HttpTimeoutInterceptor implements HttpInterceptor {
  /**
   * Constructor
   * @param {number} defaultTimeout number
   */
  constructor(@Inject(DEFAULT_TIMEOUT) protected defaultTimeout: number) {}

  /**
   * Intercept
   * @param {HttpRequest} req HttpRequest
   * @param {HttpHandler} next HttpHandler
   * @returns {Observable} Observable
   */
  intercept(req: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    const timeoutValue = this.defaultTimeout;
    console.log(timeoutValue);
    const timeoutValueNumeric = Number(timeoutValue);

    return next.handle(req).pipe(timeout(timeoutValueNumeric));
  }
}
