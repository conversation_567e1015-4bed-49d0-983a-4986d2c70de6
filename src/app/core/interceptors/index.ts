import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpLanguageInterceptor } from './http.language.interceptor';
import { HttpBaseInterceptor } from './http.base.interceptor';
import { HttpCorrelationIdInterceptor } from './http.correlationId.interceptor';
import { HttpTokenInterceptor } from './http.token.interceptor';
import { HttpResponseInterceptor } from './http.response.interceptor';

export const httpInterceptorProviders = [
  {
    provide: HTTP_INTERCEPTORS,
    useClass: HttpBaseInterceptor,
    multi: true,
  },
  {
    provide: HTTP_INTERCEPTORS,
    useClass: HttpTokenInterceptor,
    multi: true,
  },
  {
    provide: HTTP_INTERCEPTORS,
    useClass: HttpCorrelationIdInterceptor,
    multi: true,
  },
  {
    provide: HTTP_INTERCEPTORS,
    useClass: HttpLanguageInterceptor,
    multi: true,
  },
  {
    provide: HTTP_INTERCEPTORS,
    useClass: HttpResponseInterceptor,
    multi: true,
  },
];
