import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpH<PERSON><PERSON>, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { I18nService } from '../services';

/**
 *
 */
@Injectable()
export class HttpLanguageInterceptor implements HttpInterceptor {
  /**
   * Constructor
   * @param {I18nService} i18nService I18nService
   */
  constructor(private i18nService: I18nService) {}

  /**
   * Intercept
   * @param {HttpRequest} req HttpRequest
   * @param {HttpHandler} next HttpHandler
   * @returns {Observable} Observable
   */
  intercept(req: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    let currentLanguage = '';
    this.i18nService.getCurrentLanguageCode().subscribe((language) => (currentLanguage = language));

    return next.handle(req.clone({ setHeaders: { ['Accept-Language']: currentLanguage } }));
  }
}
