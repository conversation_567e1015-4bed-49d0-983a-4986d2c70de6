# Angular Unit Testing Guide with AI Assistance

## Introduction

This guide provides a comprehensive approach to unit testing Angular applications using AI tools. It covers best practices, patterns, and step-by-step instructions for testing various Angular components, with a special focus on NgRx store elements.

### Core Testing Libraries

- **Jasmine**: The default testing framework for Angular
- **Karma**: The test runner that executes tests in a browser environment
- **Angular Testing Utilities**: Provided by `@angular/core/testing`

# Analyze the Component/Service

1. Understand the component's purpose and functionality
2. Identify inputs, outputs, and dependencies
3. Review existing code to understand behavior

## Best Practices

1. **Follow AAA Pattern**: Arrange, Act, Assert
3. **Test One Thing per Test**: Each test should verify one specific behavior
4. **Keep Tests DRY**: Extract common setup to beforeEach
5. **Use Descriptive Test Names**: Test names should describe what's being tested
6. **Test Edge Cases**: Include tests for error conditions
7. **Avoid Testing Implementation Details**: Focus on behavior, not implementation
8. **Maintain Test Independence**: Tests should not depend on each other
9. **Use TestBed.configureTestingModule Appropriately**: Only include what's needed
10. **Follow Project's Coding Standards**: Tests should follow the same standards as the code

## WORK
1. Read the attached files and theirs related HTML (available with component logic files) carefully and understand them
2. Follow AAA Pattern to create the markdown test scenarios file and store it in folder 'tests/test-cases/unit' with follow name pattern <file-name>.spec.md
3. Follow sample test case define:

No.1: should properly initialize with HttpClient dependency
   - Given: ApiService is being instantiated
   - When: Constructor is called
   - Then: Should properly inject HttpClient service

4. Focus on the logic, variable especially in html file. Avoid the null/undefined issue, that cause issue to break the
5. The test cases need to be covered at least 95% line of codes
6. ONLY NEED TO CREATE THE TEST CASES FILE