## Introduction

This guide provides a comprehensive approach to unit testing Angular applications using AI tools. It covers best practices, patterns, and step-by-step instructions for testing various Angular components, with a special focus on NgRx store elements.

# Execute and Verify

1. Read the test-cases file, the logic files carefully and create the test spec file. The test file should put the same folder as the logic file
2. Run the tests to verify they pass
3. Do not try to correct the logic of the tested file, Just need to update the test files to make sure it follow the test-case.
If on of the test-case is failed (the actual logic do not pass the test case), Pointed out for the developer to manual update the logic themself
4. The test case in the spec file should follow the test cases defined, even the test name. For example

## test-senarios.md

No.1: should properly initialize

## test.spec.ts

  it('No.1: should properly initialize', () => {
    expect(service).toBeTruthy();
  });

5. Run test using headless mode, if there is the failed case, Fix the case and re-run. Make sure it works
