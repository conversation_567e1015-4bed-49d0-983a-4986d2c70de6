This guide provides a comprehensive approach to unit testing Angular applications using AI tools. It covers best practices, patterns, and step-by-step instructions for testing various Angular components, with a special focus on NgRx store elements.

1. Understand the component's purpose and functionality
2. Identify inputs, outputs, and dependencies
3. Review existing code to understand behavior

You are a senior QA/QC, Follow <PERSON> Pattern, read the markdown test scenarios file and store it in folder 'tests/test-cases/unit' with follow name pattern <file-name>.spec.md

# Refine and Extend the Generated Tests

0. Read the attached files and their related file (HTML files) carefully to understand it. You will need to writing the unit test.
1. Review all the tests for accuracy, update the test-cases if needed

## ADD MISSING CASES INTO THE TEST-CASE MARKDOWN FILES
