image:
  name: sonarsource/sonar-scanner-cli:11
  entrypoint: ['']

variables:
  SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar' # Defines the location of the analysis task cache
  GIT_DEPTH: '0' # Tells git to fetch all the branches of the project, required by the analysis task

build-sonar:
  stage: lint
  cache:
    policy: pull-push
    key: 'sonar-cache-$CI_COMMIT_REF_SLUG'
    paths:
      - '${SONAR_USER_HOME}/cache'
      - sonar-scanner/
  script:
    - sonar-scanner -Dsonar.host.url="${SONAR_HOST_URL}"
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == 'main'
    - if: $CI_COMMIT_BRANCH == 'develop'
