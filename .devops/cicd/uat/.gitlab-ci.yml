stages:
  - deploy

deploy:
  stage: deploy
  needs: []
  variables:
    CONTAINER_NAME: sha_ui_uat
    DOCKER_IMAGE_NAME: sha/ui:uat
  script:
    - docker build -f .devops/docker/Dockerfile -t $DOCKER_IMAGE_NAME-dry-run .
    - docker rm -f $CONTAINER_NAME || true
    - docker image rm $DOCKER_IMAGE_NAME || true
    - docker tag $DOCKER_IMAGE_NAME-dry-run $DOCKER_IMAGE_NAME
    - docker run -d --name $CONTAINER_NAME --restart unless-stopped --add-host keycloak-uat.shs.com.vn:*********** --add-host api-sha-uat.shs.com.vn:************ -p $UAT_PORT:4200 $DOCKER_IMAGE_NAME
  tags:
    - sha-ui-runner
