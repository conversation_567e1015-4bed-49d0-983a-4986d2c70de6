variables:
  SRC_PATH: .
  DOC<PERSON><PERSON><PERSON><PERSON>_PATH: .devops/docker
  RUNNER_HOME_PATH: ~/.ssh

stages:
  - build
  - deploy

#### BUILD #####
build:
  stage: build
  needs: []
  variables:
    MODULE_NAME: qa
    CI_IMAGE_NAME: $CI_REGISTRY_IMAGE/$MODULE_NAME:$CI_COMMIT_REF_SLUG
  script:
    - docker build --build-arg ENVIRONMENT=QA -f $DOCKERFILE_PATH/Dockerfile -t $CI_IMAGE_NAME $SRC_PATH
    - echo -n $CI_JOB_TOKEN | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    - docker push $CI_IMAGE_NAME
  tags:
    - SHA-ui-runner

#### DEPLOY QA #####
deploy:
  stage: deploy
  needs: ['build']
  variables:
    MODULE_NAME: qa
    CI_IMAGE_NAME: $CI_REGISTRY_IMAGE/$MODULE_NAME:$CI_COMMIT_REF_SLUG
    CONTAINER_NAME: sha-ui_$MODULE_NAME
  before_script:
    - echo DEPLOY $CONTAINER_NAME
  script:
    - ssh $SERVER_DEV "docker --version"
    - ssh $SERVER_DEV
      "echo -n $CI_JOB_TOKEN | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY"
    - ssh $SERVER_DEV
      "docker pull $CI_IMAGE_NAME; docker rm -f $CONTAINER_NAME; docker run -d --name $CONTAINER_NAME --restart unless-stopped -p $TEST_PORT:80 $CI_IMAGE_NAME; docker image prune -f"
  environment:
    name: QA
    url: https://wis.qa.minastik.com
  tags:
    - SHA-ui-runner
