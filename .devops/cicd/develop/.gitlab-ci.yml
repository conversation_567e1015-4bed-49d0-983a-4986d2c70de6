variables:
  SRC_PATH: .
  <PERSON>OC<PERSON><PERSON><PERSON><PERSON>_PATH: .devops/docker
  RUNNER_HOME_PATH: ~/.ssh

stages:
  - build
  - deploy

#### BUILD ####
build:
  stage: build
  needs: []
  variables:
    MODULE_NAME: develop
    CI_IMAGE_NAME: $CI_REGISTRY_IMAGE/$MODULE_NAME:$CI_COMMIT_REF_SLUG
  script:
    - docker build --build-arg ENVIRONMENT=DEV -f $DOCKERFILE_PATH/Dockerfile -t $CI_IMAGE_NAME $SRC_PATH
    - echo -n $CI_JOB_TOKEN | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    - docker push $CI_IMAGE_NAME
  tags:
    - SHA-ui-runner

#### DEPLOY ####
deploy:
  stage: deploy
  needs:
    - job: build
      optional: true
  variables:
    MODULE_NAME: develop
    CI_IMAGE_NAME: $CI_REGISTRY_IMAGE/$MODULE_NAME:$CI_COMMIT_REF_SLUG
    CONTAINER_NAME: sha-ui_$MODULE_NAME
  before_script:
    - echo DEPLOY $CONTAINER_NAME
  script:
    - ssh -i ~/wis-qa.pem $SERVER_DEV "docker --version"
    - ssh -i ~/wis-qa.pem $SERVER_DEV
      "echo -n $CI_JOB_TOKEN | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY"
    - ssh -i ~/wis-qa.pem $SERVER_DEV
      "docker pull $CI_IMAGE_NAME; docker rm -f $CONTAINER_NAME; docker run -d --name $CONTAINER_NAME --restart unless-stopped -p $DEV_PORT:80 $CI_IMAGE_NAME; docker image prune -f"
  environment:
    name: DEV
    url: https://sha.dev.minastik.com
  tags:
    - SHA-ui-runner

cleanup:
  stage: clean
  needs: ['deploy']
  script:
    - echo ">>>>>>>>> CLEANING on gitlab server..."
    - ls
    - pwd
    - rm -rf ./*
    - docker system prune -af
    - echo ">>>>>>>>> CLEAN on gitlab server DONE"
    - ssh -i ~/wis-qa.pem $SERVER_DEV "docker system prune -af"
  tags:
    - SHA-ui-runner
