pipeline {
    agent {label 'docker'}

    environment {
        // Common
        SERVICE = "sha-ui"
        PROJECT = "sha"

        // Docker
        DOCKER_REGISTRY = 'harbor.minastik.com'
        DOCKERFILE_PATH = '.devops/docker'
        ENVIRONMENT = 'K8S_DEV'

        // Helm chart
        CHART_PATH = '.devops/charts'
        CHART_VERSION = '1.0.0'

        // Git
        BRANCH = "develop"
        GITLAB_REPO_PATH = "**************"
        GITLAB_REPO_NAMESPACE = "nDaniel87"

        // Combination
        DOCKER_IMAGE = "${DOCKER_REGISTRY}/${PROJECT}/${SERVICE}:${BRANCH}"
        CHART_NAME = "${SERVICE}"
        OCI_URL = "oci://${DOCKER_REGISTRY}/${PROJECT}/${CHART_NAME}"
        GIT_REPO = "${GITLAB_REPO_PATH}:${GITLAB_REPO_NAMESPACE}/${SERVICE}.git"
    }

    stages {
        stage('Checkout') {
            steps {
                echo 'Pulling latest code from GitLab...'
                sh "rm -rf ${SERVICE}"
                sh "git clone -b ${BRANCH} ${GIT_REPO}"
            }
        }

        // stage('Check_lint') {
        //     steps {
        //         script {
        //             echo 'Running lint checks...'
        //             sh 'npm install --prefer-offline'
        //             sh 'npm run lint'
        //         }
        //     }
        // }

        stage('Build') {
            steps {
                echo 'Building Docker image...'
                sh "docker build --progress=plain --no-cache --build-arg ENVIRONMENT=${ENVIRONMENT} -f ${SERVICE}/${DOCKERFILE_PATH}/Dockerfile -t ${DOCKER_IMAGE} ./${SERVICE}"

                echo 'Logging in to Docker registry...'
                sh "echo -n ${DOCKER_PASSWORD} | docker login -u ${DOCKER_USERNAME} --password-stdin ${DOCKER_REGISTRY}"

                echo 'Pushing Docker image to registry...'
                sh "docker push ${DOCKER_IMAGE}"

                echo 'Building Helm chart...'
                sh "helm package ${SERVICE}/${CHART_PATH}/${CHART_NAME} --version ${CHART_VERSION}"

                echo 'Logging in to Docker registry...'
                sh "echo -n ${DOCKER_PASSWORD} | docker login -u ${DOCKER_USERNAME} --password-stdin ${DOCKER_REGISTRY}"

                echo 'Pushing Helm chart to Harbor...'
                echo "oci://${DOCKER_REGISTRY}/${PROJECT}/${SERVICE}"
                sh "helm push ${CHART_NAME}-${CHART_VERSION}.tgz oci://${DOCKER_REGISTRY}/${PROJECT}"
            }
        }

        stage('Deploy') {
            steps {
                script {
                    echo 'Adding Helm OCI support...'
                    sh "echo -n ${DOCKER_PASSWORD} | helm registry login --username ${DOCKER_USERNAME} --password-stdin ${DOCKER_REGISTRY}" // Log in to the OCI registry
                    sh "echo '${HELM_VALUES}' > ./values.yaml"
                    sh "cat values.yaml"

                    // Check if the release is already installed
                    def isInstalled = sh(script: "helm status ${CHART_NAME} -n ${PROJECT}", returnStatus: true) == 0

                    if (isInstalled) {
                        echo "Helm release ${CHART_NAME} exists. Upgrading..."
                        sh "helm upgrade ${CHART_NAME} -n ${PROJECT} ${OCI_URL} --version ${CHART_VERSION} -f values.yaml"
                    } else {
                        echo "Helm release ${CHART_NAME} not found. Installing..."
                        sh "helm install ${CHART_NAME} -n ${PROJECT} ${OCI_URL} --version ${CHART_VERSION} -f values.yaml"
                    }
                }

            }
        }

        stage('Clean') {
            steps {
                script {
                    echo 'Cleaning up Docker images...'
                    sh "rm -rf ${SERVICE}"
                    sh "docker system prune -af"
                }
            }
        }
    }
}