global:
  imageRegistry: ""
  imagePullSecrets: []
  defaultStorageClass: ""
  storageClass: ""
  compatibility:
    openshift:
      adaptSecurityContext: auto
kubeVersion: ""
nameOverride: ""
fullnameOverride: ""
commonLabels: {}
commonAnnotations: {}
extraDeploy: []
image:
  registry: harbor.minastik.com
  repository: sha/sha-ui
  tag: develop
  pullPolicy: Always
  pullSecrets:
    - harbor-sha-ui-registry-token
replicaCount: 1
revisionHistoryLimit: 10
podAffinityPreset: ""
podAntiAffinityPreset: soft
nodeAffinityPreset:
  type: ""
  key: ""
  values: []
affinity: {}
nodeSelector: {}
tolerations: []
topologySpreadConstraints: []
extraPodSpec: {}
podLabels: {}
podAnnotations: {}
automountServiceAccountToken: false
hostAliases: []
priorityClassName: ""
schedulerName: ""
podSecurityContext:
  enabled: true
  fsGroupChangePolicy: Always
  sysctls: []
  supplementalGroups: []
  fsGroup: 1001
containerSecurityContext:
  enabled: true
  seLinuxOptions: {}
  runAsUser: 0 #  Root UID
  runAsGroup: 1001
  runAsNonRoot: false
  privileged: false
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: false
  # capabilities:
  #   drop: ["ALL"]
  seccompProfile:
    type: "RuntimeDefault"
command: []
args: []
lifecycleHooks: {}
resourcesPreset: "nano"
resources:
  requests:
    cpu: 1
    memory: 512Mi
  limits:
    cpu: 1
    memory: 1024Mi
startupProbe:
  enabled: false
  path: "/"
  port: http
  initialDelaySeconds: 180
  periodSeconds: 20
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1
livenessProbe:
  enabled: true
  port: http
  initialDelaySeconds: 180
  periodSeconds: 20
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1
readinessProbe:
  enabled: true
  path: "/"
  port: http
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1
customStartupProbe: {}
customLivenessProbe: {}
customReadinessProbe: {}
extraVolumeMounts: []
extraEnvVars: []
extraEnvVarsCM: ""
extraEnvVarsSecret: ""
containerPorts:
  http: 80
  https: 443
initContainers: []
sidecars: []
updateStrategy:
  type: RollingUpdate
pdb:
  create: true
  minAvailable: ""
  maxUnavailable: ""
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 3
  targetCPU: 70
  targetMemory: false
service:
  type: ClusterIP
  ports:
    http: 80
    https: 443
  nodePorts:
    http: ""
    https: ""
  clusterIP: None
  loadBalancerIP: ""
  loadBalancerSourceRanges: []
  annotations: {}
  externalTrafficPolicy: Local
  extraPorts: []
  sessionAffinity: None
  sessionAffinityConfig: {}
networkPolicy:
  enabled: true
  allowExternal: true
  allowExternalEgress: true
  extraIngress: []
  extraEgress: []
  ingressNSMatchLabels: {}
  ingressNSPodMatchLabels: {}
ingress:
  enabled: true
  selfSigned: false
  pathType: ImplementationSpecific
  apiVersion: ""
  ingressClassName: ""
  hostname: "sha.dev.k8s.minastik.com"
  path: /
  annotations: {}
  tls: false
  extraHosts: []
  extraPaths: []
  extraTls: []
  secrets: []
  extraRules: []