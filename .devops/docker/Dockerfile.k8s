# Stage 1: Build the Angular app
FROM node:18-alpine3.17 AS build

ARG ENVIRONMENT=STAGING
ENV _ENVIRONMENT=$ENVIRONMENT

RUN echo $_ENVIRONMENT

WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build:k8s

# Stage 2: Serve the Angular app using nginx
FROM nginx:1.25.2-alpine
COPY default.conf /etc/nginx/conf.d/
COPY --from=build /app/dist/sha-ui/browser /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
