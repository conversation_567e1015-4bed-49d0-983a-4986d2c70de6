{"$schema": "http://json-schema.org/schema#", "type": "object", "properties": {"ingress": {"type": "object", "form": true, "title": "Ingress Configuration", "properties": {"enabled": {"type": "boolean", "form": true, "title": "Use a custom hostname", "description": "Enable the ingress resource that allows you to access the Apache installation."}, "hostname": {"type": "string", "form": true, "title": "Hostname", "hidden": {"value": false, "path": "ingress/enabled"}}}}, "service": {"type": "object", "form": true, "title": "Service Configuration", "properties": {"type": {"type": "string", "form": true, "title": "Service Type", "description": "Allowed values: \"ClusterIP\", \"NodePort\" and \"LoadBalancer\""}}}, "replicaCount": {"type": "integer", "form": true, "title": "Number of Replicas"}}}