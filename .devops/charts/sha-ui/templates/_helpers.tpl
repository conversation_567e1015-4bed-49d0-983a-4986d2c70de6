{{/* vim: set filetype=mustache: */}}
{{/*
Return the proper sha-ui image name
*/}}
{{- define "sha-ui.image" -}}
{{- include "common.images.image" (dict "imageRoot" .Values.image "global" .Values.global) -}}
{{- end -}}

{{/*
Return the proper sha-ui Docker Image Registry Secret Names
*/}}
{{- define "sha-ui.imagePullSecrets" -}}
{{- include "common.images.pullSecrets" (dict "images" (list .Values.image) "global" .Values.global) -}}
{{- end -}}

{{/*
Return true if mouting a static web page
*/}}
{{- define "sha-ui.useHtdocs" -}}
{{ default "" (or .Values.cloneHtdocsFromGit.enabled .Values.htdocsConfigMap .Values.htdocsPVC) }}
{{- end -}}

{{/*
Return associated volume
*/}}
{{- define "sha-ui.htdocsVolume" -}}
{{- if .Values.cloneHtdocsFromGit.enabled }}
emptyDir: {}
{{- else if .Values.htdocsConfigMap }}
configMap:
  name: {{ .Values.htdocsConfigMap }}
{{- else if .Values.htdocsPVC }}
persistentVolumeClaim:
  claimName: {{ .Values.htdocsPVC }}
{{- end }}
{{- end -}}

{{/*
Validate data
*/}}
{{- define "sha-ui.validateValues" -}}
{{- $messages := list -}}
{{- $messages := append $messages (include "sha-ui.validateValues.htdocs" .) -}}
{{- $messages := append $messages (include "sha-ui.validateValues.htdocsGit" .) -}}
{{- $messages := without $messages "" -}}
{{- $message := join "\n" $messages -}}
 {{- if $message -}}
{{-   printf "\nVALUES VALIDATION:\n%s" $message | fail -}}
{{- end -}}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "sha-ui.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Validate data (htdocs)
*/}}
{{- define "sha-ui.validateValues.htdocs" -}}
{{- if or (and .Values.cloneHtdocsFromGit.enabled (or .Values.htdocsPVC .htdocsConfigMap )) (and .Values.htdocsPVC (or .Values.htdocsConfigMap .Values.cloneHtdocsFromGit.enabled )) (and .Values.htdocsConfigMap (or .Values.htdocsPVC .Values.cloneHtdocsFromGit.enabled )) }}
sha-ui: htdocs
    You have selected more than one way of deploying htdocs. Please select only one of htdocsConfigMap cloneHtdocsFromGit or htdocsVolume
{{- end }}
{{- end -}}

{{/*
Validate data (htdocs git)
*/}}
{{- define "sha-ui.validateValues.htdocsGit" -}}
{{- if .Values.cloneHtdocsFromGit.enabled }}
  {{- if not .Values.cloneHtdocsFromGit.repository }}
sha-ui: htdocs-git-repository
    You did not specify a git repository to clone. Please set cloneHtdocsFromGit.repository
  {{- end }}
  {{- if not .Values.cloneHtdocsFromGit.branch }}
sha-ui: htdocs-git-branch
    You did not specify a branch to checkout in the git repository. Please set cloneHtdocsFromGit.branch
  {{- end }}
{{- end -}}
{{- end -}}

{{/*
Validate values of sha-ui - Incorrect extra volume settings
*/}}
{{- define "sha-ui.validateValues.extraVolumes" -}}
{{- if and (.Values.extraVolumes) (not (or .Values.extraVolumeMounts .Values.cloneHtdocsFromGit.extraVolumeMounts)) -}}
sha-ui: missing-extra-volume-mounts
    You specified extra volumes but not mount points for them. Please set
    the extraVolumeMounts value
{{- end -}}
{{- end -}}

{{/*
Return the proper git image name
*/}}
{{- define "git.image" -}}
{{- include "common.images.image" (dict "imageRoot" .Values.git "global" .Values.global) -}}
{{- end -}}

{{/*
Get the vhosts config map name.
*/}}
{{- define "sha-ui.vhostsConfigMap" -}}
{{- if .Values.vhostsConfigMap -}}
    {{- printf "%s" (tpl .Values.vhostsConfigMap $) -}}
{{- else -}}
    {{- printf "%s-vhosts" (include "common.names.fullname" . ) | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}

{{/*
Get the httpd.conf config map name.
*/}}
{{- define "sha-ui.httpdConfConfigMap" -}}
{{- if .Values.httpdConfConfigMap -}}
    {{- printf "%s" (tpl .Values.httpdConfConfigMap $) -}}
{{- else -}}
    {{- printf "%s-httpd-conf" (include "common.names.fullname" . ) | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}
