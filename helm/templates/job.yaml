{{- if eq .Values.type "Job" -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Values.applicationName}}
spec:
  backoffLimit: 0
  template:
    spec:
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
        - name: {{ .Values.imagePullSecrets }}
      {{- end }}
      containers:
        - name: {{ .Values.applicationName }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          envFrom:
            {{- toYaml .Values.envFrom | nindent 12 }}
          volumeMounts:
            {{- toYaml .Values.volumeMounts | nindent 12 }}
      volumes:
        {{- toYaml .Values.volumes | nindent 12 }}
      restartPolicy: Never
{{- end }}