{{- if .Values.externalIngress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.applicationName }}-external
  annotations:
    {{- toYaml .Values.externalIngress.annotations | nindent 4 }}
spec:
  ingressClassName: "kong"
  rules:
    - host: {{ .Values.externalIngress.hosts}}
      http:
        paths:
          - path: {{ .Values.externalIngress.path }}
            pathType: {{ .Values.externalIngress.pathType }}
            backend:
              service:
                name: {{ .Values.applicationName }}
                port:
                  number: {{ .Values.service.port }}
{{- end }}