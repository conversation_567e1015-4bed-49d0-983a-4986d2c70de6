{{- if .Values.agencyIngress -}}
{{- if .Values.agencyIngress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.applicationName }}-agency
  annotations:
    {{- toYaml .Values.agencyIngress.annotations | nindent 4 }}
spec:
  ingressClassName: "kong"
  rules:
    - host: {{ .Values.agencyIngress.hosts}}
      http:
        paths:
          - path: {{ .Values.agencyIngress.path }}
            pathType: {{ .Values.agencyIngress.pathType }}
            backend:
              service:
                name: {{ .Values.applicationName }}
                port:
                  number: {{ .Values.service.port }}
{{- end }}
{{- end }}