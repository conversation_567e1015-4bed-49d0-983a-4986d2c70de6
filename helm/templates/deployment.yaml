{{- if eq .Values.type "Deployment" -}}
apiVersion: apps/v1
kind: {{ .Values.type }}
metadata:
  name: {{ .Values.applicationName }}
  labels:
    name: {{ .Values.applicationName }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 200%
      maxUnavailable: 0
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      name: {{ .Values.applicationName }}
  template:
    metadata:
      annotations:
        {{- if .Values.metrics.enabled }}
        prometheus.io/scrape: "true"
        prometheus.io/port: {{ .Values.metrics.port | quote }}
        prometheus.io/path: {{ .Values.metrics.path | quote }}
        {{- end }}
        {{- if .Values.istio.enabled }}
        sidecar.istio.io/proxyCPU: {{ .Values.istio.proxycpu | quote }}
        sidecar.istio.io/proxyMemoryLimit: {{ .Values.istio.proxymemorylimit | quote }}
        {{- end }}
        name: {{ .Values.applicationName }}
      labels:
        name: {{ .Values.applicationName }}
    spec:
      affinity:
        {{- toYaml .Values.affinity | nindent 8 }}
      {{- if .Values.hostAliases }}
      hostAliases:
        {{- toYaml .Values.hostAliases | nindent 6 }}
      {{- end }}
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
        - name: {{ .Values.imagePullSecrets }}
      {{- end }}
      containers:
        - name: {{ .Values.applicationName }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.containerPort}}
              protocol: TCP
          {{- if .Values.health.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.health.livenessProbePath}}
              port: http
            initialDelaySeconds: 100
            timeoutSeconds: 30
          readinessProbe:
            httpGet:
              path: {{ .Values.health.readinessProbePath}}
              port: http
            initialDelaySeconds: 100
            timeoutSeconds: 30
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          envFrom:
            {{- toYaml .Values.envFrom | nindent 12 }}
          volumeMounts:
            {{- toYaml .Values.volumeMounts | nindent 12 }}
      volumes:
        {{- toYaml .Values.volumes | nindent 12 }}
{{- end }}