{{- if .Values.service.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.applicationName }}
  labels:
    name: {{ .Values.applicationName }}
  {{- if  .Values.service.disableRetry }}
  annotations:
    konghq.com/override: disable-retry
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    name: {{ .Values.applicationName }}
{{- end }}