# replica when don't have HPA
replicaCount: 2
# Type of application Job | Deployment
type: Deployment

# Image to run
image:
    repository: registry.shs.com.vn/sha-uat/sha-ui
    pullPolicy: Always
    tag: 210c06c3583c7dfbfe7a72fc248883f559a3204b-14

# Application name
applicationName: "sha-ui"

# Secret to pull image from private repository
imagePullSecrets: "registry.shs.com.vn"

# Service to connect internal K8S
service:
    enabled: true
    type: ClusterIP
    port: 80
    containerPort: 80
    disableRetry: true


# Ingress to expose service to internet
externalIngress:
    enabled: false


# Resource management
resources:
    limits:
        memory: 1Gi
    requests:
        cpu: 100m
        memory: 250Mi

# Health check
health:
    enabled: false
    livenessProbePath: /iam/actuator/health/liveness
    readinessProbePath: /iam/actuator/health/readiness

# Auto scaling
autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    # targetCPUUtilizationPercentage: 300
    targetMemoryUtilizationPercentage: 300

# Enable expose metrics
metrics:
    enabled: false
    port: 8080
    path: /iam/actuator/prometheus

istio:
    enabled: true
    proxycpu: 200m
    proxymemorylimit: 1Gi


# Set Application env here
configMap:
  default.conf: |
    server {
      listen 80;
      client_max_body_size 150M;
      client_body_buffer_size 16K;

      location / {
        root   /usr/share/nginx/html;
        index  index.html;
        try_files $uri $uri/ /index.html;
      }
    }
    
# envFrom:
#   - secretRef:
#       name: sha-ui
      
secret: {}
#   annotations:
#     avp.kubernetes.io/path: "sha-service-uat/data/sha-ui"
#   type: Opaque
#   data:


  


# Set volumeMounts
# volumeMounts:
    # - name: certkafka
      # mountPath: /certkafka
      # readOnly: true
    # - name: backuplog
      # mountPath: /logs
# # Set volumes
# volumes:
    # - name: certkafka
      # persistentVolumeClaim:
        # claimName: certkafka

    # - name: backuplog
      # persistentVolumeClaim:
        # claimName: backuplog



# Pods in a Deployment will prefer deployed on difference node
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: name
            operator: In
            values:
            - shs-ui
        topologyKey: kubernetes.io/hostname


 
